// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import VueVirtualScroller from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import Vuex from 'vuex'
import App from './App'
import ViewUI from 'view-design'
import ElementUI from 'element-ui'
import axios from 'axios'
import 'jquery/dist/jquery.min.js'
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap/dist/js/bootstrap.min.js'
import 'view-design/dist/styles/iview.css'
import 'font-awesome/css/font-awesome.css'
import 'element-ui/lib/theme-chalk/index.css'
import router from './router'
import util from '@/util/util.js'
import auth from '@/util/auth.js'
import SSOWeb from '@mtfe/sso-web'
import { Bus } from '@/global/bus'
import userEnv from '@/assets/user_env'
import VueQriously from 'vue-qriously'
import VueQuillEditor from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import vuePicturePreview from 'vue-picture-preview'
import store from './vuex/index'
import VueClipboard from 'vue-clipboard2'
import VueCropper from 'vue-cropper'
import VueCompositionApi from '@vue/composition-api'
import MTD_NEXT from '@ss/mtd-vue2'
import '@ss/mtd-vue2/lib/theme-chalk/index.css'

Vue.use(VueCompositionApi)
Vue.use(MTD_NEXT)

Vue.use(VueCropper)
Vue.use(ElementUI)
Vue.use(ViewUI)
Vue.use(util)
Vue.use(auth)
Vue.use(VueQriously)
Vue.use(VueQuillEditor)
Vue.use(vuePicturePreview)
Vue.use(Vuex)
Vue.use(VueVirtualScroller)
Vue.use(VueClipboard)

Vue.prototype.$axios = axios
Vue.prototype.env = userEnv

console.log(process.env.NODE_ENV)
let online = false
let ssoWeb = ''

let clientId = ''
if (process.env.NODE_ENV === 'prod') {
  console.log('线上环境')
  online = true
  clientId = '573beaded4'
  ssoWeb = SSOWeb({
    clientId: '573beaded4',
    accessEnv: 'product'
  })
} else {
  console.log('线下环境')
  online = false
  clientId = '66877119'
  ssoWeb = SSOWeb({
    clientId: '66877119',
    accessEnv: 'ppe'
  })
  Vue.config.devtools = true
}

Vue.config.productionTip = false

axios.interceptors.response.use(
  axiosResponse => {
    return axiosResponse
  },
  errResponse => {
    return Promise.reject(errResponse)
  }
)

let portal = new Vue({
  router,
  store,
  template: '<App/>',
  components: { App }
})

Bus.ssoWeb = ssoWeb

ssoWeb.login().then(ssoid => {
  if (typeof ssoid === 'string') {
    axios
      .post(
        portal.getDomain('cq') + '/portal/sso-parse',
        JSON.stringify({
          ssoid: ssoid,
          isOnline: online,
          clientId: clientId
        }),
        { headers: { isOnline: online, SSOID: ssoid } }
      )
      .then(function(message) {
        let response = message.data
        if (Object.keys(response).indexOf('msg') === -1) {
          let userInfo = {
            userId: response.staffId,
            userName: response.name,
            userLogin: response.loginName,
            ssoId: ssoid,
            isOnline: online,
            uid: response.uid,
            msg: '',
            MCDBGID: response.MCDBGID,
            MCDBUID: response.MCDBUID,
            bgId: response.bgId,
            bgName: response.bgName,
            bgSub1Id: response.bgSub1Id,
            bgSub1Name: response.bgSub1Name
          }
          axios.defaults.headers.common['isOnline'] = online
          axios.defaults.headers.common['SSOID'] = ssoid
          Bus.$emit('refreshUserInfo', userInfo)
          Bus.userInfo = userInfo
          portal.$mount('#app')
        } else {
          // 强制刷新，触发退出登陆事件
          window.location.href = window.location.href
          ssoWeb.logout()
        }
      })
      .catch(function(e) {
        console.log(e)
        let userInfo = {
          userLogin: 'undefined',
          msg: '访问解析SSOID服务超时'
        }
        Bus.$emit('refreshUserInfo', userInfo)
        Bus.userInfo = userInfo
        portal.$mount('#app')
      })
  } else {
    let userInfo = {
      userLogin: 'undefined',
      msg: 'SSO鉴权失败'
    }
    Bus.$emit('refreshUserInfo', userInfo)
    Bus.userInfo = userInfo
    portal.$mount('#app')
  }
})

export default portal
