/* eslint-disable */
let url = "http://client.hotel.test.sankuai.com/";
//let url = "http://0.0.0.0:8080/";

let task_url = "http://clientcommon.hotel.test.sankuai.com//client/task";

let comp_url = "http://msstest-corp.sankuai.com/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility";

let clientImageUrl = "/client-img-api/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/";

let package_url = comp_url + "/static/app/";

let jira_url = "https://flow.sankuai.com/browse/";

let job_url = "http://qa.sankuai.com/microscope/jobInfo?jobId=";

let client_common_url = "http://localhost:9096/";

let vision_url = "http://*************:9092/";

let clientAnalyticUrl = "http://localhost:8089";

let compatibilityUrl = "http://localhost:8001/";

let shortLink_job_url = "http://qa.sankuai.com/clientShortLinkJob/jobInfo";

let auitestagent_result_url = "http://qa.sankuai.com/microscope/auitestagentResult";

let es_url = "https://aiengineering.sankuai.com";
let es_test_url = "https://llm.hotel.test.sankuai.com";

let llm_service_url = "http://localhost:8002";

let sb_url = "http://switchboard.hotel.test.sankuai.com";

if (process.env.NODE_ENV === "prod") {
  url = "https://client.hotel.test.sankuai.com/";
  llm_service_url = "https://llm.hotel.test.sankuai.com";
  client_common_url = "https://clientcommon.hotel.test.sankuai.com/";
  compatibilityUrl = "https://compatibility.hotel.test.sankuai.com/";
  clientAnalyticUrl = "http://*************:8080";
  clientImageUrl = "https://msstest-img.sankuai.com/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/";
}

let coe_base_url = process.env.NODE_ENV !== "prod" ? "http://localhost:8002/coe" : "https://llm.hotel.test.sankuai.com/coe";
// let coe_base_url = 'https://llm.hotel.test.sankuai.com/coe'
let coe_prod_url = "https://aiengineering.sankuai.com/coe";
let campaign_base_url =
  process.env.NODE_ENV !== "prod" ? "http://localhost:8002/campaign/activity" : "https://llm.hotel.test.sankuai.com/campaign/activity";

export default {
  url,
  llm_service_url,
  vision_url,
  comp_url,
  task_url,
  package_url,
  jira_url,
  job_url,
  client_common_url,
  clientAnalyticUrl,
  compatibilityUrl,
  shortLink_job_url,
  clientImageUrl,
  coe_base_url,
  coe_prod_url,
  campaign_base_url,
  auitestagent_result_url,
  es_url,
  es_test_url,
  sb_url
};
