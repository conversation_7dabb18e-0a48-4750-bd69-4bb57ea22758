import Vue from 'vue'
// import Bus from '@/global/bus'
import Router from 'vue-router'
import Home from '@/components/Homepage/HomePageDynamic'
import MachineManage from '@/components/Import/MachineManage'
import JsonEditor from '@/components/JsonEditor/JsonEditor'
import coverRateChart from '@/components/ConfigurationCenter/coverRatechartPage'
import Watch from '@/components/Import/Watch'
import OnlineBug from '@/components/Import/OnlineBug'
import CodeReport from '@/components/Import/CodeReport'
import Lyrebird from '@/components/Import/Lyrebird'
import Pegasus from '@/components/Import/Pegasus'
import RssHomepage from '@/components/Rss/RssHomepage'
import ClientVersionReport from '@/components/ReportGenerate/ClientVersionReport'
import ServerReport from '@/components/ReportGenerate/ServerReport'
import TestReport from '@/components/ReportGenerate/TestReport'
import ClientTestReport from '@/components/ReportGenerate/ClientTestReport'
import Analytics from '@/components/Analytics/AnalyticsHomepage'
import AnalyticsOnes from '@/components/AnalyticsOnes/AnalyticsHomepage'
import DetailEfficiencyReport from '@/components/AnalyticsDetail/DetailEfficiency'
import DetailReport from '@/components/AnalyticsDetail/DetailReport'
import DetailOnlineQuality from '@/components/AnalyticsDetail/DetailOnlineQualityReport'
import AnalyticsClientMetrics from '@/components/ReportGenerate/AnalyticsClientMetrics'
import AnalyticsOnesClient from '@/components/AnalyticsOnesClient/ClientOnesMetrics'
import DetailClientReport from '@/components/ReportGenerate/DetailClientReport'
import ClientOnesDetailReport from '@/components/AnalyticsDetailOnesClient/ClientOnesDetailReport'
import cq from '@/components/CodeQuality/codeQualityHomepage'
import ContentNotFound from '@/components/Common/NotFoundContent'
import PushService from '@/components/PushServices/pushServices'
import CD from '@/components/CD/ContinuousDeliveryHomepage'
import CQ from '@/components/CodeQuality/codeQuality'
import RD from '@/components/AnalyticsDetailOnes/RequirementDetail'
import Dashboard from '@/components/Dashboard/DashboardHomepage'
import HomePageWatch from '@/components/Watch/HomepageWatch'
import HomePageOpen from '@/components/Homepage/OpenHomepage'
import HomePageSiteConfig from '@/components/Homepage/SiteConfigHomepage'
import IntegrationTest from '@/components/IntegrationTest/IntegrationTestDetailPage'
import Direction from '@/components/Direction/direction-homepage'
import DirectionDetail from '@/components/Direction/direction-detail'
import DirectionConfig from '@/components/Direction/DirectionConfigHomepage'
import ForeignDashboard from '@/components/Foreign/Dashboard'
import DataSynchronization from '@/components/DataSynchronization/DataSynchronization'
import testEnvManagement from '@/components/TestEnvManagement/TestEnvManagement'
import clientCd from '@/components/ClientPipeline/ClientQualityPlatform'
import goodsData from '@/components/goodsData/goodsCon'
import securityCkeck from '@/components/SecurityCheck/SecurityCheck'
import codeAnalysis from '@/components/CodeAnalysis/CodeAnalysis'
import clientPage from '@/components/ClientQuality/Page'
import contentNF from '@/components/ClientQuality/baseComponents/ContentNF'
import robust from '@/components/ClientQuality/ClientRobustInfo'
import clientVision from '@/components/ClientQuality/ClientVision'
import visionComponents from '@/components/ClientQuality/Compatibility/VisionComponents'
import clientScheme from '@/components/ClientQuality/ClientPage/ClientScheme'
import clientShortLinkJob from '@/components/ClientQuality/ShortLink/ClientShortLinkJob'
import clientShortLinkJobInfo from '@/components/ClientQuality/ShortLink/ClientShortLinkJobInfo'
import compatibilityUserConfig from '@/components/ClientQuality/Compatibility/CompatibilityUserConfig'
import clientMicroscope from '@/components/ClientQuality/ClientMicroscope'
import clientJobInfo from '@/components/ClientQuality/Compatibility/CompatibilityJobInfo'
import clientLogView from '@/components/ClientQuality/baseComponents/ClientLogView'
import DetailOnlineQualityOnes from '@/components/AnalyticsDetailOnes/DetailOnlineQualityReport'
import DetailReportOnes from '@/components/AnalyticsDetailOnes/DetailReport'
import Config from '@/components/AnalyticsOnes/Config'
import JSONSchema from '@/components/JSON2Schema/JSON2Schema'
import customAnalytics from '@/components/AnalyticsCustom/AnalyticsCustomHomepage'
import DeployReport from '@/components/ReportGenerate/DeployReport'
import CDReport from '@/components/ReportGenerate/CDReport'
import clientAnalytic from '@/components/ClientQuality/ClientAnalytic/views/ClientAnalytic'
import autotestAnalytic from '@/components/ClientQuality/ClientAutotest/AutotestAnalytic'
import AutotestShortLinkAssistAndVisionAssistResult from '@/components/ClientQuality/ClientAutotest/AutotestShortLinkAssistAndVisionAssistResult'
import clientPageBoard from '@/components/ClientQuality/ClientPage/ClientPageBoard'
import businessHomePage from '@/components/ClientQuality/BusinessHomePage'
import PageCenter from '@/components/ClientQuality/BusinessDetail/PageCenter'
import PageDetail from '@/components/ClientQuality/PageDetail/PageHome'
import SceneCenter from '@/components/ClientQuality/PageDetail/SceneCenter'
import UrlModel from '@/components/ClientQuality/UrlScheme/UrlModel'
import TestCase from '@/components/ClientQuality/PageDetail/TestCase'
import PageInfo from '@/components/ClientQuality/PageDetail/PageInfo'
import SceneDetail from '@/components/ClientQuality/SceneDetail/SceneHome'
import SceneInfo from '@/components/ClientQuality/SceneDetail/SceneInfo'
import HJInspect from '@/components/ClientQuality/SceneDetail/HJInspect'
import CaseDetail from '@/components/ClientQuality/SceneDetail/CaseHome'
import AutotestBasicConfig from '@/components/ClientQuality/ClientConfigCenter/AutotestBasicConfig'
import AutotestStrategyConfig from '@/components/ClientQuality/ClientConfigCenter/AutotestStrategyConfig'
import clientShortLinkResultGroupByJob from '@/components/ClientQuality/ShortLink/ClientShortLinkResultGroupByJob'
import RaptorMonitorHomepage from '@/components/RaptorMonitor/RaptorMonitorHomepage'
import TriggerList from '@/components/ClientQuality/BusinessDetail/AutotestTriggerStatus/TriggerList'
import CheckerReport from '@/components/ClientQuality/Checker/CheckerReport'
import AutotestTrigger from '@/components/ClientQuality/ClientAutotest/AutotestTrigger.vue'
import autotestMeasure from '@/components/ClientQuality/Compatibility/AutotestMeasureInfo'
import ApiList from '@/components/ClientQuality/BusinessDetail/ApiList/ApiList'
import ApiDetail from '@/components/ClientQuality/BusinessDetail/ApiDetail/ApiDetail'
import AbList from '@/components/ClientQuality/BusinessDetail/AbList/AbList'
import AbDetail from '@/components/ClientQuality/BusinessDetail/AbDetail/AbDetail'
import PageApiInfo from '@/components/ClientQuality/PageDetail/Api/PageApiInfo'
import deviceOperation from '@/components/ClientQuality/Compatibility/DeviceOperation'
import UIKnowledgeAnnotation from '@/components/ClientQuality/Compatibility/UIKnowledgeAnnotation'
import COEGetter from '@/components/CoeAnalysis/DataGetter'
import COETabPage from '@/components/CoeAnalysis/COETabPage'
import COETask from '@/components/CoeAnalysis/TaskList'
import COEDetail from '@/components/CoeAnalysis/COEDetail'
import COEExperience from '@/components/CoeAnalysis/Experience'
import COEOverview from '@/components/CoeAnalysis/COEOverview'
import COEResult from '@/components/CoeAnalysis/COEResult'
import COEStorage from '@/components/CoeAnalysis/COEStorage'
import compatibilityTaskList from '@/components/ClientQuality/Compatibility/CompatibilityTaskTable'
import CampaignActivityAnalysisActivityShow from '@/components/CampaignActivityAnalysis/ActivityShow'
import TranslationCheck from '@/components/ClientQuality/ME/TranslationCheck'
import EvalStart from '@/components/LLMEvals/EvalStart'
import EvalTaskMetric from '@/components/LLMEvals/MetricView'
import WorkflowStart from '@/components/LLMEvals/WorkflowStart'
import RemoteTaskList from '@/components/LLMEvals/RemoteTaskList'
import RemoteTaskDetail from '@/components/LLMEvals/RemoteTaskDetail'
import EvalPromptSuggestion from '@/components/LLMEvals/PromptSuggestion/PromptSuggestView'
import DetailCardSingleView from '@/components/LLMEvals/DetailCardSingleView'
Vue.use(Router)

const router = new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
      meta: {
        title: 'MQP'
      }
    },
    {
      path: '/me/translationCheck',
      name: 'TranslationCheck',
      component: TranslationCheck,
      meta: {
        title: 'TranslationCheck'
      }
    },
    {
      path: '/evals/task/detail/single',
      name: 'DetailCardSingleView',
      component: DetailCardSingleView,
      meta: {
        title: 'DetailCardSingleView'
      }
    },
    {
      path: '/evals/task/metric',
      name: 'EvalTaskMetric',
      component: EvalTaskMetric,
      meta: {
        title: 'EvalTaskMetric'
      }
    },
    {
      path: '/evals/prompt/suggest',
      name: 'EvalPromptSuggestion',
      component: EvalPromptSuggestion,
      meta: {
        title: 'EvalPromptSuggestion'
      }
    },
    {
      path: '/evals/start',
      name: 'LLMEvals',
      component: EvalStart,
      meta: {
        title: 'LLMEvals'
      }
    },
    {
      path: '/evals/workflow/start',
      name: 'WorkflowStart',
      component: WorkflowStart,
      meta: {
        title: 'WorkflowStart'
      }
    },
    {
      path: '/evals/remote/tasklist',
      name: 'RemoteTaskList',
      component: RemoteTaskList,
      meta: {
        title: 'RemoteTaskList'
      }
    },
    {
      path: '/evals/remote/task',
      name: 'RemoteTaskDetail',
      component: RemoteTaskDetail,
      meta: {
        title: 'RemoteTaskDetail'
      }
    },
    {
      path: '/campaign/mindmap',
      name: 'ActivityShow',
      component: CampaignActivityAnalysisActivityShow,
      meta: {
        title: 'ActivityShow'
      }
    },
    {
      path: '/coe/coegetter',
      name: 'COEGetter',
      component: COEGetter,
      meta: {
        title: 'COEGetter'
      }
    },
    {
      path: '/coe/coestorage',
      name: 'COEStorage',
      component: COEStorage,
      meta: {
        title: 'COEStorage'
      }
    },
    {
      path: '/coe/task',
      name: 'COETask',
      component: COETask,
      meta: {
        title: 'COETask'
      }
    },
    {
      path: '/coe/experience',
      name: 'COEExperience',
      component: COEExperience,
      meta: {
        title: 'COEExperience'
      }
    },
    {
      path: '/coe/detail/:task_id',
      redirect: '/coe/detail/:task_id/task_description'
    },
    {
      path: '/coe/detail/:task_id/task_description',
      name: 'COEDetail',
      component: COEDetail,
      meta: {
        title: 'COEDetail'
      }
    },
    {
      path: '/coe/detail/:task_id/coe_result',
      name: 'COEResult',
      component: COEResult,
      meta: {
        title: 'COEResult'
      }
    },
    {
      path: '/coe/detail/:task_id/coe_overview',
      name: 'COEOverview',
      component: COEOverview,
      meta: {
        title: 'COEOverview'
      }
    },
    {
      path: '/coe/result_tab/task_id/:task_id', // ?coe_id=xxx&type=xxx
      name: 'COETab',
      component: COETabPage,
      meta: {
        title: 'COETab'
      }
    },
    {
      path: '/direction/homepage',
      name: 'Direction',
      meta: {
        title: '方向管理'
      },
      component: Direction
    },
    {
      path: '/direction/detail/config/:id/:tab',
      name: 'direction-config-homepage',
      meta: {
        title: '项目配置页'
      },
      component: DirectionConfig
    },
    {
      path: '/direction/detail/config/:id',
      name: 'direction-config-homepage',
      meta: {
        title: '项目配置页'
      },
      component: DirectionConfig
    },
    {
      path: '/direction/:tab',
      name: 'Direction-2',
      meta: {
        title: '方向管理'
      },
      component: Direction
    },
    {
      path: '/detail-homepage/:id',
      name: 'Direction-detail',
      meta: {
        title: '方向详情'
      },
      component: DirectionDetail
    },
    {
      path: '/detail-homepage/:id/:tab',
      name: 'Direction-detail',
      meta: {
        title: '方向详情'
      },
      component: DirectionDetail
    },
    {
      path: '/rss/config',
      name: 'rss-manage',
      meta: {
        title: '订阅管理'
      },
      component: RssHomepage
    },
    {
      path: '/homepage/cd',
      name: 'Continuous Delivery',
      meta: {
        title: '持续交付'
      },
      component: CD
    },
    {
      path: '/homepage/cd/client',
      name: 'Continuous Delivery Tab',
      meta: {
        title: '客户端持续交付'
      },
      component: clientCd
    },
    {
      path: '/homepage/cd/:tab',
      name: 'Continuous Delivery Tab',
      meta: {
        title: '持续交付'
      },
      component: CD
    },
    {
      path: '/homepage/cd/it/:id',
      name: 'Integration Test Detail',
      meta: {
        title: '集成测试Pipeline'
      },
      component: IntegrationTest
    },
    {
      path: '/homepage/cq',
      name: 'Code Quality',
      meta: {
        title: 'Code Quality'
      },
      component: CQ
    },
    {
      path: '/homepage/dashboard',
      name: 'Dashboard',
      meta: {
        title: 'Dashboard'
      },
      component: Dashboard
    },
    {
      path: '/homepage/raptorMonitor',
      name: 'RaptorMonitor',
      meta: {
        title: 'RaptorMonitor'
      },
      component: RaptorMonitorHomepage
    },
    {
      path: '/analytics',
      name: 'analytics ones',
      meta: {
        title: '过程度量 on Ones',
        // keepAlive: true,
        isBack: false
      },
      component: AnalyticsOnes
    },
    {
      path: '/homepage/analytics/ones',
      name: 'analytics ones',
      meta: {
        title: '过程度量 on Ones',
        // keepAlive: true,
        isBack: false
      },
      component: AnalyticsOnes
    },
    {
      path: '/homepage/analytics/ones/:tab',
      name: 'analytics ones tab',
      meta: {
        title: '过程度量 on Ones',
        // keepAlive: true,
        isBack: false
      },
      component: AnalyticsOnes
    },
    {
      path: '/homepage/analytics/custom/:keyword/ones',
      name: 'analytics ones',
      meta: {
        title: '过程度量 on Ones',
        // keepAlive: true,
        isBack: false
      },
      component: AnalyticsOnes
    },
    {
      path: '/homepage/analytics/custom/:keyword/ones/:tab',
      name: 'analytics ones tab',
      meta: {
        title: '过程度量 on Ones',
        // keepAlive: true,
        isBack: false
      },
      component: AnalyticsOnes
    },
    {
      path: '/homepage/analytics',
      name: 'analytics',
      meta: {
        title: '过程度量',
        // keepAlive: true,
        isBack: false
      },
      component: Analytics
    },
    {
      path: '/homepage/analytics/:tab',
      name: 'analytics tab',
      meta: {
        title: '过程度量',
        // keepAlive: true,
        isBack: false
      },
      component: Analytics
    },
    {
      path: '/homepage/new-config',
      name: 'config',
      meta: {
        title: '配置接入'
        // keepAlive: true,
      },
      component: Config
    },
    {
      path: '/custom/Analytics',
      name: 'customAnalytics',
      meta: {
        title: '自定义过程度量'
      },
      component: customAnalytics
    },
    {
      path: '/homepage/watch',
      name: 'Homepage-Watch',
      meta: {
        title: '监控大盘'
      },
      component: HomePageWatch
    },
    {
      path: '/homepage/open',
      name: 'Homepage-open',
      meta: {
        title: '开放平台'
      },
      component: HomePageOpen
    },
    {
      path: '/homepage/siteConfig',
      name: 'Homepage-site-config',
      meta: {
        title: '站点设置'
      },
      component: HomePageSiteConfig
    },
    {
      path: '/coverRate-config',
      name: 'coverRate',
      meta: {
        title: '覆盖率图表显示'
      },
      component: coverRateChart
    },
    {
      path: '/watch',
      name: 'watch',
      meta: {
        title: '酒旅工具Python服务监控中心'
      },
      component: Watch
    },
    {
      path: '/req-info/:reqid',
      name: 'reqDetailInfo',
      meta: {
        title: '需求详细页面'
      },
      component: RD
    },
    {
      path: '/detail',
      name: 'detailReport',
      meta: {
        title: '过程度量详细报告'
      },
      component: DetailReport
    },
    {
      path: '/overview/tice/ones/detail',
      name: 'detailOnesReport',
      meta: {
        title: '过程度量详细报告 on Ones'
      },
      component: DetailReportOnes
    },
    {
      path: '/online/issuedetail',
      name: 'detailOnlineReport',
      meta: {
        title: '线上质量详细数据'
      },
      component: DetailOnlineQuality
    },
    {
      path: '/online/issuedetail/ones',
      name: 'detailOnlineOnesReport',
      meta: {
        title: '线上质量详细数据 on Ones'
      },
      component: DetailOnlineQualityOnes
    },
    {
      path: '/efficiency/detail',
      name: 'detailefficiency',
      meta: {
        title: '人效详细报告'
      },
      component: DetailEfficiencyReport
    },
    {
      path: '/analytics/client/ones',
      name: 'analyticsClientOnes',
      meta: {
        title: 'ones客户端过程度量'
      },
      component: AnalyticsOnesClient
    },
    {
      path: '/analytics/client',
      name: 'analyticsClient',
      meta: {
        title: '客户端过程度量'
      },
      component: AnalyticsClientMetrics
    },
    {
      path: '/client/detail/ones',
      name: 'detailClient',
      meta: {
        title: '客户端详细数据'
      },
      component: ClientOnesDetailReport
    },
    {
      path: '/client/detail',
      name: 'detailClient',
      meta: {
        title: '客户端详细数据'
      },
      component: DetailClientReport
    },
    {
      path: '/report',
      name: 'report',
      meta: {
        title: '报告生成自动化'
      },
      component: TestReport
    },
    {
      path: '/server-report',
      name: 'serverReport',
      meta: {
        title: '报告生成自动化'
      },
      component: ServerReport
    },
    {
      path: '/test-report',
      name: 'testReport',
      meta: {
        title: '报告生成自动化'
      },
      component: TestReport
    },
    {
      path: '/client-test-report',
      name: 'clientTestReport',
      meta: {
        title: '报告生成自动化'
      },
      component: ClientTestReport
    },
    {
      path: '/client-version-report',
      name: 'clientVersionReport',
      meta: {
        title: '报告生成自动化'
      },
      component: ClientVersionReport
    },
    {
      path: '/report/deploy-report',
      name: 'DeployReport',
      meta: {
        title: '部署时长报告'
      },
      component: DeployReport
    },
    {
      path: '/report/cd-report',
      name: 'CDReport',
      meta: {
        title: '持续交付运营报告'
      },
      component: CDReport
    },
    {
      path: '/code-report',
      name: 'codeReport',
      meta: {
        title: '开发质量报告'
      },
      component: CodeReport
    },
    {
      path: '/lyrebird',
      name: 'lyrebird',
      meta: {
        title: 'Lyrebird'
      },
      component: Lyrebird
    },
    {
      path: '/online-bug',
      name: 'onlineBug',
      meta: {
        title: '线上故障'
      },
      component: OnlineBug
    },
    {
      path: '/machine-manage',
      name: 'machineManage',
      meta: {
        title: '测试设备管理平台'
      },
      component: MachineManage
    },
    {
      path: '/pegasus',
      name: 'pegasus',
      meta: {
        title: 'Pegasus'
      },
      component: Pegasus
    },
    {
      path: '/push-service',
      name: 'pushService',
      meta: {
        title: '推送服务'
      },
      component: PushService
    },
    {
      path: '/json-editor',
      name: 'jsonEditor',
      meta: {
        title: 'Json在线编辑器'
      },
      component: JsonEditor
    },
    {
      path: '/data-synchronization',
      name: 'dataSynchronization',
      meta: {
        title: '数据同步服务'
      },
      component: DataSynchronization
    },
    {
      path: '/testenv-management',
      name: 'test-env-management',
      meta: {
        title: '测试环境管理'
      },
      component: testEnvManagement
    },
    {
      path: '/security-check',
      name: 'securityCkeck',
      meta: {
        title: '安全性测试'
      },
      component: securityCkeck
    },
    {
      path: '/code-analysis',
      name: 'codeAnalysis',
      meta: {
        title: '代码变更分析'
      },
      component: codeAnalysis
    },
    {
      path: '/git-info/:git/project/:project/repo/:repo/config/:tab',
      name: 'repoConfigHomepage',
      meta: {
        title: 'Code Quality'
      },
      component: cq
    },
    {
      path: '/git-info/:git/project/:project/repo/:repo/config',
      name: 'repoConfigHomepage-2',
      meta: {
        title: 'Code Quality'
      },
      component: cq
    },
    {
      path: '/git-info/:git/project/:project/repo/:repo/result/:tab',
      name: 'repoResultHomepage',
      meta: {
        title: 'Code Quality'
      },
      component: cq
    },
    {
      path: '/git-info/:git/project/:project/repo/:repo/result',
      name: 'repoResultHomepage-2',
      meta: {
        title: 'Code Quality'
      },
      component: cq
    },
    {
      path: '/git-info/:git/project/:project/repo/:repo',
      name: 'repoResultHomepage-3',
      meta: {
        title: 'Code Quality'
      },
      component: cq
    },
    {
      path: '/foreign/dashboard',
      name: 'foreign-dashboard',
      meta: {
        title: '对外大盘'
      },
      component: ForeignDashboard
    },
    {
      path: '/goods-data',
      name: 'goodsData',
      meta: {
        title: '数据创建'
      },
      component: goodsData
    },
    {
      path: '/json-2-json-schema',
      name: 'JsonSchema',
      meta: {
        title: 'JsonSchema生成工具'
      },
      component: JSONSchema
    },
    {
      path: '/client',
      component: clientPage,
      meta: ['MMCD', 'MMCD'],
      children: [
        {
          path: '/robust',
          component: robust,
          meta: ['客户端健壮性', '业务数据']
        },
        {
          path: '/clientShortLinkJob',
          component: clientShortLinkJob,
          meta: ['ShortLink', 'Job列表']
        },
        {
          path: '/autotest/ConfigStatus',
          name: 'AutotestTrigger',
          component: AutotestTrigger,
          meta: {
            title: '自动化触发信息'
          }
        },
        {
          path: '/clientShortLinkJob/JobInfo',
          component: clientShortLinkJobInfo,
          meta: ['ShortLink', '任务信息']
        },
        {
          path: '/clientChecker/checkerInfo',
          component: CheckerReport,
          meta: ['Checker', '报警结果信息']
        },
        {
          path: '/autotest/Result/jobInfo',
          component: clientShortLinkResultGroupByJob,
          meta: ['AutoTest', '失败原因详情']
        },
        {
          path: '/clientVision',
          component: clientVision,
          meta: ['Vision', '神经网络算法']
        },
        {
          path: '/visionComponents',
          component: visionComponents,
          meta: ['Vision', '应用市场']
        },
        {
          path: '/clientScheme',
          component: clientScheme,
          meta: ['基础设施', '页面管理']
        },
        {
          path: '/compatibilityUserConfig',
          component: compatibilityUserConfig,
          meta: ['HyperJump', '配置管理']
        },
        {
          path: '/microscope/jobInfo',
          component: clientMicroscope,
          meta: ['HyperJump', 'Job详情']
        },
        {
          path: '/microscope/UIKnowledgeAnnotation',
          component: UIKnowledgeAnnotation,
          meta: ['HyperJump', 'UI知识库标注']
        },
        {
          path: '/microscope/jobList',
          component: clientJobInfo,
          meta: ['HyperJump', 'Job列表']
        },
        {
          path: '/deviceOperation',
          component: deviceOperation,
          meta: ['HyperJump', '设备运维']
        },
        {
          path: '/microscope/taskList',
          component: compatibilityTaskList,
          meta: ['HyperJump', 'Task列表']
        },
        {
          path: '/client/logView',
          component: clientLogView,
          meta: ['任务日志', '任务日志']
        },
        {
          path: '/client/pageBoard',
          component: clientPageBoard,
          meta: ['页面管理', 'Inspect']
        },
        {
          path: '/client/analytic',
          component: clientAnalytic,
          meta: ['客户端过程度量', '客户端过程度量']
        },
        {
          path: '/autotest/analytic',
          component: autotestAnalytic,
          meta: ['自动化度量报告', '自动化度量报告']
        },
        {
          path: '/client/autotestMeasure',
          component: autotestMeasure,
          meta: { title: '自动化度量详情' }
        },
        {
          path: '/contentNF',
          component: contentNF,
          meta: ['Home', 'NotFound']
        },
        {
          path: 'businessDetail/:businessId',
          name: 'businessDetail',
          component: businessHomePage,
          meta: { title: '方向详情' },
          props: true,
          children: [
            {
              path: 'base',
              name: 'base',
              component: AutotestBasicConfig,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'strategy',
              name: 'strategy',
              component: AutotestStrategyConfig,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'page',
              name: 'page',
              component: PageCenter,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'data',
              name: 'data',
              component: AutotestShortLinkAssistAndVisionAssistResult,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'trigger',
              name: 'trigger',
              component: TriggerList,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'api',
              name: 'api',
              component: ApiList,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'ab',
              name: 'ab',
              component: AbList,
              meta: { title: '方向详情' },
              props: route => ({ tab: route.name })
            }
          ]
        },
        {
          path: 'page/:pageId',
          name: 'pageDetail',
          component: PageDetail,
          props: true,
          meta: { title: '页面详情' },
          children: [
            {
              path: 'scene',
              name: 'scene',
              component: SceneCenter,
              meta: { title: '页面详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'url',
              name: 'url',
              component: UrlModel,
              meta: { title: '页面详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'test',
              name: 'test',
              component: TestCase,
              meta: { title: '页面详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'base',
              name: 'pageInfo',
              component: PageInfo,
              meta: { title: '页面详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'api',
              name: 'apiInfo',
              component: PageApiInfo,
              meta: { title: '页面详情' },
              props: route => ({ tab: route.name })
            }
          ]
        },
        {
          path: 'scene/:sceneId',
          name: 'sceneDetail',
          component: SceneDetail,
          props: true,
          meta: { title: '场景详情' },
          children: [
            {
              path: 'base',
              name: 'sceneInfo',
              component: SceneInfo,
              meta: { title: '场景详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'inspect',
              name: 'inspect',
              component: HJInspect,
              meta: { title: '场景详情' },
              props: route => ({ tab: route.name })
            },
            {
              path: 'case',
              name: 'case',
              component: CaseDetail,
              meta: { title: '场景详情' },
              props: route => ({ tab: route.name })
            }
          ]
        },
        {
          path: 'detail/api/:apiId',
          name: 'apiDetail',
          component: ApiDetail,
          props: true,
          meta: { title: '接口详情' }
        },
        {
          path: 'detail/ab/:abId',
          name: 'abDetail',
          component: AbDetail,
          props: true,
          meta: { title: 'AB实验详情' }
        }
      ]
    },
    {
      path: '*',
      name: 'contentNotFound',
      meta: {
        title: '迷路了...'
      },
      component: ContentNotFound
    }
  ]
})

const routerPush = Router.prototype.push
Router.prototype.push = function(location) {
  return routerPush.call(this, location).catch(err => err)
}

router.beforeEach((to, from, next) => {
  if (to.meta.hasOwnProperty('title')) {
    document.title = to.meta.title
  } else {
    document.title = to.meta[1]
  }
  if (from.name === 'detailReport') {
    to.meta.isBack = true
  }
  next()
})

export default router
