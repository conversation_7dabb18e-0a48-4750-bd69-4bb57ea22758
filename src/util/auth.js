import axios from 'axios/index'
import { Bus } from '@/global/bus'

export default {
  install (Vue, options) {
    // 获取服务的domain
    Vue.prototype.authTicePipeline = function (mis, key) {
      let status = false
      let sysAdmin = ['liuzhenzhou', 'wangpeng54', 'liyonggang', 'chenchaoyi']
      let admin = []
      if (sysAdmin.indexOf(mis) !== -1) {
        return true
      } else {
        switch (key) {
          case 'config-admin':
            admin = ['hanying05', 'lijingqi', 'zhaoshimei', 'yumingjuan', 'gongxixi', 'chenxiang06', 'huangweiwei', 'lijie.cui', 'zhangmiao07', 'lizhen18']
            status = admin.indexOf(mis) !== -1
            break
          case 'config-workflow':
            admin = []
            status = admin.indexOf(mis) !== -1
            break
        }
        return status
      }
    }
    Vue.prototype.authDirection = function (mis, key) {
      let sysAdmin = ['liuzhenzhou', 'wangpeng54', 'liyonggang', 'chenchaoyi', 'zhangmiao07', 'jiachenyu02', 'sunbo09', 'zhangyong23', 'xubangzhi', 'wangwenhao02']
      return sysAdmin.indexOf(mis) !== -1
    }
    Vue.prototype.toolchainAuth = function (mis) {
      let sysAdmin = ['liuzhenzhou', 'wangpeng54', 'zhangmiao07', 'sunbo09', 'zhangyong23', 'xubangzhi', 'wangwenhao02']
      return sysAdmin.indexOf(mis) !== -1
    }
    Vue.prototype.isHaveRootAuth = function (self, mis, callback = false) {
      let url = self.getDomain('cq') + '/portal/config/query'
      axios.post(url, JSON.stringify({
        type: 'tool_site_root_person'
      })).then(function (message) {
        if (message.data.status === 'success') {
          let data = message.data.data
          if (data.length > 0) {
            let rootList = data[0].config.rootPersonList
            let misList = []
            rootList.forEach(function (item) {
              misList.push(item.mis)
            })
            self.haveAuth = misList.indexOf(mis) !== -1
            if (!self.haveAuth) {
              Bus.$emit('refreshContentNotAuth401')
            }
          } else {
            self.haveAuth = false
            Bus.$emit('refreshContentNotAuth401')
          }
        } else {
          self.haveAuth = false
          Bus.$emit('refreshContentNotAuth401')
        }
        if (!self.haveAuth && callback) {
          self.noAuthCallBack()
        }
      })
    }
  }
}
