import Highcharts from 'highcharts/highstock'
import HighchartsMore from 'highcharts/highcharts-more'

HighchartsMore(Highcharts)

export default {
  install (Vue, options) {
    // 获取服务的domain
    Vue.prototype.getDomain = function (service) {
      let result = document.location.protocol + '//'
      switch (service) {
        case 'cq':
          result += 'qa.sankuai.com/cq/cq'
          break
        case 'common':
          result = 'http://common.vip.sankuai.com'
          break
        case 'common-test':
          result = 'http://common.hotel.test.sankuai.com'
          break
        case 'config':
          result += 'config-hotel.sankuai.com'
          break
        case 'config-test':
          result += 'config.hotel.test.sankuai.com'
          break
        case 'account':
          result += 'pm.sankuai.com/hook'
          break
        case 'envmonitor':
          result += 'envmonitor.hotel.test.sankuai.com'
          break
        case 'rq':
          result += '************:8488'
          break
        case 'cover-holiday':
          result += '************:8080'
          break
        case 'ticePipeline':
          result += 'ticepipeline.hotel.vip.sankuai.com'
          break
        case 'pm':
          result += 'pm.sankuai.com'
          break
        case 'servermetric':
          result = 'http://analytics.hotel.test.sankuai.com'
          break
        case 'goodsData':
          result = 'http://************:8123'
          break
        case 'env-monitor':
          result = 'http://qa.sankuai.com/env-monitor'
          break
        case 'client':
          result = 'http://***************:8990'
          break
        case 'cd':
          result += 'qa.sankuai.com/data/cd'
      }
      return result
    }

    // 获取时间String YYYY-MM-DD hh:mm:ss
    Vue.prototype.getFormatTime = function (date) {
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      let hour = date.getHours()
      let minute = date.getMinutes()
      let second = date.getSeconds()
      let totalResult = ''
      let YmdResult = ''
      let hmsResult = ''
      totalResult += year + '-'
      YmdResult += year + '-'
      if (month >= 10) {
        totalResult += month + '-'
        YmdResult += month + '-'
      } else {
        totalResult += '0' + month + '-'
        YmdResult += '0' + month + '-'
      }
      if (day >= 10) {
        totalResult += day + ' '
        YmdResult += day + ' '
      } else {
        totalResult += '0' + day + ' '
        YmdResult += '0' + day
      }
      if (hour >= 10) {
        totalResult += hour + ':'
        hmsResult += hour + ':'
      } else {
        totalResult += '0' + hour + ':'
        hmsResult += '0' + hour + ':'
      }
      if (minute >= 10) {
        totalResult += minute + ':'
        hmsResult += minute + ':'
      } else {
        totalResult += '0' + minute + ':'
        hmsResult += '0' + minute + ':'
      }
      if (second >= 10) {
        totalResult += second
        hmsResult += second
      } else {
        totalResult += '0' + second
        hmsResult += '0' + second
      }
      return {
        YMDhms: totalResult,
        YMD: YmdResult,
        hms: hmsResult,
        year: year,
        mounth: month,
        day: day,
        hour: hour,
        minute: minute,
        second: second,
        q: Math.floor((month - 1) / 3 + 1)
      }
    }

    Vue.prototype.sortbyTime = function (arrayTime) {
      if (arrayTime) {
        for (let i = arrayTime.length - 1; i > 0; i -= 1) {
          for (let j = 0; j < i; j += 1) {
            let baseyear = parseInt(arrayTime[j].split('-')[0])
            let basemonth = parseInt(arrayTime[j].split('-')[1])
            let year = parseInt(arrayTime[j + 1].split('-')[0])
            let month = parseInt(arrayTime[j + 1].split('-')[1])
            if ((baseyear > year) || (baseyear === year && basemonth > month)) {
              const temp = arrayTime[j]
              arrayTime[j] = arrayTime[j + 1]
              arrayTime[j + 1] = temp
            }
          }
        }
      }
      return arrayTime
    }

    Vue.prototype.sortArraybyTime = function (arrayTime) {
      if (arrayTime) {
        for (let i = arrayTime.length - 1; i > 0; i -= 1) {
          for (let j = 0; j < i; j += 1) {
            let baseyear = parseInt(arrayTime[j].period.split('-')[0])
            let basemonth = parseInt(arrayTime[j].period.split('-')[1])
            let year = parseInt(arrayTime[j + 1].period.split('-')[0])
            let month = parseInt(arrayTime[j + 1].period.split('-')[1])
            if ((baseyear > year) || (baseyear === year && basemonth > month)) {
              let temp = arrayTime[j]
              arrayTime[j] = arrayTime[j + 1]
              arrayTime[j + 1] = temp
            }
          }
        }
      }
      return arrayTime
    }

    // 数组按时间顺序排序
    Vue.prototype.sortArray = function (array) {
      var directionlist = []
      var datasource = {}
      var result = []
      if (array) {
        for (let each in array) {
          if (directionlist.indexOf(array[each].direction) !== -1) {
            datasource[array[each].direction].push(array[each])
          } else {
            directionlist.push(array[each].direction)
            datasource[array[each].direction] = []
            datasource[array[each].direction].push(array[each])
          }
        }
        for (let e in datasource) {
          this.sortArraybyTime(datasource[e])
          for (let each in datasource[e]) {
            result.push(datasource[e][each])
          }
        }
      }
      return result
    }

    Vue.prototype.sortbyKey = function (array, item) {
      return array.sort(this.compare(item))
    }

    Vue.prototype.compare = function (property) {
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        if (value1 < value2) {
          return -1
        }
        if (value1 > value2) {
          return 1
        }
        return 0
      }
    }

    // 获取cookie
    Vue.prototype.getCookie = function (name) {
      let reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
      let arr = document.cookie.match(reg)
      if (arr) {
        return (arr[2])
      } else {
        return null
      }
    }

    // 调试使用cookie
    Vue.prototype.getDefaultCookie = function () {
      return '0b9fc452a3*245bdb33c38f7558dc0d9'
    }

    // 2个数组去重相加, key为键值，仅适用于当数组内的元素为对象时。
    Vue.prototype.arrayUniqueConcat = function (arrayA, arrayB, key = null) {
      if (key === null) {
        let temp = arrayA.concat(arrayB)
        return Array.from(new Set(temp))
      } else {
        let keyArray = []
        for (let item of arrayA) {
          if (keyArray.indexOf(item[key]) < 0) {
            keyArray.push(item[key])
          }
        }
        return arrayA.concat(arrayB.filter(function (element) {
          if (keyArray.indexOf(element[key]) < 0) {
            return element
          }
        }))
      }
    }

    // 列表深拷贝
    Vue.prototype.deepCopyList = function (temp) {
      return JSON.parse(JSON.stringify(temp))
    }

    // 更新数组中的数据
    Vue.prototype.updateArrayList = function (Array, sum) {
      const temp = JSON.parse(JSON.stringify(Array))
      for (const each in temp) {
        if (sum === 0) {
          temp.set(each, 0)
        } else {
          temp.set(each, temp[each] / sum)
        }
      }
      return temp
    }

    // 根据SSH地址获取Addr信息
    Vue.prototype.getRepoInfoFromSSHAddr = function (sshAddr) {
      if (sshAddr) {
        let temp = sshAddr.toString().split('/')
        let server = 'sankuai'
        if (sshAddr.indexOf('git.dianpingoa.com') !== -1) {
          server = 'dianpingoa'
        }
        return {
          name: temp[temp.length - 1].split('.git')[0],
          project: temp[temp.length - 2],
          server: server
        }
      } else {
        return {
          name: '',
          project: '',
          server: ''
        }
      }
    }
    Vue.prototype.getUnion = function (a, b) {
      // 并集
      return Array.from(new Set(a.concat(b)))
    }

    Vue.prototype.getIntersection = function (a, b) {
      // 交集
      let bSet = new Set(b)
      return Array.from(new Set(a.filter(v => bSet.has(v))))
    }

    Vue.prototype.getDifference = function (a, b) {
      // 差集
      let aSet = new Set(a)
      let bSet = new Set(b)
      return Array.from(new Set(a.concat(b).filter(v => !aSet.has(v) || !bSet.has(v))))
    }

    Vue.prototype.sumOfList = function (array) {
      let sum = 0
      for (let item of array) {
        sum += item
      }
      return sum
    }

    // 满屏高度
    Vue.prototype.getToolchainScreeHeight = function (type = null) {
      if (!type) {
        return (window.screen.height - 250).toString() + 'px'
      } else {
        if (type === 'half') {
          return ((window.screen.height - 290) / 2).toString() + 'px'
        }
        if (type === 'quarter') {
          return ((window.screen.height - 330) / 4).toString() + 'px'
        }
        if (type === 'line') {
          return '50px'
        }
        if (type === 'full') {
          return (window.screen.height - 180).toString() + 'px'
        }
        if (type === 'home') {
          return (window.screen.height - 320).toString() + 'px'
        }
      }
    }

    // highcharts 图标 柱状图
    // chartname: highchart定义名称 categories:横坐标 series：数据
    Vue.prototype.highchartscolumn = function (chartname, categories, series, stack, charttitle, subtitle, suffix, isxAxisallowDecimal, isyAxisallowDecimal, colorset) {
      if (!charttitle) {
        charttitle = ''
      }
      if (!subtitle) {
        subtitle = ''
      }
      if (!isxAxisallowDecimal) {
        isxAxisallowDecimal = false
      }
      if (!isyAxisallowDecimal) {
        isyAxisallowDecimal = false
      }
      if (!stack) {
        charttitle = 'normal'
      }
      if (!suffix) {
        suffix = ''
      }
      if (!colorset) {
        colorset = ['#c581d6', '#20B2AA', '#FFA500', '#8192D6', '#c1405a', '#434348', '#90ed7d']
      }
      Highcharts.chart(chartname, {
        chart: {
          type: 'column'
          // inverted: false
        },
        title: {
          text: charttitle
        },
        subtitle: {
          text: subtitle
        },
        xAxis: {
          categories: categories,
          allowDecimals: isxAxisallowDecimal
        },
        yAxis: {
          min: 0,
          title: {
            text: null
          },
          allowDecimals: isyAxisallowDecimal
        },
        plotOptions: {
          column: {
            stacking: stack
          }
        },
        tooltip: {
          valueSuffix: suffix
        },
        series: series,
        colors: colorset,
        credits: {
          enabled: false
        }
      })
    }

    // 饼图
    Vue.prototype.highchartspie = function (chartname, data, colorset) {
      if (!colorset) {
        colorset = ['#c581d6', '#20B2AA', '#FFA500', '#8192D6', '#c1405a', '#434348', '#90ed7d']
      }
      Highcharts.chart(chartname, {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false,
          type: 'pie'
        },
        colors: colorset,
        title: {
          text: ''
        },
        tooltip: {
          pointFormat: '<b>{point.percentage:.2f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: false,
            cursor: 'pointer',
            dataLabels: {
              enabled: true,
              formatter: function () {
                if (this.percentage > 0 && this.percentage !== 100) {
                  return this.y
                }
              }
            },
            showInLegend: true
          }
        },
        series: [{
          data: data
        }],
        credits: {
          enabled: false
        }
      })
    }
  }
}
