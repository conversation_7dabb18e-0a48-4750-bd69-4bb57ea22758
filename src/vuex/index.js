import Vue from 'vue'
import Vuex from 'vuex'
import state from './state'
import mutations from './mutations'
import ClientAnalytic from '../components/ClientQuality/ClientAnalytic/state/index'
import clientQuality from '../components/ClientQuality/state/index'
import COEStore from '../components/CoeAnalysis/store/index'
import CampaignStore from '../components/CampaignActivityAnalysis/store/index'
import LLMEvalStorage from '../components/LLMEvals/store'

Vue.use(Vuex)
const debug = process.env.NODE_ENV !== 'prod'

export default new Vuex.Store({
  state,
  mutations,
  strict: debug,
  modules: {
    ClientAnalytic,
    clientQuality,
    COEStore,
    CampaignStore,
    LLMEvalStorage
  }
})
