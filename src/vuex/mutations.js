// export default {
//   mutations: {
//     setDirectionIdList (state, data) {
//       console.log('multation', data)
//       state.directionIdList = JSON.parse(JSON.stringify(data))
//     }
//   }
// }
const mutation = {
  setDirectionList (state, data) {
    state.directionList = JSON.parse(JSON.stringify(data))
  },
  setDirectionIdList (state, data) {
    state.directionIdList = JSON.parse(JSON.stringify(data))
  },
  setsubDirectionList (state, data) {
    state.subdirectionList = JSON.parse(JSON.stringify(data))
  },
  setsubDirectionIdList (state, data) {
    state.subdirectionIdList = JSON.parse(JSON.stringify(data))
  },
  setPersonList (state, data) {
    state.personList = JSON.parse(JSON.stringify(data))
  }
}
export default mutation
