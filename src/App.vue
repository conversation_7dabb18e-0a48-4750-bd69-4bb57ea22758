<template>
  <div id="app">
    <!--<router-view/>-->
    <div v-if="mis !== 'undefined'">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive">
        </router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive">
      </router-view>
      <lg-preview></lg-preview>
    </div>
    <sso-error v-else :msg="msg"></sso-error>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import SsoError from './components/Common/SSOError'
  export default {
    name: 'app',
    components: {SsoError},
    data: function () {
      return {
        mis: Bus.userInfo.userLogin,
        msg: Bus.userInfo.msg
      }
    }
  }
</script>

<style>

</style>
