<template>
  <div>
    <head-component style="margin-bottom: 15px"></head-component>
    <Row class="clientHeader">
      <Col span="16">
        <Span style="font-size: 24px;padding-left: 5px">客户端质量监控平台</Span>
      </Col>
      <Col span="2">
        <Select v-model="app" Style="width: 80px" @on-change="getDetailList">
          <Option v-for="(item, index) in appList" :value="item.value" :key="index">{{item.label}}</Option>
        </Select>
      </Col>
      <Col span="2">
        <Select v-model="platform" Style="width: 80px" @on-change="getDetailList">
          <Option v-for="(item, index) in platformList" :value="item.value" :key="index">{{item.label}}</Option>
        </Select>
      </Col>
      <Col span="2">
        <Select v-model="channel" Style="width: 100px" @on-change="getDetailList">
          <Option v-for="(item, index) in channelList" :value="item.label" :key="index">{{item.label}}</Option>
        </Select>
      </Col>
      <Col span="2">
        <Select v-model="version" Style="width: 100px" @on-change="getDetailList">
          <Option v-for="(item, index) in versionList" :value="item.value" :key="index">{{item.label}}</Option>
        </Select>
      </Col>
    </Row>
    <Alert v-if="isUnActive" type="error" Style="margin: 20px">
      服务错误
      <span slot="desc">
        服务器未返回有效结果
      </span>
    </Alert>
    <Row :gutter="4">
      <Col v-for="(build, index) in detailList" :key=index span="6">
        <div class="build-card">
          <Row>
            <Col  span="1">
              <div v-if="build.result===2" style="background-color: #00D1C3" class="build-card-result">
              </div>
              <div v-else-if="build.result===0" style="background-color: #19be6b" class="build-card-result">
              </div>
              <div v-else-if="build.result===1" style="background-color: #ed4014" class="build-card-result">
              </div>
              <div v-else style="background-color: #80848f" class="build-card-result">
              </div>
            </Col>
            <Col span="23" style="padding: 10px;">
              <a style="font-size: 16px;font-weight: bold" :href="build.buildUrl" target="view_window">{{build.buildId}}</a>
              <span style="font-size: 12px;margin-left: 10px;margin-right: 10px">{{build.buildTime}}</span>
              <Tag v-for="(tag, index) in build.channels" style="margin-left: 5px"  :key="index">{{tag}}</Tag>
              <div style="padding-top: 10px">
                <div v-for="type in build.typeResult" style="padding-left: 10px;padding-top: 5px" :key="type">
                  <Icon v-if="type.result===0" type="md-checkmark-circle" color="#19be6b" size="18"></Icon>
                  <Icon v-else-if="type.result===1" type="md-close-circle" color="#ed4014" size="18"></Icon>
                  <Icon v-else-if="type.result===2" type="ios-loading" color="#00D1C3" size="18"></Icon>
                  <Icon v-else type="md-remove-circle" color="#808695" size="18"></Icon>
                  {{type.autoType}}
                  <div style="display: block"></div>
                    <div v-for="channel in type.channel" style="padding-left: 10px;display: inline-block" :key="channel">
                      <Icon v-if="channel.autoResult===0" type="md-checkmark-circle" color="#19be6b" size="14"></Icon>
                      <Icon v-else-if="channel.autoResult===1" type="md-close-circle" color="#ed4014" size="14"></Icon>
                      <Icon v-else-if="channel.autoResult===2" type="ios-loading" color="#00D1C3" size="14" ></Icon>
                      <Icon v-else type="md-remove-circle" color="#808695" size="12"></Icon>
                      <a :href="channel.resultAddress" target="view_window">{{channel.autoChannel}}</a>
                    </div>
                  <div style="clear: both"></div>
                </div>
              </div>

            </Col>
          </Row>
        </div>
      </Col>
    </Row>


    <Page class="clientPage" :page-size=pageSize :total=totalPage :current=page_index @on-change="changePage"></Page>

  </div>
</template>

<script>
  import axios from 'axios'
  export default {
    name: 'client-quality-platform',
    data () {
      return {
        appList: [
          {
            value: '1',
            label: '美团'
          },
          {
            value: '2',
            label: '点评'
          }
        ],
        app: '1',
        platformList: [
          {
            value: '1',
            label: 'Android'
          },
          {
            value: '2',
            label: 'iOS'
          }
        ],
        channelList: [
          {
            label: 'Hotel'
          },
          {
            label: 'Overseahotel'
          },
          {
            label: 'Train'
          },
          {
            label: 'Flight'
          },
          {
            label: 'Oversea'
          },
          {
            label: 'Travel'
          },
          {
            label: '功能测试包-提测触发'
          }
        ],
        channel: 'Hotel',
        platform: '1',
        versionList: [],
        version: '9.7.0',
        detailList: [],
        pageSize: 20,
        totalPage: 100,
        currentPage: 1,
        page_index: 1,
        isUnActive: true
      }
    },
    methods: {
      initInfo: function () {
        let self = this
        let url = this.getDomain('client') + '/monitor/1.0/getAll'
        let params = {
          'app': this.app,
          'platform': this.platform,
          'page_index': this.page_index,
          'page_count': 20,
          'channel': this.channel,
          'is_showall': 1
        }
        axios.post(url, JSON.parse(JSON.stringify(params))).then(function (message) {
          let data = message.data
          if (data.code === 200) {
            self.detailList = data.data.detail
            self.totalPage = data.data.total_page
            self.version = data.data.versions[0]
            self.createVersionList(data.data.versions)
            self.isUnActive = false
          }
        })
      },
      getDetailList: function () {
        let self = this
        let url = this.getDomain('client') + '/monitor/1.0/getAll'
        let params = {
          'app': this.app,
          'platform': this.platform,
          'app_version': this.version,
          'page_index': this.page_index,
          'channel': this.channel,
          'page_count': 20,
          'is_showall': 1
        }
        axios.post(url, JSON.parse(JSON.stringify(params))).then(function (message) {
          let data = message.data
          console.log(message)
          if (data.code === 200) {
            self.detailList = data.data.detail
            self.totalPage = data.data.total_page
            self.createVersionList(data.data.versions)
            self.isUnActive = false
          }
        })
      },
      changePage (current) {
        this.page_index = current
        this.getDetailList()
      },
      createVersionList: function (versionList) {
        this.versionList = []
        for (let item of versionList) {
          let version = {}
          version.label = item
          version.value = version.label
          this.versionList.push(version)
        }
      },
      getBGColor: function (tag) {
        switch (tag) {
          case 'Hotel':
            return 'rgb(207, 19, 34)'
          case 'Overseahotel':
            return 'rgb(56, 158, 13)'
          case 'Train':
            return 'rgb(255, 122, 69)'
          case 'Flight':
            return 'rgb(64, 168, 255)'
          case 'Travel':
            return 'rgb(89, 126, 247)'
          case 'Oversea':
            return 'rgb(54, 207, 201)'
          case 'Apartment':
            return 'rgb(146, 84, 222)'
          default:
            return 'rgb(75, 75, 75)'
        }
      }
    },
    created: function () {
      this.initInfo()
    }
  }
</script>

<style scoped>
  .clientHeader{
    margin-top: 10px;
    border: solid;
    border-top-width: 0px;
    border-bottom-width: 5px;
    border-bottom-color: #00D1C3;
    border-left-width: 0px;
    border-right-width: 0px;
    padding-top:10px;
  }
  .build-card{
    border: 1px solid #e8eaec;
    margin-top: 10px;
    height: 200px;
  }
  .build-card-result{
    height: 100%;
    width: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
  .clientPage{
    text-align: center;
    margin-top: 10px;
  }
</style>
