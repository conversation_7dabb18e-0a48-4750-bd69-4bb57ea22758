<template>
  <div>
    <head-component id="head"></head-component>
    <div style="padding-top:20px;margin-left: 10%;margin-right: 10%;width: auto">
      <div style="text-align:center;">
        <button type="button" class="btn btn-success" @click ="changeMode()">切换模式</button>
      </div>
      <br/>
      <div :id='id' style="height: 675px;"></div>
    </div>
  </div>
</template>

<script>
  import JSONEditor from 'jsoneditor'
  import 'jsoneditor/dist/jsoneditor.min'
  import 'jsoneditor/dist/jsoneditor.min.css'
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  Vue.component('head-component', Head)
  export default {
    name: 'json-editor',
    data: function () {
      return {
        id: 'jsoneditor',
        useJsonEditor: true,
        jsonEditor: {}
      }
    },
    methods: {
      changeMode () {
        if (this.useJsonEditor) {
          this.jsonEditor.setMode('code')
        } else {
          this.jsonEditor.setMode('tree')
        }
        this.useJsonEditor = !this.useJsonEditor
      }
    },
    mounted: function () {
      let container = document.getElementById(this.id)
      let options = {
        // onChange: function () {}
      }
      this.jsonEditor = new JSONEditor(container, options)
      let sourceJson = {
        'Array': [1, 2, 3],
        'Boolean': true,
        'Null': null,
        'Number': 123,
        'Object': {'a': 'b', 'c': 'd'},
        'String': 'Hello World'
      }
      this.jsonEditor.set(sourceJson)
    }
  }
</script>

<style scoped>
</style>
