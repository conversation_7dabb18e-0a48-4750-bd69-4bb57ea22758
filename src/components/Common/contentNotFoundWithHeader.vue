<template>
  <div>
    <head-component></head-component>
    <Card :style="notExistCardStyle">
      <div style="text-align: center">
        <img src="../../../static/contentNotFound.png" data-holder-rendered="true" style="height: 300px; width: 250px;padding-top: 150px">
      </div>
      <div style="text-align: center;font-weight: bolder;color: #666666;padding-top: 15px">{{msg}}</div>
    </Card>
  </div>
</template>

<script>
  let screeHeight = window.screen.height
  let notExistCardStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '15px',
    'minHeight': (screeHeight - 205).toString() + 'px',
    'marginBottom': '20px'
  }

  export default {
    name: 'content-not-found-with-header',
    props: {
      msg: {
        type: String,
        default: '╮(╯_╰)╭ 诶，好像迷路了......'
      }
    },
    data: function () {
      return {
        notExistCardStyle: notExistCardStyle
      }
    }
  }
</script>

<style scoped>

</style>
