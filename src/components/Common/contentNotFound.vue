<template>
  <Card :style="notExistCardStyle">
    <div style="text-align: center">
      <img src="../../../static/contentNotFound.png" data-holder-rendered="true" style="height: 300px; width: 250px;padding-top: 150px">
    </div>
    <div style="text-align: center;font-weight: bolder;color: #666666;padding-top: 15px">{{msg}}</div>
  </Card>
</template>

<script>
  let screeHeight = window.screen.height
  let notExistCardStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '15px',
    'minHeight': (screeHeight - 205).toString() + 'px',
    'marginBottom': '20px'
  }

  export default {
    name: 'content-not-found',
    props: {
      msg: {
        type: String,
        default: '加载中'
      }
    },
    data: function () {
      return {
        notExistCardStyle: notExistCardStyle
      }
    }
  }
</script>

<style scoped>

</style>
