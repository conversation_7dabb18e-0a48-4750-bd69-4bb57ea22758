<template>
  <div>
    <div style="text-align: center">
      <img src="../../../static/contentNotFound.png" data-holder-rendered="true" style="height: 300px; width: 250px;padding-top: 150px">
    </div>
    <div style="text-align: center;font-weight: bolder;color: #666666;padding-top: 15px">
      <div>数据获取失败！</div>
      <div style="margin-top: 10px">错误原因: <span style="font-weight: bolder;padding-left: 3px; color: #1a1a1a">{{msg}}</span></div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'content-not-found-without-content',
    props: {
      msg: {
        type: String,
        default: '╮(╯_╰)╭ 诶，好像迷路了......'
      }
    }
  }
</script>

<style scoped>

</style>
