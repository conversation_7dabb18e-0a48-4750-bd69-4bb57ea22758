<template>
  <div>
    <head-component></head-component>
    <Breadcrumb v-if="needBreadCrumb" style="margin-top: 15px; margin-left: 1%">
      <BreadcrumbItem to="/">首页</BreadcrumbItem>
    </Breadcrumb>
    <Card :style="notExistCardStyle" dis-hover :bordered="needBorder">
      <div style="display: flex; justify-content: center">
        <div style="text-align: right; width: 50%" v-if="code !== 100">
          <img src="../../../static/404.svg" data-holder-rendered="true" style="height: 500px; width: 500px;padding-top: 150px; opacity: 0.7">
        </div>
        <div style="text-align: right; width: 50%" v-else>
          <img src="../../../static/200.svg" data-holder-rendered="true" style="height: 500px; width: 500px;padding-top: 150px; opacity: 0.7">
        </div>
        <div style="text-align: left; width: 50%; padding-top: 250px">
          <div style="color: #515A6E; font-size: 72px; padding-left: 100px; font-weight: bolder">{{code}}</div>
          <div style="color: #808695; font-weight:400; padding-top: 5px; font-size: 22px; padding-left: 100px">{{msg}}</div>
          <div v-if="needButtonGroup" style="padding-left: 100px; margin-top: 15px">
            <Button v-for="(button, index) of buttonGroupData" :key="index" :to="button.url">{{button.text}}</Button>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  let screeHeight = window.screen.height
  let notExistCardStyle = {
    'marginLeft': '1%',
    'marginRight': '1%',
    'marginTop': '10px',
    'minHeight': (screeHeight - 215).toString() + 'px',
    'marginBottom': '20px'
  }
  Bus.$on('refreshNotFoundComponents404', function (msg = null, code = -1) {
    if (msg !== null) {
      Bus.NotFoundContent.msg = msg
    } else {
      Bus.NotFoundContent.msg = '╮(╯_╰)╭ 诶，好像迷路了......'
    }

    if (code !== -1) {
      Bus.NotFoundContent.code = code
    } else {
      Bus.NotFoundContent.code = 404
    }
  })

  export default {
    name: 'content-not-found',
    components: {},
    props: {
      needHeader: {
        type: Boolean,
        default: true
      },
      needBreadCrumb: {
        type: Boolean,
        default: true
      },
      needBorder: {
        type: Boolean,
        default: true
      },
      needButtonGroup: {
        type: Boolean,
        default: false
      },
      buttonGroupData: {
        type: Array,
        default: () => []
      }
    },
    data: function () {
      return {
        msg: '加载中',
        code: 100,
        notExistCardStyle: notExistCardStyle
      }
    },
    mounted: function () {
      Bus.NotFoundContent = this
    }
  }
</script>

<style scoped>

</style>
