<template>
  <div class="layout">
    <Layout>
      <Header id="header" :style="{background: '#17233d',height:'50px'}">
        <Row type="flex">
          <Col order="1" :xs="24" :sm="12" :md="8" :lg="6">
            <a href="/"><img class="layout-logo" src="/static/mqp_logo.png" style="margin-left: 0; padding-top:10px;height:40px;width:120px;cursor: pointer"></a>
            <!--<img class="layout-logo" src="/static/hbtrip_logo.png" style="height:50px;width:165px;cursor: pointer">-->
            <!--<div style="color:#ffffff;font-family: 'STXihei';margin-top: -5px;cursor: pointer">• <span style="font-weight: bold;font-size: 16px">酒旅测试组</span></div>-->
          </Col>
          <Col order="2" :xs="0" :sm="12" :md="11" :lg="14" style="text-align: right; height: 50px;">
            <marquee style="width: 300px; color: #ffffff; font-weight: bolder; margin: auto; height: 50px; margin-top: -5px;padding-right: 20px" v-if="topNoticeEnable">
              <Icon type="md-volume-down"/>{{topNotice}}
            </marquee>
          </Col>
          <Col order="3" :xs="0" :sm="0" :md="2" :lg="2" style="height: 50px; margin-top: -5px; text-align: center; font-weight: bolder">
            <div style="margin-left: 25%; font-weight: bolder">
              <Dropdown>
                <a style="color: #ffffff; text-decoration: none; font-weight: bolder">
                  技术支持
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <img src="../../../static/qrCode.png" data-holder-rendered="true" style="height: 400px; width: 350px">
                </DropdownMenu>
              </Dropdown>
            </div>
          </Col>
          <Col order="4" :xs="0" :sm="0" :md="3" :lg="2">
            <div class="layout-nav" style="margin-left:25%; width: 100%; margin-right: 0">
              <div id='userInfo' style="display: flex;color: #ffffff">
                <div v-if="!userInfo.userUrl"><Avatar icon="md-person"/></div>
                <div v-else><Avatar icon="md-person" /></div>
                <Dropdown @on-click="exitSystem">
                  <a style="color: #ffffff; text-decoration: none; font-weight: bolder">
                    <div style="padding-left: 10px; margin-top: 2px">{{userInfo.userName}}<Icon type="ios-arrow-down"></Icon></div>
                  </a>
                  <DropdownMenu slot="list">
                    <DropdownItem style="text-align: center" name="quit"><Icon type="md-exit" style="padding-right: 5px"/>退出</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
            </div>
          </Col>
        </Row>
      </Header>
    </Layout>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import axios from 'axios'
  let userInfo = {
    userId: '',
    userName: 'Dev',
    userLogin: 'Dev',
    userUrl: ''
  }

  Bus.$on('refreshUserInfo', function (UserInfo) {
    userInfo = UserInfo
  })

  export default {
    data: function () {
      return {
        filter: '',
        userInfo: userInfo,
        loading: false,
        topNotice: '',
        topNoticeEnable: false
      }
    },
    methods: {
      refreshUserInfo (userInfo) {
        this.userName = userInfo.userName
      },
      jumper (value) {
        if (value) {
          let index = value.indexOf('#-#')
          let url = value.substring(0, index)
          let type = parseInt(value.substring(index + 3, index.length))
          this.filter = ''
          this.value = ''
          if (type === 1) {
            if (url.toString() !== '/') {
              url = '/' + url.toString()
            }
            this.$router.push({path: url})
          } else {
            window.open(url)
          }
        }
      },
      getTopNotice () {
        let self = this
        let url = this.getDomain('cq') + '/portal/config/query'
        axios.post(url, JSON.stringify({
          'type': 'top_notice'
        })).then(function (message) {
          if (message.data.status === 'success') {
            let data = message.data.data
            if (data.length > 0) {
              self.topNotice = data[0].config.data
              self.topNoticeEnable = data[0].config.enable
            }
          }
        })
      },
      exitSystem (value) {
        console.log(value)
        if (value === 'quit') {
          // 退出
          window.location.href = window.location.href
          Bus.ssoWeb.logout()
        }
      }
    },
    mounted: function () {
      let self = this
      this.getTopNotice()
      setInterval(function () {
        self.getTopNotice()
      }, 1000 * 60 * 5)
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .layout{
    background: #020031;
    position: relative;
    /*overflow: hidden;*/
  }
  .layout-logo{
    height:50px;
    width:200px;
    position: relative;
    float: left;
    top: 0;
    left: 0;
  }
  .layout-nav{
    text-align: right;
    width: 50px;
    margin: 0 auto;
    margin-right: 0;
    font-weight: bolder;
    margin-top: -8px;
    font-size: 14px;
  }
</style>
