<template>
  <div class="layout" style="background-color: #ffffff">
    <Layout>
      <div id="header" :style="{background: '#f5f7f9',height:'75px',textAlign:'center'}">
        <div class="footer">
          <div class="footer-content">
            © 2015-{{year}} Copyright
            <span style="color: #7c7c7c;">到店平台测试中心</span>
            <a href="https://123.sankuai.com/" target="_blank"><img src="/static/mt_logo.png" style="width: 50px;margin-top: -4px; margin-left: 2px"/></a>
            <Row style="margin-top: 5px">
              <Icon type="md-chatbubbles" style="color: #ff0000;"></Icon><span style="padding-right: 5px"> 北京</span>
              <Icon type="md-chatbubbles" style="color: #ff0000;"></Icon><span style="padding-right: 5px"> 上海</span>
            </Row>
          </div>
        </div>
      </div>
    </Layout>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  let userInfo = {
    userId: '',
    userName: 'Dev',
    userLogin: 'Dev'
  }

  Bus.$on('refreshUserInfo', function (UserInfo) {
    userInfo = UserInfo
  })

export default {
    name: 'common-footer',
    data: function () {
      return {
        userInfo: userInfo,
        year: '2018'
      }
    },
    methods: {
      refreshUserInfo (userInfo) {
        this.userName = userInfo.userName
      },
      currentYear: function () {
        let date = new Date()
        this.year = this.getFormatTime(date)['year']
      }
    },
    mounted: function () {
      this.currentYear()
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .layout{
    background: #020031;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    margin-top: 15px;
  }
  .footer{
    padding-bottom: 10px;
    text-align: center;
    color: #7c7c7c;
    font-size: 14px;
    font-weight: 800;
  }
  .footer-content{
    border-top: #dddee1 1px;
    padding-top: 10px;
    width: 100%;
  }
</style>
