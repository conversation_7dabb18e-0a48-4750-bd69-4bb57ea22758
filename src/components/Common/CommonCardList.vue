<template>
  <div style="width: 100%">
    <Row style="width: 100%" type="flex">
      <Col span="12">
        <Row type="flex">
          <div v-if="filterDirectionList && filterDirectionList.length > 0">
            <Select v-model="filterDirection" placeholder="请选择筛选方向" multiple style="width:auto" label-in-value @on-change="selectfilter">
              <Option v-for="item in filterDirectionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
          <div v-if="filterStageList && filterStageList.length > 0">
            <Select v-model="filterStage" multiple clearable style="width:auto;margin-left: 8px" placeholder="请选择Stage" @on-change="selectfilter">
              <OptionGroup label="正选">
                <Option v-for="item in filterStageList" :value="item.value + '=' + 'true'" :key="item.value">{{ item.label }}</Option>
              </OptionGroup>
              <OptionGroup label="反选">
                <Option v-for="item in filterStageListSelect" :value="item.value + '=' + 'false'" :key="item.value">{{ item.label }}</Option>
              </OptionGroup>
            </Select>
          </div>
          <div v-if="type === 'table'" style="margin-left: 3px;">
            <Button icon="ios-download-outline" size="small" type="dashed" class="tool-button" style="margin-top: 4px"
                    @click="exportCsv">导出
            </Button>
          </div>
        </Row>
      </Col>
      <Col span="12">
        <div style="text-align: right; margin-bottom: 15px;">
          <Input v-model="search" suffix="ios-search" placeholder="搜索查询" style="width: 50%" @on-change="filter"/>
        </div>
      </Col>
    </Row>
    <Row v-if="type === 'card'">
      <Col :span="span" v-for="(item, index) of displayList" :key="index">
        <slot name="customContent" :customdata="item"></slot>
      </Col>
    </Row>
    <Row v-if="type === 'table'">
      <Col :span="span">
        <Table stripe border :columns="customColumn" :data="displayList" ref="filtertable"></Table>
        <!--<slot name="customTable" :tabledata="displayList"></slot>-->
      </Col>
    </Row>
    <Row v-if="totalSize > pageSize">
      <div style="text-align: right;margin-top: 15px">
        <Page :total="totalSize" show-total @on-change="changePage" :current='currentPage' :page-size="pageSize"/>
      </div>
    </Row>
  </div>
</template>

<script>
  export default {
    name: 'common-card-list',
    props: {
      type: {
        type: String,
        default: 'card'
      },
      filterlist: {
        type: Array,
        default: function () {
          return []
        }
      },
      filterkey: {
        type: String,
        default: ''
      },
      customColumn: {
        type: Array,
        default: function () { return [] }
      },
      stagelist: {
        type: Array,
        default: function () {
          return []
        }
      },
      initaldata: {
        type: Array,
        default: function () { return [] }
      },
      sortKey: {
        type: Array,
        default: function () { return [] }
      },
      sortOrder: {
        type: Array,
        default: function () { return [] }
      },
      keywordlist: {
        type: Array,
        default: function () { return [] }
      },
      span: {
        type: Number,
        default: 4
      },
      row: {
        type: Number,
        default: 10
      },
      id: {
        type: String,
        default: ''
      },
      name: {
        type: String,
        default: ''
      }
    },
    data: function () {
      return {
        search: '',
        backupInitialData: [],
        filterDirection: [],
        currentPage: 1,
        totalSize: 0,
        pageSize: 0,
        filterStage: [],
        filterStageList: this.getStageFilter(),
        filterDirectionList: [],
        filterStageListSelect: [],
        displayList: this.getdisplaydata(),
        sortedInitalData: []
      }
    },
    methods: {
      getfilterdata: function () {
        this.filterDirectionList = []
        let copyfilterlist = JSON.parse(JSON.stringify(this.filterlist))
        for (const each of copyfilterlist) {
          const temp = {
            value: each,
            label: each
          }
          this.filterDirectionList.push(temp)
        }
      },
      getStageFilter: function () {
        this.filterStageList = []
        this.filterStageListSelect = []
        let copyfilterlist = JSON.parse(JSON.stringify(this.stagelist))
        for (const each of copyfilterlist) {
          const temp = {
            value: each.value,
            label: each.label
          }
          const othertemp = {
            value: each.value,
            label: `~${each.label}`
          }
          this.filterStageList.push(temp)
          this.filterStageListSelect.push(othertemp)
        }
      },
      filterStageSet: function (stage) {
        let result = []
        for (var each of this.sortedInitalData) {
          if (each[stage]) {
            result.push(each)
          }
        }
        return result
      },
      exportCsv: function () {
        this.$refs.filtertable.exportCsv({
          filename: '服务详情',
          columns: this.customColumn,
          data: this.backupInitialData
        })
      },
      getdisplaydata: function () {
        if (this.sortKey) {
          let temp = JSON.parse(JSON.stringify(this.initaldata))
          for (let i = this.sortKey.length - 1; i >= 0; i--) {
            temp = this.sortbyKey(temp, this.sortKey[i], this.sortOrder[i])
          }
          this.sortedInitalData = temp
        } else {
          this.sortedInitalData = this.sortbyKey(JSON.parse(JSON.stringify(this.initaldata)))
        }
        this.displayList = []
        this.backupInitialData = []
        this.backupInitialData = JSON.parse(JSON.stringify(this.sortedInitalData))
        this.totalSize = this.backupInitialData.length
        this.pageSize = (24 / this.span) * this.row
        if (this.totalSize < this.pageSize) {
          for (let i = 0; i < this.totalSize; i++) {
            this.displayList.push(this.backupInitialData[i])
          }
        } else {
          for (let i = 0; i < this.pageSize; i++) {
            this.displayList.push(this.backupInitialData[i])
          }
        }
      },
      selectfilter: function () {
        this.displayList = []
        this.backupInitialData = []
        let trueStageSet = []
        let stageSet = []
        if (this.filterStage.length > 0) {
          let trueStageSelect = []
          let falseStageSelect = []
          for (let config of this.filterStage) {
            let stage = config.split('=')[0]
            let state = config.split('=')[1]
            if (state === 'true') {
              trueStageSelect.push(stage)
            } else {
              falseStageSelect.push(stage)
            }
          }
          if (trueStageSelect.length === 0) {
            trueStageSet = JSON.parse(JSON.stringify(this.sortedInitalData))
          } else {
            let tempSet = JSON.parse(JSON.stringify(this.sortedInitalData))
            for (var i = 0; i < trueStageSelect.length; i++) {
              let trueset = []
              for (var each of tempSet) {
                if (each[trueStageSelect[i]]) {
                  trueset.push(each)
                }
              }
              tempSet = JSON.parse(JSON.stringify(trueset))
            }
            trueStageSet = JSON.parse(JSON.stringify(tempSet))
          }
          if (falseStageSelect.length === 0) {
            stageSet = trueStageSet
          } else {
            let tempSet = JSON.parse(JSON.stringify(trueStageSet))
            for (var j = 0; j < falseStageSelect.length; j++) {
              let falseset = []
              for (var eve of tempSet) {
                if (!eve[falseStageSelect[j]]) {
                  falseset.push(eve)
                }
              }
              tempSet = JSON.parse(JSON.stringify(falseset))
            }
            stageSet = JSON.parse(JSON.stringify(tempSet))
          }
        } else {
          stageSet = JSON.parse(JSON.stringify(this.sortedInitalData))
        }
        if (this.filterDirection.length > 0) {
          for (var direction of stageSet) {
            if (this.filterkey) {
              for (var dir = 0; dir < this.filterDirection.length; dir++) {
                if (direction[this.filterkey].indexOf(this.filterDirection[dir]) !== -1) {
                  this.backupInitialData.push(direction)
                  break
                }
              }
            } else {
              this.backupInitialData = JSON.parse(JSON.stringify(stageSet))
            }
          }
          this.changePage(1)
        } else {
          this.backupInitialData = JSON.parse(JSON.stringify(stageSet))
          this.changePage(1)
        }
      },
      filter: function () {
        this.displayList = []
        this.backupInitialData = []
        if (this.search) {
          for (var each of this.sortedInitalData) {
            if (this.keywordlist.length > 0) {
              for (var i = 0; i < this.keywordlist.length; i++) {
                if (each[this.keywordlist[i]].indexOf(this.search.toUpperCase()) !== -1) {
                  this.backupInitialData.push(each)
                  break
                }
              }
            } else {
              if (each.indexOf(this.search.toUpperCase()) !== -1) {
                this.backupInitialData.push(each)
              }
            }
          }
          this.changePage(1)
        } else {
          this.backupInitialData = JSON.parse(JSON.stringify(this.sortedInitalData))
          this.changePage(1)
        }
      },
      changeCurrentPage: function () {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.displayList = []
        let count = this.backupInitialData.length > pageSize * currentPage ? pageSize * currentPage : this.backupInitialData.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.displayList.push(this.backupInitialData[i])
        }
      },
      changePage: function (page) {
        this.currentPage = page
        this.totalSize = this.backupInitialData.length
        this.changeCurrentPage()
      }
    },
    mounted: function () {
      this.getdisplaydata()
    },
    watch: {
      initaldata: function () {
        this.getdisplaydata()
      },
      stagelist: function () {
        this.getStageFilter()
      },
      filterlist: function () {
        this.getfilterdata()
      }
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }
</style>
