<template>
  <div>
    <head-withoutout-person></head-withoutout-person>
    <Card :style="notExistCardStyle">
      <div style="text-align: center">
        <img src="../../../static/qrCode.png" data-holder-rendered="true" style="height: 576px; width: 432px;padding-top: 100px">
      </div>
      <div style="text-align: center;font-weight: bolder;color: #666666;padding-top: 15px">质量保障平台访问失败，失败原因：{{msg}}</div>
    </Card>
  </div>
</template>

<script>
  import HeadWithoutoutPerson from './HeadWithoutPerson'
  let screeHeight = window.screen.height
  let notExistCardStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '15px',
    'minHeight': (screeHeight - 205).toString() + 'px',
    'marginBottom': '20px'
  }

  export default {
    name: 'sso-error',
    props: {
      msg: {
        type: String,
        default: '未知错误'
      }
    },
    components: {HeadWithoutoutPerson},
    data: function () {
      return {
        notExistCardStyle: notExistCardStyle
      }
    }
  }
</script>

<style scoped>

</style>
