<template>
  <div style="cursor: default">
    <tag v-if="type === 'ones'" color="#ffffff"><span style="font-size: 12px;font-weight: bolder;color: #4a9eff">{{name}} ({{id}})</span></tag>
    <tag v-if="type === 'jira'" color="#ffffff"><span style="font-size: 12px;font-weight: bolder;color: #fa8c16">{{name}} ({{id}})</span></tag>
  </div>
</template>

<script>
  export default {
    name: 'direction',
    props: {
      type: {
        type: String,
        default: 'ones'
      },
      id: {
        type: String,
        default: ''
      },
      name: {
        type: String,
        default: ''
      }
    }
  }
</script>

<style scoped>

</style>
