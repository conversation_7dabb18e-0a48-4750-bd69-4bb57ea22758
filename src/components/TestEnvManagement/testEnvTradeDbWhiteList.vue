<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="noteTip">本页面主要用于配置交易平台数据清理白名单，需提供需加白的19位订单号及加白说明！</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span
        class="ciTip">薛朝辉(xuechaohui02)</span></p>
    </Card>
    <i-form :model="whiteListModel" :label-width="100">
      <form-item label="orderId">
        <i-input v-model.trim="whiteListModel.orderId"
                 placeholder="请填写订单号 e.g. *******************"></i-input>
      </form-item>
      <form-item label="description">
        <i-input v-model.trim="whiteListModel.description" placeholder="请填写加白说明 e.g. 交易平台单测订单"></i-input>
      </form-item>
      <div class="test">
        <form-item>
          <i-button type="primary" @click="execute  ">立即触发</i-button>
        </form-item>
      </div>
    </i-form>
  </div>
</template>


<script>
  import axios from 'axios'
  // import { agileReport } from '@/global/variable'
  export default {
    name: 'test-env-trade-Db-White-list',
    data () {
      return {
        page: 'testEnvTradeDbWhiteList',
        whiteListModel: {
          orderId: '',
          description: ''
        }
      }
    },
    methods: {
      execute () {
        let self = this
        axios.post('https://qareport.hotel.test.sankuai.com/tradeDbClean/saveWhiteList', {
          orderId: this.whiteListModel.orderId,
          description: this.whiteListModel.description
        }).then(function (message) {
          if (message.status === 200) {
            self.$Message.success('触发成功，请等一会儿！')
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.message + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  #notice-card {
    margin-bottom: 24px;
  }

  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .noteTip {
    color: #79212e;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .test {
    display: flex;
  }
  .test .ivu-form-item-content {
    margin-left: 20px;
  }
</style>



