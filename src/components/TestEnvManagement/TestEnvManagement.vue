<template>
  <div>
    <head-component></head-component>
    <Tabs class="tab">
<!--      <TabPane label="定时删除部署及泳道2.0升级白名单申请" name="testEnvWhitelist">-->
<!--        <test-env-white-list></test-env-white-list>-->
<!--      </TabPane>-->
      <TabPane label="MCC通知黑名单申请" name="testEnvMccblackList">
        <test-env-mcc-black-list></test-env-mcc-black-list>
      </TabPane>
      <TabPane label="泳道分支检测变更提醒申请" name="testEnvPreMergeList">
        <test-env-pre-Merge-list></test-env-pre-Merge-list>
      </TabPane>
      <TabPane label="交易平台DB清理白名单申请" name="testEnvTradeDbWhiteList">
        <test-env-trade-Db-White-list></test-env-trade-Db-White-list>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import TestEnvWhiteList from './testEnvWhitelist'
  import TestEnvMccBlackList from './testEnvMccblacklist'
  import testEnvPreMergeList from './testEnvPreMergeList'
  import testEnvTradeDbWhiteList from './testEnvTradeDbWhiteList'

  export default {
    components: {
      TestEnvMccBlackList,
      TestEnvWhiteList,
      testEnvPreMergeList,
      testEnvTradeDbWhiteList},
    name: 'test-env-management',
    data: function () {
      return {

      }
    }
  }
</script>

<style scoped>
  .tab {
    margin-top: 30px;
    margin-left: 1%;
    margin-right: 1%;
    width: auto
  }
</style>
