<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i aria-hidden="true" class="fa fa-circle-o"></i> <span class="noteTip">不进行定时删除部署泳道白名单</span>：不会晚间定时删除和次日定时部署
      </p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span
        class="ciTip">李雪（lixue29）、李可欣（likexin06）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a
        href="https://km.sankuai.com/page/216894111" target="_blank">到店线下资源管理机制</a></span></p>
    </Card>
    <i-form :model="whiteListModel" :label-width="100">
<!--      <form-item label="白名单类型">-->
<!--        <i-select placeholder="请选择需要添加的白名单类型" v-model="whiteListModel.type">-->
<!--          <i-option value="0">不定时删除部署</i-option>-->
<!--          <i-option value="1">只定时部署不删除</i-option>-->
<!--        </i-select>-->
<!--      </form-item>-->
      <form-item label="泳道uuid">
        <i-input v-model.trim="whiteListModel.stackUuId"
                 placeholder="请填写泳道uuid e.g. cb4480ab-0976-4b3f-812e-616f49726c1b"></i-input>
      </form-item>
      <form-item label="申请原因">
        <i-input v-model.trim="whiteListModel.reason" placeholder="请填写申请原因"></i-input>
      </form-item>
      <template>
        <form-item label="生效时间">
          <i-input v-model.trim="whiteListModel.deadline" placeholder="请填写到期时间 e.g. 20201212" ></i-input>
        </form-item>
      </template>
      <form-item label="申请人">
        <i-input placeholder="请填写申请人mis号 e.g. lixue29" v-model.trim="whiteListModel.createUser"></i-input>
      </form-item>
      <form-item>
        <i-button type="primary" @click="save">创建</i-button>
      </form-item>
    </i-form>
  </div>
</template>


<script>
  import axios from 'axios'
  // import { agileReport } from '@/global/variable'
  import { Bus } from '@/global/bus'
  export default {
    name: 'test-env-white-list',
    data () {
      return {
        page: 'testEnvWhitelist',
        whiteListModel: {
          stackUuId: '',
          plus: '',
          createUser: '',
          type: 0,
          reason: '',
          deadline: '',
          mis: Bus.userInfo.userLogin
        }
      }
    },
    methods: {
      save () {
        let self = this
        axios.post('https://qareport.hotel.test.sankuai.com/stack/whitelist/add', {
          stackUuId: this.whiteListModel.stackUuId,
          deadline: this.whiteListModel.deadline,
          createUser: this.whiteListModel.createUser,
          reason: this.whiteListModel.reason,
          type: 0
        }).then(function (message) {
          if (message.data.msg === 'success') {
            self.$Modal.success({
              title: '生成成功！无需审核，直接生效！'
            })
          } else {
            self.$Spin.hide()
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.data.msg + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  #notice-card {
    margin-bottom: 24px;
  }

  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .noteTip {
    color: #79212e;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>



