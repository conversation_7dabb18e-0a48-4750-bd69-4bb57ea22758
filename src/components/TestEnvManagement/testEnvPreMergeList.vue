<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="noteTip">本名单对应的泳道会检测：源分支对比泳道内部署分支是否有变更，如有变更并会发消息通知给用户和大象群(群通知需要申请权限)！</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span
        class="ciTip">司丹丹（sidandan）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a
        href="https://km.sankuai.com/page/465435459" target="_blank">Feature分支定期同步master代码</a></span></p>
    </Card>
    <i-form :model="whiteListModel" :label-width="100">
      <form-item label="cargouuid">
        <i-input v-model.trim="whiteListModel.cargouuid"
                 placeholder="请填写服务泳道uuid e.g. com.sankuai.nibscp.flow.make"></i-input>
      </form-item>
      <form-item label="sourceBranch">
        <i-input v-model.trim="whiteListModel.sourceBranch" placeholder="请填写源分支 key e.g. master"></i-input>
      </form-item>
      <form-item label="大象通知人">
        <i-input v-model.trim="whiteListModel.createUser" placeholder="请填写通知人mis号 e.g. sidandan"></i-input>
      </form-item>
      <form-item label="大象通知群">
              <i-input v-model.trim="whiteListModel.groupid" placeholder="请填写通知群 mis号 e.g. 11111"></i-input>
      </form-item>
      <form-item label="截止日期">
        <i-input v-model.trim="whiteListModel.deadline" placeholder="请填写截止日期 mis号 e.g. 20210405"></i-input>
      </form-item>
      <div class="test">
        <form-item>
          <i-button type="primary" @click="execute  ">立即触发</i-button>
        </form-item>
        <form-item>
          <i-button type="primary" @click="createtask">创建任务</i-button>
        </form-item>
      </div>
    </i-form>
  </div>
</template>


<script>
  import axios from 'axios'
  // import { agileReport } from '@/global/variable'
  export default {
    name: 'test-env-pre-Merge-list',
    data () {
      return {
        page: 'testEnvPreMergeList',
        whiteListModel: {
          cargouuid: '',
          sourceBranch: '',
          createUser: '',
          groupid: '',
          deadline: ''
        }
      }
    },
    methods: {
      execute () {
        let self = this
        axios.post('https://qa.sankuai.com/analysis/preMergeBycargouuid', {
          stack_uuid: this.whiteListModel.cargouuid,
          sourceBranch: this.whiteListModel.sourceBranch,
          to: this.whiteListModel.createUser,
          groupid: this.whiteListModel.groupid
        }).then(function (message) {
          if (message.status === 200) {
            self.$Message.success('触发成功，请等一会儿！')
            self.$Modal.success({
              title: '触发成功'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.message + '</P>'
            })
          }
        })
      },
      createtask () {
        let self = this
        axios.post('https://qa.sankuai.com/analysis/preMergeAdd', {
          stack_uuid: this.whiteListModel.cargouuid,
          sourceBranch: this.whiteListModel.sourceBranch,
          to: this.whiteListModel.createUser,
          groupid: this.whiteListModel.groupid,
          deadline: this.whiteListModel.deadline

        }).then(function (message) {
          if (message.status === 200) {
            self.$Message.success('创建成功，已落地数据库！')
            self.$Modal.success({
              title: '触发成功'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.message + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  #notice-card {
    margin-bottom: 24px;
  }

  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .noteTip {
    color: #79212e;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .test {
    display: flex;
  }
  .test .ivu-form-item-content {
    margin-left: 20px;
  }
</style>



