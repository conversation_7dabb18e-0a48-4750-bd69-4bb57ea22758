<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="noteTip">本名单对应的MCC主干变更不会发消息通知！</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span
        class="ciTip">李雪（lixue29）、李可欣（likexin06）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a
        href="https://km.sankuai.com/page/273970682" target="_blank">到店测试环境主干管理机制</a></span></p>
    </Card>
    <i-form :model="whiteListModel" :label-width="100">
      <form-item label="appkey">
        <i-input v-model.trim="whiteListModel.appkey"
                 placeholder="请填写服务appkey e.g. com.sankuai.nibscp.flow.make"></i-input>
      </form-item>
      <form-item label="mccKey">
        <i-input v-model.trim="whiteListModel.mccKey" placeholder="请填写需申请的mcc key e.g. CashPayPermission"></i-input>
      </form-item>
      <form-item label="申请人">
        <i-input v-model.trim="whiteListModel.createUser" placeholder="请填写申请人mis号 e.g. lixue29"></i-input>
      </form-item>
      <form-item>
        <i-button type="primary" @click="save">创建</i-button>
      </form-item>
    </i-form>
  </div>
</template>


<script>
  import axios from 'axios'
  // import { agileReport } from '@/global/variable'
  export default {
    name: 'test-env-mcc-black-list',
    data () {
      return {
        page: 'testEnvMccblacklist',
        whiteListModel: {
          appkey: '',
          mccKey: '',
          createUser: ''
        }
      }
    },
    methods: {
      save () {
        let self = this
        axios.post('https://qareport.hotel.test.sankuai.com/notify/mcc/blacklist', {
          appkey: this.whiteListModel.appkey,
          mccKey: this.whiteListModel.mccKey,
          creator: this.whiteListModel.createUser
        }).then(function (message) {
          if (message.status === 200) {
            self.$Message.success('生成成功！')
            self.$Modal.success({
              title: '生成成功'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.message + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  #notice-card {
    margin-bottom: 24px;
  }

  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }

  .noteTip {
    color: #79212e;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>



