<template>
  <div>
    <h4 style="text-align:center">过程度量详细数据信息({{message}})</h4>
    <Checkbox-group v-model="tableColumnsChecked" @on-change="changeTableColumns">
      <Checkbox label="planTransferTime">计划提测时间</Checkbox>
      <Checkbox label="actualTransferTime">实际提测时间</Checkbox>
      <Checkbox label="transferDelayReason">提测delay原因</Checkbox>
      <Checkbox label="planTestPD">计划测试PD</Checkbox>
      <Checkbox label="actualTestPd">实际测试PD</Checkbox>
      <Checkbox label="planTestFinishTime">计划测完时间</Checkbox>
      <Checkbox label="actualTestFinishTime">实际测完时间</Checkbox>
      <Checkbox label="planLaunchTime">计划上线时间</Checkbox>
      <Checkbox label="actualLaunchTime">实际上线时间</Checkbox>
      <Checkbox label="prdUrl">需求文档</Checkbox>
      <Checkbox label="hasExpectEarn">预期收益</Checkbox>
      <Checkbox label="evaluationUrl">效果评估wiki</Checkbox>
      <Checkbox label="totalDevPd">总开发PD</Checkbox>
      <Checkbox label="beDevPd">后端开发PD</Checkbox>
      <Checkbox label="feDevPd">前端开发PD</Checkbox>
      <Checkbox label="totalBugCnt">Bug总数</Checkbox>
      <Checkbox label="validBugCnt">有效bug总数</Checkbox>
      <Checkbox label="beBugCnt">后端Bug数</Checkbox>
      <Checkbox label="feBugCnt">前端Bug数</Checkbox>
      <Checkbox label="appBugCnt">客户端Bug数</Checkbox>
      <Checkbox label="averageBugPerDay">平均工时有效Bug</Checkbox>
    </Checkbox-group>

    <Col style="text-align: right; margin-bottom: 1%; margin-right: 1%">
    <!--<Button type="primary" @click="createWiki()">导出wiki</Button>-->
    </Col>
    <Table stripe border :columns="columns4" :data="processRawData"></Table>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import axios from 'axios'
  import { analyticswikiAPI } from '@/global/variable'
  Bus.$on('refreshprocessDetailData', function (data) {
    Bus.processDetailObject.processRawData = Bus.processDetailObject.processData(data)
  })
  export default {
    name: 'detailProcessReport',
    data: function () {
      return {
        message: '全部',
        taskurl: [],
        columns4: [],
        processRawData: [],
        tableColumnsChecked: ['direction', 'taskSummary', 'planTransferTime', 'actualTransferTime', 'transferDelayReason', 'planTestPD', 'actualTestPd', 'planLaunchTime', 'actualLaunchTime', 'totalDevPd', 'validBugCnt', 'averageBugPerDay']
      }
    },
    methods: {
      changeExpectEarn (data) {
        if (!data) {
          return '无'
        } else {
          return '有'
        }
      },
      getmessage () {
        if (this.$route.query.reqtype === '1') {
          this.message = '产品'
        }
        if (this.$route.query.reqtype === '2') {
          this.message = '技术'
        }
      },
      processData (data) {
        if (data) {
          let metricsStats = []
          let rawData = data['processRaw']

          for (let groupRaw in rawData) {
            for (let eachTask in rawData[groupRaw]) {
              rawData[groupRaw][eachTask]['direction'] = groupRaw
              metricsStats.push(rawData[groupRaw][eachTask])
              // console.log('metricsStats', metricsStats)
            }
          }
          this.$Spin.hide()
          Bus.$emit('refreshExportWiki')
          Bus.processWiki = []
          Bus.processWiki = rawData
          // console.log('导出的详细数据', Bus.processWiki)
          return metricsStats
        } else {
          return []
        }
      },
      createWiki () {
        let self = this
        self.$Spin.show()
        let wikidata = Bus.processdetaildata
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/createprocess',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      },
      changeTableColumns () {
        this.columns4 = this.getTable2Columns()
      },
      getTable2Columns () {
        const table2ColumnList = {
          direction: {
            title: '方向',
            key: 'direction',
            sortable: true
          },
          taskSummary: {
            title: '项目名称',
            key: 'taskSummary',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['taskUrl']
                  },
                  on: {
                    click: () => {
                      // console.log('!!!!!attention')
                      // console.log(h)
                      // console.log(params)
                      window.open(params.row['taskUrl'])
                    }
                  }
                }, params.row['taskSummary'])
              ])
            }
          },
          planTransferTime: {
            title: '计划提测时间',
            key: 'planTransferTime',
            sortable: true
          },
          actualTransferTime: {
            title: '实际提测时间',
            key: 'actualTransferTime',
            sortable: true
          },
          transferDelayReason: {
            title: '提测delay原因',
            key: 'transferDelayReason'
          },
          planTestPD: {
            title: '计划测试PD',
            key: 'planTestPD',
            sortable: true
          },
          actualTestPd: {
            title: '实际测试PD',
            key: 'actualTestPd',
            sortable: true
          },
          planTestFinishTime: {
            title: '计划测完时间',
            key: 'planTestFinishTime',
            sortable: true
          },
          actualTestFinishTime: {
            title: '实际测完时间',
            key: 'actualTestFinishTime',
            sortable: true
          },
          planLaunchTime: {
            title: '计划上线时间',
            key: 'planLaunchTime',
            sortable: true
          },
          actualLaunchTime: {
            title: '实际上线时间',
            key: 'actualLaunchTime',
            sortable: true
          },
          prdUrl: {
            title: '需求文档',
            key: 'prdUrl',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['prdUrl']
                  },
                  on: {
                    click: () => {
                      window.open(params.row['prdUrl'])
                    }
                  }
                }, '需求文档')
              ])
            }
          },
          hasExpectEarn: {
            title: '预期收益',
            key: 'hasExpectEarn',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, this.changeExpectEarn(params.row['hasExpectEarn'])
                )
              ])
            }
          },
          evaluationUrl: {
            title: '效果评估wiki',
            key: 'evaluationUrl'
            // render: (h, params) => {
            //  return h('div', [
            //      h('a', {
            //        props: {
            //          href: params.row['evaluationUrl']
            //        },
            //        on: {
            //         click: () => {
            //            window.open(params.row['evaluationUrl'])
            //          }
            //        }
            //    }, this.changeExpectEarn(params.row['evaluationUrl']))
            //    ])
            //  }
          },
          beDevPd: {
            title: '后端开发PD',
            key: 'beDevPd',
            sortable: true
          },
          feDevPd: {
            title: '前端开发PD',
            key: 'feDevPd',
            sortable: true
          },
          jointDebugPd: {
            title: '联调PD',
            key: 'jointDebugPd',
            sortable: true
          },
          totalDevPd: {
            title: '总开发PD',
            key: 'totalDevPd',
            sortable: true
          },
          totalBugCnt: {
            title: 'Bug总数',
            key: 'totalBugCnt',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['subTaskJQLUrl']
                  },
                  on: {
                    click: () => {
                      // console.log('!!!!!attention')
                      // console.log(h)
                      // console.log(params)
                      window.open(params.row['subTaskJQLUrl'])
                    }
                  }
                }, params.row['totalBugCnt'])
              ])
            },
            sortable: true
          },
          validBugCnt: {
            title: '有效bug总数',
            key: 'validBugCnt',
            sortable: true
          },
          beBugCnt: {
            title: '后端Bug数',
            key: 'beBugCnt',
            sortable: true
          },
          feBugCnt: {
            title: '前端Bug数',
            key: 'feBugCnt',
            sortable: true
          },
          appBugCnt: {
            title: '客户端Bug数',
            key: 'appBugCnt',
            sortable: true
          },
          averageBugPerDay: {
            title: '平均工时有效Bug',
            key: 'averageBugPerDay',
            sortable: true
          }
        }
        let data = []
        this.tableColumnsChecked.forEach(col => data.push(table2ColumnList[col]))
        // console.log('tableColumnsDisplayed', data)
        return data
      }
    },
    mounted: function () {
      Bus.processDetailObject = this
      this.changeTableColumns()
      this.getmessage()
    }
  }
</script>

<style scoped>

</style>
