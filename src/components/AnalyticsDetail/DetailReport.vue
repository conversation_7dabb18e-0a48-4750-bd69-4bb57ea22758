<template>
  <div>
    <head-component id="head"></head-component>
    <div>
      <!--<div v-if="backallow" style="padding-left:10px;padding-top: 10px;">-->
      <!--<Button type="primary" size="small" disabled>-->
      <!--<Icon type="chevron-left"></Icon><span style="padding-left: 3px">回退</span>-->
      <!--</Button>-->
      <!--</div>-->
      <!--<div v-else style="width: 1%;padding-left:10px;padding-top: 10px;">-->
      <!--<Button type="primary" size="small" @click='backspace'>-->
      <!--<Icon type="chevron-left"></Icon><span style="padding-left: 3px">回退</span>-->
      <!--</Button>-->
      <!--</div>-->
      <Row span="24">
        <Col order="1" span="23">
        <Tabs :value="detail" style='margin-top: 30px;margin-left:50px;margin-right:20px;width: auto;'>
          <TabPane label="过程度量" name="process_metrics">
            <detail-process-report></detail-process-report>
          </TabPane>
          <TabPane label="质量数据" name="quality_metrics">
            <detail-quality-report></detail-quality-report>
          </TabPane>
        </Tabs>
        </Col>
        <Col v-if="displayexport" order="2" span="1" style="margin-top: 10px;">
        <div>
          <Button type="primary" @click="modal2 = true">导出质<br>量报告</Button>
          <Modal
            v-model="modal2" title="提示：" @on-ok="ok" @on-cancel="cancel1">
            <p>是否导出数据到wiki？</p>
          </Modal>
        </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import { analyticswikiAPI, analyticsbaseAPI } from '@/global/variable'
  import Vue from 'vue'
  import router from '@/router'
  import Head from '@/components/Common/Head'
  import axios from 'axios'
  import DetailProcessReport from './DetailProcessReport'
  import DetailQualityReport from './DetailQualityReport'
  Vue.component('head-component', Head)
  Bus.$on('refreshDefaultTab', function () {
    Bus.DetailObject.detail = 'quality_metrics'
    // console.log(Bus.DetailObject)
  })
  Bus.$on('refreshsearch', function () {
    Bus.DetailObject.search = false
    // console.log('refresh search arrive', Bus.DetailObject.search)
  })
  Bus.$on('refreshExportWiki', function () {
    Bus.DetailObject.displayexport = true
  })
  // Bus.$on('refreshbackallow', function () {
  //   Bus.DetailObject.backallow = false
  // })
  export default {
    components: {
      DetailQualityReport, DetailProcessReport},
    name: 'detailReport',
    data: function () {
      return {
        displayexport: false,
        modal2: false,
        display7: true,
        search: true,
        backallow: true,
        detail: 'process_metrics'
      }
    },
    methods: {
      changeExpectEarn (data) {
        if (!data) {
          return '无'
        }
        if (data === 'None') {
          return ''
        }
      },
      processData (rawdata) {
        // console.log(rawdata)
        if (rawdata) {
          let metricsStats = []
          for (let period in rawdata) {
            for (let task in rawdata[period]) {
              for (let eachrow in rawdata[period][task]) {
                rawdata[period][task][eachrow]['direction'] = task
                metricsStats.push(rawdata[period][task][eachrow])
              }
            }
          }
          this.$Spin.hide()
          return metricsStats
        } else {
          return []
        }
      },
      cancel1 () {
      },
      backspace () {
        // Bus.$emit('refreshbackdata')
        // this.$router.back(-1)
        let params = this.$route.query
        delete params['name']
        delete params['period']
        router.push({ path: '/analytics', query: params })
        // console.log('router', this.$router.back(-1))
      },
      ok () {
        let self = this
        self.$Spin.show()
        let wikidata = {
          processRaw: Bus.processWiki,
          qualityView: Bus.qualityWiki,
          period: this.$route.query.period,
          direction: this.$route.query.name
        }

        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/exportreport',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    created: function () {
      // console.log('create', this.search)
    },
    mounted: function () {
      Bus.DetailObject = this
      const self = this
      Bus.DetailObject.$Spin.show()
      let params = {
        directionName: '',
        period: '',
        type: '',
        reqType: ''
      }
      if (Bus.DetailObject.$route.query.name) {
        params.directionName = Bus.DetailObject.$route.query.name
      }
      if (Bus.DetailObject.$route.query.period) {
        params.period = Bus.DetailObject.$route.query.period
      }
      if (Bus.DetailObject.$route.query.bizline) {
        if (Bus.DetailObject.$route.query.bizline === '0') {
          params.type = 1
        } else if (Bus.DetailObject.$route.query.group === '0') {
          params.type = 2
        } else if (Bus.DetailObject.$route.query.project) {
          params.type = 3
        }
      }
      if (Bus.DetailObject.$route.query.reqtype) {
        params.reqType = Bus.DetailObject.$route.query.reqtype
      }
      axios.get(analyticsbaseAPI + '/detail', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          // console.log(message)
          // console.log('保存的查询条件', Bus.metricsdata)
          // router.push({ path: '/detail' })
          // Bus.$emit('refreshDefaultTab')
          Bus.detailData = []
          Bus.detailData = message['data']['data']
          setTimeout(function () {
            // console.log(Bus.detailData)
            Bus.$emit('refreshprocessDetailData', Bus.detailData)
            Bus.$emit('refreshqualityDetailData', Bus.detailData)
          }, 100)
          // console.log('Bus-detailData', Bus.detailData)
        } else {
          self.$Spin.hide()
          alert(message['data']['msg'])
        }
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
      // }
    }
  }
</script>

<style scoped>

</style>
