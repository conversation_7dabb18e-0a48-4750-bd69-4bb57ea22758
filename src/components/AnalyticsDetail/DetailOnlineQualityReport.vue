<template>
  <div>
    <h4 style="text-align:center;margin-top: 20px">线上问题（线上bug+线上故障)</h4>
    <div style="margin-left: 30px;margin-right: 30px;margin-top: 20px">
      <Table stripe border :columns="onlineQualityDetailColumns" :data="tableData"></Table>
    </div>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import { analyticsbaseAPI } from '@/global/variable'
  import axios from 'axios'

  Bus.$on('refreshOnlineQualityDetail', function (data) {
    Bus.onlineQualityDetailObject.tableData = Bus.onlineQualityDetailObject.processData(data)
    // console.log('线上质量详细数据', Bus.onlineQualityDetailObject.tableData)
  })

  export default {
    name: 'detailOnlineQualityReport',
    data: function () {
      return {
        tableData: [],
        onlineQualityDetailColumns: [
          {
            title: '方向',
            key: 'direction',
            // width: 20,
            sortable: true
          },
          {
            title: '问题名称',
            key: 'issueSummary',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['taskUrl']
                  },
                  on: {
                    click: () => {
                      window.open(params.row['taskUrl'])
                    }
                  }
                }, params.row['issueSummary'])
              ])
            }
          },
          {
            title: '解决状态',
            key: 'status',
            // width: 20,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: this.getStatusColor(params.row.status)
                  }
                }, params.row.status)
              ])
            }
          },
          {
            title: '解决结果',
            key: 'resolution',
            // width: 20,
            sortable: true
          },
          {
            title: '原因',
            key: 'bugReason',
            // width: 20,
            sortable: true
          },
          {
            title: '影响范围',
            key: 'affection',
            // width: 20,
            sortable: true
          },
          {
            title: '归属方',
            key: 'bugBelong',
            // width: 20,
            sortable: true
          },
          {
            title: '服务模块',
            key: 'components',
            // width: 20,
            sortable: true
          },
          {
            title: '优先级',
            key: 'priority',
            // width: 20,
            sortable: true
          },
          {
            title: '上线时间',
            key: 'releasedTime',
            // width: 20,
            sortable: true
          },
          {
            title: '发现时间',
            key: 'detectionTime',
            // width: 20,
            sortable: true
          },
          {
            title: '解决时间',
            key: 'solvedTime',
            // width: 20,
            sortable: true
          },
          {
            title: 'rd负责人',
            key: 'rdInCharge',
            // width: 20,
            sortable: true
          },
          {
            title: 'qa负责人',
            key: 'qaInCharge',
            // width: 20,
            sortable: true
          }
        ]
      }
    },
    methods: {
      getStatusColor: function (status) {
        if (status === '未开始') {
          return 'red'
        }
      },
      processData (data) {
        if (data) {
          let onlineQuliatyDetail = []

          for (let groupRaw in data) {
            for (let eachTask in data[groupRaw]) {
              data[groupRaw][eachTask]['direction'] = groupRaw
              onlineQuliatyDetail.push(data[groupRaw][eachTask])
              // console.log('metricsStats', metricsStats)
            }
          }
          this.$Spin.hide()
          return onlineQuliatyDetail
        } else {
          return []
        }
      }
    },
    mounted: function () {
      Bus.onlineQualityDetailObject = this
      Bus.onlineQualityDetailObject.$Spin.show()
      const self = this

      let params = {
        directionName: Bus.onlineQualityDetailObject.$route.query.name,
        period: Bus.onlineQualityDetailObject.$route.query.period,
        type: Bus.onlineQualityDetailObject.$route.query.queryType
      }
      axios.get(analyticsbaseAPI + '/online/issuedetail', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          Bus.detailData = []
          Bus.detailData = message['data']['data']
          console.log(Bus.detailData)
          Bus.$emit('refreshOnlineQualityDetail', Bus.detailData)
        } else {
          self.$Spin.hide()
          alert(message['data']['msg'])
        }
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
