<template>
  <div>
    <head-component id="head"></head-component>
    <div>
      <!--<div v-if="backallow" style="padding-left:10px;padding-top: 10px;">-->
      <!--<Button type="primary" size="small" disabled>-->
      <!--<Icon type="chevron-left"></Icon><span style="padding-left: 3px">回退</span>-->
      <!--</Button>-->
      <!--</div>-->
      <!--<div v-else style="width: 1%;padding-left:10px;padding-top: 10px;">-->
      <!--<Button type="primary" size="small" @click='backspace'>-->
      <!--<Icon type="chevron-left"></Icon><span style="padding-left: 3px">回退</span>-->
      <!--</Button>-->
      <!--</div>-->
      <Row span="24">
        <Col order="1">
        <Tabs :value="detail" style='margin-top: 30px;margin-left:50px;margin-right:50px;width: auto;'>
          <TabPane label="RD人效" name="rd_metrics">
            <rd-efficiency-detail></rd-efficiency-detail>
          </TabPane>
          <TabPane label="QA人效" name="qa_metrics">
            <qa-efficiency-detail></qa-efficiency-detail>
          </TabPane>
        </Tabs>
        </Col>
        <Col v-if="displayexport" order="2" span="1" style="margin-top: 10px;">
        <div>
          <Button type="primary" @click="modal2 = true">导出质<br>量报告</Button>
          <Modal
            v-model="modal2" title="提示：" @on-ok="ok" @on-cancel="cancel1">
            <p>是否导出数据到wiki？</p>
          </Modal>
        </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import { analyticswikiAPI, analyticsbaseAPI } from '@/global/variable'
  import Vue from 'vue'
  import router from '@/router'
  import Head from '@/components/Common/Head'
  import rdEfficiencyDetail from '@/components/AnalyticsDetail/rdEfficiencyDetail'
  import qaEfficiencyDetail from '@/components/AnalyticsDetail/qaEfficiencyDetail'
  import axios from 'axios'
  Vue.component('head-component', Head)
  Vue.component('rd-efficiency-detail', rdEfficiencyDetail)
  Vue.component('qa-efficiency-detail', qaEfficiencyDetail)
  Bus.$on('refreshDefaultTab', function () {
    Bus.DetailEfficiencyObject.detail = 'qa_metrics'
    // console.log(Bus.DetailObject)
  })
  Bus.$on('refreshefficiencysearch', function () {
    Bus.DetailEfficiencyObject.search = false
    // console.log('refresh search arrive', Bus.DetailObject.search)
  })
  Bus.$on('refreshExportWiki', function () {
    Bus.DetailEfficiencyObject.displayexport = true
  })
  Bus.$on('refreshefficiencybackallow', function () {
    Bus.DetailEfficiencyObject.backallow = false
  })
  export default {
    name: 'detailefficiency',
    data: function () {
      return {
        displayexport: false,
        modal2: false,
        display7: true,
        search: true,
        backallow: true,
        detail: 'qa_metrics'
      }
    },
    methods: {
      // changeExpectEarn (data) {
      //   if (!data) {
      //     return '无'
      //   }
      //   if (data === 'None') {
      //     return ''
      //   }
      // },
      // processData (rawdata) {
      //   // console.log(rawdata)
      //   if (rawdata) {
      //     let metricsStats = []
      //     for (let period in rawdata) {
      //       for (let task in rawdata[period]) {
      //         for (let eachrow in rawdata[period][task]) {
      //           rawdata[period][task][eachrow]['direction'] = task
      //           metricsStats.push(rawdata[period][task][eachrow])
      //         }
      //       }
      //     }
      //     this.$Spin.hide()
      //     return metricsStats
      //   } else {
      //     return []
      //   }
      // },
      cancel1 () {
      },
      backspace () {
        // Bus.$emit('refreshbackdata')
        // this.$router.back(-1)
        let params = this.$route.query
        delete params['name']
        delete params['period']
        router.push({ path: '/analytics', query: params })
        // console.log('router', this.$router.back(-1))
      },
      ok () {
        let self = this
        self.$Spin.show()
        let wikidata = {
          processRaw: Bus.processWiki,
          qualityView: Bus.qualityWiki
        }
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/exportreport',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    created: function () {
      // console.log('create', this.search)
    },
    mounted: function () {
      Bus.DetailEfficiencyObject = this
      const self = this
      // console.log('mounted', this.search)
      // console.log('mounted进入')
      Bus.DetailEfficiencyObject.$Spin.show()
      let params = {
        directionName: '',
        period: '',
        type: '',
        reqType: ''
      }
      if (Bus.DetailEfficiencyObject.$route.query.name) {
        params.directionName = Bus.DetailEfficiencyObject.$route.query.name
      }
      if (Bus.DetailEfficiencyObject.$route.query.period) {
        params.period = Bus.DetailEfficiencyObject.$route.query.period
      }
      if (Bus.DetailEfficiencyObject.$route.query.bizline) {
        if (Bus.DetailEfficiencyObject.$route.query.bizline === '0') {
          params.type = 1
        } else if (Bus.DetailEfficiencyObject.$route.query.group === '0') {
          params.type = 2
        } else if (Bus.DetailEfficiencyObject.$route.query.project) {
          params.type = 3
        }
      }
      if (Bus.DetailEfficiencyObject.$route.query.reqtype) {
        params.reqType = Bus.DetailEfficiencyObject.$route.query.reqtype
      }
      axios.get(analyticsbaseAPI + '/efficiency/detail', {
        // axios.get('http://0.0.0.0:11000/analytics/efficiency/detail', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          // console.log(message)
          // console.log('保存的查询条件', Bus.metricsdata)
          // router.push({ path: '/detail' })
          // Bus.$emit('refreshDefaultTab')
          Bus.detailEfficiencyData = []
          Bus.detailEfficiencyData = message['data']['data']
          // console.log(Bus.detailData)
          Bus.$emit('refreshrdEfficiencyDetailData', Bus.detailEfficiencyData)
          Bus.$emit('refreshqaEfficiencyDetailData', Bus.detailEfficiencyData)
          // console.log('Bus-detailData', Bus.detailData)
        } else {
          self.$Spin.hide()
          alert(message['data']['msg'])
        }
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
    }
  }
</script>

<style scoped>

</style>
