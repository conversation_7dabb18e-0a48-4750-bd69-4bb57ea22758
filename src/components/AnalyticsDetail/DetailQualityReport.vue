<template>
  <div>
    <h4 style="text-align:center">质量详细数据信息({{message}})</h4>
    <Col style="text-align: right; margin-bottom: 1%; margin-right: 1%">
    <!--<Button type="primary" @click="createWiki()">导出wiki</Button>-->
    </Col>
    <div id="qualitydetailtable" style="margin-left:30%">
      <v-table
        :columns="columns7"
        :table-data="qualityRawData"
        even-bg-color="#f4f4f4"
        row-hover-color="#eee"
        row-click-color="#edf7ff"
        :cell-merge="cellMerge"
        :column-cell-class-name="columnCellClass"
      ></v-table>
    </div>
    <!--<Table stripe border :columns="columns7" :data="qualityRawData"></Table>-->
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import axios from 'axios'
  import Vue from 'vue'
  import 'vue-easytable/libs/themes-base/index.css'
  import { analyticswikiAPI } from '@/global/variable'
  import { VTable } from 'vue-easytable'
  Vue.component(VTable.name, VTable)
  Bus.$on('refreshqualityDetailData', function (data) {
    // console.log('begin')
    Bus.qualityDetailObject.qualityRawData = Bus.qualityDetailObject.qualityData(data)
    // console.log(Bus.qualityDetailObject.qualityRawData)
  })
  export default {
    name: 'detailQualityReport',
    data: function () {
      return {
        message: '全部',
        taskurl: [],
        columns7: [
          {
            field: 'no',
            title: '序号',
            titleAlign: 'center',
            columnAlign: 'center',
            titleCellClassName: 'title-cell-class-name-test',
            // color: #495060,
            // font: 12px,
            width: 150
            // sortable: true
          },
          {
            field: 'class',
            title: '类别',
            titleAlign: 'center',
            columnAlign: 'center',
            titleCellClassName: 'title-cell-class-name-test',
            width: 150
            // sortable: true
          },
          {
            field: 'kind',
            title: '类型',
            titleAlign: 'center',
            columnAlign: 'center',
            titleCellClassName: 'title-cell-class-name-test',
            width: 250
            // sortable: true
          },
          // {
          //   field: 'last',
          //   title: '上次',
          //   titleAlign: 'center',
          //   columnAlign: 'center',
          //   width: 150
          //   // sortable: true
          // },
          {
            field: 'this',
            title: '本次',
            titleAlign: 'center',
            columnAlign: 'center',
            titleCellClassName: 'title-cell-class-name-test',
            width: 150
            // sortable: true
          }
          // {
          //   field: 'ratio',
          //   title: '比率',
          //   titleAlign: 'center'
          //   // sortable: true
          // }
          // {
          //   title: '项目名称',
          //   key: 'taskSummary',
          //   render: (h, params) => {
          //     return h('div', [
          //       h('a', {
          //         props: {
          //           href: params.row['taskUrl']
          //         },
          //         on: {
          //           click: () => {
          //             window.open(params.row['taskUrl'])
          //           }
          //         }
          //       }, params.row['taskSummary'])
          //     ])
          //   }
          // },
          // {
          //   title: '后端开发PD',
          //   key: 'rdDevPd',
          //   sortable: true
          // },
          // {
          //   title: '前端开发PD',
          //   key: 'feDevPd',
          //   sortable: true
          // },
          // {
          //   title: '联调PD',
          //   key: 'jointDebugPd',
          //   sortable: true
          // },
          // {
          //   title: '计划提测时间',
          //   key: 'planTransferTime',
          //   sortable: true
          // },
          // {
          //   title: '实际提测时间',
          //   key: 'actualTransferTime',
          //   sortable: true
          // },
          // {
          //   title: '计划测试PD',
          //   key: 'planTestPD',
          //   sortable: true
          // },
          // {
          //   title: '实际测试PD',
          //   key: 'actualTestPd',
          //   sortable: true
          // },
          // {
          //   title: '计划测完时间',
          //   key: 'planTestFinishTime',
          //   sortable: true
          // },
          // {
          //   title: '实际测完时间',
          //   key: 'actualTestFinishTime',
          //   sortable: true
          // },
          // {
          //   title: '计划上线时间',
          //   key: 'planLaunchTime',
          //   sortable: true
          // },
          // {
          //   title: '实际上线时间',
          //   key: 'actualLaunchTime',
          //   sortable: true
          // },
          // {
          //   title: '需求文档',
          //   key: 'prdUrl',
          //   render: (h, params) => {
          //     return h('div', [
          //       h('a', {
          //         props: {
          //           href: params.row['prdUrl']
          //         },
          //         on: {
          //           click: () => {
          //             window.open(params.row['prdUrl'])
          //           }
          //         }
          //       }, '需求文档')
          //     ])
          //   }
          // },
          // {
          //   title: '预期收益',
          //   key: 'hasExpectEarn',
          //   render: (h, params) => {
          //     return h('div', [
          //       h('span', {
          //       }, this.changeExpectEarn(params.row['hasExpectEarn'])
          //       )
          //     ])
          //   }
          // },
          // {
          //   title: '效果评估wiki',
          //   key: 'evaluationUrl',
          //   render: (h, params) => {
          //     return h('div', [
          //       h('a', {
          //         props: {
          //           href: params.row['evaluationUrl']
          //         },
          //         on: {
          //           click: () => {
          //             window.open(params.row['evaluationUrl'])
          //           }
          //         }
          //       }, this.changeExpectEarn(params.row['evaluationUrl']))
          //     ])
          //   }
          // }
        ],
        qualityRawData: []
      }
    },
    methods: {
      cellMerge (rowIndex, rowData, field) {
        if (field === 'no' && rowData[field] === '1') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color:#495060">1</span>'
          }
        } else if (field === 'class' && rowData[field] === '开发测试工时') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color:#495060">开发测试工时</span>'
          }
        } else if (field === 'class' && rowData[field] === 'Bug数据') {
          return {
            colSpan: 1,
            rowSpan: 3,
            content: '<span style="color:#495060">Bug数据</span>'
          }
        } else if (field === 'class' && rowData[field] === '前端质量数据') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color:#495060">前端质量数据</span>'
          }
        } else if (field === 'class' && rowData[field] === '后端质量数据') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color: #495060">后端质量数据</span>'
          }
        } else if (field === 'no' && rowData[field] === '2') {
          return {
            colSpan: 1,
            rowSpan: 3,
            content: '<span style="color:#495060">2</span>'
          }
        } else if (field === 'no' && rowData[field] === '3') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color:#495060">3</span>'
          }
        } else if (field === 'no' && rowData[field] === '4') {
          return {
            colSpan: 1,
            rowSpan: 5,
            content: '<span style="color:#495060">4</span>'
          }
        }
      },
      changeExpectEarn (data) {
        if (!data) {
          return '无'
        }
        if (data === 'None') {
          return ''
        }
      },
      qualityData (data) {
        // console.log('quality data:')
        // console.log(data)
        if (data) {
          let rawdata = {}
          rawdata = data['qualityView']['projectQualityStats']
          // console.log(rawdata)
          let metricsStats = []
          metricsStats.push({'no': '1', 'class': '开发测试工时', 'kind': '开发联调总工时（PD）', 'last': ' ', 'this': rawdata.totalDevPd})
          metricsStats.push({'no': '1', 'class': '开发测试工时', 'kind': '测试总工时（PD）', 'last': ' ', 'this': rawdata.qaTestPd})
          metricsStats.push({'no': '1', 'class': '开发测试工时', 'kind': '测试人效', 'last': ' ', 'this': rawdata.testPdRatio})
          metricsStats.push({'no': '1', 'class': '开发测试工时', 'kind': '平均开发联调工时（PD/个）', 'last': ' ', 'this': rawdata.averageDevPeriod})
          metricsStats.push({'no': '1', 'class': '开发测试工时', 'kind': '平均测试工时（PD/个）', 'last': ' ', 'this': rawdata.averageTestPeriod})
          metricsStats.push({'no': '2', 'class': 'Bug数据', 'kind': 'Bug总数（个）', 'last': ' ', 'this': rawdata.totalBugCnt})
          metricsStats.push({'no': '2', 'class': 'Bug数据', 'kind': '有效Bug数（个）', 'last': ' ', 'this': rawdata.validBugCnt})
          metricsStats.push({'no': '2', 'class': 'Bug数据', 'kind': '工时平均有效Bug数（个/PD）', 'last': ' ', 'this': rawdata.averageBugPerDay})
          metricsStats.push({'no': '3', 'class': '前端质量数据', 'kind': '前端开发工时（PD）', 'last': ' ', 'this': rawdata.feDevPd})
          metricsStats.push({'no': '3', 'class': '前端质量数据', 'kind': '前端联调工时（PD）', 'last': ' ', 'this': rawdata.feJointDebugPd})
          metricsStats.push({'no': '3', 'class': '前端质量数据', 'kind': '前端联调占比', 'last': ' ', 'this': rawdata.feDebugPdRatio})
          metricsStats.push({'no': '3', 'class': '前端质量数据', 'kind': '前端有效Bug总数', 'last': ' ', 'this': rawdata.feValidBugCnt})
          metricsStats.push({'no': '3', 'class': '前端质量数据', 'kind': '前端工时平均有效Bug数（个/PD）', 'last': ' ', 'this': rawdata.feAverageBugPerDay})
          metricsStats.push({'no': '4', 'class': '后端质量数据', 'kind': '后端开发工时（PD）', 'last': ' ', 'this': rawdata.beDevPd})
          metricsStats.push({'no': '4', 'class': '后端质量数据', 'kind': '后端联调工时（PD）', 'last': ' ', 'this': rawdata.beJointDebugPd})
          metricsStats.push({'no': '4', 'class': '后端质量数据', 'kind': '后端联调占比', 'last': ' ', 'this': rawdata.beDebugPdRatio})
          metricsStats.push({'no': '4', 'class': '后端质量数据', 'kind': '后端有效Bug总数', 'last': ' ', 'this': rawdata.beValidBugCnt})
          metricsStats.push({'no': '4', 'class': '后端质量数据', 'kind': '后端工时平均有效Bug数（个/PD）', 'last': ' ', 'this': rawdata.beAverageBugPerDay})
          metricsStats.push({'no': '5', 'class': '产品质量数据', 'kind': '需求相关Bug总数（个）', 'last': ' ', 'this': rawdata.pmBugCnt})
          Bus.$emit('refreshExportWiki')
          Bus.qualityWiki = []
          Bus.qualityWiki = data['qualityView']
          return metricsStats
        } else {
          return []
        }
      },
      columnCellClass () {
        return 'column-cell-class-name-test'
      },
      getmessage () {
        if (this.$route.query.reqtype === '1') {
          this.message = '产品'
        }
        if (this.$route.query.reqtype === '2') {
          this.message = '技术'
        }
      },
      createWiki () {
        let self = this
        self.$Spin.show()
        let wikidata = Bus.qualitydetaildata
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/createprocess',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    mounted: function () {
      Bus.qualityDetailObject = this
      this.getmessage()
    }
  }
</script>

<style>
  .title-cell-class-name-test {
    background-color: #f8f8f9;
    color: #495060;
    font-size: 12px;
    font-weight: bold;
  }
  .column-cell-class-name-test {
    font-size: 12px;
    color: #495060 !important;
  }
</style>
