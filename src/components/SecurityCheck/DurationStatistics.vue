<template>
  <div>
    <Col style="height:500px" span="24" order="1">
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 本工具用于生成安全组件扫描时间维度统计文档 </p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 参数说明-时间段：返回时间段内的批次对应数据；时间段应该大于等于1周，并包含完整的自然周</p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 参数说明-方向：返回选定方向时间维度的对比数据，方向只能单选</p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找 <span class="ciTip">邹艳雪（zouyanxue）</span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/417092965" target="_blank">组件安全扫描说明文档</a></span></p>
      </Card>
      <Timeline style="padding-top: 15px">
        <TimelineItem>
          <p class="time">方向:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" filterable :transfer="false"
                        style="width: 50%" change-on-select @on-change="getCurrentChooseDirection"></Cascader>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">报告起止时间:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="20" style="width: 98.8%">
            <Col span="30" order="1">
              <DatePicker style="width: 200px;" :value="dateRange" type="daterange"
                          :clearable=false placement="bottom-end" placeholder="请选择时间间隔" @on-change="filterByTime"
                          transfer></DatePicker>
            </Col>
          </Row>
        </TimelineItem>
      </Timeline>
      <Row type="flex" :gutter="2" justify="center">
        <Col span="2" order="1">
          <i-button type="primary" @click="submit()">生成</i-button>
        </Col>
      </Row>
    </Col>
  </div>
</template>

<script>
  import axios from 'axios'
  export default {
    name: 'duration-statistics',
    components: {},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      date.setDate(date.getDate() - 1)
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 6)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      return {
        style: {},
        directionList: [],
        currentChooseDirection: -1,
        direction: [],
        business: '',
        businessName: '',
        isWiki: '0',
        isEmail: '0',
        isHtml: '1',
        mailto: '',
        parent_id: '235650717',
        dateRange: dateRange,
        start: start,
        end: end
      }
    },
    methods: {
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      submit: function () {
        let self = this
        if (self.business && self.start && self.end) {
          self.run()
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      },
      run: function () {
        this.url = 'https://qareport.hotel.test.sankuai.com/notify/componentcheck/generateDurationStatisticsWiki?duration=' + this.start + '_' + this.end + '&direction=' + this.business
        let self = this
        axios.get(this.url).then(function (message) {
          if (message.data.status === 200) {
            self.$Message.success('生成成功！')
            self.$Modal.success({
              title: '生成成功',
              content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + message.data.data.toString() + '">' + message.data.data.toString() + '</a></P>'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.data.msg + '</P>'
            })
          }
        }).catch(function (error) {
          // 由网络或者服务器抛出的错误
          self.$Spin.hide()
          self.$Message.error(error.toString())
        })
      },
      getDirections: function () {
        let self = this
        axios(this.getDomain('config') + '/mcd/org/basic?direction_id=1&disable=1').then(function (message) {
          if (message.data.result) {
            self.directionList.length = 0
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      getCurrentChooseDirection: function (value, selectedData) {
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = {
          key: item.direction_id.toString(),
          label: item.label,
          value: item.value
        }
        console.log(item.value.toString())
        this.business = item.direction_id.toString()
        this.businessName = item.value.toString()
      }
    },
    mounted: function () {
      this.getDirections()
    }
  }
</script>


<style scoped>
  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
