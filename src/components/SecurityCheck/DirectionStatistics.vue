<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 本工具用于生成安全组件扫描方向维度统计文档 </p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 参数说明-时间点：格式为yyyyMMdd，如20200817，结果返回该时间点所在周的数据</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 参数说明-方向：返回选定方向的统计数据，方向可多选</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 参数说明-展开到叶子节点：选择是结果聚合到叶子节点方向，选择否数据按所选方向聚合</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找 <span class="ciTip">邹艳雪（zouyanxue）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/417092965" target="_blank">组件安全扫描说明文档</a></span></p>
     </Card>

    <Card :style="{borderWidth:0,marginTop:'10px',marginBottom:'5px'}"
                dis-hover>
            <div slot="title">
              <Row type="flex" style="width: 100%">
                <Col span="25" style="display: flex">
                  <div>
                    <span :style="{fontWeight:'bolder'}">查询时间点</span>
                  </div>
                  <div style="margin-top: -8px;width: 200px;margin-left: 20px">
                    <Input v-model="date" placeholder="e.g:20200810"/>
                  </div>
                  <div style="margin-left: 20px">
                    <span :style="{fontWeight:'bolder'}">查询方向</span>
                  </div>
                  <div style="margin-top: -8px;width: 400px;margin-left: 20px">
                    <direction :needDefault="true" :width="width"></direction>
                  </div>
                  <div style="margin-left: 20px">
                    <span :style="{fontWeight:'bolder'}">展开到叶子节点</span>
                  </div>
                  <div style="margin-top: -8px;width: 200px;margin-left: 20px">
                    <Select v-model="mode">
                      <Option v-for="item in directionItems" :value="item.key" :key="item.value">{{item.value}}</Option>
                    </Select>
                  </div>
                </Col>
              </Row>
            </div>
            <div>
              <div>
                <Row type="flex" :gutter="2" justify="center">
                  <Col span="2" order="1">
                    <Button type="primary" @click = 'save()'>生成</Button>
                  </Col>
                </Row>
              </div>
            </div>
    </Card>

  </div >
</template>

<script>
  import Direction from '../../components/AnalyticsOnes/multipleDirectionComponent'
  import axios from 'axios'
  export default {
    name: 'direction-statistics',
    components: {Direction},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 7)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      // 数据初始化
      return {
        start: start,
        end: end,
        dateRange: dateRange,
        directionList: [],
        width: 500,
        parentPage: '',
        directionItems: [
          {key: 'true', value: '是'},
          {key: 'false', value: '否'}
        ]
      }
    },
    methods: {
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      save: function () {
        let self = this
        if (this.date && this.mode) {
          self.searchResult()
        } else {
          self.$Message.info('存在必填参数未填！')
        }
      },
      searchResult: function () {
        let self = this
        axios.get('https://qareport.hotel.test.sankuai.com/notify/componentcheck/generateDirectionStatisticsWiki?date=' + this.date + '&directions=' + this.$store.state.directionIdList + '&mode=' + this.mode).then(function (message) {
          if (message.data.status === 200) {
            self.$Message.success('生成成功！')
            self.$Modal.success({
              title: '生成成功',
              content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + message.data.data.toString() + '">' + message.data.data.toString() + '</a></P>'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.data.msg + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
