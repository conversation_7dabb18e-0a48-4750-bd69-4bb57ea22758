<template>
    <div>
        <head-component></head-component>
        <Tabs class="tab" style='margin-top: 30px;margin-left: 5%;margin-right: 5%;width: auto;'>
            <TabPane label="方向维度报告" name="directionStatistics">
                <direction-statistics></direction-statistics>
            </TabPane>
            <TabPane label="时间维度报告" name="durationStatistics">
                <duration-statistics></duration-statistics>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
  import directionStatistics from './DirectionStatistics'
  import durationStatistics from './DurationStatistics'
  export default {
    name: 'securityCkeck',
    components: {directionStatistics, durationStatistics},
    data: function () {
      return {

      }
    }
  }
</script>

<style scoped>
  .tab{
    margin-top: 30px;
    margin-left: 1%;
    margin-right: 1%;
    width: auto
  }
</style>
