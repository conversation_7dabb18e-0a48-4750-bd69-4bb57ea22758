<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/dashboard">测试环境大盘</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="tab">
      <dashboard-unit></dashboard-unit>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import Vue from 'vue'
  import coverRatechart from '../ConfigurationCenter/coverRatechart'
  import { Bus } from '@/global/bus'
  import DashboardUnit from './dashboard-unit'
  import CommonFooter from '../Common/Footer'
  Vue.component('coverrate-chart-component', coverRatechart)
  export default {
    name: 'DashboardHomepage',
    components: {CommonFooter, DashboardUnit},
    data: function () {
      return {
        tab: 'dashboard',
        style: {
          'width': '100%',
          'height': (window.innerHeight - 55).toString() + 'px'
        },
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    }
  }
</script>

<style scoped>
  .tab{
    margin-top: 15px;
    margin-left: 1.0%;
    margin-right: 1.0%;
    width: auto
  }
</style>
