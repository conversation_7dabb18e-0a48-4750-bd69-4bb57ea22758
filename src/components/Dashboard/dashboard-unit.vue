<style scoped>
</style>
<template>
  <div style="margin-top: 20px">
    <Card :style="{borderWidth:0,marginTop:'10',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
      <div>
        <Row slot="title" :style="{fontWeight:'bolder'}" type="flex">
          <span style="padding-top: 5px">可用性 | 稳定性统计结果</span>
          <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" :transfer="false"
                    style="width: 410px;margin-top: 0px;padding-left: 10px" change-on-select
                    @on-change="getCurrentDirection">
          </Cascader>
          <Button type="primary" @click="handleSubmit(formInline),setTableData(formInline)" style="margin-left: 5px">
            查询
          </Button>
          <span style="font-weight: bolder;margin-left: 35px; padding-top: 5px">
           筛选条件：
         </span>
          <Select v-model="formInline.flag" @on-change="handleSubmit(formInline),setTableData(formInline)" style="width: 100px; margin-left: 3px">
            <Option value='0'>稳定性</Option>
            <Option value='1'>可用性</Option>
          </Select>
          <Select v-model="formInline.quarter" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option value='1'>最近1季度</Option>
            <Option value='2'>最近2季度</Option>
            <Option value='3'>最近3季度</Option>
            <Option value='4'>最近4季度</Option>
            <Option value='5'>最近5季度</Option>
          </Select>
          <Select v-model="formInline.month" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in monthList" :value="item.value" :key="item.value">最近{{item.label}}月</Option>
          </Select>
          <Select v-model="formInline.week" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in weekList" :value="item.value" :key="item.value">最近{{item.label}}周</Option>
          </Select>
          <Select v-model="formInline.day" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in dayList" :value="item.value" :key="item.value">最近{{item.label}}天</Option>
          </Select>
          <Button type="primary" @click="exportData()" style="margin-left: 5px">
            数据导出
          </Button>
        </Row>
      </div>
      <Row type="flex">
        <Content :style="{paddingBottom: '20px', minHeight: '15px', background: '#fff'}">
          <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
            <div slot="title">
              <Row type="flex" style="width: 98.8%">
                <Col style="text-align: right;width: 80%;display: flex">
                  <div>
                    <span :style="{fontWeight:'bolder'}" v-if="parseInt(formInline.flag) === 0">稳定性数据表格（达标值：<span
                      style="color: green; ">99.9%</span>）</span>
                    <span :style="{fontWeight:'bolder'}" v-else>可用性数据表格（达标值：<span
                      style="color: green; ">99.5%</span>）</span>
                  </div>
                </Col>
              </Row>
            </div>
            <Row>
              <Col style="width: 100%">
                <Table size="small" v-if="tableDisplay" stripe :columns="columns" :data="values" ref="table" :loading="loading" :no-data-text = "loadText" style="overflow-x: scroll; max-width: 100%"></Table>
              </Col>
            </Row>
          </Card>

          <TimelineItem id="decrease">
            <div style="margin-left: 5px;margin-top: -8px">
              <span style="font-weight: bolder;margin-left: 0px; padding-top: 5px" v-if="parseInt(formInline.flag) === 0">稳定性下降跟进</span>
              <span style="font-weight: bolder;margin-left: 0px; padding-top: 5px" v-else>可用性下降跟进</span>
              <DatePicker :value="dateRange" type="daterange" :clearable=false placement="bottom-end"
                          placeholder="请选择时间间隔" style="width: 180px" @on-change="filterByTime"></DatePicker>
              <span style="font-weight: bolder;margin-left: 35px; padding-top: 5px">
           筛选条件：
         </span>
              <Select v-model="status" style="width: 100px; margin-left: 3px">
                <Option value='2'>已跟进</Option>
                <Option value='1'>待跟进</Option>
              </Select>
              <Button type="primary" @click="setTableData(formInline)" style="margin-left: 5px">
                查询
              </Button>
            </div>
            <Table id="tool-table-header-reject" style="margin-top: 15px;width: 98.8%" border stripe :columns="decreaseColumns" :data="decreaseValues"></Table>
          </TimelineItem>

          <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
            <div slot="title">
              <Row type="flex" style="width: 98.8%">
                <Col style="text-align: right;width: 80%;display: flex">
                  <div>
                    <span :style="{fontWeight:'bolder'}" v-if="parseInt(formInline.flag) === 0">稳定性数据图表</span>
                    <span :style="{fontWeight:'bolder'}" v-else>可用性数据图表</span>
                  </div>
                </Col>
              </Row>
            </div>
            <div>
              <Row type="flex">
                <Col span="12" style="padding-right: 10px">
                  <div id="chart1" class="container1"></div>
                </Col>
                <Col span="12" style="padding-right: 10px">
                  <div id="chart2" class="container1"></div>
                </Col>
              </Row>
              <Row type="flex">
                <Col span="12" style="padding-right: 10px">
                  <div id="chart3" class="container1"></div>
                </Col>
                <Col span="12" style="padding-right: 10px">
                  <div id="chart4" class="container1"></div>
                </Col>
              </Row>
            </div>
          </Card>
          <div>
            <Row>
              <Col :key="index" span="6" v-for="(item, index) of decreaseReasonChartData">
                <div style="font-weight: bolder;padding-top: 5px"><div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div><span style="font-size: 16px">{{index}}下降原因分析</span></div>
                <common-chart :charttype="'pie'" :data="item" width="100%"></common-chart>
              </Col>
            </Row>
          </div>
        </Content>
      </Row>
    </Card>
  </div>
</template>
<script>
    import axios from 'axios'
    import { Bus } from '@/global/bus'
    import Highcharts from 'highcharts/highstock'  // 必须是highstock 不然nevigator不生效
    import CommonChart from '../AnalyticsOnes/commonChart'
    // import { Bus } from '@/global/bus'

    export default {
      name: 'dashboard-unit',
      components: {CommonChart},
      data: function () {
        let date = new Date()
        let dateRange = []
            // 设置初始时间
        let end = this.getTimeString(date)
        date.setDate(date.getDate() - 7)
        let start = this.getTimeString(date)
        dateRange.push(start)
        dateRange.push(end)
        return {
          decreaseReasonChartData: {},
          mis: Bus.userInfo.userLogin,
          start: start,
          end: end,
          status: '2',
          dateRange: dateRange,
          directionList: [],
          direction: [],
          key: '1',
          tableDisplay: true,
          chart: null,
          stockChart1: null,
          stockChart2: null,  // 以上新增加用来画图
          stockChart3: null,
          stockChart4: null,
          categories: [],
          formInline: {},
          loading: true,
          loadText: '暂无数据',
          ruleInline: {
            quarter: [{required: true}],
            month: [{required: true}],
            week: [{required: true}],
            day: [{required: true}]
          },
          columns: [],
          values: [],
          decreaseColumns: [{
            title: 'id',
            key: 'id',
            align: 'left',
            width: '70px'
          }, {
            title: '日期',
            key: 'date',
            align: 'left'
          }, {
            title: '主干地址',
            key: 'stack',
            align: 'left',
            width: '120px',
            render: (h, params) => {
              let url = params.row.stackUrl
              let name = params.row.stackName
              return h('a', {
                attrs: {
                  href: url,
                  target: '_blank'
                }
              }, name)
            }
          }, {
            title: '子业务方向',
            key: 'tag',
            align: 'left'
          }, {
            title: '分数',
            key: 'score',
            align: 'left'
          }, {
            title: '下降原因',
            key: 'reason',
            align: 'left',
            width: '150px',
            render: (h, params) => {
              return h('Select', {
                props: {
                  value: params.row.reason,
                  transfer: true
                },
                style: {
                  width: '98%',
                  margin: 'auto',
                  textAlign: 'left'
                },
                on: {
                  'on-change': (value) => {
                    params.row.reason = value
                    let index = params.row.id
                    this.changeRejectData(index, 'reason', params.row.reason)
                                        // this.saveRejectData(index, 'reason')
                  }
                }
              }, params.row.reasonOptions.map(function (option) {
                if (Object.keys(option).length > 0) {
                  return h('Option', {
                    props: {
                      value: option.id,
                      label: option.decreaseReason
                    },
                    style: {
                      width: '98%',
                      margin: 'auto',
                      textAlign: 'left'
                    }
                  })
                }
              }))
            }
          }, {
            title: '描述',
            key: 'desc',
            align: 'left',
            width: '600px',
            render: (h, params) => {
              let vm = this
              return h('Input', {
                props: {
                  value: params.row.desc
                },
                style: {
                  width: '550px'
                },
                on: {
                  'on-change': event => {
                    params.row.desc = event.target.value
                    vm.data[params.index] = params.row()
                    // this.changeRejectData(index, 'desc', params.row.desc)
                  }
                }
              })
            }
          }, {
            title: '操作人',
            key: 'mis',
            align: 'left'
          }, {
            title: '更新时间',
            key: 'updateTime',
            align: 'left'
          }, {
            title: '操作',
            key: 'desc',
            align: 'left',
            render: (h, params) => {
              return h('Button', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.saveRejectData(params.row)
                  }
                }
              }, '保存')
            }
          }],
          decreaseValues: [],
          weekList: [{
            value: '1',
            label: '1'
          }, {
            value: '2',
            label: '2'
          }, {
            value: '3',
            label: '3'
          }, {
            value: '4',
            label: '4'
          }],
          monthList: [
            {
              value: '1',
              label: '1'
            }, {
              value: '2',
              label: '2'
            }, {
              value: '3',
              label: '3'
            }
          ],
          dayList: [
            {
              value: '1',
              label: '1'
            }, {
              value: '2',
              label: '2'
            }, {
              value: '3',
              label: '3'
            }, {
              value: '4',
              label: '4'
            }, {
              value: '5',
              label: '5'
            }, {
              value: '6',
              label: '6'
            }, {
              value: '7',
              label: '7'
            }
          ]
        }
      },
      methods: {
        getCurrentDirection: function (value, selectedData) {
          let self = this
          for (let i in selectedData) {
            self.$set(self.formInline, 'directionId', selectedData[i].direction_id)
          }
          let item = selectedData[selectedData.length - 1]
          self.currentChooseDirection = item
        },
        getAnchorUrl: function () {
          return '?start=' + this.start + '&end=' + this.end + '#reject'
        },
        saveRejectData (row) {
          axios.post('https://qareport.hotel.test.sankuai.com/notify/fixReason', {
            id: row.id,
            flag: row.flag,
            reasonId: row.reason,
            reasonDes: row.desc,
            operator: this.mis
          }).then(response => {
            if (row.reason === null) {
              alert('请选择下降原因！')
            } else {
              alert('保存成功')
            }
          }
          )
        },
        changeRejectData (index, key, value) {
          for (let record of this.decreaseValues) {
            let idx = record.id
            if (index === idx) {
              record[key] = value
            }
          }
        },
        setTableData (name) {
          if (Object.keys(name).length === 0) {
            return
          }
          let self = this
          self.decreaseValues = []
          let param = {
            directionId: name.directionId,
            flag: name.flag,
            from: name.from || this.start,
            to: name.to || this.end,
            status: name.status || this.status
          }
          this.$router.push('/homepage/dashboard?directionId=' + param.directionId + '&flag=' + param.flag + '&from=' + param.from + '&to=' + param.to + '&status=' + param.status)
          self.$Spin.show()
          axios.get('https://qareport.hotel.test.sankuai.com/notify/monitorDetail', {
            params: param
          }).then(function (response) {
            self.$Spin.hide()
            self.display = true
            setTimeout(function () {
              let result = []
              for (let columnValue of response.data.data.stackTagMonitorEntities) {
                let unit = {
                  id: columnValue.id,
                  date: columnValue.date.split(' ')[0],
                  stackUrl: columnValue.stackUrl,
                  stackName: columnValue.stackName,
                  tag: columnValue.tag,
                  score: param.flag === '0' ? columnValue.tagStabilityScore : columnValue.tagAvailableScore,
                  reason: param.flag === '0' ? columnValue.stabilityReasonId : columnValue.availableReasonId,
                  reasonOptions: response.data.data.reasonConfig,
                  desc: param.flag === '0' ? columnValue.stabilityReasonDes : columnValue.availableReasonDes,
                  mis: columnValue.operator,
                  updateTime: columnValue.updateTime.split('T')[0],
                  operation: '',
                  flag: param.flag
                }

                result.push(unit)
              }

              let reason = response.data.data.decreaseReasons
              let tempReason = []
              let reject = []
              for (let re in reason) {
                reject.push({
                  name: re,
                  y: reason[re]
                })
              }
              let rejectTemp = {
                data: reject
              }
              tempReason.push(rejectTemp)
              let index = name.flag === '0' ? '稳定性' : '可用性'
              self.decreaseReasonChartData[index] = tempReason

              self.decreaseValues = result
              self.loading = false
              self.refreshTable()
              self.createChart()
            }, 1)
          }).catch(function () {
            self.display = false
            self.$Spin.hide()
            setTimeout(() => {
              self.$Message.error('加载数据失败')
            }, 1500)
          })
        },
        filterByTime: function (start) {
          this.dateRange = start
          this.start = start[0]
          this.end = start[1]
          let self = this
          self.timeChange = true
        },
        getTimeString: function (obj) {
          let result = ''
          let year = obj.getFullYear()
          let month = obj.getMonth() + 1
          let day = obj.getDate()
          result += year.toString() + '-'

          if (month >= 1 && month <= 9) {
            result = result + '0' + month
          } else {
            result += month
          }

          result += '-'

          if (day >= 0 && day <= 9) {
            result = result + '0' + day
          } else {
            result += day
          }
          return result
        },
        getDirections: function () {
          let self = this
          let url = this.getDomain('config') + '/mcd/org/basic?direction_id=1'
          axios.get(url).then(function (message) {
            self.directionList = []
            self.directionList.push(message.data.info)
          })
        },
        handleSubmit (name) {
          if (Object.keys(name).length === 0) {
            return
          }

          if (name.quarter === undefined && name.month === undefined && name.week === undefined && name.day === undefined) {
            name.quarter = '2'
            name.month = '2'
            name.week = '2'
            name.day = '2'
          }

          let self = this
          self.columns = []
          self.values = []

          let param = {
            flag: name.flag,
            quarter: name.quarter || 0,
            month: name.month || 0,
            week: name.week || 0,
            day: name.day || 0,
            directionId: name.directionId
          }
          this.$router.push('/homepage/dashboard?directionId=' + param.directionId + '&flag=' + param.flag + '&quarter=' + param.quarter + '&month=' + param.month + '&week=' + param.week + '&day=' + param.day)

          self.$Spin.show()
          axios.get('https://qareport.hotel.test.sankuai.com/notify/dashboard', {
            params: param
          }).then(function (response) {
            self.$Spin.hide()
            for (let col of response.data.data.columns) {
              delete col.width
            }
            self.display = true
            setTimeout(function () {
              self.columns = response.data.data.columns
              self.values = response.data.data.values
              self.loading = false
              self.refreshTable()
              self.createChart()
            }, 100)
          }).catch(function () {
            self.display = false
            self.$Spin.hide()
            setTimeout(() => {
              self.$Message.error('加载数据失败')
            }, 1500)
          })
        },
        exportData: function () {
          this.$refs.table.exportCsv({
            filename: '导出原始数据'
          })
        },
        refreshTable: function () {
          this.tableDisplay = false
          let self = this
          setTimeout(function () {
            self.tableDisplay = true
          }, 200)
        },
        createChart: function () {
          let self = this
          let chartData = []
          let chartMonthData = []
          let chartWeekData = []
          let chartDayData = []
          Highcharts.setOptions({
                    // navigator: {
                    //   enabled: true
                    // },
            colors: ['#ed3f14', '#C1FFC1', '#5cadff', '#19be6b', '#ff9900', '#2b85e4'],
                    // 禁用UTC时间
            global: {
              useUTC: false
            }
          })
          this.stockChart1 = new Highcharts.Chart('chart1', {
                    // navigator: {
                    //   enabled: true
                    // },
                    // colors: ['#ed3f14', '#C1FFC1', '#5cadff', '#19be6b', '#ff9900', '#2b85e4'],
                    // // 禁用UTC时间
                    // global: {
                    //   useUTC: false
                    // },
            chart: {
              zoomType: 'x'
            },
            title: {
              text: '季度数据'
            },
            xAxis: {
              type: 'categories', // 设置类型为datetime ,并设置显示格式
              categories: [],
              formatter: function () {
                return this.value
              }
            },
            legend: {
              layout: 'vertical',
              align: 'right',
              verticalAlign: 'top'
            },
            credits: {
              enabled: false
            },
            yAxis: {
              max: 100,
              min: 99,
              title: null,
              labels: {
                formatter: function () {
                  return this.value + '%'
                }
              },
              lineWidth: 2
            },
            series: [],
            tooltip: {
              shared: true,
              crosshairs: true
                        // dateTimeLabelFormats: {
                        //   millisecond: '%H:%M:%S.%L',
                        //   second: '%H:%M:%S',
                        //   minute: '%y-%m-%d %H:%M',
                        //   hour: '%y-%m-%d %H:%M',
                        //   day: '%y-%m-%d',
                        //   week: '%y-%m-%d',
                        //   month: '%y-%m',
                        //   year: '%Y'
                        // }
            },
            plotOptions: {
              line: {
                dataLabels: {
                                // 开启数据标签
                  enabled: true
                }
              },
              series: {
                events: {
                  legendItemClick: function (event) {
                    let series = this.chart.series
                    let index = this.index
                    if (series[index].visible) {
                                        // 除当前其他反转
                      for (let item in series) {
                        if (!series[item].visible) {
                          series[item].setVisible(true, true)
                        } else {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    } else {
                                        // 当前反转、其他取false
                      series[index].setVisible(true, true)
                      for (let item in series) {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  }
                }
              }
            }
          })
          this.stockChart2 = new Highcharts.Chart('chart2', {
            chart: {
              zoomType: 'x'
            },
            title: {
              text: '月数据'
            },
            xAxis: {
              type: 'categories', // 设置类型为datetime ,并设置显示格式
              categories: []
                        // labels: {
                        //   formatter: function () {
                        //     return Highcharts.dateFormat('%y-%m', new Date(this.x))
                        //   }
                        // },
                        // tooltip: {
                        //   pointFormat: '{point.x:%y-%m}: {point.y} '
                        // },
                        // dateTimeLabelFormats: {
                        //   millisecond: '%H:%M:%S.%L',
                        //   second: '%H:%M:%S',
                        //   minute: '%H:%M',
                        //   hour: '%H:%M',
                        //   day: '%m-%d',
                        //   week: '%m-%d',
                        //   month: '%y-%m',
                        //   year: '%Y'
                        // }
            },
            legend: {
              layout: 'vertical',
              align: 'right',
              verticalAlign: 'top'
            },
            credits: {
              enabled: false
            },
            yAxis: {
              max: 100,
              min: 99,
              title: null,
              labels: {
                formatter: function () {
                  return this.value + '%'
                }
              },
              lineWidth: 2
            },
            series: [],
            tooltip: {
              shared: true,
              crosshairs: true
                        // dateTimeLabelFormats: {
                        //   millisecond: '%H:%M:%S.%L',
                        //   second: '%H:%M:%S',
                        //   minute: '%y-%m-%d %H:%M',
                        //   hour: '%y-%m-%d %H:%M',
                        //   day: '%y-%m-%d',
                        //   week: '%y-%m-%d',
                        //   month: '%y-%m',
                        //   year: '%Y'
                        // }
            },
            plotOptions: {
              line: {
                dataLabels: {
                                // 开启数据标签
                  enabled: true
                }
              },
              series: {
                events: {
                  legendItemClick: function (event) {
                    let series = this.chart.series
                    let index = this.index
                    if (series[index].visible) {
                                        // 除当前其他反转
                      for (let item in series) {
                        if (!series[item].visible) {
                          series[item].setVisible(true, true)
                        } else {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    } else {
                                        // 当前反转、其他取false
                      series[index].setVisible(true, true)
                      for (let item in series) {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  }
                }
              }
            }
          })
          this.stockChart3 = new Highcharts.Chart('chart3', {
            chart: {
              zoomType: 'x'
            },
            title: {
              text: '周数据'
            },
            xAxis: {
              type: 'categories', // 设置类型为datetime ,并设置显示格式
              categories: []
            },
            legend: {
              layout: 'vertical',
              align: 'right',
              verticalAlign: 'top'
            },
            credits: {
              enabled: false
            },
            yAxis: {
              max: 100,
              min: 99,
              title: null,
              labels: {
                formatter: function () {
                  return this.value + '%'
                }
              },
              lineWidth: 2
            },
            series: [],
            tooltip: {
              shared: true,
              crosshairs: true
            },
            plotOptions: {
              line: {
                dataLabels: {
                                // 开启数据标签
                  enabled: true
                }
              },
              series: {
                events: {
                  legendItemClick: function (event) {
                    let series = this.chart.series
                    let index = this.index
                    if (series[index].visible) {
                                        // 除当前其他反转
                      for (let item in series) {
                        if (!series[item].visible) {
                          series[item].setVisible(true, true)
                        } else {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    } else {
                                        // 当前反转、其他取false
                      series[index].setVisible(true, true)
                      for (let item in series) {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  }
                }
              }
            }
          })
          this.stockChart4 = new Highcharts.Chart('chart4', {
            chart: {
              zoomType: 'x'
            },
            title: {
              text: '天数据'
            },
            xAxis: {
              type: 'categories', // 设置类型为datetime ,并设置显示格式
              categories: []
            },
            legend: {
              layout: 'vertical',
              align: 'right',
              verticalAlign: 'top'
            },
            credits: {
              enabled: false
            },
            yAxis: {
              max: 100,
              min: 99,
              title: null,
              labels: {
                formatter: function () {
                  return this.value + '%'
                }
              },
              lineWidth: 2
            },
            series: [],
            tooltip: {
              shared: true,
              crosshairs: true
            },
            line: {
              dataLabels: {
                            // 开启数据标签
                enabled: true
              }
            },
            plotOptions: {
              line: {
                dataLabels: {
                                // 开启数据标签
                  enabled: true
                }
              },
              series: {
                events: {
                  legendItemClick: function (event) {
                    let series = this.chart.series
                    let index = this.index
                    if (series[index].visible) {
                                        // 除当前其他反转
                      for (let item in series) {
                        if (!series[item].visible) {
                          series[item].setVisible(true, true)
                        } else {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    } else {
                                        // 当前反转、其他取false
                      series[index].setVisible(true, true)
                      for (let item in series) {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  }
                }
              }
            }
          })
          let category = []
          let categoryMonth = []
          let categoryWeek = []
          let categoryDay = []
                // 获取所有的category
          for (var m = 0; m < self.values.length; m++) {
            let keys = Object.keys(self.values[m])
            for (let j = 0; j < keys.length; j++) {
                        // console.log(keys[j] + '   ' + keys[j].length)
              if (keys[j].indexOf('Q') !== -1) {
                if (category.indexOf(keys[j]) === -1) {
                  category.push(keys[j])
                }
              }
              if (keys[j].length === 7 && keys[j].indexOf('W') === -1) {
                if (categoryMonth.indexOf(keys[j]) === -1) {
                  categoryMonth.push(keys[j])
                }
              }
              if (keys[j].indexOf('W') !== -1) {
                if (categoryWeek.indexOf(keys[j]) === -1) {
                  categoryWeek.push(keys[j])
                }
              }
                        // if (keys[j].length === 10 || keys[j].length === 27) {
              if (keys[j].length === 10) {
                if (categoryDay.indexOf(keys[j]) === -1) {
                  categoryDay.push(keys[j])
                }
              }
            }
          }
          category.sort()
          categoryMonth.sort()
          categoryWeek.sort()
          categoryDay.sort()
          for (var i = 0; i < self.values.length; i++) {
            chartData[i] = {}
            chartMonthData[i] = {}
            chartWeekData[i] = {}
            chartDayData[i] = {}
            chartData[i].data = []
            chartMonthData[i].data = []
            chartWeekData[i].data = []
            chartDayData[i].data = []
            chartData[i].title = self.values[i].direction
            chartMonthData[i].title = self.values[i].direction
            chartWeekData[i].title = self.values[i].direction
            chartDayData[i].title = self.values[i].direction
                    // alert(keys.toString())
            let data = self.values[i]
            for (let j = 0; j < category.length; j++) {
              if (data[category[j]]) {
                            // console.log(data[category[j]])
                chartData[i].data.push(
                                data[category[j]]
                            )
              } else {
                chartData[i].data.push(' ')
              }
            }
            for (let j = 0; j < categoryMonth.length; j++) {
              if (data[categoryMonth[j]]) {
                chartMonthData[i].data.push(
                                // keys[j],
                                data[categoryMonth[j]]
                            )
              } else {
                chartMonthData[i].data.push(' ')
              }
            }
            for (let j = 0; j < categoryWeek.length; j++) {
              if (data[categoryWeek[j]]) {
                chartWeekData[i].data.push(
                                // keys[j],
                                data[categoryWeek[j]]
                            )
              } else {
                chartWeekData[i].data.push(' ')
              }
            }
            for (let j = 0; j < categoryDay.length; j++) {
              if (data[categoryDay[j]]) {
                chartDayData[i].data.push(
                                // keys[j],
                                data[categoryDay[j]]
                            )
              } else {
                chartDayData[i].data.push(' ')
              }
            }
          }
          for (let t = 0; t < self.values.length; t++) {
                    // alert(keys.toString())
            this.stockChart1.addSeries({
              type: 'line',
              name: chartData[t].title,
                        // color: chartData[t].color,
              data: chartData[t].data // 给所有数据添加一个排序
            })
          }
          this.stockChart1.xAxis[0].setCategories(category)
          for (let t = 0; t < self.values.length; t++) {
                    // alert(keys.toString())
            this.stockChart2.addSeries({
              type: 'line',
              name: chartMonthData[t].title,
                        // color: chartData[t].color,
              data: chartMonthData[t].data// 给所有数据添加一个排序
            })
          }
          this.stockChart2.xAxis[0].setCategories(categoryMonth)
          for (let t = 0; t < self.values.length; t++) {
                    // alert(keys.toString())
            this.stockChart3.addSeries({
              type: 'line',
              name: chartWeekData[t].title,
              data: chartWeekData[t].data// 给所有数据添加一个排序
            })
          }
          this.stockChart3.xAxis[0].setCategories(categoryWeek)
          for (let t = 0; t < self.values.length; t++) {
                    // alert(keys.toString())
            this.stockChart4.addSeries({
              type: 'line',
              name: chartDayData[t].title,
              data: chartDayData[t].data// 给所有数据添加一个排序
            })
          }
          this.stockChart4.xAxis[0].setCategories(categoryDay)
        }
      },
      mounted: function () {
        let query = this.$route.query
        if (Object.keys(query).length === 0) {
          query = {
            directionId: '204',
            flag: '0',
            quarter: '2',
            month: '2',
            week: '2',
            day: '2'
          }
        }
        let self = this
        self.$set(self.formInline, 'directionId', query.directionId)
        self.$set(self.formInline, 'flag', query.flag)
        self.$set(self.formInline, 'quarter', query.quarter)
        self.$set(self.formInline, 'month', query.month)
        self.$set(self.formInline, 'week', query.week)
        self.$set(self.formInline, 'day', query.day)

          // 重新赋值from & to
        if (query.from != null && query.to != null) {
          self.dateRange = []
          self.dateRange.push(query.from)
          self.dateRange.push(query.to)
        }
          // 重新赋值status
        if (query.status != null) {
          this.$nextTick(() => {
            self.status = query.status
          })
        }
        // 重新赋值direction
        let url = this.getDomain('config') + '/mcd/org/basic_info?direction_id=' + query.directionId
        axios.get(url).then(function ({data: {info}}) {
          self.direction = []
          for (let i in info.parent_list) {
            self.direction.push(info.parent_list[i].direction_name)
          }
        })

        this.getDirections()
        this.setTableData(query)
        this.handleSubmit(query)
      }
    }
</script>

<style scoped>
  .container1 {
    margin-top: 10px;
    padding-top: 20px;
    /*width: 700px;*/
    height: 250px;
    /*margin-left: 10px;  !*不设置时自动居中*!*/
  }
</style>
