<template>
  <div style="margin-top: 30px; margin-left: 5%; margin-right: 5%; width: auto;">
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i aria-hidden="true" class="fa fa-exclamation"></i> 注意事项</h4>
      <p><i aria-hidden="true" class="fa fa-circle-o"></i> 使用中遇到问题请找 <span
        class="ciTip">李可欣（likexin06）、孙逢钧（sunfengjun）</span></p>
    </Card>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <p class="time">仓库:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i aria-hidden="true" class="fa fa-question-circle"></i> 如：scp-make
        </p>
        <Row :gutter="16" type="flex">
          <Col order="1" span="24">
            <Input placeholder="请填写仓库名" v-model="repo"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">git地址:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i aria-hidden="true" class="fa fa-question-circle"></i>如：ssh://*******************/nib/scp-make.git
        </p>
        <Row :gutter="16" type="flex">
          <Col order="1" span="24">
            <Input placeholder="请填写GIT地址" v-model="git"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">源分支:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i aria-hidden="true" class="fa fa-question-circle"></i>通常为feature分支，如：feature/IEGPR-2020-7774672/switchCalendarPrice
        </p>
        <Row :gutter="16" type="flex">
          <Col order="1" span="24">
            <Input placeholder="请填写源分支" v-model="sourceBranch"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">目标分支:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i aria-hidden="true" class="fa fa-question-circle"></i>通常为master分支
        </p>
        <Row :gutter="16" type="flex">
          <Col order="1" span="24">
            <Input placeholder="请填写目标分支" v-model="targetBranch"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">消息接收人:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i aria-hidden="true" class="fa fa-question-circle"></i>填写mis号，如：likexin06，若需要多个接收人用英文逗号分割
        </p>
        <Row :gutter="16" type="flex">
          <Col order="1" span="24">
            <Input placeholder="请填写消息接收人" v-model="to"/>
          </Col>
        </Row>
      </TimelineItem>

    </Timeline>
    <Row :gutter="2" justify="center" type="flex">
      <Col order="1" span="2">
        <Button @click='submit()' type="primary">提交</Button>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  export default {
    name: 'CodeAnalysis',
    data: function () {
      return {
        repo: '',
        git: '',
        sourceBranch: '',
        targetBranch: '',
        to: ''
      }
    },
    methods: {
      submit: function () {
        let self = this
        if (this.repo && this.git && this.sourceBranch && this.targetBranch && this.to) {
          axios.post('https://qareport.hotel.test.sankuai.com/analysis/chainParseDirect', {
            repo: this.repo,
            git: this.git,
            sourceBranch: this.sourceBranch,
            targetBranch: this.targetBranch,
            to: this.to
          }).then(function (message) {
            if (message.data.success === true) {
              self.$Message.success('提交成功！')
              self.$Modal.success({
                title: '提交成功！'
              })
            } else {
              self.$Spin.hide()
              self.$Message.error('提交失败！')
              self.$Modal.error({
                title: '提交失败',
                content: '<P>错误原因：' + message.data.message + '</P>'
              })
            }
          })
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
