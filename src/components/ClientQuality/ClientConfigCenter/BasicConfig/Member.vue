<template>
  <div>
    <Tag
      v-for="item in userList"
      :key="item.id"
      :name="item.misId"
      type="border"
      :closable="
        $store.state.clientQuality.configCenterUserPermission.isAdmin ||
        $store.state.clientQuality.configCenterUserPermission.permissionLevel ==
          10 ||
        (role != 'manager' && $store.state.clientQuality.configCenterUserPermission.permissionLevel <
          100)
      "
      @on-close="deleteUserById(item.id)"
      
      >{{ item.misId }}</Tag
    >
    <div v-if="isEdit" style="width: 200px">
      <Input
        class="suffix"
        v-model="misId"
        placeholder="请输入MIS ID"
        size="small"
      >
        <ButtonGroup size="small" slot="suffix">
          <Button type="primary" @click="addUser()">确定</Button>
          <Button @click="cancelEdit()">取消</Button>
        </ButtonGroup>
      </Input>
    </div>
    <Button
      v-else-if="
        $store.state.clientQuality.configCenterUserPermission.isAdmin ||
        $store.state.clientQuality.configCenterUserPermission.permissionLevel <
          100
      "
      icon="ios-add"
      type="dashed"
      :disabled="!$store.state.clientQuality.configCenterUserPermission.isAdmin && (role == 'manager' && $store.state.clientQuality.configCenterUserPermission.permissionLevel >
          10)"
      size="small"
      @click="add"
      >Add</Button
    >
  </div>
</template>
<script>
import { Bus } from '@/global/bus'
/* eslint-disable */
export default {
  name: "Member",
  props: ["userList", "role"],
  data() {
    return {
      isEdit: false,
      misId: ""
    };
  },
  methods: {
    async deleteUserById(userId) {
      this.$axios({
        method: "post",
        data: {
          userId: userId,
        },
        url: this.env.url + "autoTestConfig/deleteUserById",
      }).then((res) => {
        let message = res.data;
        if (message.code === 200) {
          this.$Message.success("删除成功！~");
          this.$store.dispatch(
            "getUsersByBusinessId",
            this.$route.params.businessId
          );
          this.$store.dispatch('getPermission', {
            misId: Bus.userInfo.userLogin,
            businessId: this.$route.params.businessId
          });
        } else {
          this.$Message.error("删除失败！");
        }
      });
    },
    add() {
      this.isEdit = true;
    },
    cancelEdit() {
      this.misId = "";
      this.isEdit = false;
    },
    async addUser() {
      if (this.misId.trim() === "") {
        this.$Message.error("MIS ID 不可以为空");
      } else {
        this.$axios({
          method: "post",
          data: {
            businessId: this.$route.params.businessId,
            misId: this.misId,
            role: this.role,
          },
          url: this.env.url + "autoTestConfig/addUser",
        })
          .then((res) => {
            let message = res.data;
            if (message.code === 200) {
              this.$Message.success("保存成功！~");
              this.isEdit = false;
              this.$store.dispatch(
                "getUsersByBusinessId",
                this.$route.params.businessId
              );
              this.misId = "";
            } else if (message.errorMsg === "请勿重复添加！") {
              this.$Message.error("请勿重复添加！");
            }
          })
          .catch(function (error) {});
      }
    },
  },
};
</script>
<style scoped>
.suffix /deep/ .ivu-input-suffix {
  width: 80px;
}
.ivu-btn-small{
  font-size: 12px;
}
.ivu-btn-group-small>.ivu-btn{
  font-size: 12px;
}
</style>