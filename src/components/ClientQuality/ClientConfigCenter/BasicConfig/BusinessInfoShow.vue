<template>
  <Form :model="businessInfo" :label-width="120" label-position="left" style="margin-top: 20px">
    <FormItem label="业务方向ID">
      <strong>{{ businessInfo.id }}</strong>
    </FormItem>
    <FormItem label="业务方向标识">
      <strong>{{ businessInfo.business }}</strong>
    </FormItem>
    <FormItem label="业务方向名称">
      <p>{{ businessInfo.label }}</p>
    </FormItem>
    <FormItem label="业务相关产品">
      <Tag v-for="item in businessInfo.productList " color="orange" :key=item.id>
        {{item.label}}
      </Tag>
    </FormItem>
    <FormItem label="短链路代码仓库">
      <span>{{ businessInfo.uiTestRepo }}</span>
    </FormItem>
  </Form>
</template>
<script>
/* eslint-disable */
export default {
  name: 'BusinessInfoShow',
  props: ['businessInfo'],
  data() {
    return {}
  }
}
</script>
<style scoped>
.ivu-form >>> .ivu-form-item-label {
  font-size: 12px;
}
div >>> .ivu-form-item-content {
  font-size: 12px;
}
</style>