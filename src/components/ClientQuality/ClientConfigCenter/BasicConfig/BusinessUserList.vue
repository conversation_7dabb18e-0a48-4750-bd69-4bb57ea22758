<template>
  <div>
    <Row class="user-cell">
      <Col span="2">
        <strong>管理员: </strong>
      </Col>
      <Col span="22">
        <Member :userList="userList.managers" role="manager"></Member>
      </Col>
    </Row>
    <Row class="user-cell">
      <Col span="2">
        <strong>成员:</strong>
      </Col>
      <Col span="22">
        <Member :userList="userList.members" role="member"></Member>
      </Col>
    </Row>
    <Row class="user-cell">
      <Col span="2">
        <strong>邮件组:</strong>
      </Col>
      <Col span="22">
        <Member :userList="userList.mailGroups" role="mailGroup"></Member>
      </Col>
    </Row>
  </div>
</template>
<script>
/* eslint-disable */
import Member from "./Member";
export default {
  name: "BusinessUserList",
  components: { Member },
  props: ["userList"],
  data() {
    return {};
  },
};
</script>
<style scoped>
.user-cell {
  margin-bottom: 20px;
}
</style>