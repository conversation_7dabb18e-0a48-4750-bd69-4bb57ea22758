<template>
    <Form
      :model="editInfo"
      ref="refEditInfo"
      :label-width="120"
      label-position="left"
      style="width: 650px; margin-top: 20px"
    >
    <FormItem label="检查项">   
      <Select v-model="editInfo.checker" multiple filterable transfer placeholder="Select" style="width:500px">
        <Option v-for="(value, index) in checkerList" :value="value" :key="index">{{ value }}</Option>
      </Select>
      <a href="https://km.sankuai.com/collabpage/1954351338#id-%E6%89%AB%E6%8F%8F%E6%A3%80%E6%9F%A5%E9%A1%B9"
        target="_blank">
        <Icon type="md-help-circle" />
      </a>
    </FormItem>

    <FormItem label="大象群id" >
      <Input v-model="editInfo.daxiangId" style="width: 200px;" />
      <Poptip trigger="hover" content="修改大象群id需联系zhangsongna申请推送权限，未配置大象群id无法正确推送检查结果" placement="right-start">
        <Icon type="md-help-circle" style="margin-left:5px" />
      </Poptip>
    </FormItem>

    <FormItem>
      <Button type="primary" size="small" @click="submit">Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="toView">Cancel</Button>
    </FormItem>
    </Form>
  </template>
  <script>
  /* eslint-disable */
  import { Bus } from "@/global/bus";
  import { FormItem, Poptip } from "view-design";
  export default {
    name: "DataCheckEdit",
    props: ["editInfo"],
    data() {
        return {
          checkerList: [
              "NotExistsURLScheme",
              "NotExistsCategory",
              "NotExistsQaManager",
              "NotExistsP1Scene",
              "NotExistsApiDiffScene",
              "ClientABTestWithoutPage",
              'NotExistsFedoCategory',
              "NotExistsFedoCategory_MAXandMRN",
              "NotExistsScene",
              "NotExistsRequiredQuery"
          ]
        };
    },
    methods: {
        toView() {
            Bus.$emit("cancelDataCheckEdit");
        },
        submit() {
            this.$axios({
                method: "post",
                data: this.editInfo,
                url: this.env.url + "autoTestConfig/updateBusinessById",
            })
                .then((res) => {
                let message = res.data;
                if (message.code === 200) {
                    Bus.$emit("submitDataCheckEdit");
                }
                else {
                    this.$Message.error("保存失败！");
                }
            })
                .catch(function (error) { });
        },
    },
    components: { FormItem, Poptip }
};
  </script>
  <style scoped>
  .ivu-form >>> .ivu-form-item-label{
    font-size: 12px;
  }
  div >>> .ivu-form-item-content{
    font-size: 12px;
  }
  div >>> .ivu-btn-small{
    font-size: 12px;
  }

  </style>