<template>
  <Form
    :model="editInfo"
    ref="refEditInfo"
    :label-width="120"
    label-position="left"
    style="width: 600px; margin-top: 20px"
  >
    <FormItem label="业务方向ID">
      <strong>{{ editInfo.id }}</strong>
    </FormItem>
    <FormItem label="业务方向标识" prop="name">
      <strong>{{ editInfo.business }}</strong>
    </FormItem>
    <FormItem label="业务方向名称">
      <Input
        v-model="editInfo.label"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 5 }"
      >
      </Input>
    </FormItem>
    <FormItem label="短链路代码仓库">
      <Input v-model="editInfo.uiTestRepo" />
    </FormItem>

    <FormItem>
      <Button type="primary" size="small" @click="submit">Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="toView"
        >Cancel</Button
      >
    </FormItem>
  </Form>
</template>
<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
export default {
  name: "BusinessInfoEdit",
  props: ["editInfo"],
  data() {
    return {};
  },
  methods: {
    toView() {
      Bus.$emit("cancelEdit");
    },
    submit() {
      this.$axios({
        method: "post",
        data: this.editInfo,
        url: this.env.url + "autoTestConfig/updateBusinessById",
      })
        .then((res) => {
          let message = res.data;
          if (message.code === 200) {
            Bus.$emit("submitEdit");
          } else {
            this.$Message.error("保存失败！");
          }
        })
        .catch(function (error) {});
    },
  },
};
</script>
<style scoped>
.ivu-form >>> .ivu-form-item-label{
  font-size: 12px;
}
div >>> .ivu-form-item-content{
  font-size: 12px;
}
div >>> .ivu-btn-small{
  font-size: 12px;
}
</style>