<template>
    <Form :model="businessInfo" :label-width="120" label-position="left" style="margin-top: 20px">
      <FormItem label="检查项">
        <div v-if="businessInfo.checker && businessInfo.checker.length">
          <Tag v-for="item in businessInfo.checker" color="orange" :key=item.id>
            {{item}}
          </Tag>
        </div>
        <span v-else>无</span>
      </FormItem>
      <FormItem label="大象群id">
        <span>{{ businessInfo.daxiangId || '无' }}</span>
      </FormItem>
    </Form>
  </template>
  <script>
  /* eslint-disable */
  export default {
    name: 'DataCheckShow',
    props: ['businessInfo'],
    data() {
      return {}
    }
  }
  </script>
  <style scoped>
  .ivu-form >>> .ivu-form-item-label {
    font-size: 12px;
  }
  div >>> .ivu-form-item-content {
    font-size: 12px;
  }
  </style>