<template>
  <div>
    <Modal :value="configLayerShow" :title="configLayerTitle" @on-cancel="closeConfigInfo" @on-visible-change="setDefalutNode">
      <Form ref="formItem" :model="formItem" :rules="formRules" :label-width="80">
        <Form-item label="配置名称" prop="configName">
          <Input placeholder="国内住宿跟版-提测" v-model="formItem.configName"></Input>
        </Form-item>
        <FormItem label="触发时机" prop="nodeId">
          <Select placeholder="Native-提测" v-model="formItem.nodeId">
            <Option v-for="item in nodeList" :value="item.id" :key="item.id">{{ item.publishName }}-{{ item.integrationName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="产品类型" prop="productIds">
          <Select placeholder="美团-iOS" v-model="formItem.productIds" multiple>
            <Option v-for="(item, index) in productList" :value="item.id" :key="index">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="是否启用" prop="enable">
          <i-switch size="large" v-model="formItem.enable" :true-value="1" :false-value="0">
            <span slot="open">启用</span>
            <span slot="close">关闭</span>
          </i-switch>
        </FormItem>
      </Form>
      <div slot="footer">
        <div v-if="configLayerTitle.indexOf('添加') != -1">
          <Button type="primary" size="small" @click="checkConfigInfo(1)">确定</Button>
          <Button type="info" size="small" @click="closeConfigInfo">取消</Button>
        </div>
        <div v-if="configLayerTitle.indexOf('编辑') != -1">
          <Button type="primary" size="small" @click="checkConfigInfo(1)">另存为</Button>
          <Button type="primary" size="small" @click="checkConfigInfo(0)">保存</Button>
          <Button type="info" size="small" @click="closeConfigInfo">取消</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
export default {
  name: 'AutotestConfigInfo',
  props: ['formItem', 'configLayerTitle', 'nodeList', 'configLayerShow'],
  data() {
    return {
      formRules: {
        configName: [
          {
            required: true,
            message: '请填写配置名称',
            trigger: 'change'
          }
        ],
        nodeId: [
          {
            required: true,
            message: '请选择触发时机',
            trigger: 'change',
            type: 'number'
          }
        ],
        productIds: [
          {
            required: true,
            message: '请选择产品类型',
            type: 'array',
            trigger: 'change'
          }
        ]
      },
      productList: []
    }
  },
  mounted() {
    this.getProductList()
  },
  methods: {
    setDefalutNode() {
      //如只有一个node类型，则默认选中该node
      if (this.nodeList.length === 1) {
        this.formItem.nodeId = this.nodeList[0].id
      }
    },
    getProductList() {
      this.$axios({
        method: 'get',
        url: this.env.url + 'page/getProductList'
      })
        .then((res) => {
          let message = res.data
          this.productList = message
        })
        .catch(function (error) {})
    },
    checkConfigInfo(isAdd) {
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          if (isAdd == 1) {
            this.addConfigInfo()
          }
          if (isAdd == 0) {
            this.updateConfigInfo()
          }
        } else {
          this.$Notice.warning({
            title: '请补充信息'
          })
        }
      })
    },
    addConfigInfo() {
      this.$axios({
        method: 'post',
        data: {
          configName: this.formItem.configName,
          businessId: this.$route.params.businessId,
          productIds: this.formItem.productIds,
          nodeId: this.formItem.nodeId,
          enable: this.formItem.enable
        },
        url: this.env.url + 'autoTestConfig/updateConfig'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '配置添加成功！~'
            })
            this.closeConfigInfo('0')
          } else if (message.code === 204) {
            this.$Notice.warning({
              title: '请勿重复添加'
            })
          } else {
            this.$Notice.warning({
              title: '添加失败 :('
            })
          }
        })
        .catch(function (error) {})
    },
    updateConfigInfo() {
      this.$axios({
        method: 'post',
        data: {
          configId: this.formItem.configItem.id,
          configName: this.formItem.configName,
          businessId: this.$route.params.businessId,
          productIds: this.formItem.productIds,
          nodeId: this.formItem.nodeId,
          enable: this.formItem.enable
        },
        url: this.env.url + 'autoTestConfig/updateConfig'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '配置保存成功！~'
            })
            this.closeConfigInfo('0')
          } else if (message.code === 204) {
            this.$Notice.warning({
              title: '请勿重复添加'
            })
          } else {
            this.$Notice.warning({
              title: '添加失败 :('
            })
          }
        })
        .catch(function (error) {})
    },
    closeConfigInfo(val) {
      this.$emit('closeConfigInfo', val)
    }
  }
}
</script>

<style>
.ivu-input {
  font-size: 12px;
}
.ivu-select-multiple .ivu-select-selection .ivu-select-placeholder {
  font-size: 12px;
}
</style>
