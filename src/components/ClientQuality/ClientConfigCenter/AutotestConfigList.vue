<template>
  <div>
    <dl>
      <div v-for="(item, index) in configList" :key="item.id">
        <dt class="config-list">
          <Collapse simple class="config-collapse" v-model="collapseVal[index]">
            <Panel name="1" class="config-panel" hide-arrow>
              <!--列表标题-->
              <p>
                <Row>
                  <Col class="config-index" span="1">#{{ index + 1 }}</Col>
                  <Col span="5" class="config-title">
                  {{ item.configName }}
                  <Badge :count="strategyCounts[index]" type="normal">
                  </Badge>
                  </Col>
                  <Col span="9">
                  <Tag color="blue">{{ item.nodeInfo.publishName }}-{{
                        item.nodeInfo.integrationName
                      }}</Tag>
                  <span v-for="productItem in item.productLabel">
                    <Tag color="volcano">{{ productItem }}</Tag>
                  </span>
                  </Col>
                  <Col span="2">
                  <Tag class="form-button" :color="getEnableStatus(item.enable)"><span v-if="item.enable === 1">已启用</span>
                    <span v-else>未启用</span>
                  </Tag>
                  </Col>
                  <Col v-if="permission" span="2">
                  <Button class="form-button" @click.stop="
                        openConfigEditLayer('编辑触发节点配置', item)
                      ">编辑</Button><Button class="form-button" @click.stop="beforeDeleteConfig(item.id)">删除</Button>
                  </Col>
                  <Col v-if="permission" span="2">
                  <Button class="form-button" icon="ios-add" type="dashed" @click.stop="
                        openStrategyEditLayer('添加执行策略', item.id)
                      ">点击添加自动化策略</Button>
                  </Col>
                </Row>
              </p>
              <!--列表展开内容-->
              <div slot="content">
                <div class="card-div" v-for="(testItem, index) in testItemList" :key="testItem.name">
                  <AutotestStrategyCardTable :strategyList="item.strageties[testItem.name]" :autotestType="testItem"></AutotestStrategyCardTable>
                </div>
              </div>
            </Panel>
          </Collapse>
        </dt>
        <dd></dd>
      </div>
    </dl>
    <!-- 策略添加/编辑弹层 -->
    <AutotestStragegyInfo :strategyLayerShow="strategyLayerShow" :strategyLayerTitle="strategyLayerTitle" :strategyFormItem="strategyFormItem" v-on:closeStrategyInfo="closeStrategyInfo"></AutotestStragegyInfo>
  </div>
</template>
<script>
/* eslint-disable */
import AutotestStrategyCardTable from './AutotestStrategyCardTable'
import AutotestStragegyInfo from './AutotestStrategyInfo'
export default {
  name: 'AutotestConfigList',
  inject: ['reload'],
  props: ['businessInfo', 'configList', 'formItem', 'nodeType'],
  components: { AutotestStrategyCardTable, AutotestStragegyInfo },
  data() {
    return {
      strategyLayerShow: false,
      strategyLayerTitle: '',
      strategyFormItem: {
        configId: 0,
        businessPriorityName: '',
        businessPriority: '',
        taskType: ['requirement', 'defect'],
        autotestType: 'shortLink',
        versionPriority: '1',
        params: {
          directionalTriggered: '0',
          casePriority: ['p1', 'p2', 'p3', 'p4'],
          advancedParams: {}
        }
      },
      collapseVal: {}
    }
  },
  mounted() {},
  computed: {
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    strategyCounts() {
      return this.configList.map(function (item) {
        let count = 0
        for (var val in item.strageties) {
          count += item.strageties[val].length
        }
        return count
      })
    },
    permission() {
      if (
        this.$store.state.clientQuality.configCenterUserPermission.isAdmin ||
        this.$store.state.clientQuality.configCenterUserPermission
          .permissionLevel < 20
      ) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    reflesh() {
      this.reload()
    },
    openConfigEditLayer(layerTitle, configItem) {
      this.$emit(
        'openConfigEditLayer',
        this.nodeType,
        layerTitle,
        false,
        configItem
      )
    },
    closeConfigInfo(val) {
      this.$emit('closeConfigInfo', val)
    },
    openStrategyEditLayer(layerTitle, configId) {
      this.strategyFormItem.configId = configId
      this.strategyLayerTitle = layerTitle
      this.strategyLayerShow = true
      this.strategyFormItem.businessPriorityName = this.businessInfo.business
      this.strategyFormItem.businessPriority = this.businessInfo.id
      this.strategyFormItem.taskType = ['requirement', 'defect']
      this.strategyFormItem.autotestType = 'shortLink'
      this.strategyFormItem.versionPriority = '1'
      this.strategyFormItem.params.directionalTriggered = '0'
      this.strategyFormItem.params.casePriority = ['p1', 'p2', 'p3', 'p4']
      this.strategyFormItem.params.advancedParams = {}
    },
    closeStrategyInfo(val) {
      if (val === '0') {
        this.$emit('getStrategyByBusinessId')
      }
      this.strategyLayerShow = false
    },
    beforeDeleteConfig(configId) {
      var message = confirm('删除配置的同时也会删除其下的策略，确定要删除么？')
      if (message) {
        this.deleteConfigById(configId)
      }
    },
    deleteConfigById(configId) {
      this.$axios({
        method: 'post',
        data: {
          configId: configId
        },
        url: this.env.url + 'autoTestConfig/deleteConfigById'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '删除成功！~'
            })
            this.reflesh()
          } else {
            this.$Notice.warning({
              title: '删除失败，请刷新页面后再试试 :('
            })
          }
        })
        .catch(function (error) {})
    },
    getEnableStatus(isEnable) {
      if (isEnable === undefined) {
        return 'default'
      }
      switch (isEnable) {
        case 1:
          return 'green'
        case 0:
          return 'default'
        default:
          return 'yellow'
      }
    }
  }
}
</script>
<style scoped>
.config-index {
  color: #007fff;
  font-size: 14px;
}
.config-title {
  font-size: 14px;
  font-weight: bold;
}
.config-list {
  vertical-align: bottom;
  border-style: none none dashed none;
  border-bottom-width: 1.8px;
  border-color: Gainsboro;
  font-weight: normal;
}
.config-collapse {
  border-style: none;
  vertical-align: bottom;
}
.config-panel {
  vertical-align: bottom;
}
.folder-can {
  color: #007fff;
  margin-left: 55px;
  display: inline-block;
}
.form-button {
  margin-right: 4px;
  padding: 1px 4px;
  font-size: 12px;
  height: 22px;
}
.card-div {
  margin-left: 40px;
  margin-bottom: -10px;
}
</style>