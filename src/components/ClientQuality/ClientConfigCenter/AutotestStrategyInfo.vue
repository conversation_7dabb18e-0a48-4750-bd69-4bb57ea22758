<template>
  <div>
    <Modal :value="strategyLayerShow" :title="strategyLayerTitle" @on-cancel="closeStrategyInfo">
      <Form ref="strategyFormItem" :model="strategyFormItem" :rules="strategyFormRules" :label-width="120">
        <FormItem label="自动化类型" prop="autotestType">
          <Select placeholder="短链路自动化" v-model="strategyFormItem.autotestType" value="shortLink">
            <Option v-for="item in testItemList" :value="item.name" :key="item.index">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <Row>
          <Col span="12">
          <FormItem prop="versionPriority">
            <span slot="label">
              <Poptip>
                触发版本
                <Icon type="ios-help-circle-outline" size="13" />
                <div slot="content">
                  <a href="https://km.sankuai.com/page/995658074" target="_blank">自动化触发使用哪个版本APP</a>
                </div>
              </Poptip>
            </span>
            <Select placeholder="1" v-model="strategyFormItem.versionPriority">
              <Option value="1" label="1">
                <span>1</span>
                <span style="float: right; color: #ccc">版本优先级1</span>
              </Option>
              <Option value="2" label="2">
                <span>2</span>
                <span style="float: right; color: #ccc">版本优先级2</span>
              </Option>
              <Option value="3" label="3">
                <span>3</span>
                <span style="float: right; color: #ccc">版本优先级3</span>
              </Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem prop="businessPriority">
            <span slot="label">
              <Poptip content="触发哪个业务线的自动化">
                触发业务方向
                <Icon type="ios-help-circle-outline" size="13" />
              </Poptip>
            </span>
            <Select placeholder="hotel" v-model="strategyFormItem.businessPriority">
              <Option v-for="item in this.$store.state.clientQuality.businessList" :value="item.value" :key="item.label">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col>
          <FormItem prop="taskType">
            <span slot="label">
              <Poptip content="哪种类型的提测会触发自动化">
                task类型
                <Icon type="ios-help-circle-outline" size="13" />
              </Poptip>
            </span>
            <Select placeholder="需求" v-model="strategyFormItem.taskType" multiple>
              <Option v-for="item in taskTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col>
          <FormItem prop="directionalTriggered">
            <span slot="label">
              <Poptip content="执行哪些Case（页面维度）">
                Case范围
                <Icon type="ios-help-circle-outline" size="13" />
              </Poptip>
            </span>
            <Select v-model="strategyFormItem.params.directionalTriggered">
              <Option v-for="item in directionalTriggeredList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
        </Row>
        <FormItem prop="casePriority">
          <span slot="label">
            <Poptip content="执行哪些Case（场景优先级维度）">
              Case优先级
              <Icon type="ios-help-circle-outline" size="13" />
            </Poptip>
          </span>
          <Select placeholder="p1" v-model="strategyFormItem.params.casePriority" multiple>
            <Option v-for="item in casePriorityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="caseImpl">
          <span slot="label">
            <Poptip content="执行哪些Case（场景技术栈/产物）">
              产物
              <Icon type="ios-help-circle-outline" size="13" />
            </Poptip>
          </span>
          <Select placeholder="不选择默认为全部技术栈" v-model="strategyFormItem.params.implement" multiple>
            <Option v-for="item in implList" :value="item" :key="item">{{item}}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <span slot="label">阻塞通过基线</span>
          <Input style="width:40%" v-model="strategyFormItem.passRate" />
          <span>%</span>
          <i-switch size="small" v-model="strategyFormItem.passRateSwitch" :true-value="1" :false-value="0" />
        </FormItem>
        <FormItem prop="Advanced">
          <span slot="label">
            <Tooltip theme="light">
              Advanced
              <Icon type="ios-help-circle-outline" size="13" />
              <div slot="content">
                高级参数，示例：
                <pre>{{ advancedExample }}</pre>
              </div>
            </Tooltip>
          </span>
          <vue-json-editor v-model="strategyFormItem.params.advancedParams" :mode="'code'" lang="zh" />
        </FormItem>
        <FormItem>
          <span slot="label">备注</span>
          <Input v-model="strategyFormItem.params.remark" type="textarea" placeholder="自动化策略描述信息，不影响Case执行" />
        </FormItem>
      </Form>
      <div slot="footer">
        <div v-if="strategyLayerTitle == '添加执行策略'">
          <Button class="form-button" type="primary" size="small" @click="checkStrategyInfo(1)">确定</Button>
          <Button class="form-button" type="info" size="small" @click="closeStrategyInfo">取消</Button>
        </div>
        <div v-if="strategyLayerTitle == '编辑执行策略'">
          <Button type="primary" size="small" @click="checkStrategyInfo(1)">另存为</Button>
          <Button type="primary" size="small" @click="checkStrategyInfo(0)">保存</Button>
          <Button type="info" size="small" @click="closeStrategyInfo">取消</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import vueJsonEditor from 'vue-json-editor'
export default {
  name: 'AutotestStragegyInfo',
  inject: ['reload'],
  props: ['strategyLayerShow', 'strategyFormItem', 'strategyLayerTitle'],
  components: { vueJsonEditor },
  data() {
    return {
      strategyFormRules: {
        autotestType: [
          {
            required: true,
            message: '请选择自动化类型',
            trigger: 'change'
          }
        ],
        versionPriority: [
          {
            required: true,
            message: '请选择版本优先级',
            trigger: 'change'
          }
        ],
        businessPriority: [
          {
            required: true,
            message: '请选择业务线优先级',
            trigger: 'change',
            type: 'number'
          }
        ],
        taskType: [
          {
            required: true,
            message: '请选择task类型',
            trigger: 'change',
            type: 'array'
          }
        ]
      },
      directionalTriggeredList: [
        {
          value: '0',
          label: '全量页面'
        },
        {
          value: '1',
          label: '相关页面'
        }
      ],
      taskTypeList: [
        {
          value: 'requirement',
          label: '需求'
        },
        {
          value: 'defect',
          label: '缺陷'
        }
      ],
      casePriorityList: [
        {
          value: 'p1',
          label: 'P1'
        },
        {
          value: 'p2',
          label: 'P2'
        },
        {
          value: 'p3',
          label: 'P3'
        },
        {
          value: 'p4',
          label: 'P4'
        }
      ],
      params: {
        casePriority: [],
        directionalTriggered: '',
        implement: [],
        advancedParams: {},
        remark: ''
      },
      advancedExample: {
        mockFlag: true,
        debugLink: true,
        visionComponents: 'CaptureWhiteScreen,CaptureWhiteScreen',
        appLogin: 'user/pwd',
        checkers: 'long_size.py,api_response.py',
        horn: {
          hornKey1: {
            content1: false
          }
        },
        abKey: {
          abKey1: 'a',
          abKey2: 'd'
        }
      }
    }
  },
  mounted() {
    if (this.$store.state.clientQuality.implList.length === 0) {
      this.$store.dispatch('getImplList')
    }
  },
  computed: {
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    implList() {
      return this.$store.state.clientQuality.implList
    }
  },
  methods: {
    reflesh() {
      this.reload()
    },
    checkStrategyInfo(isAdd) {
      this.$refs['strategyFormItem'].validate((valid) => {
        if (valid) {
          if (isAdd == 1) {
            this.addStrategyInfo()
          }
          if (isAdd == 0) {
            this.updateStrategyInfo()
          }
        } else {
          this.$Notice.warning({
            title: '请补充信息'
          })
        }
      })
    },
    addStrategyInfo() {
      this.$axios({
        method: 'post',
        data: {
          configId: this.strategyFormItem.configId,
          autotestType: this.strategyFormItem.autotestType,
          versionPriority: this.strategyFormItem.versionPriority,
          businessPriority: this.strategyFormItem.businessPriority,
          taskType: this.strategyFormItem.taskType.join(','),
          params: this.strategyFormItem.params,
          passRate: this.strategyFormItem.passRateSwitch ? this.strategyFormItem.passRate : ''
        },
        url: this.env.url + 'autoTestConfig/updateStrategy'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '策略添加成功！~'
            })
            this.closeStrategyInfo('0')
            this.reflesh()
          } else if (message.code === 204) {
            this.$Notice.warning({
              title: '请勿重复添加'
            })
          } else {
            this.$Notice.warning({
              title: '添加失败 :('
            })
          }
        })
        .catch(function (error) {})
    },
    updateStrategyInfo() {
      this.$axios({
        method: 'post',
        data: {
          configId: this.strategyFormItem.configId,
          strategyId: this.strategyFormItem.id,
          autotestType: this.strategyFormItem.autotestType,
          versionPriority: this.strategyFormItem.versionPriority,
          businessPriority: this.strategyFormItem.businessPriority,
          taskType: this.strategyFormItem.taskType.join(','),
          params: this.strategyFormItem.params,
          passRate: this.strategyFormItem.passRateSwitch ? this.strategyFormItem.passRate : ''
        },
        url: this.env.url + 'autoTestConfig/updateStrategy'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '策略更新成功！~'
            })
            this.closeStrategyInfo('0')
            this.reflesh()
          } else if (message.code === 204) {
            this.$Notice.warning({
              title: '已有相同策略，请重新编辑 :('
            })
          } else {
            this.$Notice.warning({
              title: '添加失败 :('
            })
          }
        })
        .catch(function (error) {})
    },
    closeStrategyInfo(val) {
      this.$emit('closeStrategyInfo', val)
    }
  }
}
</script>
<style>
pre {
  border: none;
  background-color: transparent;
  padding: 3px;
  font-weight: 300;
  font-size: 12px;
}
.ivu-form .ivu-form-item-label {
  font-size: 12px;
}
.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  font-size: 12px;
}
.ivu-form-item-content {
  font-size: 12px;
}
.ivu-poptip-popper {
  font-size: 12px;
}
.ivu-tooltip-popper {
  font-size: 12px;
}
</style>
