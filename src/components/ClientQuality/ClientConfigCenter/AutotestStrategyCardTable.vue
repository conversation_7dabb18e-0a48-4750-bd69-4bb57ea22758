<template>
  <table id="example" class="card-table">
    <tbody>
      <!--满足3条数据换行-->
      <tr v-for="(item, index) in strategyList" v-if="index % 3 == 0">
        <td>
          <div v-if="strategyList[index] != null">
            <AutotestStrategyCard
              :strategyInfo="strategyList[index]"
              :autotestType="autotestType"
            ></AutotestStrategyCard>
          </div>
        </td>
        <td>
          <div v-if="strategyList[index + 1] != null">
            <AutotestStrategyCard
              :strategyInfo="strategyList[index + 1]"
              :autotestType="autotestType"
            ></AutotestStrategyCard>
          </div>
        </td>
        <td>
          <div v-if="strategyList[index + 2] != null">
            <AutotestStrategyCard
              :strategyInfo="strategyList[index + 2]"
              :autotestType="autotestType"
            ></AutotestStrategyCard>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>
<script>
/* eslint-disable */
import AutotestStrategyCard from "./AutotestStrategyCard";
export default {
  name: "AutotestStrategyCardTable",
  props: ["strategyList", "autotestType"],
  components: { AutotestStrategyCard },
  data() {
    return {};
  },
  mounted() {},
  methods: {},
};
</script>
<style scoped>
.card-table {
  border-width: 0;
  margin-top: 15px;
}
.card-table td {
  width: 30%;
}
</style>
