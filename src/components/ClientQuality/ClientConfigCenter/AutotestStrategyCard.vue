<template>
  <div>
    <div>
      <Tooltip class="card-tooltip" :content="autotestType.label" placement="top">
        <Card style="width: 370px" class="strategy-card" dis-hover>
          <p slot="title" class="card-title">
            <Row>
              <Col span="2">
              <img height="16pd" :src="autotestType.icon" />
              </Col>
              <Col v-if="strategyInfo != null" span="7"><span>{{ strategyInfo.businessPriorityName }}</span>
              </Col>
              <Col v-if="strategyInfo != null" span="6">
              <span>
                {{ versionPriorityMap[strategyInfo.versionPriority] }}</span>
              </Col>
              <Col v-if="strategyInfo != null">
              <Tag class="card-tag">
                {{ taskTypeMap[strategyInfo.taskType] }}
              </Tag>
              </Col>
            </Row>
          </p>
          <Modal v-model="modal" width="360">
            <template #header>
              <p style="color:#f60;text-align:center">
                <Icon type="ios-information-circle"></Icon>
                <span>提醒</span>
              </p>
            </template>
            <div style="text-align:center">
              <p>你确定要删除吗？</p>
            </div>
            <template #footer>
              <Button :loading="modal_loading" @click="cancelModal">取消</Button>
              <Button type="error" :loading="modal_loading" @click="deleteStrategy(strategyInfo.id)">删除</Button>
            </template>
          </Modal>
          <p href="#" slot="extra" v-if="permission">
            <Button class="form-button" icon="ios-create" size="small" @click="openStrategyEditLayer('编辑执行策略', strategyInfo)" />
            <Button class="form-button" icon="ios-trash" size="small" @click="deleteStrategyWarning" />
          </p>
          <p>
            <Row>
              <Col v-if="strategyInfo != null" span="7">
              <div class="card-content">
                <div v-if="strategyInfo.params.directionalTriggered == '0'">
                  全量页面
                </div>
                <div v-if="strategyInfo.params.directionalTriggered == '1'">
                  相关页面
                </div>
              </div>
              </Col>
              <Col v-if="strategyInfo != null" span="16">
              <div class="card-content">
                Case优先级：
                <Tag v-for="priority in strategyInfo.params.casePriority" :key="priority" :color="getPriorityTagColor(priority)">{{ priority.toUpperCase() }}</Tag>
              </div>
              </Col>
              <Col v-if="strategyInfo != null" span="24">
              <div class="card-content" v-if="strategyInfo.params.implement">
                产物：
                <Tag class="card-content" v-for="impl in strategyInfo.params.implement" :key="impl" color="blue">{{ impl }}</Tag>
                &emsp;
              </div>
              <div v-show="strategyInfo.passRate > 0" class="card-content">
                阻塞通过基线：
                <Tag class="card-content" color="pink">{{ strategyInfo.passRate }}%</Tag>
              </div>
              <div v-show= "strategyInfo.passRate == 0" class="card-content">
                <Tag class="card-content" color="pink">未配置阻塞参数</Tag>
              </div>
              </Col>
            </Row>
            <Row>
              <Col v-if="strategyInfo != null">
              <Tooltip placement="right-end" theme="light" transfer:true max-width="400">
                <span class="card-content" style="color:#2d8cf0;font-weight:500">AdvancedParams</span>
                <Icon style="color:#2d8cf0;" type="ios-arrow-forward"></Icon>
                <div slot="content">
                  <JsonViewer :value="strategyInfo.params.advancedParams" :expand-depth=5 copyable></JsonViewer>
                </div>
              </Tooltip>
              </Col>
            </Row>
            <Row>
              <Col v-if="strategyInfo.params.remark">
              <span class="card-content" style="color:#9ea7b4">{{strategyInfo.params.remark}}</span>
              </Col>
            </Row>
          </p>
        </Card>
      </Tooltip>
    </div>
    <div>
      <!-- 策略添加/编辑弹层 -->
      <AutotestStragegyInfo :strategyLayerShow="strategyLayerShow" :strategyLayerTitle="strategyLayerTitle" :strategyFormItem="strategyFormItem" v-on:closeStrategyInfo="closeStrategyInfo"></AutotestStragegyInfo>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import AutotestStragegyInfo from './AutotestStrategyInfo'
import JsonViewer from 'vue-json-viewer'
export default {
  name: 'AutotestStrategyCard',
  inject: ['reload'],
  props: ['strategyInfo', 'autotestType'],
  components: { AutotestStragegyInfo, JsonViewer },
  data() {
    return {
      modal: false,
      taskTypeMap: {
        requirement: '需求',
        defect: '缺陷',
        'requirement,defect': '需求,缺陷'
      },
      versionPriorityMap: {
        1: '版本优先级1',
        2: '版本优先级2',
        3: '版本优先级3'
      },
      collapseVal: [],
      strategyLayerShow: false,
      strategyLayerTitle: '',
      strategyFormItem: {
        configId: 0,
        businessPriorityName: '',
        businessPriority: '',
        taskType: ['requirement', 'defect'],
        autotestType: 'shortLink',
        versionPriority: '1',
        params: {
          directionalTriggered: '0',
          casePriority: ['p1', 'p2', 'p3', 'p4'],
          implement: [],
          advancedParams: {}
        },
        passRate: '',
        passRateSwitch: 0
      }
    }
  },
  mounted() {},
  computed: {
    permission() {
      if (
        this.$store.state.clientQuality.configCenterUserPermission.isAdmin ||
        this.$store.state.clientQuality.configCenterUserPermission
          .permissionLevel < 20
      ) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    reflesh() {
      this.reload()
    },
    openStrategyEditLayer(layerTitle, strategyItem) {
      this.strategyLayerTitle = layerTitle
      this.strategyLayerShow = true
      this.strategyFormItem.configId = strategyItem.configId
      this.strategyFormItem.id = strategyItem.id
      this.strategyFormItem.businessPriorityName =
        strategyItem.businessPriorityName
      this.strategyFormItem.businessPriority = parseInt(
        strategyItem.businessPriority
      )
      this.strategyFormItem.taskType = strategyItem.taskType.split(',')
      this.strategyFormItem.autotestType = strategyItem.autotestType
      this.strategyFormItem.versionPriority = strategyItem.versionPriority
      this.strategyFormItem.params = strategyItem.params
      if(strategyItem.passRate > 0){
        this.strategyFormItem.passRate = strategyItem.passRate
        this.strategyFormItem.passRateSwitch = 1
      }
    },
    closeStrategyInfo(val) {
      if (val === '0') {
        this.$emit('getStrategyByBusinessId')
      }
      this.strategyLayerShow = false
    },
    cancelModal() {
      this.modal = false
    },
    deleteStrategyWarning() {
      this.modal = true
    },
    deleteStrategy(strategyId) {
      this.modal = false
      this.$axios({
        method: 'post',
        data: {
          stragetyId: strategyId
        },
        url: this.env.url + 'autoTestConfig/deleteStrategyById'
      })
        .then((res) => {
          let message = res.data
          if (message.code === 200) {
            this.$Notice.success({
              title: '删除成功！~'
            })
            this.reflesh()
          } else {
            this.$Notice.warning({
              title: '删除失败，请刷新页面后再试试 :('
            })
          }
        })
        .catch(function (error) {})
    },
    getPriorityTagColor(priority) {
      if (priority === undefined) {
        return 'default'
      }
      switch (priority.toUpperCase()) {
        case 'P1':
          return 'error'
        default:
          return 'primary'
      }
    }
  }
}
</script>
<style scoped>
.strategy-card {
  margin-bottom: 10px;
  height: 200px;
}
.card-title {
  font-size: 12px;
  height: 22px;
  vertical-align: bottom;
  margin-bottom: 0;
  margin-top: 2px;
}
.card-tag {
  line-height: 20px;
  margin: 0px 0px 0px 0px;
}
.card-content {
  font-size: 12px;
  display: inline-block;
  margin-bottom: 5px;
}
.card-tooltip {
  font-size: 12px;
}
.form-button {
  border: none;
  margin: 0 0 12px -8px;
}
</style>
