<template>
  <Row>
    <Col span="22">
      <Row class="title">
        <span class="title-text">业务方向信息</span>
        <div
          v-if="
            $store.state.clientQuality.configCenterUserPermission.isAdmin ||
            $store.state.clientQuality.configCenterUserPermission
              .permissionLevel < 100
          "
          style="display: contents"
        >
          <Tooltip content="Edit" placement="top">
            <Button
              class="button-edit"
              icon="md-create"
              type="text"
              @click="toEdit"
              :disabled="isEdit"
            ></Button>
          </Tooltip>
        </div>
      </Row>
      <BusinessInfoShow
        class="form-position"
        v-if="!isEdit"
        :businessInfo="businessInfo"
      ></BusinessInfoShow>
      <BusinessInfoEdit
        class="form-position"
        v-else
        :editInfo="editInfo"
      ></BusinessInfoEdit>
      <Row class="title">
        <span class="title-text">数据检查项</span>
        <div
          v-if="
            $store.state.clientQuality.configCenterUserPermission.isAdmin ||
            $store.state.clientQuality.configCenterUserPermission
              .permissionLevel == 10
          "
          style="display: contents"
        >
          <Tooltip content="Edit" placement="top">
            <Button
              class="button-edit"
              icon="md-create"
              type="text"
              @click="toDataCheckEdit"
              :disabled="isDataCheckEdit"
            ></Button>
          </Tooltip>
        </div>
      </Row>
      <DataCheckShow
        class="form-position"
        v-if="!isDataCheckEdit"
        :businessInfo="businessInfo"
      ></DataCheckShow>
      <DataCheckEdit
        class="form-position"
        v-else
        :editInfo="editInfo"
      ></DataCheckEdit>
      <Row class="title">
        <span>相关人员信息</span>
      </Row>
      <BusinessUserList
        class="form-position"
        :userList="userList"
      ></BusinessUserList>
    </Col>
    <Col span="1">
      <br />
    </Col>
  </Row>
</template>
<script>
/* eslint-disable */
import BusinessInfoShow from "./BasicConfig/BusinessInfoShow";
import BusinessInfoEdit from "./BasicConfig/BusinessInfoEdit";
import DataCheckEdit from "./BasicConfig/DataCheckEdit";
import DataCheckShow from "./BasicConfig/DataCheckShow";
import BusinessUserList from "./BasicConfig/BusinessUserList";
import { Bus } from "@/global/bus";
export default {
  name: "AutotestBasicConfig",
  props: ["businessId", "tab"],
  components: { BusinessInfoEdit, BusinessInfoShow, DataCheckEdit, DataCheckShow, BusinessUserList },
  data() {
    return {
      isEdit: false,
      isDataCheckEdit: false,
      editInfo: {},
    };
  },
  created() {
    Bus.$on("cancelEdit", () => {
      this.isEdit = false;
    });
    Bus.$on("cancelDataCheckEdit", () => {
      this.isDataCheckEdit = false;
    });
    Bus.$on("submitEdit", () => {
      this.$store.dispatch("getBusinessById", this.businessInfo.id);
      this.isEdit = false;
    });
    Bus.$on("submitDataCheckEdit", () => {
      this.$store.dispatch("getBusinessById", this.businessInfo.id);
      this.isDataCheckEdit = false;
    });
  },
  mounted() {
    this.$store.commit("setCurrentTab", this.tab);
    this.$store.dispatch("getUsersByBusinessId", this.$route.params.businessId);
  },
  computed: {
    businessInfo() {
      return this.$store.state.clientQuality.currentBusiness;
    },
    userList() {
      return this.$store.state.clientQuality.businessUsers;
    },
  },
  methods: {
    initEditInfo() {
      this.editInfo.id = this.businessInfo.id;
      this.editInfo.business = this.businessInfo.business;
      this.editInfo.uiTestRepo = this.businessInfo.uiTestRepo;
      this.editInfo.label = this.businessInfo.label;
      this.editInfo.checker = this.businessInfo.checker;
      this.editInfo.daxiangId = this.businessInfo.daxiangId;
    },
    toEdit() {
      this.initEditInfo();
      this.inputClass = null;
      this.isEdit = true;
    },
    toDataCheckEdit() {
      this.initEditInfo();
      this.isDataCheckEdit = true;
    }
  },
};
</script>
<style scoped>
.form-position {
  margin-left: 25px;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 12px;
}
.button-edit {
  font-size: 14px;
}
.name {
  font-size: 18px;
  color: #464c5b;
  font-weight: bold;
  margin-left: 5px;
  margin-right: 10px;
}
.title {
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
}
.title-text{
  padding-top: 5px;
}
.error-img {
  width: 200px;
}
</style>