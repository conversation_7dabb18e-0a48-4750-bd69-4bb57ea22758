<template>
  <div>
    <Row>
      <Col span="22">
      <Row class="title">
        <span>FLOW</span>
        <div v-if="permission" style="display: contents">
          <Tooltip content="Add" placement="top">
            <Button class="button-add" icon="md-add-circle" type="text" @click="openConfigEditLayer('flow', '添加触发节点配置', true)"></Button>
          </Tooltip>
        </div>
      </Row>
      <AutotestConfigList class="form-position" :businessInfo="businessInfo" :formItem="formItem" nodeType="flow" :configList="strategyObj.flow" v-on:closeConfigInfo="closeConfigInfo" v-on:openConfigEditLayer="openConfigEditLayer"></AutotestConfigList>
      <Row class="title">
        <span>TIME</span>
        <div v-if="permission" style="display: contents">
          <Tooltip content="Add" placement="top">
            <Button class="button-add" icon="md-add-circle" type="text" @click="openConfigEditLayer('time', '添加定时节点配置', true)"></Button>
          </Tooltip>
        </div>
      </Row>
      <AutotestConfigList class="form-position" :businessInfo="businessInfo" :formItem="formItem" nodeType="time" :configList="strategyObj.time" v-on:closeConfigInfo="closeConfigInfo" v-on:openConfigEditLayer="openConfigEditLayer"></AutotestConfigList>
      <Row class="title">
        <span>DIRECTED</span>
        <div v-if="permission" style="display: contents">
          <Tooltip content="Add" placement="top">
            <Button class="button-add" icon="md-add-circle" type="text" @click="openConfigEditLayer('directed', '添加定向测试配置', true)"></Button>
          </Tooltip>
        </div>
      </Row>
      <AutotestConfigList class="form-position" :businessInfo="businessInfo" :formItem="formItem" nodeType="directed" :configList="strategyObj.directed" v-on:closeConfigInfo="closeConfigInfo" v-on:openConfigEditLayer="openConfigEditLayer"></AutotestConfigList>
      </Col>
      <Col span="1">
      <br />
      </Col>
    </Row>
    <!-- Loading -->
    <Spin size="large" fix v-if="isLoading"></Spin>
    <!-- 配置信息弹层 -->
    <AutotestConfigInfo :configLayerShow="configLayerShow" :formItem="formItem" :configLayerTitle="configLayerTitle" :nodeList="nodeList" v-on:closeConfigInfo="closeConfigInfo"></AutotestConfigInfo>
    <router-view v-if="isRouterAlive"></router-view>
  </div>
</template>
<script>
/* eslint-disable */
import AutotestConfigList from './AutotestConfigList'
import AutotestConfigInfo from './AutotestConfigInfo'
export default {
  name: 'AutotestStrategyConfig',
  components: { AutotestConfigInfo, AutotestConfigList },
  props: ['tab'],
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouterAlive: true,
      formItem: {
        businessId: this.$route.query.businessId,
        configName: '',
        nodeId: 0,
        productIds: [],
        enable: true,
        configItem: {}
      },
      strategyObj: {},
      configLayerShow: false,
      configLayerTitle: '',
      isLoading: true,
      nodeList: []
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.getStrategyByBusinessId()
    this.$store.dispatch('getBusinessList')
    this.$store.dispatch('getTestItemList')
    this.$store.dispatch('getNodeList')
  },
  computed: {
    businessInfo() {
      return this.$store.state.clientQuality.currentBusiness
    },
    permission() {
      if (
        this.$store.state.clientQuality.configCenterUserPermission.isAdmin ||
        this.$store.state.clientQuality.configCenterUserPermission
          .permissionLevel < 20
      ) {
        return true
      } else {
        return false
      }
    },
    allNode() {
      return this.$store.state.clientQuality.nodeList
    }
  },
  methods: {
    reload() {
      this.getStrategyByBusinessId()
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
    getStrategyByBusinessId() {
      this.isLoading = true
      this.$axios({
        method: 'get',
        params: {
          businessId: this.$route.params.businessId
        },
        url: this.env.url + 'autoTestConfig/getStrategyByBusinessId'
      }).then((res) => {
        let message = res.data
        this.strategyObj = message
        this.isLoading = false
      })
    },
    getNodeList(nodeType) {
      let list = []
      for (let node of this.allNode) {
        if (node.nodeType === nodeType) {
          list.push(node)
        }
      }
      return list
    },
    openConfigEditLayer(nodeType, layerTitle, isAdd, configItem) {
      this.nodeList = this.getNodeList(nodeType)
      this.configLayerTitle = layerTitle
      this.configLayerShow = true
      if (isAdd) {
        this.formItem.configName = ''
        this.formItem.nodeId = 0
        this.formItem.productIds = []
        this.formItem.productLabel = []
        this.formItem.enable = 1
      } else {
        this.formItem.configName = configItem.configName
        this.formItem.nodeId = configItem.nodeId
        this.formItem.productIds = configItem.productIds
        this.formItem.enable = configItem.enable
        this.formItem.configItem = configItem
      }
    },
    closeConfigInfo(val) {
      if (val === '0') {
        this.getStrategyByBusinessId()
      }
      this.configLayerShow = false
    }
  }
}
</script>
<style scoped>
.form-position {
  margin-left: 25px;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 12px;
}
.button-add {
  font-size: 14px;
}
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
}
.error-img {
  width: 200px;
}
span {
  padding: 5px 0;
}
</style>