<template>
    <div>
        <Table width="860" border :data="showData" :columns="runEffectColumns"></Table>
    </div>
</template>

<script>

/* eslint-disable */
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestRunEffectTableSum',
  components: {DataTable},
  props: ['coverageData'],
   data () {
    return {
      tableData:[],
      runEffectColumns:[
        {
            title: '方向',
            align: 'center',
            key: 'channelName',
            width: 100
        }, 
        {
            title: '接口覆盖',
            align: 'center',
            render:(h, params)=>{
                var text = params.row.allAutoTestApiCoverageRate
                var textInfo;
                if(params.row.allAutoTestApiCoverageRate == "--"){
                textInfo = ""
                } else{
                textInfo = " <br/> ( " + params.row.allAutoTestApiCoverageNum + " / " + params.row.apiBaseNum + " )"
                }
                return h('div', [
                h('span', {
                    domProps: {
                    innerHTML: text
                    }  
                }),
                h('span', {
                    domProps: {
                    innerHTML: textInfo
                    } ,
                    style: {
                    color: 'gray',
                    fontSize: '10px'
                    }  
                })
                ])
            },
            },
            {
            title: '页面覆盖',
            align: 'center',
            render:(h, params)=>{
                var text = params.row.allAutoTestPageCoverageRate
                var textInfo;
                if(params.row.allAutoTestPageCoverageRate == "--"){
                textInfo = ""
                } else{
                textInfo = " <br/> ( " + params.row.allAutoTestPageCoverageNum + " / " + params.row.pageBaseNum + " )"
                }
                return h('div', [
                h('span', {
                    domProps: {
                    innerHTML: text
                    }  
                }),
                h('span', {
                    domProps: {
                    innerHTML: textInfo
                    } ,
                    style: {
                    color: 'gray',
                    fontSize: '10px'
                    }  
                })
                ])
            },  
            },
            {
            title: '代码覆盖',
            align: 'center',
            key: 'shortLinkCodeCoverageRate' 
            }
        ]
    }
  },
  mounted(){
    console.log(this.coverageData)
    this.tableData = this.coverageData
  },
  computed:{
    showData: function () {
        let showData = this.coverageData
        console.log(showData)
        return showData
    }
  }
}
</script>

<style scoped>
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
</style>