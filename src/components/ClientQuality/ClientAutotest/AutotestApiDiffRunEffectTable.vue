<template>
  <div :style='{ background: "#ffff"}'>
    <Table border v-show="isDetailShow" :columns='newColumns' :data='showData' width='100px' size='small' :span-method='cellMerge' @on-filter-change='columnFilterChange'>
    </Table>
    <div style=' overflow: hidden' v-if='tableData.length >= 20'>
      <div style='float: right; height: 90px'>
        <Page :total='tableData.length' :current='showIndex' :page-size='showSize' :page-size-opts='pageSizeOpts' show-sizer show-total @on-page-size-change='changePageSize' @on-change='changePage' />
      </div>
    </div>
    <div style="height: 20px;"></div>
    <div class='spin-container' v-if='isRequesting' style='padding-top: 50px'>
      <Spin>
        <Icon type='ios-loading' size='18' class='spin-icon-load'></Icon>
      </Spin>
    </div>
    <AutotestApiDiffRunEffectDetailTable v-show="isShowDetailMeasureTable" ref="AutotestApiDiffRunEffectDetailTable"></AutotestApiDiffRunEffectDetailTable>
  </div>
</template>

<script>
import ConfigSelect from '../baseComponents/ConfigSelect'
import AutotestApiDiffRunEffectDetailTable from './AutotestApiDiffRunEffectDetailTable'
export default {
  name: 'ApiDiffMeasureInfo',
  components: {AutotestApiDiffRunEffectDetailTable, ConfigSelect},
  data() {
    return {
      isDetailShow: true, // 是否展示执行情况大表格
      showDataExplain: false,
      nowDate: [], // 用户选择时间
      tableData: [], // 存放table待渲染数据
      triggerCount: {},
      productFilter: [], // 存放用户选择的product
      nodeFilter: [], // 存放用户选择的node
      misIDFilter: [], // 存放用户选择的misID
      pageSelection: [], // 存放用户选择的页面名称
      misIDFilterRow: [], // 存放当前行用户选择的misID
      pageNameRow: [], // 存放当前行用户选择的页面名称
      startTime: '',
      endTime: '',
      isRequesting: false,
      groupId: '', // 用户选择的groupId
      unitId: '', // 用户选择的unitId
      businessId: '', // 用户选择的businessId
      testSlot: [], // 触发项
      mergerItems: [], // 待合并的行
      mergerRosRecord: {}, // 待合并列内容
      propObj: {}, // 存储更新后表头数据
      showIndex: 1, // 分页参数
      showSize: 50, // 分页参数
      pageSizeOpts: [10, 15, 20, 30, 50, 100],
      isShowDetailMeasureTable: false,
      isMisIDShow: false, // 默认不展示
      isPageNameShow: false // 默认不展示
    }
  },
  computed: {
    newColumns() {
      let beforeColumns = [
        {
          title: '事业群',
          key: 'bgLabel',
          align: 'center'
        },
        {
          title: '事业部',
          key: 'buLabel',
          align: 'center'
        },
        {
          title: '业务方向',
          key: 'businessLabel',
          align: 'center'
        }
      ]
      if (this.isMisIDShow) {
        beforeColumns.push({
          title: 'misID',
          key: 'misID',
          align: 'center'
        })
      }
      // 如果isPageNameShow为true，添加页面列
      if (this.isPageNameShow) {
        beforeColumns.push({
          title: '页面',
          key: 'pageName',
          align: 'center'
        })
      }
      beforeColumns.push(
        {
          title: '研发测试节点',
          key: 'nodeLabel',
          align: 'center'
        },
        {
          title: '产品类型',
          key: 'productLabel',
          align: 'center'
        },
        {title: '运行稳定性',
          key: 'operationalStability',
          align: 'center',
          children: [
            {
              title: 'Job成功率',
              key: 'jobSuccessRate',
              align: 'center',
              render: (h, params) => {
                // params.row.jobSuccessRate：取当前行中的jobSuccessRate字段值并只取前面数字部分
                let jobSuccessRateNum = parseFloat(params.row.jobSuccessRate)
                return h('span', {
                  style: {
                    color: jobSuccessRateNum < 80 ? 'red' : ''
                  }
                }, params.row.jobSuccessRate)
              }
            },
            {
              title: 'DIFF通过率',
              key: 'picPassRate',
              align: 'center',
              // 设置表头的icon弹窗样式
              renderHeader: (h, { column }) => {
                return h('div', [
                  column.title,
                  h('Tooltip', {
                    props: {
                      content:
                        '⚡️️ DIFF通过率说明\n' +
                        '1. 通过率以视觉插件ApiDiffRecord的执行结果为准\n' +
                        '2. 未配置ApiDiffRecord插件的DIFF任务通过率都为0\n' +
                        '3. 通过率只统计配置了对比脚本的节点，过滤刷新任务节点\n' +
                        '4. 脚本和插件配置及使用说参考文档：https://km.sankuai.com/collabpage/1312716946\n',
                      placement: 'top',
                      transfer: true, // 解决表头fixed导致弹窗被截断展示的问题
                      maxWidth: 600
                    }
                  }, [
                    h('Icon', {
                      props: {
                        type: 'ios-help-circle-outline',
                        size: 16
                      },
                      style: {
                        marginLeft: '10px',
                        cursor: 'pointer'
                      }
                    })
                  ])
                ])
              },
              // 设置当前列内容成功率低于80%时的红色样式和超链接
              render: (h, params) => {
                let picPassRateNum = parseFloat(params.row.picPassRate)
                return h('Tooltip', {
                  props: {
                    content: '点击可查看该触发项详情',
                    placement: 'top-start'
                  }
                }, [
                  h('a', {
                    on: {
                      click: () => {
                        this.getDetailMeasureInfo(params.row)
                        console.log('参数值是:', JSON.stringify(params.row, null, 2))
                      }
                    },
                    style: {
                      color: picPassRateNum < 80 ? 'red' : ''
                    }
                  }, params.row.picPassRate)
                ])
              }
            }
          ]
        },
        {
          title: '运行效率(TP90)',
          key: 'operationalEfficiency',
          children: [
            {
              title: 'Job运⾏耗时',
              key: 'jobDuration',
              align: 'center'
            }
          ],
          align: 'center'
        }
      )
      return beforeColumns
    },
    showData: function () {
      let showData = this.tableData
      if (this.showSize < this.tableData.length) {
        showData = this.tableData.slice(
          (this.showIndex - 1) * this.showSize,
          (this.showIndex - 1) * this.showSize + this.showSize
        )
      }
      return showData
    }
  },
  methods: {
    getTableData(
      nowDate,
      unitId,
      groupId,
      businessId,
      productFilter,
      nodeFilter,
      misIDFilter,
      pageSelection
    ) {
      this.pageSelection = pageSelection
      console.log('筛选的mis' + JSON.stringify(this.pageSelection, null, 2))
      this.isDetailShow = false // 设置暂不展示执行情况表格
      this.isShowDetailMeasureTable = false
      this.isRequesting = true // 设置展示loading样式
      this.tableData = []

      this.startTime = this.formateTime(new Date(Date.parse(nowDate[0])))
      this.endTime = this.formateTime(
        new Date(Date.parse(nowDate[1]) + 24 * 60 * 60 * 1000)
      )
      this.$axios({
        data: {
          misIds: misIDFilter,
          pages: pageSelection,
          nodeIds: nodeFilter,
          productIds: productFilter,
          startTime: this.startTime,
          endTime: this.endTime,
          businessId: businessId || undefined,
          buId: unitId || undefined,
          bgId: groupId || undefined
        },
        method: 'POST',
        url: this.env.url + 'autoTestAnalytic/apiDiff/apiDiffMeasureInfo'
      })
        .then((res) => {
          let node = ''
          let measureJobSuccess = ''
          let measurePicPassRate = ''
          let measureJobDuration = ''
          this.isRequesting = false // 设置不展示loading
          let triggerResult = res.data || {}
          this.tableData = [] // 清空运行效果数据
          this.tableData = triggerResult.apiDiffMeasureInfo || [] // 存储执行情况数
          this.isMisIDShow = this.tableData.some(item => item.misID)
          this.isPageNameShow = this.tableData.some(item => item.pageName)

          for (let i = 0; i < this.tableData.length; i++) {
            node =
              this.tableData[i].publish_name +
              '-' +
              this.tableData[i].integration_name
            this.tableData[i].nodeLabel = node
            measureJobSuccess =
              this.tableData[i].measureInfo['jobSuccessRate']
            this.tableData[i].jobSuccessRate = measureJobSuccess
            measurePicPassRate = this.tableData[i].measureInfo['picPassRate']
            this.tableData[i].picPassRate = measurePicPassRate
            measureJobDuration = this.tableData[i].measureInfo['jobDuration']
            this.tableData[i].jobDuration = measureJobDuration
          }
          this.mergerItems = [0, 1, 2, 3] // 默认合并前4列
          if (this.isMisIDShow && this.isPageNameShow) {
            this.mergerItems.push(4, 5) // 如果两者都显示，则合并前6列
          } else if (this.isMisIDShow || this.isPageNameShow) {
            this.mergerItems.push(4) // 如果只显示了其中一个，则合并前5列
          }
          this.getColkey(this.newColumns)
          this.getSpanArr(this.showData, this.mergerItems)
          this.$nextTick(() => {
            this.isDetailShow = true
          })
          this.showIndex = 1
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取某个产品+节点下的运行详情接口
    getDetailMeasureInfo(row) {
      this.misIDFilterRow = row.misID ? [row.misID] : []
      this.pageNameRow = this.findPageByPageName(row.pageName)
      this.$refs.AutotestApiDiffRunEffectDetailTable.getDetailMeasureInfo(row, this.startTime, this.endTime, this.misIDFilterRow, this.pageNameRow)
      this.isShowDetailMeasureTable = true
    },
    // 格式化时间
    formateTime: function (time) {
      return (
        time.getFullYear() +
        '-' +
        (time.getMonth() >= 9
          ? time.getMonth() + 1
          : '0' + (time.getMonth() + 1)) +
        '-' +
        (time.getDate() > 9 ? time.getDate() : '0' + time.getDate())
      )
    },
    // 合并单元格函数
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (this.mergerItems.includes(columnIndex)) {
        let item = this.mergerRosRecord[columnIndex]
        const _row = item[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    // 合并单元格数据处理
    getSpanArr(data, array) {
      //  循环数据处理
      let mergerRosRecord = {}
      let pos = 0
      for (let n of array) {
        pos = 0
        mergerRosRecord[n] = [] // 数据清空(重新调此方法的时候需要清空上一次的数据)
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            mergerRosRecord[n].push(1)
            pos = 0
          } else {
            //  判断当前元素与上一个元素是否相同
            if (
              data[i][this.propObj[n]] === data[i - 1][this.propObj[n]] &&
              data[i][this.propObj[n - 1]] === data[i - 1][this.propObj[n - 1]]
            ) {
              mergerRosRecord[n][pos] += 1
              mergerRosRecord[n].push(0)
            } else {
              mergerRosRecord[n].push(1)
              pos = i
            }
          }
        }
      }
      this.mergerRosRecord = { ...mergerRosRecord }
    },
    // 获取列的key
    getColkey(colData) {
      let index = 0
      let tmpObj = {}
      for (let i of colData) {
        if (!i.children) {
          tmpObj[index] = i.key
          index++
        } else {
          for (let k of i.children) {
            tmpObj[index] = k.key
            index++
          }
        }
      }
      this.propObj = { ...tmpObj }
    },
    changePage(index) {
      this.showIndex = index
      this.$nextTick(() => {
        this.getSpanArr(this.showData, this.mergerItems)
      })
    },
    changePageSize(size) {
      this.showSize = size
      this.$nextTick(() => {
        this.getSpanArr(this.showData, this.mergerItems)
      })
    },
    columnFilterChange() {
      this.changePageSize(100)
      this.changePage(1)
    },
    findPageByPageName(pageName) {
      for (let page of this.pageSelection) {
        if (page.pageName === pageName) {
          return [page]
        }
      }
      // 如果没有找到，返回空数组
      return []
    },
    renderTitleWithIcon(h) {
      return h('div', [
        'DIFF通过率',
        h('Icon', {
          props: {
            type: 'ios-help-circle-outline',
            size: 16
          },
          style: {
            marginLeft: '5px',
            cursor: 'pointer'
          },
          on: {
            mouseenter: (event) => {
              this.showTooltip(event, '注意，这是通过率')
            },
            mouseleave: () => {
              this.hideTooltip()
            }
          }
        })
      ])
    },
    showTooltip(event, content) {
      // 显示 Tooltip 的方法
      this.$Tooltip.show({
        content: content,
        placement: 'top',
        target: event.target
      })
    },
    hideTooltip() {
      // 隐藏 Tooltip 的方法
      this.$Tooltip.hide()
    }

  }
}
</script>

<style scoped>
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.select-location {
  text-align: center;
  margin-top: -5px;
}
html {
  height: 100%;
  display: table;
}
body {
  display: table-cell;
  height: 100%;
  margin: 100px;
}
.ivu-table-header table {
  width: 100% !important;
}
</style>
