<template>
  <div :style='{ background: "#ffff"} '>
      <Col>
          <ConfigSelect @selectInfo="getTableData"></ConfigSelect>
      </Col>    
    <Table
      border
      :columns='newColumns'
      :data='showData'
      width='100px'
      size='small'
      :span-method='cellMerge'
      @on-filter-change='columnFilterChange'
    >
      <template
        v-for='(item, index) in testSlot'
        slot-scope='{ row }'
        :slot='item'
      >
        <span :key='index'>
          <Icon
            v-if='row.autoTestType.includes(item)'
            type='ios-checkmark-circle'
          />
        </span>
      </template>
      <template
        v-for='(item, index) in testSlot'
        slot-scope='{ row }'
        :slot='item + "Count"'
      >
        <span :key='index'>
          <span class='scene-name'>{{ row.triggerCount[item] || 0 }} </span>
        </span>
      </template>
    </Table>
    <div style=' overflow: hidden;' v-if='tableData.length >= 20'>
      <div style='float: right; height: "90px"'>
        <Page
          :total='tableData.length'
          :current='showIndex'
          :page-size='showSize'
          :page-size-opts='pageSizeOpts'
          show-sizer
          show-total
          @on-page-size-change='changePageSize'
          @on-change='changePage'
        />
      </div>
    </div>
    <div class='spin-container' v-if='isRequesting' style='padding-top: 50px'>
      <Spin>
        <Icon type='ios-loading' size='18' class='spin-icon-load'></Icon>
      </Spin>
    </div>
  </div>
</template>
<script>
import ConfigSelect from '../baseComponents/ConfigSelect.vue'
import DataTable from '../baseComponents/DataTable.vue'

export default {
  name: 'AntotestTrigger',
  components: {DataTable, ConfigSelect},
  data() {
    return {
      // nowDate: [], // 用户选择时间
      tableData: [], // 存放table待渲染数据
      triggerCount: {},
      isRequesting: false,
      testSlot: [], // 触发项
      mergerItems: [0, 1, 2, 3], // 待合并的行
      mergerRosRecord: {}, // 待合并列内容
      propObj: {}, // 存储更新后表头数据
      showIndex: 1, // 分页参数
      showSize: 20, // 分页参数
      pageSizeOpts: [10, 15, 20, 30, 50, 100]
    }
  },
  mounted() {},
  computed: {
    newColumns() {
      let beforeColumns = [
        {
          title: 'BG',
          key: 'bgLabel',
          align: 'center'
        },
        {
          title: 'BU',
          key: 'buLabel',
          align: 'center'
        },
        {
          title: '业务方向',
          key: 'businessLabel',
          align: 'center'
        },
        {
          title: '研发测试节点',
          key: 'nodeLabel',
          align: 'center'
        },
        {
          title: '产品类型',
          key: 'productLabel',
          align: 'center'
        },
        {
          title: '检查项',
          key: 'autoTestType',
          align: 'center',
          children: []
        },
        {
          title: '触发个数',
          key: 'triggerCount',
          children: [],
          align: 'center'
        }
      ]
      beforeColumns = this.setTabelHeader(this.testItemList, beforeColumns)
      return beforeColumns
    },
    showData: function () {
      let showData = this.tableData
      if (this.showSize < this.tableData.length) {
        showData = this.tableData.slice(
          (this.showIndex - 1) * this.showSize,
          (this.showIndex - 1) * this.showSize + this.showSize
        )
      }
      return showData
    },
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    }
  },
  methods: {
    getTableData(nowDate, unitInfo, groupInfo, businessInfo, productFilter, nodeFilter) {
      if (nowDate.length === 0 || (businessInfo === 0 && groupInfo === 0 && unitInfo === 0)) {
        this.$Message.info('请选择日期以及方向')
      } else {
        (this.isRequesting = true); // 设置展示loading样式
        (this.tableData = [])
        this.getColkey(this.newColumns)
        this.startTime = Date.parse(nowDate[0])
        this.endTime = Date.parse(nowDate[1])
        this.$axios({
          data: {
            node: nodeFilter,
            product: productFilter,
            startTime: this.startTime,
            endTime: this.endTime,
            business: businessInfo || undefined,
            bu: unitInfo || undefined,
            bg: groupInfo || undefined
          },
          method: 'POST',
          url: this.env.url + 'ccd/getConfigStatus'
        })
          .then((res) => {
            this.triggerCount = []
            this.isRequesting = false // 设置不展示loading
            let triggerResult = res.data || {}
            this.tableData = [] // 清空运行效果数据
            this.tableData = triggerResult.data || [] // 存储执行情况数
            this.triggerCount = this.tableData.triggerCount
            this.getSpanArr(this.showData, this.mergerItems)
            this.showIndex = 1
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },
    setTabelHeader(testItemList, columns) {
      let newColumns = columns
      this.testSlot = []
      newColumns[5].children = testItemList.map((item) => {
        this.testSlot.push(item.name)
        return {
          title: item.label,
          slot: item.name,
          align: 'center',
          width: 80
        }
      })
      newColumns[6].children = testItemList.map((item) => {
        return {
          title: item.label,
          slot: item.name + 'Count',
          align: 'center',
          width: 80
        }
      })
      return newColumns
    },
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (this.mergerItems.includes(columnIndex)) {
        let item = this.mergerRosRecord[columnIndex]
        const _row = item[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data, array) {
      //  循环数据处理
      let mergerRosRecord = {}
      let pos = 0
      for (let n of array) {
        pos = 0
        mergerRosRecord[n] = [] // 数据清空(重新调此方法的时候需要清空上一次的数据)
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            mergerRosRecord[n].push(1)
            pos = 0
          } else {
            //  判断当前元素与上一个元素是否相同
            if (data[i][this.propObj[n]] === data[i - 1][this.propObj[n]] && data[i][this.propObj[n - 1]] === data[i - 1][this.propObj[n - 1]]) {
              mergerRosRecord[n][pos] += 1
              mergerRosRecord[n].push(0)
            } else {
              mergerRosRecord[n].push(1)
              pos = i
            }
          }
        }
      }
      this.mergerRosRecord = { ...mergerRosRecord }
    },
    // 获取列的key
    getColkey(colData) {
      let index = 0
      let tmpObj = {}
      for (let i of colData) {
        if (!i.children) {
          tmpObj[index] = i.key
          index++
        } else {
          for (let k of i.children) {
            tmpObj[index] = k.key
            index++
          }
        }
      }
      this.propObj = { ...tmpObj }
    },
    changePage(index) {
      this.showIndex = index
      this.getSpanArr(this.showData, this.mergerItems)
    },
    changePageSize(size) {
      this.showSize = size
      this.getSpanArr(this.showData, this.mergerItems)
    },
    columnFilterChange() {
      this.changePageSize(100)
      this.changePage(1)
    }
  }
}
</script>
<style scoped>
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite
}
.select-location {
  text-align: center;
  margin-top: -5px
}
.layout-nav {
  padding-left: 80px；
}
html {
  height: 100%;
  display: table;
}
body {
  display: table-cell;
  height: 100%;
  margin: 100px
}
</style>

