<template>
  <div>
    <div class="spin-container" v-if="isRequesting" style="padding-top: 700px">
      <Spin>
        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
      </Spin>
    </div>
    <p
      v-show="isProductFilterShow"
      style="margin-bottom:10px;margin-top:30px;margin-left:10px;font-size:16px"
      class="title"
    >各自动化执行时长统计</p>
    <div
      v-show="isProductFilterShow"
      :style="{padding: '10px',minHeight: '60px', background: '#fff',float:left}"
      class="title"
    >
      <Select
        multiple
        placeholder="请选择自动化运行平台"
        :show-all-levels="false"
        style="width: 260px"
        v-model="productFilter"
        @on-change="jobProductFilter"
      >
        <Option v-for="item in productList" :value="item.id" :key="item.id">{{item.label}}</Option>
      </Select>
    </div>
    <div>
      <v-table
        v-show="isDetailShow"
        is-horizontal-resize
        style="width:100%"
        :columns="runEffectColumns"
        :table-data="tableData"
        even-bg-color="#f8f8f9"
        :cell-merge="cellMerge"
        :column-cell-class-name="columnCellClass"
        @on-custom-comp="customCompFunc"
      ></v-table>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import Highcharts from "highcharts";
import HighchartsNoData from "highcharts/modules/no-data-to-display";
HighchartsNoData(Highcharts);
import Vue from "vue";
import "vue-easytable/libs/themes-base/index.css";
import { VTable, VPagination } from "vue-easytable";
import { now } from "moment";

export default {
  name: "AutotestCcdRunTimeTable",
  components: {
    VTable,
    VPagination
  },
  data() {
    return {
      product: "", //自动化运行平台
      productList: [],
      productFilter: [],
      tableData: [],
      isRequesting: false, //是否展示loading
      isProductFilterShow: false, //是否展示运行平台筛选项
      runEffectColumns: [
        {
          field: "businessName",
          title: "业务方向",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          isFrozen: true,
          isResize: true,
          titleCellClassName: "title-cell-class-name"
        },
        {
          field: "nodeName",
          title: "触发节点",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        },
        {
          field: "autotestType",
          title: "自动化类型",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        },
        {
          field: "TP90",
          title: "Job总时长TP90",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        },
        {
          field: "TP50",
          title: "Job总时长TP50",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        },
        {
          field: "runTimeTP90",
          title: "Job运行时长TP90",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        },
        {
          field: "waitTimeTP90",
          title: "Job等待时长TP90",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          isResize: true
        }
      ],
      timeRange: [],
      allParam: []
    };
  },
  methods: {
    //筛选自动化运行平台值
    jobProductFilter: async function(value) {
      await this.$nextTick();
      this.productFilter = value;
      // console.log(this.productsFilter);
      this.$set(this.allParam, "productId", this.productFilter);
      this.getCcdRunTimeTable(this.allParam);
    },
    // 列样式设置
    columnCellClass(rowIndex, columnName, rowData) {
      // 给业务方向列设置className
      if (columnName === "businessName") {
        return "business-column-cell-class-name";
      }
      // 给业务方向列设置className
      else if (columnName === "nodeName") {
        return "node-column-cell-class-name";
      } else {
        return "other-column-cell-class-name";
      }
    },
    // 合并单元格
    cellMerge(rowIndex, rowData, field) {
      if (field === "nodeName") {
        if (
          rowIndex > 0 &&
          this.tableData[rowIndex].businessName ===
            this.tableData[rowIndex - 1].businessName &&
          this.tableData[rowIndex].nodeName ===
            this.tableData[rowIndex - 1].nodeName
        ) {
          return {
            rowSpan: 0,
            colSpan: 0,
            content: rowData.nodeName
          };
        } else {
          // 返回相同内容的行数
          return {
            rowSpan: 3,
            colSpan: 1,
            content: rowData.nodeName
          };
        }
      }
      if (field === "businessName") {
        if (
          rowIndex > 0 &&
          this.tableData[rowIndex].businessName ===
            this.tableData[rowIndex - 1].businessName
        ) {
          return {
            rowSpan: 0,
            colSpan: 0,
            content: rowData.businessName
          };
        } else {
          let rows = 1;
          // 反之 查询相同的内容有多少行 进行合并
          for (let i = rowIndex + 1; i < this.tableData.length; i++) {
            if (
              this.tableData[i].businessName ===
              this.tableData[rowIndex].businessName
            ) {
              rows++;
            }
          }
          // 返回相同内容的行数
          return {
            rowSpan: rows,
            colSpan: 1,
            content: rowData.businessName
          };
        }
      }
    },
    //展示运行效果表格
    showCcdRunTimeInfo(params, defaultProductList) {
      this.allParam = params;
      this.productList = defaultProductList;
      this.productFilter = []; //清空筛选项
      this.tableData = []; //清空运行效果数据
      console.log(this.productList);
      this.isProductFilterShow = true;
      // console.log(this.productList);
      this.isDetailShow = false;
      this.isRequesting = true;
      this.getCcdRunTimeTable(params);
    },
    //获取自动化运行效果数据
    getCcdRunTimeTable(params) {
      this.$axios({
        data: params,
        method: "POST",
        url: this.env.url + "ccd/autotest/runningTime"
      }).then(res => {
        let runEffect = res.data;
        console.log(res.data);
        this.tableData = []; //清空运行效果数据
        if (runEffect.code === 200) {
          this.tableData = runEffect.data; //存储执行情况数据
          console.log(this.tableData);
          this.isRequesting = false; //设置不展示loading
          this.isDetailShow = true; //设置展示运行效果表格
        } else if (runEffect.code === 400) {
          this.tableData = [];
          this.isRequesting = false; //设置不展示loading
          this.isDetailShow = true; //设置不展示运行效果表格
        }
      });
    }
  }
};
</script>
<style >
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.title {
  color: #808695;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}
.chart {
  width: 450px;
  height: 350px;
  margin: 0 auto;
}
.title-cell-class-name {
  background-color: #eceaea;
  color: #515a6e;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.business-column-cell-class-name {
  background-color: #ffffff;
  color: #515a6e;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.other-column-cell-class-name {
  color: #515a6e;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.node-column-cell-class-name {
  background-color: #ffffff;
  color: #515a6e;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.title-cell-class-name {
  background-color: #f8f8f9;
  color: #515a6e;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.column-cell-class-name {
  background-color: #ffffff;
  color: #515a6e;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
</style>