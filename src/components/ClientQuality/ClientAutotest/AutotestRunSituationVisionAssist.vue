<template>
  <div>
    <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
      </div>
      <Timeline v-show = isAssistShow style="padding-top: 15px;padding-left: 2px">
        <TimelineItem>
          <Icon type="ios-pricetags-outline" slot="dot"></Icon>
          <span class= time-line-title>运行稳定性</span>
          <Row  v-show = isAssistShow style="min-width:800px,padding-top: 35px">            
            <div id="resultInfo" class="chart" style="margin:50px"></div>
          </Row> 
        </TimelineItem>
        <TimelineItem>
            <Icon type="ios-pricetags-outline" slot="dot"></Icon>
            <span class= time-line-title>运行效率</span>
            <Table width="600" style="margin:50px" v-show = isAssistShow border :data="this.assistInfoTableData" :columns="this.assistInfoTableColumns"></Table>
          </TimelineItem>
        </Timeline>
    </div>
</template>

<script>
  /* eslint-disable */
import Highcharts from 'highcharts';
import HighchartsNoData from 'highcharts/modules/no-data-to-display'
HighchartsNoData(Highcharts)
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestRunSituationVisionAssist',
  components: {DataTable},
  props: [],
   data () {
    return {
      isRequesting: false,
      isAssistShow: false,
      visionSituationAssist:{},
      assistInfoTableData:[],
      assistInfoTableColumns:[
        {
            title: 'Job运行耗时（TP50）',
            align: 'center',
            key: 'count',
            children:[
                {
                  title: '成功Job运行耗时',
                  align: 'center',
                  children:[
                      {
                        title: 'iOS',
                        align: 'center',
                        key: 'visionIosSuccessJobDurationTp50'
                      },
                      {
                        title: 'Android',
                        align: 'center',
                        key: 'visionAndSuccessJobDurationTp50'
                      }
                  ]
                },
                {
                  title: '失败Job运行耗时',
                  align: 'center',
                  children:[
                      {
                        title: 'iOS',
                        align: 'center',
                        key: 'visionIosFailedJobDurationTp50'
                      },
                      {
                        title: 'Android',
                        align: 'center',
                        key: 'visionAndFailedJobDurationTp50'
                      }
                  ]
                }
            ]
        },
      ],
    }
  },
  methods: {
      //是否展示辅助指标&调用接口；若不展示，则无需请求接口
      isShow(isShow,businessId,timeRange){
        if(!isShow){
            return
        }
        this.getVisionAssistInfo(businessId,timeRange)        //获取辅助指标数据
      },
      getVisionAssistInfo(businessId,timeRange){
       this.isRequesting = true        //设置展示loading
        this.isAssistShow = false       //设置不展示辅助指标模块
        this.$axios({
              params:{
                  startTime: timeRange[0],
                  endTime: timeRange[1],
                  businessId: businessId
              },
              method:"get",
              url:this.env.url+"autoTestAnalytic/vision/runInfoAssist",
          }).then((res) => {
              this.isRequesting = false       //设置不展示loading
              this.isAssistShow = true        //设置展示辅助指标模块
              let message = res.data;
              this.visionSituationAssist = message;        //存储辅助指标数据
              this.assistInfoTableData=[]       //清空效率数据
              this.assistInfoTableData.push(message)       //存储效率数据
              this.resultInfo = message.visionSuccessRate       //存储折线图数据
              this.showDetailInfo(message.visionSuccessRate,message.channelName)
          }).catch(function (error) {
        })
      },
      
      showDetailInfo(resultInfo,channelName){
        this.lineChart('resultInfo',channelName+'-页面一致率',resultInfo)
      },
      lineChart(containerId, title, resultData,){
        let iosRegressSuccessRateList = []
        let androidRegressSuccessRateList = []
        let iosNewSuccessRateList = []
        let androidNewSuccessRateList = []
        iosRegressSuccessRateList = resultData.iosRegressionDailySuccessRate
        androidRegressSuccessRateList = resultData.androidRegressionDailySuccessRate
        iosNewSuccessRateList = resultData.iosDailySuccessRate
        androidNewSuccessRateList = resultData.androidDailySuccessRate
        Highcharts.setOptions({ global: { useUTC: false } })
        Highcharts.chart(containerId, {
        chart: {
            type: 'spline'
        },
        title: {
            text: title,
            margin: 20,
            style: {
            color: '#808695',
            fontWeight: 'bold',
            fontSize: '16px'
            }
        },
        xAxis: {
            type: 'datetime',
            tickPixelInterval: 10,
            title: {
            text: null
            },
            dateTimeLabelFormats: { 
                    day: '%m-%d',
                }
        },
        colors: ['#6CF', '#84bf96', '#fdb933', '#c63c26'],
        yAxis: {
            title: {
            text: '页面一致率（%）'
            },
            min: 0,
            max: 100
        },
        tooltip: {
            headerFormat: '<b>{series.name}</b><br>',
            pointFormat: '{point.x:%Y-%m-%d}: {point.y:.2f}%'
        },
        plotOptions: {
            spline: {
            marker: {
                enabled: true
            }
            }
        },
        credits: {  
            enabled: false     //不显示LOGO 
        },
        series: [{
            name: '提测触发-iOS',
            data: iosNewSuccessRateList
        },
        {
            name: '提测触发-Android',
            data: androidNewSuccessRateList
        },
          {
            name: '回归/发布触发-iOS',
            data: iosRegressSuccessRateList
        },
        {
            name: '回归/发布触发-Android',
            data: androidRegressSuccessRateList
        }
        ],
        lang: {
            noData: "",
        },
        })
    }
  }   
}
</script>

<style scoped>
.chart{
  width:450px;
  height:350px;
  margin:0 auto;
}
.title{
  color:#808695;
  font-size:16px;
  font-weight:bold;
}
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
.time-line-title{
  font-weight: bolder;
  float:left;
  font-size: 15px;
}
</style>