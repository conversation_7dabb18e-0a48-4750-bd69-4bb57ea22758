<template>
  <div>
    <div :style="{padding: '1px',background: '#fff'}">
      <Layout :style="{minHeight: '100vh'}">
        <Sider
          ref="side1"
          hide-trigger
          collapsible
          :collapsed-width="78"
          v-model="isCollapsed"
          theme="light"
          :style="{background: '#fff'}"
          :width="140"
        >
          <Menu
            active-name="1"
            theme="light"
            width="auto"
            :open-names="['2']"
            @on-select="changeActive"
            :class="menuitemClasses"
            :style="{background: '#fff'}"
          >
            <MenuItem name="1">
              <span>自动化度量报告</span>
            </MenuItem>
            <MenuItem name="2">
              <span>持续交付情况</span>
            </MenuItem>
          </Menu>
        </Sider>
        <Layout>
          <div :style="{padding: '5px',minHeight: '50px', background: '#fff'}">          
            <Row v-show="active=='1'">
              <Col span="24">
                <div class="tab" style="background-color: #FFFFFF; padding-top: 0px">
                  <Alert show-icon type="warning" style="margin-left: 1%;margin-right: 1%; font-weight: bolder;">
                    <template slot="desc">迁移周知: 自动化度量详情，请移步到 <a href="http://qa.sankuai.com/client/autotestMeasure" target="_self">http://qa.sankuai.com/client/autotestMeasure</a>查看</template>
                  </Alert>
                </div>
              </Col>
              <Col span="12">
                <div>
                  <DatePicker
                    type="daterange"
                    split-panels
                    :value="nowDate"
                    placeholder="请选择日期"
                    style="width: 260px"
                    @on-change="autoTestFilterTime"
                  ></DatePicker>
                </div>
              </Col>
              <Col span="12">
                <div style="padding: 0 10px">
                  <Select
                    multiple
                    placeholder="请选择业务线"
                    :show-all-levels="false"
                    style="width: 300px"
                    v-model="autoTestBusinessFilter"
                    @on-change="autoTestBussinessFilter"
                  >
                    <Option
                      v-for="item in businessList"
                      :value="item.business"
                      :key="item.id"
                    >{{item.business}}</Option>
                  </Select>
                </div>
              </Col>
            </Row>
            <Row v-show="active=='2'">
              <Col span="8">
                <div>
                  <DatePicker
                    type="daterange"
                    split-panels
                    :value="nowDate"
                    placeholder="请选择日期"
                    style="width: 260px"
                    @on-change="jobCcdRunEffectTime"
                  ></DatePicker>
                </div>
              </Col>
              <Col span="8">
                <div style="padding: 0 10px">
                  <Select
                    multiple
                    placeholder="请选择业务线"
                    :show-all-levels="false"
                    style="width: 300px"
                    v-model="businessFilter"
                    @on-change="jobBusinessFilter"
                  >
                    <Option
                      v-for="item in businessList"
                      :value="item.id"
                      :key="item.id"
                    >{{item.business}}</Option>
                  </Select>
                </div>
              </Col>
              <Col span="8">
                <div style="padding: 0 10px">
                  <Select
                    multiple
                    placeholder="请选择触发节点"
                    :show-all-levels="false"
                    style="width: 260px"
                    v-model="triggerNodeFilter"
                    @on-change="jobTriggerNodeIdFilter"
                  >
                    <Option
                      v-for="item in triggerNodeList"
                      :value="item.id"
                      :key="item.id"
                    >{{item.publishName+item.integrationName}}</Option>
                  </Select>
                </div>
              </Col>
            </Row> 
          </div>
          <Header :style="{padding: '5px',minHeight: '20px'}" class="layout-header-bar">
            <Icon
              @click.native="collapsedSider"
              :class="rotateIcon"
              style="margin-left:2px,float:left"
              type="md-menu"
              size="20"
            ></Icon>
          </Header>
          <Content :style="{padding: '16px', minHeight: '200px', background: '#f5f7f9'}">
            <Alert v-show="active=='1'" show-icon :style="{paddingBottom: '6px',paddingTop: '6px'}">
              <span slot="desc" style="font-weight:bolder;font-size:16px;color:#333333">
                数据说明
                <Icon @click="showDataExplain = true" type="ios-help-circle-outline" />
              </span>
              <Drawer
                width="640"
                title="度量数据说明"
                placement="right"
                :closable="false"
                v-model="showDataExplain"
              >
                <span style="font-weight:bolder;font-size:14px;line-height:22px">
                  详情可查看:
                  <a href="https://km.sankuai.com/page/185406389">客户端自动化测试及持续交付度量体系</a>
                </span>
                <br />
                <br />
                <span style="font-weight:bolder;font-size:14px;line-height:22px">运行稳定性：</span>
                <br />
                <span style="font-weight:bolder;line-height:22px">1.Job成功率</span> : 成功Job数/触发Job总数;
                <br />
                <span style="font-weight:bolder;line-height:22px">2.Case成功率</span> : 成功case数 / (成功case数+失败case数+跳过case数);
                <br />
                <span style="font-weight:bolder;line-height:22px">3.页面一致率</span>: 页面一致数/运行页面总数 （统计范围：回归及发布任务）
                <br />
                <span style="font-weight:bolder;line-height:22px">4.Case跳过率</span> : 跳过Case数/ (成功Case数+失败Case数+跳过Case数）
                <br />
                <span style="font-weight:bolder;line-height:22px">5.Case非业务原因代码导致失败率</span> = 1 -（成功Case + 业务Bug导致Fail的Case）/ （成功Case + 所有失败Case）
                <br />
                <span style="font-weight:bolder;line-height:22px">6.Case TOP3失败原因及占比</span>: 取占比前3的Case失败原因
                <br />
                <br />
                <br />
                <span style="font-weight:bolder;font-size:14px;margin-top: 5px;">运行效率：</span>
                <br />
                <span style="font-weight:bolder;line-height:22px">1.Job运行耗时</span>：筛选时间范围内，各Job运行耗时集合，取90分位数;
                <br />
                <span style="font-weight:bolder;line-height:22px">2.Case运行耗时</span>：筛选时间范围内，各条Case运行耗时集合，取90分位数
                <br />
              </Drawer>
              <div slot="desc" style="font-size:12px;margin-top: 2px;font-weight:bolder">
                短链路自动化:
                <span
                  style="font-weight:normal"
                >Case成功率 = 成功case数 / (成功case数+失败case数+跳过case数)</span>
              </div>
              <div slot="desc" style="font-size:12px;margin-top: 2px;font-weight:bolder">
                视觉测试:
                <span style="font-weight:normal">页面一致率 = 页面一致数/运行页面总数 （统计范围：回归及发布任务）</span>
              </div>
            </Alert>
            <Tabs
              v-show="active=='1'"
              type="card"
              :style="{padding: '15px', minHeight: '280px', background: '#fff'}"
            >
              <TabPane label="执行情况">
                <AutotestRunSituationTable ref="AutotestRunSituationTable"></AutotestRunSituationTable>
              </TabPane>
              <TabPane label="运行效果">
                <AutotestRunEffectTable ref="AutotestRunEffectTable"></AutotestRunEffectTable>
              </TabPane>
            </Tabs>
            <Tabs
              v-show="active=='2'"
              type="card"
              :style="{padding: '15px', minHeight: '280px', background: '#fff'}"
            >
              <TabPane label="运行情况">
                <AutotestCcdRunEffectTable ref="AutotestCcdRunEffectTable"></AutotestCcdRunEffectTable>
              </TabPane>
            </Tabs>
          </Content>
        </Layout>
      </Layout>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import AutotestRunSituationTable from "./AutotestRunSituationTable";
import AutotestRunEffectTable from "./AutotestRunEffectTable";
import AutotestCcdRunTimeTable from "./AutotestCcdRunTimeTable";
import AutotestCcdRunEffectTable from "./AutotestCcdRunEffectTable";
import { time } from "highcharts";
export default {
  name: "AutotestAnalytic",
  components: {
    AutotestRunSituationTable,
    AutotestRunEffectTable,
    AutotestCcdRunTimeTable,
    AutotestCcdRunEffectTable
  },
  data() {
    return {
      isCollapsed: false,
      showDataExplain: false,
      channel: "",
      active: "1",
      nowDate: [],
      timeRange: [],
      business: "", //默认业务方向
      businessList: [],
      triggerNodeList: [], //触发节点筛选
      autoTestBusinessFilter: ["hotel","travel", "overseahotel"], //业务方向默认筛选
      businessFilter: [],
      triggerNodeFilter: [], // 触发节点默认筛选为空
      ccdRunEffectTimeFilter: [] //筛选时间
    };
  },
  computed: {
    menuitemClasses: function() {
      return ["menu-item", this.isCollapsed ? "collapsed-menu" : ""];
    },
    rotateIcon() {
      return ["menu-icon", this.isCollapsed ? "rotate-icon" : ""];
    }
  },
  mounted() {
    let endTime = this.formateTime(new Date());
    let startTime = this.formateTime(
      new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
    );
    this.nowDate = [startTime, endTime];
    //支持进入页面携带一个方向参数 或者 不携带方向参数
    if (this.$route.query.channel) {
      this.channel = this.$route.query.channel;
    }
    this.getShortLinkRunSituation(this.nowDate, this.businessFilter); //调用执行情况组件中的方法，展示执行情况表格数据
    this.getAutotestRunEffectTable(this.nowDate, this.businessFilter); //调用运行效果组件中的方法，展示运行效果表格数据

    this.getBusiness();
    this.getTriggerNode();
  },
  methods: {
    collapsedSider() {
      this.$refs.side1.toggleCollapse();
    },
    formateTime: function(time) {
      return (
        time.getFullYear() +
        "-" +
        (time.getMonth() >= 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        "-" +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate())
      );
    },
    changeActive: function(name) {
      this.active = name;
    },
    getAutotestRunEffectAndSituation(timeRange) {
      this.getShortLinkRunSituation(timeRange,businessFilter);
      this.getAutotestRunEffectTable(timeRange,businessFilter);
    },
    getShortLinkRunSituation(timeRange,businessFilter) {
      this.timeRange = timeRange;
      this.businessFilter = businessFilter;
        this.$refs.AutotestRunSituationTable.showRunsituationInfo(
          timeRange,
          businessFilter
        );
    },
    getAutotestRunEffectTable(timeRange,businessFilter) {
      (this.timeRange = timeRange),
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          timeRange,
          businessFilter
        );
    },
    getCcdRunEffectTable(timeRange, businessFilter, triggerNodeIdFilter) {
      ((this.timeRange = timeRange),
      (this.businessFilter = businessFilter),
      (this.triggerNodeIdFilter = triggerNodeIdFilter)),
        this.$refs.AutotestCcdRunEffectTable.showCcdRunEffectInfo(
          timeRange,
          businessFilter,
          triggerNodeIdFilter
        );
    },
    //持续交付筛选时间
    jobCcdRunEffectTime(timeRange) {
      this.ccdRunEffectTimeFilter = timeRange;
      this.getCcdRunEffectTable(
        timeRange,
        this.businessFilter,
        this.triggerNodeFilter
      );
    },
    //自动化度量筛选时间
    autoTestFilterTime(timeRange) {
        this.$refs.AutotestRunSituationTable.showRunsituationInfo(
          timeRange,
          this.autoTestBusinessFilter
        );
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          timeRange,
          this.autoTestBusinessFilter
        );
    },
    //持续交付筛选方向
    jobBusinessFilter: async function(value) {
      await this.$nextTick();
      this.businessFilter = value;
      this.getCcdRunEffectTable(this.timeRange, value, this.triggerNodeFilter);
    },
    //自动化度量报告筛选方向
    autoTestBussinessFilter: async function(value) {
        await this.$nextTick();
        this.autoTestBusinessFilter = value;
        this.$refs.AutotestRunSituationTable.showRunsituationInfo(
          this.timeRange,
          this.autoTestBusinessFilter
        );
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          this.timeRange,
          this.autoTestBusinessFilter
        );
    },
    //筛选触发节点
    jobTriggerNodeIdFilter: async function(value) {
      await this.$nextTick();
      this.triggerNodeFilter = value;
      this.getCcdRunEffectTable(this.timeRange, this.businessFilter, value);
    },
    getBusiness() {
      this.$axios({
        method: "get",
        url: this.env.url + "autoTestConfig/getAllBusinessName"
      })
        .then(res => {
          let message = res.data;
          this.businessList = message;
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    getTriggerNode() {
      this.$axios({
        params: {
          nodeType: "flow,time"
        },
        method: "get",
        url: this.env.url + "autoTestConfig/getNodeList"
      })
        .then(res => {
          let message = res.data;
          this.triggerNodeList = message;
          console.log(this.triggerNodeList);
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #dadada;
  position: relative;
  border-radius: 1px;
  overflow: hidden;
  margin-top: 15px;
  margin-left: 5px;
}
.layout-logo {
  width: 200px;
  height: 30px;
  /*background: #5b6270;*/
  border-radius: 3px;
  float: left;
  position: relative;
  top: 15px;
  left: 20px;
}
.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width 0.2s ease 0.2s;
  margin-left: -5px;
}
.menu-item i {
  transform: translateX(0px);
  transition: font-size 0.2s ease, transform 0.2s ease;
  vertical-align: middle;
  font-size: 10px;
}
.collapsed-menu span {
  width: 50px;
  transition: width 0.2s ease;
}
.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size 0.2s ease 0.2s, transform 0.2s ease 0.2s;
  vertical-align: middle;
  font-size: 22px;
}
.menu-icon {
  transition: all 0.3s;
  margin-left: 5px;
  float: left;
}
.rotate-icon {
  transform: rotate(-90deg);
  margin-left: 5px;
  float: left;
}
.layout-header-bar {
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  height: 35px;
}
.tab {
    margin-top: 15px;
    margin-left: 1.5%;
    margin-right: 1.5%;
    width: auto
  } 
</style>