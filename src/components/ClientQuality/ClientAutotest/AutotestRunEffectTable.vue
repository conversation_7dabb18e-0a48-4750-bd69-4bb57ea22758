<template>
    <div>
        <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
        </div>
        <p v-show = isDetailShow style="margin-bottom:10px;margin-top:10px;margin-left:10px" class="title">覆盖情况汇总</p>
        <AutotestRunEffectTableSum v-show="isDetailShow" :coverageData="this.tableData"></AutotestRunEffectTableSum>
        <p v-show = isDetailShow style="margin-bottom:10px;margin-top:30px;margin-left:10px" class="title">各自动化详细信息</p>
        <Table v-show="isDetailShow" stripe border :data="this.tableData" :columns="runEffectColumns"></Table>
        <AutotestRunEffectTableAssist v-show="isAssistShow" ref="AutotestRunEffectTableAssist" :coverageData="this.tableData"></AutotestRunEffectTableAssist>
    </div>
</template>

<script>

/* eslint-disable */
import DataTable from "../baseComponents/DataTable";
import AutotestRunEffectTableSum from "./AutotestRunEffectTableSum"
import AutotestRunEffectTableAssist from "./AutotestRunEffectTableAssist"
export default {
  name: 'AutotestRunEffectTable',
  components: {DataTable,AutotestRunEffectTableSum,AutotestRunEffectTableAssist},
   data () {
    return {
      tableData:[],
      isRequesting:false,        //是否展示loading
      isDetailShow: false,        //是否展示运行效果表格
      isAssistShow: false,
      selectChannel:"",
      coverageData:{},
      runEffectColumns:[
        {
            title: '方向',
            align: 'center',
            key: 'channelName',
            width: 100
        },{
            title: '短链路',
            align: 'center',
            width: 160,
            children: [
              {
                title: '接口覆盖',
                align: 'center',
                render:(h, params)=>{
                  var text = params.row.shortLinkApiCoverageRate
                  var textInfo;
                  if(params.row.shortLinkApiCoverageRate == "--"){
                    textInfo = ""
                  } else{
                    textInfo = " <br/> ( " + params.row.shortLinkApiCoverageNum + " / " + params.row.apiBaseNum + " )"
                  }
                  return h('div', [
                    h('span', {
                      domProps: {
                        innerHTML: text
                      }  
                    }),
                    h('span', {
                      domProps: {
                        innerHTML: textInfo
                      } ,
                      style: {
                        color: 'gray',
                        fontSize: '10px'
                      }  
                    })
                  ])
                },
              },
              {
                title: '页面覆盖',
                align: 'center',
                render:(h, params)=>{
                  var text = params.row.shortLinkPageCoverageRate
                  var textInfo;
                  if(params.row.shortLinkPageCoverageRate == "--"){
                    textInfo = ""
                  } else{
                    textInfo = " <br/> ( " + params.row.shortLinkPageCoverageNum + " / " + params.row.pageBaseNum + " )"
                  }
                  return h('div', [
                    h('span', {
                      domProps: {
                        innerHTML: text
                      }  
                    }),
                    h('span', {
                      domProps: {
                        innerHTML: textInfo
                      } ,
                      style: {
                        color: 'gray',
                        fontSize: '10px'
                      }  
                    })
                  ])
                },  
              },
              {
                title: '代码覆盖',
                align: 'center',
                key: 'shortLinkCodeCoverageRate' 
              },
              {
                title: '业务方bug',
                align: 'center',
                key: 'shortLinkBusinessBug'
              },
              {
                title: '其它方bug',
                align: 'center',
                key:'shortLinkOtherBug'
              }
            ]
        },{
            title: '兼容性',
            align: 'center',
            width: 160,
            children: [
              {
                title: '接口覆盖',
                align: 'center',
                render:(h, params)=>{
                  var text = params.row.compatibilityApiCoverageRate
                  var textInfo;
                  if(params.row.compatibilityApiCoverageRate == "--"){
                    textInfo = ""
                  } else{
                    textInfo = " <br/> ( " + params.row.compatibilityApiCoverageNum + " / " + params.row.apiBaseNum + " )"
                  }
                  return h('div', [
                    h('span', {
                      domProps: {
                        innerHTML: text
                      }  
                    }),
                    h('span', {
                      domProps: {
                        innerHTML: textInfo
                      } ,
                      style: {
                        color: 'gray',
                        fontSize: '10px'
                      }  
                    })
                  ])
                },
              },
              {
                title: '页面覆盖',
                align: 'center',
                key: 'compatibilityPageCoverageRate',
                render:(h, params)=>{
                  var text = params.row.compatibilityPageCoverageRate
                  var textInfo;
                  if(params.row.compatibilityPageCoverageRate == "--"){
                    textInfo = ""
                  } else{
                    textInfo = " <br/> ( " + params.row.compatibilityPageCoverageNum + " / " + params.row.pageBaseNum + " )"
                  }
                  return h('div', [
                    h('span', {
                      domProps: {
                        innerHTML: text
                      }  
                    }),
                    h('span', {
                      domProps: {
                        innerHTML: textInfo
                      } ,
                      style: {
                        color: 'gray',
                        fontSize: '10px'
                      }  
                    })
                  ])
                },
              },
              {
                title: '代码覆盖',
                align: 'center',
                key: 'compatibilityCodeCoverageRate'
              },
              {
                title: '业务方bug',
                align: 'center',
                key:'compatibilityBusinessBug'
              },
              {
                title: '其它方bug',
                align: 'center',
                key:'compatibilityOtherBug'
              }
            ]
        },{
            title: '详细信息',
            align: 'center',
            width: 70,
            render:(h, params)=>{
              return h('div',[
                h('Button',{
                  props: {
                    type:'primary',
                    size: 'small'
                  },
                    style: {
                    marginRight: '5px'
                  },
                    on: {
                    click: () => {
                      this.selectChannel = params.row.channel
                      this.selectChannelName = params.row.channelName
                      this.isAssistShow = true
                      this.$refs.AutotestRunEffectTableAssist.isShow(true,this.selectChannel,this.coverageData,this.selectChannelName)
                    }
                  }
                },'查看')
              ])
            },
        },
      ],
      channel: '',
      nowDate: [],
      timeRange: [],  
      allParam: [],
      coverage : {
          'shortLinkApiCoverageRate': '--',
          'shortLinkPageCoverageRate': '--',
          'shortLinkCodeCoverageRate': '--',
          'compatibilityApiCoverageRate': '--',
          'compatibilityPageCoverageRate': '--',
          'compatibilityCodeCoverageRate': '--'
      }
    } 
  },
  methods: {
    //展示运行效果表格
    showAutotestRunEffectInfo(timeRange,channel){
      this.isDetailShow = false,
      this.channel = channel
      this.timeRange = timeRange
      this.isAssistShow = false
      this.getAutotestRunEffectTable(timeRange,channel)
    },
    //获取自动化运行效果数据
    getAutotestRunEffectTable(timeRange,channel){
      this.isDetailShow = false,        //设置不展示运行效果表格
      this.isRequesting = true, //设置展示loading样式
      this.tableData = [];  //清空运行效果数据
      let startTimeStr = timeRange[0] + 'T00:00:01';
      let endTimeStr = timeRange[1] + 'T23:59:59';
      let startTimeStamp = new Date(startTimeStr);
      let endTimeStamp = new Date(endTimeStr);
      let startTime = startTimeStamp.getTime();
      let endTime = endTimeStamp.getTime();
      this.allParam = {
        "channel": channel,
        "startTime": startTime,
        "endTime": endTime,
      }
      this.$axios.all([
        this.getIssueNum(),
        this.getCoverageRate(channel,timeRange)
      ]).then((res)=>{
        console.log(res)
        this.tableData = [];  //清空运行效果数据
        if (res && res[0] && res[1]){
          if(res[0].data.code === 200){
            this.coverageData = res[1].data
            let issueNum = res[0].data.data.tableData;
            for (let i = 0; i < issueNum.length; i++) {
              let singleRow = Object.assign({}, issueNum[i], this.coverage);
              let channel = singleRow.channel
              console.log(channel)
              if (res[1].data.hasOwnProperty(channel)){
                singleRow['apiBaseNum'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("apiBaseNum")? res[1].data[channel].uiTest.apiBaseNum: "--"
                singleRow['pageBaseNum'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("pageBaseNum")? res[1].data[channel].uiTest.pageBaseNum: "--"                
                singleRow['shortLinkApiCoverageNum'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("apiCoverageNum")? res[1].data[channel].uiTest.apiCoverageNum: "--"
                singleRow['shortLinkPageCoverageNum'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("pageCoverageNum")? res[1].data[channel].uiTest.pageCoverageNum: "--"
                singleRow['compatibilityApiCoverageNum'] = 
                  res[1].data[channel].compatibility.hasOwnProperty("apiCoverageNum")? res[1].data[channel].compatibility.apiCoverageNum: "--"
                singleRow['compatibilityPageCoverageNum'] = 
                  res[1].data[channel].compatibility.hasOwnProperty("pageCoverageNum")? res[1].data[channel].compatibility.pageCoverageNum: "--"
                singleRow['shortLinkApiCoverageRate'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("apiCoverageRate")? res[1].data[channel].uiTest.apiCoverageRate: "--"
                singleRow['shortLinkPageCoverageRate'] = 
                  res[1].data[channel].uiTest.hasOwnProperty("pageCoverageRate")? res[1].data[channel].uiTest.pageCoverageRate: "--"
                singleRow['compatibilityApiCoverageRate'] = 
                  res[1].data[channel].compatibility.hasOwnProperty("apiCoverageRate")? res[1].data[channel].compatibility.apiCoverageRate: "--"
                singleRow['compatibilityPageCoverageRate'] = 
                  res[1].data[channel].compatibility.hasOwnProperty("pageCoverageRate")? res[1].data[channel].compatibility.pageCoverageRate: "--"
                singleRow['allAutoTestApiCoverageNum'] = 
                  res[1].data[channel].allAutoTest.hasOwnProperty("apiCoverageNum")? res[1].data[channel].allAutoTest.apiCoverageNum: "--"
                singleRow['allAutoTestPageCoverageNum'] = 
                  res[1].data[channel].allAutoTest.hasOwnProperty("pageCoverageNum")? res[1].data[channel].allAutoTest.pageCoverageNum: "--"
                singleRow['allAutoTestApiCoverageRate'] = 
                  res[1].data[channel].allAutoTest.hasOwnProperty("apiCoverageRate")? res[1].data[channel].allAutoTest.apiCoverageRate: "--"
                singleRow['allAutoTestPageCoverageRate'] = 
                  res[1].data[channel].allAutoTest.hasOwnProperty("pageCoverageRate")? res[1].data[channel].allAutoTest.pageCoverageRate: "--"
                
              }
              this.tableData.push(singleRow);
            }
          }else{
            let error =res[0].data.data.msg;
            let errorToStr = JSON.stringify(error);
            console.log(errorToStr);          
          }
          console.log(this.tableData)
          this.isRequesting = false;    //设置不展示loading 
          this.isDetailShow = true;     //设置展示运行效果表格
        }
      });
    }, 
    //请求bug个数数据
    getIssueNum() {
      return this.$axios({
        method:"post",
        data:this.allParam,
        url: this.env.client_common_url+"client/ones/issues/search/issueNum"
      })
    },
    //请求自动化覆盖率数据
    getCoverageRate(channel,timeRange) {
      var value = "";
      if(channel.length > 0){
          value = channel.join(",");
      }
      return  this.$axios({
        params:{
            start_time: timeRange[0],
            end_time: timeRange[1],
            channel: value
        },
        method:"get",
        url:this.env.client_common_url+"client/coverage/rate",
      })
    }
  }
}
</script>

<style scoped>
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
.title{
  color:#808695;
  font-size:16px;
  font-weight:bold;
  text-align:left;
}
</style>