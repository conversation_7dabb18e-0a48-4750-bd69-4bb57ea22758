<template>
  <div>
    <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
      <Spin>
        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
      </Spin>
    </div>
    <p
      v-show="isDetailShow"
      style="margin-bottom:10px;margin-top:30px;margin-left:10px;font-size:16px"
      class="title"
    >
      各自动化触发成功率统计
      <Tooltip max-width="500" placement="right-start">
        <Icon type="ios-help-circle-outline" />
        <div slot="content">
          <p>计算公式:</p>
          <p>自动化触发成功率=自动化触发成功数/自动化期望触发数</p>
          <p>数据来源:</p>
          <p>持续集成调用自动化触发接口，根据接口返回判断触发状态</p>
        </div>
      </Tooltip>
    </p>
    <v-table
      v-show="isDetailShow"
      :columns="runEffectColumns"
      :table-data="tableData"
      :cell-merge="cellMerge"
      :column-cell-class-name="columnCellClass"
      @on-custom-comp="customCompFunc"
    ></v-table>
  </div>
</template>

<script>
/* eslint-disable */
import Highcharts from "highcharts";
import HighchartsNoData from "highcharts/modules/no-data-to-display";
HighchartsNoData(Highcharts);
import Vue from "vue";
import "vue-easytable/libs/themes-base/index.css";
import { VTable, VPagination } from "vue-easytable";
import { now } from "moment";

export default {
  name: "AutotestCcdSuccessRateTable",
  components: {
    VTable,
    VPagination
  },
  data() {
    return {
      tableData: [],
      isRequesting: false, //是否展示loading
      isDetailShow: false, //是否展示运行效果表格
      showDataExplain: false, //是否展示数据说明
      runEffectColumns: [
        {
          field: "autotestType",
          title: "自动化类型",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name"
        },
        {
          field: "successRate",
          title: "触发成功率",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class-name",
          formatter: function(rowData, rowIndex, pagingIndex, field) {
            return rowData.successRate !== "100.00%"
              ? '<span style="color:red;">' + rowData.successRate + "</span>"
              : rowData.successRate;
          }
        }
      ]
    };
  },
  methods: {
    // 列样式设置
    columnCellClass(rowIndex, columnName, rowData) {
      // 给业务方向列设置className
      if (columnName === "autotestType") {
        return "column-cell-class-name";
      }
    },
    //展示运行效果表格
    showCcdSuccessRateInfo(startTime, endTime) {
      this.isDetailShow = false;
      this.getCcdSuccessRateTable(startTime, endTime);
    },
    //获取自动化运行效果数据
    getCcdSuccessRateTable(startTime, endTime) {
      (this.isDetailShow = false), //设置不展示运行效果表格
        (this.isRequesting = true), //设置展示loading样式
        (this.tableData = []); //清空运行效果数据
      this.$axios({
        params: {
          startTime: startTime,
          endTime: endTime
        },
        method: "GET",
        url: this.env.url + "ccd/trigger/autotest/successRate"
      }).then(res => {
        let runEffect = res.data;
        console.log(res.data);
        this.tableData = []; //清空运行效果数据
        if (runEffect.code === 200) {
          this.tableData = runEffect.data; //存储执行情况数据
          console.log(this.tableData);
          this.isRequesting = false; //设置不展示loading
          this.isDetailShow = true; //设置展示运行效果表格
        } else if (runEffect.code === 400) {
          this.tableData = [];
          this.isRequesting = false; //设置不展示loading
          this.isDetailShow = true; //设置不展示运行效果表格
        }
      });
    }
  }
};
</script>
<style scoped>
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.title {
  color: #808695;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}
.chart {
  width: 450px;
  height: 350px;
  margin: 0 auto;
}
.title-cell-class-name-test {
  background-color: #f60;
  color: #fff;
}
</style>