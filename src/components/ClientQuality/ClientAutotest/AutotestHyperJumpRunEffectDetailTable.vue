<template>
  <div>
    <div v-show="isShowDetailMeasureTable" style="text-align: left;margin-left: 40px;margin-top: -5px;margin-bottom:15px">
        <a style="display: inline-block;margin-bottom:10px;font-size:20px;color:#333333;">
        <span>视觉自动化结果详情</span>
         </a>
    </div>
    <Table border v-show="isShowDetailMeasureTable" :columns='detailColumns' :data='showDetailData' width='100px' size='small' :span-method='cellDetailMerge' @on-filter-change='columnFilterDetailChange'></Table>
    <div style=' overflow: hidden' v-if='detailTableData.length >= 20'>
      <div style='float: right; height: 90px'>
        <Page :total='detailTableData.length' :current='detailShowIndex' :page-size='detailShowSize' :page-size-opts='pageSizeOpts' show-sizer show-total @on-page-size-change='changeDetailPageSize' @on-change='changeDetailPage' />
      </div>
    </div>
    <div class='spin-container' v-if='isRequesting' style='padding-top: 50px'>
      <Spin>
        <Icon type='ios-loading' size='18' class='spin-icon-load'></Icon>
      </Spin>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CompatibilityDetailMeasureInfo',
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showDataExplain: false,
      nowDate: [], // 用户选择时间
      startTime: '',
      endTime: '',
      isRequesting: false,
      groupId: '', // 用户选择的groupId
      unitId: '', // 用户选择的unitId
      businessId: '', // 用户选择的businessId
      testSlot: [], // 触发项
      detailMergeItems: [0, 1, 2],
      mergerRosRecordDetail: {}, // 待合并列内容
      propObj: {}, // 存储更新后表头数据
      detailShowIndex: 1,
      detailShowSize: 50,
      pageSizeOpts: [10, 15, 20, 30, 50, 100],
      showMisID: false, // 是否展示misID列
      showPageName: false, // 是否展示pageName列
      detailTableData: [], // 点击超链接后，展开新的表格信息
      isShowDetailMeasureTable: false// 控制detailMeasureTable展示
    }
  },
  mounted() {
    this.$store.dispatch('getProductList')
    this.$store.dispatch('getNodeList')
    this.$store.dispatch('getMisIdList')
    this.$store.dispatch('getPageNameList')
    let endTime = this.formateTime(new Date())
    let startTime = this.formateTime(
      new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
    )
    this.nowDate = [startTime, endTime]
  },
  computed: {
    detailColumns() {
      let beforeColumns = [
        {
          title: '研发测试节点',
          key: 'nodeLabel',
          align: 'center'
        },
        {
          title: '产品类型',
          key: 'productLabel',
          align: 'center'
        },
        {
          title: 'misID',
          key: 'misId',
          align: 'center'
        },
        {
          title: '页面',
          key: 'pageName',
          align: 'center',
          render: (h, params) => {
            return h('a', {
              attrs: {
                href: `https://qa.sankuai.com/client/page/${params.row.pageId}/scene`,
                target: '_blank'
              }
            }, params.row.pageName)
          }
        },
        {
          title: '对⽐⼀致率',
          key: 'picPassRate',
          align: 'center',
          render: (h, params) => {
            const rate = params.row.picPassRate
            const rateNum = parseFloat(rate)
            if (rateNum < 80) {
              return h('span', { style: { color: 'red' } }, rate)
            } else {
              return h('span', rate)
            }
          }
        },
        {
          title: 'Job链接',
          key: 'jobLink',
          align: 'center',
          render: (h, params) => {
            if (params.row.jobIds && params.row.jobIds.length > 0) {
              return h('span', params.row.jobIds.map((jobId, index) => {
                return [
                  h('a', {
                    attrs: {
                      href: `https://qa.sankuai.com/microscope/jobInfo?jobId=${jobId}`,
                      target: '_blank'
                    }
                  }, `${jobId}`),
                  index < params.row.jobIds.length - 1 ? h('span', ' | ') : null
                ]
              }).flat())
            } else {
              return h('span', '无Job链接')
            }
          }
        }
      ]
      return beforeColumns
    },

    showDetailData: function () {
      let showDetailData = this.detailTableData
      if (this.detailShowSize < this.detailTableData.length) {
        showDetailData = this.detailTableData.slice(
          (this.detailShowIndex - 1) * this.detailShowSize,
          (this.detailShowIndex - 1) * this.detailShowSize + this.detailShowSize
        )
      }
      return showDetailData
    }
  },
  methods: {
    // 获取具体的负责人、页面的页面一致率
    getDetailMeasureInfo(row, startTime, endTime) {
      this.isShowDetailMeasureTable = false // 设置暂不展示执行情况表格
      this.isRequesting = true // 设置展示loading样式
      this.detailTableData = []
      this.getColkey(this.detailColumns)
      this.$axios({
        method: 'POST',
        url: this.env.url + 'autoTestAnalytic/vision/DetailMeasureInfo',
        data: {
          businessId: row.businessId || undefined,
          buId: row.buId || undefined,
          bgId: row.bgId || undefined,
          productIds: [row.productId],
          nodeIds: [row.nodeId],
          startTime: startTime,
          endTime: endTime
        }
      }).then(res => {
        let node = ''
        this.isRequesting = false // 设置不展示loading
        let triggerResult = res.data || {}
        this.detailTableData = [] // 清空运行效果数据
        this.detailTableData = triggerResult.compatibilityDetailMeasureInfo || [] // 存储执行情况数
        for (let i = 0; i < this.detailTableData.length; i++) {
          node = this.detailTableData[i].publish_name + '-' + this.detailTableData[i].integration_name
          this.detailTableData[i].nodeLabel = node
        }
        this.getSpanArr(this.showDetailData, this.detailMergeItems)
        this.isShowDetailMeasureTable = true
        this.detailShowIndex = 1
      }).catch(err => {
        console.log(err)
      })
    },

    cellDetailMerge({ row, column, rowIndex, columnIndex }) {
      if (this.detailMergeItems.includes(columnIndex)) {
        let item = this.mergerRosRecordDetail[columnIndex]
        const _row = item[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },

    getSpanArr(data, array) {
      //  循环数据处理
      let mergerRosRecordDetail = {}
      let pos = 0
      for (let n of array) {
        pos = 0
        mergerRosRecordDetail[n] = [] // 数据清空(重新调此方法的时候需要清空上一次的数据)
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            mergerRosRecordDetail[n].push(1)
            pos = 0
          } else {
            //  判断当前元素与上一个元素是否相同
            if (
              data[i][this.propObj[n]] === data[i - 1][this.propObj[n]] &&
              data[i][this.propObj[n - 1]] === data[i - 1][this.propObj[n - 1]]
            ) {
              mergerRosRecordDetail[n][pos] += 1
              mergerRosRecordDetail[n].push(0)
            } else {
              mergerRosRecordDetail[n].push(1)
              pos = i
            }
          }
        }
      }
      this.mergerRosRecordDetail = { ...mergerRosRecordDetail }
    },
    // 获取列的key
    getColkey(colData) {
      let index = 0
      let tmpObj = {}
      for (let i of colData) {
        if (!i.children) {
          tmpObj[index] = i.key
          index++
        } else {
          for (let k of i.children) {
            tmpObj[index] = k.key
            index++
          }
        }
      }
      this.propObj = { ...tmpObj }
    },
    changeDetailPage(index) {
      this.detailShowIndex = index
      this.getSpanArr(this.showDetailData, this.detailMergeItems)
    },
    changeDetailPageSize(size) {
      this.detailShowSize = size
      this.getSpanArr(this.showDetailData, this.detailMergeItems)
    },

    columnFilterDetailChange() {
      this.changeDetailPageSize(100)
      this.changeDetailPage(1)
    }
  }
}
</script>
<style scoped>
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.select-location {
  text-align: center;
  margin-top: -5px;
}
html {
  height: 100%;
  display: table;
}
body {
  display: table-cell;
  height: 100%;
  margin: 100px;
}
.ivu-table-header table {
  width: 100% !important;
}
</style>
