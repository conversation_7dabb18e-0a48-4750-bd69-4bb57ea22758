<template>
    <div>
        <Table stripe border :data="tableData" :columns="runSituationColumns" no-data-text="暂无数据"></Table>
    </div>
</template>

<script>

/* eslint-disable */
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestCDTable',
  components: {DataTable},
   data () {
    return {
      tableData:[],
      runSituationColumns:[
        {
            title: '方向',
            align: 'center',
            width: 70
        },{
            title: '短链路',
            align: 'center',
            width: 160,
            children: [
              {
                title: '运行',
                align: 'center',
                children: [
                    {
                        title: '提测节点',
                        align: 'center',
                        key: "aaa"
                    },
                    {
                        title: '发布节点',
                        align: 'center'
                    },
                    {
                        title: '发布中热修节点',
                        align: 'center'
                    },
                    {
                        title: '热修复节点',
                        align: 'center'
                    }
                ]
              },{
                title: '效果',
                align: 'center',
                children: [
                    {
                        title: '提测节点',
                        align: 'center'
                    },
                    {
                        title: '发布节点',
                        align: 'center'
                    },
                    {
                        title: '发布中热修节点',
                        align: 'center'
                    },
                    {
                        title: '热修复节点',
                        align: 'center'
                    }
                ]
              }
            ]
        },{
            title: '兼容性',
            align: 'center',
            width: 160,
            children: [
              {
                title: '运行',
                align: 'center',
                children: [
                    {
                        title: '提测节点',
                        align: 'center'
                    },
                    {
                        title: '发布节点',
                        align: 'center'
                    },
                    {
                        title: '发布中热修节点',
                        align: 'center'
                    },
                    {
                        title: '热修复节点',
                        align: 'center'
                    }
                ]
              },{
                title: '效果',
                align: 'center',
                children: [
                    {
                        title: '提测节点',
                        align: 'center'
                    },
                    {
                        title: '发布节点',
                        align: 'center'
                    },
                    {
                        title: '发布中热修节点',
                        align: 'center'
                    },
                    {
                        title: '热修复节点',
                        align: 'center'
                    }
                ]
              }
            ]
        }
      ]
    }
  },
  methods: {
    
  }
}
</script>

<style scoped>

</style>