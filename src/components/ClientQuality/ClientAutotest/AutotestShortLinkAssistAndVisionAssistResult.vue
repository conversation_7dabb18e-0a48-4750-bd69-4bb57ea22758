<template>
  <div>
    <div :style="{padding: '0px 20px 0px 20px',background: '#fff'}">
      <Layout :style="{minHeight: '180vh'}">
        <div :style="{padding: '0px 5px 50px 5px', minHeight: 'auto', background: '#fff'}">  <!-- 修改padding和高度 -->
          <Row>
            <div style="text-align: left; margin-left: 40px; margin-top: -50px; margin-bottom: 10px">  <!-- 调整margin-top -->
                <a style="display: inline-block; margin-bottom: 5px; font-size:15px; color:#333333;"></a>
            </div>
          </Row>

          <!-- 第一行筛选器 -->
          <Row :gutter="120" >
            
            <Col span="6">
              <div style="display: flex; align-items: center;">
                <span style="width:70px; text-align:right; margin-right:8px; font-size:14px; color:#515a6e; font-weight:bolder; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif">
                  <span style="position: relative; top: -1px; left: -1px; color: red">*</span>
                  日期：
                </span>
                <DatePicker 
                  type="daterange" 
                  :value="timeRange" 
                  split-panels 
                  placeholder="请选择时间范围" 
                  style="width: 200px" 
                  @on-change="changeTimeRange">
                </DatePicker>
              </div>
            </Col>
            
           
            <Col span="6">
              <div style="display: flex; align-items: center;">
                <span style="width:70px; text-align:right; margin-right:8px;font-size:14px; color:#515a6e; font-weight:bolder; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif">产品：</span>
                <Select
                  v-model="productFilter"
                  multiple
                  label-in-value
                  placeholder="请选择产品"
                  style="width: 200px"
                  @on-change="handleProductFilter"
                >
                  <Option v-for="item in productList" :value="item.id" :key="'product-' + item.id">
                    {{ item.label }}
                  </Option>
                </Select>
              </div>
            </Col>
            
            
            <Col span="6">
              <div style="display: flex; align-items: center;">
                <span style="width:70px; text-align:right; margin-right:8px; font-size:14px; color:#515a6e; font-weight:bolder; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif">触发节点：</span>
                <Select
                  v-model="nodeFilter"
                  multiple
                  label-in-value
                  placeholder="请选择触发节点"
                  style="width: 200px"
                  @on-change="handleNodeFilter"
                >
                  <Option v-for="item in allNode" :value="item.id" :key="'node-' + item.id">
                    {{ item.publishName + '-' + item.integrationName }}
                  </Option>
                </Select>
              </div>
            </Col>
            
            
            <Col span="6">
              <div style="display: flex; align-items: center;">
                <span style="width:70px; text-align:right; margin-right:8px; font-size:14px; color:#515a6e; font-weight:bolder; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif">MIS：</span>
                <Select
                  v-model="misIDFilter"
                  multiple
                  placeholder="请选择MIS"
                  style="width: 200px"
                  @on-change="handleMisIDFilter"
                >
                  <Option v-for="item in misIdList" :value="String(item)" :key="'mis-' + item">
                    {{ item }}
                  </Option>
                </Select>
              </div>
            </Col>
          </Row>
          <Row :gutter="120" style="margin-top: 20px">  <!-- 从10px改为20px -->
            <Col span="6">
              <div style="display: flex; align-items: center;">
                <span style="width:70px; text-align:right; margin-right:8px;font-size:14px; color:#515a6e; font-weight:bolder; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif">页面：</span>
                <Select
                  v-model="pageSelection"
                  multiple
                  placeholder="请选择页面"
                  style="width: 200px"
                  @on-change="handlePageFilter"
                >
                  <Option v-for="item in pageNameList" :value="String(item.id)" :key="'page-' + item.id">
                    {{ item.pageName }}
                  </Option>
                </Select>
              </div>
            </Col>
            <Col span="6">
              <div style="display: flex; 
                         align-items: center; 
                         padding-left: 78px;  
                         width: 100%;">
                <Button 
                  type="primary" 
                  @click="search" 
                  style="
                    width: 100px; 
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;">
                  <Icon type="ios-search" style="margin-right: 4px;"/>
                  <span>Search</span>
                </Button>
                <Icon 
                  @click="showDataExplain = true" 
                  type="ios-help-circle-outline" 
                  size="20" 
                  style="margin-left: 10px; cursor: pointer;"/>
              </div>
            </Col>
          </Row>
        </div>
        <Menu 
          class="client-menu" 
          mode="horizontal" 
          theme="light" 
          :active-name="tab" 
          @on-select="handleSelect"
          style="padding: 0px 15px;">
          <MenuItem name="compatibility">
            视觉自动化
          </MenuItem>
          <MenuItem name="shortlink">
            短链路自动化
          </MenuItem>
          <MenuItem name="videoPlayer">
            视频自动化
          </MenuItem>
          <MenuItem name="apiDiff">
            API DIFF
          </MenuItem>
        </Menu>

        <!-- 为所有表格添加统一缩进容器 -->
        <div style="padding: 15px 30px;">
          <AutotestHyperJumpRunEffectTable v-show="tab === 'compatibility'" ref="AutotestHyperJumpRunEffectTable"></AutotestHyperJumpRunEffectTable>
          <AutotestShortlinkRunEffectTable v-show="tab === 'shortlink'" ref="AutotestShortlinkRunEffectTable"></AutotestShortlinkRunEffectTable>
          <AutotestVideoPlayerRunEffectTable v-show="tab === 'videoPlayer'" ref="AutotestVideoPlayerRunEffectTable"></AutotestVideoPlayerRunEffectTable>
          <AutotestApiDiffRunEffectTable v-show="tab === 'apiDiff'" ref="AutotestApiDiffRunEffectTable"></AutotestApiDiffRunEffectTable>
        </div>

      </Layout>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import AutotestHyperJumpRunEffectTable from "./AutotestHyperJumpRunEffectTable";
import AutotestShortlinkRunEffectTable from "./AutotestShortlinkRunEffectTable";
import AutotestVideoPlayerRunEffectTable from "./AutotestVideoPlayerRunEffectTable";
import AutotestApiDiffRunEffectTable from "./AutotestApiDiffRunEffectTable";

export default {
  name: "AutotestShortLinkAssistAndVisionAssistResult",
  components: {
    AutotestHyperJumpRunEffectTable,
    AutotestShortlinkRunEffectTable,
    AutotestVideoPlayerRunEffectTable,
    AutotestApiDiffRunEffectTable
  },
  data() {
    return {
      showDataExplain: false,
      tab: 'compatibility', // 默认显示视觉自动化的表格
      timeRange: [],
      businessId: '', // 从URL获取的业务ID
      businessList: [], // 存储所有业务信息
      bgId: undefined,
      buId: undefined,
      productFilter: [], // 存放用户选择的product
      nodeFilter: [], // 存放用户选择的node
      misIDFilter: [], // 存放用户选择的misID
      pageSelection: [], // 存放用户选择的页面名称
      misIdList: [], // MIS ID列表
      pageNameList: [], // 页面名称列表
      selectedProducts: [], // 存储选中的产品
      selectedNodes: [] // 存储选中的节点
    };
  },
  computed: {
    productList() {
      return this.$store.state.clientQuality.productList || [];
    },
    allNode() {
      return this.$store.state.clientQuality.nodeList || [];
    }
  },
  mounted() {
    let endTime = this.formateTime(new Date());
    let startTime = this.formateTime(
      new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
    );
    this.timeRange = [startTime, endTime];
    this.businessId = this.$route.params.businessId;
    
    // 初始化数据
    this.getBusiness();
    this.$store.dispatch('getBusinessProduct', this.businessId);
    this.$store.dispatch('getNodeList');
  },
  methods: {
    formateTime: function(time) {
      return (
        time.getFullYear() +
        "-" +
        (time.getMonth() >= 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        "-" +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate())
      );
    },
    async getBusiness() {
      try {
        // 先获取当前用户的业务归属
        const userBusinessResponse = await this.$axios({
          method: 'get',
          params: {
            misId: Bus.userInfo.userLogin
          },
          url: this.env.url + 'autoTestConfig/getAllBusinessName'
        });
        
        if (userBusinessResponse.data) {
          this.businessList = userBusinessResponse.data;
          
          // 根据businessId获取具体业务信息
          const businessResponse = await this.$axios({
            method: 'get',
            params: {
              businessId: this.businessId
            },
            url: this.env.url + 'autoTestConfig/getBusinessByBusinessName'
          });
          
          if (businessResponse && businessResponse.data) {
            const businessInfo = businessResponse.data;
            this.bgId = businessInfo.bgId;
            this.buId = businessInfo.buId;
            
            // 获取业务信息后再获取MIS和页面列表
            this.getMisIDList();
            this.getPageNameList();
          }
        }
      } catch (error) {
        console.error('获取业务信息失败:', error);
      }
    },
    getMisIDList() {
      return this.$axios({
        params: {
          businessId: this.businessId || undefined,
          buId: this.buId || undefined,
          bgId: this.bgId || undefined
        },
        method: 'get',
        url: this.env.url + 'autoTestAnalytic/autoTest/misIdInfo'
      }).then(response => {
        let triggerResult = response.data || {};
        this.misIdList = triggerResult.misIdList || [];
      }).catch(error => {
        console.log(error);
        this.misIdList = [];
      });
    },
    getPageNameList() {
      return this.$axios({
        params: {
          businessId: this.businessId || undefined,
          buId: this.buId || undefined,
          bgId: this.bgId || undefined
        },
        method: 'get',
        url: this.env.url + 'autoTestAnalytic/autoTest/pageInfo'
      }).then(response => {
        let triggerResult = response.data || {};
        this.pageNameList = triggerResult.pageList || [];
      }).catch(error => {
        console.log(error);
        this.pageNameList = [];
      });
    },
    handleSelect(name) {
      this.tab = name;
      this.updateTableData();
    },
    changeTimeRange(value) {
      this.timeRange = value;
      this.updateTableData();
    },
    // 产品筛选
    handleProductFilter(value) {
      this.selectedProducts = value;
      this.productFilter = value.map(item => item.value);
    },
    // 触发节点筛选
    handleNodeFilter(value) {
      this.selectedNodes = value;
      this.nodeFilter = value.map(item => item.value);
    },
    // misID筛选
    handleMisIDFilter(value) {
      this.misIDFilter = value;
    },
    // 页面筛选
    handlePageFilter(value) {
      this.pageSelection = value;
    },
    // 搜索按钮点击事件
    search() {
      this.updateTableData();
    },
    updateTableData() {
      switch(this.tab) {
        case 'compatibility':
          this.$refs.AutotestHyperJumpRunEffectTable.getTableData(
            this.timeRange,
            this.buId,
            this.bgId,
            this.businessId,
            this.productFilter,
            this.nodeFilter,
            this.misIDFilter,
            this.pageSelection
          );
          break;
        case 'shortlink':
          this.$refs.AutotestShortlinkRunEffectTable.getTableData(
            this.timeRange,
            this.buId,
            this.bgId,
            this.businessId,
            this.productFilter,
            this.nodeFilter,
            this.misIDFilter,
            this.pageSelection
          );
          break;
        case 'videoPlayer':
          this.$refs.AutotestVideoPlayerRunEffectTable.getTableData(
            this.timeRange,
            this.buId,
            this.bgId,
            this.businessId,
            this.productFilter,
            this.nodeFilter,
            this.misIDFilter,
            this.pageSelection
          );
          break;
        case 'apiDiff':
          this.$refs.AutotestApiDiffRunEffectTable.getTableData(
            this.timeRange,
            this.buId,
            this.bgId,
            this.businessId,
            this.productFilter,
            this.nodeFilter,
            this.misIDFilter,
            this.pageSelection
          );
          break;
      }
    }
  }
};
</script>

<style scoped>
/* 添加全局字体定义 */
.client-menu,
.ivu-select-selection,
.ivu-btn {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  font-size: 14px;
}

/* 表格内容字体 */
.ivu-table td {
  font-size: 13px;
  color: #333;
}

/* 标题字体修正 */
div[style*="font-size:25px"] {
  font-size: 16px !important;
  font-weight: 500;
}
</style>

.client-menu {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

/* 由于 Select 的下拉菜单在 body 下，我们需要添加全局样式 */
</style>

<style>
.ivu-select-dropdown {
  z-index: 1000 !important;
}

.ivu-date-picker-cells {
  z-index: 1000 !important;
}
</style>
