<template>
    <div>
        <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
      </div>
      <Timeline v-show = isAssistShow style="padding-top: 15px;padding-left: 2px">
           <TimelineItem>
            <Icon type="ios-pricetags-outline" slot="dot"></Icon>
            <span class= time-line-title>运行稳定性</span>
            <Row v-show = isAssistShow style="padding-top: 35px">
              <Col :md="12">
                <div id="iOS-resultInfo" class="chart"></div>
              </Col>
              <Col :md="12">
                <div id="Android-resultInfo" class="chart"></div>
              </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <Icon type="ios-pricetags-outline" slot="dot"></Icon>
          <span class= time-line-title>失败原因</span>
          <Row style="padding-top: 35px">
            <Col :md="12" >
              <p style="margin-bottom:15px;" class="title">iOS端Case失败原因Top3</p>
              <Table width="420" style="margin:0 auto;" border :data="iosFailReasonInfo" :columns="failReasonColumns"></Table>
            </Col>
            <Col :md="12">
            <p style="margin-bottom:15px;" class="title">Android端Case失败原因Top3</p>
              <Table width="420" style="margin:0 auto;" border :data="androidFailReasonInfo" :columns="failReasonColumns"></Table>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
             <Icon type="ios-pricetags-outline" slot="dot"></Icon>
             <span class= time-line-title>运行效率</span>
        <Table v-show = isAssistShow width="550" style="margin-top:50px;float:middle;margin-left:70px" border :data="this.assistInfoTableData" :columns="this.jobAssistInfoTableColumns"></Table>
        <Table v-show = isAssistShow width="1100" style="margin-top:50px;float:middle;margin-left:70px" border :data="this.assistInfoTableData" :columns="this.caseAssistInfoTableColumns"></Table>
        </TimelineItem>
        </Timeline>
    </div>
</template>

<script>

/* eslint-disable */
import Highcharts from 'highcharts';
import HighchartsNoData from 'highcharts/modules/no-data-to-display'
HighchartsNoData(Highcharts)
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestRunSituationShortlinkAssist',
  components: {DataTable},
  props: [],
   data () {
    return {
      isRequesting: false,
      isAssistShow: false,
      iosFailReasonInfo:[],
      androidFailReasonInfo:[],
      shortLinkRunSituationAssist:{},
      assistInfoTableData:[],
      jobAssistInfoTableColumns:[
        {
            title: 'Job运行耗时',
            renderHeader: (h, params) => {
              var text = 'Job运行耗时';
              return h('div', {
              domProps:{
                innerHTML: text
              },
              style:{
                fontSize: '16px',
                fontWeight: 'bolder',
                color:'#333333'
              }
              })
            },
            align: 'center',
            key: 'count',
            children:[
                {
                    title: '总成功运行耗时（TP50）',
                    align: 'center',
                    key: 'allJobDurationTp50'
                },
                {
                    title: '成功运行耗时（TP50）',
                    align: 'center',
                    children:[
                        {
                            title: 'iOS',
                            align: 'center',
                            key: 'iosJobDurationTp50'
                        },
                        {
                            title: 'Android',
                            align: 'center',
                            key: 'androidJobDurationTp50'
                        }
                    ]
                }
            ]
        }
      ],
      caseAssistInfoTableColumns:[
        {
            title: 'Case运行耗时',
            renderHeader: (h, params) => {
              var text = 'Case运行耗时';
              return h('div', {
              domProps:{
                innerHTML: text
              },
              style:{
                fontSize: '16px',
                fontWeight: 'bolder',
                color:'#333333'
              }
              })
            },
            align: 'center',
            key: 'count',
            children:[
                {
                    title: '总成功运行耗时（TP50）',
                    align: 'center',
                    key: 'allPassCaseDurationTp50'
                },
                {
                    title: '总失败运行耗时（TP50）',
                    align: 'center',
                    key: 'allFailuresCaseDurationTp50'
                },
                {
                    title: '成功运行耗时（TP50）',
                    align: 'center',
                    children:[
                        {
                            title: 'iOS',
                            align: 'center',
                            key: 'iosPassDurationTp50'
                        },
                        {
                            title: 'Android',
                            align: 'center',
                            key: 'androidPassDurationTp50'
                        }
                    ]
                },
                {
                    title: '失败运行耗时（TP50）',
                    align: 'center',
                    children:[
                        {
                            title: 'iOS',
                            align: 'center',
                            key: 'iosFailDurationTp50'
                        },
                        {
                            title: 'Android',
                            align: 'center',
                            key: 'androidFailDurationTp50'
                        }
                    ]
                }
            ]
        },
      ],
      failReasonColumns:[
        {
            title: '失败原因',
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style:{
                    float:'left'
                  }
                }, params.row.main_err_msg + ": "+params.row.sub_err_msg)
              ])
            }
        },
        {
            title: '失败次数',
            align: 'center',
            width: 100,
            key: 'count'
        },
        {
            title: '失败占比',
            align: 'center',
            width: 100,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  props: {
                  }
                }, ((params.row.count/params.row.totalFail)*100).toFixed(2) + "%")
              ])
            }
        }
      ],
    }
  },
  methods: {
      //是否展示辅助指标&调用接口；若不展示，则无需请求接口
      isShow(isShow,channel,timeRange){
        if(!isShow){
            return
        }
        console.log(timeRange)
        this.getShortlinkAssistInfo(channel,timeRange)        //获取辅助指标数据
        console.log(timeRange)
      },
      getShortlinkAssistInfo(channel,timeRange){
        this.isRequesting = true        //设置展示loading
        this.isAssistShow = false       //设置不展示辅助指标模块
        this.$axios({
              params:{
                  startTime: timeRange[0],
                  endTime: timeRange[1],
                  channel: channel
              },
              method:"get",
              url:this.env.url+"autoTestAnalytic/runInfo/shortLinkAssist",
          }).then((res) => {
              this.isRequesting = false       //设置不展示loading
              this.isAssistShow = true        //设置展示辅助指标模块
              let message = res.data;
              this.shortLinkRunSituationAssist = message;        //存储辅助指标数据
              this.iosFailReasonInfo = message.iosFailReasonInfo        //存储iOS失败原因数据
              this.androidFailReasonInfo = message.androidFailReasonInfo       //存储Android失败原因数据
              this.assistInfoTableData=[]       //清空效率数据
              this.assistInfoTableData.push(message)       //存储效率数据
              this.resultInfo = message.resultInfo       //存储折线图数据
              this.showDetailInfo(message.resultInfo,message.channelName)
          }).catch(function (error) {
        })
      },
      showFailReasonInfo(iosFailReasonInfo,androidFailReasonInfo){
        this.iosFailReasonInfo = iosFailReasonInfo
        this.androidFailReasonInfo = androidFailReasonInfo
      },
      showDetailInfo(resultInfo,channelName){
          this.lineChart('iOS-resultInfo',channelName+'-Case失败率',resultInfo,'fail')
          this.lineChart('Android-resultInfo',channelName+'-Case跳过率',resultInfo,'skip')
      },
      lineChart(containerId, title, resultData,platform){
        let iosList = []
        let androidList = []
        let yAxisText = 10
        let yAxisPlotLines = null
        if (platform === 'fail'){
            iosList = resultData.iosFailList
            androidList = resultData.androidFailList
            yAxisText = "非业务原因导致失败率"
            yAxisPlotLines=10
        } else {
            iosList = resultData.iosSkipList
            androidList = resultData.androidSkipList
            yAxisText = "跳过率"
            yAxisPlotLines=null
        }
        Highcharts.setOptions({ global: { useUTC: false } })
        Highcharts.chart(containerId, {
        chart: {
            type: 'spline',
            spacingRight:30
        },
        title: {
            text: title,
            margin: 20,
            style: {
            color: '#808695',
            fontWeight: 'bold',
            fontSize: '16px'
            }
        },
        xAxis: {
            type: 'datetime',
            tickPixelInterval: 10,
            title: {
              text: null
            },
            dateTimeLabelFormats: { 
                day: '%m-%d',
            }
        },
        colors: ['#6CF', '#84bf96', '#06C', '#036', '#000'],
        yAxis: {
            plotLines: [{ 
              color: 'red',
              width: 1,
              value: yAxisPlotLines,
              dashStyle: 'ShortDash',
              label: {
                text: '10%',
                align: 'right',
                x: 30,
                y: 4,
                verticalAlign: 'middle',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                }
              }
            }],
            title: {
            text: yAxisText+'（%）'
            },
            min: 0,
            max: 100
        },
        tooltip: {
            headerFormat: '<b>{series.name}</b><br>',
            pointFormat: '{point.x:%Y-%m-%d}: {point.y:.2f}%'
        },
        plotOptions: {
            spline: {
            marker: {
                enabled: true
            }
            }
        },
        credits: {  
            enabled: false     //不显示LOGO 
        },
        series: [{
            name: 'iOS端',
            data: iosList
        },{
            name: 'Android端',
            data: androidList
        }],
        lang: {
            noData: "",
        },
        })
    }
  }
}
</script>

<style scoped>
.chart{
  width:500px;
  height:350px;
  margin:0 auto;
  
}
.title{
  color:#808695;
  font-size:16px;
  font-weight:bold;
  text-align: center;
}
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
.time-line-title{
  font-weight: bolder;
  float:left;
  font-size: 15px;
}
</style>