<template>
  <div>
    <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
      <Spin>
        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
      </Spin>
    </div>
    <p
      v-show="isDetailShow"
      style="margin-bottom:10px;margin-top:30px;margin-left:10px;font-size:20px"
      class="title"
    >
      持续集成运行情况信息
      <Tooltip max-width="500" placement="right-start">
        <Icon type="ios-help-circle-outline" />
        <div slot="content">
          <p>名词释义:</p>
          <p>任务分发成功率: 统计持续交付链路触发自动化任务正确率</p>
          <p>任务执行总时长: 计算从持续交付链路触发自动化开始到所有自动化任务运行结束时间</p>
        </div>
      </Tooltip>
    </p>
    <p
      v-show="isTextShow"
      style="margin-bottom:10px;margin-top:30px;margin-left:10px;color:#FF9900"
      class="title"
    >{{tipText}}</p>
    <v-table
      v-show="isDetailShow"
      :columns="runEffectColumns"
      :table-data="tableData"
      even-bg-color="#f8f8f9"
      border-color="#f8f8f9"
      :cell-merge="cellMerge"
      :column-cell-class-name="columnCellClass"
      @on-custom-comp="customCompFunc"
    ></v-table>
    <p
      v-show="isDetailShow"
      style="margin-bottom:10px;margin-top:30px;margin-left:10px;font-size:20px"
      class="title"
    >
      持续集成错误触发Bug数:
      <a
        style="margin-bottom:10px;margin-top:30px;margin-left:10px;font-size:20px"
        href="https://km.sankuai.com/page/185406389"
      >{{bugNumber}}</a>
    </p>
    <div v-show="isAssistShow">
      <p
        style="margin-bottom:3px;margin-top:30px;margin-left:10px;font-size:20px"
        class="title"
      >辅助指标</p>
      <hr
        style="FILTER: alpha(opacity=100,finishopacity=0,style=3)"
        width="100%"
        color="#987cb9"
        size="5"
      />
      <AutotestCcdSuccessRateTable ref="AutotestCcdSuccessRateTable"></AutotestCcdSuccessRateTable>
      <AutotestCcdRunTimeTable ref="AutotestCcdRunTimeTable"></AutotestCcdRunTimeTable>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import Highcharts from "highcharts";
import HighchartsNoData from "highcharts/modules/no-data-to-display";
HighchartsNoData(Highcharts);
import DataTable from "../baseComponents/DataTable";
import Vue from "vue";
import "vue-easytable/libs/themes-base/index.css";
import { VTable, VPagination } from "vue-easytable";
import { now } from "moment";
import AutotestCcdRunTimeTable from "./AutotestCcdRunTimeTable";
import AutotestCcdSuccessRateTable from "./AutotestCcdSuccessRateTable";

export default {
  name: "AutotestCCDRunEffectTable",
  components: {
    DataTable,
    VTable,
    VPagination,
    AutotestCcdSuccessRateTable,
    AutotestCcdRunTimeTable
  },
  data() {
    return {
      tableData: [],
      isRequesting: false, //是否展示loading
      isDetailShow: false, //是否展示运行效果表格
      isAssistShow: false, // 是否展示辅助指标
      isTextShow: true, // 是否展示提示文案
      tipText: "请选择业务方向和触发节点", //提示文案
      bugNumber: 0, //错误触发bug数
      startTime: "",
      endTime: "",
      productList: [], //自动化运行平台
      allParam: [],
      runEffectColumns: [
        {
          field: "businessName",
          title: "业务方向",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class",

          isFrozen: true
        },
        {
          field: "nodeName",
          title: "触发节点",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class"
        },
        {
          field: "successRate",
          title: "任务分发成功率",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class",

          formatter: function(rowData, rowIndex, pagingIndex, field) {
            return rowData.successRate !== "100.00%"
              ? '<span style="color:red;">' + rowData.successRate + "</span>"
              : rowData.successRate;
          }
        },
        {
          field: "TP90",
          title: "任务执行总时长TP90",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class"
        },
        {
          field: "TP50",
          title: "任务执行总时长TP50",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class"
        },
        {
          field: "custome-adv",
          title: "辅助指标",
          width: 160,
          titleAlign: "center",
          columnAlign: "center",
          titleCellClassName: "title-cell-class",

          componentName: "table-operation"
        },
        {
          field: "null",
          title: "",
          width: 1,
          titleAlign: "center",
          columnAlign: "center"
        }
      ]
    };
  },
  mounted() {
    this.getProduct();
    console.log(this.productList);
  },
  methods: {
    // 列样式设置
    columnCellClass(rowIndex, columnName, rowData) {
      // 给业务方向列设置className
      if (columnName === "businessName") {
        return "business-column-cell-class-name";
      } else {
        return "other-column-cell-class-name";
      }
    },
    // 点击查看辅助指标
    customCompFunc(params) {
      console.log(params);
      if (params.type === "assist") {
        this.isAssistShow = true;
        this.$refs.AutotestCcdSuccessRateTable.showCcdSuccessRateInfo(
          this.startTime,
          this.endTime
        );
        this.$refs.AutotestCcdRunTimeTable.showCcdRunTimeInfo(
          this.allParam,
          this.productList
        );
      }
    },
    // 获取自动化运行平台筛选项
    getProduct() {
      this.$axios({
        method: "get",
        url: this.env.url + "/page/getProductList"
      })
        .then(res => {
          let message = res.data;
          this.productList = message;
          console.log(this.productList);
          // console.log(this.productList);
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 合并单元格
    cellMerge(rowIndex, rowData, field) {
      if (field === "custome-adv") {
        if (rowIndex > 1) {
          return {
            rowSpan: 0,
            colSpan: 0
          };
        } else {
          return {
            rowSpan: this.tableData.length,
            colSpan: 1,
            componentName: "table-operation"
          };
        }
      }
      if (field === "businessName") {
        if (
          rowIndex > 0 &&
          this.tableData[rowIndex].businessName ===
            this.tableData[rowIndex - 1].businessName
        ) {
          return {
            rowSpan: 0,
            colSpan: 0,
            content: rowData.businessName
          };
        } else {
          let rows = 1;
          // 反之 查询相同的内容有多少行 进行合并
          for (let i = rowIndex + 1; i < this.tableData.length; i++) {
            if (
              this.tableData[i].businessName ===
              this.tableData[rowIndex].businessName
            ) {
              rows++;
            }
          }
          // 返回相同内容的行数
          return {
            rowSpan: rows,
            colSpan: 1,
            content: rowData.businessName
          };
        }
      }
    },
    //展示运行效果表格
    showCcdRunEffectInfo(timeRange, businessId, nodeId) {
      this.isDetailShow = false;
      this.isAssistShow = false;
      this.isTextShow = true;
      this.getCcdRunEffectTable(timeRange, businessId, nodeId);
    },
    //获取自动化运行效果数据
    getCcdRunEffectTable(timeRange, businessIdList, nodeIdList) {
      if ((businessIdList.length === 0) | (nodeIdList.length === 0)) {
        this.tableData = [];
        this.isRequesting = false; //设置不展示loading
        this.isDetailShow = false; //设置不展示运行效果表格
        this.isTextShow = true;
        this.tipText = "请选择业务方向和触发节点"; //展示提示文案
      } else {
        (this.isDetailShow = false), //设置不展示运行效果表格
          (this.isRequesting = true), //设置展示loading样式
          (this.tableData = []),
          (this.isTextShow = false); //清空运行效果数据
        let startTimeStr = timeRange[0] + "T00:00:01";
        let endTimeStr = timeRange[1] + "T23:59:59";
        let startTimeStamp = new Date(startTimeStr);
        let endTimeStamp = new Date(endTimeStr);
        this.startTime = startTimeStamp.getTime();
        this.endTime = endTimeStamp.getTime();
        this.allParam = {
          businessId: businessIdList,
          nodeId: nodeIdList,
          startTime: this.startTime,
          endTime: this.endTime
        };
        this.$axios({
          data: this.allParam,
          method: "POST",
          url: this.env.url + "ccd/runEffects"
        }).then(res => {
          let runEffect = res.data;
          console.log(res.data);
          this.tableData = []; //清空运行效果数据
          if (runEffect.code === 200) {
            this.tableData = runEffect.data; //存储执行情况数据
            console.log(this.tableData);
            this.isTextShow = false; //设置不展示提示文案
            this.isRequesting = false; //设置不展示loading
            this.isDetailShow = true; //设置展示运行效果表格
          } else if (runEffect.code === 400) {
            this.tableData = [];
            this.isTextShow = true; //设置展示提示文案
            this.isRequesting = false; //设置不展示loading
            this.isDetailShow = true; //设置不展示运行效果表格
            this.tipText = runEffect.message;
          }
        });
      }
    }
  }
};
// 自定义列组件
Vue.component("table-operation", {
  template: `<span>
        <a href="" @click.stop.prevent="assistShow(rowData,index)">点击查看辅助指标</a>
        </span>`,
  props: {
    rowData: {
      type: Object
    },
    field: {
      type: String
    },
    index: {
      type: Number
    }
  },
  methods: {
    assistShow() {
      // 参数根据业务场景随意构造
      let params = { type: "assist", index: this.index, rowData: this.rowData };
      this.$emit("on-custom-comp", params);
    }
  }
});
</script>
<style >
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.title {
  color: #808695;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}
.chart {
  width: 450px;
  height: 350px;
  margin: 0 auto;
}
.title-cell-class {
  background-color: #f8f8f9;
  color: #515a6e;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  font-weight: bold;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.other-column-cell-class-name {
  color: #515a6e;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
.business-column-cell-class-name {
  background-color: #ffffff;
  color: #515a6e;
  font-weight: bold;
  font-size: 12px;
  box-sizing: border-box;
  width: inherit;
  height: 100%;
  max-width: 100%;
}
</style>