<template>
    <div>
         <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
        </div>
        <Table :cell-class-name = cellClassName v-show="isDetailShow" stripe border :data="this.autoTestRunSituation" :columns="runSituationColumns" ></Table>
    </div>
</template>
 
<script>

/* eslint-disable */
import Highcharts, { color } from 'highcharts';
import HighchartsNoData from 'highcharts/modules/no-data-to-display'
HighchartsNoData(Highcharts)
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestRunSituationTable',
  components: {DataTable},
  props: [],
   data () {
    return {
      isDetailShow: true,        //是否展示执行情况大表格
      autoTestRunSituation: [],       //执行情况数据
      isRequesting:false,        //是否展示loading
      timeRange:[],        //选中的时间区间
      runSituationColumns:[
        {
            title: '方向',
            align: 'center',
            width: 85,
            key: 'channelName',
            renderHeader: (h, params) => {
              var text = '方向';             
              return h('div', {
                    domProps:{
                      innerHTML: text
                    },
                    style:{
                      color:'#333333'
                    }
                  })},
            render:(h, params)=>{
              return h('div',[
                h('div',{
                  style:{
                    color:'#333333'
                  }
                },params.row.channelName)
              ])
            },
            fixed: 'left'
        },{
            title: '短链路',
            renderHeader: (h, params) => {
              var text = '短链路';
              return h('div', {
                domProps:{
                  innerHTML: text
                },
                style:{
                  fontSize: '16px',
                  fontWeight: 'bolder',
                  color:'#333333'
                }
              })
            },
            align: 'center',
            children: [
              {
                title: '运行稳定性',
                align: 'center',
                renderHeader: (h, params) => {
                  var text = '运行稳定性';
                  return h('div', {
                    domProps:{
                      innerHTML: text
                    },
                    style:{
                      color:'#333333'
                    }
                  })
                },
                children: [
                  {
                    title: '',
                    renderHeader: (h, params) => {
                      var text = 'Job<br/>成功率';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    align: 'center',
                    render:(h, params)=>{
                      var colors;
                      if(parseFloat(params.row.passRate)<parseFloat("90%")){
                        colors = 'red'
                      } else{
                        colors='#333333'
                      }
                      return h('div', [
                        h('span', {
                          style: {
                            color: colors
                          }
                        }, params.row.jobPassRate,
                        params.row.jobPassRate)
                      ])
                    }
                  },
                  {
                    title: '',
                    renderHeader: (h, params) => {
                      var text = 'Case<br/>成功率';
                      return h('div', {
                      domProps:{
                        innerHTML: text
                      },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    align: 'center',
                    render: (h, params) => {
                      var colors;
                      if(parseFloat(params.row.passRate)<parseFloat("90%")){
                        colors = 'red'
                      } else{
                        colors='#333333'
                      }
                      return h('div', [
                        h('span', {
                          style: {
                            color: colors
                          }
                        }, params.row.passRate,
                        params.row.passRate)
                      ])
                    },
                  }
                ]
              },
              {
                title: '运行效率（TP90）',
                align: 'center',
                renderHeader: (h, params) => {
                  var text = '运行效率（TP90）';
                  return h('div', {
                    domProps:{
                      innerHTML: text
                    },
                    style:{
                      color:'#333333'
                    }
                  })
                },
                children: [
                  {
                    title: '',
                    renderHeader: (h, params) => {
                      var text = 'Job<br/>运行耗时';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    render:(h, params)=>{
                      return h('div',[
                        h('div',{
                          style:{
                            color:'#333333'
                          }
                        },params.row.allJobDurationTp90)
                      ])
                    },
                    align: 'center',                    
                    // key:'allJobDurationTp90',
                  },
                  {
                    title: 'Case耗时',
                    renderHeader: (h, params) => {
                      var text = 'Case<br/>运行耗时';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    render:(h, params)=>{
                      return h('div',[
                        h('div',{
                          style:{
                            color:'#333333'
                          }
                        },params.row.allCaseDurationTp90)
                      ])
                    },
                    align: 'center',
                    key:'allCaseDurationTp90',
                  },
                ]
              }
            ]
        },
        {
            title: '',
            align: 'center',
            width: 3,
        },
        {
            title: '视觉测试',
            align: 'center',
            renderHeader: (h, params) => {
              var text = '视觉测试';
              return h('div', {
              domProps:{
                innerHTML: text
              },
              style:{
                fontSize: '16px',
                fontWeight: 'bolder',
                color:'#333333'
              }
              })
            },
            children: [
              {
                title: '运行稳定性',
                align: 'center',
                renderHeader: (h, params) => {
                  var text = '运行稳定性';
                  return h('div', {
                    domProps:{
                      innerHTML: text
                    },
                    style:{
                      color:'#333333'
                    }
                  })
                },
                children: [
                  {
                    title: '',
                    renderHeader: (h, params) => {
                      var text = 'Job<br/>成功率';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    align: 'center',
                    render:(h, params)=>{
                      var colors;
                      if(parseFloat(params.row.visionJobSuccessRate)<parseFloat("100%")){
                        colors = 'red'
                      } else{
                        colors='#333333'
                      }
                      return h('div', [
                        h('span', {
                          style: {
                            color: colors
                          }
                        }, params.row.visionJobSuccessRate,
                        params.row.visionJobSuccessRate)
                      ])
                    },
                  },
                  {
                    title: '',
                    renderHeader: (h, params) => {
                      var text = '页面<br/>一致率';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    align: 'center',
                    render: (h, params) => {
                      var colors;
                      if(parseFloat(params.row.visionPicPassRate)<parseFloat("70%")){
                        colors = 'red'
                      }else{
                        colors='#333333'
                      }
                      return h('div', [
                        h('span', {
                          style: {
                            color: colors
                          }
                        }, params.row.visionPicPassRate,
                        params.row.visionPicPassRate)
                      ])
                    },
                  }
                ]
              },
              {
                title: '运行效率（TP90）',
                align: 'center',
                renderHeader: (h, params) => {
                  var text = '运行效率（TP90）';
                  return h('div', {
                    domProps:{
                      innerHTML: text
                    },
                    style:{
                      color:'#333333'
                    }
                  })
                },
                children: [
                  {
                    title: 'Job运行耗时',
                    align: 'center',
                    renderHeader: (h, params) => {
                      var text = 'Job运行耗时';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    children:[
                      {
                        title: 'iOS',
                        align: 'center',
                        renderHeader: (h, params) => {
                          var text = 'iOS';
                          return h('div', {
                            domProps:{
                              innerHTML: text
                            },
                            style:{
                              color:'#333333'
                            }
                          })
                        },
                        render:(h, params)=>{
                          return h('div',[
                            h('div',{
                              style:{
                                color:'#333333'
                              }
                            },params.row.visionIosJobDurationTp90)
                          ])
                        },
                        key:'visionIosJobDurationTp90'
                      },
                      {
                        title: 'Android',
                        renderHeader: (h, params) => {
                          var text = 'Android';
                          return h('div', {
                            domProps:{
                              innerHTML: text
                            },
                            style:{
                              color:'#333333'
                            }
                          })
                        },
                        align: 'center',
                        render:(h, params)=>{
                          return h('div',[
                            h('div',{
                              style:{
                                color:'#333333'
                              }
                            },params.row.visionAndJobDurationTp90)
                          ])
                        },
                        key:'visionAndJobDurationTp90'
                      }
                    ]  
                  },
                  {
                    title: 'Case运行耗时',
                    renderHeader: (h, params) => {
                      var text = 'Case运行耗时';
                      return h('div', {
                        domProps:{
                          innerHTML: text
                        },
                        style:{
                          color:'#333333'
                        }
                      })
                    },
                    align: 'center',
                    children:[
                      {
                        title: 'iOS',
                        renderHeader: (h, params) => {
                          var text = 'iOS';
                          return h('div', {
                            domProps:{
                              innerHTML: text
                            },
                            style:{
                              color:'#333333'
                            }
                          })
                        },
                        align: 'center',
                        render:(h, params)=>{
                          return h('div',[
                            h('div',{
                              style:{
                                color:'#333333'
                              }
                            },params.row.visionIosPicDurationTP90)
                          ])
                        },
                        key:'visionIosPicDurationTP90'
                      },
                      {
                        title: 'Android',
                        renderHeader: (h, params) => {
                          var text = 'Android';
                          return h('div', {
                            domProps:{
                              innerHTML: text
                            },
                            style:{
                              color:'#333333'
                            }
                          })
                        },
                        render:(h, params)=>{
                          return h('div',[
                            h('div',{
                              style:{
                                color:'#333333'
                              }
                            },params.row.visionAndPicDurationTP90)
                          ])
                        },
                        align: 'center',
                        key:'visionAndPicDurationTP90'
                      }
                    ]    
                  }
                ]
              }
            ]
        }
      ]
    }
  },
  methods: {
      //展示执行情况表格
      showRunsituationInfo(timeRange,channel){
        this.getAutoTestRunSituation(timeRange,channel)
        console.log("showRunsituationInfo");
        this.timeRange = timeRange
      },
      
      //获取自动化执行情况数据
      getAutoTestRunSituation(timeRange,channel){
        this.isDetailShow = false  //设置暂不展示执行情况表格
        this.shortLinkRunSituation = []  //清空执行情况数据
        this.isRequesting=true, //设置展示loading样式
        this.timeRange = timeRange
        //请求自动化执行情况数据
        var value = "";
        //多个channel情况下通过逗号拼接，请求后端
        if(channel.length > 0){
          value = channel.join(",");
        }
        console.log(value)
        this.$axios({
            params:{
                startTime: timeRange[0],
                endTime: timeRange[1],
                channel: value
            },
            method:"get",
            url:this.env.url+"autoTestAnalytic/runInfo/runSituation",
        }).then((res) => {
            let message = res.data;
            this.autoTestRunSituation = message;  //存储执行情况数据
            this.isRequesting=false,      //设置不展示loading  
            this.isDetailShow = true      //展示执行情况表格
        }).catch(function (error) {
      })
    },
    cellClassName() {
        return 'emptyClass';　　// class名称
      },  
  }
}
</script>

<style scoped>
.chart{
  width:450px;
  height:350px;
  margin:0 auto;
}
.title{
  color:#808695;
  font-size:16px;
  font-weight:bold;
  text-align:left;
}
.ivu-table-cell td{
  color: #fff;
}
.ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
  .emptyClass td{
    color:green
  }
</style>