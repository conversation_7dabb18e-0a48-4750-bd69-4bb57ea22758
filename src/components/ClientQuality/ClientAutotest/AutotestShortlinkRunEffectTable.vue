<template>
    <div :style='{ background: "#ffff"}'>
      <Table border v-show="isDetailShow" :columns='newColumns' :data='showData' width='100px' size='small' :span-method='cellMerge' @on-filter-change='columnFilterChange'>
      </Table>
      <div style=' overflow: hidden' v-if='tableData.length >= 20'>
        <div style='float: right; height: "90px"'>
          <Page :total='tableData.length' :current='showIndex' :page-size='showSize' :page-size-opts='pageSizeOpts' show-sizer show-total @on-page-size-change='changePageSize' @on-change='changePage' />
        </div>
      </div>
      <div class='spin-container' v-if='isRequesting' style='padding-top: 50px'>
        <Spin>
          <Icon type='ios-loading' size='18' class='spin-icon-load'></Icon>
        </Spin>
      </div>
    </div>
  </template>
  
  <script>
  import ConfigSelect from '../baseComponents/ConfigSelect'
  import DataTable from '../baseComponents/DataTable'
  
  export default {
    name: 'CompatibilityMeasureInfo',
    components: { ConfigSelect, DataTable },
    data() {
      return {
        isDetailShow: true, // 是否展示执行情况大表格
        showDataExplain: false,
        nowDate: [], // 用户选择时间
        tableData: [], // 存放table待渲染数据
        triggerCount: {},
        productFilter: [], // 存放用户选择的product
        nodeFilter: [], // 存放用户选择的node
        startTime: '',
        endTime: '',
        isRequesting: false,
        groupId: '', // 用户选择的groupId
        unitId: '', // 用户选择的unitId
        businessId: '', // 用户选择的businessId
        testSlot: [], // 触发项
        mergerItems: [0, 1, 2, 3], // 待合并的行
        mergerRosRecord: {}, // 待合并列内容
        propObj: {}, // 存储更新后表头数据
        showIndex: 1, // 分页参数
        showSize: 50, // 分页参数
        pageSizeOpts: [10, 15, 20, 30, 50, 100]
      }
    },
    mounted() {
      this.$store.dispatch('getProductList')
      this.$store.dispatch('getNodeList')
      let endTime = this.formateTime(new Date())
      let startTime = this.formateTime(
        new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
      )
      this.nowDate = [startTime, endTime]
    },
    computed: {
      newColumns() {
        let beforeColumns = [
          {
            title: '事业群',
            key: 'bgLabel',
            align: 'center'
          },
          {
            title: '事业部',
            key: 'buLabel',
            align: 'center'
          },
          {
            title: '业务方向',
            key: 'businessLabel',
            align: 'center'
          },
          {
            title: '研发测试节点',
            key: 'nodeLabel',
            align: 'center'
          },
          {
            title: '产品类型',
            key: 'productLabel',
            align: 'center'
          },
          {
            title: '运行稳定性',
            key: 'operationalStability',
            align: 'center',
            children: [
              {
                title: 'Case成功率',
                key: 'caseSuccessRate',
                align: 'center',
                render: (h, params) => {
                  const rate = params.row.caseSuccessRate
                  const rateNum = parseFloat(rate)
                  if (rateNum < 90) {
                    return h('span', { style: { color: 'red' } }, rate)
                  } else {
                    return h('span', rate)
                  }
                }
              },
              {
                title: 'Case跳过率',
                key: 'caseSkipRate',
                align: 'center',
                render: (h, params) => {
                  const rate = params.row.caseSkipRate
                  const rateNum = parseFloat(rate)
                  if (rateNum > 10) {
                    return h('span', { style: { color: 'red' } }, rate)
                  } else {
                    return h('span', rate)
                  }
                }
              }
            ]
          },
          {
            title: '运行效率(TP90)',
            key: 'operationalEfficiency',
            children: [
              {
                title: 'Job运⾏耗时',
                key: 'jobDuration',
                align: 'center'
              },
              {
                title: 'Case运⾏耗时',
                key: 'caseDuration',
                align: 'center'
              }
            ],
            align: 'center'
          }
        ]
        return beforeColumns
      },
      showData: function () {
        let showData = this.tableData
        if (this.showSize < this.tableData.length) {
          showData = this.tableData.slice(
            (this.showIndex - 1) * this.showSize,
            (this.showIndex - 1) * this.showSize + this.showSize
          )
        }
        return showData
      },
      productList() {
        return this.$store.state.clientQuality.productList
      },
      allNode() {
        return this.$store.state.clientQuality.nodeList
      }
    },
    methods: {
      changeTimeRange(value) {
        this.nowDate = value
      },
      getTableData(
        nowDate,
        unitId,
        groupId,
        businessId,
        productFilter,
        nodeFilter
      ) {
        this.isDetailShow = false // 设置暂不展示执行情况表格
        this.isRequesting = true // 设置展示loading样式
        this.tableData = []
        this.getColkey(this.newColumns)
        this.startTime = this.formateTime(new Date(Date.parse(nowDate[0])))
        this.endTime = this.formateTime(
          new Date(Date.parse(nowDate[1]) + 24 * 60 * 60 * 1000)
        )
        this.$axios({
          data: {
            nodeIds: nodeFilter,
            productIds: productFilter,
            startTime: this.startTime,
            endTime: this.endTime,
            businessId: businessId || undefined,
            buId: unitId || undefined,
            bgId: groupId || undefined
          },
          method: 'POST',
          url: this.env.url + 'autoTestAnalytic/shortLink/shortLinkMeasureInfo'
        })
          .then((res) => {
            console.log(res)
            let node = ''
            let measureCaseSuccess = ''
            let measureSkipPassRate = ''
            let measureJobDuration = ''
            let measurecaseDuration = ''
            this.isRequesting = false // 设置不展示loading
            let triggerResult = res.data || {}
            this.tableData = [] // 清空运行效果数据
            this.tableData = triggerResult.shortLinkMeasureInfo || [] // 存储执行情况数

            for (let i = 0; i < this.tableData.length; i++) {
              node =
                this.tableData[i].publish_name +
                '-' +
                this.tableData[i].integration_name
              this.tableData[i].nodeLabel = node
              measureCaseSuccess =
                this.tableData[i].measureInfo['caseSuccessRate']
              this.tableData[i].caseSuccessRate = measureCaseSuccess
              measureSkipPassRate = this.tableData[i].measureInfo['caseSkipRate']
              this.tableData[i].caseSkipRate = measureSkipPassRate
              measureJobDuration = this.tableData[i].measureInfo['jobDuration']
              this.tableData[i].jobDuration = measureJobDuration
              measurecaseDuration = this.tableData[i].measureInfo['caseDuration']
              this.tableData[i].caseDuration = measurecaseDuration
            }
            this.getSpanArr(this.showData, this.mergerItems)
            this.isDetailShow = true // 展示执行情况表格
            this.showIndex = 1
          })
          .catch((err) => {
            console.log(err)
          })
      },
      formateTime: function (time) {
        return (
          time.getFullYear() +
          '-' +
          (time.getMonth() >= 9
            ? time.getMonth() + 1
            : '0' + (time.getMonth() + 1)) +
          '-' +
          (time.getDate() > 9 ? time.getDate() : '0' + time.getDate())
        )
      },
      cellMerge({ row, column, rowIndex, columnIndex }) {
        if (this.mergerItems.includes(columnIndex)) {
          let item = this.mergerRosRecord[columnIndex]
          const _row = item[rowIndex]
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      },
      getSpanArr(data, array) {
        //  循环数据处理
        let mergerRosRecord = {}
        let pos = 0
        for (let n of array) {
          pos = 0
          mergerRosRecord[n] = [] // 数据清空(重新调此方法的时候需要清空上一次的数据)
          for (let i = 0; i < data.length; i++) {
            if (i === 0) {
              mergerRosRecord[n].push(1)
              pos = 0
            } else {
              //  判断当前元素与上一个元素是否相同
              if (
                data[i][this.propObj[n]] === data[i - 1][this.propObj[n]] &&
                data[i][this.propObj[n - 1]] === data[i - 1][this.propObj[n - 1]]
              ) {
                mergerRosRecord[n][pos] += 1
                mergerRosRecord[n].push(0)
              } else {
                mergerRosRecord[n].push(1)
                pos = i
              }
            }
          }
        }
        this.mergerRosRecord = { ...mergerRosRecord }
      },
      // 获取列的key
      getColkey(colData) {
        let index = 0
        let tmpObj = {}
        for (let i of colData) {
          if (!i.children) {
            tmpObj[index] = i.key
            index++
          } else {
            for (let k of i.children) {
              tmpObj[index] = k.key
              index++
            }
          }
        }
        this.propObj = { ...tmpObj }
      },
      getBusinessID(data) {
        this.businessId = data
      },
      getGroupID(data) {
        this.groupId = data
        this.unitId = ''
        this.businessId = ''
      },
      getUnitID(data) {
        this.unitId = data
        this.businessId = ''
      },
      changePage(index) {
        this.showIndex = index
        this.getSpanArr(this.showData, this.mergerItems)
      },
      changePageSize(size) {
        this.showSize = size
        this.getSpanArr(this.showData, this.mergerItems)
      },
      columnFilterChange() {
        this.changePageSize(100)
        this.changePage(1)
      }
    }
  }
  </script>
  <style scoped>
  .spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
  .select-location {
    text-align: center;
    margin-top: -5px;
  }
  html {
    height: 100%;
    display: table;
  }
  body {
    display: table-cell;
    height: 100%;
    margin: 100px;
  }
  .ivu-table-header table {
    width: 100% !important;
  }
  </style>