<template>
    <div>
        <p v-show = isAssistShow style="margin-bottom:30px;margin-top:50px;margin-left:10px" class="title">{{this.channelName}}各页面覆盖情况</p>
        <Row v-show = isAssistShow>
            <Col :md="8" style="margin-left:10px">
              <Table v-show = isAssistShow  border :data="this.tableData" :columns="runEffectColumns"></Table>
            </Col>
            <Col :md="8" style="margin-left:30px">
              <Table v-show = tableData2Show  border :data="this.tableData2" :columns="runEffectColumns"></Table>            
            </Col>
        </Row>
    </div>
</template>

<script>

/* eslint-disable */
import DataTable from "../baseComponents/DataTable";
export default {
  name: 'AutotestRunEffectTableAssist',
  components: {DataTable},
  props: [],
   data () {
    return {
      tableData:[],
      tableData2:[],
      tableData2Show:false,
      channelName:'',
      isAssistShow:false,
      runEffectColumns:[
        {
            title: '页面',
            align: 'center',
            key: 'pageName',
            width: 150
        }, 
        {
            title: '接口覆盖',
            align: 'center',
            render:(h, params)=>{
                var text = params.row.apiCoverageRate
                var textInfo;
                if(params.row.apiCoverageRate == "--"){
                textInfo = ""
                } else{
                textInfo = "  (" + params.row.apiCoverageNum + "/" + params.row.apiBaseNum + ")"
                }
                return h('div', [
                h('span', {
                    domProps: {
                    innerHTML: text
                    }  
                }),
                h('span', {
                    domProps: {
                    innerHTML: textInfo
                    } ,
                    style: {
                    color: 'gray',
                    fontSize: '10px'
                    }  
                })
                ])
            },
        }
      ]
    }
  },
  methods:{
    isShow(isShow,channel,coverageData,channelName){
        this.isAssistShow = false
        this.tableData2Show = false
        if(!isShow){
            return
        }
        this.channelName=channelName
        if(!(coverageData.hasOwnProperty(channel))){
            console.log(coverageData.hasOwnProperty(channel))
            this.isAssistShow=isShow
            this.tableData=[]
            return
        }
        if(!coverageData[channel].page.hasOwnProperty("allAutoTest")){
            console.log(coverageData[channel].page.hasOwnProperty("allAutoTest"))
            this.isAssistShow=isShow
            this.tableData=[]
            return
        }
        let tableData=coverageData[channel].page.allAutoTest
        if (tableData.length>9){
            let index = (tableData.length)%2 == 0? (tableData.length/2):(Math.floor(tableData.length/2)+1)
            this.tableData=tableData.slice(0,index)
            this.tableData2=tableData.slice(index)
            this.tableData2Show = true
        }
        else{
            this.tableData2Show = false
            this.tableData=tableData
        }
        this.isAssistShow=isShow
        console.log(this.tableData)
    }
  }
}
</script>

<style scoped>
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
.title{
  color:#808695;
  font-size:16px;
  font-weight:bold;
  text-align:left;
}
</style>