<template>
    <div>
      <Breadcrumb style="text-align: left">
        <BreadcrumbItem to="/microscope/jobList"><Icon type="ios-list" size="20"/> Job列表</BreadcrumbItem>
        <BreadcrumbItem><Icon type="ios-paper-outline" size="20"/> 任务报告</BreadcrumbItem>
      </Breadcrumb>
      <Affix :offset-top="12">
        <RadioGroup v-model="imageShowOptions.showMode" type="button" size="small" @on-change="changeShowMode">
          <Radio label="task" title="按照设备任务为单位展示"><Icon type="md-list" /> Task</Radio>
          <Radio label="scheme" title="按照URLScheme为单位展示"><Icon type="md-image" /> Scheme</Radio>
        </RadioGroup>
         <RadioGroup v-model="diffImageShow" type="button" size="small" @on-change="changeImageShow">
          <Radio label="diff" title="展示对比图"><Icon type="ios-images" /> 对比图</Radio>
          <Radio label="run" title="展示执行图"><Icon type="ios-image" /> 执行图</Radio>
        </RadioGroup>
        <ButtonGroup>
          <Button size="small" @click="changeImageSize('s')" title="图片缩小">
            <Icon type="ios-arrow-back"></Icon>
            S
          </Button>
          <Button size="small"  @click="changeImageSize('l')" title="图片放大">
            L
            <Icon type="ios-arrow-forward"></Icon>
          </Button>
        </ButtonGroup>
        <Button type="info" size="small"  @click="openLink('/compatibilityUserConfig','site')" title="配置任务">
          <Icon type="ios-settings" />
        </Button>
        <Button type="info" size="small" :disabled="!canRerunJob" @click="confirmRerunJob()" title="重跑任务">
          <Icon type="md-refresh" />
        </Button>
    <RadioGroup v-model="viewOrFilter" type="button" size="small" @on-change="selectViewed">
          <Radio label="true" title="全部case展示"><Icon type="md-eye" /> 显示全部</Radio>
          <Radio label="false" title="暂未标记展示"><Icon type="md-time" /> 暂未标记</Radio>
        </RadioGroup>
      <el-tooltip content="已标记的问题case数" placement="top">
        <Progress :stroke-width="6" :percent="ShowProgress()"  style="width: 100px; white-space: nowrap; margin-left: 2px; ">
          <template v-slot:default="scope">
          <div class="progress-value">{{filterCount}}/{{ getDenominator() }}</div>
          </template>
        </Progress>
      </el-tooltip>
      </Affix>
      <div class="spin-container" v-if="isRequesting">
        <Spin>
          <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
        </Spin>
      </div>
      <template v-if="isRequesting === false">
        <Tabs value="detail" style="padding-top: 10px;overflow: visible;">
          <TabPane label="任务详情" icon="ios-browsers" name="detail">
            <JobCommentModal v-if="jobInfo.id" :showJobCommentModal="showJobCommentModal" :jobId="jobInfo.id" :business="jobInfo.business" @cancelModal="showJobCommentModal=false"></JobCommentModal>
            <div style="text-align: left;">
              <div v-for="job in jobInfoList" :key="job.id" name="job.id" style="padding-top:5px;" :class="{'job-info-card-focus': focusJobId===job.id}">
                  <img v-if="job.plat === 'Android'" src="/static/img/android.png" style="width: 25px; height: 25px" />
                  <img v-if="job.plat === 'Harmony'" src="/static/img/harmony.png" style="width: 25px; height: 25px" />
                  <img v-else-if="job.plat === 'Web'" src="/static/img/web.png" style="width: 25px; height: 25px" />
                  <img v-else src="/static/img/ios.png" style="width: 25px; height: 25px" />
                  <Tag color="blue" title="任务类型"><Icon type="md-flag" /> {{filterTestItem(job.jobType)}}</Tag>   
                  <Tag color="gold" title="App-业务线"><Icon type="md-briefcase" /> {{job.category}}-{{job.business}}</Tag>
                  <Tag color="cyan" title="任务来源" >
                    <Icon type="md-return-right" /> {{ transformToSourceName(job.sourceType) + '-' + job.triggerNode }}
                    <a v-if="job.sourceType !== 'Microscope'" target="_blank" :href="job.sourceUrl">
                      <Icon type="ios-link"></Icon> 
                    </a>
                    <a v-else target="_blank" :href="getCustomUrl(job.jobName,job.business)">
                      <Icon type="ios-link"></Icon>
                    </a>
                  </Tag>
                  <Tag v-if="job.category ==='微信'" color="geekblue">
                    <Poptip trigger="hover" title="微信小程序二维码" content="content" placement="bottom-start" transfer @on-popper-show="showMPQRCode(job)">
                      <template v-if="job.qrLoading">
                        <Icon type="ios-loading" />
                        小程序二维码
                        <div slot="content">
                          生成中，需要重新打包生成，可能耗时较久，请稍后...
                        </div>
                      </template>
                      <template v-else>
                        <Icon type="md-qr-scanner" />
                        小程序二维码
                        <div slot="content">
                          <img v-if="job.packageUrl.length > 1 && job.packageUrl.endsWith('.png')" :src="job.packageUrl" style="width: 180px; height: 180px"/>
                          <span v-else-if="job.packageUrl.length === 0">生成中，需要重新打包生成，可能耗时较久，请稍后...</span>
                          <span v-else-if="job.packageUrl.length === 1" >未生成小程序二维码，仅自动触发和正确使用<a href="https://km.sankuai.com/page/1581701432" target="_blank">启动传参插件</a>的任务支持此功能</span>
                          <span v-else>{{job.packageUrl}}</span>
                        </div>
                      </template>
                    </Poptip>
                  </Tag>
                  <Tag v-else-if="job.plat=='Android'" color="geekblue">
                    <Poptip trigger="hover" title="下载Android版本" content="content" placement="bottom-start" transfer>
                        <Icon type="md-qr-scanner" />
                        {{job.appVersion}}
                        <a :href="job.packageUrl"><Icon type="md-download" /></a>
                        <div slot="content">
                          <qriously :value="job.packageUrl" :size="180"/>
                        </div>
                    </Poptip>
                  </Tag>
                  <Tag v-else title="iOS安装包优先跳转至HPX，找不到HPX链接时，直接下载ipa文件" color="geekblue">
                    <a :href="job.hpxTaskUrl" target="_blank">
                      <Icon type="md-return-right" />
                      {{job.appVersion}}
                    </a>
                    <a :href="job.packageUrl">
                      <Icon type="md-download" />
                    </a>
                  </Tag>
                  <Tag v-if="job.hitsTotal !== 0" color="purple" title="CheckerResult" @click.native="jumpToCheckerInfo(job.id, job.business)">
                    <Icon type="ios-warning" />Checker报警详情 <Icon type="ios-link" />
                  </Tag>
                  <Tag style="consor:" color="green" title="Job运营记录" @click.native="showJobComment(job)">
                    <Icon type="ios-bookmark" />Job运营记录<Icon type="md-create" />
                  </Tag>
                  <Button v-if="['compatibility', 'videoPlayer'].includes(job.jobType)" type="info" size="small" :loading="job.jobCreating"  @click="createJob(job)" title="新建Job">
                    重建
                  </Button>
              </div>
            </div>
            <div>
              <Card dis-hover :padding="5" style="margin-top:5px;">
                <Row>
                  <Col span="11">
                    <div class="content-title-container">
                      <Row>
                        <Col span="3" class="content-title">结果标签:</Col>
                      </Row>
                      <Row>
                        <Col span="21" align="left">
                          <template v-for="filterData in statusFilterData">
                            <div :key="'div_' + filterData.key" class="filter-item" style="padding-left: 20px;">
                              <Checkbox v-model="filters.statusFilter[filterData.key].status"
                              :key="'checkbox_' + filterData.key" :indeterminate="filters.statusFilter[filterData.key].indeterminate"
                              @on-change="changeSceneStatusByGroup(filterData.key)" class="title">
                                <span :style="filters.statusFilter[filterData.key].style">{{filterData.label}}({{filterData.sceneCount}})</span>
                              </Checkbox>
                              <Select v-model="filters.statusFilter[filterData.key].filter" multiple :max-tag-count="3" :key="'select_' + filterData.key" @on-change="changeSceneStatus(filterData.key)" size="small">
                                  <Option v-for="item in filterData.list" :value="item.title" :key="item.title">{{ item.title }}({{item.sceneCount}})</Option>
                              </Select>
                            </div>
                          </template>
                        </Col>
                      </Row>
                    </div>
                  </Col>
                  <Col span="13">
                    <div class="content-title-container">
                      <Row class="filter-item">
                        <Col span="3" class="content-title">执行设备:</Col>
                        <Col span="18" align="left">
                          <Select v-model= "filters.phoneFilter"  multiple filterable @on-change="flashShowData()" placeholder="请选择..." size="small" :max-tag-count="6" class="custom-select">
                            <Option v-for="(phone, key) in snPhoneMap" :value="phone.model+phone.version" :key="key">{{phone.model}}-{{phone.version}}</Option>
                          </Select>
                        </Col>
                      </Row>
                      <Row class="filter-item">
                        <Col span="3" class="content-title">场景名称:</Col>
                        <Col span="18" align="left">
                          <Input v-model="filters.sceneNameFilter" placeholder="请输入场景名称关键字..." @on-change="flashShowData()" />
                        </Col>
                      </Row>
                      <Row class="filter-item">
                        <Col span="3" class="content-title">MRN 包:</Col>
                        <Col span="18" align="left">
                          <Select v-model="filters.mrnBundleFilter" multiple filterable @on-query-change="filterMRNBundle" @on-change="getMRNPages" :max-tag-count="3" size="small">
                            <Option v-for="item in mrnBundleList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                          </Select>
                        </Col>
                      </Row>
                      <Row class="filter-item">
                        <Col span="3" class="content-title">misID:</Col>
                        <Col span="18" align="left">
                          <Select v-model="filters.misIdFilter" multiple filterable placeholder="请选择"  @on-change="flashShowData()" :max-tag-count="3" size="small">
                            <Option v-for="user in misIdList" :value="user" :key="user">{{ user }}</Option>
                          </Select>
                        </Col>
                      </Row>
                    </div>
                  </Col>
                </Row>
              </Card>
            </div>

            <div style="height:730px;overflow:auto;">
              <template v-if="imageShowOptions.showMode==='task'">
                <div v-for="(task,index) in taskModeData" :key="task.taskId">
                  <CompatibilityTask :viewAll="viewAll" :filterIds="filterIds" :imageShowOptions="imageShowOptions" :jobs="jobInfoList" :taskIndex="index" :taskInfo="task" :userPermission="userPermission" :snPhoneMap="snPhoneMap" :statusLabel="filters.statusFilter" :taskStatusOptions="taskStatusOptions" @focusJob="focusJob" @blurJob="focusJobId=''" @selectChange="sceneResultSelectChange" @selectAutoFilter="selectAutoFilter"></CompatibilityTask>
                </div>
              </template>
              <template v-else>
                <DynamicScroller style="height:720px;" :items="schemeModeData" direction="vertical" :min-item-size="1000" :buffer="1000">
                  <template v-slot="{ item, index, active }">
                    <DynamicScrollerItem :item="item" :active="active" :data-index="index">
                      <CompatibilityTask :viewAll="viewAll" :filterIds="filterIds" :imageShowOptions="imageShowOptions" :jobs="jobInfoList"  :taskIndex="index" :taskInfo="item" :userPermission="userPermission" :snPhoneMap="snPhoneMap" :statusLabel="filters.statusFilter" :taskStatusOptions="taskStatusOptions" @focusJob="focusJob" @blurJob="focusJobId=''"
                      @selectChange="sceneResultSelectChange" @selectAutoFilter="selectAutoFilter"></CompatibilityTask>
                    </DynamicScrollerItem>
                  </template>
                </DynamicScroller>
                <BackTop :height="100"></BackTop>
              </template>
            </div>
          </TabPane>
          <TabPane label="测试报告" icon="ios-state" name="report" v-if="reportData">
            <ClientTelescope :reportData="reportData"></ClientTelescope>
          </TabPane>
        </Tabs>
      </template>
      <UserConfigModal :show="showUserConfigModal" isNew="true" :jobInfo="userJob" :pageList="toRunPages" v-on:closeModal="showUserConfigModal=false" v-on:toRun="toRunJob" temporaryMode="true"> </UserConfigModal>
      <CompatibilityPhoneContainer :showPhoneContainer="showPhoneContainer" :appPlat="appPlat" :userJob="userJob" v-on:closePhoneInfo="showPhoneContainer=false"></CompatibilityPhoneContainer>
    </div>
</template>

<script>
    /* eslint-disable */
    import ImageView from "./Compatibility/ImageView";
    import CompatibilityTask from "./Compatibility/CompatibilityTask";
    import JobCommentModal from "./Compatibility/JobCommentModal"
    import event from "@/assets/event_bus";
    import ClientTelescope from "./Compatibility/ClientTelescope";
    import CompatibilityPhoneContainer from "./Compatibility/CompatibilityPhoneContainer";
    import UserConfigModal from "./Compatibility/CompatibilityUserConfigModal"
    import { Bus } from '@/global/bus'
    export default {
        name: "ClientMicroscope",
        components: {CompatibilityTask, ImageView, ClientTelescope, JobCommentModal, CompatibilityPhoneContainer, UserConfigModal},
        data(){
          return{
            imageShowOptions:{
              showMode:"task",
              imageShowSize: "236px",
              imageShowPageIDs: 'all'
            },
            isRequesting:false,
            reportData: null,
            viewAll:true,
            viewOrFilter:"true",
            filterIds:null,
            filterCount:null,
            taskModeData: [],
            schemeModeData: [],
            jobInfo: {},
            jobInfoList: [],
            snPhoneMap: {},
            statusFilterData: [],
            filters: {
              statusFilter: {
                'AUTO_CHECK_PASS': {status: false, indeterminate: false, color: 'success', style:'color:green;', filter: []},
                'AUTO_CHECK_FAIL': {status: true, indeterminate: false, color: 'warning', style:'color:orange;', filter: []},
                'VERIFICATION': {status: true, indeterminate: false, color: 'error', style:'color:red;', filter: []}
              },
              misIdFilter: [],
              mrnBundleFilter: [],
              phoneFilter: [],
              sceneNameFilter: ''
            },
            misIdList: [],
            mrnPageFilter: [],
            testItems: [],
            showJobCommentModal: false,
            mrnBundleList: [],
            jobIdCheckerHitsTotalMap: {},
            diffImageShow:'diff',
            userPermission: {
              isAdmin: false,
              isMember: false
            },
            focusJobId: '',
            previewIntervalID: 0,
            taskStatusOptions: [],
            showUserConfigModal: false,
            userJob: null,
            toRunPages: [],
            checkedSceneResultIds: {},
            showPhoneContainer: false,
            mockIdConfigMap: {},
            appPlat: null,
            jobCreating: false
          }
        },
      computed:{
        canRerunJob: function() {
          let reRun = false
          if (this.reportData) {
            if (this.userPermission.isAdmin || this.userPermission.isMember) {
              let jobEnd = true
              this.reportData.jobTask.forEach(task => {
                if (task.status === 'Waiting' || task.status === 'Running') {
                  jobEnd = false
                }
              })
              // job中所有task都未结束状态时，才能重跑
              reRun = jobEnd
            }
          }
          return reRun
        }
      },  
      mounted(){
        if (this.filterIds === null) {
          this.selectFilter(this.viewAll);
        }
        if(this.$route.query.jobId){
          let jobId = this.$route.query.jobId
          let pairJobs = this.$route.query.pairJobs
          this.getJobIdCheckerHitsTotalMap()
          this.getTestItems()
          this.getReportData(jobId,pairJobs).then((res) => {
            this.jobInfoList = this.reportData.jobInfoList
            for (let i in this.jobInfoList) {
              let job = this.jobInfoList[i]
              if (job.category === '微信') {
                job.packageUrl = ''
                job.qrLoading = false
              }
              job.jobCreating = false
              if (job.plat === 'iOS') {
                this.getHPXAppName(job).then((res)=>{
                  if (job.hpxAppName) {
                    this.getHPXUrl(job).then((res)=> {
                      if (res.data.status === 1 && res.data.data.bundle_id === job.bundleId) {
                        job.hpxTaskUrl = res.data.data.taskUrl
                      } else {
                        job.hpxTaskUrl = job.packageUrl
                      }
                    })
                  }
                })
              }
            }
            this.jobInfo = this.jobInfoList[0]
            this.snPhoneMap = this.reportData.phoneData
            for (let k in this.snPhoneMap) {
              this.filters.phoneFilter.push(this.snPhoneMap[k].model+this.snPhoneMap[k].version)
            }
            this.statusFilterData = this.reportData.resultDistribution
            this.taskStatusOptions = this.reportData.taskStatusOptions
            this.misIdList = this.reportData.users
            this.changeSceneStatusByGroup()
            this.getBusinessByName(this.jobInfo.business).then((res) => {
              let businessId = res.data.id
              this.hasAdminPermission(businessId)
            })
            this.getMRNBundleList()
            let viewName = this.getSceneNameByViewId()
            if (viewName !== '') {
              this.filters.sceneNameFilter = viewName
              for (let key in this.filters.statusFilter) {
                let item = this.filters.statusFilter[key]
                if (item.status === false) {
                  item.status = true
                  this.changeSceneStatusByGroup(key)
                }
              }
            }
            this.flashShowData()
            this.isRequesting=false
          })
        }
      },
      methods:{
        toRunJob(newUserJob) {
          this.userJob = newUserJob
          this.appPlat = this.userJob.platform
          this.showPhoneContainer=true
        },
        sceneResultSelectChange(taskId, ids) {
          this.checkedSceneResultIds[taskId] = ids
        },
        filterSceneResultByCheckIds(ids) {
          // 找到对应的sceneResults
          let checkedSceneResults = []
          for(let i in this.reportData.jobTask) {
            let sceneResultList = this.reportData.jobTask[i].sceneResultList
            for (let j in sceneResultList) {
              let sceneResult = sceneResultList[j]
              if (ids.has(sceneResult.id)) {
                checkedSceneResults.push(sceneResult)
              }
            }
          }
          return checkedSceneResults
        },
        createJob(job) {
          let sceneResultIds = new Set()
          for (let k in this.checkedSceneResultIds) {
            this.checkedSceneResultIds[k].forEach(id => {
              sceneResultIds.add(id)
            })
          }
          if (sceneResultIds.length <= 0) {
            this.$Notice.warning({title: '未勾选重建任务需要的场景'})
            return
          }
          job.jobCreating = true
          // 勾选场景，重试
          this.getUserJobTemplate(job.id).then((res) => {
            let info = res.data
            if (info.code !== 0) {
              this.$Notice.error({title: info.message});
              job.jobCreating = false
            } else {
              this.userJob = info.data
              this.appPlat = this.userJob.platform
              // 选择的场景 sceneIds
              let sceneIds = new Set()
              let mockIdConfigMap = {}
              let checkedResult = this.filterSceneResultByCheckIds(sceneResultIds)
              for (let i in checkedResult) {
                let result = checkedResult[i]
                sceneIds.add(result.sceneId)
                // 存在多组交互分裂场景
                let configId = ''
                let mockId = result.mockId
                let index = result.mockId.indexOf('_')
                if (index >= 0) {
                  let cid = result.mockId.substring(index + 1)
                  // 防止cid后存在后缀例如：_dianping等情况出现
                  let cid_index = cid.indexOf('_')
                  if (cid_index > 0) {
                    cid = cid.substring(0, cid_index)
                  }
                  if (isNaN(cid) === false) {
                    mockId = result.mockId.substring(0, index)
                    configId = cid
                  } else {
                    mockId = result.mockId.substring(0, index)
                    configId = ''
                  }
                }
                if (mockId) {
                  if (mockId in mockIdConfigMap) {
                    mockIdConfigMap[mockId].push(configId)
                  } else {
                    mockIdConfigMap[mockId] = [configId]
                  }
                }
              }
              // 补充pages信息
              let productId = this.userJob.product_id
              //这里找不到page，前面都正确
              this.getPageObjectBySceneIds(sceneIds, productId).then(res => {
                let pages = res.data
                this.userJob.pages = pages
                // 更新app_params
                let app_params = JSON.parse(this.userJob.app_params)
                app_params.action_filter = mockIdConfigMap
                this.userJob.app_params = JSON.stringify(app_params)
                this.toRunPages = pages
                console.log('重建任务')
                console.log(this.userJob)
                job.jobCreating = false
                this.showUserConfigModal = true
              })
            }
          })
        },
        selectAutoFilter() {
          this.selectFilter(this.viewAll)
        },
        selectViewed(value) {
          if (value === "false") {
            this.viewAll = false
          } else {
            this.viewAll = true
          }
          this.selectFilter(this.viewAll)
        },
        selectFilter(viewAll) {
          let jobId = this.$route.query.jobId
          let pairJobs = this.$route.query.pairJobs
          this.$axios({
            method:"get",
            params:{
              "jobId": jobId,
              "pairJobs":pairJobs,
              "viewAll": viewAll
            },
            url:this.env.url+"client/compatibility/filteredByLabel"
          }).then((res) => {
            this.filterIds = res.data.jobFilterInfos
          }).catch(function (error) {
            console.log(error)
          })
        },
        ShowProgress() {
          let ids = new Set(this.filterIds.map(item => item.id));
          this.filterCount = this.getProblemNumByfilterIds(ids)
          return Math.round(this.filterCount/this.getDenominator()*100)
        },        
        getDenominator() {
          //有问题的case总和作为分母
          return this.statusFilterData[1]['sceneCount']+this.statusFilterData[2]['sceneCount']
        },
        getProblemNumByfilterIds(ids) {
          let count = 0
          for(let i in this.reportData.jobTask) {
            let sceneResultList = this.reportData.jobTask[i].sceneResultList
            for (let j in sceneResultList) {
              let sceneResult = sceneResultList[j]
              if (ids.has(sceneResult.id) && sceneResult['type'] !='AUTO_CHECK_PASS' ) {
                count += 1
              }
            }
          }
          if(this.viewAll === false) {
            //如果是未标记查询，那么上面统计的是还未看的数量，需要反过来
            count = this.getDenominator() - count
          }
          return count
        },
        getUserJobTemplate(jobId) {
          return this.$axios({
            method:"get",
            params:{
              "jobId":jobId
            },
            url:this.env.url+"client/compatibility/userJob/template"
          }).catch(function (error) {
            console.log(error)
          })
        },
        getPageObjectBySceneIds(sceneIds, product_id) {
          return this.$axios({
            method:"post",
            data:{
              "sceneIds": JSON.stringify(sceneIds),
              "productId": product_id
            },
            url:this.env.url+"page/getPageObject"
          }).catch(function (error) {
            console.log(error)
          })
        },
        getSceneNameByViewId() {
          let sceneName = ''
          let viewId = this.$route.query.view;
          for(let i in this.reportData.jobTask) {
            let sceneResultList = this.reportData.jobTask[i].sceneResultList
            for (let j in sceneResultList) {
              let sceneResult = sceneResultList[j]
              if (viewId === sceneResult.id.toString()) {
                sceneName = sceneResult.name
                break
              }
            }
            if (sceneName !== '') {
              break
            }
          }
          return sceneName
        },
        getReportData(jobId, pairJobs){
          this.isRequesting=true;
          return this.$axios({
            method:"get",
            params:{
              "jobId":jobId,
              "pairJobs":pairJobs
            },
            url:this.env.url+"client/compatibility/reportInfo"
          }).then((res) => {
            this.reportData = res.data
          }).catch(function (error) {
            console.log(error)
          })
        },
        getTestItems(){
          this.$axios({
            method:"get",
            url:this.env.url+"page/getTestItem"
          }).then((res) => {
            let message = res.data;
            if(Array.isArray(message)){
              this.testItems = message
            }
          }).catch(function (error) {
            console.log(error)
          })
        },
        async getMRNBundleList(){
          this.$axios({
            method:"get",
            url:this.env.url+"client/MRNBundleList"
          }).then((res) => {
            let message = res.data;
            if(this.jobInfo.business==='travel'){
              for(let i=0; i< message.length;i++){
                if(message[i].value.includes("rn_travel")){
                  this.mrnBundleList.push(message[i]);
                }
              }
            } else if(this.jobInfo.business==='hotel') {
              for (let i = 0; i < message.length; i++) {
                if (message[i].value.includes("rn_hotel")) {
                  this.mrnBundleList.push(message[i]);
                }
              }
            } else if(this.jobInfo.business==='overseahotel'){
                for(let i=0; i< message.length;i++){
                  if(message[i].value.includes("rn_hotel")|| message[i].value.includes("rn_overseahotel")){
                    this.mrnBundleList.push(message[i]);
                  }
              }
            } else {
              this.mrnBundleList = message;
            }
          }).catch(function (error) {
            console.log(error)
          })
        },
        getMRNPages(){
          return this.$axios({
            method: 'get',
            params: {
              'category': this.filters.mrnBundleFilter.join()
            },
            url:this.env.url+"page/sceneList"
          }).then((res) => {
            this.mrnPageFilter = []
            for(let i = 0; i < res.data.length; i++){
              // 使用sceneId
              this.mrnPageFilter.push(res.data[i].id)
            }
            this.flashShowData()
          }).catch(function (error) {
            console.log(error)
          });
        },
        hasAdminPermission: function(businessId) {
          return this.$axios({
            method: 'get',
            params: {
              misId: Bus.userInfo.userLogin,
              businessId: businessId
            },
            url: this.env.url + 'autoTestConfig/getUserPermission'
          }).then((res) => {
            // 超级管理员或者业务管理员
            this.userPermission.isAdmin = res.data.isAdmin || res.data.permissionLevel === 10
            this.userPermission.isMember = res.data.permissionLevel !== 100
          }).catch(function (error) {
            console.log(error)
          })
        },
        async getBusinessByName(business) {
          return this.$axios({
            method: 'get',
            params: {
              businessName: business
            },
            url: this.env.url + 'autoTestConfig/getBusinessByBusinessName'
          }).catch(function (error) {
            console.log(error)
          })
        },
        getCheckerResults(id, sourceType) {
          return this.$axios({
              method: 'post',
              data: {
              size: 10000,
              query: {
                  bool: {
                  filter: {
                      bool: {
                      must: [
                          {
                          match_phrase: {
                              channel: 'notice'
                          }
                          },
                          {
                          match_phrase: {
                              'env.runner.jobId': id
                          }
                          },
                          {
                          match_phrase: {
                              'env.source_type': sourceType
                          }
                          }
                      ]
                      }
                  }
                  }
              },
              sort: {
                  '@timestamp': {
                  order: 'desc'
                  }
              },
              aggs: {
                  checker_name: {
                  terms: {
                      field: 'event.sender.function.keyword',
                      size: 1000
                  },
                  aggs: {
                      checker_title: {
                      terms: {
                          field: 'event.title.keyword',
                          size: 10000
                      }
                      }
                  }
                  }
              }
              },
              url: this.env.client_common_url + 'client/es/search/lyrebird-*'
          })
        },
        getHPXAppName(job) {
          let url = 'http://hpx.sankuai.com/api/open/getAppNameMapping'
          return this.$axios({
            method: 'get',
            url: url
          }).then((res) => {
            if (res.data.status === 1) {
              let mapping = res.data.data
              for (let key in mapping) {
                if (key.includes(job.category + '(' + job.plat.toLocaleUpperCase() + ')')) {
                  job.hpxAppName = mapping[key]
                  break
                }
              }
            }
          }).catch(function (error) {
            console.log(error)
          })
        },
        getHPXUrl(job) {
          let url = 'https://hpx.sankuai.com/api/open/getAppPackageInfo'
          return this.$axios({
            method: 'get',
            params: {
              appName: job.hpxAppName,
              buildNumber: job.buildId
            },
            url: url
          }).catch(function (error) {
            console.log(error)
            job.hpxTaskUrl = job.packageUrl
          })
        },
        getPreviewMPQRCodeParams(job) {
          if (job.packageUrl.length > 1) {
            job.qrLoading = false
            return
          }
          job.qrLoading = true
          let url = this.env.url+"client/compatibility/previewMPQRCode"
          return this.$axios({
            method: 'get',
            params: {
              jobId: job.id
            },
            url: url
          }).then((res => {
            if (res.data.code === 0 && 'data' in res.data) {
              if (res.data.data.code === 0) {
                job.packageUrl = res.data.data.url
                job.qrLoading = false
                clearInterval(this.previewIntervalID)
                this.previewIntervalID = 0
                this.$Notice.info({title: "预览二维码已生成", duration: 10, closable: true})
              } else if(res.data.data.code === -1) {
                // 还未完成二维码生成
                console.log('preview_mp_qrcode', res.data.data.info)
              } else {
                // 错误
                clearInterval(this.previewIntervalID)
                this.previewIntervalID = 0
                job.qrLoading = false
                this.$Notice.error({title: es.data.data.info, duration: 10, closable: true})
                job.packageUrl = "微信小程序预览二维码生成失败"
              }
            } else {
              job.packageUrl = "-"
              clearInterval(this.previewIntervalID)
              this.previewIntervalID = 0
              job.qrLoading = false
            }
          })).catch(function (error) {
            job.packageUrl = "-"
            clearInterval(this.previewIntervalID)
            this.previewIntervalID = 0
            job.qrLoading = false
          })
        },
        showMPQRCode(job) {
          if (this.previewIntervalID !== 0) {
            return
          }
          this.getPreviewMPQRCodeParams(job)
          this.previewIntervalID = setInterval(()=>this.getPreviewMPQRCodeParams(job), 8000);
          setTimeout(()=>{
            if (this.previewIntervalID !== 0) {
              clearInterval(this.previewIntervalID)
              this.previewIntervalID = 0
              job.qrLoading = false
              job.packageUrl = "微信小程序预览二维码生成超时"
              this.$Notice.error({title: "微信小程序预览二维码生成超时", duration: 10, closable: true})
            }
          }, 300000)
        },
        getJobIdCheckerHitsTotalMap() {
          let jobId = this.$route.query.jobId
          let pairJobs = this.$route.query.pairJobs
          let jobIds = [jobId]
          if (pairJobs) {
            let pairJobIds = pairJobs.split(",")
            if (pairJobIds.length > 0) {
              for (let i in pairJobIds) {
                if (pairJobIds[i].length > 0) {
                  jobIds.push(pairJobIds[i])
                }
              }
            }
          }
          for (let i in jobIds) {
            this.getCheckerResults(jobIds[i], "compatibility").then((res) => {
              this.jobIdCheckerHitsTotalMap[jobIds[i]] = res.data.hits.total
            })
          }
        },
        showJobComment(job) {
          this.jobInfo = job
          this.showJobCommentModal = true
        },
        flashShowData: function() {
          this.filterTaskModeData()
          this.filterSchemeModeData()
        },
        filterTaskModeData: function () {
          if (this.reportData === null) {
            return []
          }
          // 根据筛选条件筛选出展示的数据
          let modeData = []
          let misIdPageFilter = []
          if (this.filters.misIdFilter.length > 0) {
            for (let i in this.filters.misIdFilter) {
              misIdPageFilter = misIdPageFilter.concat(this.reportData.userScenes[this.filters.misIdFilter[i]])
            }
          }
          let statusList = []
          for (let i in this.filters.statusFilter) {
            statusList = statusList.concat(this.filters.statusFilter[i].filter)
          }
          for (let i in this.reportData.jobTask) {
            let taskData = this.reportData.jobTask[i]
            let sceneResultList = []
            for(let j in taskData.sceneResultList) {
              let sceneResult = taskData.sceneResultList[j]
              //机型筛选
              let model = this.snPhoneMap[sceneResult.sn].model+this.snPhoneMap[sceneResult.sn].version
              if(this.filters.phoneFilter.length < Object.entries(this.snPhoneMap).length && !this.filters.phoneFilter.includes(model)){
                  continue
              }
              // MRN包筛选
              if (this.filters.mrnBundleFilter.length > 0 && !this.mrnPageFilter.includes(sceneResult.sceneId)) {
                continue
              }
              // 页面名称筛选
              if (this.filters.sceneNameFilter.length > 0 && !sceneResult.name.includes(this.filters.sceneNameFilter)) {
                continue
              }
              // MisId筛选
              if (misIdPageFilter.length > 0 && !misIdPageFilter.includes(sceneResult.pageInfoId)) {
                continue
              }
              // 结果状态过滤
              if (!statusList.includes(sceneResult.resultTitle)) {
                continue
              }
              sceneResultList.push(sceneResult)
            }
            let showData = JSON.parse(JSON.stringify(taskData))
            showData.sceneResultList = sceneResultList
            modeData.push(showData)
          }
          this.taskModeData = modeData
          event.$emit("changeJobScheme","changeJobScheme");
        },
        filterSchemeModeData: function() {
          if (this.reportData === null) {
            this.schemeModeData = []
          }
          // 根据筛选条件筛选出展示的数据
          let modeData = []
          let misIdPageFilter = []
          if (this.filters.misIdFilter.length > 0) {
            for (let i in this.filters.misIdFilter) {
              misIdPageFilter = misIdPageFilter.concat(this.reportData.userScenes[this.filters.misIdFilter[i]])
            }
          }
          let statusList = []
          for (let i in this.filters.statusFilter) {
            statusList = statusList.concat(this.filters.statusFilter[i].filter)
          }
          for (let i in this.reportData.jobSchema) {
            let schemeData = this.reportData.jobSchema[i]
            // MRN包筛选
            if (this.filters.mrnBundleFilter.length > 0 && !this.mrnPageFilter.includes(schemeData.sceneId)) {
              continue
            }
            // 页面名称筛选
            if (this.filters.sceneNameFilter.length > 0 && !schemeData.name.includes(this.filters.sceneNameFilter)) {
              continue
            }
            // MisId筛选
            if (misIdPageFilter.length > 0 && !misIdPageFilter.includes(schemeData.pageInfoId)) {
              continue
            }
            let sceneResultList = []
            let noSceneCheck = true
            for(let j in schemeData.sceneResultList) {
              let sceneResult = schemeData.sceneResultList[j]
              //机型筛选
              let model = this.snPhoneMap[sceneResult.sn].model
              if(this.filters.phoneFilter.length < Object.entries(this.snPhoneMap).length && !this.filters.phoneFilter.includes(model)){
                  continue
              }
              // 结果状态过滤
              if (statusList.includes(sceneResult.resultTitle)) {
                noSceneCheck = false
              }
              sceneResultList.push(sceneResult)
            }
            if (noSceneCheck) {
              continue
            }
            let showData = JSON.parse(JSON.stringify(schemeData))
            showData.sceneResultList = sceneResultList
            modeData.push(showData)
          }
          this.schemeModeData = modeData
          event.$emit("changeJobScheme","changeJobScheme");
        },
        focusJob(jobId) {
          this.focusJobId = jobId
        },
        changeSceneStatus(key) {
          let statusFilter = this.filters.statusFilter[key]
          for (let i in this.statusFilterData) {
            let filterData = this.statusFilterData[i]
            if (filterData.key === key) {
              if (0 < statusFilter.filter.length && statusFilter.filter.length < filterData.list.length) {
                statusFilter.indeterminate = true
              } else if (statusFilter.filter.length === filterData.list.length) {
                statusFilter.status = true
                statusFilter.indeterminate = false
              } else {
                statusFilter.status = false
                statusFilter.indeterminate = false
              }
              break
            }
          }
          this.flashShowData()
        },
        changeSceneStatusByGroup(key) {
          for (let i in this.statusFilterData) {
            let filterData = this.statusFilterData[i]
            // 指定key时，仅需改动key关联的数据
            if (key && key !== filterData.key) {
              continue
            }
            let statusFilter = this.filters.statusFilter[filterData.key]
            statusFilter.filter = []
            if (statusFilter.status) {
              for (let j in filterData.list) {
                statusFilter.filter.push(filterData.list[j].title)
              }
              statusFilter.indeterminate = false
            }
          }
          this.flashShowData()
        },
        filterMRNBundle(value){
          let isExist = false;
          for(let i=0;i<this.mrnBundleList.length;i++){
            if(this.mrnBundleList[i].label.includes(value)){
              isExist = true;
            }
          }
          if(!isExist){
            this.mrnBundleList.push({value:value,label:value})
          }
          this.flashShowData()
        },
        jumpToCheckerInfo(jobId, business){
          let routeData = this.$router.resolve({ path: '/clientChecker/checkerInfo', query: { id: jobId, sourceType: 'compatibility', business: business} });
          window.open(routeData.href, '_blank');
        },
        filterTestItem(itemName){
          let label = itemName;
          for(let i=0;i<this.testItems.length;i++){
            if(this.testItems[i].name === itemName){
              label = this.testItems[i].label
              break
            }
          }
          return label
        },
        transformToSourceName(sourceType){
          if(sourceType === 'gatedLaunch'){
            return '发布'
          } else if (sourceType === 'regression'){
            return'回归'
          } else {
            return sourceType
          }
        },
        getCustomUrl(jobName,business){
          let name = ""
          const match = jobName.match(/MMCD(.*)/);
          if (match && match[1]) {
            name = match[1].slice(1);
          }
          return "https://qa.sankuai.com/compatibilityUserConfig?name=" + name + "&business=" + business 
        },
        changeShowMode() {
          console.log(this.imageShowOptions.showMode)
          this.flashShowData()
        },
        changeImageShow() {
          event.$emit("changeImageShow","changeImageShow")
        },
        changeImageSize(model){
          if(model==="s"){
            let showSize = this.imageShowOptions.imageShowSize.split("px")[0]-50;
            if(showSize > 100){
              this.imageShowOptions.imageShowSize = showSize+"px";
            }
          }
          else {
            let showSize = this.imageShowOptions.imageShowSize.split("px")[0]-0+50;
            if(showSize < 500){
              this.imageShowOptions.imageShowSize = showSize+"px";
            }

          }
        },
        openLink(path, type){
          let link = '';
          if(type === "site"){
            let routeData = this.$router.resolve({path: path});
            link = routeData.href;
          }else{
            link = path
          }
          window.open(link, '_blank')
        },
        confirmRerunJob(){
          let job_id = this.$route.query.jobId
          this.$Modal.confirm({
            title: '任务重跑确认',
            content: '确定重跑第' + job_id + '条任务吗？',
            onOk: () => {
              this.$axios({
                method:"post",
                url:this.env.url+"client/compatibility/jobRerun/" + job_id
              }).then((res) => {
                let message = res.data;
                if(message.code === 0){
                  this.$Notice.success({
                    title: "第" + job_id + "号Job下的所有Task已进入重跑状态"
                  });
                  this.reload();
                }
                else {
                  console.log(message);
                  this.$Notice.error({
                    title: "第" + job_id + "号Job重跑失败，错误码：" + message.code.toString()
                  });
                }
              }).catch(function (error) {
                console.log(error);
                this.$Notice.error({
                  title: "第" + job_id + "号Job重跑失败"
                });
              })
            }
          });
        },
      }
    }
</script>

<style scoped>
  .pages {
    position: fixed;
    bottom: 5%;
    right: 42%;
  }
  .spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  .spin-container{
    padding: 35px;
  }
  .content-title-container{
    border-left: 3px ;
    font-size: small;
    text-overflow:ellipsis;/* 省略的文本用省略号表示 */
    line-height: 200%;
  }
  .custom-select {
  max-height: 45px; /* 设置最大高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
 }
  .content-title {
     text-align: left;
     font-size: small;
     font-weight: bold;
     padding-left: 12px;
   }
   .filter-item {
     display: flex;
     padding-bottom: 2px;
   }
   .filter-item .title {
     min-width: 130px;
     font-size: smaller;
   }
   .job-info-card-focus {
      background: aliceblue;
   }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
