<template>
  <Row>
    <Col span="17">
    <Confirmation :confirmInfo="confirmInfo"></Confirmation>
    <Row class="title">
      <span>页面基础信息</span>
      <div v-if="permission" style="display:contents">
        <Tooltip content="Edit" placement="top">
          <Button class="button-edit" icon="md-create" type="text" @click="toEdit" :disabled="isEdit"></Button>
        </Tooltip>
        <Tooltip v-if="pageInfo&&pageInfo.online===1" content="offLine" placement="top">
          <Button class="button-edit" icon="md-remove-circle" type="text" @click="offline"></Button>
        </Tooltip>
        <Tooltip v-else content="onLine" placement="top">
          <Button class="button-edit" icon="md-navigate" type="text" @click="showOnlineOptions"></Button>
        </Tooltip>
        <Tooltip content="Delete" placement="top">
          <Button class="button-edit" icon="ios-trash" type="text" @click="deletePage"></Button>
        </Tooltip>
      </div>

      <Modal v-model="showOnlineModal" title="选择恢复方式" @on-ok="handleOnlineOption">
        <RadioGroup v-model="onlineOption">
          <Radio label="1">仅恢复页面</Radio>
          <Radio label="2">连带恢复场景</Radio>
        </RadioGroup>
      </Modal>
    </Row>
    <Info class="form-position" v-if="!isEdit" :pageInfo="pageInfo"></Info>
    <InfoEdit class="form-position" v-else :editInfo="editInfo"></InfoEdit>
    <Row class="title">
      <span>页面相关产品</span>
      <Poptip trigger="hover" content="变更产品将会影响到该页面下所有场景，请谨慎操作" placement="right-start">
        <Icon type="md-help-circle" style="margin-left:5px" />
      </Poptip>
    </Row>
    <div class="form-position">
      <ProductSelect v-for="(item, index) in pageInfo.productList" :key="index" :product="item"></ProductSelect>
      <ProductSelect v-if="isAdd" :product="productsNew" @submit="productSubmit"></ProductSelect>
      <Button v-if="permission" class="add-button" :disabled="isAdd" type="dashed" icon="md-add" @click="addProduct">Add Product</Button>
    </div>
    <Row class="title">
      <span>页面相关负责人</span>
    </Row>
    <UserList class="form-position" :pageUsers="pageInfo.pageUsers" :permission="permission"></UserList>
    </Col>
    <Col span="1">
    <br>
    </Col>
    <Col span="6" style="text-align:center">
    <img class="error-img" src="/static/img/cqp-error.jpg" />
    <br>
    <span>暂无页面示意图</span>
    </Col>
  </Row>
</template>
<script>
import Confirmation from '../baseComponents/Confirmation'
import Info from './DetailInfo/InfoShow'
import InfoEdit from './DetailInfo/InfoEdit'
import UserList from './DetailInfo/UserList'
import ProductSelect from './DetailInfo/ProductSelect'
import { Bus } from '@/global/bus'

export default {
  name: 'PageInfo',
  props: ['tab'],
  components: { Confirmation, Info, InfoEdit, ProductSelect, UserList },
  data() {
    return {
      isEdit: false,
      isAdd: false,
      productsNew: {},
      editInfo: {},
      confirmInfo: {
        type: 'delete',
        show: false,
        message: '删除页面将永久删除下属所有场景及相关Mock数据，确认要删除么？',
        title: '警告⚠️'
      },
      showOnlineModal: false,
      onlineOption: '1'
    }
  },
  created() {
    Bus.$on('confirmDelete', this.deleteAction)
    Bus.$on('cancelEdit', () => {
      this.isEdit = false
    })
    Bus.$on('submitEdit', () => {
      this.$store.dispatch('getPageInfoById', this.pageInfo.id)
      this.isEdit = false
    })
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
  },
  computed: {
    pageInfo() {
      return this.$store.state.clientQuality.pageInfo.pageInfo
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    showOnlineOptions () {
      this.showOnlineModal = true
    },
    handleOnlineOption() {
      this.showOnlineModal = false
      if (this.onlineOption === '1') {
        this.online(false)
      } else if (this.onlineOption === '2') {
        this.online(true)
      }
    },
    initEditInfo() {
      this.editInfo.id = this.pageInfo.id
      this.editInfo.name = this.pageInfo.name
      this.editInfo.categories = this.pageInfo.categories
      this.editInfo.priority = this.pageInfo.priority
      this.editInfo.remark = this.pageInfo.remark
    },
    toEdit() {
      this.initEditInfo()
      this.$store.dispatch('getMRNList')
      this.inputClass = null
      this.isEdit = true
    },
    offline() {
      let edit = {}
      edit.online = 0
      this.$store.dispatch('setPageInfo', edit)
    },
    online (withScenes) {
      // 恢复页面时可选择是否恢复场景
      let edit = {}
      edit.online = 1
      edit.withScenes = withScenes
      this.$store.dispatch('setPageInfo', edit)
    },
    deletePage() {
      this.confirmInfo.show = true
    },
    async deleteAction() {
      await this.$store.dispatch('deletePageInfo', this.pageInfo.id)
      let routeData = this.$router.resolve({
        path: '/client/businessDetail/' + this.pageInfo.businessId + '/page'
      })
      window.open(routeData.href, '_self')
    },
    addProduct() {
      this.isAdd = true
    },
    productSubmit(reuslt) {
      this.productsNew = {}
      this.isAdd = false
    }
  }
}
</script>
<style scoped>
.form-position {
  margin-left: 25px;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 12px;
}
.button-edit {
  padding: 0px 5px 0px;
  font-size: 14px;
  vertical-align: bottom;
}
.name {
  font-size: 18px;
  color: #464c5b;
  font-weight: bold;
  margin-left: 5px;
  margin-right: 10px;
}
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
}
.error-img {
  width: 200px;
}
.add-button {
  margin-top: 10px;
  margin-bottom: 20px;
}
</style>
