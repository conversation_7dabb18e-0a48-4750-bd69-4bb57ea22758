<template>
  <div class="layout">
    <BusinessHead :businessInfo="businessInfo" :pageInfo="pageInfo" :sceneInfo="sceneInfo"></BusinessHead>
    <Menu class="client-menu" mode="horizontal" theme="light" :active-name="tab">
      <MenuItem name="scene" :to="{name:'scene'}">
      <Icon type="ios-book" />
      场景列表
      </MenuItem>
      <MenuItem name="url" :to="{name:'url'}">
      <Icon type="md-link" />
      URL模型
      </MenuItem>
      <MenuItem name="apiInfo" :to="{name:'apiInfo'}">
      <Icon type="md-list-box" />
      接口信息
      </MenuItem>
      <!-- <MenuItem name="strategy" :to="{name:'strategy'}">
      <Icon type="md-build" />
      触发策略配置
      </MenuItem> -->
      <!-- <MenuItem name="data">
            <Icon type="md-stats" />
            运营数据大盘
        </MenuItem> -->
      <MenuItem v-if="pageInfo&&pageInfo.test&&pageInfo.test!=''" name="test" :to="{name:'test'}">
      <Icon type="ios-body" />
      测试设计
      </MenuItem>
      <MenuItem name="pageInfo" :to="{name:'pageInfo'}">
      <Icon type="ios-body" />
      基础信息
      </MenuItem>
      <!-- <MenuItem name="ab">
            <Icon type="md-funnel" />
            AB TEST
        </MenuItem> -->
    </Menu>
    <router-view :pageInfo="pageInfo"></router-view>
  </div>

</template>
<script>
import BusinessHead from '../baseComponents/BusinessHead'
export default {
  name: 'PageDetail',
  components: { BusinessHead },
  props: ['pageId'],
  data() {
    return {
      sceneInfo: null
    }
  },
  mounted() {
    this.$store.dispatch('getPageInfoById', this.pageId)
  },
  computed: {
    businessInfo: function () {
      return this.$store.state.clientQuality.pageInfo.businessInfo
    },
    pageInfo: function () {
      return this.$store.state.clientQuality.pageInfo.pageInfo
    },
    tab() {
      return this.$store.state.clientQuality.currentTab
    }
  }
}
</script>
<style scoped>
.layout {
  background: #fff;
  text-align: left;
}
.client-menu {
  margin-bottom: 15px;
}
</style>