<template>
  <Row>
    <Col span="21">
    <Form :model="search" :label-width="65" inline>
      <Row>
        <Col span="19">
        <FormItem label="名称">
          <Input type="text" style="width:200px" v-model="search.name" placeholder="Enter Page Name" />
        </FormItem>
        <FormItem v-if="pageInfo" label="产品">
          <Select v-model="search.product" multiple filterable transfer placeholder="Select" :max-tag-count="1" style="width:210px">
            <Option v-for="item in pageInfo.products" :value="item.id" :key="item.id">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="产物" :label-width="70">
          <Select v-model="search.impls" multiple filterable transfer placeholder="Select" :max-tag-count="1" style="width:190px">
            <Option v-for="item in implList" :value="item" :key="item.index">{{ item }}</Option>
          </Select>
        </FormItem>
        <FormItem label="在线" :label-width="75">
          <i-switch v-model="search.online" :true-value="1" :false-value="0" />
        </FormItem>
        <FormItem label="测试项" :label-width="80">
          <Select v-model="search.testSupport" filterable transfer placeholder="Select" :max-tag-count="1" style="width:190px">
            <Option v-for="item in testItemList" :value="item.name" :key="item.index">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="优先级">
          <CheckboxGroup v-model="search.priorityList">
            <Checkbox label="1">P1</Checkbox>
            <Checkbox label="2">P2</Checkbox>
            <Checkbox label="3">P3</Checkbox>
            <Checkbox label="4">P4</Checkbox>
          </CheckboxGroup>
        </FormItem>

        </Col>
        <Col span="5">
        <FormItem>
          <Button type="primary" icon="md-search" @click="searchScene">Search</Button>
          <Button @click="clearSearch">Reset</Button>
        </FormItem>
        </Col>
      </Row>
    </Form>
    </Col>
    <Col span="2" offset="1">
    <Button v-if="permission" icon="md-add" type="success" @click="addScene">New</Button>
    </Col>
  </Row>

</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'SceneFilter',
  props: ['pageInfo', 'permission'],
  data() {
    return {
      search: {
        name: '',
        priorityList: [],
        online: 1,
        product: [],
        impls: [],
        testSupport: ''
      }
    }
  },
  computed: {
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    implList() {
      let list = []
      if (this.search.product.length >= 1) {
        for (let item of this.pageInfo.products) {
          if (this.search.product.indexOf(item.id) > -1) {
            for (let impl of item.implList) {
              if (list.indexOf(impl) === -1) {
                list.push(impl)
              }
            }
          }
        }
      } else {
        for (let item of this.pageInfo.products) {
          for (let impl of item.implList) {
            if (list.indexOf(impl) === -1) {
              list.push(impl)
            }
          }
        }
      }

      return list
    }
  },
  mounted() {
    this.$store.dispatch('getTestItemList')
  },
  methods: {
    searchScene() {
      let query = {}
      query.name = this.search.name
      query.online = this.search.online
      query.priority = this.search.priorityList.toString()
      query.product = this.search.product.toString()
      query.impl = this.search.impls.toString()
      query.testSupport = this.search.testSupport
      this.$store.commit('setSceneFilter', query)
      this.$store.commit('setCurrentPage', 1)
      this.$store.dispatch('getSceneList')
    },
    clearSearch() {
      this.$store.commit('setSceneFilter', {})
      this.search = {
        name: '',
        priorityList: [],
        online: 1,
        product: [],
        impls: [],
        testSupport: ''
      }
      this.$store.commit('setCurrentPage', 1)
      this.$store.dispatch('getSceneList')
    },
    addScene() {
      let editInfo = {
        type: 'scene',
        data: {
          name: '',
          parent: Number(this.$route.params.pageId),
          priority: 2,
          online: 1
        },
        businessId: this.pageInfo.businessId
      }
      Bus.$emit('editPage', editInfo)
    }
  }
}
</script>
<style scoped>
.add-button {
  float: right;
  margin-right: 30px;
}
</style>