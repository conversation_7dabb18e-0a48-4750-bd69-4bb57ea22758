<template>
  <div>
    <Confirmation :confirmInfo="confirmInfo"></Confirmation>
    <Table v-if="sceneList&&sceneList.length>0" class="table-position" :columns="title" ref="selection" :data="sceneList" :show-header="isHeader" @on-selection-change="hasSelected">
      <template slot-scope="{row}" slot="name">
        <span v-if="type!='defalut'" class="scene-name" @click="view(row)">{{row.name}}</span>
        <Badge dot v-if="row.additional.autoTag === 1" >
        </Badge>
        <span v-if="type=='defalut'" class="scene-name">{{row.name}}</span>
        <Tag v-if="row.online===1" color="green">ON</Tag>
        <Tag v-else color="red">OFF</Tag>
      </template>
      <template slot-scope="{row}" slot="product">
        <div class="product111" v-for="item in row.products" :key="item.index">
          <Badge style="font-size:13px;text-align:left" status="success" :text="item.product.label" />
        </div>
      </template>
      <template slot-scope="{row}" slot="impl">
        <div class="product111" v-for="item in row.products" :key="item.index">
          <span class="span-impl" style="font-weight:500;">{{item.implement}}</span>
        </div>
      </template>
      <template slot-scope="{row}" slot="version">
        <div class="product111" v-for="item in row.products" :key="item.index">
          <Poptip v-for="mock in item.mockTrees" :key="mock.id" placement="bottom" transfer>
            <Button class="verison" type="warning" ghost>{{mock.category}}</Button>
            <div slot="content">
              <qriously :value="mock.urlscheme" :size="200" />
            </div>
          </Poptip>
        </div>
      </template>
      <template slot-scope="{row}" slot="test">
        <div class="product111" v-for="item in row.products" :key="item.index" style="text-align:left">
          <Tag v-for="test in item.testItems" :key="test.name" color="primary" type="border">
            {{test.label}}
          </Tag>
        </div>
      </template>
      <template slot-scope="{row}" slot="tag">
        <Tag v-if="row.priority === 1" color="volcano">P{{ row.priority }}</Tag>
        <Tag v-else color="blue">P{{ row.priority }}</Tag>
        <Tag v-for="item in row.tag" :key="item.index" :color="getTagColor(item)">{{item}}</Tag>
      </template>
      <template slot-scope="{row}" slot="action">
        <Button v-if="permission" size="small" type="info" ghost @click="editScene(row)">快速编辑</Button>
        <Button v-if="permission" size="small" type="error" ghost @click="deleteScene(row)">删除</Button>
        <Button v-else size="small" type="error" ghost disabled>暂无操作权限</Button> 
        <Modal v-model="modal" width="360">
          <template #header>
            <p style="color:#f60;text-align:center">
              <Icon type="ios-information-circle"></Icon>
              <span>提醒</span>
            </p>
          </template>
          <div style="text-align:center">
            <p>将同步删除mock数据组，</p>
            <p>你确定要删除吗？</p>
          </div>
          <template #footer>
            <Button :loading="modal_loading" @click="cancelModal">取消</Button>
            <Button v-if="isDeleteOne" type="error" :loading="modal_loading" @click="deleteSceneAction">删除</Button>
            <Button v-else type="error" :loading="modal_loading" @click="deleteListAction">批量删除</Button>
          </template>
        </Modal>
      </template>
    </Table>
    <div class="button-position">
      <Button v-if="permission && sceneList.length!=0" type="info" ghost @click="editSceneList" :disabled="disabled || !edited" size="small">批量修改</Button>
      <Button v-if="permission && sceneList.length!=0" type="error" ghost @click="deleteList" :disabled="disabled || !edited" size="small">
        批量删除
      </Button>
    </div>
  </div>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'SceneList',
  props: ['type', 'sceneList', 'businessId', 'permission'],
  data() {
    return {
      modal: false,
      isDeleteOne: true,
      disabled: true,
      edited: false,
      modalSceneId: '',
      title: [
        {
          type: 'index',
          width: 60,
          align: 'center',
          renderHeader: (h, params) => {
            if (this.permission) {
              return h(
                'Tooltip',
                {
                  props: {
                    placement: 'top',
                    transfer: true,
                    content: '多选/取消'
                  }
                },
                [
                  h('Button', {
                    props: {
                      icon: 'md-checkbox-outline',
                      size: '18',
                      type: 'text'
                    },
                    on: {
                      click: () => {
                        this.editTable()
                      }
                    }
                  })
                ]
              )
            } else {
              return h('span', '#')
            }
          }
        },
        { title: '名称', width: 360, key: 'name', slot: 'name' },
        {
          title: '详情 ',
          align: 'center',
          children: [
            {
              title: '产品',
              width: 180,
              key: 'product',
              slot: 'product'
            },
            {
              title: '产物',
              width: 110,
              key: 'impl',
              slot: 'impl',
              align: 'center'
            },
            {
              title: '应用版本',
              key: 'version',
              slot: 'version',
              align: 'center'
            },
            {
              title: '支持的测试项',
              key: 'test',
              slot: 'test',
              align: 'center'
            }
          ]
        },
        { title: '标签', width: 140, key: 'tag', slot: 'tag' },
        {
          title: '操作',
          width: 190,
          key: 'action',
          slot: 'action',
          align: 'center'
        }
      ]
    }
  },
  mounted() {},
  computed: {
    isHeader() {
      if (this.type === 'defalut') {
        return false
      } else {
        return true
      }
    }
  },
  methods: {
    rowClick(scene) {
      if (this.type !== 'defalut') {
        this.view(scene)
      }
    },
    getTagColor(tag) {
      if (tag === 'M') {
        return 'cyan'
      } else if (tag === 'T') {
        return 'orange'
      } else if (tag === 'V') {
        return 'magenta'
      } else if (tag === 'R') {
        return 'purple'
      } else {
        return 'green'
      }
    },
    editScene(scene) {
      let editInfo = {
        type: 'scene',
        sceneType: this.type,
        data: {
          id: scene.id,
          name: scene.name,
          parent: scene.pageId,
          priority: scene.priority,
          online: scene.online
        },
        businessId: this.businessId
      }
      Bus.$emit('editPage', editInfo)
    },
    view(scene) {
      let routeData = this.$router.resolve({
        path: '/client/scene/' + scene.id + '/base'
      })
      window.open(routeData.href, '_blank')
    },
    deleteScene(scene) {
      this.isDeleteOne = true
      this.modal = true
      this.modalSceneId = scene.id
    },
    async deleteSceneAction() {
      this.modal = false
      await this.$store.dispatch('deleteScene', this.modalSceneId)
      if (this.type === 'defalut') {
        this.$store.dispatch('getDefalutScenes', this.businessId)
      } else {
        this.$store.dispatch('getSceneList')
      }
    },
    deleteList() {
      this.isDeleteOne = false
      this.modal = true
    },
    async deleteListAction() {
      this.modal = false
      let selectedList = this.$refs.selection.getSelection()
      let ids = []
      for (var i = 0; i < selectedList.length; i++) {
        ids.push(selectedList[i].id)
      }
      await this.$store.dispatch('deleteSceneList', ids.join())
      if (this.type === 'defalut') {
        this.$store.dispatch('getDefalutScenes', this.businessId)
      } else {
        this.$store.dispatch('getSceneList')
      }
      this.disabled = true
    },
    hasSelected() {
      let selectionLenth = this.$refs.selection.getSelection().length
      if (selectionLenth !== 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    editSceneList() {
      let selectedList = this.$refs.selection.getSelection()
      let ids = []
      let names = []
      for (var i = 0; i < selectedList.length; i++) {
        ids.push(selectedList[i].id)
        names.push(selectedList[i].name)
      }
      let editInfo = {
        sceneType: this.type,
        data: {
          ids: ids,
          names: names
        },
        businessId: this.businessId
      }
      Bus.$emit('editPageList', editInfo)
      this.disabled = true
    },
    editTable() {
      if (this.edited) {
        this.title.shift()
        this.edited = false
      } else {
        this.title.unshift({ type: 'selection', width: 60, align: 'center' })
        this.edited = true
      }
    },
    cancelModal() {
      this.modal = false
    }
  }
}
</script>
<style scoped>
.scene-name {
  cursor: pointer;
  font-size: 12.5px;
  font-weight: 400;
  margin-right: 8px;
}
.table-position :after {
  /* width: 0px; */
}
.table-position {
  margin-left: 20px;
  margin-right: 45px;
  border: 0px;
}
.product111 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.verison {
  font-size: 12px;
  font-weight: 500;
  padding: 1px 2px 1px 2px;
  border-color: #ffbb96;
  background-color: #fffaf0;
}
.span-impl {
  display: -moz-inline-box;
  display: inline-block;
  width: 45px;
  text-align: center;
}
.button-position {
  margin-top: 20px;
  text-align: right;
  margin-right: 80px;
}
.table-position /deep/ .ivu-badge-dot {
  top: -12px;
  right: 0px;
}
</style>