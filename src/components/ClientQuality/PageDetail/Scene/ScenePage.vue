<template>
  <Page class="page-position" :current="current" :total="total" @on-change="changePage" :page-size="pageSize" show-total />
</template>
<script>
export default {
  name: 'ScenePage',
  props: ['total'],
  data() {
    return {
      businessId: this.$route.params.pageId,
      pageSize: 15
    }
  },
  computed: {
    current: function () {
      return this.$store.state.currentPage
    }
  },
  methods: {
    changePage(page) {
      this.$store.commit('setCurrentPage', page)
      this.$store.dispatch('getSceneList')
    }
  }
}
</script>
<style scoped>
.page-position {
  margin-top: 20px;
  text-align: right;
  margin-right: 80px;
}
</style>
