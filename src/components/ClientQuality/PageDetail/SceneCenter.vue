<template>
  <div>
    <Alert v-if="defaultScenes&&defaultScenes.length>0" show-icon closeable>检测到存在未分组的场景，请尽快处理</Alert>
    <SceneFilter :pageInfo="pageInfo" :permission="permission"></SceneFilter>
    <Row class="no-info" v-if="sceneList&&sceneList.length===0">
      <img class="error-img" src="/static/img/cqp-error.jpg" />
      <br>
      <span>暂无场景信息</span>
    </Row>
    <SceneList :sceneList="sceneList" :businessId="pageInfo.businessId" :permission="permission"></SceneList>
    <Spin size="large" fix v-if="loading"></Spin>
    <ScenePage :total="total"></ScenePage>
    <div v-if="defaultScenes&&defaultScenes.length>0">
      <p class="title">
        <span>未分组场景</span>
        <Tooltip transfer="true" content="多选/取消" placement="top">
          <Button v-if="permission" icon="md-checkbox-outline" type="text" @click="editUnderTable"></Button>
        </Tooltip>
      </p>
      <SceneList ref="underTable" type="defalut" :sceneList="defaultScenes" :businessId="pageInfo.businessId" :permission="permission"></SceneList>
    </div>
    <EditDrawer></EditDrawer>
    <EditDrawerList></EditDrawerList>
  </div>
</template>
<script>
import SceneFilter from './Scene/Filter'
import SceneList from './Scene/SceneList'
import ScenePage from './Scene/ScenePage'
import EditDrawer from '../baseComponents/EditDrawer'
import EditDrawerList from '../baseComponents/EditDrawerList.vue'
export default {
  name: 'SceneCenter',
  props: ['tab', 'pageInfo'],
  components: { SceneFilter, SceneList, ScenePage, EditDrawer, EditDrawerList },
  data() {
    return {}
  },
  watch: {
    pageInfo: {
      handler(newVal, oldVal) {
        if (newVal && newVal.hasOwnProperty('businessId')) {
          this.$store.dispatch('getDefalutScenes', newVal.businessId)
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.$store.dispatch('getSceneList')
  },
  computed: {
    loading() {
      return this.$store.state.clientQuality.isLoading
    },
    sceneList() {
      return this.$store.state.clientQuality.sceneList.sceneList
    },
    total() {
      return this.$store.state.clientQuality.sceneList.total
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    },
    defaultScenes() {
      return this.$store.state.clientQuality.defaultScenes.sceneList
    }
  },
  methods: {
    editUnderTable() {
      this.$refs.underTable.editTable()
    }
  }
}
</script>
<style scoped>
.no-info {
  text-align: center;
  padding-bottom: 20px;
  color: #464c5b;
  font-size: medium;
  font-weight: initial;
  display: block;
}
.error-img {
  width: 200px;
}
.title {
  margin-left: 20px;
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #464c5b;
}
</style>