<template>
  <Form :model="editInfo" :rules="ruleValidate" ref="refEditInfo" :label-width="80" label-position="left" style="width:600px;margin-top:20px">
    <FormItem label="页面ID">
      <strong>{{editInfo.id}}</strong>
    </FormItem>
    <FormItem label="页面名" prop="name">
      <Input v-model="editInfo.name" />
    </FormItem>
    <FormItem label="项目分组">
      <Tag v-for="category in categorySelect" :key="category.value" :name="category.value" type="border" closable color="blue" @on-close="deleteCategory">
        {{category.label}}
      </Tag>
      <Select v-model="categoryType" clearable placeholder="请选择分组类型">
        <Option v-for="item in implList" :key="item" :value="item">{{item}}</Option>
      </Select>
      <Select filterable :label-in-value="true" @on-change="categroyChange" placeholder="选择以新增分组">
        <Option v-for="item in categoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
    </FormItem>
    <FormItem label="优先级" required>
      <Select v-model="editInfo.priority">
        <Option :value="1" :key="1">P1</Option>
        <Option :value="2" :key="2">P2</Option>
        <Option :value="3" :key="3">P3</Option>
        <Option :value="4" :key="4">P3</Option>
      </Select>
    </FormItem>
    <FormItem label="备注">
      <Input v-model="editInfo.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}">
      </Input>
    </FormItem>
    <FormItem>
      <Button type="primary" size="small" @click="submit">Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="toView">Cancel</Button>
    </FormItem>
  </Form>

</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'InfoEdit',
  props: ['editInfo'],
  data() {
    return {
      categoryType: 'MRN',
      ruleValidate: {
        name: [
          {
            required: true,
            type: 'string',
            message: '页面名不可以为空',
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (this.editInfo.name.trim() === '') {
                return callback(new Error('页面名不可以为空'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      categorySelect: []
    }
  },
  mounted() {
    this.$store.dispatch('getCategoryTypeList')
    for (let cat of this.editInfo.categories) {
      let category = {}
      category.value = cat.id
      category.label = cat.name
      this.categorySelect.push(category)
    }
  },
  watch: {
    categoryType() {
      this.$store.dispatch('getMRNList', this.categoryType)
    }
  },
  computed: {
    categoryList() {
      return this.$store.state.clientQuality.mrnList
    },
    implList() {
      return this.$store.state.clientQuality.categoryType
    }
  },
  methods: {
    deleteCategory(event, val) {
      for (let index in this.categorySelect) {
        if (this.categorySelect[index].value === val) {
          this.categorySelect.splice(index, 1)
          break
        }
      }
    },
    categroyChange(val) {
      let flag = 1
      for (let cat of this.categorySelect) {
        if (cat.value === val.value) {
          flag = 0
          break
        }
      }
      if (flag === 1) {
        this.categorySelect.push(val)
      }
    },
    toView() {
      Bus.$emit('cancelEdit')
    },
    submit() {
      this.editInfo.categoryId = []
      for (let cat of this.categorySelect) {
        this.editInfo.categoryId.push(cat.value)
      }
      this.editInfo.categoryId.toString()
      this.$refs['refEditInfo'].validate(async (valid) => {
        if (valid) {
          this.$store.dispatch('setPageInfo', this.editInfo)
          Bus.$emit('submitEdit')
        } else {
        }
      })
    }
  }
}
</script>
<style scoped>
</style>