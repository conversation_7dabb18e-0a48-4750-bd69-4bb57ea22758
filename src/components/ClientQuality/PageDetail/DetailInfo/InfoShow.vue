<template>
  <Form :model="pageInfo" :label-width="80" label-position="left" style="width:600px;margin-top:20px">
    <FormItem label="页面ID">
      <strong>{{pageInfo.id}}</strong>
    </FormItem>
    <FormItem label="页面名">
      <span>{{pageInfo.name}}</span>
    </FormItem>
    <FormItem label="项目分组">
      <Tooltip v-for="category in pageInfo.categories" :key="category.id" :content="category.type" placement="top">
        <Tag :color="getTagColor(category.type)">{{category.name}}</Tag>
      </Tooltip>
    </FormItem>
    <FormItem label="在线状态">
      <Badge v-if="pageInfo&&pageInfo.online===1" text="ON" type="success"></Badge>
      <Badge v-else text="OFF"></Badge>
    </FormItem>
    <FormItem label="优先级">
      <Tag v-if="pageInfo.priority === 1" color="volcano">P{{ pageInfo.priority }}</Tag>
      <Tag v-else color="blue">P{{ pageInfo.priority }}</Tag>
    </FormItem>
    <FormItem label="备注">
      <p>{{pageInfo.remark}}</p>
    </FormItem>
  </Form>
</template>
<script>
export default {
  name: 'InfoShow',
  props: ['pageInfo'],
  data() {
    return {}
  },
  methods: {
    getTagColor(type) {
      if (type === 'MRN') {
        return 'blue'
      } else if (type === 'H5') {
        return 'geekblue'
      } else if (type === 'Naitve') {
        return 'cyan'
      } else if (type === 'MAX') {
        return 'purple'
      } else if (type === 'WMP') {
        return 'green'
      } else if (type === 'MMP') {
        return 'orange'
      } else {
        return 'default'
      }
    }
  }
}
</script>
<style scoped>
</style>