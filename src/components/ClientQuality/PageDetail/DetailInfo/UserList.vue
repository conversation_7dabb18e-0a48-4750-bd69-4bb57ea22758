<template>
  <div>
    <Row class="user-cell">
      <Col span="2">
      <strong>PM 负责人: </strong>
      </Col>
      <Col span="22">
      <Member :userList="pm" role="PM" :permission="permission"></Member>
      </Col>
    </Row>
    <Row class="user-cell">
      <Col span="2">
      <strong>RD 负责人:</strong>
      </Col>
      <Col span="22">
      <Member :userList="rd" role="RD" :permission="permission"></Member>
      </Col>
    </Row>
    <Row class="user-cell">
      <Col span="2">
      <strong>QA 负责人:</strong>
      </Col>
      <Col span="22">
      <Member :userList="qa" role="QA" :permission="permission"></Member>
      </Col>
    </Row>
    <Row class="user-cell">
      <Col span="2">
      <strong>UI 负责人:</strong>
      </Col>
      <Col span="22">
      <Member :userList="ui" role="UI" :permission="permission"></Member>
      </Col>
    </Row>
  </div>
</template>
<script>
import Member from './member'
export default {
  name: 'UserList',
  props: ['pageUsers', 'permission'],
  components: { Member },
  data() {
    return {
      pm: [],
      rd: [],
      qa: [],
      ui: []
    }
  },
  watch: {
    pageUsers: {
      handler(newVal, oldVal) {
        this.pm = []
        this.rd = []
        this.qa = []
        this.ui = []
        for (let user of newVal) {
          if (user.role === 'PM') {
            this.pm.push(user)
          } else if (user.role === 'RD') {
            this.rd.push(user)
          } else if (user.role === 'QA') {
            this.qa.push(user)
          } else if (user.role === 'UI') {
            this.ui.push(user)
          }
        }
      },
      immediate: true
    }
  }
}
</script>
<style scoped>
.user-cell {
  margin-bottom: 20px;
}
</style>