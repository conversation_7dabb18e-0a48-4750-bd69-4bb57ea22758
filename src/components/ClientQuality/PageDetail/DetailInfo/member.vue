<template>
  <div>
    <Tag v-for="item in userList" :key="item.id" :name="item.misId" type="border" :closable="permission" @on-close="deleteUser(item.id)">{{item.misId}}</Tag>
    <div v-if="isEdit" style="width:200px">
      <Input class="suffix" v-model="misId" placeholder="请输入MIS ID" size="small">
      <ButtonGroup size="small" slot="suffix">
        <Button type="primary" @click="addUser()">确定</Button>
        <Button @click="cancelEdit()">取消</Button>
      </ButtonGroup>
      </Input>
    </div>
    <Button v-else-if="permission" icon="ios-add" type="dashed" size="small" @click="add">Add</Button>
  </div>
</template>
<script>
export default {
  name: 'member',
  props: ['userList', 'role', 'permission'],
  data() {
    return {
      isEdit: false,
      misId: ''
    }
  },
  methods: {
    async deleteUser(id) {
      await this.$store.dispatch('deletePageUser', id)
      this.$store.dispatch('getPageInfoById', this.$route.params.pageId)
    },
    add() {
      this.isEdit = true
    },
    cancelEdit() {
      this.misId = ''
      this.isEdit = false
    },
    async addUser() {
      if (this.misId.trim() === '') {
        this.$Message.error('MIS ID 不可以为空')
      } else {
        let params = {
          id: 0,
          pageId: this.$route.params.pageId,
          misId: this.misId,
          role: this.role
        }
        await this.$store.dispatch('setPageUser', params)
        this.isEdit = false
        this.$store.dispatch('getPageInfoById', this.$route.params.pageId)
      }
    }
  }
}
</script>
<style scoped>
.suffix /deep/ .ivu-input-suffix {
  width: 88px;
}
</style>