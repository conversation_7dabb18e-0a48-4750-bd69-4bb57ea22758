<template>
  <div>
    <Row class="pro-info" v-if="!isEdit">
      <Col span="4">
      <Badge status="success" />
      <strong>{{product.product.label}}</strong>
      </Col>
      <Col span="20">
      <Row>
        <Col span="6"><span class="sub-title">产物:</span>{{product.implement}}</Col>
        <Col span="4">
          <span>是否继承</span>
          <Tooltip placement="top">
            <template #content>
                <p>注意：开关开启后无法关闭</p>
            </template>
            <i-switch v-model="product.isSuper" :before-change="handleBeforeChange" @on-change="openSuperSwitch" size="small" :true-value="1" :false-value="0" /> 
          </Tooltip>
        </Col>
        <Col span="4">
          <span>场景数量：</span>
          {{ this.product.sceneProductCount }}
        </Col>
        <Col span="4">
        <ButtonGroup v-if="permission">
          <Button icon="ios-trash" size="small" type="primary" ghost @click="deletePageProductWarning" :disabled="this.product.isSuper == 0 && this.product.sceneProductCount > 0"></Button>
        </ButtonGroup>
        </Col>
        <Modal v-model="modal" width="360">
        <template #header>
          <p style="color:#f60;text-align:center">
            <Icon type="ios-information-circle"></Icon>
            <span>提醒</span>
          </p>
        </template>
        <div style="text-align:center">
          <p>你确定要删除吗？</p>
        </div>
        <template #footer>
          <Button :loading="modal_loading" @click="cancelModal">取消</Button>
          <Button type="error" :loading="modal_loading" @click="deletePageProduct">删除</Button>
        </template>
      </Modal>
      </Row>
      </Col>
    </Row>
    <Form v-else :label-width="100" :model="editInfo" label-position="right" inline style="margin-top: 30px;">
      <FormItem label="选择产品" required>
        <Select v-model="editInfo.productId" filterable transfer placeholder="Select" @on-change="change" :max-tag-count=2>
          <Option v-for="pro in allProduct" :value="pro.id" :key="pro.id">{{ pro.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="产物" required>
        <Select v-model="editInfo.implement" filterable transfer placeholder="Select">
          <Option v-for="(impl,index) in implList" :value="impl" :key="index">{{ impl }}</Option>
        </Select>
      </FormItem>
      <FormItem :label-width="10" class="submit">
        <Button type="primary" size="small" @click="submit">Submit</Button>
        <Button size="small" style="margin-left: 8px" @click="toView">Cancel</Button>
      </FormItem>
    </Form>
    <Modal v-model="loading" :closable="false" :mask-closable="false">
      <template #header>
        <p style="color:#f60;text-align:center">
          <Icon type="ios-information-circle"></Icon>
          <span>注意</span>
        </p>
      </template>
      <p>【场景-产品】数据同步中，本提示框消失前请勿关闭本窗口</p>
      <template #footer>
        <Button disabled>请等待</Button>
      </template>
    </Modal>

  </div>
</template>
<script>
export default {
  name: 'ProductSelect',
  props: ['product'],
  data() {
    return {
      modal: false,
      isEdit: false,
      editInfo: {},
      implList: [],
      loading: false
    }
  },
  mounted() {
    this.initPoruct()
  },
  computed: {
    allProduct() {
      return this.$store.state.clientQuality.productList
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    initPoruct() {
      if (!this.product.id) {
        // 新增场景
        this.isEdit = true
        this.editInfo.productId = 0
        if (this.allProduct.length === 0) {
          this.$store.dispatch('getProductList')
        }
      } else {
        this.implList = this.product.product.implList
        this.editInfo.id = this.product.id
        this.editInfo.productId = this.product.productId
      }
      this.editInfo.pageId = this.$route.params.pageId
      this.editInfo.implement = this.product.implement
    },
    edit() {
      if (this.allProduct.length === 0) {
        this.$store.dispatch('getProductList')
      }
      this.isEdit = true
      this.initPoruct()
    },
    cancelModal() {
      this.modal = false
    },
    deletePageProductWarning() {
      this.modal = true
    },
    async deletePageProduct() {
      this.modal = false
      await this.$store.dispatch('deletePageProduct', this.product.id)
      this.$store.dispatch('getPageInfoById', this.$route.params.pageId)
    },
    change(productId) {
      for (let pro of this.allProduct) {
        if (pro.id === productId) {
          this.implList = pro.implList
        }
      }
    },
    toView() {
      if (this.product.id) {
        this.isEdit = false
      } else {
        this.$emit('submit', false)
      }
    },
    async submit() {
      if (
        this.editInfo.productId === 0 ||
        this.editInfo.implement === undefined
      ) {
        this.$Message.error('请完善必填信息')
      } else {
        this.loading = true
        await this.$store.dispatch('setPageProduct', this.editInfo)
        this.loading = false
        this.$store.dispatch('getPageInfoById', this.$route.params.pageId)
        this.$emit('submit', false)
        this.isEdit = false
        this.loading = false
      }
    },
    handleBeforeChange () {
      return new Promise((resolve) => {
        if (this.product.isSuper === 0) {
          resolve()
        }
      })
    },
    async openSuperSwitch() {
      await this.$store.dispatch('setPageProductSwitch', this.product)
      this.$store.dispatch('getPageInfoById', this.$route.params.pageId)
    }
  }
}
</script>
<style scoped>
.pro-info {
  margin-top: 30px;
  margin-bottom: 5px;
}
.sub-title {
  font-weight: 500;
  margin-right: 20px;
  font-size: 12px;
}
.test-check-box {
  margin-right: 30px;
}
.submit {
  margin-right: 100px;
  float: right;
}
</style>