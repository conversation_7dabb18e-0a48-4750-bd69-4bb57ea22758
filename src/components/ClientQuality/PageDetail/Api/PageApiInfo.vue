<template>
  <div>
    <Row :gutter="16">
      <Col span="22">
        <Alert show-icon>若需要新增接口，请前往
          <!-- <a :href="businessApiLink()" target="_blank">{{businessInfo.label}}>接口信息</a> -->
          <router-link :to="{path : businessApiLink()}" tag="a" target="_blank">
            {{businessInfo.label}}>接口信息
          </router-link>
        </Alert>
      </Col>
      <Col>
        <Button icon="md-add" type="success" @click="show" :disabled="!permission">关联接口</Button>
      </Col>
    </Row>
    <PageRelateApiModal :show="showModal" @confirm="apiSubmit" :apiList="businessApiList" :pageInfo="pageInfo"></PageRelateApiModal>
    <Spin size="large" fix v-if="isLoading"></Spin>
    <ApiListInfo v-else :apiList="apiList" type='page' @refreshApiList="getPageApiList(query)" :permission="permission">
    </ApiListInfo>
    <ApiListPageCount style="margin-top:15px" @transferPage="getPage" :total="total" :currentPage="query.pageCount">
    </ApiListPageCount>
  </div>
</template>

<script>
import ApiListPageCount from '../../BusinessDetail/ApiList/ApiListPageCount'
import ApiListInfo from '../../BusinessDetail/ApiList/ApiListInfo'
import PageRelateApiModal from './PageRelateApiModal'
export default {
  name: 'PageApiInfo',
  props: ['tab'],
  components: { ApiListPageCount, ApiListInfo, PageRelateApiModal },
  data() {
    return {
      query: {
        pageId: this.$route.params.pageId,
        pageCount: 1,
        pageSize: 10
      },
      isLoading: true,
      total: 0,
      isAdd: false,
      showModal: false,
      businessApiList: [],
      apiList: []
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.getPageApiList(this.query)
  },
  computed: {
    pageInfo() {
      return this.$store.state.clientQuality.pageInfo.pageInfo
    },
    businessInfo() {
      return this.$store.state.clientQuality.pageInfo.businessInfo
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    getApiList(query) {
      return this.$axios({
        method: 'get',
        params: query,
        url: this.env.url + 'api/getApiList'
      }).then((res) => {
        this.isLoading = false
        return res.data
      }).catch(error => {
        console.log(error)
      })
    },
    businessApiLink() {
      let businessId = this.businessInfo.id
      return '/client/businessDetail/' + businessId + '/api'
      // let routeData = this.$router.resolve({
      //   path: 'client/businessDetail/' + this.businessInfo.id + '/api'
      // })
      // window.open(routeData.href, '_blank')
    },
    async getPageApiList(query) {
      let message = await this.getApiList(query)
      this.apiList = message.data
      this.total = message.total
    },
    relateApi(apiId, pageId) {
      this.$axios({
        method: 'put',
        data: {
          apiId: apiId,
          pageId: pageId
        },
        url: this.env.url + 'api/page'
      }).then((res) => {
        this.isAdd = false
        this.isLoading = true
        this.getPageApiList(this.query)
      }).catch(error => {
        console.log(error)
      })
    },
    getPage(msg) {
      this.query.pageCount = msg
      this.isLoading = true
      this.getPageApiList(this.query)
    },
    addPageProduct(row) {
      this.isAdd = true
      this.selectApi.push(row.id)
    },
    apiSubmit(selectApiList) {
      this.showModal = false
      for (let i = 0; i < selectApiList.length; i++) {
        this.relateApi(selectApiList[i].value, this.pageInfo.id)
      }
    },
    async show() {
      let query = {}
      query.businessId = this.pageInfo.businessId
      let message = await this.getApiList(query)
      this.businessApiList = message.data
      this.showModal = true
    }
  }
}
</script>