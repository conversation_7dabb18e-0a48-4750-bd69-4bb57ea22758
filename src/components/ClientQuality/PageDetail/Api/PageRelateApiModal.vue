<template>
  <Drawer :closable="false" v-model="show" width="30" @on-close="cancel">
    <span class="title">
      请选择需要关联的接口
    </span>
    <Button class="edit-submit" type="primary" size="small" @click.native="confirm" :disabled="disabledSubmit">确定</Button>
    <Divider size="small" />
    <Form label-position="top">
      <FormItem style="margin-bottom:30px">
        <span slot="label">已选择{{selectApiList.length}}个接口
          <Tag color="blue">提示：所选择接口默认与页面相关产品关联</Tag>
        </span>
        <Tag v-for="apiItem in selectApiList" :key="apiItem.value" type="border" closable color="blue" @on-close="deleteApi(apiItem)">
          {{ apiItem.label }}
        </Tag>
      </FormItem>
      <FormItem label="请选择接口" required>
        <Select @on-change="apiChange" filterable placeholder="选择接口" :label-in-value=true>
          <Option v-for="item in apiList" :value="item.id" :key="item.id">{{item.apiPath}}</Option>
        </Select>
      </FormItem>
    </Form>
  </Drawer>
</template>

<script>
export default {
  name: 'PageRelateApiModal',
  props: ['show', 'apiList', 'pageInfo'],
  data() {
    return {
      selectApiList: [],
      disabledSubmit: true,
      query: {
        businessId: 0
      },
      title: '请选择关联至 ' + this.pageInfo.name + ' 的接口'
    }
  },
  methods: {
    apiChange(val) {
      let flag = 0
      for (let i = 0; i < this.selectApiList.length; i++) {
        if (val.value === this.selectApiList[i].value) {
          flag = 1
          break
        }
      }
      if (flag === 0) {
        this.selectApiList.push(val)
      }
      this.confirmButton()
    },
    deleteApi(apiItem) {
      let li = []
      li = this.selectApiList.filter(item => item.value !== apiItem.value)
      this.selectApiList = li
      this.confirmButton()
    },
    confirmButton() {
      if (this.selectApiList.length <= 0) {
        this.disabledSubmit = true
      } else {
        this.disabledSubmit = false
      }
    },
    confirm() {
      this.$emit('confirm', this.selectApiList)
      this.selectApiList = []
      this.disabledSubmit = true
    },
    cancel() {
      this.selectApiList = []
      this.disabledSubmit = true
      this.$emit('confirm', this.selectApiList)
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: 500;
  vertical-align: middle;
}
.edit-submit {
  float: right;
  margin-right: 20px;
}
</style>
