<template>
  <Collapse class="col-list" simple>
    <Panel v-for="item in businessList" :key="item.id">
      <Button class="business-title" type="text" :to="'client/businessDetail/'+item.business.id+'/page'">{{item.business.label}}</Button>
      <div class="page-count">
        <span style="color:#9ea7b4">负责页面</span>
        <strong style="color:#19be6b">{{item.pageCount}}</strong>
        <span style="color:#9ea7b4">个</span>
      </div>
      <div slot="content">
        <p v-if="item.pageList.length===0" class="extra">该方向下暂无负责页面，请进入方向详情页查看</p>
        <CellGroup>
          <Cell v-for="page in item.pageList" :key="page.id" :to="'client/page/' + page.id + '/scene'">
            <Badge v-if="page.online===1" status="success" />
            <Badge v-else status="error" />
            <strong>{{page.name}}</strong>
            <p class="page-remark">{{page.remark}}</p>
          </Cell>
        </CellGroup>
        <p v-if="item.pageCount>5" class="extra">更多页面请进入方向详情页查看</p>
      </div>
    </Panel>
  </Collapse>

</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'UserBusiness',
  data() {
    return {
      column: [{ title: 'page', slot: 'page' }]
    }
  },
  mounted() {
    this.$store.dispatch('getBusinessByUser', Bus.userInfo.userLogin)
  },
  computed: {
    businessList() {
      return this.$store.state.clientQuality.businessList
    }
  },
  methods: {
    toPage(row) {
      console.log(row)
      this.$router.push('client/page/' + row.id + '/scene')
    }
  }
}
</script>
<style scoped>
.col-list {
  border: none;
  text-align: left;
}
.business-title {
  font-size: 14px;
  font-weight: 500;
}
.page-count {
  float: right;
  font-size: xx-small;
}
.page-remark {
  margin-left: 20px;
  font-size: xx-small;
  color: #9ea7b4;
}
.extra {
  font-size: xx-small;
  color: #9ea7b4;
  text-align: center;
}
</style>