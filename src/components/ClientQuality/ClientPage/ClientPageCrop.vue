<template>
  <div>
    <Tabs type="card" closable v-model="focusConfigName" @on-tab-remove="removeConfig" @on-click="focusTab" :before-remove="beforeRemoveConfig">
      <template v-for="(config, ci) in pageConfigs" >
        <TabPane :name="config.name" :key="ci + config.id" :label="config.name">
        </TabPane>
      </template>
      <template #extra>
          <Modal
            v-model="copyConfigModal"
            title="复制新增配置"
            width="800"
            @on-cancel="resetCopyModal; copyConfigModal = false;">
            <RadioGroup v-model="copyMode" class="copy-mode-group">
              <Radio label="current">复制当前配置</Radio>
              <Radio label="search">搜索其他场景配置</Radio>
            </RadioGroup>

            <!-- 当前配置复制区域 -->
            <div v-if="copyMode === 'current'">
              <Form :label-width="90" style="margin-top:20px">
                <FormItem label="新配置名称" style="flex: 1;">
                  <Input v-model="newConfigName" placeholder="请输入新配置名称"/>
                </FormItem>
              </Form>
            </div>

            <!-- 场景搜索区域 -->
            <div v-else style="margin-top:20px">
              <!-- 仅复制交互操作开关 -->
              <div class="compact-switch-wrapper">
                <span class="switch-label">仅复制交互操作配置</span>
                <i-switch
                  v-model="isOnlyOperations"
                  size="small"
                  class="right-aligned-switch"/>
              </div>
              <Input search enter-button="搜索"
                     placeholder="请输入配置名称(名称尽量完整)"
                     v-model="searchKeyword"
                     @on-change="handleSearchChange"
                     @on-search="handleSceneSearch"/>
              <div class="scene-list" v-if="sceneResults.length > 0">
                <!-- 场景列表 -->
                <div v-for="scene in sceneResults" :key="scene.name"
                     class="scene-item" @click="handleSelectScene(scene)">
                  <div class="scene-meta">
                    <div class="scene-name">{{ scene.name }}</div>
                    <div class="scene-id">场景ID: {{ scene.sceneId }}</div>
                  </div>
                  <div class="scene-actions">{{ scene.actions.config.length }} 个配置</div>
                </div>

                <!-- 分页控制 -->
                <div class="pagination-controls">
                  <div v-if="hasMore" class="load-more">
                    <Button :loading="loading" @click="handleSceneSearch()">
                      {{ loading ? '加载中...' : '点击加载更多' }}
                    </Button>
                  </div>
                  <div v-else class="no-more">
                    没有更多数据了
                  </div>
                  <Spin v-if="loading" size="large" class="loading-spin"></Spin>
                </div>
              </div>

              <!-- 搜索结果为空时的提示 -->
              <div v-else-if="!loading" class="empty-tips">
                未找到相关场景配置
              </div>

              <!-- 配置选择模态框 -->
              <Modal
                v-model="configSelectModal"
                title="选择要复制的配置"
                @on-ok="handleCopySelectedConfigs">
                <CheckboxGroup v-model="selectedConfigs">
                  <div v-for="config in currentSceneConfigs" :key="config.id"
                       class="config-item">
                    <Checkbox :label="config.id.toString()">{{ config.name }}</Checkbox>
                  </div>
                </CheckboxGroup>
              </Modal>
            </div>

            <template #footer>
              <Button @click="resetCopyModal;  copyConfigModal = false;">取消</Button>
              <Button type="primary" @click="handleConfirmCopy">{{ copyMode === 'current' ? '立即复制' : '确认选择' }}</Button>
            </template>
           <div v-if="newConfigName === defaultConfigName" class="word-tips" style="color:black">* 基准图查找标识: {{mockId}}</div>
           <div v-else class="word-tips" style="color:black">* 基准图查找标识: {{mockId}}_{{newConfigId}}</div>
          </Modal>
          <Button type="primary" @click="handleConfigAdd" size="small">复制配置</Button>
          <Button type="primary" @click="savePageInfo" size="small">保存配置</Button>
      </template>
    </Tabs>
    <div v-if="curConfig" style="min-width:1445px;">
      <div style="text-align: left;margin-bottom: 5px;">
        <Tag color="geekblue" v-if="editConfigName == false" @click.native="startEditConfigName(curConfig.name)">{{curConfig.name}} <Icon type="md-create" /></Tag>
        <Input v-else v-model="newConfigName" style="width: 400px;">
            <template #suffix>
                <Icon type="ios-archive" @click.native="updateConfigName(curConfig)" />
            </template>
        </Input>
        <Tag color="geekblue" v-if="curConfig.id === ''">基准图查找标识: {{mockId}}</Tag>
        <Tag color="geekblue" v-else>基准图查找标识: {{mockId}}_{{curConfig.id}}</Tag>
      </div>
      <Card class="content-container">
        <template #title>
            <div class="title">
              <span>交互操作配置</span>
              <a href="https://km.sankuai.com/page/1335625987" target="_blank">
                <Icon type="md-help-circle" />
              </a>
            </div>
        </template>
        <div class="content">
          <div class="config-item">
            <span class="title">抵达被测页面交互配置</span>
            <div>
              <transition-group name="drag" class="action-list" tag="ul">
                <li @dragenter="dragEnter($event, index, curConfig)"
                  @dragover="dragOver($event, index)"
                  @dragstart="dragStart(index)"
                  draggable
                  v-for="(item, index) in curConfig.list"
                  :key="0+index"
                  class="action-item"
                >
                  <Form :label-width="40" inline>
                    <div style="display: inline-flex;">
                      <div style="min-width:1350px">
                        <!-- 操作序号 -->
                        <FormItem style="width:80px;">
                          <span>{{index + 1}}</span>
                        </FormItem>
                        <!-- 操作名称 -->
                        <FormItem v-if="item.type === 'click'" label='点击' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'wait'" label='等待' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'swipe-next-screen'" label='滑屏' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'swipe-up'" label='上滑' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'swipe-down'" label='下滑' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'swipe-left'" label='左滑' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'swipe-right'" label='右滑' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'drop-down'" label='下拉' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'input'" label='输入' style="width:40px;"></FormItem>
                        <FormItem v-if="item.type === 'agent'" label='自然语言指令' style="width:120px; white-space: nowrap;"></FormItem>

                        <!-- 操作目标 -->
                        <template v-if="['drop-down', 'wait', 'swipe-next-screen', 'agent'].includes(item.type)"></template>
                        <template v-else>
                          <FormItem v-if="item.target.type === 'text'" label="目标-文本:" :label-width="80">
                            <Input placeholder="交互文本，支持Python正则匹配" type="text" style="width:350px" v-model="item.target.value" @on-change="actionCheck(item)"></Input>
                          </FormItem>
                          <FormItem v-if="item.target.type === 'image'" label="目标-语义:" :label-width="80">
                            <img :src="item.target.value" width="50px" height="50px" />
                            <Button type="text" icon="ios-create-outline" @click="showImageCropper(curConfig, index, 'action')"></Button>
                          </FormItem>
                          <FormItem v-if="item.target.type === 'image'" label="目标-描述:" :label-width="80">
                            <Input type="text" v-model="item.target.description"></Input>
                          </FormItem>
                          <FormItem v-if="item.target.type === 'screen'" label="目标-屏幕左上角" :label-width="120" style="width:435px">
                          </FormItem>
                          <FormItem v-if="['image', 'text'].includes(item.target.type)" label="元素查找:" :label-width="80">
                            <div style="display: flex;">
                              <Input type="number" style="width:50px" v-model="item.target.index" @on-blur="actionCheck(item)"></Input>
                              <span style="line-height: 2;font-size: larger;margin: 0 5px;">/</span>
                              <Input type="number" style="width:50px" v-model="item.target.count" @on-blur="actionCheck(item)"></Input>
                              <Tooltip content="" placement="top-end">
                                <template #content>
                                  <p style="font-size: small;">查找第几个元素/总共有多少个相同元素</p>
                                </template>
                                <Icon style="line-height: 2;" color="primary" type="md-help-circle" />
                              </Tooltip>
                            </div>
                          </FormItem>
                          <FormItem label="目标-偏移:" :label-width="80">
                            <div style="display: flex;">
                              <input type="number" step="0.1" min="0" max="1" style="width:60px" class="ivu-input ivu-input-default" v-model="item.target.offset.x" @on-blur="actionCheck(item)">
                              <input type="number" step="0.1" min="0" max="1" style="width:60px" class="ivu-input ivu-input-default" v-model="item.target.offset.y" @on-blur="actionCheck(item)">
                              <Tooltip content="" placement="top-end">
                                <template #content>
                                  <p style="font-size: small;">偏移距离，支持像素或屏幕宽高倍数配置</p>
                                </template>
                                <Icon style="line-height: 2;" color="primary" type="md-help-circle" />
                              </Tooltip>
                            </div>
                          </FormItem>
                        </template>
                        <!-- 操作参数 -->
                        <FormItem v-if="['swipe-up', 'swipe-down', 'swipe-left', 'swipe-right'].includes(item.type)" label="滑动距离:" :label-width="80">
                          <input type="number" step="0.1" min="0" max="1" style="width:60px" class="ivu-input ivu-input-default" v-model="item.params.distance" @on-blur="actionCheck(item)">
                          <Tooltip content="" placement="top-end">
                            <template #content>
                              <p style="font-size: small;">滑动距离，支持像素或屏幕宽高倍数配置</p>
                            </template>
                            <Icon style="line-height: 2;" color="primary" type="md-help-circle" />
                          </Tooltip>
                        </FormItem>
                        <FormItem v-if="['swipe-up', 'swipe-down', 'swipe-left', 'swipe-right', 'swipe-next-screen'].includes(item.type)"  label="连续操作次数:" :label-width="100">
                          <input type="number" step="1" min="0" style="width:40px" class="ivu-input ivu-input-default" v-model="item.repeatCount" @on-blur="actionCheck(item)">
                        </FormItem>
                        <FormItem v-if="item.type === 'wait'" label="等待时间(s):" :label-width="100">
                          <input type="number" step="1" min="1" style="width:80px" class="ivu-input ivu-input-default" v-model="item.params.waitTime" @on-blur="actionCheck(item)">
                        </FormItem>
                        <FormItem v-if="item.type === 'input'" label="输入文本(English):" :label-width="140">
                          <Input type="text" v-model="item.params.text" @on-change="actionCheck(item)"></Input>
                        </FormItem>
                        <FormItem v-if="item.type === 'agent'" label="" :label-width="0">
                          <Input type="text" v-model="item.params.text" @on-change="actionCheck(item)" style="width:350px" placeholder="如：点击右下角的加号按钮"></Input>
                          <a href="https://km.sankuai.com/collabpage/2702787421" target="_blank" style="margin-left: 5px;">
                            <Tag color="green">
                              <strong>说明</strong>
                            </Tag>
                          </a>
                        </FormItem>
                      </div>
                      <FormItem>
                        <Button class="btn-remove" icon="md-remove" type="error" shape="circle" @click="removeAction(index, curConfig)"></Button>
                      </FormItem>
                    </div>
                  </Form>
                </li>
              </transition-group>
            </div>
          </div>
          <div class="config-item">
            <span class="title">操作后滑屏(长图拼接)</span>
            <Input style="width:80px;" type="number" v-model="curConfig.scrollCount"></Input>
          </div>
          <div v-if="attachments[curConfig.id]" style="text-align: right; padding:0px 100px;">
            <Button type="primary" @click="createAction(curConfig)"  size="small">新增交互</Button>
            <Modal
              v-model="actionModal"
              title="添加交互元素配置"
              @on-ok="pushAction(curConfig)"
              @on-cancel="actionModal=false">
              <Form :label-width="120">
                <FormItem label="交互类型">
                  <Select v-model="newAction.type" style="width:200px">
                      <Option value="click" key="click">点击（click）</Option>
                      <Option value="wait" key="wait">等待（wait）</Option>
                      <Option value="swipe-next-screen" key="swipe-next-screen">滑到下一屏（swipe-next-screen）</Option>
                      <Option value="swipe-up" key="swipe-up">上滑（swipe-up）</Option>
                      <Option value="swipe-down" key="swipe-down">下滑（swipe-down）</Option>
                      <Option value="swipe-left" key="swipe-left">左滑（swipe-left）</Option>
                      <Option value="swipe-right" key="swipe-right">右滑（swipe-right）</Option>
                      <Option value="drop-down" key="drop-down">下拉（drop-down）</Option>
                      <Option value="input" key="input">输入（input）
                        <a href="https://km.sankuai.com/collabpage/2702787421" target="_blank" style="margin-left: 5px;">
                          <Tag color="error">
                            <strong>new</strong>
                          </Tag>
                        </a>
                      </Option>
                      <Option value="agent" key="agent">自然语言指令
                        <a href="https://km.sankuai.com/collabpage/2702787421" target="_blank" style="margin-left: 5px;">
                          <Tag color="error">
                            <strong>new</strong>
                          </Tag>
                        </a>
                      </Option>
                  </Select>
                </FormItem>
                <FormItem v-if="['click', 'swipe-up', 'swipe-down', 'swipe-left', 'swipe-right', 'input'].includes(newAction.type)" label="目标类型">
                  <Select v-model="newAction.target.type" style="width:200px">
                    <Option value="screen" key="screen">屏幕左上角</Option>
                    <Option value="text" key="text">文本元素</Option>
                    <Option value="image" key="image">语义元素</Option>
                  </Select>
                </FormItem>
              </Form>
            </Modal>
            <Button type="primary" @click="savePageInfo()"  size="small">保存</Button>
          </div>
        </div>
      </Card>

      <ImageCropper v-if="curAttachment" v-model="curAttachment.basePicUrl" :show="imageCropperModal" :imgNamePrefix="imgNamePrefix" :defaultImg="curAttachment.basePicUrl" :noticeMsg="noticeMsg" :cropOption="cropOption" v-on:closeModal="closeImgCropperModal($event, curConfig)"></ImageCropper>

      <Card v-if="curAttachment" class="content-container">
        <template #title>
            <div class="title">
              <span>校验辅助配置</span>
            </div>
        </template>
        <div slot="extra">
          <i-switch size="small" v-model="curAttachment.noneTestMethod" @on-change="changeNoneTestMethod($event, curConfig)" /> 不使用校验辅助方法
        </div>
        <div v-show="curAttachment.noneTestMethod">
          <div style="padding:10px 0px">
            <Alert show-icon type="warning">
              当前页面不使用任何测试结果校验辅助方法，例如：Diff（基准图比对自动标注差异）、语义断言。
            </Alert>
          </div>
        </div>
        <div v-show="curAttachment.noneTestMethod==false" class="content">
          <RadioGroup v-model="curConfig.testMethod" type="button" >
            <Radio label="Diff">
              <span>识别区/屏蔽区配置</span>
              <a href="https://km.sankuai.com/page/457492535" target="_blank">
                <Icon type="md-help-circle" />
              </a>
            </Radio>
            <Radio label="Assert">
              <span>图像/AI断言配置</span>
              <a href="https://km.sankuai.com/page/1372581059" target="_blank">
                <Icon type="md-help-circle" />
              </a>
            </Radio>
          </RadioGroup>
          <div v-if="curConfig.testMethod == 'Assert'">
            <ul class="action-list">
              <li v-for="(item, index) in curConfig.assertions"
                :key="0+index"
                class="action-item"
              >
                <div class="assertion-container">
                  <div class="assertion-info">
                    <div class="form-item"><span>{{index + 1}}</span></div>
                    <div class="form-item">
                      <span v-if="item.type !== 'ai'">图像断言</span>
                      <span v-else-if="item.type === 'ai'">AI断言
                        <a href="https://km.sankuai.com/collabpage/2702787421" target="_blank" style="margin-left: 5px;">
                          <Tag color="green">
                            <strong>说明</strong>
                          </Tag>
                        </a>
                      </span>
                    </div>
                  </div>
                  <div class="assertion-content">
                    <template v-if="item.type !== 'ai'">
                      <div class="form-item" style="width: 300px;">
                        <div class="form-item" style="width: 200px;">
                          <img :src="item.assertion" style="height: 120px; width: 100%; object-fit: contain;" />
                        </div>
                        <Button type="text" icon="ios-create-outline" @click="showImageCropper(curConfig, index, 'assertion')"></Button>
                      </div>
                      <div class="form-item" style="height: 41.5px; width: 290px; margin-top: 13px;">
                        <span>自定义阈值：</span>
                        <input type="text" v-model="item.thresh" @blur="handleThresholdBlur(item.thresh)"/>
                        <Tooltip content="" placement="top-end">
                          <template #content>
                            <p style="font-size: small;">用于判定语义断言是否通过的标准。</p>
                          </template>
                          <Icon style="line-height: 2;" color="primary" type="md-help-circle" />
                        </Tooltip>
                      </div>
                    </template>
                    <template v-else-if="item.type === 'ai'">
                      <div class="form-item ai-assertion-input">
                        <Input v-model="item.aiAssertion" type="textarea" :rows="4" placeholder="请输入自然语言断言：如检查最终页面的小地图是否展示在迪士尼附近 / 执行过程中商品价格是否一致等" />
                      </div>
                    </template>
                  </div>
                  <div class="remove-button">
                    <Button icon="md-remove" type="error" shape="circle" @click="removeAssertion(index, curConfig)"></Button>
                  </div>
                </div>
              </li>
            </ul>
            <div style="text-align: right; padding:0px 100px;">
              <Button type="primary" @click="showAssertionTypeModal" size="small">新增断言</Button>
              <Button type="primary" @click="savePageInfo()"  size="small">保存</Button>
            </div>
          </div>
          <Modal v-model="assertionTypeModal" title="选择断言类型">
            <Button @click="selectAssertionType('image')">图像断言</Button>
            <Button @click="selectAssertionType('ai')">AI断言</Button>
          </Modal>
          <div v-if="curConfig.testMethod == 'Diff'">
            <div style="padding:10px 0px">
              <template v-if="curConfig.cropRects.length <= 0">
                <Alert show-icon type="warning">
                  当前页面无识别/屏蔽区，请根据示意图进行调整和增加
                </Alert>
              </template>
            </div>
            <template v-if="loadBasePicFinish === false">
              基准图加载中...
            </template>
            <template v-else-if="loadBasePicFinish && curAttachment.basePicUrl.indexOf('task')>0">
              <Row>
                <Col :span="12">
                  <div style="width:350px;height:600px;overflow: scroll;text-align: center;margin: 0 auto">
                    <vue-cropper :img="curAttachment.basePicUrl" ref="cropperId" autoCrop centerBox></vue-cropper>
                  </div>
                </Col>
                <Col :span="2" >
                  <div style="padding-top: 150px;padding-right: 100px">
                    <div style="padding-bottom: 20px">
                      <RadioGroup v-model="currentCropType">
                        <Radio label="target">
                          <Icon type="md-eye"></Icon>
                          <span>识别区</span>
                        </Radio>
                        <Radio label="ignore">
                          <Icon type="md-eye-off"></Icon>
                          <span>屏蔽区</span>
                        </Radio>
                      </RadioGroup>
                    </div>
                    <Button style="margin-bottom: 15px" type="info" icon="md-refresh" size="small" @click="getCrop(curConfig)">更新区域</Button>
                    <Button type="error" icon="ios-trash" size="small" @click="removeCrop(curConfig)">删除区域</Button>
                    <Button style="margin-top: 30px" type="primary" icon="md-cloud-upload" size="small"
                            @click="savePageInfo()">
                      提交修改
                    </Button>
                  </div>
                </Col>
                <Col :span="10" style="padding-top: 50px;text-align: center">
                  <div style="width:200px;height:300px;margin: 0 auto;padding: 30px 0px">
                    <vue-cropper :img=crop></vue-cropper>
                  </div>
                  <div style="padding-bottom: 15px">
                    <p>当前配置区域：{{currentCropIndex+1}}</p>
                  </div>
                  <template v-for="(cropRect, cropRectIndex) in curConfig.cropRects">
                    <Button type="dashed" icon="md-crop" :key="cropRectIndex" style="margin: 0px 5px" @click="showCrop(cropRect,cropRectIndex,curConfig)" >
                      区域{{cropRectIndex+1}}
                    </Button>
                  </template>
                  <Button shape="circle" icon="md-add" @click="addCropRect(curConfig)"></Button>
                </Col>
              </Row>
            </template>
            <template v-else>
              暂未配置基准图
            </template>
          </div>
        </div>
      </Card>
      <Card v-if="curAttachment" class="content-container">
        <template #title>
            <div class="title">
              <span>遍历交互区/屏蔽区配置</span>
            </div>
        </template>
        <div style="padding:5px 0px">
          <Alert show-icon type="info">
            参考文档：<a href="https://km.sankuai.com/collabpage/2596518831" target="_blank">https://km.sankuai.com/collabpage/2596518831</a>
          </Alert>
        </div>
        <div style="padding:10px 0px">
          <template v-if="curConfig.traverseCropRects.length <= 0">
            <Alert show-icon type="warning">
              当前页面无交互/屏蔽区，请根据示意图进行调整和增加
            </Alert>
          </template>
        </div>
        <template v-if="loadBasePicFinish === false">
          基准图加载中...
        </template>
        <template v-else-if="loadBasePicFinish && curAttachment.basePicUrl.indexOf('task')>0">
          <Row>
            <Col :span="12">
              <div style="width:350px;height:600px;overflow: scroll;text-align: center;margin: 0 auto">
                <vue-cropper :img="curAttachment.basePicUrl" ref="traverseCropperId" autoCrop centerBox></vue-cropper>
              </div>
            </Col>
            <Col :span="2" >
              <div style="padding-top: 150px;padding-right: 100px">
                <div style="padding-bottom: 20px">
                  <RadioGroup v-model="currentTraverseCropType">
                    <Radio label="target">
                      <Icon type="md-eye"></Icon>
                      <span>交互区</span>
                    </Radio>
                    <Radio label="ignore">
                      <Icon type="md-eye-off"></Icon>
                      <span>屏蔽区</span>
                    </Radio>
                  </RadioGroup>
                </div>
                <Button style="margin-bottom: 15px" type="info" icon="md-refresh" size="small" @click="getTraverseCrop(curConfig)">更新区域</Button>
                <Button type="error" icon="ios-trash" size="small" @click="removeTraverseCrop(curConfig)">删除区域</Button>
                <Button style="margin-top: 30px" type="primary" icon="md-cloud-upload" size="small"
                        @click="savePageInfo()">
                  提交修改
                </Button>
              </div>
            </Col>
            <Col :span="10" style="padding-top: 50px;text-align: center">
              <div style="width:200px;height:300px;margin: 0 auto;padding: 30px 0px">
                <vue-cropper :img=traverseCrop></vue-cropper>
              </div>
              <div style="padding-bottom: 15px">
                <p>当前配置区域：{{currentTraverseCropIndex+1}}</p>
              </div>
              <template v-for="(cropRect, cropRectIndex) in curConfig.traverseCropRects">
                <Button type="dashed" icon="md-crop" :key="cropRectIndex" style="margin: 0px 5px" @click="showTraverseCrop(cropRect,cropRectIndex,curConfig)" >
                  区域{{cropRectIndex+1}}
                </Button>
              </template>
              <Button shape="circle" icon="md-add" @click="addTraverseCropRect(curConfig)"></Button>
            </Col>
          </Row>
        </template>
        <template v-else>
          暂未配置基准图
        </template>
        <div id="traverseBlockTargetList">
          <div>
            <h3>{{ isTraverseBlockList ? '屏蔽字符集合' : '交互字符集合' }}</h3>
            <ul>
              <li v-for="(char, index) in traverseBlockTargetList" :key="index">
                {{ char }}
                <button @click="removeTraverseChar(index)">删除</button>
              </li>
            </ul>
            <input v-model="newTraverseChar" placeholder="输入新字符">
            <Button @click="addTraverseChar">新增</button>
            <Button @click="toggleBlockTargetView">切换字符集合(屏蔽/交互)</button>
            <Button icon="md-cloud-upload"
                    @click="savePageInfo()">
              保存
            </Button>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import {Bus} from "@/global/bus";
import ImageCropper from '../Compatibility/ImageCropper';
import { duration } from "moment";
import { cloneDeep } from 'lodash';
export default {
  name: "ClientPageCrop",
  props: ["pageInfo"],
  components: {ImageCropper},
  data() {
    return {
      isOnlyOperations: false, // 是否复制所有配置（false:只复制交互操作， true: 全部复制）
      copyConfigModal: false,
      copyMode: 'current', // 复制模式
      searchKeyword: '',   // 搜索关键词
      sceneResults: [],    // 搜索结果列表
      currentPage: 1,      // 当前页码
      hasMore: true,       // 是否有更多数据
      loading: false,      // 加载状态
      currentScene: null,  // 当前选中场景
      configSelectModal: false, // 配置选择弹窗
      currentSceneConfigs: [],  // 当前场景下的配置列表
      selectedConfigs: [],      // 选中的配置ID列表
      actionModal: false,
      imageCropperModal: false,
      pageConfigs: [],
      attachments: {},
      editImageIndex: -2,
      dragIndex: "",
      minCropWidth: 0,
      minCropHeight: 0,
      cropOption: {},
      noticeMsg: '',
      newAction: {},
      defaultAction: {
        target: {
          type: "text",
          value: "",
          index: 1,
          count: 1,
          offset: {
            x: 0.0,
            y: 0.0
          }
        },
        type: "click",
        repeatCount: 1
      },
      defaultAssertion: {
        assertion: ''
      },
      imgNamePrefix: '',
      currentCropType: "ignore",
      currentTraverseCropType: "ignore",
      currentCropIndex: 0,
      currentTraverseCropIndex: 0,
      defaultCropRects:[{x1:0, y1:0, x2:0.06, y2:0.03, type:'ignore'}],
      crop: "",
      traverseCrop: "",
      curConfig: null,
      curAttachment: null,
      loadFinish: false,
      loadBasePicFinish: false,
      hasConfirmPermission: false,
      businessId: -2,
      addConfigModal: false,
      editConfigName: false,
      newConfigName: '',
      newConfigId: 0,
      focusConfigName: "",
      defaultConfigName: 'Base',
      focusConfigIndex: 0,
      mockId: '',
      isTraverseBlockList: true,
      traverseBlockedList: [],  // 初始需屏蔽的字符集合
      traverseTargetList: [],
      newTraverseChar: '',
      assertionTypeModal: false,
    }
  },
  mounted() {
    this.$Loading.start()
    this.newAction = JSON.parse(JSON.stringify(this.defaultAction))
  },
  computed: {
    traverseBlockTargetList() {
      return this.isTraverseBlockList ? this.curConfig.traverseBlockCharList : this.curConfig.traverseTargetCharList
    }
  },
  watch:{
    pageInfo: function() {
      this.getMMCDBusinessId(this.pageInfo.business);
      this.getPageConfigs()
      let configId = this.$route.query.configId
      this.focusConfigName = this.pageConfigs[0].name
      if (typeof(configId) !== 'undefined' && configId !== '-1' && this.pageConfigs.length > 1) {
        let match = false
        for (let i in this.pageConfigs) {
          let c = this.pageConfigs[i]
          if (c.id === configId) {
            match = true
            this.focusConfigName = this.pageConfigs[i].name
            break
          }
        }
        if (match === false) {
          this.$Notice.error({title: "未找到匹配的交互配置组！", duration: 5, closable: true})
        }
      }
      this.$Loading.finish()
    },
    loadFinish: function() {
      this.newAction = JSON.parse(JSON.stringify(this.defaultAction))
      console.log(this.newAction)
      setTimeout(() => {
        this.curAttachment = this.attachments[this.curConfig.id]
        this.loadBasePicFinish = this.loadFinish
      }, 1000)
    },
    businessId: function () {
      if (this.businessId < 0) {
        // 未迁移MMCD的方向，仍使用原来的权限校验方式
        this.checkPermissionByHyperjump();
      } else {
        this.checkPermissionByMMCD();
      }
    },
    focusConfigName: function() {
      this.newConfigName = this.focusConfigName
      for (let i in this.pageConfigs) {
        let c = this.pageConfigs[i]
        if (c.name === this.focusConfigName) {
          this.focusConfigIndex = i
          break
        }
      }
      this.curConfig = this.pageConfigs[this.focusConfigIndex]
      this.curAttachment = this.attachments[this.curConfig.id]
      if (this.curConfig.cropRects.length > 0) {
        this.showCrop(this.curConfig.cropRects[0], 0)
      } else {
        this.crop = ""
      }
    }
  },
  methods: {
    getPageConfigs(){
      // 兼容不同版本的配置格式
      let actions = JSON.parse(this.pageInfo['actions'])
      let version = 'version' in actions ? actions['version'] : 1
      if (version < 7) {
        let configs = actions['config']
        if (version < 6) {
          // 组装数据
          let actionList = []
          if (version == 1) {
            let textKeys = actions['text_keys']
            actionList = this.getActionKeys(textKeys)
          } else {
            actionList = 'list' in actions ? actions['list'] : []
            actionList.forEach(action => {
              action['count'] = Math.max(1, action['index'])
            });
          }
          let assertions = 'assertions' in actions ? actions['assertions'] : []
          let testMethod = 'testMethod' in actions ? actions['testMethod'] : null
          // Assert-断言；Diff-基于基准图比对；None-不使用任何结果处理方式
          if (testMethod === null) {
            if (assertions.length > 0) {
              testMethod = 'Assert'
            } else {
              testMethod = 'Diff'
            }
          }
          let scrollCount = this.pageInfo['scroll_count'] === -1 ? 5 : this.pageInfo['scroll_count']
          let cropRects = []
          let traverseCropRects = []
          if(this.pageInfo.params.length > 1){
            let params = JSON.parse(this.pageInfo.params)
            if('cropRects' in params){
              cropRects = params.cropRects
            }
            if('traverseCropRects' in params){
              traverseCropRects = params.traverseCropRects
            }
          }
          configs = [
            {
              id: "",
              name: this.defaultConfigName,
              list: actionList,
              assertions: assertions,
              testMethod: testMethod,
              cropRects: cropRects,
              traverseCropRects: traverseCropRects,
              scrollCount: scrollCount
            }
          ]
        }
        // 转换到新版本
        for (let config of configs) {
          let newActions = []
          for (let action of config.list) {
            if (action['scrollCount'] > 0) {
              newActions.push({
                'type': 'swipe-next-screen',
                'repeatCount': action['scrollCount']
              })
            }
            newActions.push({
              'type': 'click',
              'target': {
                'type': action['type'],
                'value': action['value'],
                'index': Math.max(action['index'], 1),
                "count": Math.max(action['count'], 1),
                'description': action['description'],
                'offset': {
                    'x': 0,
                    'y': 0
                }
              },
              'repeatCount': 1
            })
            if (action['waitTime'] > 0) {
              newActions.push({
                'type': 'wait',
                'params': {
                    'waitTime': action['waitTime']
                },
                'repeatCount': 1
              })
            }
          }
          config.list = newActions
        }
        this.pageConfigs = configs
      } else {
        this.pageConfigs = actions['config']
      }
      for (let config of this.pageConfigs) {
          let assertions = config.assertions
          for (let assertion of assertions) {
            if(assertion.thresh === undefined){
              assertion.thresh = 0.9
            }
          }
          if (!('traverseCropRects' in config)) {
            config['traverseCropRects'] = []
          }
          if (!('traverseBlockCharList' in config)) {
            config['traverseBlockCharList'] = []
            config['traverseTargetCharList'] = []
          }
      }
      this.mockId = this.pageInfo['mock_id']
      this.fillAttachments(this.mockId)
      this.imgNamePrefix = 'action/' + this.pageInfo.scene_id + '/'
    },
    fillAttachments(mockId) {
      this.pageConfigs.forEach((config, index) => {
        let attachment = {
          cropperType: 'action',
          maxImgSize: 1000,
          noneTestMethod: config.testMethod === 'None',
          basePicUrl: ''
        }
        this.getBasePic(mockId, config.id).then(res => {
          if (res.data.code == 0 && res.data.data.picLocation !== "") {
            attachment['basePicUrl'] = this.env.clientImageUrl+res.data.data.picLocation
            var img = new Image()
            img.setAttribute('crossorigin', 'anonymous')
            img.src = attachment['basePicUrl']
            if (img.complete) {
              attachment['maxImgSize'] = img.naturalHeight
            } else {
              img.onload = function(e) {
                attachment['maxImgSize'] = img.naturalHeight
              }
            }
          }
          this.loadFinish = true
        })
        this.attachments[config.id] = attachment
      })
    },
    getBasePic(mockId, configId) {
      return this.$axios({
        method: "get",
        params: {
          mockId: mockId,
          key: configId
        },
        url: this.env.url + "client/compatibility/basePic"
      })
      .catch(function(error) {
        console.log(error);
      });
    },
    beforeRemoveConfig(index) {
      if (this.pageConfigs.length == 1) {
        this.$Notice.error({title: "至少保留一个配置", duration: 5, closable: true})
        return Promise.reject()
      }
    },
    removeConfig(name) {
      let index = 0
      if (Number.isInteger(name)) {
        index = name
      } else {
        for (let i in this.pageConfigs) {
          let c = this.pageConfigs[i]
          if (c.name === name) {
            index = i
            break;
          }
        }
      }
      let config = this.pageConfigs[index]
      console.log(index, config)
      if (config.name === this.focusConfigName) {
        if (index !== 0) {
          this.focusConfigName = this.pageConfigs[0].name
        } else {
          this.focusConfigName = this.pageConfigs[1].name
        }
      }
      this.pageConfigs.splice(index, 1)
      this.$Modal.confirm({
        title: '配置删除',
        content: '确定删除【' + config.name + '】配置吗？',
        onOk: () => {
          this.savePageInfo()
        },
        onCancel: () => {
          this.pageConfigs.splice(index, 0, config)
          this.focusConfigName = config.name
        }
      });
    },
    getActionKeys(text_keys) {
      let actionText = []
      for (let i = 0; i < text_keys.length; i++) {
        // 旧逻辑兼容
        let text = text_keys[i]
        let index = /@(\d+)/g.exec(text) ? parseInt(/@(\d+)/g.exec(text)[1]) : 1
        let scrollCount = /%(\d+)/g.exec(text) ? parseInt(/%(\d+)/g.exec(text)[1]) : 0
        let waitTime = /&(\d+)/g.exec(text) ? parseInt(/&(\d+)/g.exec(text)[1]) : 0
        let value = /([^@%&]*)/g.exec(text) ? /([^@%&]*)/g.exec(text)[1] : ''
        actionText.push({
          type: 'text',
          index: index,
          count: Math.max(1, index),
          value: value,
          scrollCount: scrollCount,
          waitTime: waitTime
        })
      }
      return actionText;
    },
    dragStart(index) {
      this.editImageIndex = -2
      this.dragIndex = index
    },
    dragEnter(e, index, config) {
      e.preventDefault()
      if (this.dragIndex !== index) {
        const moving = config.list[this.dragIndex]
        config.list.splice(this.dragIndex, 1)
        config.list.splice(index, 0, moving)
        this.dragIndex = index
      }
    },
    dragOver(e, index) {
      e.preventDefault()
    },
    focusTab(index) {
      this.dragIndex = ""
      this.editImageIndex = -2
      this.currentCropIndex = 0
      this.currentTraverseCropIndex = 0
    },
    actionCheck: function(action) {
      // 格式化
      if ('params' in action && 'distance' in action.params) {
        action.params.distance = parseFloat(action.params.distance)
      }
      if ('params' in action && 'waitTime' in action.params) {
        action.params.waitTime = parseInt(action.params.waitTime)
      }
      if ('target' in action && 'index' in action.target) {
        action.target.index = parseInt(action.target.index)

      }
      if ('target' in action && 'count' in action.target) {
        action.target.count = parseInt(action.target.count)
      }
      if ('target' in action && 'offset' in action.target) {
        action.target.offset.x = parseFloat(action.target.offset.x)
        action.target.offset.y = parseFloat(action.target.offset.y)
      }
      action.repeatCount = parseInt(action.repeatCount)
      console.log(action)
      // 操作的参数
      if (['swipe-up', 'swipe-down', 'swipe-left', 'swipe-right'].includes(action.type)) {
        if (action.params.distance <= 0) {
          this.$Notice.error({title: "滑屏距离必须大于0", duration: 5, closable: true})
          return false
        }
      } else if (action.type === 'wait') {
        if (action.params.waitTime < 0) {
          this.$Notice.error({title: "操作后等待时长不能小于0！", duration: 5, closable: true})
          return false
        }
        if (action.params.waitTime > 60) {
          this.$Notice.error({title: "等待超过60s！请谨慎使用，会增加任务执行耗时", duration: 5, closable: true})
          return false
        }
      } else if (action.type === 'input') {
        if (action.params.text === '') {
          this.$Notice.error({title: "输入操作的文本不能为空", duration: 5, closable: true})
          return false
        }
      }
      // 操作的目标信息
      if (action.target && ['text', 'image'].includes(action.target.type)) {
        if (action.target.type === 'text' && action.type !== 'agent') {
          if (action.target.value === '') {
            this.$Notice.error({title: "文本元素不能为空", duration: 5, closable: true})
            return false
          }
        } else if (action.target.type === 'image') {
          if (action.target.value === '') {
            this.$Notice.error({title: "语义元素截图不能为空", duration: 5, closable: true})
            return false
          }
          if (action.target.index > action.target.count) {
            this.$Notice.error({title: "匹配序号不能大于总元素个数", duration: 5, closable: true})
            return false
          }
        }
        if (action.target.index < 1) {
          this.$Notice.error({title: "匹配序号从1开始！", duration: 5, closable: true})
          return false
        }
        if (action.type === 'agent') {
          if (action.params.text === '') {
            this.$Notice.error({title: "自然语言指令不能为空", duration: 5, closable: true})
            return false
          }
        }
      }
      if (action.repeatCount <= 0) {
        this.$Notice.error({title: "操作次数必须大于0！", duration: 5, closable: true})
        return false
      }
      return true
    },
    removeAction(index, config) {
      config.list.splice(index, index + 1)
    },
    createAction(config) {
      this.actionModal = true
      this.newAction = JSON.parse(JSON.stringify(this.defaultAction))
    },
    pushAction(config) {
      // 操作的参数
      if (['swipe-up', 'swipe-down', 'swipe-left', 'swipe-right'].includes(this.newAction.type)) {
        this.newAction.params = {
          distance: 0
        }
      } else if (this.newAction.type === 'wait') {
        this.newAction.target = {}
        this.newAction.params = {
          waitTime: 3
        }
      } else if (this.newAction.type === 'input') {
        this.newAction.params = {
          text: ""
        }
      } else if (this.newAction.type === 'drop-down') {
        this.newAction.params = {}
        this.newAction.target = {type: "screen"}
      } else if (this.newAction.type === 'swipe-next-screen') {
        this.newAction.target = {}
        this.newAction.params = {}
      } else {
        this.newAction.params = {}
      }
      // 操作的目标信息
      if (['text', 'image'].includes(this.newAction.target.type)) {
        // 保持默认
      } else if (this.newAction.target.type === 'screen') {
        this.newAction.target = {
          type: "screen",
          offset: {
            x: 0.0,
            y: 0.0
          }
        }
      }
      config.list.push(this.newAction)
      this.newAction = JSON.parse(JSON.stringify(this.defaultAction))
      this.actionModal = false
    },
    showAssertionTypeModal() {
      this.assertionTypeModal = true;
    },
    selectAssertionType(type) {
      this.assertionTypeModal = false;
      if (type === 'image') {
        this.showImageCropper(this.curConfig, -1, 'assertion');
      } else if (type === 'ai') {
        let newAssertion = {
          type: 'ai',
          aiAssertion: '',
          thresh: 0.9
        };
        this.curConfig.assertions.push(newAssertion);
      }
    },
    showImageCropper(config, index, cropperType) {
      this.editImageIndex = index
      this.imageCropperModal = true
      this.attachments[config.id].cropperType = cropperType
      if (cropperType === 'assertion') {
        // 大约是图片面积的1/15
        this.cropOption = {
          type: 'imageRatioThreshold',
          imageRatioThreshold: 0.06, //判断截图面积占总面积比例是否符合要求的阈值
          enlarge: 1,
          full: true,
          maxImgSize: this.attachments[config.id].maxImgSize
        }
        this.noticeMsg = "语义断言配置的截图区域不能小于屏幕面积的1/15，<a href='https://km.sankuai.com/page/1372581059' target='_blank'> 详情参看 </a>"
      } else {
        this.minCropWidth = 80
        this.minCropHeight = 80
        this.cropOption = {
          type: 'minimumImageSize',
          enlarge: 2,
          full: false,
          autoCropWidth: 40,
          autoCropHeight: 40,
          minCropWidth: this.minCropWidth,
          minCropHeight: this.minCropHeight,
          maxImgSize: this.attachments[config.id].maxImgSize
        }
        this.noticeMsg = "语义交互的icon截图至少为80x80，且边缘空白区域占比较小，<a href='https://km.sankuai.com/page/1372581059' target='_blank'> 详情参看 </a>"
      }
    },
    savePageInfo() {
      if (this.hasConfirmPermission) {
        this.updatePage();
      } else {
        alert("无权限！");
      }
    },
    closeImgCropperModal(imgUrl, config) {
      if (this.attachments[config.id].cropperType === 'assertion') {
        this.closeImgCropperModalForAssertion(imgUrl, config)
      } else {
        this.closeImgCropperModalForAction(imgUrl, config)
      }
    },
    closeImgCropperModalForAction(imgUrl, config) {
      this.imageCropperModal = false
      if (imgUrl) {
        if (this.editImageIndex == -1) {
          this.newAction.target.value = imgUrl
        } else if (this.editImageIndex >= 0) {
          config.list[this.editImageIndex].target.value = imgUrl
        }
      }
      this.editImageIndex = -2
    },
    closeImgCropperModalForAssertion(imgUrl, config) {
      this.imageCropperModal = false
      if (imgUrl) {
        if (this.editImageIndex == -1) {
          let newAssertion = JSON.parse(JSON.stringify(this.defaultAssertion))
          newAssertion.assertion = imgUrl
          newAssertion.thresh = 0.9
          newAssertion.type = 'image'
          config.assertions.push(newAssertion)
        } else if (this.editImageIndex >= 0) {
          config.assertions[this.editImageIndex].assertion = imgUrl
        }
      }
      this.editImageIndex = -2
    },
    changeNoneTestMethod: function(val, config) {
      if (val) {
        config.testMethod = 'None'
      } else {
        if (config.assertions.length > 0) {
          config.testMethod = 'Assert'
        } else {
          config.testMethod = 'Diff'
        }
      }
      this.savePageInfo()
    },
    removeAssertion(index, config) {
      config.assertions.splice(index, 1)
    },
    getCrop(config){
      if (config.cropRects.length-1 < this.currentCropIndex) {
        return
      }
      let croperId = 'cropperId'
      let imageX1 = this.$refs[croperId].getImgAxis().x1
      let imageX2 = this.$refs[croperId].getImgAxis().x2
      let imageY1 = this.$refs[croperId].getImgAxis().y1
      let imageY2 = this.$refs[croperId].getImgAxis().y2
      let cropX1 = this.$refs[croperId].getCropAxis().x1
      let cropX2 = this.$refs[croperId].getCropAxis().x2
      let cropY1 = this.$refs[croperId].getCropAxis().y1
      let cropY2 = this.$refs[croperId].getCropAxis().y2
      let currentCropRect = {}
      currentCropRect.x1=((cropX1-imageX1)/(imageX2-imageX1)).toFixed(2)
      currentCropRect.y1=((cropY1-imageY1)/(imageY2-imageY1)).toFixed(2)
      currentCropRect.x2=((cropX2-imageX1)/(imageX2-imageX1)).toFixed(2)
      currentCropRect.y2=((cropY2-imageY1)/(imageY2-imageY1)).toFixed(2)
      currentCropRect.type = this.currentCropType
      this.$refs[croperId].getCropData((data) => {this.crop=data})
      config.cropRects[this.currentCropIndex] = currentCropRect
    },
    removeCrop(config){
      let cropIndex = this.currentCropIndex;
      for(let i=0;i<config.cropRects.length;i++){
        if(i === cropIndex && config.cropRects.length > 0){
          config.cropRects.splice(i, 1);
          if (config.cropRects.length > 0) {
            this.showCrop(config.cropRects[0], 0)
          } else {
            this.crop = ""
          }
          return 0
        }
      }
    },
    showCrop(cropRect, cropIndex){
      let croperId = 'cropperId'
      this.currentCropIndex =cropIndex;
      this.currentCropType = cropRect.type
      if (typeof(this.$refs[croperId]) === 'undefined') {
        return
      }
      let imageX1 = this.$refs[croperId].getImgAxis().x1
      let imageX2 = this.$refs[croperId].getImgAxis().x2
      let imageY1 = this.$refs[croperId].getImgAxis().y1
      let imageY2 = this.$refs[croperId].getImgAxis().y2
      this.$refs[croperId].cropOffsertX = imageX1 + Math.round(cropRect.x1*(imageX2-imageX1))
      this.$refs[croperId].cropW = Math.round((cropRect.x2-cropRect.x1)*(imageX2-imageX1))
      this.$refs[croperId].cropOffsertY = imageY1 + Math.round((cropRect.y1)*(imageY2-imageY1))
      this.$refs[croperId].cropH = Math.round((cropRect.y2-cropRect.y1)*(imageY2-imageY1))
      this.$refs[croperId].getCropData((data) => {this.crop=data})
    },
    addCropRect(config){
      config.cropRects.push({x1:0.1, y1:0.1, x2:0.9, y2:0.3, type:'ignore'});
      this.currentCropIndex =  config.cropRects.length-1;
      this.showCrop(config.cropRects[this.currentCropIndex], this.currentCropIndex)
    },
    getTraverseCrop(config){
      if (config.traverseCropRects.length-1 < this.currentTraverseCropIndex) {
        return
      }
      let croperId = 'traverseCropperId'
      let imageX1 = this.$refs[croperId].getImgAxis().x1
      let imageX2 = this.$refs[croperId].getImgAxis().x2
      let imageY1 = this.$refs[croperId].getImgAxis().y1
      let imageY2 = this.$refs[croperId].getImgAxis().y2
      let cropX1 = this.$refs[croperId].getCropAxis().x1
      let cropX2 = this.$refs[croperId].getCropAxis().x2
      let cropY1 = this.$refs[croperId].getCropAxis().y1
      let cropY2 = this.$refs[croperId].getCropAxis().y2
      let currentCropRect = {}
      currentCropRect.x1=((cropX1-imageX1)/(imageX2-imageX1)).toFixed(2)
      currentCropRect.y1=((cropY1-imageY1)/(imageY2-imageY1)).toFixed(2)
      currentCropRect.x2=((cropX2-imageX1)/(imageX2-imageX1)).toFixed(2)
      currentCropRect.y2=((cropY2-imageY1)/(imageY2-imageY1)).toFixed(2)
      currentCropRect.type = this.currentTraverseCropType
      this.$refs[croperId].getCropData((data) => {this.traverseCrop=data})
      config.traverseCropRects[this.currentTraverseCropIndex] = currentCropRect
    },
    removeTraverseCrop(config){
      let cropIndex = this.currentTraverseCropIndex;
      for(let i=0;i<config.traverseCropRects.length;i++){
        if(i === cropIndex && config.traverseCropRects.length > 0){
          config.traverseCropRects.splice(i, 1);
          if (config.traverseCropRects.length > 0) {
            this.showCrop(config.traverseCropRects[0], 0)
          } else {
            this.traverseCrop = ""
          }
          return 0
        }
      }
    },
    showTraverseCrop(cropRect, cropIndex){
      let croperId = 'traverseCropperId'
      this.currentTraverseCropIndex =cropIndex;
      this.currentTraverseCropType = cropRect.type
      if (typeof(this.$refs[croperId]) === 'undefined') {
        return
      }
      let imageX1 = this.$refs[croperId].getImgAxis().x1
      let imageX2 = this.$refs[croperId].getImgAxis().x2
      let imageY1 = this.$refs[croperId].getImgAxis().y1
      let imageY2 = this.$refs[croperId].getImgAxis().y2
      this.$refs[croperId].cropOffsertX = imageX1 + Math.round(cropRect.x1*(imageX2-imageX1))
      this.$refs[croperId].cropW = Math.round((cropRect.x2-cropRect.x1)*(imageX2-imageX1))
      this.$refs[croperId].cropOffsertY = imageY1 + Math.round((cropRect.y1)*(imageY2-imageY1))
      this.$refs[croperId].cropH = Math.round((cropRect.y2-cropRect.y1)*(imageY2-imageY1))
      this.$refs[croperId].getCropData((data) => {this.traverseCrop=data})
    },
    addTraverseCropRect(config){
      config.traverseCropRects.push({x1:0.1, y1:0.1, x2:0.9, y2:0.3, type:'ignore'});
      this.currentTraverseCropIndex =  config.traverseCropRects.length-1;
      this.showTraverseCrop(config.traverseCropRects[this.currentTraverseCropIndex], this.currentTraverseCropIndex)
    },
    checkPermissionByHyperjump() {
      this.$axios({
        method: "post",
        params: {
          business: this.pageInfo.business,
          user: Bus.userInfo.userLogin,
          pageId: this.pageInfo.id
        },
        url: this.env.url + "clientPage/confirmPermission"
      })
      .then(res => {
        let message = res.data;
        if (message.code === 200) {
          this.hasConfirmPermission = true
        }
      })
      .catch(function(error) {
        console.log(error);
      });
    },
    checkPermissionByMMCD() {
      this.$axios({
        method: "get",
        params: {
          businessId: this.businessId,
          misId: Bus.userInfo.userLogin
        },
        url: this.env.url + "autoTestConfig/getUserPermission"
      })
      .then(res => {
        if (res.data.permissionLevel != 100 || res.data.isAdmin) {
          this.hasConfirmPermission = true
        }
      })
      .catch(function(error) {
        console.log(error);
      });
    },
    getMMCDBusinessId (businessName) {
      this.$axios({
        method:"get",
        params: {
          businessName: businessName
        },
        url: this.env.url + "autoTestConfig/getBusinessByBusinessName"
      }).then((res) => {
        if (res.data != null) {
          // 已经迁移MMCD的方向
          this.businessId = res.data.id;
        } else {
          // 未迁移MMCD的方向
          this.businessId = -1
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    updatePage() {
      for (let config of this.pageConfigs) {
        config.scrollCount = parseInt(config.scrollCount)
        let actions = config.list
        let assertions = config.assertions
        for (let action of actions) {
          if (this.actionCheck(action) === false) {
            return
          }
        }
        for (let assertion of assertions){
          assertion.thresh = parseFloat(assertion.thresh)
          if (assertion.type === 'image' && assertion.thresh < 0.5 || assertion.thresh > 1 || isNaN(assertion.thresh)){
            this.$Notice.error({title: "阈值应当在0.5～1.0之间", duration: 12, closable: true})
            return
          }
          if (assertion.type === 'ai' && assertion.aiAssertion.trim() === '') {
            this.$Notice.error({title: "AI断言内容不能为空", duration: 12, closable: true});
            return
          }
        }
      }
      this.pageInfo.actions = JSON.stringify({"config": this.pageConfigs, "version": 7});
      this.pageInfo.rowIndex = this.pageInfo.id;
      this.$axios({
        method: "post",
        data: this.pageInfo,
        url: this.env.url + "clientPage/changePageInfo"
      })
      .then(res => {
        let message = res.data;
        if (message.code === 0) {
          this.$Notice.success({
            title: "保存成功"
          });
        }
      })
      .catch(function(error) {});
    },
    handleConfigAdd() {
      this.copyConfigModal = true
      this.resetCopyModal()
      this.newConfigName = this.generateUniqueName(this.pageConfigs[this.focusConfigIndex].name)
      this.newConfigId = this.generateNewId();
    },
  // 重置弹窗状态
    resetCopyModal() {
      this.copyMode = 'current'
      this.newConfigName = ''
      this.searchKeyword = ''
      this.sceneResults = []
      this.currentPage = 1
      this.hasMore = true
      this.currentScene = null
      this.selectedConfigs = []
      this.isOnlyOperations = false
    },
    // 场景搜索方法（整合接口调用）
    async handleSceneSearch() {
      if (!this.searchKeyword.trim()) {
        this.$Message.warning('请输入搜索场景名称')
        return
      }

      this.loading = true
      try {
        // 直接发起请求
        let res = await this.$axios({
          method:"get",
          url: this.env.url + "clientPage/getPagesBySceneName",
          params: {
            sceneName: this.searchKeyword,  // 直接使用组件数据
            page: this.currentPage,
            pageSize: 20
          }
        })
        let dataList = res.data || []
         // 修正关键逻辑：解析 actions 字符串为 JSON 对象
        this.sceneResults = [
          ...this.sceneResults,
          ...dataList.map(item => {
            try {
              // 解析 actions 字段
              const parsedActions = item.actions ? JSON.parse(item.actions) : {}
              return {
                name: item.name,
                actions: {
                  config: parsedActions.config || [] ,// 安全获取 config
                  version: parsedActions.version || 1
                },
                sceneId: item.scene_id
              }
            } catch (e) {
              console.error('解析 actions 失败:', e)
              return {
                name: item.name,
                actions: { config: [] } // 解析失败时返回空配置
              }
            }
          })
        ]
        this.hasMore = dataList.length >= 20
        this.currentPage++
      } catch (error) {
        console.error('搜索失败:', error)
        this.$Message.error('搜索服务异常')
      } finally {
        this.loading = false
      }
    },

    // 选择场景
    handleSelectScene(scene) {
      this.currentScene = scene
      this.currentSceneConfigs = this.changActionsByVersion(scene.actions).config
      this.configSelectModal = true
    },

    // 复制选中配置（仅交互操作）
    handleCopySelectedConfigs() {
      // 获取当前激活的配置作为基准
      const currentConfig = this.pageConfigs[this.focusConfigIndex]
      this.currentSceneConfigs
        .filter(c => this.selectedConfigs.includes(c.id || ''))
        .forEach(srcConfig => {
          // 创建新配置基准（深拷贝当前配置）
        let newConfig = cloneDeep(srcConfig)
        if (this.isOnlyOperations) {
          // 如果 isOnlyOperations 为 true，只复制 list 和 scrollCount，其他字段使用默认值
          newConfig = {
            id: '', // 默认值
            name: '', // 默认值
            list: srcConfig.list ? cloneDeep(srcConfig.list) : [], // 复制 list
            scrollCount: srcConfig.scrollCount || 0, // 复制 scrollCount
            // 其他字段使用默认值
            assertions: [],
            testMethod: 'Diff',
            cropRects: [],
            traverseCropRects: [],
            traverseBlockCharList: []
          }
        }
          // 生成新ID和名称
          newConfig.id = this.generateNewId()
          newConfig.name = this.generateUniqueName(srcConfig.name)
          // 添加新配置
          this.pageConfigs.push(newConfig)
          // 复制附件关联（使用当前配置的附件）
          this.attachments[newConfig.id] = JSON.parse(
            JSON.stringify(this.attachments[currentConfig.id])
          )
          this.focusConfigName = newConfig.name
        })
      this.savePageInfo()
      // 不同版本配置适应一下
      this.getPageConfigs()
      this.copyConfigModal = false
      this.resetCopyModal()
    },

    // 确认是否执行复制操作
    handleConfirmCopy() {
      if (this.copyMode === 'current') {
        this.addConfig()
        this.copyConfigModal = false
      } else {
        if (!this.currentScene) {
          this.$Message.warning('请先选择要复制的场景配置')
          return
        }
        this.configSelectModal = true
      }
    },

    // 生成新ID
    generateNewId() {
    // 时间戳防止id一致
      return Date.now().toString() + Math.floor(Math.random() * 10000000).toString()
    },
    // 新增唯一性命名方法
    generateUniqueName(originName) {
      let counter = 1
      let newName = `${originName}_copy`
      while (this.pageConfigs.some(c => c.name === newName)) {
        newName = `${originName}_copy${counter++}`
      }
      return newName
    },
    handleSearchChange(value) {
      this.newConfigName = ''
      this.sceneResults = []
      this.currentPage = 1
      this.hasMore = true
      this.currentScene = null
      this.selectedConfigs = []
    },

    startEditConfigName(configName) {
      this.editConfigName = true
      this.newConfigName = configName
    },
    updateConfigName(config) {
      if (this.newConfigName === config.name) {
        this.editConfigName = false
        return true
      }
      if (this.configNameCheck(this.newConfigName) === false) {
        return
      }
      config.name = this.newConfigName
      this.editConfigName = false
      this.focusConfigName = config.name
      this.savePageInfo()
    },
    addConfig() {
      if (this.configNameCheck(this.newConfigName) === false) {
        return
      }
      let srcConfig = this.pageConfigs[this.focusConfigIndex]
      let config = JSON.parse(JSON.stringify(srcConfig))
      config.name = this.newConfigName
      if (this.newConfigName === this.defaultConfigName) {
        config.id = ""
        this.pageConfigs.splice(0, 0, config)
      } else {
        config.id = '' + this.newConfigId
        this.pageConfigs.push(config)
      }
      let newAttachment = JSON.parse(JSON.stringify(this.attachments[srcConfig.id]))
      this.attachments[config.id] = newAttachment
      this.focusConfigName = config.name
      this.savePageInfo()
    },
    configNameCheck(name) {
      // 多配置的名称不能相同
      for (let i in this.pageConfigs) {
        let config = this.pageConfigs[i]
        if (config.name === name) {
          this.$Notice.error({title: "配置名称已经存在", duration: 5, closable: true})
          return false
        }
      }
      return true
    },
    handleThresholdBlur(thresh){
      // 输入框失去焦点，判断阈值是否合法
      thresh = parseFloat(thresh)
      if(isNaN(thresh) || thresh > 1 || thresh < 0.5){
        this.$Notice.error({title: "阈值应当在0.5～1.0之间", duration: 12, closable: true})
      }
    },
    addTraverseChar() {
      if (this.newTraverseChar.trim() !== '') {
        this.traverseBlockTargetList.push(this.newTraverseChar.trim())
        this.newTraverseChar = ''
      }
    },
    removeTraverseChar(index) {
      this.traverseBlockTargetList.splice(index, 1)
    },
    toggleBlockTargetView() {
      this.isTraverseBlockList = !this.isTraverseBlockList
    },
    // 复制的配置版本适配化
    changActionsByVersion(currentActions) {
      // 深拷贝原始数据
      const actions = JSON.parse(JSON.stringify(currentActions));
      const version = actions.hasOwnProperty('version') ? actions.version : 1;

      // 版本升级逻辑
      if (version < 7) {
        // 处理 v6 及以下版本
        if (version < 6) {
          // 兼容 v1-v5 格式
          const legacyConfig = {
            id: "",
            name: "Base",
            list: version === 1 ?
              this.getActionKeys(actions.text_keys || []) :
              (actions.list || []).map(function(action) {
                return {
                  type: action.type,
                  value: action.value,
                  index: action.index,
                  count: Math.max(typeof action.index !== 'undefined' ? action.index : 1, 1),
                  description: action.description || '',
                  waitTime: action.waitTime || 0,
                  scrollCount: action.scrollCount || 0
                };
              }),
            assertions: actions.hasOwnProperty('assertions') ? actions.assertions : [],
            testMethod: actions.hasOwnProperty('testMethod') ? actions.testMethod :
              ( (actions.assertions && actions.assertions.length) ? 'Assert' : 'Diff' ),
            cropRects: [],
            traverseCropRects: [],
            scrollCount: actions.hasOwnProperty('scroll_count') ?
              (actions.scroll_count === -1 ? 5 : actions.scroll_count) : 5
          };

          // 处理旧版参数（兼容 this.pageInfo 可能不存在的情况）
          if (this.pageInfo && this.pageInfo.params) {
            try {
              const params = JSON.parse(this.pageInfo.params);
              legacyConfig.cropRects = params.cropRects || [];
              legacyConfig.traverseCropRects = params.traverseCropRects || [];
            } catch(e) {
              console.warn('params parse error:', e);
            }
          }

          actions.config = [legacyConfig];
        }

        // 统一转换 action 结构到 v7
        if (actions.config) {
          actions.config.forEach(function(config) {
            const newList = [];
            (config.list || []).forEach(function(action) {
              // 处理滑动操作
              if (action.scrollCount > 0) {
                newList.push({
                  target: {},
                  type: 'swipe-next-screen',
                  repeatCount: action.scrollCount,
                  params: {}
                });
              }

              // 转换点击操作
              if (action.type === 'click' || !action.type) {
                newList.push({
                  type: 'click',
                  target: {
                    type: (action.target && action.target.type) || 'text',
                    value: (action.target && action.target.value) || action.value || '',
                    index: Math.max(
                      (action.target && typeof action.target.index !== 'undefined') ? action.target.index : 1,
                      1
                    ),
                    count: Math.max(
                      (action.target && typeof action.target.count !== 'undefined') ? action.target.count : 1,
                      1
                    ),
                    offset: (action.target && action.target.offset) || { x: 0, y: 0 }
                  },
                  repeatCount: typeof action.repeatCount !== 'undefined' ? action.repeatCount : 1,
                  params: {}
                });
              }

              // 转换等待操作
              if (action.waitTime > 0) {
                newList.push({
                  target: {},
                  type: 'wait',
                  repeatCount: 1,
                  params: { waitTime: action.waitTime }
                });
              }
            });
            config.list = newList;
          });
        }

        // 填充新增字段默认值
        actions.config.forEach(function(config) {
          config.traverseBlockCharList = [];
          config.traverseTargetCharList = [];
          if (config.assertions) {
            config.assertions.forEach(function(a) {
              a.thresh = typeof a.thresh !== 'undefined' ? a.thresh : 0.9;
            });
          }
        });
      }

      // 确保版本号更新
      actions.version = 7;
      return actions;
    }
  }
}
</script>

<style scoped>

.content-container {
  text-align: left;
  margin: 0px 0px 50px 0px;
}

.content-container .title {
  font-size: 20px;
  font-weight: 500;
}

.config-item {
  margin: 20px 0px;
}

.config-item .title {
  font-weight: 700;
  padding-right:20px;
  font-size: 16px;
}

.el-card .el-card__head {
  border-bottom: 1px solid #e8eaec;
  padding: 14px 16px;
  line-height: 1;
}

.ivu-card .ivu-card-head {
  background: aliceblue;
}

.action-list {
  list-style: none;
  margin: 20px 0px;
}

.action-list .drag-move {
  transition: transform 0.3s;
}
.action-list .list-item {
  cursor: move;
  border-radius: 4px;
  color: #FFF;
}
.action-item {
  margin: 10px 0px;
}
.action-item :hover{
  background: aliceblue;
}
.btn-remove :hover{
  width: 32px;
  height: 32px;
  background-color: #ed4014;
  border-radius: 32px;
}
.form-item {
  display:inline-block;
  margin: 0 20px;
}
.word-tips {
  font-size: smaller;
  color: coral;
}
.ivu-form-item {
  margin-bottom: 0px;
  vertical-align: top;
  zoom: 1;
}

.ivu-modal-body button {
  margin-right: 10px;
}

#traverseBlockTargetList {
    width: 300px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#traverseBlockTargetList h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    text-align: center;
}

#traverseBlockTargetList ul {
    list-style-type: none;
    padding: 0;
    margin-bottom: 15px;
}

#traverseBlockTargetList li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

#traverseBlockTargetList li button {
    width: 80px;  /* 设置固定宽度 */
    background-color: #ff4d4d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#traverseBlockTargetList li button:hover {
    background-color: #e60000;
}

#traverseBlockTargetList input {
    width: calc(100% - 22px);
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

#traverseBlockTargetList button {
    width: calc(100% - 10px);  /* 确保按钮不被截断 */
    padding: 10px;
    margin-top: 10px;  /* 增加间距 */
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    line-height: normal;  /* 调整行高 */
    vertical-align: middle;  /* 垂直对齐 */
}

#traverseBlockTargetList button:hover {
    background-color: #45a049;
}

#traverseBlockTargetList button:last-child {
    background-color: #008CBA;
}

#traverseBlockTargetList button:last-child:hover {
    background-color: #007bb5;
}

.assertion-container {
  display: flex;
  align-items: stretch;
  width: 100%;
  min-height: 120px; /* 确保容器有足够的高度 */
}

.assertion-info {
  flex: 0 0 100px;
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  justify-content: flex-start; /* 左对齐 */
  align-items: center; /* 垂直居中 */
  white-space: nowrap; /* 防止内容换行 */
}

.index-item, .type-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 50%; /* 平分高度 */
  padding: 0 5px;
}

.assertion-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center; /* 垂直居中 */
}

.ai-assertion-input {
  width: 100%;
}

.ai-assertion-input .ivu-input {
  width: 100%;
  min-height: 120px;
}

.remove-button {
  flex: 0 0 50px;
  display: flex;
  justify-content: center;
  align-items: center; /* 垂直居中 */
}
/* 添加样式 */
.copy-mode-group {
  margin-bottom: 20px;
}

.scene-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.scene-meta {
  flex: 1;
  min-width: 0;
}

.scene-id {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.scene-actions {
  font-size: 13px;
  color: #595959;
  flex-shrink: 0;
  margin-left: 16px;
}

.config-item {
  padding: 8px 0;
}

/* 加载列表页面 */
.load-more {
  margin: 20px 0;
  text-align: center;
}

.no-more {
  color: #999;
  text-align: center;
  padding: 10px;
  font-size: 12px;
}

.scene-list {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 15px;
  position: relative;
}

.empty-tips {
  text-align: center;
  color: #999;
  padding: 30px;
}
.compact-switch-wrapper {
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 4px 8px;
  margin-bottom: 12px;
  margin-left: auto; /* 右对齐关键属性 */
  background: transparent; /* 移除背景色 */
  border-radius: 4px;
}
.switch-label {
  font-size: 15px;
  color: #808695;  /* 调整为更柔和的文字颜色 */
  margin-right: 8px;
  white-space: nowrap; /* 防止文字换行 */
}

/* 右侧对齐开关 */
.right-aligned-switch {
  margin-left: auto;  /* 将开关推到容器最右侧 */
  transform: scale(0.85);
  /deep/ .ivu-switch-inner {
    margin: 0 4px;
  }
}
</style>
