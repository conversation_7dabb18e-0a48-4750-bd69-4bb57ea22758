<template>
  <div>
    <ClientPageTitle :pageInfo=pageInfo></ClientPageTitle>
    <div class="content-container">
      <ClientPageCrop :pageInfo=pageInfo></ClientPageCrop>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import ClientPageTitle from "./ClientPageTitle";
import ClientPageCrop from "./ClientPageCrop";
export default {
  name: "ClientPageBoard",
  components: {ClientPageTitle, ClientPageCrop},
  data() {
    return {
      pageInfo:[]
    }
  },
    mounted() {
      let pageID = this.$route.query.pageId;
      if(pageID){
        this.getPageInfo(pageID)
      }
      if(this.$route.query.os && this.$route.query.business && this.$route.query.name){
        this.getPageByName(this.$route.query.os,this.$route.query.business,this.$route.query.name);
      }
    },
    methods:{
      getPageByName(os, business, name) {
        this.$axios({
          method: "get",
          params: {
            "os": os,
            "business": business,
            "name": name
          },
          url: this.env.url + "clientPage/getPageByName"
        }).then((res) => {
          let message = res.data;
          if (message.page.id) {
            this.getPageInfo(message.page.id)
          }
          return message
        }).catch(function (error) {
          console.log(error)
        })
      },
      getPageInfo(pageId){
        this.$axios({
          method:"get",
          params:{
            "pageId": pageId
          },
          url:this.env.url+"clientPage/getPagesByPageId"
        }).then((res) => {
          let message = res.data;
          this.pageInfo=message[0];
        }).catch(function (error) {
          console.log(error)
        })
      }
  }
}
</script>

<style scoped>
  .content-container {
    background-color: #ffffff;
    margin: 10px 15px 15px 15px;
    padding: 10px 10px 15px 10px;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }
</style>
