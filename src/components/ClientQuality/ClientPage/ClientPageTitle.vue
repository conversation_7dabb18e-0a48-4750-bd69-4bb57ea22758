<template>
  <div class="content-container">
    <div class="content-title">
      <h3>{{pageInfo.name}}
        <span>
          <template v-if="pageInfo.os !== undefined && pageInfo.os.toUpperCase().trim() === 'IOS'">
            <img style="text-align: center;height: 26px;width: 26px" src="/static/img/ios.png" title="iOS"/>
          </template>
          <template v-else-if="pageInfo.os !== undefined && pageInfo.os.toUpperCase().trim() === 'ANDROID'">
            <img style="text-align: center;height: 26px;width: 26px" src="/static/img/android.png" title="Android"/>
          </template>
          <template v-else-if="pageInfo.os !== undefined && pageInfo.os.toUpperCase().trim() === 'WEB'">
            <img style="text-align: center;height: 26px;width: 26px" src="/static/img/web.png" title="Web"/>
          </template>
          <template v-else>
            <img style="text-align: center;height: 26px;width: 26px" src="/static/img/mrn.png" title="MRN"/>
            <img style="text-align: center;height: 26px;width: 26px" src="/static/img/h5.png" title="H5"/>
          </template>

          <Tag v-if="pageInfo.online" color="green" title="上线"><Icon type="md-radio-button-on" /> online</Tag>
          <Tag v-else color="default" title="下线"><Icon type="md-radio-button-off" /> offline</Tag>

          <Button type="primary" size="small" @click="pageNetClick=true">PageNet</Button>
          <Modal v-model="pageNetClick" title="PageNet" width="1600px" :label=pageNetLabel>
            <ClientPageNet :pageInfo=pageInfo></ClientPageNet>
          </Modal>

        </span>
      </h3>
    </div>


    <div style="text-align: left">
      <Tag color="gold" title="App"><Icon type="md-phone-portrait" /> {{pageInfo.app}}</Tag>
      <Tag color="cyan" title="业务线"><Icon type="md-briefcase" /> {{pageInfo.business}}</Tag>
      <Tag color="geekblue" title="Mock ID"><Icon type="md-bookmark" /> {{pageInfo.mock_id}}</Tag>
      <Tag :color="getPriorityTagColor(pageInfo.priority)" title="优先级"><Icon type="md-star-outline" /> {{pageInfo.priority}}</Tag>
      <Poptip trigger="hover" word-wrap width="500" :content="pageInfo.example" placement="right" transfer>
        <Tag color="default"><Icon type="ios-paper-outline" /> Scheme</Tag>
      </Poptip>
    </div>

  </div>

</template>

<script>
/* eslint-disable */
import ClientPageNet from "./ClientPageNet";
export default {
  name: "ClientPageTitle",
  props: ["pageInfo"],
  components: {ClientPageNet},
  data() {
    return {
      pageNetClick: false,
      pageNetLabel: (h) => {
        return h('div', [
          h('span', 'PageNet'),
          h('Badge', {
            props: {
              text: 'Beta'
            }
          })
        ])
      }
    }
  },
  methods: {
    getPriorityTagColor(priority) {
      if (priority === undefined) {
        return 'default'
      }
      switch (priority.toUpperCase()) {
        case 'P0':
          return 'gold'
        case 'P1':
          return 'volcano'
        case 'P2':
          return 'blue'
        default:
          return 'green'
      }
    }
  }
}
</script>

<style scoped>
  .content-container {
    background-color: #ffffff;
    margin: 10px 15px 15px 15px;
    padding: 10px 10px 15px 10px;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }
  .content-title{
    text-align: left;
    padding: 0 0 18px 10px
  }
</style>
