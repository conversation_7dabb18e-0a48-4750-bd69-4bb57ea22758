<template>
  <div>
    <div style="padding-bottom: 15px">
      <Row>
        <Col span="3" style="text-align: left">
          <Tag v-if="selectedPages.length === 0" color="default">{{selectedPages.length}} / {{tableData.length}}</Tag>
          <Tag v-else color="blue">{{selectedPages.length}} / {{tableData.length}}</Tag>
          <Button type="warning" icon='md-close' size="small" @click="cancelPageSelectAll" :disabled="selectedPages.length === 0" ghost>清空选择</Button>
        </Col>
        <Col span="2" style='font-size: 10px;text-align: right;padding-top:5px' v-if="pageGroupList.length>0">
          <div>根据页面组选择</div>
        </Col>
        <Col span="6">
          <div style="padding-left: 15px" v-if="pageGroupList.length>0">
            <Select v-model="selectedPageGroupId" placeholder="选择页面组" @on-change="selectPageGroup">
              <Option value="0">全部</Option>
              <template v-for="pageGroup in pageGroupList">
                <Option v-if="pageGroup.pages_id.trim() !== ''" :value="pageGroup.id" :label="pageGroup.group_name">
                  <span>{{pageGroup.group_name}}</span>
                  <span
                    style="float:right;color:#ccc"
                  >{{getGroupValidPages(pageGroup.pages_id).length}}/{{pageGroup.user}}</span>
                </Option>
              </template>
            </Select>
          </div>
        </Col>
        <Col span="3" v-show="!os && !business && selectedPages.length > 0">
          <div class="pageSelectCheck">请先选择业务方向</div>
        </Col>
        <Col span="1" offset="1" v-show="business && app && os">
          <div class="pageSelectButton">
            <Button :disabled="selectedPages.length === 0" type="primary" shape="circle" :icon="pageGroupIcon" size="small" @click="modalShow=true">{{pageGroupType}}</Button>
          </div>
        </Col>
      </Row>
    </div>

    <Table
      ref="pageTable"
      max-height="700"
      :data="showData"
      :columns="tableColumns"
      @on-select="pageSelection"
      @on-select-all="pageSelectAll"
      @on-select-cancel="cancelPageSelection"
      @on-select-all-cancel="cancelPageSelectAll"
    >
      <template slot-scope="{ row, index }" slot="page_name">
        <Tooltip trigger="hover" :disabled="row.remark.length<=0" transfer>
          <div slot="content" style="white-space: normal;">{{row.remark}}</div>
          <span v-if="!row.online" style="color: LightGrey; ">{{row.name}}</span>
          <span v-else>{{row.name}}</span>
        </Tooltip>
      </template>
      <template slot-scope="{ row, index }" slot="scan_qr">
        <template v-if="row.hasOwnProperty('name')">
          <Poptip trigger="hover" :title="row.name" content="content" placement="left" transfer>
            <div slot="content">
              <qriously :value="row.example" :size="200" />
            </div>
            <Icon type="md-qr-scanner" style="padding-top: 5px" />
          </Poptip>
        </template>
        <template v-else>
          <Poptip content="content" placement="left">
            <div slot="content">
              <qriously :value="row.url_scheme" :size="180" />
            </div>
            <Icon type="md-qr-scanner" style="padding-top: 5px" />
          </Poptip>
        </template>
      </template>
      <template slot-scope="{ row, index }" slot="page_tag">
        <template v-if="row.hasOwnProperty('priority') && row.priority.length > 1">
          <Tag :color="getPriorityTagColor(row.priority)">{{row.priority}}</Tag>
        </template>
        <template v-if="row.hasOwnProperty('mock_id') && row.mock_id !== null && row.mock_id.length > 1">
          <Tooltip transfer placement="top">
            <div slot="content">
              {{row.mock_id}}
            </div>
            <Tag color="cyan">M</Tag>
          </Tooltip>
        </template>
        <template v-if="(JSON.parse(row.actions)? JSON.parse(row.actions).text_keys:[]).length>0">
          <Tooltip transfer placement="top">
            <div slot="content">
              {{JSON.parse(row.actions).text_keys}}
            </div>
            <Tag color="orange">T</Tag>
          </Tooltip>
        </template>
        <template v-if="(row.params? JSON.parse(row.params).cropRects:[]).length>0">
          <Tooltip transfer placement="top">
            <div slot="content">
              识别/屏蔽区
            </div>
            <Tag color="green">R</Tag>
          </Tooltip>
        </template>
        <template v-if="row.example.indexOf('targetViewId')!=-1">
          <Tooltip transfer placement="top">
            <div slot="content">
              viewId交互
            </div>
            <Tag color="pink">V</Tag>
          </Tooltip>
        </template>
      </template>
    </Table>
    <div style="margin: 10px;overflow: hidden" v-if="tableData.length >= 10">
      <div style="float: right;">
        <Page
          :total="tableData.length"
          :current="showIndex"
          :page-size="showSize"
          :page-size-opts="pageSizeOpts"
          show-sizer
          show-total
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        />
      </div>
    </div>
    <Modal
      v-model="modalShow"
      :title="pageGroupType"
      @on-ok="checkUserPermission"
      @on-cancel="modalShow=false"
    >
      <Form :label-width="100">
        <FormItem label="App:">{{app}}</FormItem>
        <FormItem label="OS:">{{os}}</FormItem>
        <FormItem label="业务方向:">{{business}}</FormItem>
        <FormItem label="页面数量:">{{selectedPages.length}}</FormItem>
        <FormItem label="页面组名称:">
          <Input v-model="pageGroupName" placeholder="页面组名"></Input>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import event from "@/assets/event_bus";
export default {
  name: "ClientPageTable",
  props: ["tableData", "tableColumns"],
  data() {
    return {
      app: "",
      os: "",
      business: "",
      pageGroupList: [],
      selectedPages: [],
      pageGroupName: "",
      selectedPageGroupId: "0",
      modalShow: false,
      showIndex: 1,
      showSize: 50,
      pageSizeOpts: [10, 30, 50],
      spinShow: true,
    };
  },
  computed: {
    showData: function() {
      let showData = this.tableData;
      if (this.showSize < this.tableData.length) {
        showData = this.tableData.slice(
          (this.showIndex - 1) * this.showSize,
          (this.showIndex - 1) * this.showSize + this.showSize
        );
      }
      return showData;
    },
    pageGroupType: function() {
      let pageGroupType = "";
      if (this.selectedPageGroupId === "0") {
        pageGroupType = "创建页面组";
      } else {
        pageGroupType = "更新页面组";
      }
      return pageGroupType;
    },
    pageGroupIcon: function() {
      let pageGroupIcon = "";
      if (this.selectedPageGroupId === "0") {
        pageGroupIcon = "md-add";
      } else {
        pageGroupIcon = "md-refresh";
      }
      return pageGroupIcon;
    }
  },
  mounted() {
    event.$on("getPageData", (pageInfo, type) => {
      this.selectedPages = [];
      this.app = pageInfo.app;
      this.os = pageInfo.os;
      this.business = pageInfo.business;
      if (type !== "refresh") {
        this.selectedPageGroupId = "0";
      }
      this.pageGroupName = "";
      this.getPageGroup();
    });
  },
  updated() {
    let pageObjData = this.$refs.pageTable.objData;
    for (let j = 0; j < this.showData.length; j++) {
      this.$refs.pageTable.objData[j]._isChecked = false;
    }
    for (let i = 0; i < this.selectedPages.length; i++) {
      for (let j = 0; j < this.showData.length; j++) {
        if (pageObjData[j].id.toString() === this.selectedPages[i].toString()) {
          this.$refs.pageTable.objData[j]._isChecked = true;
        }
      }
    }
  },
  methods: {
    getPageGroup() {
      this.$axios({
        method: "post",
        data: {
          app: this.app,
          business: this.business,
          os: this.os
        },
        url: this.env.url + "clientPage/getPageGroup"
      })
        .then(res => {
          let message = res.data;
          this.pageGroupList = message;
          this.selectPageGroup();
        })
        .catch(function(error) {});
    },
    selectPageGroup() {
      if (this.selectedPageGroupId === "0") {
        this.selectedPages = [];
        this.pageGroupName = "";
      } else {
        this.$emit("getSortPages", this.selectedPageGroupId);
        for (let i = 0; i < this.pageGroupList.length; i++) {
          if (this.pageGroupList[i].id === this.selectedPageGroupId) {
            this.selectedPages = this.getGroupValidPages(this.pageGroupList[i].pages_id)
            this.pageGroupName = this.pageGroupList[i].group_name;
          }
        }
      }
    },
    checkUserPermission() {
      this.$axios({
        method: "get",
        params: {
          business: this.business,
          user: Bus.userInfo.userLogin
        },
        url: this.env.url + "clientSpecialConfigInfo/userPermission"
      })
        .then(res => {
          let message = res.data;
          if (message.code === 200) {
            this.createPageGroup();
          } else {
            alert("无权限！");
            this.getPageGroup();
            if (this.selectedPageGroupId != "0") {
              this.$emit("getSortPages", this.selectedPageGroupId);
            }
          }
        })
        .catch(function(error) {});
    },
    createPageGroup() {
      if (this.pageGroupName.length > 0) {
        this.$axios({
          method: "post",
          data: {
            app: this.app,
            os: this.os,
            business: this.business,
            pages_id: this.selectedPages.toString(),
            group_name: this.pageGroupName,
            user: Bus.userInfo.userLogin,
            id: this.selectedPageGroupId.toString()
          },
          url: this.env.url + "clientPage/createPageGroup"
        })
          .then(res => {
            let message = res.data;
            if (message.code === 0) {
              this.$Notice.success({
                title: "页面组信息更新成功"
              });
              this.getPageGroup();
              if (this.selectedPageGroupId != "0") {
                this.$emit("getSortPages", this.selectedPageGroupId);
              }
            }
          })
          .catch(function(error) {});
      } else {
        this.$Notice.warning({
          title: "请填写合法的页面组名称"
        });
      }
    },
    pageSelection(selection, row) {
      this.selectedPages.push(row.id);
    },
    cancelPageSelection(selection, row) {
      for (let i = 0; i < this.selectedPages.length; i++) {
        if (this.selectedPages[i].toString() === row.id.toString()) {
          this.selectedPages.splice(i, 1);
        }
      }
    },
    pageSelectAll(selection) {
      this.selectedPages = [];
      for (let i = 0; i < this.tableData.length; i++) {
        this.selectedPages.push(this.tableData[i].id);
      }
    },
    cancelPageSelectAll(selection) {
      this.selectedPages = [];
      let pageObjData = this.$refs.pageTable.objData;
      for (let i in pageObjData) {
        this.$refs.pageTable.objData[i]._isChecked = false;
      }
    },
    changePage(index) {
      this.showIndex = index;
    },
    changePageSize(size) {
      this.showSize = size;
    },
    getPriorityTagColor(priority) {
      switch (priority.toUpperCase()) {
        case 'P0':
          return 'gold'
        case 'P1':
          return 'volcano'
        case 'P2':
          return 'blue'
        default:
          return 'green'
      }
    },
    getGroupValidPages(pageIDs) {
      let validPageID = [];
      let pageIDList = pageIDs.trim().split(',');
      pageIDList.forEach((value) => {
        if (value.trim() !== '') {
          validPageID.push(value.trim());
        }
      })
      return validPageID;
    }
  }
};
</script>

<style scoped>
.pageSelectButton {
  padding-top: 3px;
}
.pageSelectShow {
  padding-top: 5px;
}
.pageSelectCheck {
  padding-top: 2px;
  color: red;
  font-size: small;
}
</style>
