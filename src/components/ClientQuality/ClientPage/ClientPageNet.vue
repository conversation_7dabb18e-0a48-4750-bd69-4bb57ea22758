<template>
  <div>
    <Alert show-icon style="text-align: left;">
      PageNet通过手工测试上报单点事件进行过滤和队列处理获得关联关系，逐步建立质量领域知识图谱，详见
      <a href="https://km.sankuai.com/page/357922209" target="_blank">PageNet</a>
    </Alert>
    <div id="container"></div>
  </div>
</template>

<script>
  import go from 'gojs'
  /* eslint-disable */
  export default {
    name: "ClientPageNet",
    props: ["pageInfo"],
    data(){
      return{
        make:[],
        container:[],
      }
    },
    mounted() {
      this.MAKE = go.GraphObject.make;  // for conciseness in defining templates
      this.container =
        this.MAKE(go.Diagram, "container",  // create a Diagram for the DIV HTML element
          { // enable undo & redo
            "undoManager.isEnabled": true
          });

      // define a simple Node template
      this.container.nodeTemplate =
        this.MAKE(go.Node, "Auto",  // the Shape will go around the TextBlock
          this.MAKE(go.Shape, "RoundedRectangle",
            { strokeWidth: 0, fill: "white" },  // default fill is white
            // Shape.fill is bound to Node.data.color
            new go.Binding("fill", "color")),
          this.MAKE(go.TextBlock,
            { margin: 8 },  // some room around the text
            // TextBlock.text is bound to Node.data.key
            new go.Binding("text", "key"))
        );

      // but use the default Link template, by not setting Diagram.linkTemplate

    },
    watch:{
      pageInfo(){
        if(this.pageInfo.url){
          this.getPageNet()
        }
      }
    },
    methods:{
      getPageNet() {
        this.$axios({
          method: "post",
          data: {
            pageKey:this.pageInfo.url,
            business:this.pageInfo.business
          },
          url: this.env.url + "clientPage/pageNet"
        })
          .then(res => {
            let message = res.data;
            let sourcePages = message.sourcePages;
            let targetPages = message.targetPages;
            let relatedPages = Array.from(new Set(sourcePages.concat(targetPages)));
            this.createPageNet(relatedPages);
          })
          .catch(function(error) {});
      },
      createPageNet(relatedPages){
        // create the model data that will be represented by Nodes and Links
        let keys = [];
        let links = [];
        let currentPageName = this.pageInfo.name;
        keys.push({key:currentPageName, color:"orange"});
        for(let i=0;i<relatedPages.length;i++){
          keys.push({key:relatedPages[i], color:"lightblue"});
          // links.push({from:relatedPages[i], to:currentPageName});
          links.push({from:currentPageName, to:relatedPages[i]});
        }
        this.container.model = new go.GraphLinksModel(keys, links);
      }

    }

  };
</script>

<style>
  #container {
    width: 100%;
    height: 500px;
  }
</style>
