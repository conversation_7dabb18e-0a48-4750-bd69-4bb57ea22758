<template>
  <div>
    <Table max-height="700" :data="showData" :columns="tableColumns" @on-select="getPageSelect">
      <template slot-scope="{ row, index }" slot="scan_qr">
        <template v-if="row.hasOwnProperty('name')">
          <Poptip trigger="hover" :title=row.name content="content" placement="left" transfer>
            <div slot="content">
              <qriously :value="row.example" :size="180" />
            </div>
            <Icon type="md-qr-scanner" style="padding-top: 5px"/>
          </Poptip>
        </template>
        <template v-else>
          <Poptip trigger="hover" content="content" placement="left" transfer>
            <div slot="content">
              <qriously :value="row.url_scheme" :size="180" />
            </div>
            <Icon type="md-qr-scanner" style="padding-top: 5px"/>
          </Poptip>
        </template>

      </template>
    </Table>
    <div style="margin: 10px;overflow: hidden" v-if="tableData.length >= 10">
      <div style="float: right;">
        <Page :total="tableData.length" :current="showIndex" :page-size="showSize" :page-size-opts="pageSizeOpts"
              show-sizer show-total  @on-change="changePage" @on-page-size-change="changePageSize"/>
      </div>
    </div>
  </div>
</template>

<script>
  /* eslint-disable */
  export default {
    name: "ClientSchemeTable",
    props: ['tableData', 'tableColumns'],
    data () {
      return {
        selectedPages:[],
        showIndex: 1,
        showSize: 10,
        pageSizeOpts: [10, 15, 20, 30, 50]
      }
    },
    computed: {
      showData: function () {
        let showData = this.tableData
        if (this.showSize < this.tableData.length) {
          showData = this.tableData.slice((this.showIndex - 1) * this.showSize,
            (this.showIndex - 1) * this.showSize + this.showSize)
        }
        return showData
      }
    },
    methods: {
      getPageSelect(selection, row){
        console.log(selection.length)
        console.log(row)
      },
      changePage (index) {
        this.showIndex = index
      },
      changePageSize (size) {
        this.showSize = size
      }
    }
  }
</script>

<style scoped>

</style>
