<template>
  <div>
    <Modal
      :value="pageInfoShow"
      :title="pageInfoTitle"
      @on-cancel="closePageInfo"
      :mask-closable="false"
    >

      <Form ref="formItem" :model="formItem" :rules="formRules" :label-width="80">
        <FormItem label="App" prop="app">
          <Select v-model="formItem.app" @on-change="getPageGroupsByFilter">
            <Option v-for="appName in groupAppNames" :key="appName.app_name" :value="appName.app_name">{{appName.label}}</Option>
          </Select>
        </FormItem>
        <FormItem label="平台" prop="os">
          <Select v-model="formItem.os" @on-change="getPageGroupsByFilter">
            <Option value="Android">Android</Option>
            <Option value="iOS">iOS</Option>
            <Option value="Android,iOS">Android,iOS</Option>
            <Option value="Web">Web</Option>
          </Select>
        </FormItem>
        <FormItem label="业务方向" prop="business">
          <Select v-model="formItem.business" @on-change="getPageGroupsByFilter">
            <Option v-for="group in oldBusinesses" :key="group.value" :value="group.value">{{group.label}}</Option>
          </Select>
          <div style="color:orangered"><Icon type="ios-alert-outline" /> 已迁移MMCD的业务线请移步至MMCD新增页面</div>
        </FormItem>
        <Form-item label="名称" prop="name">
          <Input v-model="formItem.name"></Input>
        </Form-item>
        <FormItem label="页面地址" prop="example">
          <Input type="textarea" :rows="2" v-model="formItem.example" placeholder="e.g. Web: https://bj.meituan.com/
          App: imeituan://www.meituan.com/travel/debugconfig?..."></Input>
        </FormItem>
        <FormItem label="页面组" prop="page_groups_id">
          <Select
            v-if="update"
            placeholder="请选择页面组"
            filterable
            multiple
            v-model="formItem.page_groups_id"
          >
            <template v-for="item in pageGroupList">
              <Option :value="item.id.toString()" :label="item.group_name">
                <span>{{item.os}}-{{item.group_name}}</span>
                <span
                  style="float:right;color:#ccc"
                >{{item.pages_id.split(',').length}}/{{item.user}}</span>
              </Option>
            </template>
          </Select>
        </FormItem>
        <FormItem label="滑屏次数">
          <Input v-model="formItem.scroll_count"></Input>
        </FormItem>
        <FormItem label="交互文本">
          <Row>
            <Col span="23">
              <Select v-model="textKeys" clearable  multiple filterable @on-query-change="getActionText"
                      placeholder="请按交互顺序输入文本">
                <Option v-for="(item,index) in actionText" :value="item.value" :key="index">{{item.label}}</Option>
              </Select>
            </Col>
            <Col span="1">
              <a href="https://km.sankuai.com/page/296924567" target="_blank">
                <Icon type="md-help-circle" />
              </a>
            </Col>
          </Row>
        </FormItem>
        <FormItem label="在线状态" prop="online">
          <i-switch size="large" v-model="formItem.online">
            <span slot="open">在线</span>
            <span slot="close">下线</span>
          </i-switch>
        </FormItem>
      </Form>
      <Collapse>
        <panel>
          其他配置
          <Form slot="content"  :label-width="60">
            <FormItem label="优先级">
              <Select v-model="formItem.priority">
                <Option value="p1">p1</Option>
                <Option value="p2">p2</Option>
                <Option value="p9">p9(ShortLink-Only)</Option>
              </Select>
            </FormItem>
            <FormItem label="最小版本">
              <Input v-model="formItem.mini_version"></Input>
            </FormItem>
            <FormItem label="备注">
              <Input v-model="formItem.remark"></Input>
            </FormItem>
          </Form>
        </panel>
      </Collapse>
      <div v-if="pageInfoTitle==='添加页面信息'" slot="footer">
        <Button type="primary" size="small" @click="checkPage('saveAs')">确定</Button>
        <Button type="info" size="small" @click="closePageInfo">取消</Button>
      </div>
      <div v-else slot="footer">
        <Button type="primary" size="small" @click="checkPage('saveAs')">另存为</Button>
        <Button type="primary" size="small" @click="checkPage('save')">保存</Button>
        <Button type="info" size="small" @click="closePageInfo">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
export default {
  name: "ClientPageInfo",
  props: ["pageInfoShow", "formItem", "pageInfoTitle", "oldBusinesses"],
  data() {
    return {
      groups: [],
      groupAppNames:[],
      formRules: {
        app: [{ required: true, message: "App信息", trigger: "change" }],
        os: [{ required: true, message: "系统信息", trigger: "change" }],
        business: [{ required: true, message: "业务方向", trigger: "change" }],
        name: [{ required: true, message: "页面名称", trigger: "change" }],
        example: [{ required: true, message: "页面地址", trigger: "change" }]
      },
      pageGroupList: [],
      oldFilter: {
        app: "",
        os: "",
        business: ""
      },
      update: true,
      textKeys: [],
      actionText : []
    };
  },
  watch: {
    pageInfoShow() {
      if (this.pageInfoShow) {
        this.oldFilter.app = this.formItem.app;
        this.oldFilter.os = this.formItem.os;
        this.oldFilter.business = this.formItem.business;
        this.actionText = this.formItem.actionText;
        this.textKeys = this.formItem.textKeys;
        this.getPageGroup();
      }
    }
  },
  mounted() {
    this.getGroupAppNames();
  },
  methods: {
    checkPage(flag) {
      this.$refs["formItem"].validate(valid => {
        if (valid) {
          if (flag === "saveAs") {
            this.formItem.rowIndex = "";
          }
          this.checkPageName();
        } else {
          this.$Notice.warning({
            title: "请补充信息"
          });
        }
      });
    },
    getActionText(text){
      clearTimeout(this.timer);
      this.timer = setTimeout(()=>{
        this.actionText=[{
          "label":text,
          "value":text
        }]
      },100);
    },
    addPage() {
      this.formItem.actions = JSON.stringify({"text_keys":this.textKeys});
      this.$axios({
        method: "post",
        data: this.formItem,
        url: this.env.url + "clientPage/addPage"
      })
        .then(res => {
          let message = res.data;
          if (message.code === 0) {
            this.$Notice.success({
              title: "保存成功"
            });
          }
          this.closePageInfo("refresh");
        })
        .catch(function(error) {});
    },
    confirmPermission() {
      this.$axios({
        method: "post",
        params: {
          business: this.formItem.business,
          user: Bus.userInfo.userLogin,
          pageId: this.formItem.rowIndex
        },
        url: this.env.url + "clientPage/confirmPermission"
      })
        .then(res => {
          let message = res.data;
          if (message.code === 200) {
            this.addPage();
          } else {
            alert("无权限！");
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    checkPageName() {
      if (this.formItem.rowIndex === "") {
        this.$axios({
          method: "get",
          params: {
            app: this.formItem.app,
            business: this.formItem.business,
            os: this.formItem.os
          },
          url: this.env.url + "clientPage/getPageName"
        })
          .then(res => {
            let message = res.data;
            let nameList = [];
            for (let i = 0; i < message.length; i++) {
              nameList.push(message[i].name);
            }
            if (nameList.indexOf(this.formItem.name) > -1) {
              this.$Notice.warning({
                title: "页面名称在当前业务已存在"
              });
            } else {
              this.confirmPermission();
            }
          })
          .catch(function(error) {});
      } else {
        this.confirmPermission();
      }
    },
    closePageInfo(type) {
      this.$emit("closePageInfo", this.formItem, type);
    },
    getGroupAppNames() {
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getGroupAppNames"
      })
        .then(res => {
          let message = res.data;
          this.groupAppNames = message;
        })
        .catch(function(error) {});
    },
    getPageGroup() {
      this.$axios({
        method: "post",
        data: {
          app: this.formItem.app,
          business: this.formItem.business,
          os: this.formItem.os
        },
        url: this.env.url + "clientPage/getPageGroup"
      })
        .then(res => {
          let message = res.data;
          this.pageGroupList = message;
          this.reload();
        })
        .catch(function(error) {});
    },
    getPageGroupsByFilter() {
      this.formItem.page_groups_id = [];
      this.getPageGroup();
    },
    reload() {
      // 移除组件
      this.update = false;
      // 在组件移除后，重新渲染组件
      // this.$nextTick可实现在DOM 状态更新后，执行传入的方法。
      this.$nextTick(() => {
        this.update = true;
      });
    }
  }
};
</script>

<style scoped>
</style>
