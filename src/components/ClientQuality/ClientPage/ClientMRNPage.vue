<template>
    <div>
      <Row>
        <Col span="6">
          <div style="padding: 15px 0">
            <Select v-model="bundleName" filterable @on-change="getPageByMRNBundleName()" :transfer="true">
              <Option v-for="item in bundleList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </Col>
      </Row>
      <ClientSchemeTable :tableColumns="pageColumns" :tableData="pageShow"></ClientSchemeTable>
    </div>
</template>

<script>
    /* eslint-disable */
    import ClientSchemeTable from "./ClientSchemeTable";
    export default {
      name: "ClientMRNPage",
      components: {ClientSchemeTable},
      data(){
        return{
          bundleList:[],
          bundleName:'',
          pageShow:[],
          pageColumns: [
            {
              title: '序号',
              type: 'index',
              width:60
            }, {
              title: '页面名称',
              key: 'name',
              width: 350
            }, {
              title: '页面key',
              key: 'page_key',
              width: 650,
            },{
              title: '页面地址',
              width: 660,
              key: 'example',
              render:(h, params)=>{
                let name = params.row.name
                let example = '';
                if(params.row.hasOwnProperty('example') && params.row.example.length > 75){
                  example = params.row.example.slice(0,75)+"...";
                }else {
                  example = params.row.example
                }
                return h("Poptip",{
                  attrs: {
                    title:name,
                    content:params.row.example,
                    width:"500",
                    wordWrap:true,
                    trigger: 'hover',
                    transfer:true
                  }
                },example)
              }
            }, {
              title: '扫码跳转',
              slot: 'scan_qr',
              width:100,
              fixed: "right"
            }
          ]
        }
      },
      mounted(){
        this.getMRNBundleList();
      },
      methods:{
        getMRNBundleList(){
          this.$axios({
            method:"get",
            url:this.env.url+"client/MRNBundleList"
          }).then((res) => {
            let message = res.data;
            this.bundleList=message;
          }).catch(function (error) {
            console.log(error)
          })
        },
        getPageByMRNBundleName(){
          this.$axios({
            method:"get",
            params:{
              'MRNBundle': this.bundleName
            },
            url:this.env.url+"client/MRNBundlePage"
          }).then((res) => {
            let message = res.data;
            this.pageShow=message;
          }).catch(function (error) {
            console.log(error)
          })
        },
      }
    }
</script>

<style scoped>

</style>
