<template>
  <div>
    <Alert type="warning" show-icon style="text-align: left">已迁移MMCD的方向: <strong>{{mmcdBusinesses.join(', ')}}</strong>请移步至MMCD页面管理</Alert>
    <Tabs>
      <TabPane label="页面管理">
        <div style="padding-bottom: 18px">
          <Row>
            <Col span="2">
              <div style="padding: 0 10px">
                <Select v-model="category" @on-change="getPageByGroup" :transfer="true">
                  <Option v-for="appName in groupAppNames" :key="appName.app_name" :value="appName.app_name">{{appName.label}}</Option>
                </Select>
              </div>
            </Col>
            <Col span="3">
              <div style="padding: 0 10px">
                <Select
                  placeholder="请选择业务线"
                  v-model="filterInfo.business"
                  @on-change="getPageByGroup"
                  :transfer="true"
                >
                  <Option
                    v-for="item in businessList"
                    :value="item.value"
                    :key="item.value"
                  >{{item.label}}</Option>
                </Select>
              </div>
            </Col>
            <Col span="2">
              <div style="padding: 0 10px">
                <Select
                  placeholder="请选择OS"
                  v-model="filterInfo.os"
                  @on-change="getPageByGroup"
                  :transfer="true"
                >
                  <Option
                    v-for="item in groupData"
                    :value="item.value"
                    :key="item.value"
                  >{{item.label}}</Option>
                </Select>
              </div>
            </Col>
            <Col span="3">
              <div style="padding: 0 10px">
                <Select
                  multiple
                  placeholder="优先级"
                  v-model="selectedPriority"
                  @on-change="getPageByGroup"
                  :transfer="true"
                >
                  <Option
                    v-for="item in priorityOptions"
                    :value="item.value"
                    :key="item.value"
                  >{{item.label}}</Option>
                </Select>
              </div>
            </Col>
            <Col span="5">
              <div style="padding: 0 10px">
                <Input
                  search
                  placeholder="输入关键字进行搜索"
                  v-model="pageFilterInfo"
                  @on-change="pageFilterDebounce"
                  clearable
                />
              </div>
            </Col>
            <Col span="3">
              <div style="padding-top: 3px">
                <Button
                  type="primary"
                  shape="circle"
                  icon="md-add"
                  size="small"
                  @click="addPage"
                >添加页面</Button>
              </div>
            </Col>
          </Row>
        </div>
        <ClientPageTable
          :tableColumns="pageColumns"
          :tableData="pageShow"
          v-on:getSortPages="getPagesByPageGroupId"
        ></ClientPageTable>
        <Spin size="large" fix v-if="loadingStatus"></Spin>
      </TabPane>
      <TabPane label="MRN Bundle">
        <ClientMRNPage></ClientMRNPage>
      </TabPane>
    </Tabs>
    <ClientPageInfo
      :pageInfoShow="pageInfoShow"
      :formItem="formItem"
      :pageInfoTitle="pageInfoTitle"
      :oldBusinesses="oldBusinesses"
      v-on:closePageInfo="closePageInfo"
    ></ClientPageInfo>
  </div>
</template>

<script>
/* eslint-disable */
import ClientPageTable from "./ClientPageTable";
import ClientPageInfo from "./ClientPageInfo";
import ClientMRNPage from "./ClientMRNPage";
import event from "@/assets/event_bus";
import { Bus } from "@/global/bus";
export default {
  name: "ClientScheme",
  components: { ClientMRNPage, ClientPageInfo, ClientPageTable },
  data() {
    return {
      loadingStatus: true,
      pageFilterInfo: "",
      mmcdBusinesses: [],
      pageColumns: [
        {
          type: "selection",
          width: 35,
          align: "center"
        },
        {
          title: "序号",
          type: "index",
          width: 60
        },
        {
          title: "App",
          key: "app",
          width: 90
        },
        {
          title: "平台",
          key: "os",
          width: 85,
          render:(h, params)=> {
            if (params.row.os.toUpperCase().trim() === 'IOS') {
              return h('img', {
                style: {
                  textAlign: 'center',
                  height: '24px',
                  width: '24px'
                },
                attrs: {
                  title: 'iOS',
                  class: 'layout-logo',
                  src: '/static/img/ios.png'
                }
              })
            } else if (params.row.os.toUpperCase().trim() === 'ANDROID') {
              return h('img', {
                style: {
                  textAlign: 'center',
                  height: '24px',
                  width: '24px'
                },
                attrs: {
                  src: '/static/img/android.png',
                  class: 'layout-logo',
                  title: 'Android'
                }
              })
            } else if (params.row.os.toUpperCase().trim() === 'WEB') {
              return h('img', {
                style: {
                  textAlign: 'center',
                  height: '24px',
                  width: '24px'
                },
                attrs: {
                  src: '/static/img/web.png',
                  class: 'layout-logo',
                  title: 'Web'
                }
              })
            } else {
              return h('div', [
                h('img', {
                  style: {
                    textAlign: 'center',
                    height: '24px',
                    width: '24px'
                  },
                  attrs: {
                    class: 'layout-logo',
                    title: 'MRN',
                    src: '/static/img/mrn.png'
                  }
                }),
                h('img', {
                  style: {
                    textAlign: 'center',
                    height: '24px',
                    width: '24px'
                  },
                  attrs: {
                    title: 'H5',
                    class: 'layout-logo',
                    src: '/static/img/h5.png'
                  }
                }),
              ])

            }
          }
        },
        {
          title: "业务",
          key: "business",
          sortable: true,
          width: 115
        },
        {
          title: "名称",
          key: "name",
          width: 450,
          slot: "page_name",
          sortable: true
        },
        {
          title: "标签",
          key: "priority",
          slot: "page_tag",
          width: 220
        },
        {
          title: "页面地址",
          key: "example",
          width: 465,
          render: (h, params) => {
            let name = params.row.name;
            let example = "";
            if (params.row.example.length > 60) {
              example = params.row.example.slice(0, 60) + "...";
            } else {
              example = params.row.example;
            }
            return h(
              "Poptip",
              {
                attrs: {
                  transfer: true,
                  title: name,
                  content: params.row.example,
                  width: "500",
                  wordWrap: true,
                  trigger: 'hover'
                }
              },
              example
            );
          }
        },
        {
          title: "扫码",
          slot: "scan_qr",
          width: 60,
          fixed: "right",
        },
        {
          title: "操作",
          width: 150,
          fixed: "right",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    size: "small",
                    icon: "md-menu"
                  },
                  style: {
                    marginRight: "5px"
                  },
                  attrs: {
                    title: "页面详情看板"
                  },
                  on: {
                    click: () => {
                      this.openLink(params.row.id)
                    }
                  }
                }
              ),
              h(
                "Button",
                {
                  props: {
                    type: "info",
                    size: "small",
                    icon: "md-create"
                  },
                  style: {
                    marginRight: "5px"
                  },
                  attrs: {
                    title: "编辑"
                  },
                  on: {
                    click: () => {
                      this.pageInfoTitle = "更新页面信息";
                      this.changePageInfo(params.row, params.row.id);
                    }
                  }
                }
              ),
              h(
                "Button",
                {
                  props: {
                    type: "error",
                    size: "small",
                    icon: 'md-trash'
                  },
                  style: {
                    marginRight: "5px"
                  },
                  attrs: {
                    title: "删除"
                  },
                  on: {
                    click: () => {
                      this.confirmPermission(params.row);
                    }
                  }
                }
              )
            ]);
          }
        },
        {
          title: "备注",
          key: "remark",
          width: 100,
          fixed: "right",
          render: (h, params) => {
            let name = params.row.remark;
            let remark = "";
            if (params.row.remark.length > 60) {
              remark = params.row.remark.slice(0, 60) + "...";
            } else {
              remark = params.row.remark;
            }
            return h(
              "Poptip",
              {
                attrs: {
                  title: name,
                  content: params.row.remark,
                  width: "500",
                  wordWrap: true,
                  transfer: true
                }
              },
              remark
            );
          }
        }
      ],
      pageData: [],
      pageShow: [],
      selectedPriority:["p1","p2"],
      pageInfoShow: false,
      pageInfoTitle: "",
      category: "meituan",
      groupAppNames:[],
      filterInfo: {
        business: "",
        os: ""
      },
      priorityOptions:[
        {
          value: "p1",
          label: "p1"
        },
        {
          value: "p2",
          label: "p2"
        },
        {
          value: "p9",
          label: "p9(ShortLink-Only)"
        },
      ],
      groupData: [
        {
          value: "Android",
          label: "Android"
        },
        {
          value: "iOS",
          label: "iOS"
        },
        {
          value: "Android,iOS",
          label: "MRN/H5"
        },
        {
          value: "Web",
          label: "Web"
        }
      ],
      formItem: {
        app: "",
        os: "",
        business: "",
        name: "",
        url: "",
        priority: "p1",
        example: "",
        scroll_count: "0",
        mini_version: "",
        mock_id: "",
        rowIndex: "",
        page_groups_id: [],
        old_page_groups_id: [],
        online: true,
        textKeys: [],
        remark: ""
      },
      deletePageGroups: {
        pageId: "",
        pageGroups: []
      },
      update: true
    };
  },
  computed:{
    businessList(){
      return this.$store.state.clientQuality.clientGroups;
    },
    oldBusinesses() {
      if (this.mmcdBusinesses.length === 0) {
        return this.businessList;
      } else {
        let validBusinesses = [];
        for (let i=0; i < this.businessList.length; i++) {
          if (this.mmcdBusinesses.indexOf(this.businessList[i].group_name) < 0) {
            validBusinesses.push(this.businessList[i]);
          }
        }
        return validBusinesses;
      }
    }
  },
  mounted() {
    this.getMMCDBusinesses();
    this.getGroupAppNames();
    this.getBusinessByUser();
  },
  methods: {
    getMMCDBusinesses () {
      this.$axios({
        method:"get",
        url:"http://client.hotel.test.sankuai.com/client/lyrebird/mock/group/business"
      }).then((res) => {
        this.mmcdBusinesses = res.data;
      }).catch(function (error) {
        console.log(error)
      })
    },
    changePageInfo(row, rowId) {
      this.formItem.app = row.app;
      this.formItem.os = row.os;
      this.formItem.business = row.business;
      this.formItem.name = row.name;
      this.formItem.url = row.url;
      this.formItem.priority = row.priority;
      this.formItem.example = row.example;
      this.formItem.scroll_count =
        row.scroll_count === -1 ? 5 : row.scroll_count;
      this.formItem.mini_version = row.mini_version;
      this.formItem.mock_id = row.mock_id;
      this.formItem.rowIndex = rowId;
      this.formItem.online = row.online;
      this.formItem.remark = row.remark;
      this.formItem.page_groups_id = [];
      this.formItem.textKeys = JSON.parse(row.actions)
        ? JSON.parse(row.actions).text_keys
        : [];
      this.formItem.actionText = this.getActionKeys(this.formItem.textKeys);
      this.getPageGroupsByPageId();
    },
    getActionKeys(text_keys) {
      let actionText = [];
      for (let i = 0; i < text_keys.length; i++) {
        actionText.push({
          label: text_keys[i],
          value: text_keys[i]
        });
      }
      return actionText;
    },
    openLink(pageId){
      let routeData = this.$router.resolve({ path: '/client/pageBoard', query: {  pageId: pageId } });
      window.open(routeData.href, '_blank');
    },
    addPage() {
      this.pageInfoTitle = "添加页面信息";
      this.pageInfoShow = true;
      this.formItem.business = this.filterInfo.business;
      this.formItem.os = this.filterInfo.os;
      this.formItem.app = this.category;
      this.formItem.example = "";
      this.formItem.url = "";
      this.formItem.name = "";
      this.formItem.mock_id = "";
      this.formItem.online = true;
      this.formItem.remark = "";
      this.formItem.page_groups_id = [];
      this.formItem.old_page_groups_id = [];
      this.formItem.textKeys = [];
      this.formItem.rowIndex = "";
    },
    closePageInfo(val, type) {
      if (type === "refresh") {
        this.refreshPages();
        // 带mock的页面通知同步脚本
        if (val.mock_id) {
          if (val.mock_id.length > 1 && val.rowIndex.toString().length > 1
          ) {
            this.updatePageInfo(val, "update");
          }
        }
      }
      this.pageInfoShow = false;
    },
    debounce(fnName, time) {//防抖
      let timeout = null;
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        this[fnName]();
      }, time);
    },
    // throttle(fnName, time) {//节流
    //   let canRun = true
    //   if (!canRun) return
    //   canRun = false
    //   setTimeout(() => {
    //       this[fnName]();
    //       canRun = true
    //   }, time);
    // },
    pageFilter() {
      let pageFilterData = [];
      if (this.pageFilterInfo === "") {
        this.pageShow = this.pageData;
        return
      }
      for (let i = 0; i < this.pageData.length; i++) {
        if (
          this.pageData[i].example.indexOf(this.pageFilterInfo) > -1 ||
          this.pageData[i].name.indexOf(this.pageFilterInfo) > -1 ||
          this.pageData[i].priority.indexOf(this.pageFilterInfo) > -1
        ) {
          pageFilterData.push(this.pageData[i]);
        }
      }
      this.pageShow = pageFilterData;
    },
    pageFilterDebounce() {
      this.debounce('pageFilter', 500);
    },
    getPageByGroup: async function(type) {
      await this.$nextTick();
      if (this.filterInfo.business && this.filterInfo.os) {
        this.loadingStatus = true;
        this.$axios({
          method: "get",
          params: {
            app: this.category,
            business: this.filterInfo.business,
            platform: this.filterInfo.os,
            priority: this.selectedPriority.toString()
          },
          url: this.env.url + "clientPage/getPageByGroup"
        })
          .then(res => {
            let message = res.data;
            this.pageData = message;
            this.pageShow = message;
            let pageInfo = {
              app: this.category,
              os: this.filterInfo.os,
              business: this.filterInfo.business
            };
            event.$emit("getPageData", pageInfo, type);
            this.pageFilter();
            this.loadingStatus = false;
          })
          .catch(function(error) {
            this.loadingStatus = false;
            console.log(error);
          });
      } else {
        this.$Notice.warning({
          title: "请选择业务线和OS"
        });
      }
    },
    getPage() {
      this.loadingStatus = true;
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getPage"
      }).then(res => {
        this.loadingStatus = false;
        let message = res.data;
        this.pageData = message;
        this.pageShow = message;
      }).catch(function(error) {
        this.loadingStatus = false;
        this.pageShow = [];
        this.pageData = [];
      });
    },
    getGroups() {
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getGroups"
      })
        .then(res => {
          let message = res.data;
          this.businessList = message;
        })
        .catch(function(error) {});
    },
    updatePageInfo(row, type) {
      this.$axios({
        method: "post",
        data: {
          row: row,
          type: type
        },
        url: this.env.client_common_url + "client/pageInfo"
      })
        .then(res => {
          let message = res.data;
          console.log(message);
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    deletePageById(row) {
      this.$axios({
        method: "post",
        data: this.deletePageGroups,
        url: this.env.url + "clientPage/deletePageById"
      })
        .then(res => {
          let message = res.data;
          if (message.code === 200) {
            alert(row.name + " 删除成功");
            this.refreshPages();
            if (row.mock_id.length > 1) {
              this.updatePageInfo(row, "del");
            }
          } else {
            alert("删除失败");
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    confirmPermission(row) {
      this.$axios({
        method: "post",
        params: {
          business: row.business,
          user: Bus.userInfo.userLogin,
          pageId: row.id
        },
        url: this.env.url + "clientPage/confirmPermission"
      })
        .then(res => {
          let message = res.data;
          if (message.code === 200) {
            this.deletePageGroups.pageGroups = message.pageGroups;
            let pageGroupMsg = "";
            if (this.deletePageGroups.pageGroups.length > 0) {
              pageGroupMsg =
                row.name +
                " 位于以下页面组中，删除后页面组中该页面也会同步删除，确定要删除么？\n";
            } else {
              pageGroupMsg = "确定删除页面 " + row.name;
            }
            for (let i = 0; i < this.deletePageGroups.pageGroups.length; i++) {
              pageGroupMsg +=
                "\n" + this.deletePageGroups.pageGroups[i].group_name;
            }
            if (confirm(pageGroupMsg)) {
              this.deletePageGroups.pageId = row.id;
              this.deletePageById(row);
            }
          } else {
            alert("无权限!");
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    refreshPages() {
      if (this.filterInfo.business && this.filterInfo.os) {
        this.getPageByGroup("refresh");
      } else {
        this.getPage();
      }
    },
    getPagesByPageGroupId(pageGroupId) {
      this.loadingStatus=true;
      this.$axios({
        method: "get",
        params: {
          app: this.category,
          business: this.filterInfo.business,
          platform: this.filterInfo.os,
          priority: this.selectedPriority.toString(),
          pageGroupId: pageGroupId
        },
        url: this.env.url + "clientPage/getPagesByPageGroupId"
      })
        .then(res => {
          let message = res.data;
          this.pageData = message;
          this.pageShow = message;
          this.pageFilter();
          this.loadingStatus=false;
        })
        .catch(function(error) {});
    },
    getGroupAppNames() {
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getGroupAppNames"
      })
        .then(res => {
          let message = res.data;
          this.groupAppNames = message;
        })
        .catch(function(error) {});
    },
    getPageGroupsByPageId() {
      this.$axios({
        method: "get",
        params: {
          pageId: this.formItem.rowIndex
        },
        url: this.env.url + "clientPage/getPageGroupsByPageId"
      })
        .then(res => {
          let message = res.data;
          this.formItem.page_groups_id = message.pageGroups;
          this.formItem.old_page_groups_id = this.formItem.page_groups_id;
          this.pageInfoShow = true;
        })
        .catch(function(error) {});
    },
    getBusinessByUser() {
      this.$axios({
        method: "get",
        params: {
          misId: Bus.userInfo.userLogin
        },
        url: this.env.url + "clientPage/getBusinessByUser"
      })
        .then(res => {
          let message = res.data;
          if (message.error) {
            this.getPage();
          } else {
            this.filterInfo.business = message.business;
            this.filterInfo.os = "Android,iOS";
            this.getPageByGroup();
          }
        })
        .catch(function(error) {});
    }
  }
};
</script>

<style scoped>
.img-responsive img {
  width: 100%;
}
</style>
