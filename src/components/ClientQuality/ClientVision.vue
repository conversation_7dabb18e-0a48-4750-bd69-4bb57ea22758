<template>
  <div>
    <Row class="step-content">
      <Col :md="12">
        <h2>网络模型算法</h2>
        <p style="padding: 28px 100px;text-align: left">网络模型算法使用具有自动提取图像特征的能力,深度的网络处理模型特征的抽象能力越强</p>
        <p style="padding: 0 100px;text-align: left">通过处理的训练数据对网络模型进行训练,形成的模型判断计算图像中活动弹窗的概率,
          和proposal算法配合端到端的图像区域目标检测</p>
        <a href="https://github.com/Meituan-Dianping/vision" target="_blank">Vision项目地址</a>
      </Col>
      <Col :md="12">
        <img class="img" src="/static/img/model_image.png">
      </Col>
    </Row>
    <div style="text-align: left">

    </div>
    <Row>
      <Col :md="12">
        <h2>在线模型计算</h2>
      </Col>
    </Row>
    <Steps :current="currentStep" class="step-content">
      <Step title="上传图片" icon="md-cloud-upload"></Step>
      <Step title="模型计算" icon="ios-analytics"></Step>
      <Step title="活动弹窗定位" icon="ios-image"></Step>
    </Steps>
    <Row  class="step-content">
      <Col :md="8">
        <div style="padding-bottom: 18px">
          <a href="/static/img/image_1.png" download="">测试图像1</a>
          <a href="/static/img/image_2.png" download="">测试图像2</a>
        </div>
        <Upload
          ref="upload"
          type="drag"
          :action="aiUrl"
          :on-success="onSuccess"
          :on-error="onError"
          :before-upload="beforeUpload"
          style="padding-left:50px">
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>Click or drag files here to upload</p>
          </div>
        </Upload>
      </Col>
      <Col :md="16">
        <div style="padding-bottom: 18px" v-if="show">
          <p v-if="hasPopWindow">发现活动弹窗</p>
          <p v-else>没有活动弹窗</p>
        </div>
        <div style="padding-bottom: 18px" v-if="hasPopWindow">
          <Tag color="primary">可信分数:{{score}}</Tag>
          <Tag color="primary">位置坐标:{{position}}</Tag>
        </div>
        <img :src="imgUrl" style="height: 500px;">
      </Col>
    </Row>
  </div>

</template>

<script>
  /* eslint-disable */
    export default {
        name: "ClientVision",
        data(){
          return {
            currentStep:0,
            aiUrl:this.env.vision_url+"client/vision",
            imgUrl:"",
            score:0,
            position:0,
            show:false,
            hasPopWindow:false,
          }
        },
        mounted:function () {

        },
        methods:{
          onSuccess(res){
            this.currentStep = 1;
            this.currentStep = 2;
            if(res.code == 0){
              this.show = true;
              if(res.data.score > 0) {
                this.hasPopWindow = true;
                this.score=res.data.score;
                this.position = res.data.position;
              }
              else this.hasPopWindow = false;
              this.imgUrl = this.env.vision_url+"static/predict_image.png";
            }

          },
          onError(res){
            console.log(res);
          },
          beforeUpload(){
            this.currentStep = 0;
            this.$refs.upload.clearFiles();
            this.imgUrl = "";
          }
        }
    }
</script>

<style scoped>
  .step-content{
    padding-top: 50px;
  }
  .img{
    height: 280px;
    padding-right: 60px;
  }

</style>
