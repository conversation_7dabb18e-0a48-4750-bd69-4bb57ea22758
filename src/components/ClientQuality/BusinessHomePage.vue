<template>
  <div class="layout">
    <BusinessHead :businessInfo="businessInfo" :pageInfo="pageInfo" :sceneInfo="sceneInfo"></BusinessHead>
    <Menu class="client-menu" mode="horizontal" theme="light" :active-name="tab">
      <MenuItem name="page" :to="{name:'page'}">
      <Icon type="ios-book" />
      页面列表
      </MenuItem>
      <MenuItem name="api" :to="{name:'api'}">
      <Icon type="md-list-box" />
      接口信息
      </MenuItem>
      <MenuItem name="ab" :to="{name:'ab'}">
      <Icon type="md-funnel" />
      AB 实验
      </MenuItem>
      <MenuItem name="strategy" :to="{name:'strategy'}">
      <Icon type="md-build" />
      触发策略配置
      </MenuItem>
      <MenuItem name="trigger" :to="{name:'trigger'}">
      <Icon type="ios-list-box" />
      触发列表
      </MenuItem>
      <!-- 新加的 -->
      <MenuItem name="data" :to="{name:'data'}">
      <Icon type="md-stats" />
      自动化运行情况
      </MenuItem>
      <MenuItem name="base" :to="{name:'base'}">
      <Icon type="ios-body" />
      基础信息
      </MenuItem>
    </Menu>
    <router-view></router-view>
  </div>
</template>
<script>
/* eslint-disable */
import AutotestBasicConfig from './ClientConfigCenter/AutotestBasicConfig'
import AutotestStrategyConfig from './ClientConfigCenter/AutotestStrategyConfig'
import TriggerList from './BusinessDetail/AutotestTriggerStatus/TriggerList'
import BusinessHead from './baseComponents/BusinessHead'
import ApiList from './BusinessDetail/ApiList/ApiList'
import AbList from './BusinessDetail/AbList/AbList'
import { Bus } from '@/global/bus'
export default {
  name: 'BusinessHomePage',
  components: {
    AutotestBasicConfig,
    AutotestStrategyConfig,
    BusinessHead,
    TriggerList,
    ApiList,
    AbList
  },
  props: ['businessId'],
  data() {
    return {
      pageInfo: null,
      sceneInfo: null,
      managers: {},
      members: {},
      permission: {
        isManager: false,
        isMember: false
      }
    }
  },
  created() {
    this.$store.dispatch('getPermission', {
      misId: Bus.userInfo.userLogin,
      businessId: this.businessId
    })
  },
  mounted() {
    this.$store.dispatch('getBusinessById', this.businessId)
  },
  computed: {
    businessInfo() {
      return this.$store.state.clientQuality.currentBusiness
    },
    tab() {
      return this.$store.state.clientQuality.currentTab
    }
  },
  methods: {}
}
</script>
<style scoped>
.layout {
  background: #fff;
  text-align: left;
}
.layout-content {
  margin: 10px 5px;
}
.client-menu {
  margin-bottom: 15px;
}
</style>