<template>
  <div class="checker-wrap">
    <Row style="margin-top: 10px; text-align: left; padding-left: 5px" justify="start" align="middle">
      <Col span="3" style="margin-top: 3px">
        <h4 align="left">Checker报警详情</h4>
      </Col>
    </Row>
    <Tabs v-if="isRequesting==false && buckets.length" type="card" class="checker-tabs" @on-click="changeTabs">
      <TabPane v-for="(bucket, bucketIndex) in buckets" :key="bucketIndex" :label="bucket.key" style="min-height: 600px">
        <Collapse
          style="text-align: left; border: none; font-size: small"
          v-for="(subBucket, subBucketIndex) in checkerTitleBuckets(bucketIndex)"
          :key="subBucketIndex"
          simple>
          <Panel>
            <div class="checker-panel">{{ subBucket.key }}</div>
            <Table size="small" slot="content" border="true"
            :columns="tableTitle" :data="subBucket.checkerData"></Table>
            <Button type="info" size="small" shape="circle" @click.stop="shotBug(subBucket)" style="float: right; margin:8px 15px 0 0">
              ShotBug
            </Button>
          </Panel>
        </Collapse>
        <div style="margin-top:20px; overflow: hidden; float: right; font-size: small">
          <Page
            :total="bucket.checker_title.buckets.length"
            :current="showIndex"
            :page-size="showSize"
            show-sizer
            show-total
            @on-change="changePage"
            @on-page-size-change="changePageSize"/>
        </div>
      </TabPane>
    </Tabs>
    <Row class="no-info" v-else>
      <img class="error-img" src="/static/img/cqp-error.jpg" />
      <br>
      <span>{{ errorMsg }}</span>
    </Row>
    <shot-bug :showBugInfo="showBugInfo" :bugInfo="bugInfo" v-on:closeBugInfo="showBugInfo=false"/>
    <div class="spin-container" v-if="isRequesting">
      <Spin fix>
        <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
      </Spin>
    </div>
  </div>
</template>


<script>
import ShotBug from '../baseComponents/ShotBug'
export default {
  name: 'CheckerReport',
  components: {ShotBug},
  data() {
    return {
      errorMsg: '暂无数据',
      routerQuery: this.$route.query,
      isRequesting: false,
      showIndex: 1,
      showSize: 10,
      tabIndex: 0,
      tableTitle: [
        {
          title: '展示内容',
          key: 'context',
          width: 200
        },
        {
          title: '详情',
          key: 'detail',
          render: (h, params) => {
            return h('div', {
              style: {
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word'
              }
            }, params.row.detail)
          }
        }
      ],
      buckets: [],
      hits: [],
      showBugInfo: false,
      bugInfo: {
        autoTestType: this.$route.query.sourceType,
        jobBusiness: this.$route.query.business,
        bugDesc: ''
      }
    }
  },
  // computed 返回一个数组
  computed: {
    // 手动处理分页数据
    checkerTitleBuckets() {
      return (index) => {
        let cacheBuckets = this.buckets[index].checker_title.buckets
        let data = cacheBuckets
        // 如果当前所有数据大于当前页需要展示的数量才需要分页
        if (this.showSize < cacheBuckets.length) {
          data = cacheBuckets.slice(
            (this.showIndex - 1) * this.showSize,
            (this.showIndex - 1) * this.showSize + this.showSize
          )
        }
        // 添加 message、checkerData 到分页后的数据上，每次最多处理当前页面要展示的条数
        let { sourceType } = this.routerQuery
        data.forEach((item) => {
          // 只展示符合条件的第一条数据，不需要遍历全部
          this.hits.some((i) => {
            let { event: { message, title }, env: { runner } } = i._source
            if (title === item.key) {
              let deviceDetail = sourceType === 'compatibility'
                ? [{
                  context: 'deviceName',
                  detail: runner.device.model
                }]
                : []

              item.checkerData = [{
                context: 'title',
                detail: title
              }, {
                context: 'message',
                detail: message
              }, {
                context: 'caseName',
                detail: runner.caseName
              }].concat(deviceDetail)
              item.message = message

              return true
            }
          })
        })
        return data
      }
    }
  },
  mounted() {
    if (this.routerQuery.id) {
      this.getCheckerResults(this.routerQuery.id, this.routerQuery.sourceType)
    }
  },
  methods: {
    shotBug(subBucket) {
      this.bugInfo.bugDesc = '问题描述:' +
        '\n\n\n【title】:' + subBucket.key + '\n 【message】:' + subBucket.message
      this.showBugInfo = true
    },
    changeTabs(index) {
      if (this.tabIndex === index) return
      this.tabIndex = index
      this.showIndex = 1
    },
    changePageSize (size) {
      this.showSize = size
    },
    changePage(index) {
      this.showIndex = index
    },
    getCheckerResults(id, sourceType) {
      this.isRequesting = true
      this.$axios({
        method: 'post',
        data: {
          size: 10000,
          query: {
            bool: {
              filter: {
                bool: {
                  must: [
                    {
                      match_phrase: {
                        channel: 'notice'
                      }
                    },
                    {
                      match_phrase: {
                        'env.runner.jobId': id
                      }
                    },
                    {
                      match_phrase: {
                        'env.source_type': sourceType
                      }
                    }
                  ]
                }
              }
            }
          },
          sort: {
            '@timestamp': {
              order: 'desc'
            }
          },
          aggs: {
            checker_name: {
              terms: {
                field: 'event.sender.function.keyword',
                size: 1000
              },
              aggs: {
                checker_title: {
                  terms: {
                    field: 'event.title.keyword',
                    size: 10000
                  }
                }
              }
            }
          }
        },
        url: 'https://shortlink.sankuai.com/storage/es/index/lyrebird/search'
      })
        .then((res) => {
          let {aggregations, hits} = res.data.res
          let code = res.data
          console.log('res---', res)
          this.isRequesting = false
          if (res.status !== 200) {
            // http返回非200
            this.errorMsg = '请求服务异常，请稍后重试'
          } else if (code === 1) {
            // 返回code为1
            this.errorMsg = '数据返回异常'
          } else if (hits.total.value === 0) {
            // 返回hits.total === 0
            this.errorMsg = 'Checker暂无报警信息'
          } else if (hits && hits.total.value !== 0) {
            console.log('res--->', res.data)
            this.buckets = aggregations.checker_name.buckets
            this.hits = hits.hits
          } else {
            this.errorMsg = '暂无数据'
          }
        })
        .catch((err) => {
          console.log('err--->', err)
          this.isRequesting = false
        })
    }
  }
}
</script>

<style>
.checker-wrap .checker-tabs {
  text-align: left;
  font-weight: bold;
  font-size: large;
  margin-top: 30px;
}

.checker-wrap .checker-panel {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.checker-wrap .ivu-collapse .ivu-collapse-item .ivu-collapse-header {
  display: flex;
  align-items: center;
}
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
.spin-container{
    padding: 35px;
}

.checker-wrap .no-info {
  display: block;
  text-align: center;
  padding-bottom: 20px;
  color: #464c5b;
  font-size: medium;
  font-weight: initial;
  opacity: .4;
}

.checker-wrap .error-img {
  width: 200px;
}
</style>
