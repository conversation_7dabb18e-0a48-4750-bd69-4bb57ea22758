<template>
  <Row>
    <Col span="17">
    <Confirmation :confirmInfo="confirmInfo"></Confirmation>
    <Row class="title">
      <span>场景基础信息</span>
      <div v-if="permission" style="display:contents">
        <Tooltip content="Edit" placement="top">
          <Button class="button-edit" icon="md-create" type="text" @click="toEdit" :disabled="isEdit"></Button>
        </Tooltip>
        <Tooltip v-if="sceneInfo&&sceneInfo.online===1" content="OFF Line" placement="top">
          <Button class="button-edit" icon="md-remove-circle" type="text" @click="offline"></Button>
        </Tooltip>
        <Tooltip v-else content="ON Line" placement="top">
          <Button class="button-edit" icon="md-navigate" type="text" @click="online"></Button>
        </Tooltip>
        <Tooltip content="Delete" placement="top">
          <Button class="button-edit" icon="ios-trash" type="text" @click="deleteScene"></Button>
        </Tooltip>
      </div>
    </Row>
    <Info class="form-position" v-if="!isEdit" :sceneInfo="sceneInfo"></Info>
    <InfoEdit class="form-position" v-else :editInfo="editInfo"></InfoEdit>
    <Row class="title">
      <span>场景所属产品</span>
      <Poptip trigger="hover" content="点击恢复继承关系，已有的自定义关系将丢失" placement="right-start">
        <Button class="refresh" icon="md-refresh" type="info" size="small" ghost @click="refresh"></Button>
      </Poptip>
    </Row>
    <div class="form-position">
      <ProductSelect v-for="(item, index) in sceneInfo.products" :key="index" :product="item"></ProductSelect>
      <ProductSelect v-if="isAdd" :product="productsNew"></ProductSelect>
      <Button v-if="permission" :disabled="isAdd" type="dashed" icon="md-add" @click="addProduct">Add Product</Button>
    </div>
    </Col>
    <Col span="1">
    <br>
    </Col>
    <Col span="6" style="text-align:center">
    <img class="error-img" src="/static/img/cqp-error.jpg" />
    <br>
    <span>暂无场景示意图</span>
    </Col>
  </Row>
</template>
<script>
import Confirmation from '../baseComponents/Confirmation'
import Info from './DetailInfo/InfoShow'
import InfoEdit from './DetailInfo/InfoEdit'
import { Bus } from '@/global/bus'
import ProductSelect from './DetailInfo/ProductSelect'

export default {
  name: 'SceneInfo',
  props: ['tab', 'sceneInfo'],
  components: { Confirmation, Info, InfoEdit, ProductSelect },
  data() {
    return {
      isEdit: false,
      editInfo: {},
      confirmInfo: {
        type: 'delete',
        show: false,
        message:
          '删除场景将同时删除其相关Mock数据，且不可恢复 \n 确认要删除么？',
        title: '警告⚠️'
      },
      productsNew: {}
    }
  },
  created() {
    Bus.$on('confirmDelete', this.deleteAction)
    Bus.$on('cancelEdit', () => {
      this.isEdit = false
    })
    Bus.$on('submitEdit', () => {
      this.$store.dispatch('getSceneInfoById', this.sceneInfo.id)
      this.isEdit = false
    })
    Bus.$on('cancelAdd', () => {
      this.productsNew = {}
    })
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
  },
  computed: {
    isAdd() {
      if (this.productsNew.hasOwnProperty('sceneId')) {
        return true
      } else {
        return false
      }
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    initEditInfo() {
      this.editInfo.id = this.sceneInfo.id
      this.editInfo.pageId = this.sceneInfo.pageId
      this.editInfo.name = this.sceneInfo.name
      this.editInfo.priority = this.sceneInfo.priority
      this.editInfo.remark = this.sceneInfo.remark
    },
    toEdit() {
      this.initEditInfo()
      this.isEdit = true
    },
    async offline() {
      let edit = {}
      edit.online = 0
      await this.$store.dispatch('setSceneInfo', edit)
      this.$store.dispatch('getSceneInfoById', this.sceneInfo.id)
    },
    async online() {
      let edit = {}
      edit.online = 1
      await this.$store.dispatch('setSceneInfo', edit)
      this.$store.dispatch('getSceneInfoById', this.sceneInfo.id)
    },
    deleteScene() {
      this.confirmInfo.show = true
    },
    async deleteAction() {
      await this.$store.dispatch('deleteScene', this.sceneInfo.id)
      let routeData = this.$router.resolve({
        path: '/client/page/' + this.sceneInfo.pageId + '/scene'
      })
      window.open(routeData.href, '_self')
    },
    addProduct() {
      let product = {
        sceneId: this.sceneInfo.id,
        productId: 0,
        miniVersion: ''
      }
      this.productsNew = product
    },
    async refresh() {
      await this.$store.dispatch('refreshScene', this.sceneInfo.id)
      this.$store.dispatch('getSceneInfoById', this.sceneInfo.id)
    }
  }
}
</script>
<style scoped>
.form-position {
  margin-left: 25px;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 12px;
}
.button-edit {
  padding: 0px 5px 0px;
  font-size: 14px;
  vertical-align: bottom;
}
.name {
  font-size: 18px;
  color: #464c5b;
  font-weight: bold;
  margin-left: 5px;
  margin-right: 10px;
}
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
}
.error-img {
  width: 200px;
}
.refresh {
  margin-left: 10px;
}
</style>