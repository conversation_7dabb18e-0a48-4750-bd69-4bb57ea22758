<template>
  <div>
    <Spin v-if="copying" fix></Spin>
    <div class="sider">
      <Card>
        <Anchor :affix="false" show-ink container="#home">
          <AnchorLink href="#action" title="交互配置" />
          <AnchorLink v-for="anchor in anchorList" :key="anchor.id" :href="'#' + anchor.id" :title="anchor.title" />
        </Anchor>
      </Card>
    </div>
    <div class="content">
      <Row :gutter="16">
        <Col span="4">
          <Menu ref="sideMenu" mode="vertical" :active-name="actionId" width="auto" @on-select="switchAction">
            <MenuItem v-for="action in actionList" :key="action.id" :name="action.id" style="font-size: 12px">
              <Row>
                <Col span="22">
                  {{ action.name }}
                </Col>
                <Col span="2">
                  <Button v-if="permission" size="small" type="text" icon="md-close-circle" style="color: #ed4014" @click="deleteAction(action.id)" />
                </Col>
              </Row>
            </MenuItem>
          </Menu>
          <Button v-if="permission" class="copy" type="dashed" long icon="md-add" @click="copyAction">复制当前交互配置</Button>
        </Col>
        <Col span="20">
          <div id="action" class="title">
            <span>交互配置</span>
            <Button icon="md-help-circle" type="text" style="display: inline-flex" to="https://km.sankuai.com/page/1335625987" target="_blank" />
            <Button v-if="permission" type="success" size="small" style="float: right" icon="md-add" @click="addCase">New Case</Button>
            <CaseEdit :edit="add" :actionId="actionId" @submit="submitCase" />
          </div>
          <div>{{ actionId }}{{ currentAction }}</div>
          <div v-for="(value, test) in caseInfo" :key="test">
            <div :id="test" class="title">
              <span>{{ getTestName(test) }}</span>
            </div>
            <div v-for="(product, caseId) in value" :key="caseId">
              <CaseCard :product="product" :testItem="test" :caseId="caseId"></CaseCard>
            </div>
          </div>
        </Col>
      </Row>
    </div>
    <Modal v-model="alert" width="360">
      <template #header>
        <p style="color: #f60; text-align: center">
          <Icon type="ios-information-circle"></Icon>
          <span>注意</span>
        </p>
      </template>
      <div style="text-align: center">
        <p>对应交互下的所有测试项配置会同步删除</p>
        <p>删除后无法恢复</p>
      </div>
      <template #footer>
        <Button type="error" size="large" long :loading="copying" @click="del">我已了解风险，确认删除</Button>
      </template>
    </Modal>
  </div>
</template>
<script>
import CaseCard from './Case/CaseCard'
import CaseEdit from './Case/CaseEdit'
import * as api from '../state/api'
export default {
  name: 'CaseHome',
  props: ['tab'],
  components: { CaseCard, CaseEdit },
  data() {
    return {
      actionId: 0,
      add: false,
      actionList: [],
      currentAction: {},
      copying: false,
      alert: false
    }
  },
  watch: {
    actionId(val) {
      this.getCurrentAction(val)
      this.$store.dispatch('getCaseList', val)
    }
  },
  async mounted() {
    if (this.$route.query.actionId) {
      this.actionId = parseInt(this.$route.query.actionId)
    }
    await this.getActionList()
    this.getCurrentAction(this.actionId)
    this.$store.commit('setCurrentTab', this.tab)
    this.$store.dispatch('getTestItemList')
    this.$store.dispatch('getCaseList', this.actionId)
  },
  updated() {
    this.$refs.sideMenu.updateActiveName()
  },
  computed: {
    caseInfo() {
      return this.$store.state.clientQuality.caseList
    },
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    anchorList() {
      let list = []
      for (let key in this.caseInfo) {
        list.push({
          id: key,
          title: this.getTestName(key)
        })
      }
      return list
    },
    permission() {
      let permission = this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    async getActionList() {
      let sceneId = this.$route.params.sceneId
      return new Promise((resolve) => {
        this.$axios({
          method: 'get',
          params: {
            sceneId: sceneId
          },
          url: this.env.url + 'scene/action/list'
        }).then((res) => {
          let message = res.data
          if (message.code === 0) {
            this.actionList = message.data
            if (this.actionList.length > 0 && this.actionId === 0) {
              this.actionId = this.actionList[0].id
            }
          }
          resolve()
        })
      })
    },
    getCurrentAction(val) {
      this.currentAction = this.actionList.find((item) => item.id === val)
    },
    getTestName(testItem) {
      for (let item of this.testItemList) {
        if (item.name === testItem) {
          return item.label
        }
      }
      return testItem
    },
    switchAction(name) {
      this.$router.push({ path: 'case', query: { actionId: name } })
      this.actionId = name
    },
    async copyAction() {
      this.copying = true
      let action = JSON.parse(JSON.stringify(this.currentAction))
      action.name = action.name + '-copy'
      await api.addAction(action).then((res) => {
        let message = res.data
        if (message.code === 1) {
          this.$message.error(message.message)
        }
      })
      await this.getActionList()
      this.copying = false
    },
    deleteAction(actionId) {
      this.alert = actionId
    },
    async del() {
      this.copying = true
      await api.deleteAction(this.alert).then((res) => {
        let message = res.data
        if (message.code === 1) {
          this.$message.error(message.message)
        }
      })
      await this.getActionList()
      console.log('deleteAction', this.alert)
      this.copying = false
      this.alert = false
    },
    addCase() {
      this.add = true
    },
    async submitCase() {
      this.add = false
      this.copying = true
      await this.$store.dispatch('getCaseList', this.actionId)
      this.copying = false
    }
  }
}
</script>
<style scoped>
.sider {
  width: 172px;
  position: fixed;
  right: 56px;
  z-index: 1;
}
.content {
  margin-right: 200px;
}
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
}
.copy {
  margin-top: 16px;
  font-size: 12px;
}
</style>