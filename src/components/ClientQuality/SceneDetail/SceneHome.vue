<template>
  <div class="layout">
    <BusinessHead :businessInfo="businessInfo" :pageInfo="pageInfo" :sceneInfo="sceneInfo"></BusinessHead>
    <Menu class="client-menu" mode="horizontal" theme="light" :active-name="tab">
      <MenuItem name="sceneInfo" :to="{ name: 'sceneInfo' }">
        <Icon type="ios-body" />
        基础信息
      </MenuItem>
      <MenuItem name="inspect" :to="{ name: 'inspect' }">
        <Icon type="md-build" />
        HyperJump Inspect
      </MenuItem>
      <MenuItem name="case" :to="{ name: 'case' }">
        <Icon type="md-build" />
        Case配置
      </MenuItem>
    </Menu>
    <router-view :sceneInfo="sceneInfo"></router-view>
  </div>
</template>
<script>
import BusinessHead from '../baseComponents/BusinessHead'
export default {
  name: 'SceneDetail',
  components: { BusinessHead },
  props: ['sceneId'],
  data() {
    return {}
  },
  mounted() {
    this.$store.dispatch('getSceneInfoById', this.sceneId)
  },
  computed: {
    businessInfo: function () {
      return this.$store.state.clientQuality.sceneInfo.businessInfo
    },
    pageInfo: function () {
      return this.$store.state.clientQuality.sceneInfo.pageInfo
    },
    sceneInfo: function () {
      return this.$store.state.clientQuality.sceneInfo.sceneInfo
    },
    tab() {
      return this.$store.state.clientQuality.currentTab
    }
  }
}
</script>
<style scoped>
.layout {
  background: #fff;
  text-align: left;
}
.client-menu {
  margin-bottom: 15px;
}
</style>