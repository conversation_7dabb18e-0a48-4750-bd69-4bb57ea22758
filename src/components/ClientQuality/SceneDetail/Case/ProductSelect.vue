<template>
  <Row :gutter="24" style="margin-bottom: 10px">
    <Col flex="12px" />
    <Col span="12">
      <Select v-model="selected" multiple filterable placeholder="请选择复用的应用产品">
        <Option v-for="option in allProduct" :value="option.id" :key="option.id">{{ option.label }}</Option>
      </Select>
    </Col>
    <Button type="primary" shape="circle" size="small" @click="select">Submit</Button>
    <Button class="cancel" type="default" shape="circle" size="small" @click="cancel">Cancel</Button>
  </Row>
</template>
<script>
export default {
  name: 'ProdcutSelect',
  props: ['actionId'],
  data() {
    return {
      selected: []
    }
  },
  mounted() {
    this.$store.dispatch('getProductList')
  },
  computed: {
    allProduct() {
      return this.$store.state.clientQuality.productList
    }
  },
  methods: {
    select() {
      this.$emit('select', this.selected)
    },
    cancel() {
      this.$emit('select', [])
    }
  }
}
</script>
<style scoped>
.cancel {
  margin-left: 10px;
}
</style>