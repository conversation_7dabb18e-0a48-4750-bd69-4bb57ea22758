<template>
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <Row class="title">
        <span>基础信息</span>
      </Row>
      <Row>
        <Input v-model="urlAuto" placeholder="输入一个URL，自动填入表单信息，已填写内容将会被覆盖" 
         search enter-button="点击生成" @on-search="autoFillUrl" style="margin-left: 10px" />
        <div class="error">
          <p>{{error}}</p>
        </div>
      </Row>
      <br>
      <FormItem label="Path" prop="path">
        <Input v-model="formValidate.path" placeholder="输入url模型path部分" ></Input>
      </FormItem>
      <FormItem label="适用产品" prop="productIds">
        <Select v-model="formValidate.productIds" multiple placeholder="请选择该模型适用的产品" @on-change="productChange">
          <Option v-for="pro in allProduct" :key="pro.id" :value="pro.id">{{pro.label}}</Option>
        </Select>
        <p v-for="pro in selectProduct" :key="pro.id">{{pro.scheme}}://{{pro.authority}}{{formValidate.path}}</p>
      </FormItem>
      <FormItem label="备注" prop="remark">
        <Input v-model="formValidate.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="填写该模型的备注信息"></Input>
      </FormItem>
      <Row class="title">
        <span>参数列表</span>
      </Row>
      <template v-for="(param,index) in formValidate.query">
        <Row :key="param.i " v-if="!param.delete">
          <Col span="5">
          <FormItem label="参数详情" :prop="'query.'+index+'.param'" :rules="{required:true}">
            <Input v-model="param.param" placeholder="参数Key"></Input>
          </FormItem>
          </Col>
          <Col span="5">
          <FormItem label=":" prop="pattern" :label-width="20">
            <Input v-model="param.pattern" placeholder="参数匹配规则"></Input>
          </FormItem>
          </Col>
          <Col span="4" style="margin-left: -50px; margin-right: 20px;">
          <FormItem :prop="'query.'+index+'.required'" :rules="{
              type: 'integer',
              required: true,
              message: 'Please select ',
              trigger: 'change'
            }">
            <RadioGroup v-model="param.required">
              <Radio :label=1>必填</Radio>
              <Radio :label="0">非必填</Radio>
            </RadioGroup>
          </FormItem>
          </Col>
          <Col span="4">
            <FormItem :prop="'query.'+index+'.type'">
              <div>
                <span>参数类型</span>
                <a href="https://km.sankuai.com/collabpage/2461221111" target="_blank">
                  <Icon type="md-help-circle" />
                </a>
              </div>
              <Select v-model="param.type" placeholder="选择参数类型" @on-change="handleTypeChange(param)">
                <Option label="枚举类" :value="1"></Option>
                <Option label="布尔类" :value="2"></Option>
                <Option label="JSON对象类" :value="3"></Option>
                <Option label="日期时间类" :value="4"></Option>
                <Option label="经纬度类" :value="5"></Option>
                <Option label="其它" :value="0"></Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="4" style="margin-left: -60px; margin-right: 50px;">
            <FormItem v-if="param.type === 1">
              <Tag v-for=" (valueItem, vIndex) in param.valueList" 
                  :key="valueItem.id" 
                  :name="valueItem.value" 
                  closable 
                  @on-close="deleteValue(param, vIndex)">
                  {{ valueItem.value }}
              </Tag>
              <div v-if="param.isEditValue" style="width: 150px">
                <Input
                  class="suffix"
                  v-model="param.newValue"
                  placeholder="请输入参数值"
                  size="small"
                >
                  <ButtonGroup size="small" slot="suffix" style="display: flex; align-items: center;">
                    <Button type="primary" @click="confirmAddValue(param, param.newValue)">确定</Button>
                    <Button @click="cancelEditValue(param)">取消</Button>
                  </ButtonGroup>
                </Input>
              </div>
              <Button
                v-else
                style="border-color: #2d8cf0;color: #2d8cf0;"
                icon="ios-add"
                type="dashed"
                size="small"
                @click="addValue(param)"
              >新增参数值</Button>
            </FormItem>
            <FormItem v-if="param.type === 4" >
              <Select v-model="param.value" placeholder="选择时间格式" style="margin-top: 33px;">
                <Option value="timestamp">时间戳 (Timestamp)</Option>
                <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
                <Option value="YYYY/MM/DD">YYYY/MM/DD</Option>
                <Option value="MM-DD-YYYY">MM-DD-YYYY</Option>
                <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                <Option value="DD-MM-YYYY">DD-MM-YYYY</Option>
                <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
                <Option value="YYYY-MM-DD HH:mm:ss">YYYY-MM-DD HH:mm:ss</Option>
                <Option value="YYYY/MM/DD HH:mm:ss">YYYY/MM/DD HH:mm:ss</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="2">
          <FormItem>
            <Button size="small" type="error" ghost @click="toDelete(param)">Delete</Button>
          </FormItem>
          </Col>
          <Col span="24" style="margin-top: -15px; margin-bottom: 10px;">
            <FormItem label="备注" prop="label">
              <Input v-model="param.label" placeholder="参数任意描述信息"></Input>
            </FormItem>
          </Col>
        </Row>
      </template>
      <FormItem>
        <Button type="dashed" @click="addParam">+ Add Param</Button>
      </FormItem>
      <FormItem>
        <Button type="primary" @click="handleSubmit('formValidate')">Submit</Button>
        <Button @click="toView">Cancel</Button>
      </FormItem>
    </Form>
  </template>
  <script>
  import * as api from '../../state/api'
  export default {
    props: ['url'],
    components: {},
    data() {
      return {
        returnURLInfo: {
          pageId: null,
          pathId: null,
          urlPath: '',
          query: []
        },
        urlAuto: '',
        selectProduct: [],
        formValidate: {
          path: '',
          productIds: [],
          remark: '',
          query: [
            {
              param: '',
              pattern: '444',
              required: 0,
              pathId: 0,
              label: '',
              type: null,
              markType: 0,
              valueList: [],
              value: '',
              newValue: '',
              delete: false,
              isEditValue: false
            }
          ]
        },
        ruleValidate: {
          path: [
            {
              required: true,
              message: 'path不能为空',
              trigger: 'blur'
            }
          ],
          productIds: [
            {
              type: 'array',
              required: true,
              min: 1,
              message: '至少选择一个适用的产品',
              trigger: 'change'
            }
          ]
        }
      }
    },
    mounted() {
      if (this.$store.state.clientQuality.productList.length <= 0) {
        this.$store.dispatch('getProductList')
      }
      this.initUrl()
    },
    computed: {
      allProduct() {
        return this.$store.state.clientQuality.productList
      },
      urlList() {
        return this.$store.state.clientQuality.urlList
      }
    },
    methods: {
      initUrl() {
        if (this.url !== undefined) {
          // 对url对象进行深拷贝
          this.formValidate = JSON.parse(JSON.stringify(this.url))
          // 初始化已经存在的参数，确保每个参数的 type，markType和valueList 字段有默认值
          this.formValidate.query.forEach(param => {
            if (typeof param.type === 'undefined') {
              this.$set(param, 'type', null)
            }
            if (typeof param.markType === 'undefined') {
              this.$set(param, 'markType', 0)
            }
            if (typeof param.value === 'undefined' || param.value === '' || param.value === null) {
              this.$set(param, 'value', null)
              param.valueList = []
            } else {
              param.valueList = param.value.split(',').map(value => ({ id: null, value: value.trim() }))
            }
          })
        } else {
          this.formValidate = {
            id: 0,
            pageId: 5336,
            path: '',
            productIds: [],
            remark: '',
            query: [
              {
                param: '',
                required: 0,
                pathId: 0,
                type: null,
                markType: 0,
                label: '',
                value: '',
                delete: false
              }
            ]
          }
        }
      },
      productChange(options) {
        this.selectProduct = []
        for (let pro of this.allProduct) {
          if (options.includes(pro.id)) {
            this.selectProduct.push(pro)
          }
        }
      },
      handleSubmit(name) {
        this.$refs[name].validate(async (valid) => {
          if (valid) {
            let queryList = []
            for (let query of this.formValidate.query) {
              if (!query.delete) {
                queryList.push(query)
              }
            }
            this.formValidate.query = queryList
            await api.setUrlInfo(this.formValidate).then((res) => {
              let message = res.data
              if (message.code && message.code === 1) {
                this.$Message.error('error')
              } else {
                this.$Message.success('success')
                this.$emit('cancel')
                this.returnURLInfo.pageId = message.pageId
                this.returnURLInfo.urlPathId = message.id
                this.returnURLInfo.urlPath = message.path
              }
            })
            this.returnURLInfo.selectProduct = this.selectProduct
            this.returnURLInfo.query = this.formValidate.query
            this.$emit('returnValue', this.returnURLInfo)
          } else {
            this.$Message.error('校验失败，请检查填写信息!')
          }
        })
      },
      toView() {
        this.$emit('cancel')
      },
      addParam() {
        this.formValidate.query.push({
          param: '',
          required: 0,
          pathId: this.formValidate.id,
          label: '',
          type: null,
          markType: 0,
          valueList: [],
          delete: false
        })
      },
      toDelete(param) {
        param['delete'] = true
        this.$forceUpdate()
      },
      renderLabelWithIcon(labelText) {
        return `<div style="display: flex; align-items: center;">
                  ${labelText}
                  <Tooltip content="参数分类说明" placement="top">
                    <Icon type="ios-help-circle-outline" style="margin-left: 8px; cursor: pointer;" @click="goToHelpPage"></Icon>
                  </Tooltip>
                </div>`
      },
      goToHelpPage() {
        window.open('www.baidu.com', '_blank')
      },
      async autoFillUrl() {
        let param = {
          targetUrl: this.urlAuto
        }
        await api.checkUrl(param).then((res) => {
          let message = res.data
          if (!message || !message.origin || JSON.stringify(message.origin) === '{}') {
            return
          }
          // 首先删除已有参数列表
          this.formValidate.query.forEach(param => {
            param.delete = true
          })
  
          // 1.获取路径部分
          this.formValidate.path = message.origin.path
  
          // 2.获取适用产品部分
          this.formValidate.productIds = message.origin.productIds
  
          // 3.获取参数部分
          message.origin.query.forEach(param => {
            if (param.param === 'mrn_biz' || param.param === 'mrn_entry' || param.param === 'mrn_component') {
              this.formValidate.query.push({
                param: param.param,
                pattern: param.value,
                required: 1,
                pathId: this.formValidate.id,
                label: '',
                delete: false
              })
            } else {
              this.formValidate.query.push({
                param: param.param,
                required: 0,
                pathId: this.formValidate.id,
                label: '',
                delete: false
              })
            }
          })
        })
      },
      addValue(param) {
        this.$set(param, 'isEditValue', true)
        this.$set(param, 'newValue', '')
      },
      updateValue(param) {
        param.value = param.valueList.map(valueItem => valueItem.value).join(',')
      },
      deleteValue(param, index) {
        param.valueList.splice(index, 1)
        this.updateValue(param)
        this.$forceUpdate()
      },
      confirmAddValue(param, value) {
         // 确保 param.valueList 存在且为数组
        if (!Array.isArray(param.valueList)) {
          param.valueList = []
        }
        if (value.trim() === '') {
          this.$Message.Notice('value 不可以为空')
        } else {
          param.valueList.push(
            {id: null, value: value}
          )
          param.isEditValue = false
          param.newValue = ''
          this.updateValue(param)
        }
      },
      cancelEditValue(param) {
        param.isEditValue = false
        param.newValue = ''
        this.$forceUpdate()
      },
      handleTypeChange(param) {
        if (param.markType === 1) {
          param.markType = 2
        } else {
          param.markType = 3
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .title {
    font-size: 14px;
    color: #464c5b;
    font-weight: bold;
    background-color: #f8f8f9;
    padding: 8px 16px;
    margin-bottom: 10px;
  }
  </style>
  