<template>
  <div class="card">
    <Icon type="ios-arrow-forward" />
    <strong style="font-size: 13px">配置详情</strong>
    <Button v-if="permission" :disabled="edit" type="text" icon="md-add-circle" style="display: inline-flex; color: #2d8cf0" @click="add"></Button>
    <Tag v-for="item in product" :key="item.id" color="volcano" :closable="permission" class="close" @on-close="deleteProduct(item.id)">
      {{ item.product.label }}
    </Tag>
    <ProductSelect v-if="edit" @select="addProduct"></ProductSelect>
    <Card class="detail">
      <ApiDiffInfo v-if="testItem=='urlDiff'" :apiDiffInfo="apiDiffInfoProp" :caseId="caseId" :product="product"></ApiDiffInfo>
    </Card>
  </div>
</template>
<script>
import * as api from '../../state/api'
import ProductSelect from './ProductSelect.vue'
import ApiDiffInfo from './ApiDiffInfo.vue'
export default {
  name: 'CaseCard',
  props: ['product', 'testItem', 'caseId'],
  components: { ProductSelect, ApiDiffInfo },
  data() {
    return {
      edit: false,
      apiDiffInfoProp: 1
    }
  },
  computed: {
    permission() {
      let permission = this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    },
    actionId() {
      return this.$route.query.actionId
    }
  },
  methods: {
    async deleteProduct(id) {
      await api.deleteSceneCase(id).then((res) => {
        if (res.data === 0) {
          this.$message.success('删除成功')
        } else {
          this.$message.error('删除失败')
        }
      })
      this.$store.dispatch('getCaseList', this.actionId)
    },
    add() {
      this.edit = true
    },
    async addProduct(proList) {
      this.edit = false
      if (proList.length <= 0) {
        return
      }
      for (let proId of proList) {
        let params = {
          actionId: this.actionId,
          productId: proId,
          caseId: this.caseId,
          testItem: this.testItem
        }
        await this.$store.dispatch('setSceneCase', params)
      }
      this.$store.dispatch('getCaseList', this.actionId)
    }
  }
}
</script>
<style scoped>
.card {
  margin-top: 12px;
}
.detail {
  margin-top: 6px;
  margin-bottom: 6px;
  height: 10%;
}
.close >>> .ivu-icon-ios-close {
  color: #fa541c !important;
}
</style>