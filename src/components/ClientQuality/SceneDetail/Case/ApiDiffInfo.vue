<template>
  <div>
    <div>
      <Form :model="caseBaseInfo" :disabled="isDisable" label-position="left" :label-width="120">
        <FormItem label="用例名称">
          <Input v-model="caseBaseInfo.name"></Input>
        </FormItem>

        <FormItem label="跳转页面">
          <Select v-model="selectPageId" filterable placeholder="" @on-select="selectUrlPage" class="page-select-container">
            <Option v-for="item in pageList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
          <Button v-if="!isDisable" @click="showModal" class="button-container">新增页面 URL</Button>
          <Poptip 
            trigger="click" 
            placement="bottom"
            word-wrap
            width="200">
            <Icon v-if="!isDisable" type="md-help-circle"/>
            <div slot="content">
              <p>新增一个不在 MMCD 中维护的URL模型，仅用于 URL DIFF 测试</p>
            </div>
          </Poptip>

          <Modal v-model="isShowModal" title="新增页面URL模型" width="80%">
            <NewURLInfo @returnValue="handleReturnValue"/>
          </Modal>
        </FormItem>

        <FormItem label="页面跳链 path">
          <Select v-model="urlPathId" filterable clearable placeholder="" @on-select="setUrlPathId">
            <Option v-for="item in urlList" :value="item.id" :key="item.id">{{ '/' + item.path }}</Option>
          </Select>
          <Collapse accordion style="margin-top: 10px">
            <Panel name="1">
              查看完整跳链
              <template #content>
                <div v-for="url in newPageUrlList">
                  <Badge color="green" :text="url"/>
                </div>
              </template>
            </Panel>
          </Collapse>
        </FormItem>

        <FormItem label="整体校验类型">
          <Select v-model="caseBaseInfo.checkType" filterable clearable>
            <Option v-for="item in checkTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>

        <FormItem label="URL 参数配置">
          <div v-for="(item, index) in caseBaseInfo.dynamicItems" :key="index">
            <Select v-model="item.value" filterable clearable placeholder="请选择参数" class="select-container">
              <Option
                v-for="param in urlPathInfo.query"
                :value="param.param"
                :key="param.id"
              >{{param.param}}</Option>
            </Select>
            <Select v-model="item.checkType" filterable clearable placeholder="请选择参数校验类型" class="input-container">
              <Option v-for="item in paramCheckTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <Icon v-if="!isDisable" type="md-remove-circle" @click="removeItem(index)" slot="append"/>
          </div>
          <Button v-if="!isDisable" type="dashed" long @click="addItem" icon="md-add" style="color: #3399ff">Add param config</Button>
        </FormItem>
      </Form>
    </div>

    <div>
      <Button v-if="isDisable" type="primary" @click="editCaseInfo">编辑</Button>
      <Button v-if="!isDisable" type="primary" @click="saveSelectedUrl">保存</Button>
      <Button v-if="!isDisable" type="default" @click="cacelEdit">取消</Button>
    </div>
  </div>
</template>

<script>
import NewURLInfo from './NewURLInfo.vue'
import * as api from '../../state/api'
export default {
  name: 'ApiDiffInfo',
  props: ['apiDiffInfo', 'caseId', 'product'],
  components: { NewURLInfo },
  data() {
    return {
      edit: false,
      caseBaseInfo: {
        name: '',
        path: '',
        checkType: null,
        dynamicItems: [
        ]
      },
      pageList: [],
      selectPage: '',
      selectPageId: null,
      configProductIds: [],
      urlList: [],
      urlPathId: null,
      urlPathInfo: {},
      selectUrlId: null,
      isAddItem: false,
      index: null,
      isDisable: true,
      urlPathKey: 0,
      isShowModal: false,
      newPageUrlList: [],
      iframeSrc: '/client/page/5336/url',
      checkTypeList: [
        {
          value: 0,
          label: '全部忽略校验'
        },
        {
          value: 1,
          label: '默认仅 value 校验'
        },
        {
          value: 2,
          label: '仅 key 校验'
        },
        {
          value: 3,
          label: 'value 类型校验'
        }
      ],
      paramCheckTypeList: [
        {
          value: 0,
          label: '忽略校验'
        },
        {
          value: 1,
          label: '默认仅 value 校验'
        },
        {
          value: 2,
          label: '仅 key 校验'
        },
        {
          value: 3,
          label: 'value 类型校验'
        }
      ]
    }
  },
  mounted() {
    this.getAllPageList()
    this.getUrlDiffCaseInfo()
    this.getProductIds()
  },
  computed: {
  },
  watch: {
    urlList: {
      handler(newVal) {
        this.urlPathKey += 1
      },
      deep: true
    }
  },
  methods: {
    async saveSelectedUrl() {
      // 利用this.urlPathInfo构造一个字典，直接查询 param 和 id的对应关系
      let paramIdDict = {}
      for (let i = 0; i < this.urlPathInfo.query.length; i++) {
        paramIdDict[this.urlPathInfo.query[i].param] = this.urlPathInfo.query[i].id
      }
      let urlQueryInfo = []
      // 这里有个问题，如果dynamicItems为空的话,就没有 Length 了
      for (let i = 0; i < this.caseBaseInfo.dynamicItems.length; i++) {
        let queryItem = {
          paramId: paramIdDict[this.caseBaseInfo.dynamicItems[i].value],
          checkType: parseInt(this.caseBaseInfo.dynamicItems[i].checkType)
        }
        urlQueryInfo.push(queryItem)
      }
      let caseInfo = {
        caseId: parseInt(this.caseId),
        caseName: this.caseBaseInfo.name,
        urlPathId: parseInt(this.urlPathId),
        checkType: parseInt(this.caseBaseInfo.checkType),
        urlQueryInfo: urlQueryInfo
      }
      await this.$store.dispatch('updateUrlDiffCaseInfo', caseInfo)
    },
    addItem() {
      if (!this.urlPathInfo.query || this.caseBaseInfo.dynamicItems.length >= this.urlPathInfo.query.length) {
        this.$Message.warning({
          'content': '参数配置已达上限，如需新增，请先在 URL 模型中添加参数！'
        })
        return
      }
      this.isAddItem = true
      this.index += 1
      this.caseBaseInfo.dynamicItems.push({
        index: this.index,
        value: '',
        checkType: null,
        id: null
      })
    },
    removeItem(index) {
      this.caseBaseInfo.dynamicItems.splice(index, 1)
    },
    async getUrlDiffCaseInfo() {
      await api.getUrlDiffCaseInfo(parseInt(this.caseId)).then((res) => {
        this.urlDiffCaseInfo = res.data.data
        this.caseBaseInfo.name = this.urlDiffCaseInfo.caseName
        this.selectUrl = this.urlDiffCaseInfo.urlInfo.path
        this.urlPathId = this.urlDiffCaseInfo.urlInfo.pathId
        this.caseBaseInfo.checkType = this.urlDiffCaseInfo.checkType
      })
      // 根据 pathId 反查 page
      await api.getUrlPathInfo(this.urlPathId).then(res => {
        this.urlPathInfo = res.data
        this.selectPageId = this.urlPathInfo.pageId
        const selectedPage = this.pageList.find(item => item.id === this.urlPathInfo.pageId)
        this.selectPage = selectedPage.name
      })
      // 获取 url path
      await api.getUrlList(this.selectPageId).then((res) => {
        this.urlList = res.data
      })

      let caseBaseInfo = this.caseBaseInfo
      this.caseBaseInfo.dynamicItems = []
      for (let i = 0; i < this.urlDiffCaseInfo.urlQueryInfo.length; i++) {
        let paramItem = {
          index: i,
          value: this.urlDiffCaseInfo.urlQueryInfo[i].param,
          checkType: this.urlDiffCaseInfo.urlQueryInfo[i].checkType,
          paramId: this.urlDiffCaseInfo.urlQueryInfo[i].paramId
        }
        caseBaseInfo.dynamicItems.push(paramItem)
      }
      this.$set(this, 'caseBaseInfo', caseBaseInfo)
      this.$set(this, 'index', this.caseBaseInfo.dynamicItems.length)
    },
    getProductIds() {
      let productIds = []
      for (let productItem of this.product) {
        productIds.push(productItem.productId)
      }
      this.configProductIds = productIds
    },
    async getAllPageList() {
      let businessId = 7
      let query = {
        online: 1,
        pageSize: 500,
        pageCount: 1,
        isDetail: 0,
        businessId: businessId
      }
      await api.getPageList(query).then((res) => {
        this.pageList = res.data.pageList
      })
    },
    async selectUrlPage(pageId) {
      this.selectPageId = pageId.value
      await api.getUrlList(pageId.value).then((res) => {
        this.urlList = []
        for (let url of res.data) {
          const setIds = new Set(url.productIds)
          if (this.configProductIds.some(id => setIds.has(id))) {
            this.urlList.push(url)
          }
        }
      })
      this.urlPathId = null
      this.caseBaseInfo.checkType = null
      this.caseBaseInfo.dynamicItems = []
      this.urlPathInfo = {}
    },
    async setUrlPathId(seltctedUrl) {
      this.urlPathId = seltctedUrl.value
      await api.getUrlPathInfo(this.urlPathId).then(res => {
        this.urlPathInfo = res.data
      })
      this.caseBaseInfo.dynamicItems = []
    },
    async setCustomizedUrlPathId(seltctedUrlid) {
      this.urlPathId = seltctedUrlid
      await api.getUrlPathInfo(this.urlPathId).then(res => {
        this.urlPathInfo = res.data
      })
      this.caseBaseInfo.dynamicItems = []
    },
    editCaseInfo() {
      this.isDisable = false
    },
    cacelEdit() {
      this.isDisable = true
      this.getUrlDiffCaseInfo()
    },
    showModal() {
      this.isShowModal = true
    },
    handleReturnValue(receivedValue) {
      // 处理从弹窗返回的值
      this.selectPageId = receivedValue.pageId
      this.caseBaseInfo.checkType = null
      // 重新获取 urlList
      this.urlList = [
        {
          id: receivedValue.urlPathId,
          path: receivedValue.urlPath
        }
      ]
      // 设定 pathId
      this.setCustomizedUrlPathId(receivedValue.urlPathId)
      let selectProduct = receivedValue.selectProduct
      let queryString = receivedValue.query
        .map(param => {
          const value = (param.value !== undefined) ? param.value : ''
          return encodeURIComponent(param.param) + '=' + encodeURIComponent(value)
        })
        .join('&')
      for (let productItem of selectProduct) {
        let scheme = productItem.scheme + '://' + productItem.authority + receivedValue.urlPath + '?' + queryString
        this.newPageUrlList.push(scheme)
      }
    }
  }
}
</script>

<style>
.custom-form .ivu-form-item {
  margin-bottom: 5px; /* 调整纵向间距 */
}
.page-select-container {
  width: 70%;
  float: left;
  box-sizing: border-box;
  padding-right: 10px;
}
.button-container {
  width: 27%;
  float: left;
  box-sizing: border-box;
  margin-right: 1%;
  color: #3399ff;
}
.button-tips {
  float: left;
  box-sizing: border-box;
}
.select-container {
  width: 48.5%;
  float: left;
  box-sizing: border-box;
  padding-right: 10px; /* Select 框间隔 */
}
.input-container {
  width: 48.5%;
  float: left;
  box-sizing: border-box;
  margin-right: 1%;
}
.ivu-input[disabled], .ivu-btn[disabled] {
  background-color: transparent;
  color: #515a6e;
}
.ivu-select-input[disabled], .ivu-select[disabled], .ivu-select-input-disabled {
  color: #515a6e !important;
  -webkit-text-fill-color: #515a6e;
  border-bottom: 1px solid #dcdee2 !important; /* 保持与非禁用状态相同的边框颜色 */
  background-color: #fff !important; /* 保持背景色一致 */
  cursor: not-allowed; /* 保持禁用状态的手势 */
}
.ivu-collapse>.ivu-collapse-item>.ivu-collapse-header {
  height: 26px;
  line-height: 26px;
}
</style>