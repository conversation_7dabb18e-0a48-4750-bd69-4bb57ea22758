<template>
  <Drawer title="新增测试项" v-model="edit" width="720" :mask-closable="false" :closable="false">
    <Spin v-if="loading" fix></Spin>
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <Alert>
        <p>- 添加后，按照默认测试项配置生成Case，如需修改请添加后编辑</p>
        <p>- 如选择多个应用，则表示在此Case在选中应用间复用</p>
      </Alert>
      <FormItem label="测试项" prop="testItem">
        <Select v-model="formValidate.testItem" placeholder="选择一个测试类型">
          <Option v-for="item in testItemList" :value="item.name" :key="item.name" :disabled="!availableTestItem.includes(item.name)">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="应用" prop="products">
        <Select v-model="formValidate.products" multiple filterable placeholder="请选择应用产品">
          <Option v-for="option in allProduct" :value="option.id" :key="option.id">{{ option.label }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button type="primary" @click="handleSubmit('formValidate')">Submit</Button>
        <Button @click="cancel">Cancel</Button>
      </FormItem>
    </Form>
  </Drawer>
</template>
<script>
import * as api from '../../state/api'
export default {
  name: 'CaseEdit',
  props: ['edit', 'actionId'],
  data() {
    return {
      loading: false,
      availableTestItem: ['urlDiff'],
      formValidate: {
        actionId: this.actionId,
        testItem: '',
        products: []
      },
      ruleValidate: {
        testItem: [{ required: true, message: '测试类型不能为空', trigger: 'change' }],
        products: [{ required: true, type: 'array', min: 1, message: '至少选择一个应用', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.$store.dispatch('getProductList')
  },
  computed: {
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    allProduct() {
      return this.$store.state.clientQuality.productList
    }
  },
  methods: {
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.loading = true
          this.formValidate.actionId = this.actionId
          api.addCase(this.formValidate).then((res) => {
            let message = res.data
            if (message.code === 0) {
              this.$message.success('新增成功')
              this.$emit('submit', this.formValidate)
            } else {
              this.$message.error(message.data)
            }
            this.loading = false
          })
        }
      })
    },
    cancel() {
      this.$emit('submit', {})
    }
  }
}
</script>
<style scoped>
</style>