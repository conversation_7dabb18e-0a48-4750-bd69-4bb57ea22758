<template>
  <ClientPageCrop :pageInfo=pageInfo></ClientPageCrop>
</template>

<script>
/* eslint-disable */
import ClientPageCrop from "../ClientPage/ClientPageCrop";
export default {
  name: 'HJInspect',
  props: ['tab'],
  components: {ClientPageCrop},
  data() {
    return {
      pageInfo: []
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    let sceneID = this.$route.params.sceneId;
    if(sceneID){
      this.getPageInfo(sceneID)
    }
  },
  methods:{
    getPageInfo(sceneId){
      this.$axios({
        method:"get",
        params:{
          "sceneId": sceneId
        },
        url:this.env.url+"clientPage/getPagesBySceneId"
      }).then((res) => {
        let message = res.data;
        this.pageInfo=message[0];
      }).catch(function (error) {
        console.log(error)
      })
    }
  }
}
</script>
