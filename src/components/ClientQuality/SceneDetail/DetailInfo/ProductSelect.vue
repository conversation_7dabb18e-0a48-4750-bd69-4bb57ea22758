<template>
  <div>
    <Row class="pro-info" v-if="!isEdit">
      <Col span="4">
      <Badge status="success" />
      <strong>{{product.product.label}}</strong>
      </Col>
      <Col span="2">
      <Tag v-if="product.superId===0" color="purple">custom</Tag>
      <Tag v-else color="default">super</Tag>
      </Col>
      <Col span="18">
      <Row>
        <Col span="8"><span class="sub-title">产物:</span>{{product.implement}}</Col>
        <Col span="8"> <span class="sub-title">最低生效版本:</span>{{product.miniVersion}}</Col>
        <Col span="4">
        <ButtonGroup v-if="permission">
          <Button icon="md-create" size="small" type="primary" ghost @click="edit()"></Button>
          <Button icon="ios-trash" size="small" type="primary" ghost @click="deleteSceneProduct()"></Button>
        </ButtonGroup>
        </Col>
        <Col span="24">
        <span class="sub-title">影响版本:</span>
        <Tag v-for="mock in product.mockTrees" :key="mock.id" color="orange">
          {{mock.category}}
        </Tag>
        </Col>
        <Col span="24">
        <span class="sub-title">支持测试项:</span>
        <Tag v-for="item in product.testItems" :key="item.name" color="primary" type="border">
          {{item.label}}
        </Tag>
        </Col>
        <Col span="24" v-if="product.advance!==undefined">
        <Tooltip placement="right-end" theme="light" transfer:true max-width="400">
          <span class="card-content" style="color:#2d8cf0;font-weight:500">高级配置</span>
          <Icon style="color:#2d8cf0;" type="ios-arrow-forward"></Icon>
          <div slot="content">
            <JsonViewer :value="product.advance" :expand-depth=5 copyable></JsonViewer>
          </div>
        </Tooltip>
        </Col>
      </Row>
      </Col>
    </Row>
    <Form v-else :label-width="100" :model="editInfo" :rules="ruleValidate" ref="refEditInfo" label-position="right" inline style="margin-top: 30px;">
      <FormItem label="选择产品" required>
        <Select v-model="editInfo.productId" filterable transfer :multiple="multiple" placeholder="Select" @on-change="change" :max-tag-count=2>
          <Option v-for="pro in allProduct" :value="pro.id" :key="pro.id">{{ pro.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="产物" required>
        <Select v-model="editInfo.implement" filterable transfer placeholder="Select">
          <Option v-for="(impl,index) in implList" :value="impl" :key="index">{{ impl }}</Option>
        </Select>
      </FormItem>
      <FormItem label="最低生效版本" prop="miniVersion" :label-width="100">
        <Input v-model="editInfo.miniVersion" placeholder="版本格式：x. y. z"></Input>
      </FormItem>
      <FormItem label="支持的测试项" :label-width="100">
        <CheckboxGroup v-model="testSupport">
          <Checkbox class="test-check-box" v-for="item in testItemList" :key="item.name" :label="item.name">
            {{item.label}}
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="高级配置" prop="advance" :label-width="100">
        <vueJsonEditor class="json" v-model="editInfo.advance" :mode="'code'" lang="zh" @has-error="jsonError" @json-change="jsonSave" />
      </FormItem>
      <FormItem :label-width="10" class="submit">
        <Button type="primary" size="small" @click="submit">Submit</Button>
        <Button size="small" style="margin-left: 8px" @click="toView">Cancel</Button>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import { Bus } from '@/global/bus'
import vueJsonEditor from 'vue-json-editor'
import JsonViewer from 'vue-json-viewer'
export default {
  name: 'ProductSelect',
  props: ['product'],
  components: { vueJsonEditor, JsonViewer },
  data() {
    return {
      isEdit: false,
      jsonErr: '',
      testSupport: [],
      editInfo: {},
      implList: [],
      multiple: false,
      ruleValidate: {
        miniVersion: [
          {
            required: false,
            type: 'string',
            trigger: 'change',
            validator: (rule, value, callback) => {
              var pattern = /^([0-9]+)(\.)([0-9]+)(\.)([0-9]+)$/
              if (
                this.editInfo.miniVersion !== undefined &&
                this.editInfo.miniVersion.trim() !== '' &&
                !pattern.test(this.editInfo.miniVersion.trim())
              ) {
                return callback(new Error('非法版本'))
              } else {
                callback()
              }
            }
          }
        ],
        advance: [
          {
            type: 'string',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.jsonErr !== '') {
                return callback(new Error(this.jsonErr))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.initPoruct()
  },
  computed: {
    allProduct() {
      return this.$store.state.clientQuality.productList
    },
    testItemList() {
      return this.$store.state.clientQuality.testItemList
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    jsonError(value) {
      this.jsonErr = value
    },
    jsonSave(value) {
      this.editInfo.advance = value
      this.jsonErr = ''
    },
    initPoruct() {
      if (!this.product.id) {
        // 新增场景
        this.isEdit = true
        this.multiple = true
        this.editInfo.productId = []
        if (this.allProduct.length === 0) {
          this.$store.dispatch('getProductList')
        }
        if (this.testItemList.length === 0) {
          this.$store.dispatch('getTestItemList')
        }
      } else {
        this.implList = this.product.product.implList
        this.editInfo.id = this.product.id
        this.editInfo.productId = this.product.productId
      }
      this.editInfo.sceneId = this.product.sceneId
      this.editInfo.implement = this.product.implement
      this.editInfo.miniVersion = this.product.miniVersion
      this.testSupport = this.product.testSupport
      this.editInfo.advance = this.product.advance
    },
    findImpl(productIdList) {
      let selectProductImpl = []
      for (let proId of productIdList) {
        for (let pro of this.allProduct) {
          if (pro.id === proId) {
            selectProductImpl.push(pro.implList)
          }
        }
      }
      if (selectProductImpl.length < 1) {
        return []
      } else if (selectProductImpl.length === 1) {
        return selectProductImpl[0]
      } else {
        let impl = []
        for (let i = 0; i < selectProductImpl.length - 1; i++) {
          impl = selectProductImpl[i].filter((v) =>
            selectProductImpl[i + 1].includes(v)
          )
        }
        return impl
      }
    },
    edit() {
      if (this.allProduct.length === 0) {
        this.$store.dispatch('getProductList')
      }
      if (this.testItemList.length === 0) {
        this.$store.dispatch('getTestItemList')
      }
      this.isEdit = true
      this.initPoruct()
    },
    async deleteSceneProduct() {
      await this.$store.dispatch('deleteSceneProduct', this.product.id)
      this.$store.dispatch('getSceneInfoById', this.$route.params.sceneId)
    },
    change(productId) {
      if (this.multiple) {
        this.implList = this.findImpl(productId)
      } else {
        for (let pro in this.allProduct) {
          if (pro.id === productId) {
            this.implList = pro.implList
          }
        }
      }
    },
    toView() {
      if (this.product.id) {
        this.isEdit = false
      } else {
        Bus.$emit('cancelAdd')
      }
    },
    submit() {
      this.$refs['refEditInfo'].validate(async (valid) => {
        if (valid) {
          if (this.editInfo.productId === 0 || this.editInfo.implement === undefined) {
            this.$Message.error('请完善必填信息')
            this.isEdit = true
          } else {
            this.editInfo.testSupport = this.testSupport
            if (this.multiple) {
              let productIdList = this.editInfo.productId
              for (let id of productIdList) {
                this.editInfo.productId = id
                await this.$store.dispatch('setSceneProduct', this.editInfo)
              }
              this.isEdit = false
            } else {
              await this.$store.dispatch('setSceneProduct', this.editInfo)
            }
            this.$store.dispatch('getSceneInfoById', this.$route.params.sceneId)
            Bus.$emit('cancelAdd')
            this.isEdit = false
          }
        }
      })
    }
  }
}
</script>
<style scoped>
.pro-info {
  margin-top: 30px;
  margin-bottom: 5px;
}
.sub-title {
  font-weight: 500;
  margin-right: 20px;
  font-size: 12px;
}
.test-check-box {
  margin-right: 30px;
}
.submit {
  margin-right: 100px;
  float: right;
}
.json {
  width: 500px;
}
</style>