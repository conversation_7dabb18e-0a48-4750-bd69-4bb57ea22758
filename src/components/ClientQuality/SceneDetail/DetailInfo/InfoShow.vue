<template>
  <Form :model="sceneInfo" :label-width="80" label-position="left">
    <FormItem label="场景ID">
      <strong>{{sceneInfo.id}}</strong>
    </FormItem>
    <FormItem label="场景名">
      <span>{{sceneInfo.name}}</span>
    </FormItem>
    <FormItem label="所属页面">
      <span>{{pageName}}</span>
    </FormItem>
    <FormItem label="在线状态">
      <Badge v-if="sceneInfo&&sceneInfo.online===1" text="ON" type="success"></Badge>
      <Badge v-else text="OFF"></Badge>
    </FormItem>
    <FormItem label="优先级">
      <Tag v-if="sceneInfo.priority === 1" color="volcano">P{{ sceneInfo.priority }}</Tag>
      <Tag v-else color="blue">P{{ sceneInfo.priority }}</Tag>
    </FormItem>
    <FormItem label="标签">
      <Tag v-for="item in sceneInfo.tag" :key="item.index" :color="getTagColor(item)">{{item}}</Tag>
    </FormItem>
    <FormItem label="备注">
      <p>{{sceneInfo.remark}}</p>
    </FormItem>
  </Form>
</template>
<script>
export default {
  name: 'Info',
  props: ['sceneInfo'],
  data() {
    return {}
  },
  computed: {
    pageName() {
      return this.$store.state.clientQuality.pageInfo.name
    }
  },
  methods: {
    getTagColor(tag) {
      if (tag === 'M') {
        return 'cyan'
      } else if (tag === 'T') {
        return 'orange'
      } else if (tag === 'V') {
        return 'magenta'
      } else if (tag === 'R') {
        return 'purple'
      } else {
        return 'green'
      }
    }
  }
}
</script>