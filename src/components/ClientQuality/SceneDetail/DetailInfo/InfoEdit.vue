<template>
  <Form :model="editInfo" :rules="ruleValidate" ref="refEditInfo" :label-width="80" label-position="left" style="width:600px;margin-top:20px">
    <FormItem label="场景ID">
      <strong>{{editInfo.id}}</strong>
    </FormItem>
    <FormItem label="场景名" prop="name">
      <Input v-model="editInfo.name" />
    </FormItem>
    <FormItem label="所属页面" required>
      <Select v-model="editInfo.pageId" filterable transfer>
        <Option v-for="item in parentList" :value="item.id" :key="item.id">{{item.name}}</Option>
      </Select>
    </FormItem>
    <FormItem label="优先级" required>
      <Select v-model="editInfo.priority">
        <Option :value="1" :key="1">P1</Option>
        <Option :value="2" :key="2">P2</Option>
        <Option :value="3" :key="3">P3</Option>
        <Option :value="4" :key="4">P4</Option>
      </Select>
    </FormItem>
    <FormItem label="备注">
      <Input v-model="editInfo.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}">
      </Input>
    </FormItem>
    <FormItem>
      <Button type="primary" size="small" @click="submit" :disabled="isSubmit">Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="toView">Cancel</Button>
    </FormItem>
  </Form>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'InfoEdit',
  props: ['editInfo'],
  data() {
    return {
      isSubmit: false,
      ruleValidate: {
        name: [
          {
            required: true,
            type: 'string',
            message: '场景名不可以为空',
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (this.editInfo.name.trim() === '') {
                return callback(new Error('场景名不可以为空'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  mounted() {
    let query = {
      isOnline: 1,
      pageSize: 500,
      isDetail: 0,
      businessId: this.$store.state.clientQuality.pageInfo.businessId
    }
    this.$store.commit('setPageFilter', query)
    this.$store.dispatch('getPageListById')
  },
  computed: {
    parentList() {
      return this.$store.state.clientQuality.pageList.pageList
    }
  },
  methods: {
    submit() {
      this.$refs['refEditInfo'].validate(async (valid) => {
        if (valid) {
          this.isSubmit = true
          await this.$store.dispatch('setSceneInfo', this.editInfo)
          this.isSubmit = false
          Bus.$emit('submitEdit')
        }
      })
    },
    toView() {
      Bus.$emit('cancelEdit')
    }
  }
}
</script>