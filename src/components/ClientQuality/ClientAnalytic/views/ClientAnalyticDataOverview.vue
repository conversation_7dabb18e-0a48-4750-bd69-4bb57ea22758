<template>
  <div>
    <Row type="flex" justify="space-around" class="code-row-bg">
      <div style="width:100%">
        <i-col span="24">
          <div style="padding: 20px;text-align:left">
            <Card :bordered="false" class="card">
              <p slot="title">资源分布</p>
              <Row type="flex" justify="center">
                <i-col span="8">
                  <div id="demandDistributionPieChart"></div>
                </i-col>
                <i-col span="8">
                  <div id="QAInvolvedPieChart"></div>
                </i-col>
              </Row>
              <Table border :columns="resourceDistribution.columns" :data="resourceDistribution.data" size="small"></Table>
            </Card>
            <Card :bordered="false" class="card">
              <p slot="title">流程落实 </p>
              <Row type="flex" justify="center">
                <i-col span="8">
                  <div id="includeRDPercentageColumnChart"></div>
                </i-col>
                <i-col span="8">
                  <div id="isRDFirstPercentageColumnChart"></div>
                </i-col>
              </Row>
              <Table border :columns="processImplementation.columns" :data="processImplementation.data" size="small"></Table>
            </Card>
          </div>
        </i-col>
      </div>
    </Row>
  </div>
</template>

<script>
import Highcharts from 'highcharts/highstock'

export default {
  name: 'ClientAnalyticOverview',
  mounted () {
    this.moreChart()
  },
  watch: {
    overview: function (newValue, oldValue) {
      this.moreChart()
    }
  },
  computed: {
    overview () {
      return this.$store.state.ClientAnalytic.overview
    },
    resourceDistribution () {
      return this.overview.table.resourceDistribution
    },
    processImplementation () {
      return this.overview.table.processImplementation
    }
  },
  methods: {
    moreChart () {
      // 初始化 Highcharts 图表
      this.demandDistributionPieChart = new Highcharts.Chart('demandDistributionPieChart', {
        chart: {
          height: 200,
          width: 400
        },
        credits: {
          enabled: false
        },
        title: {
          text: this.overview.chart.demandDistributionPieChart.title
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: true,
              format: '<b>{point.name}</b>: {point.percentage:.1f} %',
              style: {
                color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
              }
            }
          }
        },
        series: [{
          type: 'pie',
          name: this.overview.chart.demandDistributionPieChart.title,
          data: this.overview.chart.demandDistributionPieChart.data
        }]
      })
      this.QAInvolvedPieChart = new Highcharts.Chart('QAInvolvedPieChart', {
        chart: {
          height: 200,
          width: 400
        },
        credits: {
          enabled: false
        },
        colors: [
          '#ffccff',
          '#66cc99'
        ],
        title: {
          text: this.overview.chart.QAInvolvedPieChart.title
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: true,
              format: '<b>{point.name}</b>: {point.percentage:.1f} %',
              style: {
                color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
              }
            }
          }
        },
        series: [{
          type: 'pie',
          name: this.overview.chart.QAInvolvedPieChart.title,
          data: this.overview.chart.QAInvolvedPieChart.data
        }]
      })
      this.includeRDPercentageColumnChart = new Highcharts.Chart('includeRDPercentageColumnChart', {
        chart: {
          type: 'column'
        },
        credits: {
          enabled: false
        },
        title: {
          text: this.overview.chart.includeRDPercentageColumnChart.title
        },
        xAxis: {
          categories: this.overview.chart.includeRDPercentageColumnChart.column.x,
          crosshair: true
        },
        yAxis: {
          min: 0,
          max: 100,
          title: {
            text: '百分比'
          }
        },
        tooltip: {
          // head + 每个 point + footer 拼接成完整的 table
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
          '<td style="padding:0"><b>{point.y:.1f} %</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true
        },
        plotOptions: {
          column: {
            borderWidth: 0
          }
        },
        series: [
          {
            name: '涉及后台占比',
            data: this.overview.chart.includeRDPercentageColumnChart.data
          }
        ]
      })
      this.isRDFirstPercentageColumnChart = new Highcharts.Chart('isRDFirstPercentageColumnChart', {
        chart: {
          type: 'column'
        },
        credits: {
          enabled: false
        },
        title: {
          text: '后台先行需求占比'
        },
        xAxis: {
          categories: this.overview.chart.isRDFirstPercentageColumnChart.column.x,
          crosshair: true
        },
        yAxis: {
          min: 0,
          max: 100,
          title: {
            text: '百分比'
          },
          plotLines: [{
            color: 'red',
            dashStyle: 'longdashdot',
            value: 80,
            width: 2
          }]
        },
        tooltip: {
          // head + 每个 point + footer 拼接成完整的 table
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
          '<td style="padding:0"><b>{point.y:.1f} %</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true
        },
        plotOptions: {
          column: {
            borderWidth: 0
          }
        },
        series: [
          {
            name: '后台先行需求占比',
            data: this.overview.chart.isRDFirstPercentageColumnChart.data
          }
        ]
      })
    }
  }
}
</script>

<!-- Add 'scoped' attribute to limit CSS to this component only -->
<style scoped>
.card {
  margin-bottom: 40px;
}
.ivu-col-span-20 {
  width:100%
}
</style>

