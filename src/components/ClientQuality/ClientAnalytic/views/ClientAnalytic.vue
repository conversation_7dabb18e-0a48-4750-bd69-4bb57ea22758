<template>
  <div class="analytic-spin">
    <ClientAnalyticSpin/>
    <Layout class="layouts">
      <div class="headers">
        <ClientAnalyticSelector/>
      </div>
      <Content>
        <ClientAnalyticMenu/>
      </Content>
    </Layout>
  </div>
</template>

<script>
import ClientAnalyticSelector from './ClientAnalyticSelector'
import ClientAnalyticMenu from './ClientAnalyticMenu'
import ClientAnalyticSpin from './ClientAnalyticSpin.vue'

export default {
  name: 'ClientAnalyticBase',
  components: { ClientAnalyticSelector, ClientAnalyticMenu, ClientAnalyticSpin }
}
</script>

<style scoped>
.headers {
  background: white;
  padding:0 !important;
  display: inline-block;
}
.layouts {
  min-width: 1500px !important;
}
.analytic-spin{
  height: 100%;
  position: relative;
}
</style>
