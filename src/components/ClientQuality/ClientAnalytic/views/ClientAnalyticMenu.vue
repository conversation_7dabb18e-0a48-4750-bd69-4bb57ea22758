<template>
  <div>
    <Menu mode="horizontal" active-name="dataOverview" ref="menu">
      <MenuItem name="dataOverview" @click.native="checkTab('dataOverview')">
        <Icon type="ios-paper" />数据概览
      </MenuItem>
      <MenuItem name="developProcess" @click.native="checkTab('developProcess')">
        <Icon type="ios-people" />研发过程
      </MenuItem>
      <MenuItem name="testQuality" @click.native="checkTab('testQuality')">
        <Icon type="ios-stats" />提测质量
      </MenuItem>
    </Menu>
    <Row :gutter="16">
      <i-col span="24" style="padding-right:0px;">
        <div v-if="tab=='dataOverview'">
          <ClientAnalyticDataOverview />
        </div>
        <div v-if="tab=='developProcess'">
          <ClientAnalyticDevelopProcess />
        </div>
        <div v-if="tab=='testQuality'">
          <ClientAnalyticTestQuality />
        </div>
      </i-col>
    </Row>
  </div>
</template>


<script>
import ClientAnalyticDataOverview from './ClientAnalyticDataOverview'
import ClientAnalyticDevelopProcess from './ClientAnalyticDevelopProcess'
import ClientAnalyticTestQuality from './ClientAnalyticTestQuality'

export default {
  name: 'ClientAnalyticBase',
  data () {
    return {
      tab: 'dataOverview'
    }
  },
  components: { ClientAnalyticDataOverview, ClientAnalyticDevelopProcess, ClientAnalyticTestQuality },
  methods: {
    checkTab (tabName) {
      this.tab = tabName
    }
  }
}
</script>
