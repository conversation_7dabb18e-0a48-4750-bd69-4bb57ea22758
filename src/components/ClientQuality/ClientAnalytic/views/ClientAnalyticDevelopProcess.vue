<template>
  <div>
    <Row type="flex" justify="space-around" class="code-row-bg">
      <div style="width: 100%;">
        <i-col span="24">
          <div style="padding: 20px;text-align:left">
            <Card :bordered="false" class="card">
              <p slot="title">资源消耗统计</p>
              <Table border :columns="developProcess.resourceStatistic.columns" :data="developProcess.resourceStatistic.data" size="small"></Table>
            </Card>
            <Card :bordered="false" class="card">
              <p slot="title">如期交付情况</p>
              <Table border :columns="developProcess.deliverySituation.columns" :data="developProcess.deliverySituation.data" size="small"></Table>
            </Card>
          </div>
        </i-col>
      </div>
    </Row>
  </div>
</template>

<script>
export default {
  name: 'ClientAnalyticDevelopProcess',
  computed: {
    developProcess () {
      return this.$store.state.ClientAnalytic.developProcess
    }
  }
}
</script>
<style scoped>
.card {
  margin-bottom: 40px;
}
.ivu-col-span-20 {
  width:100%
}
</style>

