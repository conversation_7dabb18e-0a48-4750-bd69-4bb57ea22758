<template>
  <div>
    <Row type="flex" justify="space-around" class="code-row-bg">
      <div style="width: 100%">
        <i-col span="24">
          <div style="padding: 20px;text-align:left">
            <Card :bordered="false" class="card">
              <p slot="title">Bug统计</p>
              <Table
                border
                :columns="testQuality.issueStatistic.columns"
                :data="testQuality.issueStatistic.data"
                size="small"
              >
                <template slot-scope="{ row, index }" slot="action">
                  <Button type="primary" size="small" style="margin-right: 5px" @click="show(index)">查看方向详情</Button>
                </template>
              </Table>
            </Card>
            <Card :bordered="false" class="card">
              <p slot="title">方向详情</p>
              
              <el-row>
                
                  <el-popover
                  placement="right"
                  width="200">
                  <el-checkbox-group v-model="checkList" @change="cons">
                    <el-checkbox v-for="item in this.testQuality.testQuality.columns" :key="item.key" :label="item.title"></el-checkbox>
                  </el-checkbox-group>
                  <el-button slot="reference">列筛选</el-button>
                </el-popover>

                <el-popover
                  placement="right"
                  width="200">
                  <el-checkbox-group v-model="QAAdminCheckList" @change="cons">
                    <el-checkbox v-for="item in this.QAAdminCheckedList" :key="item.key" :label="item"></el-checkbox>
                  </el-checkbox-group>
                  <el-button slot="reference">QA负责人筛选</el-button>
                </el-popover>
                
                
              </el-row>
              <el-table :data="testQualityCheckedData" border>
                <el-table-column  v-for="(item,index) in this.testQualityCheckedCloumn" :key="index" :label="item.title" :prop="item.key"   align="center" />
                <el-table-column label="操作" fixed="right">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary" 
                      @click="jumpToTask(scope.$index)">查看详情</el-button>
                    
                  </template>
                </el-table-column>
              </el-table>
            </Card>
          </div>
        </i-col>
      </div>
    </Row>
  </div>
</template>

<script>
export default {
  name: 'ClientAnalyticTestQuality',
  data () {
    return {
      checkList: [],
      QAAdminCheckList: []
    }
  },
  computed: {
    testQuality () {
      return this.$store.state.ClientAnalytic.testQuality
    },
    testQualityCheckedCloumn () {
      let columns = this.$store.state.ClientAnalytic.testQuality.testQuality.columns
      let columnsChecked = []
      for (let i = 0; i < columns.length; i++) {
        if (this.checkList.includes(columns[i]['title'])) {
          columnsChecked.push(columns[i])
        }
      }
      return columnsChecked
    },
    testQualityCheckedData () {
      let data = this.$store.state.ClientAnalytic.testQuality.testQuality.data
      let dataChecked = []
      for (let i = 0; i < data.length; i++) {
        if (this.QAAdminCheckList.includes(data[i]['QAAdmin'])) {
          dataChecked.push(data[i])
        }
      }
      return dataChecked
    },
    QAAdminCheckedList () {
      let QAAdminCheckedList = []
      let data = this.testQuality.testQuality.data
      for (let i = 0; i < data.length; i++) {
        QAAdminCheckedList.push(data[i]['QAAdmin'])
      }
      QAAdminCheckedList = Array.from(new Set(QAAdminCheckedList))
      this.QAAdminCheckList = QAAdminCheckedList
      return QAAdminCheckedList
    }
  },
  mounted: function () {
    this.checkList = this.testQuality.testQuality.checked
  },
  methods: {
    show (index) {
      let business = this.testQuality.issueStatistic.data[index]['business']
      console.log(business)
      this.testQuality.testQuality.data = this.testQuality.testQuality.dataMap[business]
    },
    jumpToTask (index) {
      let project = this.testQualityCheckedData[index]['project']
      let task = this.testQualityCheckedData[index]['task']
      window.open(`https://ones.sankuai.com/ones/product/${project}/testdetail/${task}`, '_blank')
    }
  }
}
</script>
<style scoped>
.card {
  margin-bottom: 40px;
}
.ivu-col-span-20 {
  width:100%
}
</style>

