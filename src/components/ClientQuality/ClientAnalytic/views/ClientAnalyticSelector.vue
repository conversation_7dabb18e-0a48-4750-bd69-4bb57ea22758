<template>
  <div>
    <div style="float:left">
      <el-select 
        v-model="projectSelected" 
        placeholder="请选择项目"
        style="width:150px;"
        multiple>
        <el-option
          v-for="item in projectList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-cascader
        placeholder="请选择方向"
        style="width:150px;"
        :show-all-levels="false"
        :options="businessList"
        :props="{ multiple: true }"
        collapse-tags
        filterable
        v-model="businessSelected"
      ></el-cascader>
      <el-date-picker
        v-model="timeSelected"
        type="daterange"
        style="width:380px;"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right"
        :clearable="false"
        value-format="timestamp"
      ></el-date-picker>
      <el-select v-model="techModeSelected" placeholder="请选择技术实现方式" multiple style="width:175px;">
        <el-option
          v-for="item in techModeList"
          :value="item.value"
          :label="item.label"
          :key="item.value"
        >{{ item.label }}</el-option>
      </el-select>    
      <el-select v-model="platformSelected" placeholder="请选择平台" multiple style="width:150px;">
        <el-option
          v-for="item in platformList"
          :value="item.value"
          :label="item.label"
          :key="item.value"
        >{{ item.label }}</el-option>
      </el-select>
      <!-- 加入测试角色选择框-->
      <el-select v-model="testerSelected" placeholder="请选择测试人员角色" multiple style="width:175px;">
        <el-option
          v-for="item in testerList"
          :value="item.value"
          :label="item.label"
          :key="item.value"
        >{{ item.label }}</el-option>
      </el-select>
      <el-input  style="height:60px;width:120px;" v-model="parentPageId" placeholder="测试报告ID"></el-input>

      
      <Button
        type="warning"
        icon="ios-search"
        style="height:40px"
        :loading="spinDisplay"
        @click="getAnalyticList(projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected,testerSelected)"
      >Search</Button>
      <el-tooltip class="item" effect="dark" content="复制" placement="bottom">
        <Button
          icon="md-share"
          v-clipboard:copy="copyPath()"
          v-clipboard:success="copySuccess"
          style="height:40px"
        >分享</Button>
      </el-tooltip>
      <Button
        type="warning"
        icon="ios-search"
        style="height:40px"
        :loading="spinDisplay"
        @click="report(overview, developProcess, testQuality, projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected,testerSelected)"
      >生成报告</Button>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      'parentPageId': '',
      'pickerOptions': {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created: function () {
    this.$store.dispatch('loadCASelectors')
    this.$store.dispatch('loadCAList')
    let filter = this.$route.query.filter// router 用于 分享时拼接URL 请求的哪里的数据
    console.log(`this filter : ${typeof filter}`)
    if (filter) {
      let filterObj = JSON.parse(filter)
      this.projectSelected = filterObj['projectSelected']
      this.businessSelected = filterObj['businessSelected']
      this.timeSelected = filterObj['timeSelected']
      this.techModeSelected = filterObj['techModeSelected']
      this.platformSelected = filterObj['platformSelected']
      this.testerSelected = filterObj['testerSelected']
      this.getAnalyticList(this.projectSelected, this.businessSelected, this.timeSelected, this.techModeSelected, this.platformSelected, this.testerSelected)
    }
  },
  methods: {
    getAnalyticList (projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected, testerSelected) {
      if (projectSelected === '') {
        this.$Message.info('请选择项目')
        return
      }
      this.$store.commit('setSpinDisplay', true)
      // 后端数据模版由字符串变成了列表，前端处理techModeSelected-[['124176','48571'],'123'] -> ['124176','48571'，'123']
      techModeSelected = techModeSelected.reduce(function(a, b) {
        return a.concat(b)
      }, [])
      let params = {projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected, testerSelected}
      this.$store.dispatch('getCAList', params)
    },
    report (overview, developProcess, testQuality, projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected) {
      let parentPageId = this.parentPageId
      techModeSelected = techModeSelected.reduce(function(a, b) {
        return a.concat(b)
      }, [])
      let params = {parentPageId, overview, developProcess, testQuality, projectSelected, businessSelected, timeSelected, techModeSelected, platformSelected}
      this.$store.dispatch('createReport', params)
    },
    copyPath () {
      let params = {
        'projectSelected': this.projectSelected,
        'businessSelected': this.businessSelected,
        'timeSelected': this.timeSelected,
        'techModeSelected': this.techModeSelected,
        'platformSelected': this.platformSelected,
        'testerSelected': this.testerSelected}
      console.log(JSON.stringify(params))
      return `http://qa.sankuai.com/client/analytic?filter=${JSON.stringify(params)}`
    },
    copySuccess () {
      this.$Message.info('copied success')
    }
  },
  computed: {
    spinDisplay () {
      return this.$store.state.ClientAnalytic.spinDisplay
    },
    projectList () {
      return this.$store.state.ClientAnalytic.projectList
    },
    projectSelected: {
      get () {
        return this.$store.state.ClientAnalytic.projectSelected
      },
      set (val) {
        console.log(`projectSelected: ${val}`)
        this.$store.commit('setProjectSelected', val)
      }
    },
    businessList () {
      return this.$store.state.ClientAnalytic.businessList
    },
    businessSelected: {
      get () {
        return this.$store.state.ClientAnalytic.businessSelected
      },
      set (val) {
        console.log(`BusinessSelected: ${val.length}`)
        this.$store.commit('setBusinessSelected', val)
      }
    },
    techModeList () {
      return this.$store.state.ClientAnalytic.techModeList
    },
    techModeSelected: {
      get () {
        return this.$store.state.ClientAnalytic.techModeSelected
      },
      set (val) {
        console.log(`techModeSelected: ${val}`)
        this.$store.commit('setTechModeSelected', val)
      }
    },
    testerList () {
      return this.$store.state.ClientAnalytic.testerList
    },
    testerSelected: {
      get () {
        return this.$store.state.ClientAnalytic.testerSelected
      },
      set (val) {
        console.log(`testerSelected: ${val}`)
        this.$store.commit('setTesterSelected', val)
      }
    },
    platformList () {
      return this.$store.state.ClientAnalytic.platformList
    },
    platformSelected: {
      get () {
        return this.$store.state.ClientAnalytic.platformSelected
      },
      set (val) {
        console.log(`platformSelected: ${val}`)
        this.$store.commit('setPlatformSelected', val)
      }
    },
    timeSelected: {
      get () {
        return this.$store.state.ClientAnalytic.timeSelected
      },
      set (val) {
        console.log(`timeSelected: ${val}`)
        this.$store.commit('setTimeSelected', val)
      }
    },
    overview () {
      return this.$store.state.ClientAnalytic.overview
    },
    developProcess () {
      return this.$store.state.ClientAnalytic.developProcess
    },
    testQuality () {
      return this.$store.state.ClientAnalytic.testQuality
    }
  }
}
</script>
