import * as apis from '../apis'
import { Message } from 'element-ui'

export default {
  state: {
    // spin
    spinDisplay: false,
    // selectors
    projectList: [],
    projectSelected: '',
    businessList: [],
    businessSelected: '',
    techModeList: [],
    techModeSelected: '',
    testerList: [],
    testerSelected: '',
    platformList: [],
    platformSelected: '',
    timeSelected: [],

    // analyticList
    overview: {
      'chart': {
        'demandDistributionPieChart': {
          'title': '产品/研发 需求占比',
          'data': []
        },
        'QAInvolvedPieChart': {
          'title': 'QA / PM&RD 测试占比',
          'data': []
        },
        'includeRDPercentageColumnChart': {
          'title': '涉及后台需求占比',
          'column': {
            'x': []
          },
          'data': []
        },
        'isRDFirstPercentageColumnChart': {
          'title': '后台先行需求占比',
          'column': {
            'x': []
          },
          'data': []
        }
      },
      'table': {
        'resourceDistribution': {
          'columns': [],
          'data': []
        },
        'processImplementation': {
          'columns': [],
          'data': []
        }
      }
    },
    developProcess: {},
    testQuality: {
      'issueStatistic': {
        'data': [],
        'columns': []
      },
      'testQuality': {
        'data': [1],
        'columns': []
      }
    }

  },
  mutations: {
    // spin
    setSpinDisplay (state, spinDisplay) {
      state.spinDisplay = spinDisplay
    },
    // selectors
    setProjectList (state, projectList) {
      state.projectList = projectList
    },
    setProjectSelected (state, projectSelected) {
      state.projectSelected = projectSelected
    },
    setBusinessList (state, businessList) {
      state.businessList = businessList
    },
    setBusinessSelected (state, businessSelected) {
      state.businessSelected = businessSelected
    },
    setTechModeList (state, techModeList) {
      state.techModeList = techModeList
    },
    setTechModeSelected (state, techModeSelected) {
      state.techModeSelected = techModeSelected
    },
    // test list lxy
    setTesterList (state, testerList) {
      state.testerList = testerList
    },
    setTesterSelected (state, testerSelected) {
      state.testerSelected = testerSelected
    },
    setPlatformList (state, platformList) {
      state.platformList = platformList
    },
    setPlatformSelected (state, platformSelected) {
      state.platformSelected = platformSelected
    },
    setTimeSelected (state, timeSelected) {
      state.timeSelected = timeSelected
    },

    // analyticList
    setOverview (state, overview) {
      state.overview = overview
    },
    setDevelopProcess (state, developProcess) {
      state.developProcess = developProcess
    },
    setTestQuality (state, testQuality) {
      state.testQuality = testQuality
    }
  },
  actions: {
    // selectors
    loadCASelectors (context) {
      /*
          componments: selector
          刷新头部筛选器
      */
      apis.getSelectors().then(resp => {
        if (!resp.data.code) {
          Message({
            message: resp.data.msg,
            type: 'error'
          }, true)
          return
        }
        let selectors = resp.data.selectors
        context.commit('setProjectList', selectors.projectList)
        context.commit('setBusinessList', selectors.businessList)
        context.commit('setTimeSelected', selectors.timeRange)
        context.commit('setTechModeList', selectors.techModeList)
        context.commit('setTesterList', selectors.testerList)
        context.commit('setPlatformList', selectors.platformList)
      })
    },

    // analyticList
    loadCAList (context) {
      apis.getAnalyticList().then(resp => {
        if (!resp.data.code) {
          Message({
            message: resp.data.msg,
            type: 'error'
          }, true)
          return
        }
        let list = resp.data.list
        context.commit('setOverview', list.overview)
        context.commit('setDevelopProcess', list.developProcess)
        context.commit('setTestQuality', list.testQuality)
      })
    },
    getCAList (context, params) {
      apis.setAnalyticList(params).then(resp => {
        if (!resp.data.code) {
          context.commit('setSpinDisplay', false)
          Message({
            message: resp.data.msg,
            type: 'error'
          }, true)
          return
        }
        context.commit('setSpinDisplay', false)
        let list = resp.data.list
        context.commit('setOverview', list.overview)
        context.commit('setDevelopProcess', list.developProcess)
        context.commit('setTestQuality', list.testQuality)
      })
    },
    createReport ({ context }, params) {
      apis.createReport(params).then(resp => {
        if (!resp.data.code) {
          Message({
            message: resp.data.msg,
            type: 'error'
          }, true)
          return
        }
        Message({
          message: resp.data.msg,
          type: 'success'
        }, true)
      })
    }
  }
}
