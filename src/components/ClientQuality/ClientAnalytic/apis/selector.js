import urlConf from '../../../../assets/user_env'
import axios from 'axios'

export const getSelectors = () => {
  return axios({
    url: urlConf.clientAnalyticUrl + '/api/selectors',
    method: 'GET'
  })
}

export const createReport = (params) => {
  return axios({
    url: urlConf.clientAnalyticUrl + '/api/report',
    method: 'POST',
    headers: { 'Content-Type': 'text/plain' },
    data: params
  })
}
