<template>
  <div>
    <Layout>
      <Header id="header" :style="{ background: 'white', height: '55px'}">
        <Row type="flex">
          <Col order="1" :xs="6" :sm="6" :md="4" :lg="4" style="text-align: left;">
            <div style="margin-top: -5px;padding-left: 10px">
              <span style="text-align: center; font-size:larger;font-weight:900;color:#606979">ME多语言翻译测试</span>
              <Divider type="vertical" style="height: 30px"/>
            </div>
          </Col>
          <Col order="2" :xs="6" :sm="6" :md="1" :lg="1">
          </Col>
          <Col order="3" :xs="15" :sm="15" :md="12" :lg="12" style="
              height: 50px;
              margin-top: -5px;
              text-align: left;
              font-weight: bolder;
              padding-left: 10px">
          </Col>
          <Col order="5" offset="2" :xs="0" :sm="0" :md="3" :lg="3" style="
              height: 50px;
              margin-top: -5px;
              text-align: center;
              font-weight: bolder;
            ">
            <div style="margin-left: 25%; font-weight: bolder">
              <Divider type="vertical" style="height: 30px"/>
              <Dropdown @on-click="techHelp">
                <a href="javascript:void(0)" style="color: #606979">
                  <span><Icon type="ios-help-circle-outline" size="16"/></span> 文档 </a>
                <Icon type="ios-arrow-down" style="padding-left: 5px" />
                <DropdownMenu slot="list">
                  <DropdownItem style="text-align: left" name="userGuide">
                    <Icon type="ios-information-circle-outline" style="padding-right: 5px" />使用手册
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </Col>
          <Col order="6" :xs="0" :sm="0" :md="3" :lg="2">
            <div class="layout-nav" style="margin-left: 25%; width: 100%; margin-right: 0">
              <div id="userInfo" style="display: flex; color: #606979">
                <Dropdown trigger="click" @on-click="exitSystem">
                  <a href="javascript:void(0)" style="color: #606979;">
                    <div style="margin-top: 2px"><span><Icon type="ios-contact-outline" size="25"/></span>
                      {{ userInfo.userName }}<Icon type="ios-arrow-down" style="padding-left: 5px" />
                    </div>
                  </a>
                  <DropdownMenu slot="list">
                    <DropdownItem style="text-align: center" name="quit">
                      <Icon type="md-exit" style="padding-right: 5px" />退出
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
            </div>
          </Col>
        </Row>
      </Header>
      <Content>
        <div style="padding: 30px 100px ">
          <Timeline>
          <TimelineItem>
              <p class="time-line-title">1.扫描空间</p>
              <div style="padding: 10px">
                NameSpaceId:<Input v-model="nameSpaceId" placeholder="请输入NameSpaceId" style="width: 300px" />
              </div>
              <div style="padding: 10px">
                i18nCookie:   <Input v-model="i18nCookie" placeholder="请输入浏览器cookie" style="width: 300px" />
              </div>
              <div style="padding: 10px">
                <Button type="primary" @click="getTextKeyList">下一步</Button>
              </div>
          </TimelineItem>
          <TimelineItem>
              <p class="time-line-title">2.选择TextKey</p>
              <div style="padding: 10px 0px">
                <Checkbox v-model="translated" @on-change="refreshSourceTextKeyList">已翻译</Checkbox>
                <Checkbox v-model="published" @on-change="refreshSourceTextKeyList">已线上发布</Checkbox>
              </div>
              <Transfer
                :data="sourceTextKeyList"
                :target-keys="targetTextKeyList"
                :list-style="listStyle"
                :render-format="renderPageSelectTip"
                filterable
                :filter-method="filterMethod"
                @on-change="textKeyChange"></Transfer>
              <div style="padding: 20px 0px">
                <CheckboxGroup v-model="selectedLanguage">
                    <template v-for="(language, code) in languageMap">
                      <Checkbox :label= "language">
                        <span>{{ code }}</span>
                      </Checkbox>
                    </template>
                </CheckboxGroup>
              </div>
               <div style="padding: 10px">
                <Button type="primary" @click="translationCheck">提交</Button>
              </div>
          </TimelineItem>
       </Timeline>
        </div>
      </Content>
    </Layout>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import axios from "axios";
let userInfo = {
  userId: "",
  userName: "Dev",
  userLogin: "jinhailiang",
  userUrl: "",
};

Bus.$on("refreshUserInfo", function (UserInfo) {
  userInfo = UserInfo;
});

export default {
  data: function () {
    return {
      userInfo: userInfo,
      i18nCookie: "",
      nameSpaceId: "",
      nameSpaceKey: "",
      translated: true,
      published: true,
      originTextKeyList: [],
      sourceTextKeyList: [],
      targetTextKeyList: [],
      selectedLanguage: ["繁体中文:zh-TW", "英语:en-US"],
      languageMap: {
        "繁体中文": "繁体中文:zh-TW",
        "英语": "英语:en-US",
        "日语": "日语:ja-JP",
        "韩语": "韩语:ko-KR",
        "泰语": "泰语:th-TH"
      },
      listStyle: {
          width: '400px',
          height: '400px'
      }
    };
  },
  methods: {
    refreshUserInfo(userInfo) {
      this.userName = userInfo.userName;
    },
    exitSystem(value) {
      console.log(value);
      if (value === "quit") {
        // 退出
        window.location.href = window.location.href;
        Bus.ssoWeb.logout();
      }
    },
    techHelp(value) {
      console.log(value);
      if (value === "userGuide") {
        window.open('https://km.sankuai.com/collabpage/2298283262');
      }
    },
    filterMethod (data, query) {
        return data.key.indexOf(query) > -1;
    },
    textKeyChange (newTargetKeys) {
        this.targetTextKeyList = newTargetKeys;
    },
    renderPageSelectTip(item) {
      return '<span title="' + item.content +':'+item.key+ '">' + item.key + "</span>";
    },
    refreshSourceTextKeyList(){
      this.sourceTextKeyList = this.originTextKeyList.filter(item =>
        item.translated === (this.translated ? 1 : 0) &&
        (this.published ? item.distributeStatus === 7 : item.distributeStatus != 7))
    },
    translationCheck(){
        this.$axios({
         method: "post",
         data:{
           "selectedLanguage": this.selectedLanguage,
           "textKeyList": this.targetTextKeyList,
           "nameSpaceKey": this.nameSpaceKey,
           'misName': this.userInfo.userLogin
         },
         url: this.env.llm_service_url + "/me/val_translation"
      })
        .then(res => {
          console.log(res);
          this.$Notice.success({
              title: '成功',
               desc: '请查看大象消息'
          });
        })
        .catch(function(error) {
          this.$Notice.warning({
              title: '接口问题',
              desc: '查看控制台信息'
          });
        });
    },
    getTextKeyList(){
       this.targetTextKeyList = [];
       this.$axios({
         method: "post",
         data:{
           "nameSpaceId": this.nameSpaceId,
           "i18nCookie":  this.i18nCookie
         },
         url: this.env.llm_service_url + "/me/getTextKeyList"
      })
        .then(res => {
          console.log(res);
          this.$Notice.success({
              title: '成功',
          });
          this.originTextKeyList = res.data.data.textKeyList;
          this.nameSpaceKey = res.data.data.nameSpaceKey
          this.refreshSourceTextKeyList();
        })
        .catch(function(error) {
          this.$Notice.warning({
              title: '接口问题',
              desc: '查看控制台信息'
          });
        });
    }
  },
  mounted: function () {
    this.$store.dispatch('getBusinessList')
  },
  computed: {
    businessList() {
      return this.$store.state.clientQuality.businessList
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

.layout-nav {
  text-align: right;
  width: 50px;
  margin: 0 auto;
  margin-right: 0;
  font-weight: bolder;
  margin-top: -8px;
  font-size: 14px;
}
.time-line-title{
    font-size: 18px;
    font-weight: bold;
}
</style>

