<template>
  <div id="home">
    <template
      v-if="
        $route.path.indexOf('/microscope') > -1 ||
        $route.path === '/compatibilityUserConfig' ||
        $route.path === '/clientScheme' ||
        $route.path === '/clientSpecialConfig' ||
        $route.path === '/client/logView' ||
        $route.path === '/client/pageBoard' ||
        $route.path === '/visionComponents' ||
        $route.path === '/deviceOperation'
      "
    >
      <ClientCompatibilityHead></ClientCompatibilityHead>
    </template>
    <template v-else>
      <ClientCommonHead></ClientCommonHead>
    </template>
    <div class="layout">
      <layout>
        <Layout style="text-align: center">
          <Content :style="{ padding: '16px 30px' }">
            <template v-if="$route.path === '/client'">
              <home-page></home-page>
            </template>
            <template v-else>
              <Card shadow>
                <div style="height: fit-content; min-height: 800px">
                  <router-view :key="key"></router-view>
                </div>
              </Card>
            </template>
          </Content>
        </Layout>
      </layout>
    </div>
    <footer
      v-if="
        !(
          $route.path.indexOf('/microscope') > -1 ||
          $route.path === '/compatibilityUserConfig' ||
          $route.path === '/clientScheme' ||
          $route.path === '/clientSpecialConfig' ||
          $route.path === '/client/logView' ||
          $route.path === '/client/pageBoard' ||
          $route.path === '/visionComponents' ||
          $route.path === '/deviceOperation'
        )
      "
    >
      <template>
        <ClinetCommonFoot></ClinetCommonFoot>
      </template>
    </footer>
    <VueBotUI
        :messages="chatBotMessages"
        :options="botOptions"
        :bot-typing=botTyping
        @init="onChatBoardOpen"
        @destroy="onChatBoardClose"
        @msg-send="botSendMessage"
      >
        <template v-slot:header>
          <div style="display: flex;">
            <img src="/static/img/ai-chat-logo.png" style="height: 34px;width: 34px">
            <div style="padding-top:6px;font-size: 15px;font-weight: bold;color:#492ae8">Hyperjump Copilot</div>
          </div>
        </template>
        <template v-slot:actions>
          <div style="padding-top: 5px;padding-right: 2px">
            <Button shape="circle" size="small" icon="md-refresh" @click="clearChatHistory"></Button>
          </div>
        </template>
        <template v-slot:bubbleButton>
          <Button v-if="chatBoardOpen" shape="circle" type="text" size="large" icon="md-close"/>
          <img v-else src="https://s3plus.sankuai.com/vision-image/resource/chatbot-logo.png" style="height: 38px;width: 38px">
        </template>
      </VueBotUI>
  </div>
</template>

<script>
/* eslint-disable */
import event from '@/assets/event_bus'
import ClientCommonHead from './baseComponents/ClientCommonHead'
import ClinetCommonFoot from './baseComponents/ClientCommonFoot'
import ClientCompatibilityHead from './baseComponents/ClientCompatibilityHead'
import HomePage from './HomePage'
import { Bus } from '@/global/bus'
import { VueBotUI } from 'vue-bot-ui'

export default {
  name: 'Page',
  components: {
    HomePage,
    ClientCommonHead,
    ClientCompatibilityHead,
    ClinetCommonFoot,
    VueBotUI
  },
  data() {
    return {
      isCollapsed: false,
      chatBotMessages: [{
        agent: 'bot',
        type: 'button',
        text: '请选择你的问题',
        disableInput: true,
        options: [
          {
            text: '页面白屏',
            value: '',
            action: 'postback'
          },
          {
            text: '手机无网络',
            value: '',
            action: 'postback'
          }
        ]
      }],
      botTyping: false,
      chatBoardOpen: false,
      botOptions: {
        colorScheme: '#fff',
        inputPlaceholder: '请输入你的问题',
        botAvatarImg: 'https://s3plus.sankuai.com/vision-image/resource/chatbot-logo.png'
      },
      fridayToken: '',
    }
  },
  computed: {
    key() {
      return this.$route.path + this.$route.query.group + this.$route.query.version + this.$route.query.jobId
    }
  },
  mounted() {
    event.$on('collapsePage', () => {
      this.isCollapsed = true
    })
    this.$store.dispatch('getGroupsData')
    this.getFridayToken()
  },
  methods: {
    getFridayToken() {
      this.$axios({
        method:"get",
        url:this.env.aiengineering_url + '/gpt_stream/friday_token'
      }).then((res) => {
        this.fridayToken = res.data.token
      }).catch(function (error) {
        console.log(error)
      })
    },
    async botSendMessage(value) {
      this.userQuery = value.text
      this.chatBotMessages.push({
        agent: 'user',
        type: 'text',
        text: this.userQuery
      })
      this.botTyping = true
      let queryPrompt = this.userQuery
      // eslint-disable-next-line no-undef
      this.controller = new AbortController()
      this.response = ''
      this.stopGenerating = false
      const response = await fetch('https://aigc.sankuai.com/conversation/v2/openapi', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          'appId': '1677275525413957683',
          'userId': Bus.userInfo.userLogin,
          'userType': 'MIS',
          'utterances': [queryPrompt],
          'stream': true,
          'accessToken': this.fridayToken
        }),
        signal: this.controller.signal
      })
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let textShow = ''
      while (true) {
        if (this.stopGenerating) break
        const { done, value } = await reader.read()
        if (done) break
        let resText = decoder.decode(value)
        let resList = resText.split('\n')
        for(let i=0;i<resList.length;i++){
          let res = resList[i]
          if (res.startsWith('data:')){
            res = res.substring(5)
          }
          if(res.length > 2){
            let resObj = JSON.parse(res)
            let contentText = ''
            for(let j=0;j<resObj.data.contents.length;j++){
              let content = resObj.data.contents[j]
              if(content.type === 'TEXT' && content.text !== '[DONE]'){
                  contentText = contentText + content.text
                }
              if(content.type === 'LINK'){
                contentText = contentText + content.href
              }
              if(j===resObj.data.contents.length-1 && contentText.length > 0){
                textShow = contentText
              }
            }
          }
        }
        if (this.chatBotMessages.at(-1).agent === 'user' && textShow.length > 0) {
          this.botTyping = false
          this.chatBotMessages.push({
            agent: 'bot',
            type: 'text',
            text: textShow,
            disableInput: true
          })
        } else {
          this.chatBotMessages.at(-1).text = this.chatBotMessages.at(-1).text = textShow
        }
      }
      console.log('chat completion')
      this.chatBotMessages.at(-1).disableInput = false
    },
    stopGenerating() {
      this.controller.abort()
      this.stopGenerating = true
    },
    clearChatHistory() {
      this.chatBotMessages = [{
        agent: 'bot',
        type: 'button',
        text: '请选择你的问题',
        disableInput: true,
        options: [
          {
            text: '分析评测结果',
            value: '',
            action: 'postback'
          },
          {
            text: '提供应用改进建议',
            value: '',
            action: 'postback'
          }
        ]
      }]
    },
    onChatBoardOpen() {
      this.chatBoardOpen = true
    },
    onChatBoardClose() {
      this.chatBoardOpen=false
    }
  }
}
</script>

<style scoped>
#home {
  position: relative;
  overflow-y: scroll;
  height: 100vh;
}
.layout {
  /*border: 0px solid #d7dde4;*/
  background: #f5f7f9;
  position: relative;
  /*border-radius: 4px;*/
  overflow: hidden;
}
.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: fit-content;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width 0.2s ease 0.2s;
}
.menu-item i {
  transform: translateX(0px);
  transition: font-size 0.2s ease, transform 0.2s ease;
  vertical-align: middle;
  font-size: 16px;
}
.collapsed-menu span {
  width: 0;
  transition: width 0.2s ease;
}
.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size 0.2s ease 0.2s, transform 0.2s ease 0.2s;
  vertical-align: middle;
  font-size: 22px;
}
/deep/ .qkb-board {
  width: 500px;
}
/deep/ .qkb-board-header__title{
  font-size: 15px;
}
/deep/ .qkb-msg-bubble-component__text {
  font-size: 14px;
}
/deep/ .qkb-board-action__input {
  font-size: 15px;
}
/deep/ .qkb-mb-button-options__btn {
  font-size: 12px;
}
/deep/ .qkb-msg-bubble-component__text {
  white-space: pre-wrap;
}
</style>
