<template>
  <div>
    <Row>
      <Col span="16">
      <div class="content-container">
        <div class="content-title">
          <h4>My Business</h4>
        </div>
        <UserBusiness></UserBusiness>
      </div>
      </col>

      <Col span="8">
      <div class="content-container">
        <div class="content-title">
          <h4>
            Explore
          </h4>
        </div>
        <Card :bordered="false" class="explore-card" @click.native="getLink('client/analytic', 'site')">
          <Row>
            <Col span="4">
            <img class="explore-img" src="https://s3plus-bj02.sankuai.com/v1/mss_aa37ef4f80a2484f93b09795e1e8bd6b/mmcd-prod/icon/order_%E8%AE%A2%E5%8D%95.png">
            </Col>
            <Col span="20" style="text-align:left">
            <strong>业务过程度量</strong>
            <br />
            <span>客户端研发过程质效数据统计平台，支持一键生成测试报告</span>
            </Col>
          </Row>
        </Card>
        <Card :bordered="false" class="explore-card" @click.native="getLink('autotest/analytic', 'site')">
          <Row>
            <Col span="4">
            <img class="explore-img" src="https://s3plus-bj02.sankuai.com/v1/mss_aa37ef4f80a2484f93b09795e1e8bd6b/mmcd-prod/icon/safety_%E5%AE%89%E5%85%A8.png">
            </Col>
            <Col span="20" style="text-align:left">
            <strong>自动化度量</strong>
            <br />
            <span>自动化执行情况、运行效果数据统计分析系统，可辅助自动化运营工作</span>
            </Col>
          </Row>
        </Card>
        <Card :bordered="false" class="explore-card" @click.native="getLink('autotest/ConfigStatus', 'site')">
          <Row>
            <Col span="4">
            <img class="explore-img" src="https://s3plus-bj02.sankuai.com/v1/mss_aa37ef4f80a2484f93b09795e1e8bd6b/mmcd-prod/icon/contract_%E5%90%88%E5%90%8C.png">
            </Col>
            <Col span="20" style="text-align:left">
            <strong>持续交付度量</strong>
            <br />
            <span>客户端持续交付链路配置接入、触发及运行情况数据统计系统，提供各业务方向相关数据</span>
            </Col>
          </Row>
        </Card>
      </div>
      <div class="content-container">
        <div class="content-title">
          <h4>
            AutoTest
          </h4>
        </div>
        <div>
          <Card :bordered="false" class="explore-card" @click.native="getLink('microscope/jobList','site')">
            <Row>
              <Col span="4">
              <img class="explore-img" src="/static/img/HyperJump.png">
              </Col>
              <Col span="20" style="text-align:left">
              <strong>HyperJump</strong>
              <br />
              <span>页面场景视觉测试平台,通过深度链接直达测试场景，以图像处理技术实现的视觉测试工具</span>
              </Col>
            </Row>
          </Card>
          <Card :bordered="false" class="explore-card" @click.native="getLink('clientShortLinkJob','site')">
            <Row>
              <Col span="4">
              <img class="explore-img" src="https://s3plus-bj02.sankuai.com/v1/mss_aa37ef4f80a2484f93b09795e1e8bd6b/mmcd-prod/icon/Sign_%E7%AD%BE%E5%88%B0.png">
              </Col>
              <Col span="20" style="text-align:left">
              <strong>ShortLink</strong>
              <br />
              <span>短链路客户端UI自动化，一种基于深度链接技术，可直达被测页面的客户端测试自动化解决方案</span>
              </Col>
            </Row>
          </Card>
        </div>
      </div>
      </col>
    </Row>
  </div>
</template>

<script>
/* eslint-disable */
import MenuCard from './baseComponents/MenuCard'
import UserBusiness from './UserBusiness'
export default {
  name: 'HomePage',
  components: { MenuCard, UserBusiness },
  data() {
    return {}
  },
  methods: {
    getLink(path, type = 'site') {
      let link = ''
      if (type === 'site') {
        let routeData = this.$router.resolve({ path: path })
        link = routeData.href
      } else {
        link = path
      }
      window.open(link, '_blank')
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #ffffff;
  margin: 10px 15px 20px 15px;
  padding: 15px 10px 20px 10px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
.button {
  border-color: #fa8c16;
  background-color: #fff7e6;
  color: #fa8c16;
  font-weight: bolder;
}
.base-button {
  border-color: rgb(24, 144, 255);
  background-color: #ffffff;
  color: rgb(24, 144, 255);
  font-weight: bolder;
}
.content-title {
  text-align: left;
  padding: 0 0 18px 20px;
}
.explore-img {
  width: 52px;
}
.explore-card {
  margin-bottom: 6px;
}
</style>
