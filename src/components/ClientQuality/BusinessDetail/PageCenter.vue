<template>
  <div>
    <PageFilter :permission="permission" :businessId="businessId"></PageFilter>
    <Row class="no-info" v-if="pageList&&pageList.length===0">
      <img class="error-img" src="/static/img/cqp-error.jpg" />
      <br>
      <span>暂无页面信息</span>
    </Row>
    <Row :gutter="16" style="padding-left:10px;padding-right:10px">
      <Col span="6" v-for="page in pageList" :key="page.id">
      <PageCell :page="page" :permission="permission"></PageCell>
      </Col>
    </Row>
    <Spin size="large" fix v-if="loading"></Spin>
    <PageCount :total="total"></PageCount>
    <EditDrawer></EditDrawer>
  </div>
</template>
<script>
import PageFilter from './Page/Filter'
import PageCell from './Page/PageCell'
import PageCount from './Page/PageCount'
import EditDrawer from '../baseComponents/EditDrawer'
export default {
  name: 'PageCenter',
  props: ['tab'],
  components: { PageFilter, PageCell, PageCount, EditDrawer },
  data() {
    return {
      isSearching: false,
      businessId: this.$route.params.businessId
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    let query = {
      businessId: this.businessId,
      pageCount: 1
    }
    this.$store.commit('setPageFilter', query)
    this.$store.dispatch('getPageListById')
  },
  computed: {
    pageList() {
      return this.$store.state.clientQuality.pageList.pageList
    },
    total() {
      return this.$store.state.clientQuality.pageList.total
    },
    loading() {
      return this.$store.state.clientQuality.isLoading
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {}
}
</script>
<style scoped>
.no-info {
  text-align: center;
  padding-bottom: 20px;
  color: #464c5b;
  font-size: medium;
  font-weight: initial;
  display: block;
}
.error-img {
  width: 200px;
}
</style>