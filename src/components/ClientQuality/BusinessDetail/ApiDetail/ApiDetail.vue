<template>
  <div class="layout">
  <BusinessHead :businessInfo="businessInfo" :apiInfo="apiInfo"></BusinessHead>
  <Menu mode="horizontal" active-name="apiInfo" class="client-menu" theme="light" @on-select="changeActive">
    <MenuItem name="apiInfo">
      <Icon type="ios-body" />
      基础信息
    </MenuItem>
  </Menu>
  <div style="text-align: left;">
    <Row class="title" v-if="this.active='apiInfo'">
      <span>接口基础信息</span>
      <div v-if="permission" style="display:contents">
        <Tooltip content="Edit" placement="top">
          <Button  class="button-edit" icon="md-create" type="text" @click="toEdit" :disabled="isEdit"></Button>
        </Tooltip>
        <Tooltip content="Delete" placement="top">
          <Button class="button-edit" icon="ios-trash" type="text" @click="deleteModal=true"></Button>
          <Modal
            v-model="deleteModal"
            title="删除接口"
            @on-ok="deleteApi">
            <p>确认删除接口{{apiInfo.apiPath}}吗？</p>
          </Modal>
        </Tooltip>
      </div>
    </Row>
    <ApiDetailShow class="detail" v-if="!isEdit" :apiDetail=apiInfo></ApiDetailShow>
    <ApiDetailEdit class="detail" v-if="isEdit" :editDetail=editInfo @setApiInfo="setApiInfo" @cancelEdit="cancelEdit"></ApiDetailEdit>
    <Row class="title">
      <span>关联页面</span>
    </Row>
    <div v-for="item in apiPageInfo" :key="item.id">
      <ApiPageInfo :productList="productList" :item=item  @refreshApiList="refreshApiList" :permission="permission"></ApiPageInfo>
    </div>
    <ApiPageProductSelect class="select" v-if="isAdd" :productList="productList" :business="this.businessInfo.id" @submit="submit" @cancel="cancelAdd"></ApiPageProductSelect>
    <Button v-if="permission" class="button" :disabled="isAdd" icon="ios-add" type="dashed" @click="addPage()">新增关联页面</Button>
  </div>
</div>
</template>
<script>
import ApiDetailShow from './ApiDetailForm'
import ApiDetailEdit from './ApiDetailEdit'
import ApiPageInfo from './ApiPageInfo'
import ApiPageProductSelect from './ApiPageProductSelect'
import BusinessHead from '../../baseComponents/BusinessHead'
import { Bus } from '@/global/bus'
export default {
  name: 'ApiDetail',
  props: ['tab', 'apiId'],
  components: { ApiDetailShow, ApiDetailEdit, ApiPageInfo, BusinessHead, ApiPageProductSelect },
  data() {
    return {
      apiInfo: {},
      editInfo: {},
      apiPageInfo: [],
      isEdit: false,
      deleteModal: false,
      isAdd: false,
      active: 'apiInfo'
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.$store.dispatch('getProductList')
    this.getApiDetail()
  },
  computed: {
    productList() {
      return this.$store.state.clientQuality.productList
    },
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    getApiDetail() {
      this.$axios({
        methods: 'get',
        params: {
          apiId: this.apiId
        },
        url: this.env.url + 'api/getApiInfo'
      }).then((res) => {
        let msg = res.data
        this.apiInfo = msg.apiDetail
        this.apiPageInfo = msg.pageList
        this.businessInfo = msg.businessInfo
        this.$store.dispatch('getPermission', {
          misId: Bus.userInfo.userLogin,
          businessId: this.businessInfo.id
        })
      })
    },
    toEdit() {
      this.editInfo.id = this.apiInfo.id
      this.editInfo.name = this.apiInfo.name
      this.editInfo.apiPath = this.apiInfo.apiPath
      this.editInfo.method = this.apiInfo.method
      this.editInfo.appKey = this.apiInfo.appKey
      this.editInfo.remark = this.apiInfo.remark
      this.editInfo.priority = this.apiInfo.priority
      this.isEdit = true
    },
    cancelEdit() {
      this.isEdit = false
    },
    refreshApiList() {
      this.getApiDetail()
    },
    setApiInfo(editInfo) {
      this.$axios({
        method: 'post',
        data: editInfo,
        url: this.env.url + 'api/apiBase/modify'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.$Notice.success({
            title: '接口修改成功！~'
          })
        } else {
          this.$Notice.success({
            title: '接口修改失败~'
          })
        }
        this.apiInfo = message.data[0]
        this.isEdit = false
      })
    },
    deleteApi() {
      this.$axios({
        method: 'delete',
        params: {
          apiId: this.apiId
        },
        url: this.env.url + 'api/apiBase/delete'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.deleteModal = false
          this.$Notice.success({
            title: '接口已删除'
          })
          this.toBusinessApiPage()
        }
      })
    },
    toBusinessApiPage() {
      this.$router.push('/client/businessDetail/' + this.businessInfo.id + '/api')
    },
    addPage() {
      this.isAdd = true
    },
    submit(editInfo) {
      let pageParam = {}
      pageParam.apiId = this.apiInfo.id
      pageParam.pageId = editInfo.pageId
      if (editInfo.productId > 0) {
        pageParam.productId = []
        pageParam.productId.push(editInfo.productId)
      }
      this.$axios({
        method: 'put',
        data: pageParam,
        url: this.env.url + 'api/page'
      }).then((res) => {
        let message = res.data
        this.isAdd = false
        this.refreshApiList()
        if (res.code === 200) {
          this.$Notice.success({
            title: message.msg
          })
        } else {
          this.$Notice.success({
            title: '接口关联失败~'
          })
        }
      }).catch(error => {
        console.log(error)
      })
    },
    relateApi(apiPageId, productId) {
      this.$axios({
        method: 'put',
        data: {
          apiPageId: apiPageId,
          productId: productId
        },
        url: this.env.url + 'api/pageProduct'
      }).then((res) => {
        this.refreshApiList()
      })
    },
    cancelAdd() {
      this.isAdd = false
    },
    changeActive(name) {
      this.active = name
    }
  }
}
</script>
<style scoped>
.button-edit {
  padding: 0px 5px 0px;
  font-size: 14px;
  vertical-align: bottom;
}

.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: bold;
  background-color: #f8f8f9;
  padding: 8px 16px;
  width: 1000;
  align-items: center;
  margin-bottom: 15px;
}
.layout {
    background: #fff;
    text-align: left;
  }

.client-menu {
  margin-bottom: 15px;
}
.button {
  margin-left: 15px;
  margin-top: 15px;
}
.select {
  margin-top: 20px;
}
.detail{
  margin-left: 20px;
}
</style>