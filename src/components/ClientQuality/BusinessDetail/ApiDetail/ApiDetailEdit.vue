<template>
  <Form ref="editDetail" :model="editDetail" :label-width="150" :rules="formRules" label-position="left" class="form">
    <FormItem label="ID">
      <strong style="float:left">{{editDetail.id}}</strong>
    </FormItem>
    <FormItem label="Path" prop="apiPath">
      <Input v-model="editDetail.apiPath"></Input>
    </FormItem>
    <FormItem label="名称" prop="name">
      <Input v-model="editDetail.name"></Input>
    </FormItem>
    <FormItem label="Method" prop="method" required>
    <Select v-model="editDetail.method" placeholder="请选择">
      <Option v-for="method in methods" :key="method" :value="method">{{ method }}</Option>
    </Select>
    </FormItem>
    <FormItem label="所属服务Appkey">
      <Input v-model="editDetail.appKey"></Input>
    </FormItem>
    <FormItem label="优先级">
      <Select v-model="editDetail.priority">
        <Option :value="1" :key="1">P1</Option>
        <Option :value="2" :key="2">P2</Option>
        <Option :value="3" :key="3">P3</Option>
      </Select>
    </FormItem>
    <FormItem label="备注信息">
      <Input v-model="editDetail.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" ></Input>
    </FormItem>
    <FormItem>
      <Button type="primary" size="small" @click="submit">Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="cancelEdit">Cancel</Button>
    </FormItem>
  </Form>
</template>
<script>
export default {
  name: 'ApiDetailEdit',
  props: ['editDetail'],
  data() {
    return {
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH'],
      formRules: {
        name: [
          {
            required: true,
            trigger: 'change',
            message: '请填写接口描述',
            validator: (rule, value, callback) => {
              if (this.editDetail.name.trim() === '') {
                return callback(new Error('请填写接口描述'))
              } else {
                callback()
              }
            }
          }
        ],
        apiPath: [
          {
            required: true,
            trigger: 'change',
            type: 'string',
            validator: (rule, value, callback) => {
              var pattern = /^\//
              if (this.editDetail.apiPath.trim() === '') {
                return callback(new Error('请填写接口Path(不包含域名)'))
              } else if (!pattern.test(this.editDetail.apiPath)) {
                return callback(new Error('接口Path需要以/开头'))
              } else {
                callback()
              }
            }
          }
        ],
        method: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.editDetail.method.trim() === '') {
                return callback(new Error('请选择接口method'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs['editDetail'].validate(async (valid) => {
        if (valid) {
          this.$emit('setApiInfo', this.editDetail)
        }
      })
    },
    cancelEdit: function () {
      this.$emit('cancelEdit')
    }
  }
}
</script>
<style scoped>
.form {
  width: 600px;
  margin-top: 20px;
}
</style>
