<template>
  <div class="page-info">
    <Row class="page-info">
      <Badge status="success" />
      <strong>{{item.pageUnit.name}}</strong>
      <Button v-if="permission" class="button" icon="ios-trash" size="small" type="text" @click="deleteApiPage(item.id)"></Button>
    </Row>
    <Row class="info">
      <Col span="2">
        <span class="sub-title">关联产品:</span>
      </Col>
      <Col span="22">
        <Tag :closable="permission" v-for="productItem in item.apiPageProductList" :key="productItem.id" color="volcano" type="border" @on-close="deleteProduct(productItem.id)">
          {{productItem.product.label}}
        </Tag>
        <div v-if="isEdit" class="select">
          <Select placeholder="新增产品" v-model="model" multiple style="width: 200px" size="small" placement="top" filterable>
            <Option v-for="item in this.productList" :value="item.id" :key="item.id">{{ item.label }}</Option>
          </Select>
          <Button type="primary" size="small" @click="addProduct">确认</Button>
          <Button size="small" @click="cancelAddProduct">取消</Button>
        </div>
        <Button v-if="!isEdit" icon="ios-add" type="dashed" size="small" @click="edit()" :disabled="!permission">Add</Button>
      </Col>
    </Row>
  </div>
</template>
<script>
export default {
  name: 'ApiPageInfo',
  props: ['item', 'productList', 'permission'],
  data() {
    return {
      apiInfo: [],
      isEdit: false,
      model: []
    }
  },
  methods: {
    deleteApiPage(apiPageId) {
      this.$axios({
        method: 'delete',
        params: {
          apiPageId: apiPageId
        },
        url: this.env.url + 'api/page'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.$Notice.success({
            title: '解除关联成功！~'
          })
          this.$emit('refreshApiList')
        }
      })
    },
    relateApi(apiPageId, productId) {
      this.$axios({
        method: 'put',
        data: {
          apiPageId: apiPageId,
          productId: productId
        },
        url: this.env.url + 'api/pageProduct'
      }).then((res) => {
        this.model = []
      }).catch(error => {
        console.log(error)
      })
    },
    addProduct() {
      for (let i = 0; i < this.model.length; i++) {
        this.relateApi(this.item.id, this.model[i])
      }
      this.$emit('refreshApiList')
      this.isEdit = false
    },
    edit() {
      this.isEdit = true
    },
    cancelAddProduct() {
      this.isEdit = false
      this.model = []
    },
    deleteProduct(id) {
      this.$axios({
        method: 'delete',
        params: {
          apiPageProductId: id
        },
        url: this.env.url + 'api/pageProduct'
      }).then((res) => {
        this.$emit('refreshApiList')
      }).catch(error => {
        console.log(error)
      })
    }
  }
}
</script>
<style scoped>
.select {
  margin-top: 8px;
  margin-bottom: 8px;
}
.info{
  margin-bottom: 8px;
  margin-top: 5px;
  margin-left: 30px;
}
.sub-title {
  font-weight: 500;
  margin-right: 20px;
  font-size: 13px;
}
.page-info{
  margin-bottom: 12px;
  margin-top: 25px;
  margin-left: 10px;
  text-align: left;
  align-items: center
}
.button{
  margin-left: 10px;
}
</style>
  