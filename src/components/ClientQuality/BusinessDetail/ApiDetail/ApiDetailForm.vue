<template>
    <Form :model="apiDetail" :label-width="150" label-position="left" class="form">
      <FormItem label="ID">
        <strong>{{apiDetail.id}}</strong>
      </FormItem>
      <FormItem label="Path">
        <span>{{apiDetail.apiPath}}</span>
      </FormItem>
      <FormItem label="名称">
        <span>{{apiDetail.name}}</span>
      </FormItem>
      <FormItem label="Method">
        <span>{{apiDetail.method}}</span>
      </FormItem>
      <FormItem label="所属服务Appkey">
        <span>{{apiDetail.appKey}}</span>
      </FormItem>
      <FormItem label="优先级">
        <Tag v-if="apiDetail.priority === 1" color="volcano">P{{ apiDetail.priority }}</Tag>
        <Tag v-else color="blue">P{{ apiDetail.priority }}</Tag>
      </FormItem>
      <FormItem label="备注信息">
        <span>{{apiDetail.remark}}</span>
      </FormItem>
    </Form>
  </template>
    
  <script>
  export default {
    name: 'ApiDetailShow',
    props: ['apiDetail'],
    data() {
      return {}
    },
    methods: {}
  }
  </script>
   
  <style scoped>
  
  </style>