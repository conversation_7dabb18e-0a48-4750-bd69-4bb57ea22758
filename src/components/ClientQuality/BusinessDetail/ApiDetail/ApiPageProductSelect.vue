<template>
  <div class="product" >
    <Form inline :label-width="100">
      <FormItem  label="选择页面" required >
        <Select style="width:300px" v-model="editInfo.pageId" filterable transfer placeholder="请选择" @on-change="change">
          <Option v-for="pageItem in pageList" :value="pageItem.id" :key="pageItem.id">{{ pageItem.name }}</Option>
        </Select>
      </FormItem>
      <FormItem > 
        <span slot="label">选择产品
          <Tooltip content="若不选择，则默认与页面相关产品关联。" placement="top">
          <Icon type="md-alert" />
          </Tooltip>
        </span>
        <Select style="width:300px" v-model="editInfo.productId" filterable transfer placeholder="请选择">
          <Option v-for="productItem in productList" :value="productItem.id" :key="productItem.id">{{ productItem.label }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button type="primary" size="small" @click="submit">确认</Button>
        <Button size="small" style="margin-left: 8px" @click="cancel">取消</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'ApiPageProductSelect',
  props: ['business', 'productList'],
  data() {
    return {
      editInfo: {
        pageId: 0,
        productId: 0
      },
      model: [],
      query: {
        online: 1,
        pageSize: 500,
        pageCount: 1,
        businessId: 0,
        isDetail: 0
      }
    }
  },
  mounted() {
    this.query.businessId = this.business
    this.$store.commit('setPageFilter', this.query)
    this.$store.dispatch('getPageListById')
  },
  computed: {
    pageList() {
      return this.$store.state.clientQuality.pageList.pageList
    },
    loading() {
      return this.$store.state.clientQuality.loading
    }
  },
  methods: {
    change() {
      this.editInfo.productId = 0
    },
    submit() {
      this.$emit('submit', this.editInfo)
    },
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>
<style scoped>
.product {
  text-align: left;
}

