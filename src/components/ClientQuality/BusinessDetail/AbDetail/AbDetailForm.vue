<template>
    <Form :model="abDetail" :label-width="150" label-position="left">
      <FormItem label="ABKey">
        <a v-if="abDetail.linkUrl" :href="abDetail.linkUrl" target="_Blank" >
          <strong>{{abDetail.abKey}}</strong>
        </a>
        <strong v-else>{{abDetail.abKey}}</strong>
      </FormItem>
      <FormItem label="实验描述">
        <span>{{abDetail.name}}</span>
      </FormItem>
      <FormItem label="实验类型">
        <Tag v-if="abDetail.type === 1" color="blue">客户端</Tag>
        <Tag v-else color="green">服务端</Tag>
      </FormItem>
      <FormItem v-if="abDetail.type === 2&&abDetail.online!==-1" label="在线状态">
        <Badge v-if="abDetail.online===1" text="ON" type="success"></Badge>
        <Badge v-else-if="abDetail.online===2" text="OFF"></Badge>
      </FormItem>
      <FormItem label="策略描述">
          <div v-for="strategyItem in abDetail.strategyList" :key="strategyItem.id">
            <Badge style="text-align:left;font-size:15px;white-space: nowrap;margin-bottom: -5px;margin-top: -5px;" 
                   status="success" :text=strategyItem.description />
          </div>
      </FormItem>
      <FormItem label="策略值">
        <Tag
          v-for="valueItem in abDetail.valueList"
          :key="valueItem.id"
          style="background-color: #f7f7f7;
                      border:1px solid #e8eaec;
                      color: #515a6e;"
          >{{ valueItem.value }}
        </Tag>
      </FormItem>
      <FormItem label="AB上线时间">
        <code class="code">{{abDetail.createTime}}</code>
      </FormItem>
      <FormItem label="备注">
        <span>{{abDetail.remark}}</span>
      </FormItem>
    </Form>
  </template>
    
  <script>
  export default {
    name: 'AbDetailForm',
    props: ['abDetail'],
    data() {
      return {}
    },
    methods: {
    }
  }
  </script>
   
  <style scoped>
  .code {
    border:1px solid #ffd591; 
    background:#fff7e6;
    color: #fa8c16!important;
    font-size: 12px;
  }
  </style>