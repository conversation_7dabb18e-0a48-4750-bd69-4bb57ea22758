<template>
  <Form ref="initialEditDetail" :model="initialEditDetail" :label-width="150" :rules="formRules" label-position="left" class="form">
    <FormItem label="ABKey" prop="abKey">
      <Input v-model="initialEditDetail.abKey" placeholder="请输入实验Key"></Input>
    </FormItem>
    <FormItem label="实验平台链接" prop="linkUrl">
      <Input v-model="initialEditDetail.linkUrl" placeholder="请输入实验平台链接"></Input>
    </FormItem>
    <FormItem label="实验描述" prop="name">
      <Input v-model="initialEditDetail.name" placeholder="请输入实验内容简要说明"></Input>
    </FormItem>
    <FormItem label="实验类型" prop="type">
      <Select v-model="initialEditDetail.type">
        <Option :value="1" :key="1">客户端AB</Option>
        <Option :value="2" :key="2">服务端AB</Option>
      </Select>
    </FormItem>
    <FormItem label="策略描述">
      <div v-for="(strategyItem, index) in filteredStrategyList "
      :key="strategyItem.id" style="display: flex; align-items: flex-start;margin-bottom: 12px;margin-top: 8px;">
          <span style="margin-right: 5px; white-space: nowrap;line-height: 20px; "  >{{'策略' + (index + 1) + ':'}}</span>
          <Input v-model="strategyItem.description" type="textarea" :autosize="{minRows: 1,maxRows: 5}" placeholder="请输入实验策略描述"></Input>
          <Icon type="md-remove-circle" style="margin-left: 12px;line-height: 20px;color: #ed1414;font-size: large; cursor: pointer;" @click="deleteStrategy(index)"/>
      </div>
      <Button style="border-color: #2d8cf0;color: #2d8cf0;" icon="ios-add" type="dashed" size="small" @click="addStrategy()">新增策略描述</Button>
    </FormItem>
    <FormItem label="策略值">
      <div style="text-align: left;">
      <Tag v-for=" (valueItem,index) in filteredValueList" 
          :key="valueItem.id" 
          :name="valueItem.value" 
          closable 
          @on-close="deleteValue(index)">
          {{ valueItem.value }}
      </Tag>
      <div v-if="isEditValue" style="width: 200px">
        <Input
          class="suffix"
          v-model="value"
          placeholder="请输入策略值"
          size="small"
        >
        <ButtonGroup size="small" slot="suffix">
          <Button type="primary" @click="confirmAddValue(value)">确定</Button>
          <Button @click="cancelEditValue()">取消</Button>
        </ButtonGroup>
        </Input>
      </div>
      <Button
        v-else
        style="border-color: #2d8cf0;color: #2d8cf0;"
        icon="ios-add"
        type="dashed"
        size="small"
        @click="addValue"
        >新增策略值</Button
      >
    </div>
    </FormItem>
    <FormItem label="AB上线时间">
        <DatePicker
          type="date"
          format="yyyy-MM-dd"
          split-panels
          v-model="initialEditDetail.createTime"
          placeholder="请选择日期"
          style="width: 200px"
        />
    </FormItem>
    <FormItem label="备注">
      <Input v-model="initialEditDetail.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" ></Input>
    </FormItem>
    <FormItem>
      <Button type="primary" size="small" @click="submit">Submit</Button>
      <Button v-if="initialEditDetail.abId" size="small" style="margin-left: 8px" @click="cancelEdit">Cancel</Button>
    </FormItem>
  </Form>
</template>
<script>

export default {
  name: 'AbDetailEdit',
  props: ['editDetail'],
  data() {
    return {
      initialEditDetail: {
        strategyList: [],
        valueList: []
      },
      isEditValue: false,
      value: '',
      formRules: {
        abKey: [
          {
            required: true,
            trigger: 'change',
            message: '请填写AB实验key',
            validator: (rule, value, callback) => {
              if (this.initialEditDetail.abKey.trim() === '') {
                return callback(new Error('请填写AB实验key'))
              } else {
                callback()
              }
            }
          }
        ],
        name: [
          {
            required: true,
            trigger: 'change',
            type: 'string',
            validator: (rule, value, callback) => {
              if (this.initialEditDetail.name.trim() === '') {
                return callback(new Error('请填写AB实验描述'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.initialEditDetail = JSON.parse(JSON.stringify(this.editDetail))
  },
  computed: {
    filteredStrategyList() {
      return this.initialEditDetail.strategyList
    },
    filteredValueList() {
      return this.initialEditDetail.valueList
    }
  },
  methods: {
    addStrategy() {
      this.initialEditDetail.strategyList.push(
        {id: null, description: null}
      )
    },
    deleteStrategy(index) {
      this.initialEditDetail.strategyList.splice(index, 1)
    },
    confirmAddValue(value) {
      if (this.value.trim() === '') {
        this.$Message.Notice('value 不可以为空')
      } else {
        this.initialEditDetail.valueList.push(
          {id: null, value: value}
        )
        this.isEditValue = false
        this.value = ''
      }
    },
    cancelEditValue() {
      this.value = ''
      this.isEditValue = false
    },
    addValue() {
      this.isEditValue = true
    },
    deleteValue(index) {
      this.initialEditDetail.valueList.splice(index, 1)
    },
    formatDate(date) {
      date = new Date(date)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
    },
    submit() {
      if (!this.initialEditDetail.createTime) {
        let date = new Date()
        this.initialEditDetail.createTime = this.formatDate(date)
      } else {
        this.initialEditDetail.createTime = this.formatDate(this.initialEditDetail.createTime)
      }
      this.initialEditDetail.strategyList = this.filteredStrategyList
      this.initialEditDetail.valueList = this.filteredValueList
      if (this.initialEditDetail.abKey && this.initialEditDetail.name) {
        this.$refs['initialEditDetail'].validate(async (valid) => {
          if (valid) {
            this.$emit('setAbInfo', this.initialEditDetail)
          }
        })
      } else {
        this.$Message.error('请完善必填信息')
      }
    },
    cancelEdit: function() {
      this.initialEditDetail = JSON.parse(JSON.stringify(this.editDetail))
      this.$emit('cancelEdit')
    }
  }
}
</script>
<style scoped>
.suffix /deep/ .ivu-input-suffix {
  width: 87px;
}
.form {
width: 600px;
margin-top: 20px;
}
</style>