<template>
  <div class='product' >
    <Form :label-width='110'>
      <FormItem label='选择页面' required >
        <Select style='width:250px' v-model='editInfo.pageId' filterable transfer placeholder='请选择' @on-change="updatePageProduct">
          <Option v-for='pageItem in pageList' :value='pageItem.id' :key='pageItem.id'>{{ pageItem.name }}</Option>
        </Select>
      </FormItem>
      <div style="display: flex;">
      <FormItem label='选择产品' required style="margin-left: 20px;"> 
        <Select style='width:230px' v-model='editInfo.pageProductIdList' multiple filterable transfer placeholder='请选择'>
          <Option v-for='pageProductItem in pageProductList' :value='pageProductItem.pageProductId' :key='pageProductItem.pageProductId'>{{ pageProductItem.label }}</Option>
        </Select>
      </FormItem>
      <FormItem required>
        <span slot="label">
          <Tooltip theme="light" placement="top">
            实验状态
            <Icon type="ios-help-circle-outline" size="13" />
            <div slot="content">
              <a href="https://km.sankuai.com/collabpage/1862957619#id-4.1%20AB%E5%AE%9E%E9%AA%8C%E6%A8%A1%E5%9E%8B%E4%BF%A1%E6%81%AF%E8%AF%B4%E6%98%8E">实验状态说明参考文档 </a>
            </div>
          </Tooltip>
        </span>
        <Select style='width:150px' v-model='editInfo.status' filterable transfer placeholder='请选择'>
          <Option :value=1 :key=1>实验中</Option>
          <Option :value=2 :key=2>固定策略</Option>
          <Option :value=3 :key=3>失效</Option>
        </Select>
      </FormItem>
      <FormItem label='实验策略' required>
        <RadioGroup vertical v-model="fixedStrategyValueId">
          <Radio 
            v-for="(strategyItem,index) in editInfo.strategyList" 
            :key="index" 
            :label="strategyItem.avId" 
            :disabled="editInfo.status === 1 || editInfo.status===0 || strategyItem.avId===null"
          >
            <Select class="ivu-select-placeholder" style="width:200px" v-model="strategyItem.avId" filterable transfer placeholder="请选择策略值">
              <Option v-for="value in valueList" :value="value.id" :key="value.id" v-if="!isValueSelected(value.id,index)">{{ value.value }}</Option>
            </Select>
            <Select class="ivu-select-placeholder" style="width:200px" v-model="strategyItem.asId" filterable transfer placeholder="请选择策略描述">
              <Option v-for="strategy in strategyList" :value="strategy.id" :key="strategy.id">{{ strategy.description }}</Option>
            </Select>
            <Icon type="md-remove-circle" style="margin-left: 12px;line-height: 20px;color: #ed4014;font-size: large; cursor: pointer;" @click.stop="deleteStrategy(index)"/>
          </Radio>
          <Button style="border-color: #2d8cf0;color: #2d8cf0;margin-left: 22px;" icon="ios-add" type="dashed" size="small" @click="addStrategy()">新增策略</Button>
        </RadioGroup>
      </FormItem>
     </div>
    <FormItem>
      <div style="margin-left: -50px;">
      <Button type='primary' size='small' @click='submitAbPageProduct'>Submit</Button>
      <Button size='small' style='margin-left: 8px' @click='cancel'>Cancel</Button>
      </div>
    </FormItem>
    </Form>
  </div>
</template>
  
<script>
export default {
  name: 'AbPageProductSelect',
  props: ['pageList', 'strategyList', 'valueList'],
  data() {
    return {
      fixedStrategyValueId: -1,
      pageProductList: [],
      productList: [],
      pageProductInfoList: [],
      editInfo: {
        pageId: 0,
        pageProductIdList: [],
        status: 0,
        strategyList: [
          {
            avId: null,
            asId: null
          }
        ]
      },
      query: {
        online: 1,
        pageSize: 500,
        pageCount: 1,
        isDetail: 0
      }
    }
  },
  mounted() {
  },
  computed: {
    loading() {
      return this.$store.state.clientQuality.loading
    }
  },
  watch: {
    'editInfo.status': function(newVal) {
      if (newVal === 1) {
        this.fixedStrategyValueId = -1
      }
    }
  },
  methods: {
    isValueSelected(avId, currentIndex) {
      return this.editInfo.strategyList.some(
        (strategyItem, index) =>
          strategyItem.avId === avId && index !== currentIndex
      )
    },
    addStrategy() {
      if (this.editInfo.strategyList.length < this.valueList.length) {
        this.editInfo.strategyList.push({
          avId: null,
          asId: null
        })
      } else {
        this.$message.warning('已达到当前AB实验策略数量最大值，无法继续添加')
      }
    },
    deleteStrategy(index) {
      if (this.editInfo.strategyList.length > 1) {
        let deletedId = this.editInfo.strategyList[index].avId
        if (this.fixedStrategyValueId === deletedId) {
          this.fixedStrategyValueId = -1
        }
        this.editInfo.strategyList.splice(index, 1)
      } else {
        this.$message.warning('至少需要保留一个策略')
      }
    },
    async updatePageProduct() {
      this.editInfo.pageProductIdList = []
      if (this.editInfo.pageId) {
        await this.getPageProductListByPageId(this.editInfo.pageId)
        let pageProductList = []
        for (const item of this.productList) {
          let pageProductItem = {}
          pageProductItem['pageProductId'] = item.id
          pageProductItem['productId'] = item.productId
          pageProductItem['label'] = item.product.label + '-' + item.implement
          pageProductList.push(pageProductItem)
        }
        this.pageProductList = pageProductList
      } else {
        this.pageProductList = []
      }
    },
    getPageProductListByPageId(pageId) {
      return new Promise((resolve, reject) => {
        this.$axios({
          method: 'get',
          params: {
            pageId: pageId
          },
          url: this.env.url + 'page/getPageInfo'
        }).then((res) => {
          this.productList = res.data.pageInfo.productList
          resolve()
        }).catch((error) => {
          reject(error)
        })
      })
    },
    updateStrategyList() {
      this.editInfo.strategyList.forEach((item) => {
        if (this.editInfo.status === 1) {
          item.status = 1
          item.result = -1
        } else if (this.editInfo.status === 2) {
          item.status = item.avId === this.fixedStrategyValueId ? 2 : 3
          item.result = item.avId === this.fixedStrategyValueId ? 1 : -1
        } else if (this.editInfo.status === 3) {
          item.status = 3
          item.result = item.avId === this.fixedStrategyValueId ? 1 : -1
        }
      })
    },
    constructPageProductInfoList() {
      this.editInfo.pageProductIdList.forEach((item) => {
        let pageProductInfo = {}
        pageProductInfo['pageProductId'] = item
        pageProductInfo['status'] = this.editInfo.status
        pageProductInfo['strategyList'] = this.editInfo.strategyList
        this.pageProductInfoList.push(pageProductInfo)
      })
    },
    isFormValid() {
      if (!this.editInfo.pageId || this.editInfo.pageProductIdList.length === 0 || !this.editInfo.status || this.editInfo.strategyList.length === 0) {
        this.$Message.error('请完善必填信息')
        return false
      }
      for (let strategyItem of this.editInfo.strategyList) {
        if (!strategyItem.avId || !strategyItem.asId) {
          this.$Message.error('请完善实验策略信息')
          return false
        }
      }
      if ((this.editInfo.status === 2 || this.editInfo.status === 3) && (this.fixedStrategyValueId === -1)) {
        this.$Message.error('必须选择一个策略为指定策略~')
        return false
      }
      return true
    },
    submitAbPageProduct() {
      if (!this.isFormValid()) {
        return
      }
      this.updateStrategyList()
      this.constructPageProductInfoList()
      this.$emit('submitAbPageProduct', this.pageProductInfoList, this.editInfo.pageId, true)
    },
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>
<style scoped>
.product {
  text-align: left;
}
.ivu-select-placeholder, .ivu-input-placeholder {
  font-weight: normal;
}
</style>
