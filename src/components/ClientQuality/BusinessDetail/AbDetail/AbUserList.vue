<template>
    <div>
      <Row class="user-cell">
        <Col span="2">
        <strong>PM 负责人:</strong>
        </Col>
        <Col span="22">
        <AbMember :abId="this.abId" :userList="pm" role="PM" :permission="permission" @updateUserList="updateUserList"></AbMember>
        </Col>
      </Row>
      <Row class="user-cell">
        <Col span="2">
        <strong>RD 负责人:</strong>
        </Col>
        <Col span="22">
        <AbMember :abId="this.abId" :userList="rd" role="RD" :permission="permission" @updateUserList="updateUserList"></AbMember>
        </Col>
      </Row>
      <Row class="user-cell">
        <Col span="2">
        <strong>QA 负责人:</strong>
        </Col>
        <Col span="22">
        <AbMember :abId="this.abId" :userList="qa" role="QA" :permission="permission" @updateUserList="updateUserList"></AbMember>
        </Col>
      </Row>
    </div>
  </template>
  <script>
  import AbMember from './AbMember'
  export default {
    name: 'AbUserList',
    props: ['abId', 'AbUsersPM', 'AbUsersRD', 'AbUsersQA', 'permission'],
    components: { AbMember },
    data() {
      return {
        pm: this.AbUsersPM ? [...this.AbUsersPM] : [],
        rd: this.AbUsersRD ? [...this.AbUsersRD] : [],
        qa: this.AbUsersQA ? [...this.AbUsersQA] : []
      }
    },
    watch: {
      AbUsersPM(newVal) {
        this.pm = [...newVal]
      },
      AbUsersRD(newVal) {
        this.rd = [...newVal]
      },
      AbUsersQA(newVal) {
        this.qa = [...newVal]
      }
    },
    methods: {
      updateUserList(abUserInfo) {
        this.pm = abUserInfo.pm
        this.rd = abUserInfo.rd
        this.qa = abUserInfo.qa
      }
    }
  }
  </script>
  <style scoped>
  .user-cell {
    margin-bottom: 20px;
    margin-left: 5px;
    margin-right: 20px;
  }
  </style>