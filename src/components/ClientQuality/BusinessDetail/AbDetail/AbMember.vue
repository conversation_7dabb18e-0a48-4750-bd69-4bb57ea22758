<template>
    <div style="text-align: left;">
      <Tag
        v-for="misId in userList"
        :key="misId"
        :name="misId"
        type="border"
        :closable="permission"
        @on-close="deleteUserById(misId,role)"
        >{{ misId }}</Tag
      >
      <div v-if="isEdit" style="width: 200px">
        <Input
          class="suffix"
          v-model="misId"
          placeholder="请输入MIS ID"
          size="small"
        >
        <ButtonGroup size="small" slot="suffix">
          <Button type="primary" @click="addUser(misId,role)">确定</Button>
          <Button @click="cancelEdit()">取消</Button>
        </ButtonGroup>
        </Input>
      </div>
      <Button
        v-else-if="permission"
        icon="ios-add"
        type="dashed"
        :disabled="!permission"
        size="small"
        @click="add"
        >Add</Button
      >
    </div>
  </template>
  <script>
  /* eslint-disable */
  export default {
    name: "Ab<PERSON><PERSON><PERSON>",
    props: ['abId', 'userList', 'role','permission'],
    data() {
      return {
        isEdit: false,
        misId: '',
        params: {
          abId: this.abId,
          name: '',
          role: ''
        }
      };
    },
    computed: {
    },
    methods: {
      async deleteUserById(misId,role) {
        this.params.name = misId
        this.params.role = role
        await this.$store.dispatch('deleteUserById', this.params)
        let message = this.$store.state.clientQuality.abUserInfo
        if (message.code === 200) {
          this.$Message.success("删除成功！~");
          this.$emit('updateUserList', message.abUserInfo, role);
        } else {
          this.$Message.error("删除失败！");
        }
      },
      add() {
        this.isEdit = true
      },
      cancelEdit() {
        this.misId = ''
        this.params = {
          abId: this.abId,
          qaName: '',
          rdName: '',
          pmName: ''
        }
        this.isEdit = false
      },
      async addUser(misId,role) {
        if (this.misId.trim() === "") {
          this.$Message.error("MIS ID 不可以为空");
        } else {
          this.params.name = misId
          this.params.role = role
          await this.$store.dispatch('addUser', this.params)
          let message = this.$store.state.clientQuality.abUserInfo
          if (message.code === 200) {
            this.$Message.success("保存成功！~");
            this.isEdit = false;
            this.misId = "";
            this.$emit('updateUserList', message.abUserInfo,role);
          } else {
            this.$Message.error("保存失败！");
          }
        }
      },
    },
  };
  </script>
  <style scoped>
  .suffix /deep/ .ivu-input-suffix {
    width: 80px;
  }
  .ivu-btn-small{
    font-size: 12px;
  }
  .ivu-btn-group-small>.ivu-btn{
    font-size: 12px;
  }
  </style>