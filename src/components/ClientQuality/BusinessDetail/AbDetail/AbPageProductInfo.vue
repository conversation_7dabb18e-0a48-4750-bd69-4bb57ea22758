<template>
  <Table v-if="!isEdit && mergedData.length>0" :columns="columns" :data="mergedData" :show-header="false" :disabled-hover="true" :span-method="handleSpan" >
    <template slot-scope="{ row }" slot="pageName" >
      <span class="page-Name">{{pageName}}</span>
      <span style="display: inline-block;"></span>
      <Button v-if="permission" icon="md-create" size="small" type="primary" ghost @click="edit"></Button>
    </template>
    <template slot-scope="{ row }" slot="statusAndLabels">
      <Row style="display: flex; align-items: center;">
        <Col span="5">
            <Tag v-if="showStatus(row) === 1" color="green">实验中</Tag>
            <Tag v-else-if="showStatus(row) === 2" color="volcano">固定策略</Tag>
            <Tag v-else style="background-color: #f7f7f7;border:1px solid #e8eaec; color: ;#515a6e">失效</Tag>
        </Col>
        <Col span="19">
          <div v-for="(label, index) in row.pageProductLabels" :key="index" >
            <Badge class="productItem-info" status="success" :text=label />
          </div>
        </Col>
      </Row>
    </template>
    <template slot-scope="{ row }" slot="strategyList">
      <div v-for="(strategy, index) in row.strategyList" :key="index" class="strategy-list">
        <Tooltip v-if="strategy.result===1" theme="light" placement="right" max-width="300">
          <template slot="content">
            <span style="color: #1890ff!important;">{{ strategy.value }}</span>
          </template>
          <Tag  class='strategy-value'
                :style="{ width: getMaxStrategyValueLength(row.strategyList) > 5 ? '140px' : '60px'}"
                color="blue"
          >{{ strategy.value }}</Tag>
          <Badge dot v-if="!strategy.asId || strategy.asId==null" style="margin-bottom: 12px; margin-right: 2px;"></Badge>
        </Tooltip>
        <Tooltip v-else theme="light" placement="right" max-width="300">
          <template slot="content">
            <span style="color: #515a6e">{{ strategy.value }}</span>
          </template>
          <Tag class='strategy-value'
               :style="{ width: getMaxStrategyValueLength(row.strategyList) > 5 ? '140px' : '60px'}"
               style="background-color: #f7f7f7;
                      border:1px solid #e8eaec;
                      color: #515a6e;"
          >{{ strategy.value }}</Tag>
          <Badge dot v-if="!strategy.asId || strategy.asId==null" style="margin-bottom: 12px; margin-right: 2px;"></Badge>
        </Tooltip>
        <span style="font-size: smaller;">{{ strategy.description }}</span>
      </div>
    </template>
  </Table>
  <Form v-else inline :label-width='110'>
    <FormItem>
      <span class="page-Name" style="margin-left: -95px;">{{pageName}}</span>
    </FormItem>
    <div v-for="(item, index) in editInfo" :key="index" >
      <FormItem label='选择产品' required > 
        <Select style='width:200px' v-model='item.pageProductId' filterable transfer placeholder='请选择'>
          <Option v-for='pageProductItem in pageProductList' :value='pageProductItem.pageProductId' :key='pageProductItem.pageProductId'>{{ pageProductItem.label }}</Option>
        </Select>
      </FormItem>
      <FormItem required>
        <span slot="label">
          <Tooltip theme="light" placement="top">
            实验状态
            <Icon type="ios-help-circle-outline" size="13" />
            <div slot="content">
              <a href="https://km.sankuai.com/collabpage/1862957619#id-4.1%20AB%E5%AE%9E%E9%AA%8C%E6%A8%A1%E5%9E%8B%E4%BF%A1%E6%81%AF%E8%AF%B4%E6%98%8E">实验状态说明参考文档 </a>
            </div>
          </Tooltip>
        </span>
        <Select style='width:100px' v-model='item.status' filterable transfer placeholder='请选择' >
          <Option :value=1 :key=1>实验中</Option>
          <Option :value=2 :key=2>固定策略</Option>
          <Option :value=3 :key=3>失效</Option>
        </Select>
      </FormItem>
      <FormItem required>
        <span slot="label" style="display:inline-flex;">实验策略
        </span>
        <RadioGroup vertical v-model="item.fixedStrategyValueId">
          <Radio 
            v-for="(strategyItem, strategyItemIndex) in item.strategyList" 
            :key="strategyItemIndex" 
            :label="strategyItem.avId" 
            :disabled="item.status === 1 || strategyItem.avId===null"
          >
            <Select class="ivu-select-placeholder" style="width:200px" v-model="strategyItem.avId" filterable transfer placeholder="请选择策略值">
              <Option v-for="value in valueList" :value="value.id" :key="value.id" v-if="!isValueSelected(value.id,strategyItemIndex,index)">{{ value.value }}</Option>
            </Select>
            <Select class="ivu-select-placeholder" style="width:200px" v-model="strategyItem.asId" filterable transfer placeholder="请选择策略描述">
              <Option v-for="strategy in strategyList" :value="strategy.id" :key="strategy.id">{{ strategy.description }}</Option>
            </Select>
            <Icon type="md-remove-circle" style="margin-left: 12px;line-height: 20px;color: #ed4014;font-size: large; cursor: pointer;" @click.stop="deleteStrategy(strategyItemIndex,index)"/>
          </Radio>
          <Button style="border-color: #2d8cf0;color: #2d8cf0;margin-left: 22px;" icon="ios-add" type="dashed" size="small" @click="addStrategy(index)">新增策略</Button>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <Button icon="ios-trash" size="small" type="primary" ghost @click="deletePageProduct(index)"></Button>
      </FormItem>
    </div>
    <FormItem>
      <div style="margin-left: -75px;">
      <Button type='primary' size='small' @click='submitAbPageProduct'>Submit</Button>
      <Button size="small" style="margin-left: 8px" @click="cancelEdit">Cancel</Button>
      </div>
    </FormItem>
  </Form>
</template>
<script>
export default {
  name: 'AbPageProductInfo',
  props: ['abPageProductInfo', 'permission', 'strategyList', 'valueList'],
  data() {
    return {
      isEdit: false,
      pageId: 0,
      pageName: '',
      productList: [],
      editInfo: [],
      columns: [
        {
          title: '页面',
          width: 350,
          key: 'pageName',
          slot: 'pageName'
        },
        {
          title: '状态和关联产品',
          width: 450,
          key: 'statusAndLabels',
          slot: 'statusAndLabels'
        },
        {
          title: '策略列表',
          key: 'strategyList',
          slot: 'strategyList'
        }
      ]
    }
  },
  mounted() {
    this.pageId = this.abPageProductInfo.pageId
    this.pageName = this.abPageProductInfo.pageName
    this.editInfo = JSON.parse(JSON.stringify(this.abPageProductInfo.pageProductInfo))
    this.initStatus()
    this.getPageProductListByPageId(this.pageId)
  },
  computed: {
    mergedData() {
      const mergedResult = []
      const mergedMap = new Map()
      for (const item of this.abPageProductInfo.pageProductInfo) {
        const key = item.strategyList.map((subItem) => subItem.avId + subItem.asId + subItem.result + subItem.status).join('-')
        if (mergedMap.has(key)) {
          mergedMap.get(key).pageProductLabels.push(item.pageProductLabel)
        } else {
          const newItem = {
            pageProductLabels: [item.pageProductLabel],
            strategyList: item.strategyList
          }
          mergedResult.push(newItem)
          mergedMap.set(key, newItem)
        }
      }
      return mergedResult
    },
    pageProductList() {
      let pageProductList = []
      for (const item of this.productList) {
        let pageProductItem = {}
        pageProductItem['pageProductId'] = item.id
        pageProductItem['productId'] = item.productId
        pageProductItem['label'] = item.product.label + '-' + item.implement
        pageProductList.push(pageProductItem)
      }
      return pageProductList
    }
  },
  watch: {
    'editInfo': {
      handler(newValue) {
        newValue.forEach((item, index) => {
          if ('status' in item) {
            this.$watch(() => item.status, (newStatus) => {
              if (newStatus === 1) {
                this.$set(this.editInfo[index], 'fixedStrategyValueId', -1)
              }
            })
          }
        })
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSpan ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return [this.mergedData.length, 1]
        } else {
          return [0, 0]
        }
      }
    },
    initStatus() {
      for (let index = 0; index < this.editInfo.length; index++) {
        let item = this.editInfo[index]
        this.$set(this.editInfo, index, {
          ...item,
          status: this.showStatus(item),
          fixedStrategyValueId: (item.strategyList.find(strategyItem => strategyItem.result === 1) || {}).avId
        })
      }
    },
    showStatus(item) {
      let status = 1
      for (let i = 0; i < item.strategyList.length; i++) {
        if (item.strategyList[i].status === 2) {
          status = 2
          break
        } else if (item.strategyList[i].status === 3) {
          status = 3
        }
      }
      return status
    },
    getMaxStrategyValueLength(strategyList) {
      const lengths = strategyList.map(strategy => strategy.value ? strategy.value.length : 0)
      return lengths.length > 0 ? Math.max(...lengths) : 0
    },
    isValueSelected(avId, currentIndex, index) {
      return this.editInfo[index].strategyList.some(
        (strategyItem, index) =>
          strategyItem.avId === avId && index !== currentIndex
      )
    },
    addStrategy(index) {
      if (this.editInfo[index].strategyList.length < this.valueList.length) {
        this.editInfo[index].strategyList.push({
          avId: null,
          asId: null
        })
      } else {
        this.$message.warning('已达到当前AB实验策略数量最大值，无法继续添加')
      }
    },
    deleteStrategy(currentIndex, index) {
      if (this.editInfo[index].strategyList.length > 1) {
        let deletedId = this.editInfo[index].strategyList[currentIndex].avId
        if (this.editInfo[index].fixedStrategyValueId === deletedId) {
          this.editInfo[index].fixedStrategyValueId = -1
        }
        this.editInfo[index].strategyList.splice(currentIndex, 1)
      } else {
        this.$message.warning('至少需要保留一个策略')
      }
    },
    async getPageProductListByPageId(pageId) {
      return new Promise((resolve, reject) => {
        this.$axios({
          method: 'get',
          params: {
            pageId: pageId
          },
          url: this.env.url + 'page/getPageInfo'
        }).then((res) => {
          this.productList = res.data.pageInfo.productList
          resolve()
        }).catch((error) => {
          reject(error)
        })
      })
    },
    edit() {
      this.isEdit = true
      this.editInfo = JSON.parse(JSON.stringify(this.abPageProductInfo.pageProductInfo))
      this.initStatus()
    },
    updateStrategyList() {
      this.editInfo.forEach(item => {
        item.strategyList.forEach((strategyItem, strategyItemIndex) => {
          if (item.status === 1) {
            strategyItem.status = 1
            strategyItem.result = -1
          } else if (item.status === 2) {
            strategyItem.status = strategyItem.avId === item.fixedStrategyValueId ? 2 : 3
            strategyItem.result = strategyItem.avId === item.fixedStrategyValueId ? 1 : -1
          } else if (item.status === 3) {
            strategyItem.status = 3
            strategyItem.result = strategyItem.avId === item.fixedStrategyValueId ? 1 : -1
          }
        })
        delete item.status
        delete item.fixedStrategyValueId
      })
    },
    isFormValid() {
      for (let item of this.editInfo) {
        if (item.pageProductId && item.status && item.strategyList.length > 0) {
          for (let strategyItem of item.strategyList) {
            if (!strategyItem.avId || !strategyItem.asId) {
              this.$Message.error('请完善实验策略信息')
              return false
            }
          }
          if ((item.status === 2 || item.status === 3) && (item.fixedStrategyValueId === -1 || item.fixedStrategyValueId === undefined)) {
            this.$Message.error(`${item.pageProductLabel}必须选择一个策略为指定策略~`)
            return false
          }
        }
      }
      return true
    },
    submitAbPageProduct() {
      if (!this.isFormValid()) {
        return
      }
      this.updateStrategyList()
      this.$emit('submitAbPageProduct', this.editInfo, this.pageId, false)
      this.isEdit = false
    },
    cancelEdit() {
      this.isEdit = false
      this.editInfo = JSON.parse(JSON.stringify(this.abPageProductInfo.pageProductInfo))
      this.initStatus()
    },
    deletePageProduct(index) {
      if (this.editInfo.length === 1) {
        this.$Modal.confirm({
          title: '删除产品与AB实验关联',
          content: '删除该产品将同时删除该页面与AB实验关联，是否确认删除',
          onOk: () => {
            this.editInfo.splice(index, 1)
            this.submitAbPageProduct()
          }
        })
      } else {
        this.editInfo.splice(index, 1)
      }
    }
  }
}
</script>
<style scoped>
.page-Name{
  font-size: 15px;
  color: #464c5b;
  font-weight:bolder;
  overflow:hidden;
  white-space: nowrap;
  text-overflow:ellipsis
}

.productItem-info{
  margin-top: 5px;
  margin-bottom: 5px;
  align-items: center
}
.strategy-list{
  margin-top: 5px;
  margin-bottom: 3px;
}
.strategy-value{
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ivu-select-placeholder, .ivu-input-placeholder {
  font-weight: normal;
}
</style>