<template>
    <div class="layout">
    <BusinessHead :businessInfo="businessInfo" :abInfo="abInfo"></BusinessHead>
    <Menu mode="horizontal" active-name="abInfo" class="client-menu" theme="light" @on-select="changeActive">
      <MenuItem name="abInfo">
        <Icon type="ios-body" />
        基础信息
      </MenuItem>
    </Menu>
    <div style="text-align: left;">
    <Row class="title" v-if="this.active='abInfo'">
      <span>AB实验基础信息</span>
      <div v-if="permission && abId!=='-1'" style="display:contents">
        <Tooltip content="Edit" placement="top">
          <Button  class="button-edit" icon="md-create" type="text" @click="toEdit" :disabled="isEdit"></Button>
        </Tooltip>
        <Tooltip v-if="abInfo.online===1&&abInfo.type===2" content="OFF Line" placement="top">
          <Button class="button-edit" icon="md-remove-circle" type="text" @click="serverAbOffline"></Button>
        </Tooltip>
        <Tooltip v-if="abInfo.online===2&&abInfo.type===2" content="ON Line" placement="top">
          <Button class="button-edit" icon="md-navigate" type="text" @click="serverAbOnline"></Button>
        </Tooltip>
        <Tooltip v-if="abInfo.online!==-1" content="Delete" placement="top">
          <Button class="button-edit" icon="ios-trash" type="text" @click="deleteModal=true"></Button>
          <Modal
            v-model="deleteModal"
            title="删除AB实验"
            @on-ok="deleteAb">
            <p>确认删除AB实验{{abInfo.abKey}}吗？</p>
          </Modal>
        </Tooltip>
      </div>
    </Row>
    <AbDetailForm class="detail" v-if="!isEdit" :abDetail=abInfo></AbDetailForm>
    <AbDetailEdit class="detail" v-if="isEdit" :editDetail=editInfo @setAbInfo="setAbInfo" @cancelEdit="cancelEdit"></AbDetailEdit>
    <Row class="title">
      <span>关联页面及产品</span>
      <Poptip trigger="hover" content="一键将所有关联页面与产品状态变更为失效" placement="right-start">
        <Button class="refresh" icon="md-refresh" type="info" size="small" ghost style="margin-left: 10px;" @click="showSetFixedStrategy"></Button>
        <Modal v-model="setFixedStrategyModal" 
               title="选择AB实验的全量策略"
               @on-ok="submitSetFixedStrategy">
          <Select v-model="selectedValueId">
            <Option v-for="value in valueList" :value="value.id" :key="value.id">{{ value.value }}</Option>
          </Select>
        </Modal>
      </Poptip>
    </Row>
    <div v-for="abPageProductInfo in abPageProductInfoList" :key="abPageProductInfo.pageId">
      <AbPageProductInfo :abPageProductInfo=abPageProductInfo :permission="permission" :strategyList=strategyList :valueList=valueList @submitAbPageProduct="submitAbPageProduct"></AbPageProductInfo>
    </div>
    <AbPageProductSelect class="select" v-if="isAdd" :pageList=pageList :strategyList=strategyList :valueList=valueList @submitAbPageProduct="submitAbPageProduct" @cancel="cancelAdd"></AbPageProductSelect>
    <Button v-if="permission" class="button" :disabled="isAdd||abId==='-1'" icon="ios-add" type="dashed" @click="addPageProduct()">新增关联</Button>
    </div>
    <Row class="title">
      <span>实验相关负责人</span>
    </Row>
    <AbUserList class="form-position" v-if="abId!=='-1'" :abId="this.abId" :businessId="this.businessId" :AbUsersPM="abInfo.pm" :AbUsersRD="abInfo.rd" :AbUsersQA="abInfo.qa" :permission="permission"></AbUserList>
    </div>
</template>

<script>
import BusinessHead from '../../baseComponents/BusinessHead'
import AbDetailEdit from './AbDetailEdit'
import AbDetailForm from './AbDetailForm'
import AbPageProductInfo from './AbPageProductInfo'
import AbPageProductSelect from './AbPageProductSelect'
import AbUserList from './AbUserList'
import { Bus } from '@/global/bus'
export default {
  name: 'AbDetail',
  props: ['tab', 'abId'],
  components: {BusinessHead, AbDetailForm, AbDetailEdit, AbPageProductInfo, AbPageProductSelect, AbUserList},
  data() {
    return {
      businessId: this.$route.query.businessId,
      editInfo: {
        strategyList: [],
        valueList: []
      },
      isEdit: false,
      setFixedStrategyModal: false,
      selectedValueId: null,
      deleteModal: false,
      isAdd: false
    }
  },
  created() {
    this.$store.dispatch('getPermission', {
      misId: Bus.userInfo.userLogin,
      businessId: this.businessId
    })
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.$store.dispatch('getBusinessById', this.businessId)
    let query = {
      businessId: this.businessId,
      online: 1,
      pageSize: 500,
      pageCount: 1,
      isDetail: 0
    }
    this.$store.commit('setPageFilter', query)
    this.$store.dispatch('getPageListById')
    if (this.abId !== '-1') {
      this.$store.dispatch('getAbDetail', this.abId)
    } else {
      this.isEdit = true
    }
  },
  computed: {
    abInfo() {
      let abInfo = this.$store.state.clientQuality.abInfo.abInfo
      if (abInfo) {
        let newAbInfo = {...abInfo, createTime: this.formatDate(abInfo.createTime)}
        return newAbInfo
      }
      return {}
    },
    abPageProductInfoList() {
      let abInfo = this.$store.state.clientQuality.abInfo.abInfo
      if (abInfo && abInfo.abPageProductList) {
        return this.mergedAbPageProductInfoByPage(abInfo.abPageProductList)
      }
      return []
    },
    strategyList() {
      let abInfo = this.$store.state.clientQuality.abInfo.abInfo
      if (abInfo && abInfo.strategyList) {
        return abInfo.strategyList
      }
      return []
    },
    valueList() {
      let abInfo = this.$store.state.clientQuality.abInfo.abInfo
      if (abInfo && abInfo.valueList) {
        return abInfo.valueList
      }
      return []
    },
    businessInfo() {
      return this.$store.state.clientQuality.currentBusiness
    },
    pageList() {
      return this.$store.state.clientQuality.pageList.pageList
    },
    permission() {
      let permission =
      this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    formatDate (date) {
      date = new Date(date)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
    },
    async deleteAb() {
      await this.$store.dispatch('deleteAb', this.abId)
      this.toBusinessAbList()
    },
    toBusinessAbList() {
      this.$router.push('/client/businessDetail/' + this.businessId + '/ab')
    },
    mergedAbPageProductInfoByPage(abPageProductList) {
      let pageInfoMap = new Map()
      for (const item of abPageProductList) {
        if (!pageInfoMap.has(item.pageId)) {
          pageInfoMap.set(item.pageId,
            {
              pageId: item.pageId,
              pageName: item.pageName,
              pageProductInfo: []
            }
          )
        }
        let pageProductInfo = pageInfoMap.get(item.pageId).pageProductInfo
        let productInfo = pageProductInfo.find((info) => info.productId === item.productId && info.pageProductId === item.pageProductId)
        if (!productInfo) {
          productInfo = {
            productId: item.productId,
            productLabel: item.productLabel,
            pageProductId: item.pageProductId,
            pageProductLabel: item.pageProductLabel,
            strategyList: []
          }
          pageProductInfo.push(productInfo)
        }
        productInfo.strategyList.push({
          abPageProductId: item.abPageProductId,
          asId: item.asId,
          avId: item.avId,
          value: item.value,
          description: item.description,
          status: item.status,
          result: item.result
        })
      }
      return Array.from(pageInfoMap.values())
    },
    toEdit() {
      if (this.abId !== '-1') {
        this.editInfo.abId = this.abId
      }
      this.editInfo.abKey = this.abInfo.abKey
      this.editInfo.linkUrl = this.abInfo.linkUrl ? this.abInfo.linkUrl : ''
      this.editInfo.name = this.abInfo.name
      this.editInfo.type = this.abInfo.type
      this.editInfo.strategyList = this.abInfo.strategyList ? this.abInfo.strategyList : []
      this.editInfo.valueList = this.abInfo.valueList ? this.abInfo.valueList : []
      this.editInfo.remark = this.abInfo.remark
      this.editInfo.createTime = this.abInfo.createTime
      this.isEdit = true
    },
    cancelEdit() {
      this.isEdit = false
    },
    checkStrategyBeforeUpdateServerAbStatus(abPageProductInfoList) {
      for (let i = 0; i < abPageProductInfoList.length; i++) {
        let pageProductInfo = abPageProductInfoList[i].pageProductInfo
        for (let j = 0; j < pageProductInfo.length; j++) {
          let strategyList = pageProductInfo[j].strategyList
          if (!strategyList.some(item => item.result === 1)) {
            return false
          }
        }
      }
      return true
    },
    async updateServerAbStatus(online) {
      if (online === 2 && this.abPageProductInfoList.length > 0) {
        let result = this.checkStrategyBeforeUpdateServerAbStatus(this.abPageProductInfoList)
        console.log(result)
        if (!result) {
          this.$Message.error('操作服务端AB下线，关联页面与产品中每个产品必须选择一个策略为指定策略~')
          return
        }
      }
      let editInfo = {
        abId: this.abId,
        abKey: this.abInfo.abKey,
        online: online
      }
      await this.setAbInfo(editInfo)
    },
    async serverAbOffline() {
      await this.updateServerAbStatus(2)
    },
    async serverAbOnline() {
      await this.updateServerAbStatus(1)
    },
    async setAbInfo(editInfo) {
      let data = {...editInfo, businessId: this.businessId}
      await this.$store.dispatch('setAbInfo', data)
      let responseCode = this.$store.state.clientQuality.abInfo.code
      if (responseCode === 200) {
        this.$Notice.success({
          title: 'AB实验信息基础修改成功！~'
        })
        if (!editInfo.abId) {
          this.$router.push({ path: `/client/detail/ab/${this.abInfo.id}`, query: { businessId: this.businessId } })
        }
        this.isEdit = false
      } else {
        this.isEdit = true
        let message = this.$store.state.clientQuality.abInfo.message
        if (message) {
          this.$Message.error(message)
        } else {
          this.$Message.error('AB实验信息基础修改失败~')
        }
      }
    },
    addPageProduct() {
      this.isAdd = true
    },
    cancelAdd() {
      this.isAdd = false
    },
    showSetFixedStrategy() {
      this.setFixedStrategyModal = true
    },
    submitSetFixedStrategy() {
      this.setFixedStrategyModal = false
      let abPageProductMap = {}
      for (let item of this.abPageProductInfoList) {
        for (let productInfo of item.pageProductInfo) {
          abPageProductMap[productInfo.pageProductId] = productInfo.strategyList.map(strategy => ({
            abPageProductId: strategy.abPageProductId,
            avId: strategy.avId
          }))
        }
      }
      this.setFixedStrategy(abPageProductMap)
    },
    async setFixedStrategy(abPageProductMap) {
      let data = {
        abId: this.abId,
        avId: this.selectedValueId,
        abPageProductMap: abPageProductMap
      }
      await this.$store.dispatch('setFixedStrategy', data)
      let responseCode = this.$store.state.clientQuality.abInfo.code
      if (responseCode === 200) {
        this.$Notice.success({
          title: `失效状态更新成功~`
        })
      } else {
        this.$Message.error(`失效状态更新失败~`)
      }
    },
    async submitAbPageProduct(editInfo, pageId, isAdd) {
      let data = {
        abId: this.abId,
        businessId: this.businessId,
        pageId: pageId,
        pageProductInfoList: editInfo
      }
      if (isAdd) {
        await this.$store.dispatch('addAbPageProduct', data)
      } else {
        await this.$store.dispatch('updateAbPageProduct', data)
      }
      this.isAdd = false
      let responseCode = this.$store.state.clientQuality.abInfo.code
      if (responseCode === 200) {
        this.$Notice.success({
          title: `页面与产品关联关系${isAdd ? '新增' : '修改'}成功~`
        })
      } else {
        this.$Message.error(`页面与产品关联关系${isAdd ? '新增' : '修改'}失败~`)
      }
    },
    changeActive(name) {
      this.active = name
    }
  }
}
</script>
  <style scoped>
  .button-edit {
    padding: 0px 5px 0px;
    font-size: 14px;
    vertical-align: bottom;
  }
  
  .title {
    font-size: 14px;
    color: #464c5b;
    font-weight: bold;
    background-color: #f8f8f9;
    padding: 8px 16px;
    width: 1000;
    align-items: center;
    margin-bottom: 15px;
  }
  .layout {
    background: #fff;
    text-align: left;
  }
  .client-menu {
    margin-bottom: 15px;
  }
  .button {
    margin-left: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .select {
    margin-top: 20px;
  }
  .detail{
    margin-left: 20px;
  }
  </style>