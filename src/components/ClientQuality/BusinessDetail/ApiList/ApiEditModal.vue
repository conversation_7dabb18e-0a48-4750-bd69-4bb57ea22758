<template>
  <Modal v-model="showApiModal" title="编辑接口信息" @on-cancel="closeModal">
    <Form ref="apiInfoItem" :model="apiInfoItem" :rules="formRules" label-position="left" :label-width="100">
      <FormItem label="path" prop="path">
        <Input v-model="apiInfoItem.path"></Input>
      </FormItem>
      <FormItem label="名称" prop="name">
        <Input v-model="apiInfoItem.name"></Input>
      </FormItem>
    <FormItem label="Method" prop="method" required>
      <Select v-model="apiInfoItem.method" placeholder="请选择">
        <Option v-for="method in methods" :key="method" :value="method">{{ method }}</Option>
      </Select>
    </FormItem>
    <FormItem label="所属服务Appkey">
      <Input v-model="apiInfoItem.appKey"></Input>
    </FormItem>
      <FormItem label="优先级" prop="priority">
        <Select v-model="apiInfoItem.priority">
          <Option :value="1" :key="1">P1</Option>
          <Option :value="2" :key="2">P2</Option>
          <Option :value="3" :key="3">P3</Option>
        </Select>
      </FormItem>
      <FormItem label="备注" prop="remark">
        <Input v-model="apiInfoItem.remark"></Input>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="primary" size="small" @click="checkApiInfo">确定</Button>
      <Button type="info" size="small" @click="closeModal">取消</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ApiEditModal',
  props: ['apiInfoItem', 'showApiModal'],
  data() {
    return {
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH'],
      formRules: {
        name: [
          {
            required: true,
            message: '请填写接口描述',
            trigger: 'change'
          }
        ],
        path: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              var pattern = /^\//
              if (value.trim() === '') {
                return callback(new Error('请填写接口Path(不包含域名)'))
              } else if (!pattern.test(value)) {
                return callback(new Error('接口Path需要以/开头'))
              } else {
                callback()
              }
            }
          }
        ],
        method: [
          {
            required: true,
            trigger: 'change',
            message: '请选择接口method'
          }
        ]
      },
      apiInfo: []
    }
  },
  mounted() {

  },
  methods: {
    checkApiInfo() {
      this.$refs['apiInfoItem'].validate((valid) => {
        if (valid) {
          this.apiInfo.push(this.apiInfoItem)
          this.addNewApi()
        } else {
          this.$Notice.warning({
            title: '请补充信息'
          })
        }
      })
    },
    addNewApi() {
      this.$axios({
        method: 'post',
        data: {
          apiInfo: this.apiInfo,
          business: this.apiInfoItem.businessId,
          addMode: 2
        },
        url: this.env.url + 'api/apiBase/add'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.$Notice.success({
            title: message.message
          })
          this.closeModal('0')
          this.apiInfo = []
        }
      })
    },
    closeModal: function (val) {
      this.$emit('closeApiInfo', val)
    }
  }
}
</script>