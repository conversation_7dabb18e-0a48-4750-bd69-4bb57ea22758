<template>
    <div>
    <Form :model="filter" inline>
      <FormItem label="接口Path" class="input">
        <Input v-model="filter.path" placeholder="请输入接口Path" style="width: 200px" />
      </FormItem>
      <FormItem label="接口名称" class="input">
        <Input v-model="filter.name" placeholder="请输入接口名称" style="width: 200px" />
      </FormItem>
      <FormItem label="产品" class="input">
        <Select v-model="filter.product" filterable transfer clearable placeholder="请选择产品" style="width:200px">
            <Option v-for="item in this.productList" :value="item.id" :key="item.id">{{ item.label }}</Option>
          </Select>
      </FormItem>
      <FormItem label="未关联页面接口" class="check">
        <Checkbox v-model="filter.unRelatedPage">&nbsp;</Checkbox>
      </FormItem>
      <FormItem>
        <Button type="primary" icon="md-search" @click="searchApiList">
          Search
        </Button>
        <Button @click="clearSearch">
          Reset
        </Button>
     </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'ApiListFilter',
  props: ['productList'],
  data() {
    return {
      filter: {
        path: '',
        name: '',
        product: '',
        unRelatedPage: false
      }
    }
  },
  methods: {
    searchApiList() {
      this.$emit('search', this.filter)
    },
    clearSearch() {
      this.filter.path = ''
      this.filter.name = ''
      this.filter.product = ''
      this.filter.unRelatedPage = false
      this.$emit('search', this.filter)
    }
  }
}
</script>

<style scoped>
.input{
    padding-left:10px;
    width:300px
}
.check{
    width:200px
}
</style>
