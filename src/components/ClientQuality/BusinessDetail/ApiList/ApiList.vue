<template>
  <div>
    <Row style="display:flex" justify="space-between">
      <ApiListFilter :productList="productList" @search="search"></ApiListFilter>
      <Button v-if="permission" class="button" icon="md-add" type="success" @click="addApi()">新增接口</Button>
    </Row>
    <Spin size="large" fix v-if="isLoading"></Spin>
    <ApiListInfo v-else :apiList="apiList" type='business' @getApiList="getApiList" :permission="permission"></ApiListInfo>
    <ApiListPageCount class="pageCount" @transferPage="getPage" :total="total" :currentPage="query.pageCount"></ApiListPageCount>
    <ApiEditModal :showApiModal="showApiModal" :apiInfoItem="apiInfo" @closeApiInfo="closeApiInfo"></ApiEditModal>
  </div>
</template>

<script>
import * as api from '../../state/api.js'
import ApiListPageCount from './ApiListPageCount'
import ApiListInfo from './ApiListInfo'
import ApiEditModal from './ApiEditModal'
import ApiListFilter from './ApiListFilter'
export default {
  name: 'ApiList',
  props: ['tab'],
  components: { ApiListPageCount, ApiListInfo, ApiEditModal, ApiListFilter },
  data() {
    return {
      isLoading: true,
      apiList: [],
      query: {
        businessId: this.$route.params.businessId,
        pageCount: 1,
        pageSize: 10
      },
      total: 0,
      showApiModal: false,
      apiInfo: {},
      productList: []
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.getApiList()
    this.getProductByBusinessId()
  },
  computed: {
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    getApiList() {
      this.isLoading = true
      this.$axios({
        method: 'get',
        params: this.query,
        url: this.env.url + 'api/getApiList'
      }).then((res) => {
        this.isLoading = false
        let message = res.data
        this.apiList = message.data
        this.total = message.total
      })
    },
    getPage(msg) {
      this.query.pageCount = msg
      this.getApiList()
    },
    addApi() {
      this.apiInfo = {
        remark: '',
        path: '',
        priority: 2,
        businessId: this.$route.params.businessId,
        online: 1
      }
      this.showApiModal = true
    },
    closeApiInfo(val) {
      if (val === '0') {
        this.getApiList()
      }
      this.showApiModal = false
    },
    search(filter) {
      this.query.path = filter.path
      this.query.name = filter.name
      this.query.productId = filter.product
      this.query.pageCount = 1
      this.query.unRelatedPage = filter.unRelatedPage
      this.getApiList()
    },
    getProductByBusinessId() {
      api.getProductByBusinessId(this.$route.params.businessId).then(res => {
        let message = res.data
        this.productList = message
      })
    }
  }
}
</script>

<style scoped>
.button {
  text-align: right;
  margin-right: 30px;
}
.pageCount {
  margin-top:15px;
  margin-right: 30px;
}
</style>