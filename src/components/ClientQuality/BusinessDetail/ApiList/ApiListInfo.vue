<template>
  <div>
    <div class="no-info" v-if="this.apiList&&this.apiList.length===0">
        <img class="error-img" src="/static/img/cqp-error.jpg" />
        <br>
        <span>暂无接口信息</span>
      </div>
    <Table v-if="apiList&&apiList.length>0" class="table" :columns="title" :data="apiList">
      <template slot-scope="{row}" slot="apiPath">
        <span>{{row.apiPath}}</span><br>
      </template>
      <template slot-scope="{row}" slot="name">
        <span >{{row.name}}</span>
      </template>
      <template slot-scope="{row}" slot="priority">
        <Tag v-if="row.priority === 1" color="volcano">P{{ row.priority }}</Tag>
        <Tag v-else color="blue">P{{ row.priority }}</Tag>
      </template>
      <template slot-scope="{row}" slot="product" >
        <div class="product" v-for="productItem in row.products" :key="productItem.id">
          <Tag color="volcano">{{ productItem.label }}</Tag>
        </div>
      </template>
      <template slot-scope="{row}" slot="action">
        <Button size="small" type="success" ghost @click="toApiDetailPage(row.id)">查看</Button>
        <Button v-if="type==='business' && permission" size="small" type="error" ghost @click="showDeleteModal(row.id,row.apiPath)" >删除</Button>
        <Button v-if="type==='page' && permission" size="small" type="error" ghost @click="deletePageApi(row.apiPageList[0].id)">取消关联</Button>
      </template>
    </Table>
  </div>
</template>
<script>
export default {
  name: 'ApiListInfo',
  props: ['apiList', 'type', 'permission'],
  data() {
    return {
      title: [
        { type: 'index', width: 60, align: 'center' },
        { title: '接口Path', width: 550, key: 'apiPath', slot: 'apiPath' },
        {
          title: '接口名称',
          key: 'name',
          slot: 'name'
        },
        {
          title: '优先级',
          key: 'priority',
          width: 220,
          slot: 'priority',
          align: 'center'
        },
        {
          title: '产品',
          key: 'product',
          width: 220,
          slot: 'product'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          slot: 'action',
          align: 'center'
        }
      ],
      apiInfo: [],
      isAdd: false,
      selectApiPageProduct: {},
      pageInfo: {}
    }
  },
  mounted() {
    this.pageInfo = this.$store.state.clientQuality.pageInfo.pageInfo
  },
  methods: {
    deleteApi(row) {
      this.$axios({
        method: 'delete',
        params: {
          apiId: row
        },
        url: this.env.url + 'api/apiBase/delete'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.$Notice.success({
            title: '接口删除成功！~'
          })
          this.$emit('getApiList')
        }
      })
    },
    toApiDetailPage(row) {
      let routeData = this.$router.resolve({
        path: '/client/detail/api/' + row
      })
      window.open(routeData.href, '_blank')
    },
    showDeleteModal(id, path) {
      this.$Modal.confirm({
        title: '删除接口',
        content: '<p>确认删除接口<p>' + path,
        onOk: () => {
          this.deleteApi(id)
        }
      })
    },
    deletePageApi(apiPageId) {
      this.$axios({
        method: 'delete',
        params: {
          apiPageId: apiPageId
        },
        url: this.env.url + 'api/page'
      }).then((res) => {
        let message = res.data
        if (message.code === 200) {
          this.$Notice.success({
            title: '解除关联成功！~'
          })
          this.$emit('refreshApiList')
        }
      })
    }
  }
}
</script>
<style scoped>
.table {
  margin-top: 15px;
  /* width: 1500px */
}
.product {
  margin-top: 8px;
  margin-bottom: 8px;
}
.error-img {
  width: 200px;
}
.no-info {
  text-align: center;
  padding-bottom: 20px;
  color: #464c5b;
  font-size: medium;
  font-weight: initial;
}

</style>