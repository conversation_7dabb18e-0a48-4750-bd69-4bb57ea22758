<template>
  <Page style="text-align:center" :current="current" :total="total" @on-change="changePage" :page-size="pageSize" show-total />
</template>
<script>
export default {
  name: 'PageCount',
  props: ['total'],
  data() {
    return {
      businessId: this.$route.params.businessId,
      pageSize: 20
    }
  },
  computed: {
    current: function () {
      return this.$store.state.currentPage
    }
  },
  methods: {
    changePage(page) {
      this.$store.commit('setCurrentPage', page)
      this.$store.dispatch('getPageListById')
    }
  }
}
</script>
<style scoped>
</style>
