<template>
  <Card class="page-cell" @click.native="toPage">
    <Row>
      <Col flex="auto">
        <Tooltip v-if="page.online" placement="top" content="ON">
          <Badge status="success" />
        </Tooltip>
        <Tooltip v-else placement="top" content="OFF">
          <Badge status="error" />
        </Tooltip>
        <Icon type="md-document" style="line-height:revert;" />
      <span class="title">{{ page.name }}</span>
      </Col>
      <Col flex="35px">
      <Tooltip content="快速编辑" placement="top" style="float: right;">
        <Button v-if="permission" icon="md-create" type="text" @click.stop="editPage"></Button>
      </Tooltip>
      </Col>

    </Row>
    <Row>
      <Tooltip class="desc" placement="top" :content="page.remark">
        {{ page.remark }}
      </Tooltip>
    </Row>
    <Row style="height:52px; overflow:hidden;">
      <Tooltip class="infoList" placement="top" max-width="10px">
        <div slot="content">
          <span v-for="category in page.categories" :key="category.id">
            项目分组：{{ category.name }}
          </span>
          <span v-for="product in page.products" :key="product.id">
            产品：{{ product.label }}
          </span>
        </div>
        <Tag v-if="page.priority === 1" color="error">P{{ page.priority }}</Tag>
        <Tag v-else color="primary">P{{ page.priority }}</Tag>
        <Tag v-for="category in page.categories" :key="category.id" color="blue">{{ category.name }}</Tag>
        <Tag v-for="product in page.products" :key="product.id" color="volcano">{{
          product.label
        }}</Tag>
      </Tooltip> 
    </Row>
    <div>
      <span style="color:#19be6b;font-weight:bold">{{ page.sceneCount }}</span><span> scenes</span>
    </div>
  </Card>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'PageCell',
  props: ['page', 'permission'],
  data() {
    return {}
  },
  computed: {},
  methods: {
    toPage() {
      let routeData = this.$router.resolve({
        path: '/client/page/' + this.page.id + '/scene'
      })
      window.open(routeData.href, '_blank')
    },
    editPage() {
      let editInfo = {
        type: 'page',
        data: {
          id: this.page.id,
          name: this.page.name,
          parent: this.page.businessId,
          priority: this.page.priority,
          online: this.page.online
        }
      }
      Bus.$emit('editPage', editInfo)
    }
  }
}
</script>
<style scoped>
.page-cell {
  height: 155px;
  margin-bottom: 20px;
}
.title {
  font-weight: bold;
  color: #464c5b;
  margin-left: 5px;
  margin-right: 5px;
}
.desc {
  max-height: 21px;
  overflow: hidden;
  color: #657180;
  font-size: small;
  padding-left: 8px;
}
.infoList /deep/ .ivu-tooltip-inner {
  max-width: 350px;
  padding-left: 0px;
}
.infoList /deep/ .ivu-tooltip-popper {
  line-height: 1;
  font-size: 13px;
}
</style>
