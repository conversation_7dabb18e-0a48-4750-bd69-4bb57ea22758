<template>
  <Row>
    <Col span="21">
    <Form :model="search" :label-width="70" inline>
      <Row>
        <Col span="19">
        <FormItem label="名称" class="form-item">
          <Input type="text" style="width:200px" v-model="search.word" placeholder="Enter Page Name" />
        </FormItem>
        <FormItem label="负责人" class="form-item">
          <Input type="text" style="width:200px" v-model="search.persion" placeholder="Enter MIS ID" />
        </FormItem>
        <FormItem label="只看我" class="form-item" :label-width="60">
          <Checkbox v-model="search.isMe">&nbsp;</Checkbox>
        </FormItem>
        <FormItem label="产品" class="form-item">
          <Select v-model="search.product" filterable transfer clearable placeholder="Select Product" style="width:200px">
            <Option v-for="item in products" :value="item.id" :key="item.id">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="项目分组" class="form-item">
          <Input type="text" style="width:200px" v-model="search.categoryName" placeholder="Enter Category Name" />
        </FormItem>
        <FormItem label="优先级">
          <CheckboxGroup v-model="search.priorityList">
            <Checkbox label="1">P1</Checkbox>
            <Checkbox label="2">P2</Checkbox>
            <Checkbox label="3">P3</Checkbox>
            <Checkbox label="4">P4</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="在线">
          <i-switch v-model="search.online" :true-value="1" :false-value="0" />
        </FormItem>
        </Col>
        <Col span="5">
        <FormItem class="form-item">
          <Button type="primary" :loading="isSearching" icon="md-search" @click="searchPage">
            Search
          </Button>
          <Button @click="clearSearch">Reset</Button>
        </FormItem>
        </Col>
      </Row>
    </Form>
    </Col>
    <Col span="2" offset="1">
    <Button v-if="permission" icon="md-add" type="success" @click="addPage">New</Button>
    </Col>
  </Row>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'PageFilter',
  props: ['permission', 'businessId'],
  data() {
    return {
      search: {
        word: '',
        priorityList: [],
        online: 1,
        persion: '',
        isMe: false,
        product: '',
        categoryName: ''
      },
      isSearching: false
    }
  },
  mounted() {
    this.$store.dispatch('getBusinessProduct', this.businessId)
  },
  computed: {
    products() {
      return this.$store.state.clientQuality.productList
    }
  },
  methods: {
    searchPage() {
      let query = {}
      query.word = this.search.word
      query.online = this.search.online
      query.priority = this.search.priorityList.toString()
      if (this.search.isMe) {
        query.user = Bus.userInfo.userLogin
      } else {
        query.user = this.search.persion
      }
      query.product = this.search.product
      query.categoryName = this.search.categoryName
      this.$store.commit('setPageFilter', query)
      this.$store.commit('setCurrentPage', 1)
      this.$store.dispatch('getPageListById')
    },
    clearSearch() {
      this.$store.commit('setPageFilter', {})
      this.search = {
        word: '',
        priorityList: [],
        online: 1,
        persion: '',
        product: '',
        categoryName: ''
      }
      this.$store.commit('setCurrentPage', 1)
      this.$store.dispatch('getPageListById')
    },
    addPage() {
      let editInfo = {
        type: 'page',
        data: {
          name: '',
          parent: Number(this.$route.params.businessId),
          priority: 2,
          online: 1
        }
      }
      Bus.$emit('editPage', editInfo)
    }
  }
}
</script>
<style scoped>
.add-button {
  float: right;
  margin-right: 30px;
}
.form-item {
  margin-bottom: 8px;
}
</style>