<template>
  <div>
      <AbListFilter :permission="permission" :businessId="businessId" @search="search" @toAbDetailPage="toAbDetailPage"></AbListFilter> 
      <AbListInfo :abList="abList" @getAbList="getAbList" @toAbDetailPage="toAbDetailPage" :permission="permission"></AbListInfo>
      <Spin size="large" fix v-if="isLoading"></Spin>
      <AbListPageCount class="pageCount" @transferPage="getPage" :total="total" :currentPage="pageCount"></AbListPageCount>
  </div>
  </template>
  <script>
  import AbListPageCount from './AbListPageCount'
  import AbListInfo from './AbListInfo'
  import AbListFilter from './AbListFilter'
  export default {
    name: 'AbList',
    props: ['tab'],
    components: {AbListInfo, AbListFilter, AbListPageCount},
    data() {
      return {
        businessId: this.$route.params.businessId,
        pageCount: 1,
        pageSize: 10,
        word: '',
        startTime: '',
        endTime: '',
        pageList: [],
        productList: [],
        source: '',
        misId: '',
        typeList: '1',
        statusList: '1,2,-1'
      }
    },
    mounted() {
      this.$store.commit('setCurrentTab', this.tab)
      this.getAbList()
    },
    computed: {
      isLoading() {
        return this.$store.state.clientQuality.isLoading
      },
      abList() {
        return this.$store.state.clientQuality.abList.abInfoList
      },
      total() {
        return this.$store.state.clientQuality.abList.total
      },
      permission() {
        let permission =
          this.$store.state.clientQuality.configCenterUserPermission
        if (permission.isAdmin || permission.permissionLevel < 100) {
          return true
        } else {
          return false
        }
      }
    },
    methods: {
      getAbList() {
        let data = {
          businessId: this.businessId,
          word: this.word,
          startTime: this.startTime,
          endTime: this.endTime,
          pageId: this.page,
          productId: this.product,
          misId: this.misId,
          pageCount: this.pageCount,
          pageSize: this.pageSize,
          source: this.source,
          typeList: this.typeList,
          statusList: this.statusList
        }
        this.$store.dispatch('getAbList', data)
      },
      getPage(msg) {
        this.pageCount = msg
        this.getAbList()
      },
      toAbDetailPage(row) {
        let routeData = this.$router.resolve({
          path: '/client/detail/ab/' + row,
          query: {businessId: this.$route.params.businessId}
        })
        window.open(routeData.href, '_blank')
      },
      search(filter) {
        this.word = filter.word
        this.startTime = filter.rangeDate[0]
        this.endTime = filter.rangeDate[1]
        this.page = filter.page
        this.product = filter.product
        this.pageCount = 1
        this.source = filter.source
        this.typeList = filter.typeList.toString()
        this.statusList = filter.statusList.toString()
        if (filter.misId) {
          this.misId = filter.misId
        } else {
          this.misId = ''
        }
        this.getAbList()
      }
    }
  }
  </script>
  
  <style scoped>
  .title {
  margin-left: 20px;
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #464c5b;
}
  .button {
    text-align: right;
    margin-right: 30px;
  }
  .pageCount {
    margin-top:15px;
    margin-right: 30px;
  }
  </style>