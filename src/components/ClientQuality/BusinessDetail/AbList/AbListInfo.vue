<template>
    <div>
      <div class="no-info" v-if="this.abList&&this.abList.length===0">
          <img class="error-img" src="/static/img/cqp-error.jpg" />
          <br>
          <span>暂无AB实验信息</span>
      </div>
      <Table v-if="abList&&abList.length>0" class="table" :columns="title" :data="abList">
        <template slot-scope="{row}" slot="abKey">
          <Row>
          <span class="main-title">{{row.abKey}}</span>
          <span v-if="row.type === 1"  class="sub-title" style="margin-left: -10px; color: #91d5ff;">[客户端]</span>
          <span v-else-if="row.type === 2"  class="sub-title" style="margin-left: -10px;color:#b7eb8f">[服务端]</span>
          <el-tooltip content="点击更改AB业务方向" placement="top">
            <Button type="primary" icon="md-share-alt" class="btn" @click="row.modal = true" ghost></Button>
          </el-tooltip>
          <Modal v-model="row.modal" class='vertical-center-modal' title="请要归属的业务方向" @on-ok="bizChange(row)" @on-cancel="clearBizChangeSelect" width="540">
            <div>
              <Row>
                <Alert v-if="row.pageIds.every(id => id > 0)" type="warning" show-icon>注意：当前AB已关联页面，移动业务方向后会丢失已关联页面状态</Alert>
              </Row>
              <Row>
                <Col class="select-location" span="12">
                  <Select placeholder="选择BU ..." v-model="unitId" filterable :getPopupContainer="trigger => trigger.closest('.ivu-modal')" style="padding-right: 10px;">
                    <Option v-for="item in unitList" :value="item.id" :key="item.id">{{ item.label }}</Option>
                  </Select>
                </Col>
                <Col class="select-location" span="12">
                  <Select placeholder="选择业务方向 ..." v-model="businessId" filterable :getPopupContainer="trigger => trigger.closest('.ivu-modal')" style="padding-right: 10px;">
                    <Option v-for="item in businessList" :value="item.id" :key="item.id">{{ item.label }}</Option>
                  </Select>
                </Col>
              </Row>
            </div>
          </Modal>
          </Row>
          <span class="sub-title">{{row.name}}</span>
        </template>
        <template slot-scope="{row}" slot="createTime" >
          <code class="code">{{formatDate(row.createTime)}}</code>
        </template>
        <template slot-scope="{row}" slot="pm" >
          <div class="user" v-for="(pm,index) in row.pm" :key="index">
            <Tag color="volcano">{{pm}}</Tag>
          </div>
        </template>
        <template slot-scope="{row}" slot="qa" >
          <div class="user" v-for="(qa,index) in row.qa" :key="index">
            <Tag color="blue">{{qa}}</Tag>
          </div>
        </template>
        <template slot-scope="{row}" slot="action">
          <Button size="small" type="success" ghost @click="toAbDetailPage(row.id)">编辑</Button>
          <Button v-if="permission" size="small" type="error" ghost @click="showDeleteModal(row.id,row.abKey)">删除</Button>
        </template>
      </Table>
    </div>
  </template>
  <script>
  export default {
    name: 'AbListInfo',
    props: ['abList', 'permission'],
    data() {
      return {
        currentBusinessId: this.$route.params.businessId,
        title: [
          { type: 'index', width: 100, align: 'center' },
          { title: 'AB实验', key: 'abKey', slot: 'abKey' },
          {
            title: 'AB上线时间',
            key: 'createTime',
            width: 200,
            slot: 'createTime',
            align: 'left'
          },
          {
            title: 'PM',
            key: 'pm',
            width: 200,
            slot: 'pm',
            align: 'left'
          },
          {
            title: 'QA',
            key: 'qa',
            width: 200,
            slot: 'qa',
            align: 'left'
          },
          {
            title: '操作',
            key: 'action',
            width: 250,
            slot: 'action',
            align: 'center'
          }
        ],
        abInfo: [],
        businessId: 0,
        unitId: 0,
        unitList: [],
        businessList: []
      }
    },
    mounted() {
      this.$store.dispatch('getBusinessGroup')
      this.initializeBizChangeModal()
      this.extractAllUnits()
    },
    watch: {
      abList: {
        handler(newList) {
          this.initializeBizChangeModal(newList)
        },
        deep: true
      },
      businessGroup: {
        handler(newVal) {
          this.extractAllUnits()
        },
        deep: true
      },
      unitId(newUnitId) {
        if (!newUnitId) {
          this.businessList = []
          this.unitId = 0
          this.businessId = 0
          this.$emit('unitChange', this.unitId)
          return
        }
        const selectedUnit = this.unitList.find(unit => unit.id === newUnitId)
        this.businessList = selectedUnit ? selectedUnit.businessList || [] : []
        this.$emit('unitChange', this.unitId)
      },
      businessId() {
        if (this.businessId !== 0) {
          this.$emit('businessChange', this.businessId)
        }
      }
    },
    computed: {
      businessGroup() {
        return this.$store.state.clientQuality.businessGroup
      },
      groupList() {
        return Object.values(this.businessGroup)
      }
    },
    methods: {
      formatDate (date) {
        date = new Date(date)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const day = date.getDate()
        return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
      },
      async deleteAb(row) {
        await this.$store.dispatch('deleteAb', row)
        this.$emit('getAbList')
      },
      toAbDetailPage(row) {
        this.$emit('toAbDetailPage', row)
      },
      showDeleteModal(id, abKey) {
        this.$Modal.confirm({
          title: '删除AB实验',
          content: '确认删除AB实验：' + abKey,
          onOk: () => {
            this.deleteAb(id)
          }
        })
      },
      initializeBizChangeModal(list = this.abList) {
        list.forEach(item => {
          if (item.modal === undefined) {
            this.$set(item, 'modal', false)
          }
        })
      },
      clearBizChangeSelect() {
        // 重置 Select 组件的值
        this.unitId = null
        this.businessId = null
      },
      async bizChange(row) {
        // 检查 Select 组件的值是否为空
        if (!this.unitId || !this.businessId) {
          // 保持弹窗
          this.$nextTick(() => {
            row.modal = true
            this.$Message.error('请选择业务方向')
          })
          return
        }
        // 检查 businessId 是否与当前一致
        if (Number(this.businessId) === Number(this.currentBusinessId)) {
          // 保持弹窗
          this.$nextTick(() => {
            row.modal = true
            this.$Message.error('当前已是目标方向，无需切换')
          })
          return
        }
        let request = {
          abId: [row.id],
          targetBusinessId: this.businessId
        }
        const response = await this.$store.dispatch('changeBiz', request)
        if (response.data.code === 200) {
          this.$emit('getAbList')
          this.$Message.success('业务方向切换成功～')
        } else {
          this.$Message.error('业务方向切换失败，请重试～ ')
        }
        this.clearBizChangeSelect()
      },
      extractAllUnits() {
        let allUnits = []
        for (let groupId in this.businessGroup) {
          if (this.businessGroup.hasOwnProperty(groupId)) {
            let unitMap = this.businessGroup[groupId].unitMap
            allUnits = allUnits.concat(Object.values(unitMap))
          }
        }
        this.unitList = allUnits
      }
    }
  }
  </script>
  <style scoped>

  .code {
    border:1px solid #ffd591;
    background:#fff7e6;
    color: #fa8c16!important;
    font-size: 12px;
  }
  .main-title {
    font-size: 14px;
    color: #657180;
    margin-right: 10px;
    display: inline-block
  }

  .sub-title {
    top: -6px;
    font-size: 12px;
    color: #9ea7b4;
  }
  .table {
    margin-top: 15px;
  }
  .user {
    margin-top: 4px;
    margin-bottom: 4px;
  }
  .error-img {
    width: 200px;
  }
  .no-info {
    text-align: center;
    padding-bottom: 20px;
    color: #464c5b;
    font-size: medium;
    font-weight: initial;
  }
  .vertical-center-modal{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn {
    width: 18px;
    height: 18px;
    color: #1686f5;
    background-color: transparent;
    border-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    margin-left: 1px;
    margin-top: 2px;
  }
  </style>
