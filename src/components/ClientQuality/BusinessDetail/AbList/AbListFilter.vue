<template>
        <Form :model="filter" :label-width='100' inline>
          <Row>
          <Col span="19">
            <Row>
            <FormItem label="ABKey/描述" class="form-item" :label-width='80'>
                <Input
                    v-model="filter.word"
                    placeholder="请输入ABKey或实验描述"
                    style="width: 200px"
                />
            </FormItem>
            <FormItem label="AB上线时间" class="form-item">
                <DatePicker
                    type="daterange"
                    format="yyyy-MM-dd"
                    split-panels
                    v-model="filter.rangeDate"
                    placeholder="请选择日期"
                    style="width: 200px"
                />
            </FormItem>
            <FormItem label="实验来源" class="form-item" :label-width='80'>
              <Select ref="sourceSelect" v-model="filter.source" filterable transfer clearable placeholder="请选择实验信息来源" style="width:200px">
                <Option v-for="item in source" :value="item.label" :key="item.label">{{ item.label }}</Option>
              </Select>
            </FormItem>
            <FormItem  label="负责人" class="form-item" :label-width='60'>
               <el-select ref="userSelect" v-model="filter.misId" filterable transfer clearable placeholder="请输入MIS ID" @blur="getCurVal" size="small" style="width:200px">
                <el-option v-for="item in this.misIdList" :value="item" :key="item">{{ item }}</el-option>
              </el-select>
            </FormItem>
            </Row>
            <Row>
              <FormItem label="页面" class="form-item" :label-width='80'>
                  <Select ref="pageSelect" v-model="filter.page" filterable transfer clearable placeholder="请选择页面" style="width:200px" placement="bottom">
                    <Option v-for="item in this.pageList" :value="item.id" :key="item.id">{{ item.name }}</Option>
                  </Select>
              </FormItem>
              <FormItem label="产品" class="form-item" >
              <Select ref="productSelect" v-model="filter.product" filterable transfer clearable placeholder="请选择产品" style="width:200px">
                  <Option v-for="item in this.productList" :value="item.id" :key="item.id">{{ item.label }}</Option>
              </Select>
              </FormItem>
              <FormItem label="实验状态" class="form-item" style="margin-left: -20px;">
                <Select
                  ref="statusSelect"
                  v-model="filter.statusList"
                  filterable
                  transfer
                  clearable
                  multiple
                  :max-tag-count="1"
                  placeholder="请选择实验状态"
                  style="width:200px">
                  <Option v-for="item in statusList" :value="item.value" :key="item.label">{{ item.label }}</Option>
                </Select>
              </FormItem>
              <FormItem label="实验类型" class="form-item">
                <CheckboxGroup v-model="filter.typeList">
                    <Checkbox label="1">客户端</Checkbox>
                    <Checkbox label="2">服务端</Checkbox>
                </CheckboxGroup>
              </FormItem>
            </Row>
          </Col>
          <Col span="5">
            <FormItem class="form-item" style="margin-left: -150px;">
                <Button type="primary" icon="md-search" @click="searchAbList">
                Search
                </Button>
                <Button @click="clearSearch">
                Reset
                </Button>
                <Button v-if="permission" icon="md-add" type="success" @click="addAb" style="margin-left: 20px;">New</Button>
            </FormItem>
          </Col>
          </Row>
        </Form>
</template>
<script>

import moment from 'moment'

export default {
  name: 'AbListFilter',
  props: ['permission', 'businessId'],
  data() {
    return {
      filter: {
        word: '',
        rangeDate: [],
        page: null,
        product: null,
        source: null,
        misId: null,
        typeList: '1',
        statusList: [1, 2, -1]
      },
      source: [
        {
          value: 'mmcd',
          label: 'MMCD'
        },
        {
          value: 'horizon',
          label: 'Horizon'
        },
        {
          value: 'arena',
          label: 'Arena'
        }
      ],
      statusList: [
        {
          value: 1,
          label: '实验中'
        },
        {
          value: 2,
          label: '固定策略'
        },
        {
          value: 3,
          label: '失效'
        },
        {
          value: -1,
          label: '无状态(未关联页面)'
        }
      ]
    }
  },
  mounted() {
    this.$store.dispatch('getBusinessProduct', this.businessId)
    this.$store.dispatch('getAllPageListById', this.businessId)
    this.$store.dispatch('getUsersByBusinessId', this.businessId)
  },
  computed: {
    pageList() {
      return this.$store.state.clientQuality.allPageList.pageList
    },
    productList() {
      return this.$store.state.clientQuality.productList
    },
    misIdList() {
      if (this.$store.state.clientQuality.businessUsers.members) {
        return this.$store.state.clientQuality.businessUsers.members.map(item => item.misId)
      }
      return []
    }
  },
  methods: {
    getCurVal(val) {
      this.filter.misId = val.target.value
    },
    searchAbList() {
      if (this.filter.rangeDate === null || this.filter.rangeDate.length === 0 || JSON.stringify(this.filter.rangeDate) === '{}') {
        this.filter.rangeDate = []
      } else {
        if (this.filter.rangeDate[0].length === 0) {
          this.filter.rangeDate[0] = ''
        } else {
          this.filter.rangeDate[0] = moment(this.filter.rangeDate[0]).format('YYYY-MM-DD')
        }
        if (this.filter.rangeDate[1].length === 0) {
          this.filter.rangeDate[1] = ''
        } else {
          this.filter.rangeDate[1] = moment(this.filter.rangeDate[1]).format('YYYY-MM-DD')
        }
      }
      this.$emit('search', this.filter)
    },
    clearSearch() {
      this.filter.word = ''
      this.filter.rangeDate = []
      this.filter.page = null
      this.filter.product = null
      this.filter.typeList = []
      this.filter.statusList = []
      this.filter.source = null
      this.filter.misId = null
      this.$emit('search', this.filter)
    },
    addAb() {
      this.$emit('toAbDetailPage', -1)
    }
  }
}
</script>

<style scoped>
.form-item {
  margin-right: 4px;
  margin-bottom: 8px;
  white-space: nowrap;
}
</style>
