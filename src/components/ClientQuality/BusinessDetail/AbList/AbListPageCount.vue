<template>
    <Page style="text-align:center" :current="currentPage" :total="total" @on-change="changePage" :page-size="pageSize" show-total />
  </template>
  <script>
  
  export default {
    name: 'AbListPageCount',
    props: ['total', 'currentPage'],
    data() {
      return {
        pageSize: 10
      }
    },
    computed: {
    },
    methods: {
      changePage: function (page) {
        this.$emit('transferPage', page)
      }
    }
  }
  </script>
  <style scoped>
  </style>
    