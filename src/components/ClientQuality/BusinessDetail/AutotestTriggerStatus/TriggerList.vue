<template>
  <div>
    <TriggerListFilter @transferFilter="getFilter" :nodeList="nodeList"></TriggerListFilter>
    <Spin size="large" fix v-if="isLoading"></Spin>
    <div class="no-info" v-if="this.triggerListInfo&&this.triggerListInfo.length===0">
      <img class="error-img" src="/static/img/cqp-error.jpg" />
      <br>
      <span>暂无触发信息</span>
    </div>
    <div v-else v-for="triggerInfo in this.triggerListInfo" :key="triggerInfo.id">
      <TriggerListInfo :triggerInfo="triggerInfo"></TriggerListInfo>
    </div>
    <TriggerListPageCount style="margin-top:15px" @transferPage="getPage" :total="total" :currentPage="query.pageCount"></TriggerListPageCount>
  </div>
</template>
<script>
/* eslint-disable */
import { Bus } from '@/global/bus'
import TriggerListInfo from './TriggerListInfo'
import TriggerListPageCount from './TriggerListPageCount'
import TriggerListFilter from './TriggerListFilter'
import moment from 'moment'
export default {
  name: 'TriggerList',
  props: ['tab'],
  components: { TriggerListInfo, TriggerListPageCount, TriggerListFilter },
  data() {
    return {
      query: {
        businessId: this.$route.params.businessId,
        pageCount: 1,
        pageSize: 10,
        startTime: '',
        endTime: '',
        misid: '',
        nodeId: '',
        flow: ''
      },
      total: 0,
      triggerListInfo: [],
      isLoading: true,
      nodeList: []
    }
  },
  created() {
    Bus.$on('refresh', () => {
      this.getTriggerList()
    })
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.getNodeList()
  },
  methods: {
    getTriggerList() {
      this.isLoading = true
      this.$axios({
        method: 'get',
        params: this.query,
        url: this.env.url + 'ccd/autotest/getTriggerList'
      }).then((res) => {
        let message = res.data
        for (var i = 0; i < message.triggerList.length; i++) {
          message.triggerList[i].createTime = moment(
            new Date(message.triggerList[i].createTime)
          ).format('YYYY-MM-DD HH:mm:ss')
          message.triggerList[i].endTime = moment(
            new Date(message.triggerList[i].endTime)
          ).format('YYYY-MM-DD HH:mm:ss')
        }
        this.triggerListInfo = message.triggerList
        this.total = message.total
        this.isLoading = false
      })
    },
    getNodeList() {
      this.$axios({
        method: 'get',
        params: {
          nodeType: 'flow,time'
        },
        url: this.env.url + 'autoTestConfig/getNodeList'
      }).then((res) => {
        let message = res.data
        this.nodeList = message
      })
    },
    getPage(msg) {
      this.query.pageCount = msg
      this.getTriggerList()
    },
    getFilter(msg) {
      this.query.nodeId = msg.nodeId ? msg.nodeId.join(',') : msg.nodeId
      this.query.misid = msg.isMe ? Bus.userInfo.userLogin : msg.misId
      this.query.showAll = msg.showAll ? 1 : 0
      this.query.startTime = msg.rangeDate[0]
      this.query.endTime = msg.rangeDate[1]
      this.query.pageCount = 1
      this.query.flow = msg.flow
      this.getTriggerList()
    }
  }
}
</script>
<style scoped>
.error-img {
  width: 200px;
}
.no-info {
  text-align: center;
  padding-bottom: 20px;
  color: #464c5b;
  font-size: medium;
  font-weight: initial;
}
</style>