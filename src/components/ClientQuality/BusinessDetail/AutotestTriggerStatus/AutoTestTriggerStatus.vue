<template>
<div>
    <Row type="flex" justify="start" class="card-row">
        <Col span="2">
          <img height="16pd" class="error-img" :src="autoTestItem.icon" />
          <span  style="font-size:12px;font-weight:bolder">{{autoTestItem.label}}：</span>
        </Col>
        <Col span="3" v-for="itemInfo in bizAutoTestList" :key="itemInfo.id">
          <el-tooltip :content="itemInfo.trigger_tip" placement="bottom" v-if="itemInfo.jobStatus===3">
            <Icon color="#ed4014" size=18 type="md-close-circle" />
          </el-tooltip>
          <el-tooltip :content="itemInfo.jobStatusInfo" placement="bottom" v-else>
            <img  v-if="itemInfo.jobStatus===0" height="18pd" src="/static/circles-menu-1.gif"/>
            <img  v-if="itemInfo.jobStatus===1" height="18pd" src="/static/spinning-arrows.gif"/>
            <Icon v-if="itemInfo.jobStatus===2" color="#19be6b"  size=18  type="md-share-alt"/>
            <Icon v-if="itemInfo.jobStatus===4" color="#19be6b"  size=18  type="md-done-all"/>
            <Icon v-if="itemInfo.jobStatus===5" color="#ed4014"  size=18  type="md-alert"/>
            <Icon v-if="itemInfo.jobStatus===6" color="#d3d3d3"  size=13  type="md-pause"/>
            <Icon v-if="itemInfo.jobStatus===7" color="#d3d3d3"  size=18  type="md-arrow-dropright-circle"/>
          </el-tooltip>
          <el-tooltip :content="itemInfo.business"  class="card-text" placement="bottom">
            <a color= black :href="itemInfo.reportUrl" target="_blank" v-if="itemInfo.reportUrl">
                {{itemInfo.label}}-{{itemInfo.version}}
            </a>
            <span v-else>{{itemInfo.label}}</span>
          </el-tooltip>
        </Col>
        <Col span="3" v-if="bizAutoTestList.length==0">
            <Icon size=18 type="md-remove-circle" />
            <span style="font-size:12px">未触发</span>
        </Col>
      </Row>
      </div>
</template>

<script>

/* eslint-disable */
export default {
  name: "AutoTestTriggerStatus",
  props: ["autoTestItem","autotestList","business"],
  computed: {
    bizAutoTestList() {
      return this.getBizAutoTestList(this.business);
    }
  },
  methods:{
    getBizAutoTestList(business){
      if(business==""){
        return this.autotestList;
      }else{
        let list = [];
        for(let i=0; i<this.autotestList.length; i++){
          if(this.autotestList[i].business==business){
            list.push(this.autotestList[i]);
          }
        }
      return list;
      }
    }
  }
};
</script>

<style scoped>
.card-title {
  font-size: 15px;
  height: 25px;
}
.card-text {
  font-size: 12px;
}
.test-icon{
    padding-left:5px;
}
.card-row{
    padding-left:5px;
    padding-top: 7px
}
</style>
