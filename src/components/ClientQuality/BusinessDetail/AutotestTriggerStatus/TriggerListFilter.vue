<template>
  <div>
    <Row>
      <Col span="21">
        <Form :model="filter" :label-width="80" inline>
          <FormItem label="时间">
            <DatePicker
              type="daterange"
              format="yyyy-MM-dd"
              split-panels
              v-model="filter.rangeDate"
              placeholder="请选择日期"
              style="width: 200px"
            />
          </FormItem>
          <FormItem label="需求名称" style="padding-left: 10px">
            <Input
              v-model="filter.flow"
              placeholder="请输入关键字或流水线完整链接"
              style="width: 220px"
            />
          </FormItem>
          <FormItem label="节点">
            <Select
              multiple
              style="width: 200px"
              placeholder="触发节点"
              v-model="filter.nodeId"
            >
              <Option v-for="item in nodeList" :value="item.id" :key="item.id">
                {{ item.publishName }}-{{ item.integrationName }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="相关人员">
            <Input
              v-model="filter.misId"
              placeholder="请输入 MIS Id"
              style="width: 110px"
            />
          </FormItem>
          <FormItem label="与我有关">
            <Checkbox v-model="filter.isMe">&nbsp;</Checkbox>
          </FormItem>
          <FormItem :label-width="110" label="展示未触发任务">
            <Checkbox v-model="filter.showAll">&nbsp;</Checkbox>
          </FormItem>
        </Form>
      </Col>
      <Col span="3">
        <Button @click="clearSearch">Reset</Button>
        <Button type="primary" icon="md-search" style="height:35px" @click="searchTriggerList">
          Search
        </Button>
        <el-tooltip class="item" effect="dark" content="复制" placement="bottom">
        <Button icon="md-share" v-clipboard:copy="copyPath()" v-clipboard:success="copySuccess" style="height:35px" >
          分享
        </Button>
      </el-tooltip>
      </Col>
    </Row>
  </div>
</template>
<script>
/* eslint-disable */
import moment from "moment";
export default {
  name: "TriggerListFilter",
  props: ["nodeList"],
  data() {
    return {
      filter: {
        isMe: false,
        misId: "",
        nodeId: "",
        showAll: false,
        rangeDate: [],
        flow: "",
      },
    };
  },
  created: function () {
    if (this.$route.query.filter) {
      let filter = decodeURIComponent(this.$route.query.filter);
      let filterObj = JSON.parse(filter);
      this.filter.isMe = filterObj['isMe']
      this.filter.misId = filterObj['misId']
      this.filter.nodeId = filterObj['nodeId']
      this.filter.showAll = filterObj['showAll']
      this.filter.rangeDate = filterObj['rangeDate']
      this.filter.flow = filterObj['flow']
      this.searchTriggerList();
    }else{
      let endTime = moment(new Date()).format("YYYY-MM-DD");
      let startTime = moment(
        new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
      ).format("YYYY-MM-DD");
      this.filter.rangeDate = [startTime, endTime];
      this.searchTriggerList();
    }
  },
  methods: {
    searchTriggerList: function () {
      this.filter.rangeDate[0] = moment(this.filter.rangeDate[0]).format(
        "YYYY-MM-DD"
      );
      this.filter.rangeDate[1] = moment(this.filter.rangeDate[1]).format(
        "YYYY-MM-DD"
      );
      this.$emit("transferFilter", this.filter);
    },
    copyPath () {
      let url = 'http://qa.sankuai.com/client/businessDetail/' + this.$route.params.businessId + '/trigger?filter=' + encodeURIComponent(JSON.stringify(this.filter));
      return url;
      
    },
    copySuccess () {
      this.$Message.info('复制成功')
    },
    clearSearch() {
      this.filter = {
        isMe: false,
        misId: "",
        nodeId: "",
        showAll: false,
        flow: "",
      }
      let endTime = moment(new Date()).format("YYYY-MM-DD");
      let startTime = moment(
        new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
      ).format("YYYY-MM-DD");
      this.filter.rangeDate = [startTime, endTime]
    }
  }
};
</script>
<style scoped>
.form-position {
  margin-left: 25px;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 12px;
}
</style>