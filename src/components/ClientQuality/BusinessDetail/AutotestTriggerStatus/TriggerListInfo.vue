<template>
  <div>
    <Card style="margin-left: 15px; margin-right: 15px" class="card">
      <Row style="padding-bottom: 8px" :gutter="16">
        <Col span="10" class="title">
          <a v-if="triggerInfo.flowInfo" :href="triggerInfo.flowInfo.flowUrl" target="_Blank">
            <font color="#464c5b">{{ triggerInfo.flowInfo.title }}</font>
          </a>
          <span v-else>--</span>
        </Col>
        <Col span="2" class="title">
          <el-tooltip content="点击选择需要合并查看的job" placement="top">
            <Button
              type="primary"
              @click="chooseJobs = true"
              class="btn"
              shape="circle"
              icon="md-menu"
              v-if="this.isOnlyShortlink == true && triggerInfo.autoTestInfo.length != 0 && this.IsAllFailed == false"
            ></Button>
          </el-tooltip>
        </Col>
        <Col span="4">
          <Tag width="55px" color="blue">{{ triggerInfo.realNode }}</Tag>
        </Col>
        <Col span="3" v-if="triggerInfo.flowInfo">
          <Tag v-if="triggerInfo.flowInfo.taskType == 'requirement'" color="default">需求</Tag>
          <Tag v-if="triggerInfo.flowInfo.taskType == 'defect'" color="default">缺陷</Tag>
        </Col>
        <Col sapn="1">
          <Button
            v-if="permission && !this.rebuildBlackList.includes(triggerInfo.nodeId)"
            icon="md-refresh"
            shape="circle"
            class="btn"
            @click="rebuildModel = true"
          ></Button>
          <Modal v-model="rebuildModel" title="📢 注意" ok-text="确认触发" @on-ok="rebuildTrigger(triggerInfo.id)">
            <p>MMCD会生成一条新的触发记录</p>
            <p>新老触发记录互不影响</p>
            <p>测试对象不会产生变更</p>
          </Modal>
        </Col>
        <Col span="3" style="float: right">
          <Tag width="80px" color="orange">{{ triggerInfo.createTime }}</Tag>
          <Tooltip v-if="triggerInfo.advanced" :content="triggerInfo.advanced.tag" placement="top">
            <Icon color="#19be6b" type="md-walk" />
          </Tooltip>
        </Col>
      </Row>
      <el-tabs v-if="this.bizList.length > 1 && triggerInfo.autoTestInfo.length != 0" size="small">
        <el-tab-pane :label="biz" v-for="biz in this.bizList" :key="biz.id" style="font-size: 12px">
          <div v-for="autoTestType in triggerInfo.autoTestInfo" :key="autoTestType.autoTestTypeLabel">
            <AutoTestTriggerStatus
              :autoTestItem="autoTestType.autoTestItem"
              :autotestList="autoTestType.autoTestInfo"
              :business="biz"
            ></AutoTestTriggerStatus>
          </div>
          <Row v-if="triggerInfo.autoTestInfo.length == 0" style="padding-left: 5px">
            <Col span="2">
              <span style="font-size: 12px; font-weight: bolder; padding-left: 5px">自动化测试：</span>
            </Col>
            <Col span="3">
              <Icon size="18" type="md-remove-circle" />
              <span style="font-size: 12px">自动化未触发</span>
            </Col>
          </Row>
        </el-tab-pane>
      </el-tabs>
      <template v-else>
        <div v-for="autoTestType in triggerInfo.autoTestInfo" :key="autoTestType.autoTestTypeLabel">
          <AutoTestTriggerStatus
            :autoTestItem="autoTestType.autoTestItem"
            :autotestList="autoTestType.autoTestInfo"
            business=""
          ></AutoTestTriggerStatus>
        </div>
        <Row v-if="triggerInfo.autoTestInfo.length == 0" style="padding-left: 5px">
          <Col span="2">
            <span style="font-size: 12px; font-weight: bolder; padding-left: 5px">自动化测试：</span>
          </Col>
          <Col span="3">
            <Icon size="18" type="md-remove-circle" />
            <span style="font-size: 12px">自动化未触发</span>
          </Col>
        </Row>
      </template>
    </Card>
    <Modal v-model="chooseJobs" title="请选择要合并查看的类型及任务" @on-cancel="closeModal">
      <Row style="padding-bottom: 16px">
        <RadioGroup v-model="selectedJobType" v-for="item in triggerInfo.autoTestInfo" :key="item.id" @on-change="getJobsInfo">
          <Radio :label="item.autoTestItem.name" v-if="item.autoTestItem.name != 'shortLink'" style="font-weight: 500">
            {{ item.autoTestItem.label }}
          </Radio>
        </RadioGroup>
      </Row>
      <div v-if="this.isSelectType == true">
        <CheckboxGroup v-model="selectedJobList" v-for="item in triggerInfo.autoTestInfo" :key="item.id">
          <Row v-for="options in item.autoTestInfo" :key="options.id">
            <Col :span="15">
              <Checkbox
                :label="options.reportUrl"
                v-if="options.autotest_type == selectedJobType && options.job_id != -1 && options.job_id != 0"
                style="font-weight: 500"
              >
                {{ options.business }}-{{ options.label }}
              </Checkbox>
            </Col>
          </Row>
        </CheckboxGroup>
      </div>
      <div slot="footer">
        <Button type="primary" @click="submitJobs">提交</Button>
        <Button @click="closeModal">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import AutoTestTriggerStatus from './AutoTestTriggerStatus'
import * as api from '../../state/api'
import { Bus } from '@/global/bus'
export default {
  name: 'TriggerListInfo',
  props: ['triggerInfo'],
  components: { AutoTestTriggerStatus },
  data() {
    return {
      rebuildBlackList: [35, 36, 37],
      triggerList: this.$route.params.triggerList,
      selectedJobList: [],
      chooseJobs: false,
      isOnlyShortlink: this.getIsOnlyShortlink(),
      selectedJobType: '',
      isSelectType: false,
      IsAllFailed: this.getIsAllFailed(),
      bizList: this.getBizList(),
      rebuildModel: false
    }
  },
  computed: {
    permission() {
      let permission = this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    closeModal() {
      this.chooseJobs = false
      this.selectedJobList = []
      this.selectedJobType = ''
    },
    submitJobs() {
      if (this.selectedJobList.length > 0) {
        const newSelectedJobList = []
        for (const jobUrl of this.selectedJobList) {
          const url = new URL(jobUrl)
          const jobId = url.searchParams.get('jobId') || ''
          const pairJobId = url.searchParams.get('pairJobs') || ''
          if (jobId !== '') newSelectedJobList.push(jobId)
          if (pairJobId !== '') newSelectedJobList.push(pairJobId)
        }
        if (newSelectedJobList.length > 0) {
          let url = 'http://qa.sankuai.com/microscope/jobInfo?jobId=' + newSelectedJobList[0] + '&pairJobs='
          for (let i = 1; i < newSelectedJobList.length; i++) {
            url = url + newSelectedJobList[i]
            if (i !== newSelectedJobList.length - 1) url = url + ','
          }
          window.open(url)
          this.closeModal()
        }
      } else {
        this.$Notice.info({
          title: '请选择job'
        })
      }
    },
    getIsOnlyShortlink() {
      if (this.triggerInfo.autoTestInfo.length === 1 && this.triggerInfo.autoTestInfo[0].autoTestItem.name === 'shortLink') {
        return false
      } else {
        return true
      }
    },
    getIsAllFailed() {
      for (let i = 0; i < this.triggerInfo.autoTestInfo.length; i++) {
        if (this.triggerInfo.autoTestInfo[i].autoTestItem.name !== 'shortLink') {
          for (let j = 0; j < this.triggerInfo.autoTestInfo[i].autoTestInfo.length; j++) {
            if (this.triggerInfo.autoTestInfo[i].autoTestInfo[j].job_id !== -1) {
              return false
            }
          }
        }
        return true
      }
    },
    getBizList() {
      let bizList = []
      for (let i = 0; i < this.triggerInfo.autoTestInfo.length; i++) {
        for (let j = 0; j < this.triggerInfo.autoTestInfo[i].autoTestInfo.length; j++) {
          if (bizList.includes(this.triggerInfo.autoTestInfo[i].autoTestInfo[j].business) === false) {
            bizList.push(this.triggerInfo.autoTestInfo[i].autoTestInfo[j].business)
          }
        }
      }
      return bizList
    },
    getJobsInfo() {
      this.selectedJobList = []
      this.isSelectType = true
    },
    async rebuildTrigger(triggerId) {
      await api.rebuildTrigger(triggerId).then((res) => {
        let message = res.data
        if (message.code && message.code >= 200 && message.code < 299) {
          this.$Message.success('success')
          Bus.$emit('refresh')
        } else {
          this.$Message.error('触发失败')
        }
      })
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 15px;
  color: #464c5b;
  font-weight: bolder;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.card {
  margin-bottom: 2px;
  margin-top: 5px;
}
.el-tabs /deep/ .el-tabs__nav-wrap::after {
  width: 22%;
  height: 0px;
}
.btn {
  width: 20px;
  height: 20px;
  color: #1686f5;
  background-color: rgb(240, 248, 255);
  border-color: rgb(148, 197, 238);
  font-size: 13px;
}
</style>
