<template>
  <Row>
    <Col :md="12">
      <div id="taskCreate" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="iOSGroupCreate" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="androidGroupCreate" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="typeCreate" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="configCreate" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="versionCreate" class="chart"></div>
    </Col>
    <Col :md="24">
      <div id="mtVersionSolve" class="chart"></div>
    </Col>
    <Col :md="24">
      <div id="mtCurrentSolve" class="chart"></div>
    </Col>
    <Col :md="24">
      <div id="dpVersionSolve" class="chart"></div>
    </Col>
    <Col :md="24">
      <div id="dpCurrentSolve" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="mtOnSolve" class="chart"></div>
    </Col>
    <Col :md="12">
      <div id="dpOnSolve" class="chart"></div>
    </Col>
  </Row>
</template>

<script>
  /* eslint-disable */
    import Highcharts from 'highcharts'
    export default {
      name: "clientRobustInfo",
      components: {
        Highcharts
      },
      data(){
        return{

        }
      },
      methods: {
        get_data(){
          this.$axios({
            method:"get",
            url:this.env.url+"client/robust/info"
          }).then((res) => {
            console.log(res.data);
            let message = res.data;
            columnChart("taskCreate", "健壮性task数量", message);
            columnStackChart("iOSGroupCreate", "iOS方向分组task数量", message.iOSGroupCreate);
            columnStackChart("androidGroupCreate", "Android方向分组task数量", message.androidGroupCreate);
            columnStackChart("typeCreate", "任务类型分组task数量", message.typeCreate);
            columnStackChart("configCreate", "配置文件分组task数量", message.configCreate);
            columnStackChart("versionCreate", "版本分组task数量", message.versionCreate);
            columnStackChart("mtVersionSolve", "美团版本解决状态分组", message.versionSolve.mt);
            columnStackChart("dpVersionSolve", "点评版本解决状态分组", message.versionSolve.dp);
            columnStackChart("mtOnSolve", "美团版本未解决分组", message.versionOnSolve.mt);
            columnStackChart("dpOnSolve", "点评版本未解决分组", message.versionOnSolve.dp);
            columnStackChart("mtCurrentSolve", "美团当前版本解决状态分组", message.currentVersionSolve.mt);
            columnStackChart("dpCurrentSolve", "点评当前版本解决状态分组", message.currentVersionSolve.dp);
          }).catch(function (error) {
            console.log(error)
          })
        }
      },
      mounted() {
        this.get_data();
      }
    }
    function columnChart(id, title, message) {
      Highcharts.chart( id,{
        chart: {
          type: 'column'
        },
        title: {
          text: title
        },
        yAxis: {
          min: 0,
          title: {
            text: "Task数量"
          }
        },
        xAxis: {
          type: "category"
        },
        tooltip: {
          crosshairs: true,
          shared: true
        },
        legend: {
          enabled: false
        },
        colors:["#348EED"],
        credits: {
          enabled: false
        },
        plotOptions:{
          column:{
            dataLabels: {
              enabled: true
            }
          }
        },
        series: [{"data":message.taskCreate}]
      })

    }
    function columnStackChart(id, title, message) {
      Highcharts.chart( id,{
        chart: {
          type: "column"
        },
        title: {
          text: title
        },
        yAxis: {
          min: 0,
          title: {
            text: "Task数量"
          },
          stackLabels: {
            enabled: true,
            style: {
              fontWeight: 'bold',
              color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
            }
          }
        },
        xAxis: {
          categories: message.categories
        },
        legend: {
          layout:"vertical",
          align: 'right',
          verticalAlign: 'top',
          backgroundColor: (Highcharts.theme && Highcharts.theme.background2) || 'white',
          borderColor: '#CCC',
          borderWidth: 1,
          shadow: false
        },
        colors:["#7CB5EC","#20B2AA","#FFA500","#81C2D6","#8192D6","#FFCCCC","#FF6666","#99CC66","#348EED"],
        credits: {
          enabled: false
        },
        plotOptions: {
          column: {
            maxPointWidth:50,
            stacking: "normal",
          }
        },
        series: message.series
      })

    }
</script>

<style scoped>
  .chart{
    height: 300px;
    padding-top: 10px;
    padding-left: 10px;
  }
</style>
