<template>
  <div>
    <Modal :value="showBugInfo" title="ShotBug" width = "40" @on-cancel="closeBugInfo">
      <Form ref="issueObj" :model="issueObj" class="bug-form" :rules="ruleValidate" :label-width="105">
        <Form-item label="主 题" prop="summary">
          <Input v-model="issueObj.summary"  clearable placeholder="请输入..."/>
        </Form-item>
        <Form-item label="描 述" prop="description">
          <Input v-model="issueObj.description" type="textarea" :rows="5" clearable/>
        </Form-item>
        <Form-item label="模 块" prop="moduleId" >
          <Select v-model="issueObj.moduleId" clearable >
            <Option v-for="(item,index) in moduleIdList" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
        </Form-item>
        <Form-item label="指 派 给" prop="assigned">
          <Input v-model="issueObj.assigned" clearable placeholder="请输入..."/>
        </Form-item>
        <Form-item label="创 建 者" prop="customField13034">
          <Input v-model="issueObj.customField13034" clearable placeholder="请输入..."/>
        </Form-item>
        <Form-item label= "抄 送 人" prop="cc">
          <Input v-model="issueObj.cc" clearable placeholder="请输入..."/>
        </Form-item>
        <Form-item label="关联提测" prop="customField14338">
          <Input v-model="issueObj.customField14338" clearable placeholder="eg：https://ones.sankuai.com/ones/product/20349/testdetail/6710736"/>
        </Form-item>
        <Form-item label="客户端发现阶段" prop="customField2353" >
          <Select v-model="issueObj.customField2353" clearable >
            <Option v-for="(item,index) in customField2353List" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
        </Form-item>
        <Form-item label="影响版本" prop="customField13214" >
          <Select v-model="issueObj.customField13214" clearable filterable >
            <Option v-for="(item,index) in customField13214List" :value="item.id" :key="index">{{ item.name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="严重程度" prop="severity">
          <Select v-model="issueObj.severity" clearable >
            <Option v-for="(item,index) in severityList" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
        </Form-item>
        <Form-item label="归属技术栈" prop="customField24354" >
          <Select v-model="issueObj.customField24354" clearable >
            <Option v-for="(item,index) in customField24354List" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
        </Form-item>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="createTask('issueObj')">提交</Button>
        <Button  @click="closeBugInfo" >关闭</Button>
      </div>
    </Modal>
  </div>

</template>

<script>
  /* eslint-disable */
  import event from "@/assets/event_bus";
  import { Bus } from '@/global/bus'
  export default {
    name: "ShotBug",
    props:["showBugInfo", "bugInfo"],
    data(){
      return{
        travelProject:'18888',
        hotelProject:'18762',
        moduleIdList:[],
        customField13214List:[],
        projectList: [
          {
            value :'18762',
            label :'Hotel'
          },
          {
            value :'18888',
            label :'Travel'
          }
        ],
        severityList: [
          {
            value: '0',
            label: 'Blocker'
          },
          {
            value: '1',
            label: 'Critical'
          },
          {
            value: '2',
            label: 'Major'
          },
          {
            value: '3',
            label: 'Normal'
          },
          {
            value: '4',
            label: 'Trivial'
          }
        ],
        customField24354List: [
          {
            value: '14',
            label: '服务端'
          },
          {
            value: '23989',
            label: 'Android-Native'
          },
          {
            value: '23988',
            label: 'iOS-Native'
          },
          {
            value: '21984',
            label: 'MRN'
          },
          {
            value: '648',
            label: 'Web'
          },
          {
            value: '137',
            label: 'H5'
          },
          {
            value: '1109873',
            label: '小程序-WMP'
          },
          {
            value: '1109875',
            label: '小程序-MMP'
          },
          {
            value: '1109874',
            label: '小程序-KSMP'
          },
          {
            value: '1103743',
            label: 'MAX'
          },
          {
            value: '1109629',
            label: 'Castor'
          },
          {
            value: '1100114',
            label: 'Flutter'
          },
          {
            value: '754',
            label: 'Picasso'
          },
          {
            value: '1109630',
            label: '数据算法'
          },
          {
            value: '20277',
            label: '硬件'
          },
          {
            value: '486',
            label: '其他'
          }
        ],
        customField2353List: [
          {
            value: '1109958',
            label: '评审阶段'
          },
          {
            value: '1109636',
            label: '自测联调'
          },
          {
            value: '1102530',
            label: '冒烟验收'
          },
          {
            value: "1121717",
            label: '新功能测试阶段'
          },
          {
            value: '373',
            label: '集成/回归测试'
          },
          {
            value: '1109637',
            label: '灰度/众测'
          }
        ],
        issueObj:{
          project:'',
          assigned : '',
          cc : '',
          severity : '3',
          customField23303 : Bus.userInfo.userLogin,
          summary : '【兼容性平台】',
          description : '',
          customField13214 :'',
          customField24354 :'21984',
          labels :[],
          customField2353 :'',
          customField13034 :'',
          moduleId :'',
          subtypeId :76476,
          customField14338:'',
        },
        ruleValidate:{
          project:[{ required: true, message: '项目不能为空', trigger: 'blur' }],
          assigned:[{ required: true, message: '指派给不能为空', trigger: 'blur' }],
          severity:[{ required: true, message: '严重程度不能为空', trigger: 'change' }],
          summary:[{ required: true, message: '主题不能为空', trigger: 'blur' }],
          description:[{ required: true, message: '描述不能为空', trigger: 'blur' }],
          customField13214:[{ required: true, type: "number",message: '影响版本不能为空', trigger: 'change' }],
          customField24354:[{ required: true, message: '归属技术栈不能为空', trigger: 'change' }],
          customField2353:[{ required: true, message: '发现阶段不能为空', trigger: 'change' }],
          customField13034:[{ required: true, message: '创建者不能为空', trigger: 'blur' }],
          moduleId:[{ required: true, message: '模块不能为空', trigger: 'change' }],
          customField14338:[{required: true, message: '关联提测不能为空', trigger: 'blur' }]
        }
      }
    },
    watch: {
      showBugInfo() {
        if (this.showBugInfo) {
          this.issueObj.project = this.bugInfo.jobBusiness === 'travel' ? this.travelProject : this.hotelProject;
          this.issueObj.description = this.bugInfo.bugDesc;
          // this.issueObj.customField14338 = this.bugInfo.relationTask;
          this.issueObj.summary = this.bugInfo.autoTestType === 'compatibility' ? "【视觉测试】" : "【短链路自动化】";
          this.issueObj.moduleId = this.bugInfo.jobBusiness === 'travel' ? '13770' : '13472';
          this.getModuleIdListAndLabels();
          this.getVersionList();
        }
      }
    },
    methods:{
      getModuleIdListAndLabels(){
        if (this.bugInfo.jobBusiness === 'travel' ) {
          this.moduleIdList = [
            {
              value: '13768',
              label: '美团'
            },

            {
              value: '13769',
              label: '点评'},

            {
              value: '13770',
              label: '美团点评'},

            {
              value: '13771',
              label: '小程序'}
          ];
          this.issueObj.labels[0]= this.bugInfo.autoTestType === 'compatibility' ? '18732' : '18731';
        }
        else {
          this.moduleIdList = [
            {
              value: '13470',
              label: '美团'
            },
            {
              value: '13471',
              label: '点评'},

            {
              value: '13472',
              label: '美团点评'},

            {
              value: '13473',
              label: '小程序'}
          ];
          this.issueObj.labels[0]= this.bugInfo.autoTestType === 'compatibility' ? '17429' : '17430';
        }
      },
      getVersionList(){
        this.$axios({
          method:"get",
          url:this.env.client_common_url +"client/ones/versions?projectId="+ this.issueObj.project+"&sn=500"
        }).then((res) => {
          let message = res.data;
          if(message.code===1000){
            this.customField13214List=message.data.data.items;
          }
        }).catch(function (error) {
          console.log(error)
        })
      },
      closeBugInfo(){
        this.$emit("closeBugInfo",0)
      },
      getSubtypeId() {
        let business = this.bugInfo.jobBusiness
        /**
         * 子类型，新到店模版subtypeId（124169）, 旧到店模版subtypeId（76476）
         * 最新更改记录：https://ones.sankuai.com/ones/product/17454/workItem/defect/detail/75733655?activeTabName=first
         */
        if (/travel|food|hotel/.test(business)) {
          return 124169
        } else {
          return 76476
        }
      },
      createTask(issueObj){
        this.$refs[issueObj].validate((valid) => {
          if (valid) {
            let idList = this.issueObj.customField14338.split("product/")[1].split("?")[0].split("/testdetail/");
            let projectId = idList[0];
            let submitTestId = idList[1];
            this.$Notice.info({title:"正在创建Task"});
            /**
             * 通过「发现方法 customField24355」判断是哪个自动化发现的bug
             * Shortlink 与 HyperJump 都选择 1110306
             * "customField24355":{"displayValue":"客户端自动化测试","id":1110306,"value":1110306}
             * 
             * 客户端自动化类型:
             * "customField24408":{"displayValue":"短链路自动化","id":1110315,"value":1110315}
             * "customField24408":{"displayValue":"视觉测试","id":1109633,"value":1109633}
             */
            const discoveryTypeId = 374;
            const clientAutotestTypeId = this.bugInfo.autoTestType === 'compatibility' ? 1109633 : 1110315;
            this.info = {
              projectId:projectId,
              issueObj:{
                "type": "DEFECT",
                "priority":2,
                "projectId": projectId,
                "createdBy":this.issueObj.customField13034,
                "assigned":this.issueObj.assigned,
                "cc":[this.issueObj.cc],
                "severity":this.issueObj.severity,
                "customField23303":this.issueObj.customField23303,
                "name": this.issueObj.summary,
                "desc": this.issueObj.description.replace(/\n/g,"<br>"),
                "customField13214":this.issueObj.customField13214,
                "customField24354":this.issueObj.customField24354,
                "customField23406": "1121717",
                "customField13215": discoveryTypeId,
                "customField24408": clientAutotestTypeId,
                "moduleId": this.issueObj.moduleId,
                "subtypeId": '276673'
              }
            };
            this.$axios({
              method:"post",
              data: this.info,
              url:this.env.client_common_url+"client/ones/issue"
            }).then((res) => {
              let message = res.data;
              if(message.code === 1000){
                if(message.data.code != 201){
                  let error =message.data.message;
                  let errorToStr = JSON.stringify(error);
                  console.log(errorToStr);
                  this.$Notice.error({
                      duration: 30,
                      closable: true,
                      render: h => {
                        return h('div', [
                          h('row', '提交失败,请检查!'),
                          h('row', errorToStr)
                        ])
                      }
                    }
                  )
                }else{
                  this.$Notice.success({
                    duration: 20,
                    closable: true,
                    render: h => {
                      return h('div', [
                        h('span', '提交成功'),
                        h('a', {
                          attrs: {
                            href: 'https://ones.sankuai.com/ones/product/'+message.data.data.projectId+'/workItem/defect/detail/'+message.data.data.id,
                            target: '_blank'
                          }
                        }, '缺陷详情')
                      ])
                    }
                  });
                  let issueId = message.data.data.id;
                  this.addRelationship(submitTestId,issueId)
                  this.$emit('bugSaveLabel',"bug问题")
                }
              }else {
                let error =message.get("message");
                let errorToStr = JSON.stringify(error);
                console.log(errorToStr);
                this.$Notice.error({
                    duration: 30,
                    closable: true,
                    render: h => {
                      return h('div', [
                        h('row', '提交失败!'),
                        h('row', errorToStr)
                      ])
                    }
                  }
                )
              }
            }).catch(function (error) {
              console.log(error)
            })
          }else {
            this.$Message.error('表单验证失败,请检查必填项!');
          }
        })
      },
      addRelationship(submitTestId,issueId){
        this.$axios({
          method:"post",
          data: {
            "submitTestId": submitTestId,
            "relationType": "DEFECT",
            "relationIds": [issueId]
          },
          url:this.env.client_common_url+"client/ones/testSubmit/relations"
        }).then((res) => {
          let message = res.data;
          console.log(message);
          if(message.code === 1000 && message.data.code ===200){
            this.$Notice.success({
              duration: 20,
              closable: true,
              render: h => {
                return h('div', [
                  h('span', '关联成功'),
                ])
              }
            });
            this.closeBugInfo();
          }else{
            if(message.code != 1000){
              console.log(message);
              let error =message.message;
              let errorToStr = JSON.stringify(error);
              console.log(errorToStr);
              this.$Notice.error({
                  duration: 30,
                  closable: true,
                  render: h => {
                    return h('div', [
                      h('row', '关联失败，请手动关联!'),
                      h('row', errorToStr)
                    ])
                  }
                }
              )
            }else{
              let error =message.data.message;
              let errorToStr = JSON.stringify(error);
              console.log(errorToStr);
              this.$Notice.error({
                  duration: 30,
                  closable: true,
                  render: h => {
                    return h('div', [
                      h('row', '关联失败,需手动关联!'),
                      h('row', errorToStr)
                    ])
                  }
                }
              )
            }
          }
        })
      }
    },
  }
</script>

<style scoped>
  .bug-form{
    padding-right: 30px
  }

  .bug-form .ivu-form-item {
    margin-bottom: 12px;
  }
</style>
