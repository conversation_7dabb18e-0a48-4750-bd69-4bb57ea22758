<template>
  <div>
    <Table max-height="800" :data="showData" :columns="tableColumns" @on-filter-change="columnFilterChange"></Table>
    <div style="margin: 10px;overflow: hidden" v-if="tableData.length >= 10">
      <div style="float: right;">
        <Page :total="tableData.length" :current="showIndex" :page-size="showSize" :page-size-opts="pageSizeOpts"
              show-sizer show-total  @on-change="changePage" @on-page-size-change="changePageSize"/>
      </div>
    </div>
  </div>
</template>

<script>
    export default {
      name: 'DataTable',
      props: ['tableData', 'tableColumns'],
      data () {
        return {
          showIndex: 1,
          showSize: 10,
          pageSizeOpts: [10, 15, 20, 30, 50, 100]
        }
      },
      watch: {
        tableData(newData) {
          this.showIndex = 1
        }
      },
      computed: {
        showData: function () {
          let showData = this.tableData
          if (this.showSize < this.tableData.length) {
            showData = this.tableData.slice((this.showIndex - 1) * this.showSize,
              (this.showIndex - 1) * this.showSize + this.showSize)
          }
          return showData
        }
      },
      methods: {
        changePage (index) {
          this.showIndex = index
        },
        changePageSize (size) {
          this.showSize = size
        },
        columnFilterChange () {
          this.changePageSize(100)
          this.changePage(1)
        }
      }
    }
</script>

<style scoped>

</style>
