<template>
  <div>
    <div class="text-show">
      {{logText}}
    </div>

  </div>

</template>

<script>
    /* eslint-disable */
    export default {
      name: "ClientLogView",
      data () {
        return {
          logText: "",
          logUrl:""
        }
      },
      mounted () {
        this.logUrl = this.$route.query.logUrl;
        this.getLogText();
      },
      methods: {
        getLogText () {
          this.$axios({
            method: "post",
            data: {
              "url": this.logUrl
            },
            headers:{
              "Content-Type": "application/json;charset=utf-8"
            },
            url: this.env.client_common_url+"client/shortLinkLog"
          }).then((res) => {
            let message = res.data;
            this.logText = "\n"+message.data
          }).catch(function (error) {
            console.log(error)
          })
        }
      }
    }
</script>

<style scoped>
.text-show{
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
  background: #2c3e50;
  color: #c5c8ce;
  padding-left: 15px;
  padding-right: 15px;
}
</style>
