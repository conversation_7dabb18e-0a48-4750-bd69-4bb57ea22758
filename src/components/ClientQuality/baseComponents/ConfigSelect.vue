<template>
  <div :style='{ background: "#ffff"} '>
   <Form :rules="formRules" :label-width="80">
    <Header :style='{ background: "#ffff", height: "100%",padding:"1px" }'>
      <Row>
        <Col class="select-location">
           <FormItem label="日期" prop="data">
            <DatePicker
              type="daterange"
              split-panels
              :clearable="false"
              format="yyyy-MM-dd"
              :value="nowDate"
              placeholder="请选择日期"
              style="
                margin-bottom: 0px;
                padding-left: 10px;
                padding-right: 20px;
                min-width: 237px
              "
              @on-change="changeTimeRange"
            ></DatePicker>
           </FormItem>
        </Col>
        <Col>
           <FormItem label="业务方向" prop="business">
          <BusinessSelect
            :clearSelect="clearSelect"
            style="
                margin-bottom: 0px;
                padding-left: 5px;
                padding-top: 5px;
                padding-right: 0px;
                margin-right: 0px
              "
            @businessChange="getBusinessID"
            @unitChange="getUnitID"
            @groupChange="getGroupID"
            :businessId='businessInfo'
            :groupId='groupInfo'
            :unitId='unitInfo'
          />
            </FormItem>
        </Col>
        <Col class="select-location">
          <FormItem label="产品" prop="product" style="margin-left: 2px; padding-left: 2px">
            <Select
              multiple
              :max-tag-count="2"
              v-model="productFilter"
              placeholder="请选择产品"
              style="margin-bottom: 0px; padding-left: 0px; padding-right: 5px; width: 237px;"
            >
              <Option
                v-for="item in productList"
                :value="item.id"
                :key="item.id"
                >{{ item.label }}</Option
              >
            </Select>
              </FormItem>
        </Col>
        <Col class="select-location" >
         <FormItem label="触发节点" prop="product" >
            <Select
              multiple
              :max-tag-count="2"
              v-model="nodeFilter"
              placeholder="请选择触发节点"
              style="margin-bottom: 0px; padding-left: 10px; padding-right: 10px;  min-width: 237px;"
            >
              <Option v-for="item in allNode" :value="item.id" :key="item.id">{{
                item.publishName + item.integrationName
              }}</Option>
            </Select>
             </FormItem>
        </Col>
        <Col class="select-location" v-if="tab === 'compatibility'|| tab === 'apiDiff'">
          <FormItem label="misID" prop="misID" style="margin-left: 2px; padding-left: 2px">
            <Select
              filterable
              multiple
              :max-tag-count="2"
              v-model="misIDFilter"
              placeholder="请选择misID"
              style="margin-bottom: 0px; padding-left: 0px; padding-right: 5px; width: 237px;"
            >
              <Option
                v-for="item in misIDList"
                :value="item"
                :key="item"
                >{{ item }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col class="select-location" v-if="tab === 'compatibility'|| tab === 'apiDiff'">
            <FormItem label="页面列表" prop="pageName" style="margin-left: 2px; padding-left: 2px">
              <Select
                filterable
                multiple
                :max-tag-count="2"
                v-model="pageSelections"
                placeholder="请选择页面"
                style="margin-bottom: 0px; padding-left: 0px; padding-right: 5px; width: 237px;"
              >
              <Option
                v-for="item in pageList"
                :value="item"
                :key="item.id"
              >{{ item.pageName }}</Option>
              </Select>
            </FormItem>
        </Col>
        <Col class="select-location">
           <FormItem >
            <Button
              type="primary"
              icon="ios-search"
              style="height: 30px; margin-bottom: 3px; padding-left: 10px"
              @click="
                getSelectInfo
              "
              >Search</Button
            >
           </FormItem>
        </Col>
      </Row>
    </Header>
    </Form>
  </div>
</template>
<script>
import BusinessSelect from '../baseComponents/BusinessSelect.vue'
export default {
  name: 'ConfigSelect',
  components: {BusinessSelect},
  props: {
    tab: {
      type: String,
      default: ''
    }
  },
  data: function () {
    return {
      formRules: {
        data: [{ required: true, message: '日期为必选哦', trigger: '_blank' }],
        business: [{ required: true, message: 'bg/bu/business至少选一个', trigger: '_blank' }],
        product: [{ required: false, trigger: '_blank' }],
        node: [{ required: false, trigger: '_blank' }]
      },
      nowDate: [], // 用户选择时间
      productFilter: [], // 存放用户选择的product
      nodeFilter: [], // 存放用户选择的node
      misIDFilter: [], // 存放用户选择的misID
      pageSelections: [], // 存放用户选择的页面对象
      startTime: '',
      endTime: '',
      groupInfo: 0, // 用户选择的groupId
      unitInfo: 0, // 用户选择的unitId
      businessInfo: 0, // 用户选择的businessId
      clearSelect: true,
      misIDList: [], // 存放从接口获取的misID数据
      pageList: [] // 存放从接口获取的页面列表数据
    }
  },
  mounted() {
    this.$store.dispatch('getProductList')
    this.$store.dispatch('getTestItemList')
    this.$store.dispatch('getNodeList')

    let endTime = this.formateTime(new Date())
    let startTime = this.formateTime(
    new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
    )
    this.nowDate = [startTime, endTime]
  },
  computed: {
    productList() {
      return this.$store.state.clientQuality.productList
    },
    allNode() {
      return this.$store.state.clientQuality.nodeList
    }
  },

  methods: {
    getBusinessID(bussinessId) {
      this.businessInfo = bussinessId
      this.misIDFilter = [] // 清空misID筛选内容
      this.pageSelections = [] // 清空页面列表筛选内容
      this.getMisIDList()
      this.getPageNameList()
    },
    getGroupID(groupId) {
      this.groupInfo = groupId
      this.misIDFilter = [] // 清空misID筛选内容
      this.pageSelections = [] // 清空页面列表筛选内容
      this.getMisIDList()
      this.getPageNameList()
    },
    getUnitID(unitId) {
      this.unitInfo = unitId
      this.misIDFilter = [] // 清空misID筛选内容
      this.pageSelections = [] // 清空页面列表筛选内容
      this.getMisIDList()
      this.getPageNameList()
    },
    changeTimeRange(value) {
      this.nowDate = value
    },
    formateTime: function (time) {
      return (
        time.getFullYear() +
        '-' +
        (time.getMonth() >= 9
          ? time.getMonth() + 1
          : '0' + (time.getMonth() + 1)) + '-' + (time.getDate() > 9 ? time.getDate() : '0' + time.getDate())
      )
    },
    getMisIDList() {
      return this.$axios({
        params: {
          businessId: this.businessInfo || undefined,
          buId: this.unitInfo || undefined,
          bgId: this.groupInfo || undefined
        },
        method: 'get',
        url: this.env.url + 'autoTestAnalytic/autoTest/misIdInfo'
      }).then(response => {
        let triggerResult = response.data || {}
        this.misIDList = triggerResult.misIdList || [] // 存储执行情况
      }).catch(error => {
        console.log(error)
        this.misIdList = []
      })
    },

    getPageNameList() {
      return this.$axios({
        params: {
          businessId: this.businessInfo || undefined,
          buId: this.unitInfo || undefined,
          bgId: this.groupInfo || undefined
        },
        method: 'get',
        url: this.env.url + 'autoTestAnalytic/autoTest/pageInfo'
      }).then(response => {
        let triggerResult = response.data || {}
        this.pageList = triggerResult.pageList || [] // 存储执行情况
      }).catch(error => {
        console.log(error)
        this.pageList = []
      })
    },

    getSelectInfo() {
      this.$emit('selectInfo', this.nowDate, this.unitInfo, this.groupInfo, this.businessInfo, this.productFilter, this.nodeFilter, this.misIDFilter, this.pageSelections)
    }
  }
}
</script>
