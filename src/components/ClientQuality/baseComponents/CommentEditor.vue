<template>
  <div class="comment">
    <div style="padding: 5px 3px;text-align: left">
      <Button type="text" size="small" shape="circle" @click="editComment" icon="ios-paper-outline">{{showText}}</Button>
    </div>
    <div style="overflow: auto;max-height: 300px">
      <div class="comment-show" v-if="isShow">
        <quill-editor key="commentShow" v-model="content" :options="showOption" @focus="onFocus"></quill-editor>
      </div>
      <quill-editor key="commentEdit" v-else v-model="content" :options="editorOption"></quill-editor>
    </div>
  </div>
</template>

<script>
    /* eslint-disable */
    import { quillEditor } from 'vue-quill-editor';
    export default {
      name: "CommentEditor",
      props:["commentPlaceHolder","comment"],
      data(){
        return{
          isShow:true,
          content:"",
          showOption:{
            placeholder:this.commentPlaceHolder,
            theme:"bubble",
            enabled:false,
            modules:{
              toolbar:false
            }
          },
          editorOption:{
            modules:{
              toolbar:[
                ["bold", "italic", "underline"],
                [{"color":[]},{"background":[]}],
                [{"list":"ordered"},{"list":"bullet"}],
                [{"header":1},{"header":2}],
                [{"indent":"-1"},{"indent":"+1"}],
                [{ "align": [] }],
                [{"size":["small",false,"large"]}],
                ["blockquote", "code-block"],
                [{"script":"sub"},{"script":"super"}],
                ["clean"]

              ]
            }
          }
        }
      },
      watch:{
        comment(value, _){
          this.content=value;
        }
      },
      computed:{
        showText:function () {
          let showText="";
          if (this.isShow){
            showText="编辑"
          } else {
            showText="保存"
          }
          return showText
        }
      },
      mounted(){
        this.content=this.comment;
      },
      methods:{
        onFocus(vueQuill){
          vueQuill.enable(false);
        },
        editComment(){
          this.isShow=!this.isShow;
          if(this.isShow){
            this.$emit("updateComment",this.content);
          }
        }
      }

    }
</script>

<style scoped>
  .comment{
    padding:15px 28px
  }
  .comment-show{
    border-left: 3px solid #42b983;
  }
</style>
