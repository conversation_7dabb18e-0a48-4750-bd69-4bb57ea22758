<template>
    <div style="padding-bottom: 28px;padding-top: 10px">
      <Card style="width: 320px;">
        <p slot="title" style="text-align: left">
          <Icon type="md-menu"></Icon>
          {{val.platform}}页面对比数据
        </p>
        <div style="padding-bottom: 28px">
          <div class="content-title-container">
            <p class="content-title">基准图数据</p>
          </div>
          <Row>
            <Col :md="8">已配置:</Col>
            <Col :md="3">
              <div class="content-text">
                {{val.schemaSimilar+val.schemaPage+val.increment}}
              </div>
            </Col>
            <Col :md="8">没有配置:</Col>
            <Col :md="3">
              <div class="content-text">
                {{val.basePic}}
              </div>
            </Col>
          </Row>
          <div class="content-title-container">
            <p class="content-title">主结构对比计算</p>
          </div>
          <Row>
            <Col :md="8">页面相同:</Col>
            <Col :md="3">
              <div class="content-text">
                {{val.schemaSimilar}}
              </div>
            </Col>
            <Col :md="8">页面不一致:</Col>
            <Col :md="3">
              <div class="content-text">
                {{val.schemaPage}}
              </div>
            </Col>
            <Col :md="8">增量对比:</Col>
            <Col :md="3">
              <div class="content-text">
                {{val.increment}}
              </div>
            </Col>
          </Row>
        </div>

      </Card>
    </div>
</template>

<script>
    export default {
      name: 'ClientImageInfo',
      props: ['val']
    }
</script>

<style scoped>
  .content-title{
    text-align: left;
    font-size: small;
    font-weight: bold;
    padding-left: 12px;
    padding-bottom: 5px
  }
  .content-title-container{
    padding-top: 10px;
    border-left: 3px solid #42b983
  }
  .content-text{
    font-size: 15px;
    font-weight: bold;
    color: #2b85e4;
  }
</style>