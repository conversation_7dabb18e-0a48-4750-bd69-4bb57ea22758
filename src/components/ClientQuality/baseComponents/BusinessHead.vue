<template>
  <Card class="layout-header-bar">
    <Icon type="ios-home" size="18"></Icon>
    <span v-if="businessInfo">
      <router-link :to="{ path: businessPath}" tag="a" target="_self">
        {{ busniessName }}
      </router-link>
    </span>
    <span v-if="pageInfo">
      <span>> </span>
      <router-link :to="{path:pagePath}" tag="a" target="_self">
        {{pageInfo.name}}
      </router-link>
    </span>
    <span v-if="sceneInfo">
      <span>> {{sceneInfo.name}}</span>
    </span>
    <span v-if="apiInfo">
      <span>> {{apiInfo.apiPath}}</span>
    </span>
    <span v-if="abInfo">
      <span>> {{abInfo.abKey}}</span>
    </span>
  </Card>
</template>
<script>
export default {
  name: 'BusinessHead',
  props: ['businessInfo', 'pageInfo', 'sceneInfo', 'apiInfo', 'abInfo'],
  computed: {
    businessPath: function () {
      return '/client/businessDetail/' + this.businessInfo.id + '/page'
    },
    busniessName: function () {
      if (this.businessInfo.label) {
        return this.businessInfo.label
      } else {
        return this.businessInfo.business
      }
    },
    pagePath: function () {
      return '/client/page/' + this.pageInfo.id + '/scene'
    }
  }
}
</script>
<style scoped>
.layout-header-bar {
  font-size: 24px;
  font-weight: bold;
  border: 0px;
  height: 50px;
}
</style>