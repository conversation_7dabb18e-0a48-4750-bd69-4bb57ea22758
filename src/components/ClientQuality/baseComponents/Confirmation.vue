<template>
  <Modal v-model="confirmInfo.show" width="320">
    <p slot="header" style="color:#f60;text-align:center">
      <Icon type="ios-information-circle"></Icon>
      {{confirmInfo.title}}
    </p>
    <div style="text-align:center">
      {{confirmInfo.message}}
    </div>
    <div slot="footer">
      <Button type="error" size="large" @click="confirm">确认</Button>
    </div>
  </Modal>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'Confirmation',
  props: ['confirmInfo'],
  data() {
    return {}
  },
  methods: {
    confirm() {
      this.confirmInfo.show = false
      Bus.$emit('confirmDelete')
    }
  }
}
</script>