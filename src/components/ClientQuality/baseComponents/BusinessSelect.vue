<template>
  <Row>
    <Col class="select-location" span="8">
    <Select placeholder="选择 BG ..." v-model="groupId" :clearable="clearSelect" transfer filterable style="padding-right: 10px;">
      <Option v-for="item in groupList" :value="item.id" :key="item.id">{{ item.label }}</Option>
    </Select>
    </Col>
    <Col class="select-location" span="8">
    <Select placeholder="选择BU ..." v-model="unitId" :clearable="clearSelect" transfer filterable style="padding-right: 10px;">
      <Option v-for="item in unitList" :value="item.id" :key="item.id">{{ item.label }}</Option>
    </Select>
    </Col>
    <Col class="select-location" span="8">
    <Select placeholder="选择业务方向 ..." v-model="businessId" :clearable="clearSelect" transfer filterable style="padding-right: 10px;">
      <Option v-for="item in businessList" :value="item.id" :key="item.id">{{ item.label }}</Option>
    </Select>
    </Col>
  </Row>
</template>
<script>
export default {
  name: 'BusinessSelect',
  props: {
    clearSelect: {
      type: Boolean,
      default: false
    }
  },
  data: function () {
    return {
      businessId: 0,
      groupId: 0,
      unitId: 0,
      unitList: [],
      businessList: []
    }
  },
  watch: {
    groupId() {
      if (!this.groupId) {
        this.unitList = []
        this.groupId = 0
        this.unitId = 0
        this.businessId = 0
        this.$emit('groupChange', this.groupId)
        return
      }
      this.unitList = []
      let unitMap = this.businessGroup[this.groupId.toString()].unitMap
      this.unitList = Object.values(unitMap)
      this.$emit('groupChange', this.groupId)
    },
    unitId() {
      if (!this.unitId) {
        this.businessList = []
        this.unitId = 0
        this.businessId = 0
        this.$emit('unitChange', this.unitId)
        return
      }
      this.businessList =
        // eslint-disable-next-line standard/computed-property-even-spacing
        this.businessGroup[this.groupId.toString()].unitMap[
        this.unitId.toString()
        ].businessList
      this.$emit('unitChange', this.unitId)
    },
    businessId() {
      if (this.businessId !== 0) {
        this.$emit('businessChange', this.businessId)
      }
    }
  },
  mounted: function () {
    this.$store.dispatch('getBusinessGroup')
  },
  computed: {
    businessGroup() {
      return this.$store.state.clientQuality.businessGroup
    },
    groupList() {
      return Object.values(this.businessGroup)
    }
  }
}
</script>
<style scoped>
.select-location {
  text-align: center;
  margin-top: -5px;
}
</style>