<template>
  <Drawer :closable="false" v-model="isShow" width="40">
    <Icon type="md-create" size=16 />
    <span class="title">快速编辑</span>
    <Button class="edit-submit" type="primary" size="small" @click.native="submit" :disabled="isSubmit">确定</Button>
    <Divider />
    <Form :model="editInfo" :rules="ruleValidate" ref="refEditInfo">
      <FormItem label="名称" prop="name">
        <Input v-model="editInfo.name" placeholder="请输入名称" />
      </FormItem>
      <FormItem label="归属于" prop="parent">
        <Select v-model="editInfo.parent" filterable transfer :disabled="type==='page'">
          <Option v-for="item in parentList" :value="item.id" :key="item.id">{{item.label}}{{item.name}}</Option>
        </Select>
      </FormItem>
      <FormItem label="优先级" prop="priority">
        <Select v-model="editInfo.priority" filterable transfer>
          <Option v-for="item in priorityList" :value="item.id" :key="item.id">{{item.label}}</Option>
        </Select>
      </FormItem>
      <FormItem label="在线状态" required>
        <i-switch v-model="editInfo.online" :true-value="1" :false-value="0" />
      </FormItem>
    </Form>

  </Drawer>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'EditDrawer',
  props: [],
  data() {
    return {
      isSubmit: false,
      isShow: false,
      type: '',
      sceneType: '',
      editInfo: { name: '', parent: 0, priority: 2, online: 1 },
      businessId: 0,
      priorityList: [
        { id: 1, label: 'P1' },
        { id: 2, label: 'P2' },
        { id: 3, label: 'P3' },
        { id: 4, label: 'P4' }
      ],
      ruleValidate: {
        name: [
          {
            required: true,
            type: 'string',
            message: '请填写名称',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value.trim() === '') {
                return callback(new Error('请填写名称'))
              } else {
                callback()
              }
            }
          }
        ],
        parent: [
          {
            required: true,
            message: '请选择归属',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === 0 || Number(value) === 0) {
                return callback(new Error('请选择归属'))
              } else {
                callback()
              }
            }
          }
        ],
        priority: [
          {
            required: true,
            message: '请选择优先级',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === 0 || Number(value) === 0) {
                return callback(new Error('请选择归属'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  created() {
    Bus.$on('editPage', this.showEdit)
  },
  mounted() {
    this.$store.dispatch('getBusinessByMisId', Bus.userInfo.userLogin)
  },
  methods: {
    showEdit(editInfo) {
      this.isShow = true
      this.type = editInfo.type
      if (editInfo.data !== undefined) {
        this.editInfo = editInfo.data
      }
      if (editInfo.type === 'scene') {
        let query = {
          isOnline: 1,
          pageSize: 500,
          pageCount: 1,
          isDetail: 0,
          businessId: editInfo.businessId
        }
        this.sceneType = editInfo.sceneType
        this.businessId = editInfo.businessId
        this.$store.commit('setPageFilter', query)
        this.$store.dispatch('getPageListById')
      }
    },
    submit() {
      this.$refs['refEditInfo'].validate(async (valid) => {
        if (valid) {
          this.isSubmit = true
          if (this.type === 'page') {
            this.editInfo.businessId = this.editInfo.parent
            await this.$store.dispatch('setPageInfo', this.editInfo)
            this.isShow = false
            this.$store.dispatch('getPageListById')
          } else if (this.type === 'scene') {
            this.editInfo.pageId = this.editInfo.parent
            await this.$store.dispatch('setSceneInfo', this.editInfo)
            this.isShow = false
            if (this.sceneType === 'defalut') {
              this.$store.dispatch('getSceneList')
              this.$store.dispatch('getDefalutScenes', this.businessId)
            } else {
              this.$store.dispatch('getSceneList')
            }
          }
          this.isSubmit = false
          this.editInfo = { name: '', parent: 0, priority: 2, online: 1 }
        } else {
        }
      })
    }
  },
  computed: {
    parentList() {
      let parentList = []
      if (this.type === 'page') {
        parentList = this.$store.state.clientQuality.userBizList
      } else if (this.type === 'scene') {
        parentList = this.$store.state.clientQuality.pageList.pageList
      }
      return parentList
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: 500;
  vertical-align: middle;
}
.edit-submit {
  float: right;
  margin-right: 20px;
}
</style>