<template>
  <div style="padding: 28px">
    <Card style="width: 320px;">
      <p slot="title" style="text-align: left">
        <Icon type="md-menu"></Icon>
        {{val.group}}
      </p>
      <div class="content-square">
        <Row>
          <p class="content-title">质量数据</p>
          <Col :md="6" style="padding-left: 5px">需求数量:</Col>
          <Col :md="4"><Badge :count="val.quality.requirement" type="primary"></Badge></Col>
          <Col :md="6">开发Pd:</Col>
          <Col :md="2"><Badge :count="val.quality.developmentPd" type="primary"></Badge></Col>
        </Row>
        <Row>
          <Col :md="6">后台先行:</Col>
          <Col :md="18"><Progress :percent="val.quality.serverFirst"/></Col>
        </Row>
      </div>
      <div v-if="val.compatibility" class="content-square">
        <Row>
          <p class="content-title">兼容性</p>
          <Col :md="8" style="padding-left: 8px">Schema数量:</Col>
          <Col :md="4"><Badge :count="val.comp_info.schema" type="primary"></Badge></Col>
          <Col :md="3" style="padding-left: 8px">RN:</Col>
          <Col :md="3"><Badge :count="val.comp_info.RNSchema" type="primary"></Badge></Col>
        </Row>
        <Row>
          <Col :md="4">P0:</Col>
          <Col :md="20"><Progress :percent='val.comp_info.p0'/></Col>
        </Row>
        <Row>
          <Col :md="4">P1:</Col>
          <Col :md="20"><Progress :percent='val.comp_info.p1'/></Col>
        </Row>
      </div>
      <div v-if="val.robust">
        <Row>
          <p class="content-title">健壮性</p>
          <Col :md="8" style="padding-left: 8px">Schema数量:</Col>
          <Col :md="4"><Badge :count="val.robust_info.schema" type="primary"></Badge></Col>
          <Col :md="3" style="padding-left: 8px">RN:</Col>
          <Col :md="3"><Badge :count="val.robust_info.RNSchema" type="primary"></Badge></Col>
        </Row>
        <Row>
          <Col :md="4">P0:</Col>
          <Col :md="20"><Progress :percent='val.robust_info.p0'/></Col>
        </Row>
        <Row>
          <Col :md="4">P1:</Col>
          <Col :md="20"><Progress :percent='val.robust_info.p1'/></Col>
        </Row>
      </div>

      <div slot="extra">
        <Tag color="#348EED">质量报告</Tag>
        <Tag color="#20B2AA" v-if="val.compatibility">兼容性</Tag>
        <Tag color="#FF6666" v-if="val.robust">健壮性</Tag>
      </div>
    </Card>
  </div>
</template>

<script>
    export default {
      name: 'ClientGroup',
      props: ['val']
    }
</script>

<style scoped>
  .content-title{
    text-align: left;
    font-size: small;
    font-weight: bold;
    padding-left: 12px;
    padding-bottom: 5px
  }
  .content-square{
    padding-bottom: 28px;
  }
</style>
