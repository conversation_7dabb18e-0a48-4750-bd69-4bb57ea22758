<template>
  <div class="layout">
    <Layout>
      <Header :style="{ background: '#ffff', height: '60px'}">
        <Row type="flex">
          <Col order="1" span="1">
          <a href="/client"><img class="layout-logo" src="/static/img/ccd_logo.png" /></a>
          </Col>
          <Col order="2" span="3">
          <div style="margin-top: -5px">
            <span style="text-align: center; font-size:16px;font-weight:700;">客户端持续交付</span>
          </div>
          </Col>
          <Col order="3" span="12">
          <BusinessSelect @businessChange="getbusinessId"></BusinessSelect>
          </Col>
          <Col order="6" span="2" offset="4" style="
              height: 50px;
              margin-top: -5px;
              text-align: center;
              font-weight: bolder;
            ">
          <div style="margin-left: 50%; font-weight: bolder">
            <a href="https://km.sankuai.com/page/809767636" target="_blank">文档</a>
          </div>
          </Col>
          <Col order="7" span="2">
          <div class="layout-nav" style="margin-left: 50%; width: 100%; margin-right: 0">
            <div id="userInfo" style="display: flex; ">
              <Dropdown @on-click="exitSystem">
                <div style="margin-top: 2px">
                  {{ userInfo.userName }}<Icon type="ios-arrow-down" style="padding-left: 5px"></Icon>
                </div>
                <DropdownMenu slot="list">
                  <DropdownItem style="text-align: center" name="quit">
                    <Icon type="md-exit" style="padding-right: 5px" />退出
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          </Col>
        </Row>
      </Header>
    </Layout>
  </div>
</template>

<script>
import { Bus } from '@/global/bus'
import BusinessSelect from './BusinessSelect'

let userInfo = {
  userId: '',
  userName: 'Dev',
  userLogin: 'Dev',
  userUrl: ''
}

Bus.$on('refreshUserInfo', function (UserInfo) {
  userInfo = UserInfo
})

export default {
  components: { BusinessSelect },
  data: function () {
    return {
      userInfo: userInfo
    }
  },
  methods: {
    getbusinessId(data) {
      if (!(data === undefined && data !== 0)) {
        let routeData = this.$router.resolve({
          path: '/client/businessDetail/' + data + '/page'
        })
        window.open(routeData.href, '_blank')
      }
    },
    refreshUserInfo(userInfo) {
      this.userName = userInfo.userName
    },
    exitSystem(value) {
      console.log(value)
      if (value === 'quit') {
        // 退出
        window.location.href = window.location.href
        Bus.ssoWeb.logout()
      }
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.layout {
  background: #ffff;
  position: relative;
  /*overflow: hidden;*/
}
.layout-logo {
  position: relative;
  float: left;
  top: 0;
  left: 0;
  text-align: center;
  margin-left: 0;
  height: 48px;
  width: 45px;
  padding-top: 8px;
  cursor: pointer;
}
.layout-nav {
  text-align: right;
  width: 50px;
  margin: 0 auto;
  margin-right: 0;
  font-weight: bolder;
  margin-top: -8px;
  font-size: 14px;
}
</style>
