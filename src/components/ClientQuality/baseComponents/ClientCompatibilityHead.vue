<template>
  <div>
    <Layout>
      <Header id="header" :style="{ background: 'white', height: '55px'}">
        <Row type="flex">
          <Col order="1" :xs="6" :sm="6" :md="1" :lg="1">
          <a href="/microscope/jobList"><img class="layout-logo" src="/static/img/hyperjump_logo.png" style="
                    text-align: center;
                    margin-left: 0;
                    height: 50px;
                    width: 73px;
                    padding-top: 6px;
                    cursor: pointer;
                  " /></a>

          </Col>
          <Col order="2" :xs="6" :sm="6" :md="4" :lg="4" style="text-align: left;">
            <div style="margin-top: -5px;padding-left: 30px">
              <span style="text-align: center; font-size:larger;font-weight:900;color:#606979">HyperJump视觉测试</span>
              <Divider type="vertical" style="height: 30px"/>
            </div>
          </Col>
          <Col order="3" :xs="15" :sm="15" :md="12" :lg="12" style="
              height: 50px;
              margin-top: -5px;
              text-align: left;
              font-weight: bolder;
              padding-left: 10px">
            <div style="font-weight: normal;font-size:larger;">
              <a v-bind:class="{'link-item': true, 'link-item-active': $route.path === '/compatibilityUserConfig'}" href="/compatibilityUserConfig">
                <span><Icon type="ios-settings-outline" size="20"/></span> 配置管理&nbsp;</a>
              <a v-bind:class="{'link-item': true, 'link-item-active': $route.path === '/clientScheme'}" href="/clientScheme">
                <span><Icon type="ios-paper-outline" size="20"/></span> 页面管理&nbsp;</a>
              <a v-bind:class="{'link-item': true, 'link-item-active': $route.path.indexOf('/microscope') > -1}" href="/microscope/jobList">
                <span><Icon type="ios-list" size="20"/></span> Job列表&nbsp;</a>
              <a v-bind:class="{'link-item': true, 'link-item-active': $route.path === '/visionComponents'}" href="/visionComponents">
                <span><Icon type="ios-appstore-outline" size="20"/></span> 应用市场&nbsp;</a>
              <a v-bind:class="{'link-item': true, 'link-item-active': $route.path === '/deviceOperation'}" href="/deviceOperation">
                <span><Icon type="ios-construct-outline" size="20"/></span> 设备运维&nbsp;</a>
            </div>
          </Col>
          <Col order="5" offset="2" :xs="0" :sm="0" :md="3" :lg="3" style="
              height: 50px;
              margin-top: -5px;
              text-align: center;
              font-weight: bolder;
            ">
            <div style="margin-left: 25%; font-weight: bolder">
              <Divider type="vertical" style="height: 30px"/>
              <Dropdown @on-click="techHelp">
                <a href="javascript:void(0)" style="color: #606979">
                  <span><Icon type="ios-help-circle-outline" size="16"/></span> 文档 </a>
                <Icon type="ios-arrow-down" style="padding-left: 5px" />
                <DropdownMenu slot="list">
                  <DropdownItem style="text-align: left" name="hyperjump">
                    <Icon type="ios-information-circle-outline" style="padding-right: 5px" />HyperJump简介
                  </DropdownItem>
                  <DropdownItem style="text-align: left" name="illustrate">
                    <Icon type="ios-book-outline" style="padding-right: 5px" />HyperJump快速开始
                  </DropdownItem>
                  <DropdownItem style="text-align: left" name="github">
                    <Icon type="logo-github" style="padding-right: 5px" />视觉测试开源项目 - Vision
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </Col>
          <Col order="6" :xs="0" :sm="0" :md="3" :lg="2">
            <div class="layout-nav" style="margin-left: 25%; width: 100%; margin-right: 0">
              <div id="userInfo" style="display: flex; color: #606979">
                <Dropdown trigger="click" @on-click="exitSystem">
                  <a href="javascript:void(0)" style="color: #606979;">
                    <div style="margin-top: 2px"><span><Icon type="ios-contact-outline" size="25"/></span>
                      {{ userInfo.userName }}<Icon type="ios-arrow-down" style="padding-left: 5px" />
                    </div>
                  </a>
                  <DropdownMenu slot="list">
                    <DropdownItem style="text-align: center" name="quit">
                      <Icon type="md-exit" style="padding-right: 5px" />退出
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
            </div>
          </Col>
        </Row>
      </Header>
    </Layout>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import axios from "axios";
let userInfo = {
  userId: "",
  userName: "Dev",
  userLogin: "Dev",
  userUrl: "",
};

Bus.$on("refreshUserInfo", function (UserInfo) {
  userInfo = UserInfo;
});

export default {
  data: function () {
    return {
      filter: "",
      userInfo: userInfo,
      loading: false,
      topNotice: "",
      topNoticeEnable: false,
      // businessId: 0
    };
  },
  methods: {
    refreshUserInfo(userInfo) {
      this.userName = userInfo.userName;
    },
    jumper(value) {
      if (value) {
        let index = value.indexOf("#-#");
        let url = value.substring(0, index);
        let type = parseInt(value.substring(index + 3, index.length));
        this.filter = "";
        this.value = "";
        if (type === 1) {
          if (url.toString() !== "/") {
            url = "/" + url.toString();
          }
          this.$router.push({ path: url });
        } else {
          window.open(url);
        }
      }
    },
    exitSystem(value) {
      console.log(value);
      if (value === "quit") {
        // 退出
        window.location.href = window.location.href;
        Bus.ssoWeb.logout();
      }
    },
    techHelp(value) {
      console.log(value);
      if (value === "github") {
        window.open('https://github.com/Meituan-Dianping/vision-ui');
      } else if (value === "illustrate") {
        window.open('https://km.sankuai.com/page/247943009');
      } else if (value === "hyperjump") {
        window.open('https://km.sankuai.com/page/305288166');
      }
    },
  },
  mounted: function () {
    this.$store.dispatch('getBusinessList')
  },
  computed: {
    businessList() {
      return this.$store.state.clientQuality.businessList
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.layout {
  background: #020031;
  position: relative;
  /*overflow: hidden;*/
}
.layout-logo {
  height: 50px;
  width: 50px;
  position: relative;
  float: left;
  top: 0;
  left: 0;
}
.layout-nav {
  text-align: right;
  width: 50px;
  margin: 0 auto;
  margin-right: 0;
  font-weight: bolder;
  margin-top: -8px;
  font-size: 14px;
}
.link-item {
  color: #606979;
  margin-right: 20px;
  padding-bottom: 18px;
}
.link-item:hover {
  color: #2d8cf0;
}
.link-item-active {
  color: #2d8cf0;
  border-bottom: 2px solid #2d8cf0;
}
</style>
