<template>
  <Drawer :closable="false" v-model="isShow" width="40">
    <Icon type="md-create" size=16 />
    <span class="title">快速编辑</span>
    <Button class="edit-submit" type="primary" size="small" @click.native="submit" :disabled="isSubmit">确定</Button>
    <Divider />
    <Form :model="editInfo" :rules="ruleValidate" ref="refEditInfo">
      <FormItem class="nameTag" label="名称" prop="name">
        <br>
        <Tag v-for="item in editInfo.names" :key="item">{{item}}</Tag>
      </FormItem>
      <FormItem label="归属于" prop="parent">
        <Tag color="orange">提示：修改页面会影响继承的产品信息</Tag>
        <Select v-model="editInfo.parent" @on-change="isSubmitDisabled()" filterable transfer>
          <Option v-for="item in parentList" :value="item.id" :key="item.id">{{item.label}}{{item.name}}</Option>
        </Select>
      </FormItem>
      <FormItem label="优先级" prop="priority">
        <Select v-model="editInfo.priority" @on-change="isSubmitDisabled()" filterable transfer>
          <Option v-for="item in priorityList" :value="item.id" :key="item.id">{{item.label}}</Option>
        </Select>
      </FormItem>
      <FormItem label="在线状态" required>
        <RadioGroup v-model="border">
          <Radio label="remain" border></Radio>
          <Radio label="online" border></Radio>
          <Radio label="offline" border></Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <Modal v-model="loading" :closable="false" :mask-closable="false">
      <template #header>
        <p style="color:#f60;text-align:center">
          <Icon type="ios-information-circle"></Icon>
          <span>注意</span>
        </p>
      </template>
      <p>数据同步中，本提示框消失前请勿关闭本窗口...</p>
      <template #footer>
        <Button disabled>请等待</Button>
      </template>
    </Modal>
  </Drawer>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'EditDrawerList',
  props: [],
  data() {
    return {
      isSubmit: true,
      isShow: false,
      sceneType: '',
      editInfo: { names: [] },
      businessId: 0,
      priorityList: [
        { id: 1, label: 'P1' },
        { id: 2, label: 'P2' },
        { id: 3, label: 'P3' },
        { id: 4, label: 'P4' }
      ],
      ruleValidate: {
        parent: [
          {
            required: true,
            message: '请选择归属',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === 0 || Number(value) === 0) {
                return callback(new Error('请选择归属'))
              } else {
                callback()
              }
            }
          }
        ],
        priority: [
          {
            required: true,
            message: '请选择优先级',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === 0 || Number(value) === 0) {
                return callback(new Error('请选择归属'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      loading: false,
      border: 'remain'
    }
  },
  created() {
    Bus.$on('editPageList', this.showEdit)
  },
  mounted() {
    this.$store.dispatch('getBusinessByMisId', Bus.userInfo.userLogin)
  },
  methods: {
    showEdit(editInfo) {
      this.isShow = true
      this.isSubmit = true
      if (editInfo.data !== undefined) {
        this.editInfo = editInfo.data
      }
      let query = {
        isOnline: 1,
        pageSize: 500,
        isDetail: 0,
        businessId: editInfo.businessId
      }
      this.sceneType = editInfo.sceneType
      this.businessId = editInfo.businessId
      this.$store.commit('setPageFilter', query)
      this.$store.dispatch('getPageListById')
    },
    submit() {
      this.$refs['refEditInfo'].validate(async (valid) => {
        if (valid) {
          this.isSubmit = true
          this.loading = true
          this.editInfo.pageId = this.editInfo.parent
          if (this.border === 'online') {
            this.editInfo.online = 1
          }
          if (this.border === 'offline') {
            this.editInfo.online = 0
          }
          await this.$store.dispatch('setSceneListInfo', this.editInfo)
          this.isShow = false
          if (this.sceneType === 'defalut') {
            this.$store.dispatch('getSceneList')
            this.$store.dispatch('getDefalutScenes', this.businessId)
          } else {
            this.$store.dispatch('getSceneList')
          }
          this.loading = false
          this.border = 'remain'
          this.editInfo = { names: [], parent: 0, priority: 0 }
        }
      })
    },
    isSubmitDisabled() {
      if (
        this.editInfo.names.length > 0 &&
        this.editInfo.parent !== undefined &&
        this.editInfo.priority !== undefined
      ) {
        this.isSubmit = false
      }
    }
  },
  computed: {
    parentList() {
      let parentList = []
      parentList = this.$store.state.clientQuality.pageList.pageList
      return parentList
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 14px;
  color: #464c5b;
  font-weight: 500;
  vertical-align: middle;
}
.edit-submit {
  float: right;
  margin-right: 20px;
}
.nameTag /deep/ .ivu-form-item-label {
  padding: 0 0 0 0;
}
</style>