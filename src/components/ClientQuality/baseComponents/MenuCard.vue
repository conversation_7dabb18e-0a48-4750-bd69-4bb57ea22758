<template>
  <a :href="url" target="_blank" style="color: #333333; text-decoration: none">
    <Card class="toolchain-component-homepage" style="margin-left: 0;border-left: 3px solid #dcdee2">
      <div style="text-align:center;cursor: pointer;">
        <Icon :class="icon" size="30" :style="iconStyle"/>
        <p style="font-weight: bolder">{{title}}</p>
      </div>
    </Card>
  </a>
</template>

<script>
  export default {
    name: 'menu-card',
    props: {
      title: {
        type: String,
        default: '敬请期待'
      },
      icon: {
        type: String,
        default: 'ivu-icon ivu-icon-md-clipboard'
      },
      color: {
        type: String,
        default: '#1890ff'
      },
      url: {
        type: String,
        default: '#'
      }
    },
    data: function () {
      return {
        iconStyle: {}
      }
    },
    mounted: function () {
      this.iconStyle = {
        color: this.color
      }
    }
  }
</script>

<style scoped>
  .toolchain-component-homepage {
    background-color: #ffffff;
    border-radius: 4px;
    font-size: 14px;
    height: 90px;
    margin-left: 14px;
    margin-right: 14px;
  }
</style>
