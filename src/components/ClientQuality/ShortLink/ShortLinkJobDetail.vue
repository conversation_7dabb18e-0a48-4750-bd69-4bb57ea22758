<template>
  <div>
    <Row>
      <Col :md="12">
        <h5  style="padding: 20px 0" align="left">运行信息</h5>
      </Col>
      <Col :md="12">
        <h5 class="title" style="padding: 20px 0" align="left">触发信息</h5>
      </Col>
    </Row>
    <Row>
      <Col :md="6">
        <div class="content-title-container">
          <Row>
            <Col :md="6" class="content-title">成功率:</Col>
            <Col :md="18">
              <div v-if="jobDetail.pass_rate" align="left">{{jobDetail.pass_rate}}</div>
              <div v-else align="left">暂无</div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">Job地址:</Col>
            <Col :md="18">
              <div v-if="jobDetail.build_url" align="left">
                <a :href="jobDetail.build_url" target="_Blank">{{jobDetail.job_name}}</a>
              </div>
              <div v-else align="left">暂无</div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">APP链接:</Col>
            <Col :md="18">
              <div v-if="jobDetail.app_url" align="left">
                <a :href="jobDetail.app_url" target="_Blank">
                {{jobDetail.version}}-{{jobDetail.app_build}}
                </a>
              </div>
              <div v-else align="left">暂无</div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col :md="6">
        <div class="content-title-container">
          <Row>
            <Col :md="6" class="content-title">平台:</Col>
            <Col :md="18">
              <div v-if="triggerMsg.product" align="left">{{triggerMsg.product.label}}</div>
              <div v-else align="left">{{jobDetail.platform}}</div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">开始时间:</Col>
            <Col :md="18">
              <div v-if="jobDetail.start_time" align="left">{{jobDetail.start_time}}</div>
              <div v-else align="left">暂未开始运行</div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">结束时间:</Col>
            <Col :md="18">
              <div v-if="jobDetail.end_time" align="left">{{jobDetail.end_time}}</div>
              <div v-else align="left">暂未结束运行</div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col :md="6">
        <div class="content-title-container">
          <Row v-if="triggerMsg.business">
            <Col :md="6" class="content-title">业务方向:</Col>
            <Col :md="18">
              <div align="left">
                {{triggerMsg.business.business}}
              </div>
            </Col>
          </Row>
          <Row v-if="triggerMsg.realNode">
            <Col :md="6" class="content-title">触发方式:</Col>
            <Col :md="18">
              <div align="left">
                {{triggerMsg.realNode.publishName}}{{triggerMsg.realNode.integrationName}}
              </div>
            </Col>
          </Row>
          <Row v-if="jobDetail.version">
            <Col :md="6" class="content-title">版本:</Col>
            <Col :md="18">
              <div align="left">
                {{jobDetail.version}}
              </div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col :md="6">
        <div class="content-title-container">
          <Row v-if="triggerMsg.flow && triggerMsg.flow.taskDetail">
            <Col :md="6" class="content-title">Flow链接:</Col>
            <Col :md="18">
              <div align="left" style="overflow:hidden;white-space: nowrap;text-overflow:ellipsis">
                <a v-if="triggerMsg.flow.taskDetail.title" :href="triggerMsg.flow.flowUrl" target="_Blank">
                  {{triggerMsg.flow.taskDetail.title}}
                </a>
                <a v-else :href="triggerMsg.flow.flowUrl" target="_Blank">Flow链接</a>
              </div>
            </Col>
          </Row>
          <Row v-if="triggerMsg.flow">
            <Col :md="6" class="content-title">Flow类型:</Col>
            <Col :md="18">
              <div align="left">
                <a :href="triggerMsg.flow.taskDetail.url" target="_Blank">
                  {{triggerMsg.flow.taskType}}
                </a>
              </div>
            </Col>
          </Row>
          <Row v-if="this.mrnBundle.length>0">
            <Col :md="6" class="content-title">测试内容:</Col>
            <Col :md="18">
              <div align="left">
                <div align="left" v-if="this.mrnBundle.length<2">
                  {{this.mrnBundle[0].bundle_name}} - {{this.mrnBundle[0].version}}
                </div>
              </div>
              <div align="left">
                <Poptip  v-if="this.mrnBundle.length>=2" placement="bottom">
                    <Button style="font-size:11px" size ="small">内容详情</Button>
                    <div slot="content" style="overflow:visible">
                        <div align="left" v-for="mrnBundle in this.mrnBundle">
                          {{mrnBundle.bundle_name}} - {{mrnBundle.version}}
                        </div>
                    </div>
                </Poptip>
              </div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">新报告地址:</Col>
            <Col :md="18">
              <div v-if="jobDetail.shortlink2_report_url" align="left" style="overflow:hidden;white-space: nowrap;text-overflow:ellipsis">
                <a :href="jobDetail.shortlink2_report_url" target="_Blank">{{ extractWorkflowId(jobDetail.shortlink2_report_url) }}</a>
              </div>
              <div v-else align="left">暂无</div>
            </Col>
          </Row>
        </div>
      </Col>
    </Row>
</div>
</template>
<script>
  /* eslint-disable */
  export default {
    name: "ShortLinkJobDetail",
    props: ['jobDetail','triggerMsg'],
    components: {},
    data() {
      return {
        mrnBundle:[]
      }
    },
    mounted() {
      if(this.triggerMsg.flow){
        this.getMrnBundle()
      }
    },
    methods: {
      extractWorkflowId(url) {
        const match = url.match(/workflowId=([^&]*)/);
        return match ? match[1] : url;
      },
      getMrnBundle(){
        if(!(this.triggerMsg.flow.testObject)){
          return
        }
        let testObject = this.triggerMsg.flow.testObject
        for(let i = 0;i<testObject.length;i++){
          if(testObject[i].app.toLowerCase()!=this.triggerMsg.product.name.toLowerCase()){
            continue
          }
          if(testObject[i].os.toLowerCase()!=this.triggerMsg.product.os.toLowerCase()){
            continue
          }
          this.mrnBundle = testObject[i].mrn.bundle
        }
      }
    }
  };
</script>


<style scoped>
  .content-title{
    text-align: left;
    font-size: smaller;
    font-weight: bold;
    padding-left: 12px;
  }
  .content-title-container{
    border-left: 3px solid #42b983;
    font-size: small;
    overflow:visible; /* 超出部分隐藏 */
    white-space: nowrap; /* 文本不换行 */
    text-overflow:ellipsis;/* 省略的文本用省略号表示 */
  }
</style>
