<template>
<Card v-if="this.logName === 'urlscheme'" class="logText" >
  <template v-for=" (info,index) in schemeList" style="word-break: break-all;word-wrap: break-word">
    <Divider orientation="left" style="color: #abb2bf;">Scheme {{index+1}}</Divider>
    <p v-if="info.url">URL : {{info.url}}</p>
    <p v-if="info.page">Page : {{info.page}}</p>
    <p v-if="info.mrn && (info.mrn instanceof Array) ">MRNBundle : {{info.mrn[0]['MRNBundleName']}} - {{info.mrn[0]['MRNBundleVersion']}}</p>
    <p v-else-if="info.mrn && (info.mrn instanceof Object)">MRNBundle : {{info.mrn['MRNBundleName']}} - {{info.mrn['MRNBundleVersion']}}</p>
  </template>
</Card>
<Card v-else class="logText">
  <div >
    {{message}}
  </div>
</Card>

</template>

<script>
/* eslint-disable */
  export default {
        name: "textView",
        props: ['textUrl','logName'],
        data(){
          return{
            message:"",
            schemeList:[]
          }
        },
        mounted(){
            this.getLogInfo(this.textUrl)
        },
        methods:{
          getLogInfo(textUrl){
            this.$axios({
              method: 'post',
              data:{
                  url:'https://msstest.sankuai.com/v1/mss_2ed1fa0ee87f43b1a6448b0d650d5720/shortlink/result/' + textUrl,
              },
              url: this.env.client_common_url + "client/shortLinkLog"
            }).then((res) => {
              this.message = res.data.data
              if (this.logName==='urlscheme') {
                this.schemeList = JSON.parse(this.message)
              }
            }).catch(function (error) {
              console.log(error)
            })
          },
        }
  }
</script>
<style scoped>
.logText {
  background-color:#282c34;
  color: #abb2bf;
  height: 500px;
  /* width: auto; */
  overflow-x: scroll;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word
}
</style>
