<template>
  <div>
<div class="spin-container" v-if="this.isRequesting">
  <Spin>
    <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
  </Spin>
</div>
<ShortLinkJobDetail v-if="this.isRequesting==false" :jobDetail= this.jobDetail :triggerMsg= this.triggerMsg></ShortLinkJobDetail>
<Row v-if="this.isRequesting==false" style="margin-top: 40px;text-align: left;" justify="start" align="middle">
   <Col span=2 style="margin-top: 3px">
      <h4 align="left">运行详情</h4>
  </Col>
  <Col v-if="jobDetail.total" span=15>
      <Tag color="green">成功:{{jobDetail.pass}}</Tag>
      <Tag color="red">失败:{{jobDetail.fail}}</Tag>
      <Tag color="orange">跳过:{{jobDetail.skips}}</Tag>
  </Col>
  <Col v-if="jobDetail.total" span=6 style="float:right; text-align:right; margin-left:5px">
    <span>失败原因标注情况:</span>
    <Tag v-if="notPassNum === 0" color="success" size="small">All Pass</Tag>
    <Tag v-else-if="err_msg_checked_num === notPassNum" color="success" size="small">{{err_msg_checked_num}}/{{notPassNum}}</Tag>
    <Tag v-else color="warning" size="small">{{err_msg_checked_num}}/{{notPassNum}}</Tag>
    <Tag v-if="hitsTotal !== 0" style="margin:15px" size="small" color="purple" @click.native="getChecker" target="_Blank">Checker报警详情</Tag>
    <Tag v-if="oceanReportUrl.length !== 0" style="margin:15px" size="small" color="purple" @click.native="getOceanReportUrl" target="_Blank">埋点报告</Tag>
  </Col>
</Row>
<Tabs v-if="jobDetail.total" style="font-weight:bold;font-size:large">
  <template v-for="(result,index) in resultList">
    <Tab-pane :label="resultName[index]">
    <Row v-if="result!= 'pass'"
      justify="start"
      style="margin-left:10px;margin-bottom: 15px"
      align="middle"
    >
      <Checkbox :indeterminate="indeterminate[result]" :value="checkAll[result]" @click.prevent.native ="handleCheckAll(result)" style="margin-top: 7px;float:left">
        全选
        <span v-if="checkId[result].length>0" style="margin-left:1px;color:#9ea7b4">已选择{{checkId[result].length}}条</span>
      </Checkbox>
      <Button class="button-view" @click="showErrSelect(result,true)" type="primary" size="small"  v-show="!showSelect[result]">更新失败原因</Button>
      <Cascader :data="errReason.data" trigger="hover" style="margin-left:8px;float:left;" v-show="showSelect[result]" v-model="selectValue" @on-change="handleChange(arguments)"></Cascader>
      <Button @click="useSelectReason(selectValue,checkId[result],result),showErrSelect(result,false)"  type="primary" v-show="showSelect[result]" size="small" class="button-view">保存</Button>
      <Button @click="showErrSelect(result,false)" type="primary" v-show="showSelect[result]" size="small" class="button-view">取消</Button>
      <Col span="3" offset="18" style="float:right">
        <Button style="float:right;font-size:11px" @click="sugModal(result)" size="small" type="warning">一键标注失败原因</Button>
      </Col>
      </Row>
        <template>
          <div v-for="(methods, className, index) in jobInfo[result]" :key="index">
            <Collapse style="text-align: left;display: flex; flex-direction: row " simple>
              <Checkbox v-if="result!= 'pass'"
                :value="checkClass[result][className]"
                @on-change="handleCheckClass(result,className) "
                style="margin-right:-10px;margin-top: 12px"
              ></Checkbox>
              <Panel class="class-name" name="fail" style="border:none;width:100%">
                {{className}}
                <p slot="content" v-if="result!= 'pass'">
                  <CheckboxGroup
                    v-model="checkClassGroup[result][className]"
                    @on-change="checkClassGroupChange(arguments, className, result)"
                  >
                    <template v-for="method in methods">
                      <Collapse
                        simple
                        style="text-align: left; border:none; display: flex; flex-direction: row"
                      >
                        <Checkbox  :label="method.id" style="margin-right:-10px;margin-top: 12px"><span></span></Checkbox>
                        <Panel class="method-name" name="methodList" style="border:none;width:100%">
                          <Row
                            type="flex"
                            justify="start"
                            class="code-row-bg"
                            align="middle"
                            style="margin-left:30px;margin-top: -37px;"
                          >
                            {{method.methodName}}
                            <span style="margin-left:30px;color:#9ea7b4">{{method.startTime}}</span>
                            <span style="margin-left:30px;color:#9ea7b4">用时：{{formatSeconds(method.duration)}}</span>
                            <span style="margin-left:30px;color:#9ea7b4">Case失败原因：</span>
                            <span v-if='method.mainErrMsg === method.subErrMsg' style="color:#9ea7b4">{{method.mainErrMsg}}</span>
                            <span v-else style="color:#9ea7b4">{{method.mainErrMsg + ' / ' + method.subErrMsg}}</span>
                            <Col v-show='method.mainErrMsg==""' style="margin-left:20px;color:#9ea7b4">
                              <Tag v-if='("mainErrMsgSug" in method)' type="border" color="#fd8c04" style="font-weight: 700">
                                {{method.mainErrMsgSug + ' / ' + method.subErrMsgSug}}
                                <Button shape="circle" @click.stop="errReasonChange(method.mainErrMsgSug,method.subErrMsgSug,method.id,result)" style="font-size:11px;color:#db6400;padding-right:2px;padding-left:15px;padding-top:1px;font-weight: 700"  type="text" ghost>确认</Button>
                              </Tag>
                            </Col>
                          </Row>
                          <p slot="content">
                            <Row>
                              <Col span="4">
                                <Cascader v-if="result!= 'pass'"
                                  :data="errReason.data"
                                  @on-change="useSelectReason(arguments, method.id,result)"
                                  trigger="hover"
                                >
                                  <a style="margin-left:40px;margin-top:35px" >更新case失败原因</a>
                                </Cascader>
                              </Col>
                              <Col span="4">
                                <a v-if="result!= 'pass'" type="text" size="small" style="color:#2d8cf0" @click="shotBug(className,method.methodName)">ShotBug</a>
                              </Col>
                              <Col v-if='method.logs!= null' span="6" offset="10" style="display:block;text-align: right">
                                <template v-if='"lyrebird" in method.logs' v-for="(log, index) in method.logs['lyrebird']">
                                  <Button  style="padding: 0 3px;color: #2b85e4;font-size: small" type="text"  @click="showLog('/'+ className+'/'+ method.methodName+'/lyrebird/'+log)">
                                    lyrebird_{{index}}
                                  </Button>
                                </template>
                                <template v-if='"androidLogs" in method.logs' v-for="(log, index) in method.logs['androidLogs']">
                                  <Button  style="padding: 0 3px;color: #2b85e4;font-size: small" type="text"  @click="showLog('/'+ className+'/'+ method.methodName+'/androidLogs/'+log)">
                                    androidLog_{{index}}
                                  </Button>
                                </template>
                                <template v-if='"appium-server" in method.logs' v-for="(log, index) in method.logs['appium-server']">
                                  <Button  style="padding: 0 3px;color: #2b85e4;font-size: small" type="text"  @click="showLog('/'+ className+'/'+ method.methodName+'/appium-server/'+log)">
                                    appiumLog_{{index}}
                                  </Button>
                                </template>
                              </Col>
                            </Row>
                            <Tabs name="method tap" type="card" style="margin-left:35px;margin-bottom:20px;margin-top: 20px">
                              <TabPane v-if="result!= 'pass'" label="截图" style="display: flex; flex-direction: row">
                                <template v-for="screenShot in method.screenShot" >
                                  <ImageView
                                    :imagePath="s3Path +'/'+ className+'/'+ method.methodName+'/screenshot/'+screenShot"
                                    :style="{maxWidth:imageShowOptions.imageShowSize, marginRight:'50px', marginLeft:'30px'}"
                                  ></ImageView>
                                </template>
                              </TabPane>
                              <template v-for="(log, logName, index) in method.logs">
                                <TabPane v-if="logName === 'urlscheme' || logName === 'errThrow'|| logName === 'capabilities'" :label = logName tab="method tap">
                                  <Tabs name="errorlog tab" :animated="false" style="margin-left:10px;margin-bottom:20px;margin-top: 10px">
                                    <template v-for="(log, index) in method.logs[logName]">
                                      <TabPane tab="errorlog tab" :label="'retry_'+index">
                                        <textView :textUrl="s3Path +'/'+ className+'/'+ method.methodName+'/'+ logName +'/'+log" :logName="logName"></textView>
                                      </TabPane>
                                    </template>
                                  </Tabs>
                                </TabPane>
                              </template>
                            </Tabs>
                          </p>
                        </Panel>
                      </Collapse>
                    </template>
                  </CheckboxGroup>
                </p>
                <p slot="content" v-else>
                  <template v-for="method in methods">
                    <Collapse
                      simple
                      style="text-align: left; border:none; display: flex; flex-direction: row"
                      >
                      <Panel class="method-name" name="methodList" style="border:none;width:100%" >
                        <Row
                        type="flex"
                        justify="start"
                        class="code-row-bg"
                        align="middle"
                        style="margin-left:30px;margin-top: -37px"
                        >
                        {{method.methodName}}
                        <span style="margin-left:30px;color:#9ea7b4">{{method.startTime}}</span>
                        <span style="margin-left:30px;color:#9ea7b4">用时：{{formatSeconds(method.duration)}}</span>
                        </Row>
                        <p slot="content">
                          <Tabs name="method tap" type="card" style="margin-left:35px;margin-bottom:20px;margin-top: 20px">
                            <template v-for="(log, logName, index) in method.logs">
                              <TabPane v-if="logName === 'urlscheme' || logName === 'errThrow'|| logName === 'capabilities'" :label = logName tab="method tap">
                                <Tabs name="errorlog tab" :animated="false" style="margin-left:10px;margin-bottom:20px;margin-top: 10px">
                                  <template v-for="(log, index) in method.logs[logName]">
                                    <TabPane tab="errorlog tab" :label="'retry_'+index">
                                      <textView :textUrl="s3Path +'/'+ className+'/'+ method.methodName+'/'+logName+'/'+log" :logName="logName"></textView>
                                    </TabPane>
                                  </template>
                                </Tabs>
                              </TabPane>
                              <noticeView v-if="logName === 'notice'" :textUrl="s3Path +'/'+ className+'/'+ method.methodName+'/notice/'+log[0]"></noticeView>
                            </template>
                          </Tabs>
                        </p>
                      </Panel>
                    </Collapse>
                  </template>
                </p>
              </Panel>
            </Collapse>
          </div>
        </template>
        <BackTop></BackTop>
    </Tab-pane>
  </template>
</Tabs>
<div v-if="jobDetail.status==0" style="margin-top: 20px;margin-left:15px">
  <img height="18pd" src="/static/circles-menu-1.gif"/>
  Job排队中
</div>
<div v-if="jobDetail.status==1" style="margin-top: 20px;margin-left:15px">
  <img height="18pd" src="/static/spinning-arrows.gif"/>
  Case运行中
</div>
<div v-if="jobDetail.status==3" style="margin-top: 40px;margin-left:15px">Job运行失败</div>
  <shot-bug :showBugInfo="showBugInfo" :bugInfo="bugInfo" v-on:closeBugInfo="showBugInfo=false"/>
</div>
</template>
<script>
  /* eslint-disable */
  import ImageView from "./ShortLinkImageView";
  import textView from "./ShortLinkTextView";
  import event from "@/assets/event_bus";
  import ShotBug from "../baseComponents/ShotBug";
  import noticeView from "./ShortLinkNoticeView";
  import ShortLinkJobDetail from "./ShortLinkJobDetail"
  export default {
    name: "ClientShortLinkJobInfo",
    components: {ShotBug, ImageView, textView, noticeView, ShortLinkJobDetail},
    data() {
      return {
        triggerMsg:{},
        bugInfo:{
          jobBusiness:'',
          autoTestType:'shortLink',
          bugDesc:'',
          // relationTask:''
        },
        showBugInfo: false,
        jobInfo:{},
        resultList:['pass','fail','skip'],
        resultName:['成功','失败','跳过'],
        notPassNum: 0,
        hitsTotal:0,
        err_msg_checked_num: 0,
        result:'',
        urlSchemeLog:'',
        checkIdList:{},
        selectValue:[],
        showSelect:{
          fail:false,
          skip:false,
        },
        indeterminate: {
          fail:false,
          skip:false,
        },
        checkAll: {
          fail:false,
          skip:false,
        },
        checkAllGroup: [],
        checkClass: {},
        checkClassGroup: {},
        methodId: [],
        isRequesting: false,
        failReasonDict: {},
        passInfo: {},
        failInfo: {},
        skipInfo: {},
        jobDetail: {},
        oceanReportUrl:"",
        passNum: 0,
        failNum: 0,
        skipNum: 0,
        imageShowOptions: {
          imageShowSize: "236px"
        },
        s3Path: "",
        screenshotPath: "",
        errThrowPath: "",
        logPath: "",
        value4: false,
        fail: h => {
          return h("div", [
            h("span", "失败"),
            h("Badge", {
              props: {
                count: this.failNum
              }
            })
          ]);
        },
        skip: h => {
          return h("div", [
            h("span", "跳过"),
            h("Badge", {
              props: {
                count: this.skipNum
              }
            })
          ]);
        },
        errReason: {
          data: [
            {
              value: "jijianwenti",
              label: "基建问题"
            },{
              value: "youxiaowenti",
              label: "有效问题"
            },{
              value: "kecexingwenti",
              label: "可测性问题"
            },{
              value: "ouxianwenti",
              label: "偶现问题"
            },{
              value: "wuxiaoyunxing",
              label: "无效运行"
            },{
              value: "daimawenti",
              label: "自动化代码问题",
              children: [
                {
                  value: "daimabianxie",
                  label: "代码编写问题",
                },
                {
                  value: "xuqiubiangeng",
                  label: "需求变更",
                }
              ]
            }
          ]
        },
      };
    },
    mounted() {
      if (this.$route.query.jobName && this.$route.query.buildNum && this.$route.query.buildUrl) {
        let jobName = this.$route.query.jobName;
        let buildNum = this.$route.query.buildNum;
        let buildUrl = this.$route.query.buildUrl;
        this.getJobInfoByJoburl(jobName, buildNum, buildUrl);
      }
      if (this.$route.query.id){
        let id = this.$route.query.id;
        this.getJobInfoById(id)
        this.getCheckerResults(id, "ui_test")
      }
    },
    computed:{
      checkId: function () {
        let checkIdList = {
          fail:[],
          skip:[]
        };
        let checkGroup = {
          fail:{},
          skip:{}
        };
        checkGroup = this.checkClassGroup;
        for (var key in checkGroup["fail"]){
          if (checkGroup["fail"][key].length > 0) {
            for (let i = 0; i < checkGroup["fail"][key].length; i++)
              checkIdList["fail"].push(checkGroup["fail"][key][i])
          }
        }
        for (var key in checkGroup["skip"]){
          if (checkGroup["skip"][key].length > 0) {
            for (let i = 0; i < checkGroup["skip"][key].length; i++)
              checkIdList["skip"].push(checkGroup["skip"][key][i])
          }
        }
        this.checkIdList = checkIdList
        console.log(checkIdList)
        return checkIdList
      }
    },
    methods: {
        getCheckerResults(id, sourceType) {
            this.$axios({
                method: 'post',
                data: {
                size: 10000,
                query: {
                    bool: {
                    filter: {
                        bool: {
                        must: [
                            {
                            match_phrase: {
                                channel: 'notice'
                            }
                            },
                            {
                            match_phrase: {
                                'env.runner.jobId': id
                            }
                            },
                            {
                            match_phrase: {
                                'env.source_type': sourceType
                            }
                            }
                        ]
                        }
                    }
                    }
                },
                sort: {
                    '@timestamp': {
                    order: 'desc'
                    }
                },
                aggs: {
                    checker_name: {
                    terms: {
                        field: 'event.sender.function.keyword',
                        size: 1000
                    },
                    aggs: {
                        checker_title: {
                        terms: {
                            field: 'event.title.keyword',
                            size: 10000
                        }
                        }
                    }
                    }
                }
                },
                url: this.env.client_common_url + 'client/es/search/lyrebird-*'
            })
            .then((res) => {
                this.hitsTotal = res.data.hits.total
            })
        },
      shotBug(className,methodName){
        this.bugInfo.bugDesc='问题描述:'+
          '\n\n\nNative包:'+this.jobDetail.version+'-'+this.jobDetail.app_build+
          '\n测试报告:\n'+this.env.shortLink_job_url+'?jobName='+this.jobDetail.job_name+'&buildNum='+this.jobDetail.build+'&buildUrl='+this.jobDetail.build_url+
          '\ncase相关页面与MRN包信息: \nhttps://msstest.sankuai.com/v1/mss_2ed1fa0ee87f43b1a6448b0d650d5720/shortlink/result/'+this.s3Path+'/'+className+'/'+methodName+'/'+'urlscheme'+'/'+"urlscheme_0.json";
        this.bugInfo.jobBusiness=this.jobDetail.job_name.split('_')[0].toLowerCase();
        // this.bugInfo.relationTask=this.jobDetail.msg.taskUrl;
        this.showBugInfo=true;
      },
      getErrMsgNum(){
         var num = parseInt(this.jobDetail['fail'])+parseInt(this.jobDetail['skips'])
         this.err_msg_checked_num = this.jobDetail['err_msg_checked_num']
         this.notPassNum = num
      },
      showLog(logUrl){
            let routeData = this.$router.resolve({ path: '/client/logView', query: {  logUrl: this.logPath+logUrl } });
            console.log(this.logPath)
            console.log(logUrl)
            window.open(routeData.href, '_blank');
          },
      handleCheckAll(status) {
        let resultInfo = {}
        if (this.indeterminate[status]) {
          this.checkAll[status] = false;
        } else {
          this.checkAll[status] = !this.checkAll[status];
        }
        if (status === "fail") {
          resultInfo = this.failInfo
        } else if(status === "skip"){
          resultInfo = this.skipInfo
        }
        this.indeterminate[status] = false;
        if (this.checkAll[status]) {
          for (const className of Object.keys(resultInfo)) {
            this.checkClassGroup[status][className] = resultInfo[className] ? resultInfo[className].map(method => method.id) : [];
            this.checkClass[status][className] = true;
          }
        } else {
          for (const className of Object.keys(resultInfo)) {
            this.checkClassGroup[status][className] = [];
            this.checkClass[status][className] = false;
          }
        }
        console.log(this.checkAll[status])
        console.log(this.indeterminate[status])
        console.log(this.checkClassGroup);
      },
      handleCheckClass(status,className) {
        let resultInfo = {}
        if (status === "fail") {
          resultInfo = this.failInfo
        } else if(status === "skip"){
          resultInfo = this.skipInfo
        }
        this.checkClass[status][className] = !this.checkClass[status][className];
        if (this.checkClass[status][className]) {
          this.checkClassGroup[status][className] = resultInfo[className] ? resultInfo[className].map(method => method.id) : [];
          this.checkClass[status][className] = true;
        } else {
          this.checkClassGroup[status][className] = [];
          this.checkClass[status][className] = false;
        }

        let indeterminate = false;
        let isAllSelected = true;
        let isAllDeselected = true;
        for (const className of Object.keys(resultInfo)) {
          if (!this.checkClass[status][className]) {
            indeterminate = true;
          }
          isAllSelected &= this.checkClass[status][className];
          isAllDeselected &= !this.checkClass[status][className];
          console.log(!this.checkClass[status][className]);
        }
        if (isAllSelected) {
          this.checkAll[status] = true;
          this.indeterminate[status] = false;
        } else if (isAllDeselected) {
          this.checkAll[status] = false;
          this.indeterminate[status] = false;
        } else {
          this.indeterminate[status] = indeterminate;
        }
        console.log(isAllDeselected);
        console.log(className);
        console.log(this.checkClassGroup[status][className]);
      },
      checkClassGroupChange(args, className,status) {
        let resultInfo = {}
        if (status === "fail") {
          resultInfo = this.failInfo
        } else if(status === "skip"){
          resultInfo = this.skipInfo
        }
        if (args[0].length === resultInfo[className].length) {
          this.checkClass[status][className] = true;
        } else if (args.length > 0) {
          this.checkClass[status][className] = false;
        } else {
          this.checkClass[status][className] = false;
        }

        let indeterminate = false;
        let isAllSelected = true;
        let isAllDeselected = true;
        for (const className of Object.keys(resultInfo)) {
          if (!this.checkClass[status][className]) {
            indeterminate = true;
          }
          isAllSelected &= this.checkClass[status][className];
          isAllDeselected &= !this.checkClass[status][className];
        }
        if (isAllSelected) {
          this.checkAll[status] = true;
          this.indeterminate[status] = false;
        } else if (isAllDeselected) {
          this.checkAll[status] = false;
          this.indeterminate[status] = false;
        } else {
          this.indeterminate[status] = indeterminate;
        }
        console.log(this.checkClassGroup[status][className]);
        console.log(this.checkClass[status][className]);
      },
      showErrSelect(status,isShow) {
        if (status === "fail") {
          this.showSelect[status] = isShow;
        } else if(status === "skip"){
          this.showSelect[status] = isShow;
        }
      },
      getChecker(){
        let { id } = this.$route.query
        let routeData = this.$router.resolve({ path: '/clientChecker/checkerInfo', query: { id: id, sourceType: 'ui_test' } });
        window.open(routeData.href, '_blank');
      },
      getOceanReportUrl(){
          window.open(this.oceanReportUrl,'_blank')

      },
      handleChange(args) {
        this.selectValue = args;
        console.log(this.selectValue)
      },
      sugModal (result) {
        this.$Modal.confirm({
          title: '失败原因标记',
          content: '确认全部使用提示标记失败原因？',
          onOk: () => {
              this.allUseSugReason(result)
          }
        });
      },
      allUseSugReason(status) {
        console.log(status)
        let resultInfo = {}
        if (status === "fail") {
          resultInfo = this.failInfo
        } else if(status === "skip"){
          resultInfo = this.skipInfo
        }
        let methodIdList = [];
        for (var key in resultInfo) {
          for (let j = 0; j < resultInfo[key].length; j++) {
            if((resultInfo[key][j].mainErrMsg==='') && ("mainErrMsgSug" in resultInfo[key][j])){
              let mainReason = resultInfo[key][j].mainErrMsgSug;
              let subReason = resultInfo[key][j].subErrMsgSug;
              let methodId = resultInfo[key][j].id;
              this.errReasonChange(mainReason,subReason,methodId,status)
            }
          }
        }
      },
      useSelectReason(args,methodId,status) {
        let mainReason = args["1"][0]["label"];
        let subReason;
        if(args["1"].length === 1){
          subReason = mainReason
        } else {
          subReason = args["1"][1]["label"];
        }
        this.errReasonChange(mainReason,subReason,methodId,status)
      },
      errReasonChange(mainReason,subReason,methodId,status) {
        console.log(methodId);
        let resultInfo = {}
        if (status === "fail") {
          resultInfo = this.failInfo
        } else if(status === "skip"){
          resultInfo = this.skipInfo
        }
        let methodIdList = [];
        if (!(methodId instanceof Array)) {
          methodIdList.push(methodId);
        } else{
          methodIdList= methodId;
        }
        console.log(resultInfo)
        for (var key in resultInfo) {
          for (let j = 0; j < resultInfo[key].length; j++) {
            if (methodIdList.indexOf(resultInfo[key][j].id) > -1) {
              console.log(resultInfo[key][j].id);
              console.log(resultInfo[key][j].mainErrMsg);
              if(resultInfo[key][j].mainErrMsg===''){
                this.err_msg_checked_num = parseInt(this.err_msg_checked_num) + 1
              }
              resultInfo[key][j].mainErrMsg = mainReason;
              resultInfo[key][j].subErrMsg = subReason;
              console.log(JSON.stringify(resultInfo[key][j]));
            }
          }
        }
        if (status === "fail") {
          this.failInfo = resultInfo;
        } else if(status === "skip"){
          this.skipInfo = resultInfo;
        }
        this.$axios({
          method: "get",
          params: {
            idList: methodIdList.join(','),
            mainErrMsg: mainReason,
            subErrMsg: subReason
          },
          url: this.env.url + "clientShortLink/updateFailReason"
        })
          .then(res => {
            console.log(res.data.code)
            if (res.data.code === 0) {
              this.$Notice.success({
                title: '更新失败原因成功',
              });
            } else {
              this.$Notice.error({
                title: '更新失败原因失败'
              });
            }
          })
          .catch(function(error) {
            console.log(error);
          });
      },
      formatSeconds (time) {
        let min = Math.floor(time % 3600)
        let val = Math.floor(min / 60) + '分' + Math.floor(time % 60) + '秒'
        return val
      },
      getJobInfoByJoburl(jobName, buildNum, buildUrl){
        var params = {
          "jobName":jobName,
          "buildNum":buildNum,
          "buildUrl":buildUrl
        }
        this.getTraceDetail(params)
      },
      getJobInfoById(jobId){
        var params = {
          "id":jobId
        }
        this.getTraceDetail(params)
      },
      async getTraceDetail(params) {
        this.isRequesting = true
        await this.getJobInfo(params)
        if(this.jobDetail.trace_id==null || this.jobDetail.trace_id==0){
          this.isRequesting=false
          return
        }
        console.log(this.jobDetail.trace_id)
        this.$axios({
          method: "get",
          params: {
              traceId: this.jobDetail.trace_id
          },
          url: this.env.url + "ccd/getTriggerDetail"
        }).then(res => {
             this.isRequesting = false
             this.triggerMsg=res.data
          })
          .catch(function(error) {
            console.log(error);
          });
      },
      async getJobInfo(params) {
        return new Promise((resolve,reject) => {
        this.$axios({
          method: "get",
          params: params,
          url: this.env.url + "clientShortLink/shortLinkJobDetail"
        })
          .then(res => {
            // this.isRequesting = false
            let passNum = 0;
            let failNum = 0;
            let skipNum = 0;
            let checkClass = {
              fail:{},
              skip:{},
            };
            let checkClassGroup = {
              fail:{},
              skip:{},
            };
            let jobName = res.data.jobDetail.job_name;
            let buildNum = res.data.jobDetail.build;
            let buildUrl = res.data.jobDetail.build_url;
            this.passInfo = res.data.passes;
            this.failInfo = res.data.failures;
            this.skipInfo = res.data.skips;
            this.jobDetail = res.data.jobDetail;
            if(JSON.stringify(res.data.jobDetail).indexOf("oceanReportUrl")!=-1){
              this.oceanReportUrl = res.data.jobDetail.oceanReportUrl;
            }
            this.getErrMsgNum()
            this.jobInfo['pass'] = this.passInfo
            this.jobInfo['fail'] = this.failInfo
            this.jobInfo['skip']= this.skipInfo
            for (var key in this.passInfo) {
              passNum = passNum + this.passInfo[key].length;
            }
            for (var key in res.data.failures) {
              failNum = failNum + res.data.failures[key].length;
              checkClass["fail"][key] = false;
              checkClassGroup["fail"][key] = [];
            }
            for (var key in res.data.skips) {
              skipNum = skipNum + res.data.skips[key].length;
              checkClass['skip'][key] = false;
              checkClassGroup['skip'][key] = [];
            }
            this.passNum = passNum;
            this.failNum = failNum;
            this.skipNum = skipNum;
            this.checkClass = checkClass;
            this.checkClassGroup = checkClassGroup;
            this.s3Path = this.jobDetail.job_name + "/" + this.jobDetail.build
            this.screenshotPath = this.jobDetail.job_name + "/" + this.jobDetail.build + "/" + "screenshot";
            this.errThrowPath = this.jobDetail.job_name + "/" + this.jobDetail.build + "/" + "errThrow";
            this.logPath =
              "https://msstest.sankuai.com/v1/mss_2ed1fa0ee87f43b1a6448b0d650d5720/shortlink/result/" +
              this.jobDetail.job_name +
              "/" +
              this.jobDetail.build;
            resolve(res)
          })
          .catch(function(error) {
            reject(error)
            console.log(error);
          })
        })
      }
    }
  };
</script>


<style scoped>
  .class-name {
    font-weight: 500;
    font-size: medium;
  }
  .method-name {
    font-weight: normal;
    font-size: smaller;
  }
  .content-title{
    text-align: left;
    font-size: smaller;
    font-weight: bold;
    padding-left: 12px;
  }
  .content-title-container{
    border-left: 3px solid #42b983;
    font-size: small;
    overflow:visible; /* 超出部分隐藏 */
    white-space: nowrap; /* 文本不换行 */
    text-overflow:ellipsis;/* 省略的文本用省略号表示 */
  }
  .button-view{
    height:25px;
    font-size:11px;
    margin-left:5px;
    outline:none;
    text-align:left;
    float:left;
    margin-top: 4px
  }
  .spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
