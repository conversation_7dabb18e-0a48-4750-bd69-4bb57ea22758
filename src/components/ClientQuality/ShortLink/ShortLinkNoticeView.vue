<template >
<TabPane v-if="noticeList.length >0" label = "notice" tab="method tap">
    <Tabs :animated="false" style="margin-left:10px;margin-bottom:20px;margin-top: 10px">
        <template v-for="(log, index) in noticeList">
            <TabPane :label="noticeList[index].sender.function">
                <Card class="logText">
                    <p v-if="noticeList[index].title">Title : {{noticeList[index].title}}</p>
                    <p v-if="noticeList[index].message">Message : {{noticeList[index].message}}</p>
                    <p v-if="noticeList[index].timestamp">Timestamp : {{noticeList[index].timestamp}}</p>
                    <p v-if="noticeList[index].sender.file">File : {{noticeList[index].sender.file}}</p>
                </Card>
            </TabPane>
        </template>
    </Tabs>
    </TabPane>
</template>


<script>
/* eslint-disable */
  export default {
        name: "noticeView",
        props: ['textUrl'],
        data(){
          return{
            message:"",
            noticeList:[]
          }
        },
        mounted(){
            this.getLogInfo(this.textUrl)
        },
        methods:{
          getLogInfo(textUrl){
            this.$axios({
              method: 'post',
              data:{
                  url:'https://msstest.sankuai.com/v1/mss_2ed1fa0ee87f43b1a6448b0d650d5720/shortlink/result/' + textUrl,

              },
              url: "http://clientcommon.hotel.test.sankuai.com/client/shortLinkLog"
            }).then((res) => {
              this.message = res.data.data
              let notice = JSON.parse(this.message)
              for(let i in notice){
                this.noticeList.push(JSON.parse(notice[i].content))
              }
            }).catch(function (error) {
              console.log(error)
            })
          },
        }
  }
</script>
<style scoped>
.logText {
  background-color:#282c34;
  color: #abb2bf;
  height: 500px;
  /* width: auto; */
  overflow-x: scroll;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
  line-height:1.5
}
</style>