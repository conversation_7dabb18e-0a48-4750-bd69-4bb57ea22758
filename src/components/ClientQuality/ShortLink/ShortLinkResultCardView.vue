<template>
  <div :style="style" >
    <div>
      <span style="color:#333; font-weight: bolder; font-size: 16px">{{titleText}}</span>
    </div>
   <div v-if="text_type==='result'" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
     <span style="font-weight: bolder; font-size: 32px">
        {{data}}
     </span>
     <span style="color: #333; font-size: 16px">{{text}}</span>
   </div>
   <div v-if="text_type==='source'" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
     <span style="font-weight: 500; font-size: 15px">
        {{data}}
     </span><br/>
     <span style="font-weight: 500; font-size: 15px">
        {{text}}
     </span>
   </div>
  </div>
</template>

<script>
/* eslint-disable */
  export default {
        name: "shortLinkResultCardView",
        props: {
            titleText:'',
            data:'',
            text:'',
            text_type:'',
            rightLine:false
        },
        data(){
          return{
            style:{}
          }
        },
        mounted(){
            this.style = {
                textAlign: 'center',
                marginTop: '5px',
                marginBottom: '5px'
            }
            this.needRightLine(this.rightLine)
        },
        methods:{
          needRightLine(rightLine){
            if (rightLine) {
              this.style.borderRightStyle = 'dotted'
              this.style.borderRightColor = '#808695'
              this.style.borderRightWidth = '1px'
            } else{
              this.style.borderRightStyle = ''
              this.style.borderRightColor = ''
              this.style.borderRightWidth = ''
            }
          }
        }
  }
</script>
<style scoped>
</style>