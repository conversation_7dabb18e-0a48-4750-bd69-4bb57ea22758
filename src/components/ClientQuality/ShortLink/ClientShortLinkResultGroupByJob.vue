<template>
    <div>
      <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
      </div>
      <Timeline v-show="isShow" style="padding-top: 10px;padding-left: 2px">
        <TimelineItem>
          <Icon type="ios-pie-outline" slot="dot"></Icon>
             <span class= time-line-title style="display:block; float:left; text-align:right;font-weight: bolder;font-size: larger;">失败原因占比</span>
        <Row style="padding-top: 35px">
            <div id="subErrReason" class="chart"></div>
        </Row>
        </TimelineItem>
        <TimelineItem>
          <Icon type="ios-pie-outline" slot="dot"></Icon>
          <span class= time-line-title style="display:block; float:left; text-align:right;font-weight: bolder;font-size: larger;">Job信息</span>
        <br/><br/>
        <Tabs type="card" >
          <template v-for="(tabName,index) in subErrReason">
              <TabPane :label= tabName :key="index">
                <DataTable v-show="isShow" :tableData="jobInfo[tabName]" :tableColumns="jobColumns" style="margin-left:10px;margin-bottom:10px;margin-top: 5px"></DataTable>
              </TabPane>
          </template>
        </Tabs>
        </TimelineItem>
      </Timeline>
    </div>
</template>

<script>
  /* eslint-disable */
  import Highcharts from 'highcharts';
  import HighchartsNoData from 'highcharts/modules/no-data-to-display'
  HighchartsNoData(Highcharts)
  import DataTable from "../baseComponents/DataTable";
    export default {
      name: "clientShortLinkResultGroupByJob",
      components: {DataTable},
      data() {
          return {
            tableData: [],
            jobInfo:{},
            isRequesting:false,
            isShow:false,
            subErrReason:[],
            jobColumns:[
              {
                title: '序号',
                type: 'index',
                width: 80
              },{
                title: 'build号',
                key:'build',
                width: 80
              },
              {
                title: '任务名称',
                render:(h, params)=>{
                  return h("a",{
                      attrs: {
                        target:'_blank',
                        href:params.row.buildUrl
                      }
                    },params.row.jobName)
                },
              },{
                title: '应用包',
                render:(h, params)=>{
                  var appUrl = params.row.appUrl;
                  var appInfo = params.row.version + "-" + params.row.appBuild
                  if (appUrl == null) {
                    appInfo = ""
                  }
                  return h("a",{
                      attrs: {
                        target:'_blank',
                        href:appUrl
                      }
                    },appInfo)
                },
              },{
                title: '触发方式',
                key:'triggered',
              },
              {
                title: 'QA',
                key:'QA',
              },
              {
                title: '任务链接',
                render:(h, params)=>{
                  return h("a",{
                      attrs: {
                        target:'_blank',
                        href:params.row.taskUrl
                      }
                    },params.row.taskSummary)
                },
                width: 300,
              },{
                title: 'case失败数',
                sortable:true,
                key:'num',
                width: 150,
                align:"center"
              },{
                title: '查看Job详情',
                width: 120,
                render:(h, params)=>{
                  var id = params.row.id
                  return h('div',[
                    h('Button',{
                      props: {
                        size: 'small',
                        type: 'primary'
                      },
                      style: {
                        marginRight: '10px',
                        width: '70px'
                      },
                      on: {
                      click: () => {
                        this.getTask(id)
                      }
                    }
                    },'查看')
                  ])
                }
              }
            ]
          }
        },
        mounted() {
          if (this.$route.query.channel && this.$route.query.startTime && this.$route.query.endTime && this.$route.query.platform && this.$route.query.status && this.$route.query.mainErrReason) {
            let channel = this.$route.query.channel;
            let startTime = this.$route.query.startTime;
            let endTime = this.$route.query.endTime;
            let platform = this.$route.query.platform;
            let status = this.$route.query.status;
            let mainErrReason = this.$route.query.mainErrReason;
            console.log("构造方法")
            this.getJobInfo(channel, startTime, endTime, platform, status, mainErrReason);
          } else {
            console.log("页面参数失败")
          }
        },
        methods: {
            getTask(id){
              let routeData = this.$router.resolve({ path: '/clientShortLinkJob/jobInfo', query: {  id: id } });
              window.open(routeData.href, '_blank');
            },
            formateTime: function (time) {
              return time.getFullYear() + '-' + (time.getMonth() >= 9 ? (time.getMonth() + 1) : '0' + (time.getMonth() + 1)) + '-' + (time.getDate() > 9 ? time.getDate() : '0' + time.getDate())
            },
            getJobInfo(channel, startTime, endTime, platform, status, mainErrReason){
              this.isRequesting = true //设置展示loading样式
              this.isShow = false
              this.$axios({
                params:{
                  channel: channel,
                  startTime: startTime,
                  endTime: endTime,
                  platform: platform,
                  status: status,
                  mainErrReason: mainErrReason
                },
                method:"get",
                url: this.env.url + "clientShortLink/shortLinkResultInfoGroupByJob",
              }).then((res) => {
                this.isRequesting = false //不展示loading样式
                this.isShow = true
                let message = res.data;
                this.jobInfo = message.jobInfo.reasonInfo;
                for (let i = 0;i < message.jobInfo.subErrReason.length;i++) {
                  this.subErrReason.push(message.jobInfo.subErrReason[i].name);
                }
                let jobFormalData = [];
                console.log("请求成功");
                this.tableData = this.jobInfo;
                console.log(this.jobInfo);
                let statusText;
                if (status === 'failures') {
                  statusText = '失败';
                } else {
                  statusText = '跳过';
                }
                let title = platform + statusText + 'case-' + mainErrReason + '-分布图';
                let reasonData = message.jobInfo.subErrReason;
                this.pieChart("subErrReason", title, reasonData);
              }).catch(function (error) {
                console.log(error);
                console.log("请求失败");
              })
            },
            pieChart(containerId, title, reasonData) {
              if(reasonData){
                  let i,j;
                  let dataLen = reasonData.length;
                  let subReasonData = [];
                  let noDataText;
                  if(title.includes("失败")){
                    noDataText = '无失败case'
                  } else{
                    noDataText = '无跳过case'
                  }
                  // Build the data arrays
                  for (i = 0; i < dataLen; i += 1) {
                      // add subReason data
                      subReasonData.push({
                          name: reasonData[i].name,
                          y: reasonData[i].y
                      });
                  }
                  console.log(subReasonData);
                  // Create the chart
                    Highcharts.chart(containerId, {
                      colors:[
                          '#008891',
                          '#f7a35c',
                          '#6D80CC',
                          '#FFD56B', 
                          '#7cb5ec',
                          '#f15c80',
                          '#c42525',
                          '#a6c96a',
                          '#3CB371',
                          '#20B2AA'
                      ],
                      chart: {
                          type: 'pie',
                          marginTop: 10
                      },
                      title: {
                          text: title,
                          margin: 1,
                          style: {
                            color: '#808695',
                            fontWeight: 'bold',
                            fontSize: '16px'
                          }
                      },
                      plotOptions: {
                          pie: {
                              allowPointSelect: true,
                              cursor: 'pointer',
                              shadow: false,
                              center: ['50%', '50%']
                          }
                      },
                      tooltip: {
                              pointFormat: '{series.name}: 共{point.y}个; 占比<b>{point.percentage:.1f}%</b>'
                      },
                      series: [{
                          type: 'pie',
                          innerSize: '70%',
                          name: 'subReason',
                          data: subReasonData,
                          size: '50%',
                          dataLabels: {
                              softConnector: false,
                              enabled: true,
                              formatter: function () {
                                return this.percentage > 1? this.point.name + ':</b> ' + Highcharts.numberFormat(this.percentage,2) + '%' + '（' + this.y +'）' : null;
                              },
                              distance: 20,
                              connectorWidth: 2,
                              style: {
                                  color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
                              }
                          }
                      }
                      ],
                      credits: {
                          enabled: false     //不显示LOGO
                      },
                      lang: {
                        noData: noDataText,
                      },
                      noData: {
                        style: {
                          fontWeight: 'bold',
                          fontSize: '20px',
                        }
                      }
                  }
                );
              }
          }
        }
      }
</script>

<style scoped>
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  .chart{
  width:500px;
  height:350px;
  margin:0 auto;
}
</style>
