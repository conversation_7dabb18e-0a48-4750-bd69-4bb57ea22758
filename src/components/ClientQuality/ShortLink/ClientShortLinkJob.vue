<template>
    <div>
      <Row >
        <Col span="4">
          <div>
            <DatePicker type="daterange" split-panels :value="nowDate" placeholder="Select date" style="width: 200px" @on-change="getJobInfo"></DatePicker>
          </div>
        </Col>
        <Col span="3">
          <div>
            <Select v-model="businessFilter" clearable placeholder="请选择业务线" style="margin-bottom:3px;padding-left:10px" @on-change="jobBusinessFilter" :transfer="true">
              <Option v-for="item in businessList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </Col>
        <Col span="3">
          <div>
            <Select v-model="platformFilter" clearable placeholder="请选择平台" style="margin-bottom:3px;padding-left:10px" @on-change="jobPlatformFilter" :transfer="true">
              <Option v-for="item in platformList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </Col>
        <Col span="3">
          <div>
            <Select multiple filterable v-model="bundleFilter" clearable placeholder="请选择MRN Bundle" style="margin-bottom:3px;padding-left:10px" @on-change="jobBundleFilter" :transfer="true">
              <Option v-for="item in bundleList[business]" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </Col>
        <Col span="3">
          <div>
            <Input multiple filterable v-model="appFilter" clearable placeholder="请输入应用包构建号" style="margin-bottom:3px;padding-left:10px" @on-change="jobAppFilter(appFilter)"/>
          </div>
        </Col>
        <Col span="3" offset="4">
          <div style="text-align:right;margin-bottom:25px;margin-top:7px">
            <Checkbox  v-model="showTest" @on-change="jobShowDebug">调试Job</Checkbox>
          </div>
        </Col>
      </Row>
      <div class="spin-container" v-if="isRequesting" style="padding-top: 50px">
          <Spin>
            <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
          </Spin>
      </div>
      <DataTable v-show="isShow" no-data-text="--" :tableData="tableData" :tableColumns="jobColumns"></DataTable>

    </div>
</template>

<script>
  /* eslint-disable */
  import DataTable from "../baseComponents/DataTable";
  import { Bus } from '@/global/bus'
  import ShortLinkJobMsg from "./ShortLinkJobMsg";
    export default {
      name: "ClientShortLinkJob",
      components: {DataTable,ShortLinkJobMsg},
      data() {
          return {
            platformList:[
              {
                  label: 'Android',
                  value: 'android'
              },
              {
                  label: 'iOS',
                  value: 'ios'
              }],
            bundleListRes:[],
            bundleList:{
              'hotel':[],
              'travel':[],
              'overseahotel':[],
              'all':[]
            },
            business:'all',
            businessList:[
              {
                  label: 'Overseahotel',
                  value: 'overseahotel'
              },
              {
                  label: 'Hotel',
                  value: 'Hotel'
              },
              {
                  label: 'Travel',
                  value: 'Travel'
              },
              {
                label: 'phoenix',
                value: 'phoenix'
              },
              {
                label: 'bus',
                value: 'bus'
              },
              {
                label: 'Food',
                value: 'Food'
              },
              {
                label: 'dzutrade',
                value: 'dzutrade'
              },
              {
                label: 'PVT',
                value: 'PVT'
              }],
            isRequesting:false,
            isShow:false,
            businessFilter:'',
            platformFilter:'',
            bundleFilter:'',
            appFilter:'',
            filterData:'',
            showTest: false,
            modalTitle:'',
            nowDate:[],
            rowIndex:'',
            jobInfo:[],
            tableData:[],
            jobFilterData:[],
            jobColumns:[
              {
                type: 'expand',
                width: 30,
                render: (h, params) => {
                return h(ShortLinkJobMsg, {
                  props: {
                    row: params.row
                    }
                  })
                }
              },{
                title: '平台',
                key:'platform',
                render: (h,params) => {
                  var srcs = "";
                  if(params.row.platform.toLowerCase().indexOf("android") > -1){
                    srcs = "/static/img/android.png"
                  } else{
                    srcs = "/static/img/ios.png"
                  }
                  return h('img',{
                      attrs: {
                        src:srcs
                      },
                      style: {
                        width: '25px',
                        height: '25px'
                      }
                    })
                },
                align:"center"
              },{
                title: '方向',
                render:(h, params)=>{
                  return h("a",{
                      attrs: {
                        target:'_blank',
                        href:params.row.build_url
                      }
                    },params.row.job_name.split("_",1))
                },
                // width: 110,
                align:"center"
              },{
                title: '成功率',
                slot:'pass_rate',
                render: (h, params) => {
                  var colors;
                  if(parseFloat(params.row.pass_rate)>=parseFloat("100%")){
                    colors = 'green'
                  } else {
                    colors = 'volcano'
                  }
                  return h('a', [
                    h('tag', {
                      props: {
                        color: colors
                      },
                      style:{
                        width:'55px'
                      }
                    }, params.row.pass_rate)
                  ])
                },
                align:"center"
              },{
                title: '成功',
                key:'pass',
              },{
                title: '失败',
                key:'fail',
              },{
                title: '跳过',
                key:'skips',
                // width: 58
              },{
                title: '应用包',
                render:(h, params)=>{
                  var appUrl = params.row.app_url;
                  var appInfo = params.row.version + "-" + params.row.app_build
                  if (appUrl == null) {
                    appInfo = ""
                  }
                  return h("a",{
                      attrs: {
                        target:'_blank',
                        href:appUrl
                      }
                    },appInfo)
                },
                width: 200
              },{
                title: '开始时间',
                key: 'start_time',
                width: 200
              },{
                title: '运行耗时',
                key: 'duration',
                width: 100
              },
              {
                title: '失败原因标记情况',
                render:(h, params)=>{
                  var num  = parseInt(params.row.fail)+parseInt(params.row.skips);
                  var types  = params.row.err_msg_checked_num === num ? 'success':'warning';
                  var fail_checked_num = params.row.err_msg_checked_num + '/' + num
                  if(num===0){
                    fail_checked_num = 'Pass'
                  }
                  return h('div',[
                    h('Button',{
                      props: {
                        type:types,
                        size: 'small'

                      },
                      style: {
                        marginRight: '5px',
                        width:'80px'
                      },
                      on: {
                      click: () => {
                        this.getTask(params.row.id)
                      }
                    }
                    },fail_checked_num)
                  ])
                },
                width: 150
              }
            ]
          }
        },
        mounted(){
          let endTime = this.formateTime(new Date())
          let startTime = this.formateTime(new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000))
          this.nowDate = [startTime, endTime]
          this.getJobInfo(this.nowDate);
          this.jobShowDebug()
          this.getMRNBundleList()
        },
        methods: {
            formateTime: function (time) {
              return time.getFullYear() + '-' + (time.getMonth() >= 9 ? (time.getMonth() + 1) : '0' + (time.getMonth() + 1)) + '-' + (time.getDate() > 9 ? time.getDate() : '0' + time.getDate())
            },
            //获取所有的MRN包名
            getMRNBundleList(){
              this.$axios({
                method:"get",
                url:this.env.url+"client/MRNBundleList"
              }).then((res) => {
                let message = res.data;
                this.bundleListRes = message;
                //遍历所有的包名，将hotel、travel和overseahotel相关的bundle分别存储，方便筛选
                for(let i=0; i< message.length;i++){
                  this.bundleList["all"].push(message[i]);
                  if(message[i].value.includes("rn_hotel")){
                    this.bundleList["hotel"].push(message[i]);
                  }
                  if(message[i].value.includes("rn_travel")){
                    this.bundleList["travel"].push(message[i]);
                  }
                  if(message[i].value.includes("rn_overseahotel")){
                    this.bundleList["overseahotel"].push(message[i]);
                  }
                }
                console.log(this.bundleList)
              }).catch(function (error) {
                console.log(error)
              })
            },
            //筛选方向
            jobBusinessFilter(value){
              if(typeof value === "string"){
                this.businessFilter=value
                if(value.toLowerCase() == 'hotel' || value.toLowerCase() == 'overseahotel' || value.toLowerCase() == 'travel'){
                  this.business = value.toLowerCase()
                } else {
                  this.business = 'all'
                }
              } else {
                this.businessFilter = ''
                this.business = 'all'
              }
              console.log(this.businessFilter)
              this.jobFilter(this.businessFilter,this.platformFilter,this.bundleFilter,this.appFilter)
            },
            //筛选平台
            jobPlatformFilter(value){
              if(typeof value === "string"){
                this.platformFilter = value
              } else {
                this.platformFilter=''
              }
              console.log(this.platformFilter)
              this.jobFilter(this.businessFilter,this.platformFilter,this.bundleFilter,this.appFilter)
            },
            //筛选bundle
            jobBundleFilter(value){
              if(value.length>0){
                this.bundleFilter = value
              } else {
                this.bundleFilter=[]
              }
              console.log(this.bundleFilter)
              this.jobFilter(this.businessFilter,this.platformFilter,this.bundleFilter,this.appFilter)
            },
            jobAppFilter(value){
              if(value.length>0){
                this.appFilter = value
              } else {
                this.appFilter=""
              }
              console.log(this.appFilter)
              this.jobFilter(this.businessFilter,this.platformFilter,this.bundleFilter,this.appFilter)
            },
            //根据3个筛选项的变更，筛选出对应job
            jobFilter(businessFilter,platformFilter,bundleFilter,appFilter){
              let filterData = []
              if(!businessFilter.length>0 && !platformFilter.length>0 && !bundleFilter.length>0 && !this.appFilter.length>0){
                this.tableData = this.jobFilterData
                return;
              }
              for (let i = 0; i < this.jobFilterData.length; i++) {
                if (businessFilter.length>0 ) {
                  if(!(this.jobFilterData[i].job_name.indexOf(businessFilter) > -1)){
                    continue;
                  }
                }
                if(platformFilter.length>0 ){
                  if(!(this.jobFilterData[i].platform.toLowerCase() === platformFilter.toLowerCase())){
                    continue;
                  }
                }
                console.log(bundleFilter)
                if(bundleFilter.length>0 ){
                  let j = 0
                  for(; j < bundleFilter.length; j++){
                    if(this.jobFilterData[i].mrnBundle == "" ||this.jobFilterData[i].mrnBundle.indexOf(bundleFilter[j]) > -1){
                      break;
                    }
                  }
                  if(j>=bundleFilter.length){
                    continue;
                  }
                }
                if(appFilter.length>0){
                  console.log(this.jobFilterData[i].app_build)
                  if(!(this.jobFilterData[i].app_build.indexOf(appFilter)>-1)){
                    continue;
                  }
                }
                filterData.push(this.jobFilterData[i]);
              }
              console.log(filterData)
              this.tableData = filterData
            },
            getJobInfo(timeRange){
              this.isRequesting=true
              this.isShow=false
              this.$axios({
                params:{
                  startTime: timeRange[0],
                  endTime: timeRange[1]
                },
                method:"get",
                url:this.env.url+"clientShortLink/shortLinkJob"
              }).then((res) => {
                this.isRequesting=false
                this.isShow=true
                let message = res.data;
                this.jobInfo=message;
                let jobFormalData=[];
                for (let i = 0; i < this.jobInfo.length; i++) {
                  if (this.jobInfo[i].msg != null && this.jobInfo[i].msg.QA != null )
                    this.jobInfo[i].QA = this.jobInfo[i].msg.QA
                }
                this.jobShowDebug()
              }).catch(function (error) {
              })
            },
            getTask(id){
            let routeData = this.$router.resolve({ path: '/clientShortLinkJob/jobInfo', query: {  id: id} });
            window.open(routeData.href, '_blank');
            },
            jobShowDebug(){
            let jobFilterData = [];
            if(this.showTest){
              for (let i = 0; i < this.jobInfo.length; i++) {
                if (this.jobInfo[i].job_name.includes("Debug")) {
                  jobFilterData.push(this.jobInfo[i]);
                }
              }
            }else {
              for (let i = 0; i < this.jobInfo.length; i++) {
                if (!this.jobInfo[i].job_name.includes("Debug")) {
                  jobFilterData.push(this.jobInfo[i]);
                }
              }
            }
            this.jobFilterData = jobFilterData;
            this.jobFilter(this.businessFilter,this.platformFilter,this.bundleFilter,this.appFilter)
          },
        }
      }
</script>

<style scoped>
.spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
