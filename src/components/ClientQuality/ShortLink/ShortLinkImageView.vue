<template>
  <div>
    <vue-load-image>
      <img class="image-show" slot="image" :src="imageUrl+imagePath"/>
      <img class="image-show" slot="preloader" src="/static/img/image_loading.gif"/>
      <div slot="error">Image not found</div>
    </vue-load-image>
  </div>
</template>

<script>
    import VueLoadImage from 'vue-load-image'
    export default {
      name: 'ImageView',
      props: ['imagePath'],
      components: {
        'vue-load-image': VueLoadImage
      },
      data () {
        return {
          imageSize: '@500w',
          imageUrl: 'https://msstest.sankuai.com/v1/mss_2ed1fa0ee87f43b1a6448b0d650d5720/shortlink/result/'
        }
      }
    }
</script>

<style scoped>
  .image-show{
    border-radius: 5px;
    border:1px solid darkgray;
    width: 100%;
  }
</style>