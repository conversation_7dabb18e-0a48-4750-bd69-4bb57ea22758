<template>
    <div style="margin-left: 30px">
      <Row>
        <Col :span="4" v-if="row.source_type">build id：{{row.build}}</Col>
      </Row>
      <Row style="padding-top: 10px">
        <Col :span="4" v-if="row.msg && row.msg.taskType">Task类型：{{row.msg.taskType}}</Col>
        <Col :span="4" v-if="row.msg && row.msg.publishType">发布单类型：{{row.msg.publishType}}</Col>
        <Col :span="4" v-if="row.msg && row.msg.version">版本：{{row.msg.version}}</Col>
        <Col :span="12" v-if="row.msg && row.msg.taskSummary">任务：
          <a v-if="row.msg.taskUrl" :href="row.msg.taskUrl" target="_blank">{{row.msg.taskSummary}}</a>
          <span v-else :href="row.msg.taskUrl" target="_blank">{{row.msg.taskSummary}}</span>
        </Col>
      </Row>
    </div>
</template>

<script>
    export default {
      name: 'ShortLinkJobMsg',
      props: {
        row: Object
      }
    }
</script>

<style scoped>

</style>