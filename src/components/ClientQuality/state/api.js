import axios from 'axios'
import vueContext from '../../../main.js'
import { Bus } from '@/global/bus'

function mmcdAxios(axiosConfig) {
  const service = axios.create({
    baseURL: vueContext.env.url
  })
  service.interceptors.request.use(config => {
    if (config.method === 'post' || config.method === 'put') {
      let data = config.data
      config.data = JSON.stringify({
        ...data,
        userId: Bus.userInfo.userLogin,
        operator: Bus.userInfo.userLogin
      })
      config.headers = {
        'Content-Type': 'application/json'
      }
    } else if (config.method === 'get' || config.method === 'delete') {
      let params = config.params
      config.params = {
        ...params,
        userId: Bus.userInfo.userLogin,
        operator: Bus.userInfo.userLogin
      }
    }
    return config
  })
  return service(axiosConfig)
}
export default mmcdAxios

export const getBusinessList = misId => {
  return mmcdAxios({
    url: 'autoTestConfig/getAllBusinessName',
    method: 'GET',
    params: {
      misId: misId
    }
  })
}

export const getBusinessGroup = () => {
  return mmcdAxios({
    method: 'GET',
    url: 'autoTestConfig/getBusinessList'
  })
}

export const getUrlList = pageId => {
  return mmcdAxios({
    method: 'GET',
    params: {
      pageId: pageId
    },
    url: 'url/urlList'
  })
}

export const getUrlPathInfo = pathId => {
  return mmcdAxios({
    method: 'GET',
    params: {
      pathId: pathId
    },
    url: 'url/urlSelect'
  })
}

export const setUrlInfo = urlInfo => {
  return mmcdAxios({
    method: 'POST',
    data: urlInfo,
    url: 'url/urlInfo'
  })
}

export const checkUrl = Info => {
  return mmcdAxios({
    method: 'POST',
    data: Info,
    url: 'url/check'
  })
}

export const deleteUrl = id => {
  return mmcdAxios({
    method: 'DELETE',
    params: {
      urlId: id
    },
    url: 'url/urlInfo'
  })
}

export const getCaseInfo = actionId => {
  return mmcdAxios({
    method: 'GET',
    params: {
      actionId: actionId
    },
    url: vueContext.env.url + 'scene/case/sceneCase'
  })
}

export const deleteSceneCase = id => {
  return mmcdAxios({
    method: 'DELETE',
    params: {
      id: id
    },
    url: vueContext.env.url + 'scene/case/sceneCase'
  })
}

export const updateSceneCase = info => {
  return mmcdAxios({
    method: 'POST',
    data: info,
    url: vueContext.env.url + 'scene/case/sceneCase'
  })
}

export const addAction = info => {
  return mmcdAxios({
    method: 'POST',
    data: info,
    url: vueContext.env.url + 'scene/action/add'
  })
}

export const deleteAction = actionId => {
  return mmcdAxios({
    method: 'DELETE',
    params: {
      actionId: actionId
    },
    url: vueContext.env.url + 'scene/case/deleteAction'
  })
}

export const addCase = info => {
  return mmcdAxios({
    method: 'POST',
    data: info,
    url: vueContext.env.url + 'scene/case/add'
  })
}

/* eslint-disable */

export const getBusinessById = id => {
  return axios({
    method: 'GET',
    params: {
      businessId: id
    },
    url: vueContext.env.url + 'autoTestConfig/getBusinessById'
  })
}

export const getUsersByBusinessId = id => {
  return axios({
    method: 'GET',
    params: {
      businessId: id
    },
    url: vueContext.env.url + 'autoTestConfig/getUsersByBusinessId'
  })
}

export const getPageList = query => {
  return axios({
    method: 'GET',
    params: query,
    url: vueContext.env.url + 'page/getPageList'
  })
}

export const getPageInfo = id => {
  return axios({
    method: 'GET',
    params: {
      pageId: id
    },
    url: vueContext.env.url + 'page/getPageInfo'
  })
}

export const getSceneList = query => {
  return axios({
    method: 'GET',
    params: query,
    url: vueContext.env.url + 'page/getSceneList'
  })
}

export const getCategory = type => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'page/getCategory',
    params: {
      type: type
    }
  })
}

export const setPageInfo = pageInfo => {
  return axios({
    method: 'PUT',
    data: pageInfo,
    url: vueContext.env.url + 'page/setPageInfo'
  })
}

export const deletePageInfo = id => {
  return axios({
    method: 'DELETE',
    params: {
      pageId: id
    },
    url: vueContext.env.url + 'page/pageInfo'
  })
}

export const setSceneInfo = sceneInfo => {
  return axios({
    method: 'PUT',
    data: sceneInfo,
    url: vueContext.env.url + 'page/scene'
  })
}
export const setSceneListInfo = sceneListInfo => {
  return axios({
    method: 'PUT',
    data: sceneListInfo,
    url: vueContext.env.url + 'page/sceneList'
  })
}

export const getSceneInfo = id => {
  return axios({
    method: 'GET',
    params: {
      sceneId: id
    },
    url: vueContext.env.url + 'page/scene'
  })
}

export const deleteScene = id => {
  return axios({
    method: 'DELETE',
    params: {
      sceneId: id
    },
    url: vueContext.env.url + 'page/scene'
  })
}

export const deleteSceneList = ids => {
  return axios({
    method: 'DELETE',
    params: {
      sceneIds: ids
    },
    url: vueContext.env.url + 'page/sceneList'
  })
}

export const getAllProduct = () => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'page/getProductList'
  })
}

export const getProductByBusinessId = id => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'autoTestConfig/getProductByBusinessId',
    params: {
      businessId: id
    }
  })
}

export const deleteSceneProduct = id => {
  return axios({
    method: 'DELETE',
    params: {
      sceneProductId: id
    },
    url: vueContext.env.url + 'page/sceneProduct'
  })
}

export const setSceneProduct = spInfo => {
  return axios({
    method: 'PUT',
    data: spInfo,
    url: vueContext.env.url + 'page/sceneProduct'
  })
}

export const refreshSceneProduct = sceneId => {
  return axios({
    method: 'POST',
    data: { sceneId: sceneId },
    url: vueContext.env.url + 'page/resetSceneProduct'
  })
}

export const deletePageUser = id => {
  return axios({
    method: 'DELETE',
    params: {
      pageUserId: id
    },
    url: vueContext.env.url + 'page/pageUser'
  })
}

export const setPageUser = pageUserInfo => {
  return axios({
    method: 'PUT',
    data: pageUserInfo,
    url: vueContext.env.url + 'page/pageUser'
  })
}

export const getAllTestItem = () => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'page/getTestItem'
  })
}

export const getImplement = () => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'page/getImplement'
  })
}

export const getCategoryType = () => {
  return axios({
    method: 'GET',
    url: vueContext.env.url + 'page/getCategoryType'
  })
}

export const getBusinessByUser = misId => {
  return axios({
    method: 'GET',
    params: {
      misId: misId
    },
    url: vueContext.env.url + 'autoTestConfig/getBusinessByUser'
  })
}

export const getNodeList = nodeType => {
  return axios({
    method: 'GET',
    params: {
      nodeType: nodeType
    },
    url: vueContext.env.url + 'autoTestConfig/getNodeList'
  })
}

export const setPageProduct = ppInfo => {
  return axios({
    method: 'PUT',
    data: ppInfo,
    url: vueContext.env.url + 'page/pageProduct'
  })
}

export const setPageProductSwitch = ppInfo => {
  return axios({
    method: 'PUT',
    data: ppInfo,
    url: vueContext.env.url + 'page/pageProductSwitch'
  })
}

export const deletePageProduct = ppId => {
  return axios({
    method: 'DELETE',
    params: {
      id: ppId
    },
    url: vueContext.env.url + 'page/pageProduct'
  })
}

export const getAbList = filterInfo => {
  return axios({
    method: 'POST',
    data: filterInfo,
    url: vueContext.env.url + 'ab/getAbList'
  })
}

export const deleteAb = abId => {
  return axios({
    method: 'DELETE',
    params: {
      abId: abId
    },
    url: vueContext.env.url + 'ab/abInfo'
  })
}

export const getAbDetail = abId => {
  return axios({
    method: 'GET',
    params: {
      abId: abId
    },
    url: vueContext.env.url + 'ab/abInfo'
  })
}

export const setAbInfo = abInfo => {
  return axios({
    method: 'POST',
    data: abInfo,
    url: vueContext.env.url + 'ab/abInfo'
  })
}

export const addAbPageProduct = abPageProductInfo => {
  return axios({
    method: 'PUT',
    data: abPageProductInfo,
    url: vueContext.env.url + 'ab/abPageProductInfo'
  })
}

export const updateAbPageProduct = abPageProductInfo => {
  return axios({
    method: 'POST',
    data: abPageProductInfo,
    url: vueContext.env.url + 'ab/abPageProductInfo'
  })
}

export const setFixedStrategy = data => {
  return axios({
    method: 'POST',
    data: data,
    url: vueContext.env.url + 'ab/setFixedStrategy'
  })
}

export const deleteUserById = userInfo => {
  return axios({
    method: 'DELETE',
    params: userInfo,
    url: vueContext.env.url + 'ab/abUser'
  })
}

export const addUser = userInfo => {
  return axios({
    method: 'POST',
    params: userInfo,
    url: vueContext.env.url + 'ab/abUser'
  })
}

export const rebuildTrigger = triggerid => {
  return axios({
    method: 'POST',
    data: { triggerId: triggerid },
    url: vueContext.env.sb_url + '/autoTest/rebuild'
  })
}

export const changeBiz = request => {
  return axios({
    method: 'POST',
    data: request,
    url: vueContext.env.url + 'ab/changeBiz'
  })
}

export const getUrlDiffCaseInfo = (caseId)  => {
  return axios({
    method: 'GET',
    params: {
      caseId: caseId
    },
    url: vueContext.env.url + 'urlDiff/caseInfo'
  })
}

export const updateUrlDiffCaseInfo = (caseInfo) => {
  return axios({
    method: 'PUT',
    data: caseInfo,
    url: vueContext.env.url + 'urlDiff/caseInfo'
  })
}