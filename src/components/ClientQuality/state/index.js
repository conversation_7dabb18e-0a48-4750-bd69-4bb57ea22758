/* eslint-disable space-before-function-paren */
import vueContext from '../../../main.js'
import * as api from './api.js'
import { Bus } from '@/global/bus'
/* eslint-disable */
export default {
  state: {
    clientGroups: [],
    businessList: [],
    businessGroup: {},
    userBizList: [],
    currentBusiness: {},
    businessUsers: {},
    configCenterUserPermission: {
      isAdmin: false,
      permissionLevel: 100
    },
    nodeList: [],
    currentTab: '',
    pageList: [],
    allPageList: [],
    currentPage: 1,
    pageSize: 15,
    pageInfo: {},
    pageFilter: {},
    isLoading: false,
    sceneFilter: {},
    sceneList: [],
    sceneInfo: {},
    defaultScenes: [],
    mrnList: [],
    productList: [],
    testItemList: [],
    implList: [],
    categoryType: [],
    urlList: [],
    urlPathInfo: {},
    abList: {},
    abInfo: {},
    pageProductList: [],
    abUserInfo: {},
    caseList: []
  },
  mutations: {
    setClientGroups(state, clientGroups) {
      state.clientGroups = clientGroups
    },
    setBizList(state, businessList) {
      state.businessList = businessList
    },
    setBusienssGroup(state, businessGroup) {
      state.businessGroup = businessGroup
    },
    setUserBizList(state, businessList) {
      state.userBizList = businessList
    },
    setCurrentBusiness(state, info) {
      state.currentBusiness = info
    },
    setBusinessUsers(state, businessUsers) {
      state.businessUsers = businessUsers
    },
    setCurrentTab(state, tab) {
      state.currentTab = tab
    },
    setPermission(state, configCenterUserPermission) {
      state.configCenterUserPermission.isAdmin = configCenterUserPermission.isAdmin
      state.configCenterUserPermission.permissionLevel = configCenterUserPermission.permissionLevel
    },
    setPageList(state, pageList) {
      state.pageList = pageList
    },
    setAllPageList(state, pageList) {
      state.allPageList = pageList
    },
    setCurrentPage(state, page) {
      state.currentPage = page
    },
    setPageInfo(state, pageInfo) {
      state.pageInfo = pageInfo
    },
    setPageFilter(state, filter) {
      state.pageFilter = filter
    },
    setIsLoading(state, status) {
      state.isLoading = status
    },
    setSceneFilter(state, sceneFilter) {
      state.sceneFilter = sceneFilter
    },
    setSceneList(state, sceneList) {
      state.sceneList = sceneList
    },
    setSceneInfo(state, sceneInfo) {
      state.sceneInfo = sceneInfo
    },
    setDefaultScenes(state, scenes) {
      state.defaultScenes = scenes
    },
    setMRNList(state, MRNList) {
      state.mrnList = MRNList
    },
    setProductList(state, productList) {
      state.productList = productList
    },
    setTestItem(state, testList) {
      state.testItemList = testList
    },
    setImplList(state, implList) {
      state.implList = implList
    },
    setCateType(state, categoryType) {
      state.categoryType = categoryType
    },
    setNodeList(state, nodeList) {
      state.nodeList = nodeList
    },
    setUrlList(state, urlList) {
      state.urlList = urlList
    },
    setUrlPathInfo(state, urlPathInfo) {
      state.urlPathInfo = urlPathInfo
    },
    setAbList(state, abList) {
      state.abList = abList
    },
    setAbInfo(state, abInfo) {
      state.abInfo = abInfo
    },
    setAbUserInfo(state, abUserInfo) {
      state.abUserInfo = abUserInfo
    },
    setCaseList(state, caseList) {
      state.caseList = caseList
    },
    setUrlDiffCaseInfo(state, urlDiffCaseInfo) {
      state.urlDiffCaseInfo = urlDiffCaseInfo
    }
  },
  actions: {
    getBusinessGroup(context) {
      api
        .getBusinessGroup()
        .then(res => {
          let message = res.data
          context.commit('setBusienssGroup', message)
        })
        .catch(function(error) {
          console.log(error)
        })
    },
    getBusinessList(context) {
      api
        .getBusinessList()
        .then(res => {
          let message = res.data
          context.commit('setBizList', message)
        })
        .catch(function(error) {
          console.log(error)
        })
    },
    getBusinessByMisId(context, id) {
      api.getBusinessList(id).then(res => {
        let message = res.data
        context.commit('setUserBizList', message)
      })
    },
    getBusinessById(context, id) {
      api.getBusinessById(id).then(res => {
        let message = res.data
        context.commit('setCurrentBusiness', message)
      })
    },
    getUsersByBusinessId(context, id) {
      api.getUsersByBusinessId(id).then(res => {
        let message = res.data
        context.commit('setBusinessUsers', message)
      })
    },
    getBusinessByUser(context, misId) {
      api.getBusinessByUser(misId).then(res => {
        let message = res.data
        context.commit('setBizList', message)
      })
    },
    getGroupsData(context) {
      vueContext
        .$axios({
          method: 'get',
          url: vueContext.env.url + 'clientPage/getGroups'
        })
        .then(res => {
          let message = res.data
          context.commit('setClientGroups', message)
        })
        .catch(function(error) {
          console.log(error)
        })
    },
    getPermission(context, data) {
      vueContext
        .$axios({
          method: 'get',
          params: {
            misId: data.misId,
            businessId: data.businessId
          },
          url: vueContext.env.url + 'autoTestConfig/getUserPermission'
        })
        .then(res => {
          let message = res.data
          context.commit('setPermission', message)
        })
        .catch(function(error) {
          console.log(error)
        })
    },
    getPageListById(context) {
      context.commit('setIsLoading', true)
      let query = context.state.pageFilter
      if (!query.hasOwnProperty('businessId')) {
        query.businessId = vueContext.$route.params.businessId
      }
      if (!query.hasOwnProperty('online')) {
        query.online = 1
      }
      query.pageCount = context.state.currentPage
      if (query.pageSize >= 100) {
        query.pageCount = 1
      }
      api.getPageList(query).then(res => {
        let message = res.data
        context.commit('setPageList', message)
        context.commit('setIsLoading', false)
      })
    },
    getAllPageListById(context, businessId) {
      context.commit('setIsLoading', true)
      let query = {
        online: 1,
        pageSize: 500,
        pageCount: 1,
        isDetail: 0,
        businessId: businessId
      }
      api.getPageList(query).then(res => {
        let message = res.data
        context.commit('setAllPageList', message)
        context.commit('setIsLoading', false)
      })
    },
    getPageInfoById(context, id) {
      api.getPageInfo(id).then(res => {
        let message = res.data
        context.commit('setPageInfo', message)
        context.commit('setCurrentBusiness', message.businessInfo)
        context.dispatch('getPermission', {
          misId: Bus.userInfo.userLogin,
          businessId: message.businessInfo.id
        })
      })
    },
    getSceneList(context) {
      context.commit('setIsLoading', true)
      let query = context.state.sceneFilter
      if (!query.hasOwnProperty('pageId')) {
        query.pageId = vueContext.$route.params.pageId
      }
      query.pageCount = context.state.currentPage
      if (!query.hasOwnProperty('online')) {
        query.online = 1
      }
      api.getSceneList(query).then(res => {
        let message = res.data
        const pageSize = context.state.pageSize
        let pageSized = Math.ceil(message.total / pageSize)
        if (pageSized < context.state.currentPage && pageSized > 0) {
          context.commit('setCurrentPage', pageSized)
          context.dispatch('getSceneList')
        } else {
          context.commit('setSceneList', message)
          context.commit('setIsLoading', false)
        }
      })
    },
    getSceneInfoById(context, id) {
      api.getSceneInfo(id).then(res => {
        let message = res.data
        context.commit('setSceneInfo', message)
        context.commit('setCurrentBusiness', message.businessInfo)
        context.commit('setPageInfo', message.pageInfo)
        context.dispatch('getPermission', {
          misId: Bus.userInfo.userLogin,
          businessId: message.businessInfo.id
        })
      })
    },
    getDefalutScenes(context, businessId) {
      let query = {}
      query.pageId = businessId * -1
      query.isOnline = 1
      api.getSceneList(query).then(res => {
        let message = res.data
        context.commit('setDefaultScenes', message)
      })
    },
    getMRNList(context, type) {
      if (type === undefined) {
        type = 'MRN'
      }
      api.getCategory(type).then(res => {
        let message = res.data
        context.commit('setMRNList', message)
      })
    },
    setPageInfo(context, pageInfo) {
      if (!pageInfo.hasOwnProperty('id')) {
        pageInfo.id = vueContext.$route.params.pageId
      }
      return api.setPageInfo(pageInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
          context.commit('setPageInfo', message)
        }
      })
    },
    deletePageInfo(context, id) {
      return api.deletePageInfo(id).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('delete error')
        } else {
          vueContext.$Message.success('delete success')
        }
      })
    },
    setPageProduct(context, ppInfo) {
      return api.setPageProduct(ppInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('Add Fail')
        } else {
          vueContext.$Message.success('Add Success')
        }
      })
    },
    setPageProductSwitch(context, ppInfo) {
      return api.setPageProductSwitch(ppInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('Open Fail')
        } else {
          vueContext.$Message.success('Open Success')
        }
      })
    },
    deletePageProduct(context, ppId) {
      return api.deletePageProduct(ppId).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('Delete Error')
        } else {
          vueContext.$Message.success('Delete Success')
        }
      })
    },
    setSceneInfo(context, sceneInfo) {
      if (!sceneInfo.hasOwnProperty('id')) {
        sceneInfo.id = vueContext.$route.params.sceneId
      }
      return api.setSceneInfo(sceneInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
        }
      })
    },
    setSceneListInfo(context, sceneListInfo) {
      return api.setSceneListInfo(sceneListInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
        }
      })
    },
    deleteScene(context, sceneId) {
      return api.deleteScene(sceneId).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('delete error')
        } else {
          vueContext.$Message.success('delete success')
        }
      })
    },
    deleteSceneList(context, sceneIds) {
      return api.deleteSceneList(sceneIds).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('delete error')
        } else {
          vueContext.$Message.success('delete success')
        }
      })
    },
    setSceneProduct(context, spInfo) {
      return api.setSceneProduct(spInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
        }
      })
    },
    deleteSceneProduct(context, spId) {
      return api.deleteSceneProduct(spId).then(res => {
        let message = res.data
        if (message.code && message.code === 200) {
          vueContext.$Message.success('delete success')
        } else {
          vueContext.$Message.error('delete error')
        }
      })
    },
    refreshScene(context, sceneId) {
      return api.refreshSceneProduct(sceneId).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('Refresh Error')
        } else {
          vueContext.$Message.success('Refresh Success')
        }
      })
    },
    getProductList(context) {
      if (context.state.productList.length <= 0) {
        return api.getAllProduct().then(res => {
          let message = res.data
          context.commit('setProductList', message)
        })
      }
    },
    getBusinessProduct(context, id) {
      return api.getProductByBusinessId(id).then(res => {
        let message = res.data
        context.commit('setProductList', message)
      })
    },
    setPageUser(context, pageUserInfo) {
      return api.setPageUser(pageUserInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
        }
      })
    },
    deletePageUser(context, pageUserId) {
      return api.deletePageUser(pageUserId).then(res => {
        let message = res.data
        if (message.code && message.code === 200) {
          vueContext.$Message.success('delete success')
        } else {
          vueContext.$Message.error('delete error')
        }
      })
    },
    getTestItemList(context) {
      api.getAllTestItem().then(res => {
        let message = res.data
        context.commit('setTestItem', message)
      })
    },
    getImplList(context) {
      api.getImplement().then(res => {
        let message = res.data
        context.commit('setImplList', message)
      })
    },
    getCategoryTypeList(context) {
      api.getCategoryType().then(res => {
        let message = res.data
        context.commit('setCateType', message)
      })
    },
    getNodeList(context, nodeType) {
      api.getNodeList(nodeType).then(res => {
        let message = res.data
        context.commit('setNodeList', message)
      })
    },
    getUrlList(context, pageId) {
      api.getUrlList(pageId).then(res => {
        let message = res.data
        context.commit('setUrlList', message)
        console.log('getUrlList正在执行')
      })
    },
    getUrlPathInfo(context, pathId) {
      api.getUrlPathInfo(pathId).then(res => {
        let message = res.data
        context.commit('setUrlPathInfo', message)
      })
    },
    setUrlInfo(context, urlInfo) {
      return api.setUrlInfo(urlInfo).then(res => {
        let message = res.data
        if (message.code && message.code === 1) {
          vueContext.$Message.error('update error')
        } else {
          vueContext.$Message.success('update success')
        }
      })
    },
    getAbList(context, abFilter) {
      context.commit('setIsLoading', true)
      api.getAbList(abFilter).then(res => {
        let message = res.data
        context.commit('setAbList', message)
        context.commit('setIsLoading', false)
      })
    },
    deleteAb(context, abId) {
      return api.deleteAb(abId).then(res => {
        let message = res.data
        if (message.code && message.code === 200) {
          vueContext.$Message.success('实验删除成功！~')
        }
      })
    },
    getAbDetail(context, abId) {
      return api.getAbDetail(abId).then(res => {
        let message = res.data
        context.commit('setAbInfo', message)
      })
    },
    setAbInfo(context, abInfo) {
      return api.setAbInfo(abInfo).then(res => {
        let message = res.data
        context.commit('setAbInfo', message)
      })
    },
    addAbPageProduct(context, abPageProductInfo) {
      return api.addAbPageProduct(abPageProductInfo).then(res => {
        let message = res.data
        context.commit('setAbInfo', message)
      })
    },
    updateAbPageProduct(context, abPageProductInfo) {
      return api.updateAbPageProduct(abPageProductInfo).then(res => {
        let message = res.data
        context.commit('setAbInfo', message)
      })
    },
    setFixedStrategy(context, data) {
      return api.setFixedStrategy(data).then(res => {
        let message = res.data
        context.commit('setAbInfo', message)
      })
    },
    deleteUserById(context, userInfo) {
      return api.deleteUserById(userInfo).then(res => {
        let message = res.data
        context.commit('setAbUserInfo', message)
      })
    },
    addUser(context, userInfo) {
      return api.addUser(userInfo).then(res => {
        let message = res.data
        context.commit('setAbUserInfo', message)
      })
    },
    changeBiz(context, request) {
      return api.changeBiz(request)
    },
    getCaseList(context, actionId) {
      return api.getCaseInfo(actionId).then(res => {
        let message = res.data
        context.commit('setCaseList', message)
      })
    },
    setSceneCase(context, info) {
      return api.updateSceneCase(info).then(res => {
        let message = res.data
        if (message !== 0) {
          this.$Message.error('更新失败')
        }
      })
    },
    getUrlDiffCaseInfo(context, caseId) {
      return api.getUrlDiffCaseInfo(caseId).then(res => {
        let message = res.data
        context.commit('setUrlDiffCaseInfo', message)
      }) 
    },
    updateUrlDiffCaseInfo(context, caseInfo) {
      return api.updateUrlDiffCaseInfo(caseInfo).then(res => {
        let success = res.data.success
        if (success) {
          vueContext.$Message.success('update success')   
        } else {
          vueContext.$Message.success('update error')
        }
      })
    }
  },

  namespaced: false
}
