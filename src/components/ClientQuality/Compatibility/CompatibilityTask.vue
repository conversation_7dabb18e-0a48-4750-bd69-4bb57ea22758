<template>
    <div style="padding: 10px 0 35px 0">
      <card>
        <div slot="title">
          <Row type="flex" justify="start">
            <template v-if="imageShowOptions.showMode==='task'">
              <Col :md="11" style="text-align: left">
                <div>
                  <img v-if="snPhoneMap[taskInfo.sn].brand.toLowerCase() === 'iphone'" src="/static/img/iPhone.png" style="height: 20px;width: 20px" />
                  <img v-else-if="snPhoneMap[taskInfo.sn].model.toLowerCase() === 'chrome'" src="/static/img/chrome.png" style="height: 18px;width: 18px" />
                  <img v-else-if="snPhoneMap[taskInfo.sn].model.toLowerCase() === 'firefox'" src="/static/img/firefox.png" style="height: 18px;width: 18px" />
                  <img v-else-if="snPhoneMap[taskInfo.sn].model.toLowerCase() === 'webkit'" src="/static/img/nodewebkit-original.png" style="height: 18px;width: 18px" />
                  <img v-else src="/static/img/android_phone.png" style="height: 20px;width: 20px" />
                  &nbsp;<Strong><a @click.prevent="jumpToTaskList(taskInfo)">{{snPhoneMap[taskInfo.sn].model}}</a></Strong>&nbsp;
                  <Tag type="border" color="purple" title="设备系统版本">
                    <Icon v-if="snPhoneMap[taskInfo.sn].brand.toLowerCase() === 'iphone'" type="logo-apple"/>
                    <Icon v-else-if="snPhoneMap[taskInfo.sn].brand.toLowerCase() === 'microsoft'" type="ios-browsers"/>
                    <Icon v-else type="logo-android"/>
                    {{snPhoneMap[taskInfo.sn].version}}
                  </Tag>
                  <Tag type="border" color="geekblue" title="设备分辨率">
                    <Icon v-if="snPhoneMap[taskInfo.sn].brand.toLowerCase() === 'microsoft'" type="md-browsers" />
                    <Icon v-else type="md-phone-portrait" />
                    {{snPhoneMap[taskInfo.sn].screen}}
                  </Tag>
                  <template v-if="taskInfo.errorTitle">
                    <Poptip trigger="hover" :content="taskInfo.errorTitle" placement="bottom">
                      <Badge :status="getBadgeColor(taskInfo)" :text="getTaskStatusLabel(taskInfo)"/>
                    </Poptip>
                  </template>
                  <template v-else>
                    <Badge :status="getBadgeColor(taskInfo)" :text="getTaskStatusLabel(taskInfo)"/>
                  </template>
                  <Progress v-if="getJobProgress(taskInfo) === 100" :stroke-width="5" :percent="100" :status="getProgressColor(taskInfo)" style="width: 150px"/>
                  <Progress v-else :percent="getJobProgress(taskInfo)" :stroke-width="5" :status="getProgressColor(taskInfo)" style="width: 150px" />
                  <Button size="small" type="error" icon="md-square" @click="stopTaskConfirm()" :disabled="getTaskStopDisabled()" ghost>
                    停止任务
                  </Button>
                </div>
              </Col>
            </template>
            <template v-else>
              <Col span="11" style="text-align: left" >
                <Poptip trigger="hover" word-wrap width="500" :content="taskInfo.scheme" placement="right" transfer>
                  <Tag type="border" color="default"><strong><Icon type="ios-paper-outline" /> Scheme {{taskInfo.index+1}}</strong></Tag>
                  <span>{{taskInfo.name}}</span>
                </Poptip>
                <i-switch size="small" style="margin-left: 20px;" v-model="showAllSchemes"/> 显示全部
              </Col>
              <Poptip title="扫码跳转页面" content="content" placement="right" transfer>
                <div slot="content">
                  <qriously :value="taskInfo.scheme" :size="180" />
                </div>
                <Icon type="md-qr-scanner" size="16" style="padding-top: 3px"/>
              </Poptip>
            </template>


            <Col span="8">
              <Tag v-if="this.checkAllGroup.length > 0" color="blue">{{this.checkAllGroup.length}} / {{this.sceneResultList.length}}</Tag>
              <Tag v-else color="default">{{this.checkAllGroup.length}} / {{this.sceneResultList.length}}</Tag>
              <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox>
              <Button size="small" shape="circle" type="info" icon="md-refresh" @click="changePics"
                      :disabled="this.checkAllGroup.length === 0" ghost>
                更新基准图
              </Button>
              <Button size="small" shape="circle" type="primary" icon="md-bookmark" @click="showImageLabel=true" :disabled="this.checkAllGroup.length === 0" ghost>
                对比结果标记
              </Button>
            </Col>
          </Row>
        </div>
        <template  v-if="imageShowOptions.showMode==='task'">
          <div slot="extra">
            <Divider type="vertical" style="height: 25px"/>
            <Button style="padding: 0 2px;color: #2b85e4;font-size: small" type="text"
                    @click="showLog('/task_'+taskInfo.taskId+'.log')">
              <Icon type="md-document" /> Task日志<Icon type="ios-arrow-forward" />
            </Button>
            <Button style="padding: 0 2px;color: #0fccbf;font-size: small" type="text"
                    @click="showLog('/task_'+taskInfo.taskId+'.lyrebird.log')">
              <Icon type="ios-document-outline" /> Lyrebird日志<Icon type="ios-arrow-forward" />
            </Button>
            <Dropdown v-if="getTaskResult(taskInfo).length > 0" placement="bottom-end">
              <Button style="padding: 0 2px;color: #2b85e4;font-size: small" type="text">
                <Icon type="ios-photos-outline" /> 其他<Icon type="ios-arrow-down"></Icon>
              </Button>
              <template #list>
                  <DropdownMenu>
                    <DropdownItem v-for="(tr,i) in getTaskResult(taskInfo)" :key="i">
                      <a v-if="tr.success" target="_blank" :href="tr.url">{{tr.label}}</a>
                      <a v-else target="_blank" :href="tr.url" style="color:red;">{{tr.label}}</a>
                    </DropdownItem>
                  </DropdownMenu>
              </template>
            </Dropdown>
          </div>
        </template>

        <div class="task-view-container">
          <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
            <template v-if="sceneResultList.length > 0">
              <template v-if="imageShowOptions.showMode==='task'">
                <!--如果job类型是智能交互测试，则展示智能交互测试component-->
                <template v-if="jobs[0].jobType==='UITestAgent'">
                  <button @click="AUITestAgentToggleExpand" class="aui-expand-button">{{ AUITestAgentIsExpanded ? '收起' : '展开' }}</button>
                  <div v-if="AUITestAgentIsExpanded">
                    <AUITestAgentResult :sceneResultList="sceneResultList" />
                  </div>
                </template>
                <template v-else-if="hasDFSTraversal(sceneResultList)">
                  <DFSTraversalResult :sceneResultList="sceneResultList" :taskId="taskInfo.taskId"/>
                </template>
                <template v-else>
                  <vue-resizable @resize:move="handleResize" :active="['b']">
                    <DynamicScroller :style="taskViewContainerStyle" :items="sceneResultList" direction="horizontal" :min-item-size="280">
                      <template v-slot="{ item, index, active }">
                        <DynamicScrollerItem :item="item" :active="active" :data-index="index"
                                            style="width: 300px;" class="image-scroll-item">
                          <div class="image-view" @mouseenter="$emit('focusJob', item.jobId)" @mouseleave="$emit('blurJob', item.jobId)">
                            <div style="padding:0px 15px;text-align: left;">
                              <div style="padding-bottom: 3px;">
                                <Poptip trigger="hover" :title="item.name" content="content" placement="bottom-start" transfer>
                                  <Icon type="md-qr-scanner" style="padding-bottom: 1px;padding-right: 5px" size="16"/>
                                  <div slot="content">
                                    <qriously :value="item.scheme" :size="180"/>
                                  </div>
                                </Poptip>
                                <Poptip trigger="hover" word-wrap width="400"
                                        :title="item.name" :content="item.scheme" placement="bottom" transfer>
                                  <Tag type="border" color="default"><strong><Icon type="ios-paper-outline" /> Scheme {{item.index+1}}</strong></Tag>
                                </Poptip>
                                <Checkbox :key="item.id" :label="item.id" style="padding-left: 55px;">选择</Checkbox>
                              </div>
                              <Row style="font-size: 11px" type="flex">
                                <Col :md="15" style="padding-top: 4px;max-width: 100%;" >
                                  <template  v-if="item.hasOwnProperty('baseUpdateTime') || item.hasOwnProperty('baseAppVersion')">
                                    <template>
                                      基准图
                                    </template>
                                    <template v-if="item.hasOwnProperty('baseUpdateTime')">
                                      <Icon type="md-time"/> {{item.baseUpdateTime}}
                                    </template>
                                    <Button size="small" icon="md-trash" type="text" @click="removeBasePic(item)" title="删除基准图"></Button>
                                  </template>
                                </Col>
                                <Col :md="5">
                                  <Dropdown v-if="getExtensionResult(item).length > 0" placement="bottom-end">
                                    <Button size="small" style="color: #2b85e4;margin-top: 3px;" type="text">
                                      其他<Icon type="ios-arrow-down"></Icon>
                                    </Button>
                                    <template #list>
                                        <DropdownMenu>
                                          <a v-for="(tr,i) in getExtensionResult(item)" :key="i" target="_blank" :href="tr.url">
                                            <DropdownItem>
                                              <span v-if="tr.success" style="color: #2b85e4;"> {{tr.label}} </span>
                                              <span v-else style="color:red;"> {{tr.label}} </span>
                                            </DropdownItem>
                                          </a>
                                        </DropdownMenu>
                                    </template>
                                  </Dropdown>
                                </Col>
                              </Row>

                              <Row>
                                <Col :md="10">
                                  <Tooltip placement="bottom-start" theme="light" enterable :content-style="{ maxWidth: '300px', maxHeight: '200px', overflow: 'auto' }">
                                    <Tag class="status-label" :color="statusLabel[item.type].color" @click.native="changeComponentResult(item.componentResults, item.index+1)">
                                      <span class="status-label" @mouseover="showTooltip = false"  >
                                        {{item.resultTitle}}
                                      </span>
                                      <span class="question-icon"  @mouseover="showTooltip = true" >
                                        <Icon type="ios-arrow-down"></Icon>
                                      </span>
                                    </Tag>
                                    <div slot="content">
                                      <div v-if="showTooltip" style="width: 220px; max-height: 50px; overflow-y: auto;  overflow-x: hidden; white-space: normal; overflow-wrap: break-word; word-break: break-word; ">
                                        {{item.resultTitleDesc}}
                                      </div>
                                      <div v-if="!showTooltip" style="width: 300px; display: flex; flex-direction: column;">
                                        <p>
                                          <Icon type="md-alert" color="orange"/>
                                          <a href="https://qa.sankuai.com/visionComponents" target="_blank" style="font-size: 14px; color: orange; font-weight: bold;">插件结果</a>
                                        </p>
                                        <p v-for="componentRes in item.componentResults" :key="componentRes.component">
                                          <span @mouseover="showDetails = true" @mouseleave="showDetails = false" style="font-size: 12px;font-weight: bold;">{{componentRes.component}}</span>
                                          <span  style="padding-left:20px;" v-if="showDetails && componentRes.desc !== ''">{{componentRes.desc}}</span>
                                        </p>
                                      </div>
                                    </div>
                                  </Tooltip>
                                </Col>
                                <Col :md="11">
                                  <template v-if="item.picLocation.indexOf('mp4') > -1">
                                    <Tag class="video-label" color="geekblue" @click.native="showImage(item, index)"><Icon type="ios-play"/>视频详情</Tag>
                                  </template>
                                  <template v-if="item.resultTitle === '出现请求DIFF'">
                                    <Tag class="diff-label" color="orange" @click.native="openDiffModal(item)"><Icon type="ios-paw"/>DIFF结果</Tag>
                                  </template>
                                  <template v-if="item.resultTitle === 'URL DIFF不通过'">
                                    <Tag class="diff-label" color="orange" @click.native="openUrlDiffModal(item)"><Icon type="ios-paw"/>DIFF结果</Tag>
                                  </template>
                                  <template v-else>
                                    <Tooltip placement="bottom-end" theme="light">
                                      <Tag class="comment-label" color="geekblue" v-if="item.label !== undefined && item.label !== ''"><Icon type="ios-bookmark" /> {{ item.label }}</Tag>
                                      <Tag class="comment-label" color="default" v-else>暂无标记</Tag>
                                      <div slot="content">
                                        <p>
                                            <span style="font-size: 10px;color: #2f54eb;">{{ item.label }}</span>
                                        </p>
                                        <p>
                                          <template v-if="item.scoreResult.text < 0.35">
                                            <Progress :percent="item.scoreResult.text*100" :stroke-width="5" status="wrong">
                                              <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                            </Progress>
                                          </template>
                                          <template v-else-if="item.scoreResult.text > 0.98">
                                            <Progress :percent="item.scoreResult.text*100" :stroke-width="5" status="success">
                                              <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                            </Progress>
                                          </template>
                                          <template v-else>
                                            <Progress :percent="item.scoreResult.text*100" :stroke-width="5">
                                              <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                            </Progress>
                                          </template>
                                        </p>
                                        <p>
                                          <template v-if="item.scoreResult.attention < 0.3">
                                            <Progress :percent="item.scoreResult.attention*100" :stroke-width="5" status="wrong">
                                              <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                            </Progress>
                                          </template>
                                          <template v-else-if="item.scoreResult.attention > 0.95">
                                            <Progress :percent="item.scoreResult.attention*100" :stroke-width="5" status="success">
                                              <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                            </Progress>
                                          </template>
                                          <template v-else>
                                            <Progress :percent="item.scoreResult.attention*100" :stroke-width="5">
                                              <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                            </Progress>
                                          </template>
                                        </p>
                                      </div>
                                    </Tooltip>
                                  </template>
                                </Col>
                              </Row>
                            </div>
                            <div style="padding: 15px">
                              <template v-if="item.picLocation.indexOf('mp4') > -1">
                                <ClientVideoPlayer :videoLocation="item.picLocation"></ClientVideoPlayer>
                              </template>
                              <template v-else-if="getAUITestAgentFlag(item)">
                                <a @click="openAUITestAgentResultPage(item)">
                                  <ImageView v-if="item.diffStatus === 'NOT_SIMILAR' && diffImageShow === 'diff'" :imagePath="item.result" :randNum="randNum" :style="{maxWidth:imageShowSize}"/>
                                  <ImageView v-else :imagePath="item.picLocation" :randNum="randNum" :style="{maxWidth:imageShowSize}"/>
                                </a>
                              </template>
                              <template v-else>
                                <a @click="showImage(item, index)">
                                  <ImageView v-if="item.diffStatus === 'NOT_SIMILAR' && diffImageShow === 'diff'" :imagePath="item.result" :randNum="randNum" :style="{maxWidth:imageShowSize}"/>
                                  <ImageView v-else :imagePath="item.picLocation" :randNum="randNum" :style="{maxWidth:imageShowSize}"/>
                                </a>
                              </template>
                            </div>
                          </div>
                        </DynamicScrollerItem>
                      </template>
                    </DynamicScroller>
                  </vue-resizable>
                </template>
              </template>
              <template v-else>
                <div style="text-align: left;overflow: auto;">
                  <div v-for="(item, index) in sceneResultList" :key="item.id" :data-index="index" style="width: 300px;height:500px;display: inline;" class="image-scroll-item">
                    <div class="image-view" @mouseenter="$emit('focusJob', item.jobId)" @mouseleave="$emit('blurJob', item.jobId)">
                      <div style="padding:0px 15px">
                        <div style="padding-bottom: 3px">
                          <template v-if="item.hasOwnProperty('sn')">
                            <Poptip trigger="hover"  :content="'Version:'+snPhoneMap[item.sn].version+'  '+'分辨率:'+snPhoneMap[item.sn].screen"
                                    placement="bottom" transfer>
                              <Tag type="border" color="default">
                                <strong>
                                  <Icon v-if="snPhoneMap[item.sn].brand.toLowerCase() === 'microsoft'" type="md-browsers" />
                                  <Icon v-else type="ios-phone-portrait" />
                                  {{snPhoneMap[item.sn].model}}
                                </strong>
                              </Tag>
                            </Poptip>
                          </template>
                          <Checkbox :key="item.id" :label="item.id" style="padding-left: 55px;">选择</Checkbox>
                        </div>
                        <Row style="font-size: 11px" type="flex">
                          <template  v-if="item.hasOwnProperty('baseUpdateTime') || item.hasOwnProperty('baseAppVersion')">
                            <Col :md="17" style="padding-top: 4px;max-width: 100%;" >
                              <template>
                                基准图
                              </template>
                              <template v-if="item.hasOwnProperty('baseUpdateTime')">
                                <Icon type="md-time"/> {{item.baseUpdateTime}}
                              </template>
                              <Button size="small" icon="md-trash" type="text" @click="removeBasePic(item)" title="删除基准图"></Button>
                            </Col>
                          </template>
                          <Col :md="5">
                            <Dropdown v-if="getExtensionResult(item).length > 0" placement="bottom-end">
                              <Button size="small" style="color: #2b85e4;margin-top: 3px;" type="text">
                                其他<Icon type="ios-arrow-down"></Icon>
                              </Button>
                              <template #list>
                                  <DropdownMenu>
                                    <a v-for="(tr,i) in getExtensionResult(item)" :key="i" target="_blank" :href="tr.url">
                                      <DropdownItem>
                                        <span v-if="tr.success" style="color: #2b85e4;"> {{tr.label}} </span>
                                        <span v-else style="color:red;"> {{tr.label}} </span>
                                      </DropdownItem>
                                    </a>
                                  </DropdownMenu>
                              </template>
                            </Dropdown>
                          </Col>
                        </Row>

                        <Row>
                          <Col :md="11">
                            <template v-if="item.picLocation.indexOf('mp4') > -1">
                              <Tag class="label" color="geekblue" @click.native="showImage(item,index)"><Icon type="ios-play"/>视频详情</Tag>
                            </template>
                            <template v-else>
                              <Tooltip placement="bottom-start" theme="light" enterable>
                                <Tag class="status-label" :color="statusLabel[item.type].color" @click.native="changeComponentResult(item.componentResults, item.index+1)">{{item.resultTitle}}</Tag>
                                <div slot="content"  style="width: 300px; display: flex; flex-direction: column;">
                                  <p>
                                    <Icon type="md-alert" color="orange"/>
                                    <a href="https://qa.sankuai.com/visionComponents" target="_blank" style="font-size: 14px; color: orange; font-weight: bold;">插件结果</a>
                                  </p>
                                  <p v-for="componentRes in item.componentResults" :key="componentRes.component">
                                    <span @mouseover="showDetails = true" @mouseleave="showDetails = false" style="font-size: 12px;font-weight: bold;">{{componentRes.component}}</span>
                                    <span  style="padding-left:20px;" v-if="showDetails && componentRes.desc !== ''">{{componentRes.desc}}</span>
                                  </p>
                                </div>
                              </Tooltip>
                            </template>
                          </Col>
                          <Col :md="11">
                            <Tooltip placement="bottom-end" theme="light">
                              <Tag class="comment-label" color="geekblue" v-if="item.label !== undefined && item.label !== ''"><Icon type="ios-bookmark" /> {{ item.label }}</Tag>
                              <Tag class="comment-label" color="default" v-else>暂无标记</Tag>
                              <div slot="content">
                                <p>
                                    <span style="font-size: 10px;color: #2f54eb;">{{ item.label }}</span>
                                </p>
                                <p>
                                  <template v-if="item.scoreResult.text < 0.35">
                                    <Progress :percent="item.scoreResult.text*100" :stroke-width="5" status="wrong">
                                      <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                    </Progress>
                                  </template>
                                  <template v-else-if="item.scoreResult.text > 0.98">
                                    <Progress :percent="item.scoreResult.text*100" :stroke-width="5" status="success">
                                      <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                    </Progress>
                                  </template>
                                  <template v-else>
                                    <Progress :percent="item.scoreResult.text*100" :stroke-width="5">
                                      <span style="font-size: 10px">语义 {{item.scoreResult.text}}</span>
                                    </Progress>
                                  </template>
                                </p>
                                <p>
                                  <template v-if="item.scoreResult.attention < 0.3">
                                    <Progress :percent="item.scoreResult.attention*100" :stroke-width="5" status="wrong">
                                      <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                    </Progress>
                                  </template>
                                  <template v-else-if="item.scoreResult.attention > 0.95">
                                    <Progress :percent="item.scoreResult.attention*100" :stroke-width="5" status="success">
                                      <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                    </Progress>
                                  </template>
                                  <template v-else>
                                    <Progress :percent="item.scoreResult.attention*100" :stroke-width="5">
                                      <span style="font-size: 10px">元素 {{item.scoreResult.attention}}</span>
                                    </Progress>
                                  </template>
                                </p>
                              </div>
                            </Tooltip>
                          </Col>
                        </Row>
                      </div>
                      <div style="padding: 15px">
                        <template v-if="item.picLocation.indexOf('mp4') > -1">
                          <ClientVideoPlayer :videoLocation="item.picLocation"></ClientVideoPlayer>
                        </template>
                        <template v-else>
                          <a @click="showImage(item,index)">
                            <ImageView v-if="item.diffStatus === 'NOT_SIMILAR' && diffImageShow === 'diff'" :imagePath="item.result" :randNum="randNum" :style="{maxWidth:imageShowSize, height:500 + 'px'}"/>
                            <ImageView v-else :imagePath="item.picLocation" :randNum="randNum" :style="{maxWidth:imageShowSize,height:500 + 'px'}"/>
                          </a>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </template>
          </CheckboxGroup>
        </div>
      </card>
      <Modal v-model="imageShow" :width="modalWidth" v-if="imageShowInfo!== null">
        <div slot="header">
          <Row>
            <Col :md="4">
              <template v-if="imageShowInfo.picLocation.indexOf('mp4') > -1">
                <div>
                  <Tag color="primary">视频详情</Tag>
                </div>
              </template>
              <template v-else>
                <Tooltip placement="bottom-start" theme="light" enterable>
                  <Tag class="status-label" :color="statusLabel[imageShowInfo.type].color" @click.native="changeComponentResult(imageShowInfo.componentResults, imageShowInfo.index+1)">{{imageShowInfo.resultTitle}}</Tag>
                  <div slot="content"  style="width: 300px; display: flex; flex-direction: column;">
                    <p style="display: block;">
                      <Icon type="md-alert" color="orange"/><a href="https://qa.sankuai.com/visionComponents" target="_blank" style="font-size: 14px; color: orange; font-weight: bold;">插件结果</a>
                    </p>
                    <p class="status-label-p" v-for="componentRes in imageShowInfo.componentResults" :key="componentRes.component">
                      <span @mouseover="showDetails = true" @mouseleave="showDetails = false" style="font-size: 12px;font-weight: bold;">{{componentRes.component}}</span>
                      <span style="padding-left:20px;width:auto;" v-if="showDetails && componentRes.desc !== ''">{{componentRes.desc}}</span>
                    </p>
                  </div>
                </Tooltip>
              </template>
            </Col>
            <Col :md="15">
              <h4>
                <Poptip trigger="hover" word-wrap width="500" :content="imageShowInfo.scheme" placement="right" transfer>
                  {{imageShowInfo.name}} <Tag color="default" type="border"><Icon type="ios-paper-outline" />
                  Scheme {{imageShowInfo.index + 1}}</Tag>
                </Poptip>
              </h4>
            </Col>
            <Col :md="5">
              <Tag color="default" type="border">
                <i-switch size="small" v-model="debugMode"></i-switch>
                <Poptip trigger="hover" word-wrap width="500" content="debug模式是用于展示视觉比对过程中关键环节的中间结果,帮助理解比对结果。例如:在使用布局比对并开启debug模式时，展示元素合并展示图。" transfer>
                  <Icon type="ios-bug" />
                </Poptip>
              </Tag>
              <Dropdown v-if="getExtensionResult(imageShowInfo).length > 0" placement="bottom-end">
                <Button size="small" style="color: #2b85e4;margin-top: 1px;border: 1px solid #2b85e4;" type="text">
                  其他<Icon type="ios-arrow-down"></Icon>
                </Button>
                <template #list>
                  <DropdownMenu>
                    <a v-for="(tr,i) in getExtensionResult(imageShowInfo)" :key="i" target="_blank" :href="tr.url">
                      <DropdownItem>
                        <span v-if="tr.success" style="color: #2b85e4;"> {{tr.label}} </span>
                        <span v-else style="color:red;"> {{tr.label}} </span>
                      </DropdownItem>
                    </a>
                  </DropdownMenu>
                </template>
              </Dropdown>
            </Col>
          </Row>
          <Row style="padding-top: 10px" type="flex">
            <Col :md="4">
              <Tag color="default">
                <Icon type="ios-phone-portrait" />
                {{snPhoneMap[imageShowInfo.sn].model}}
              </Tag>
            </Col>
            <Col :md="11" style="text-align: left">
              <template v-if="imageShowInfo.hasOwnProperty('baseUpdateTime')">
                基准图
                <Tag color="default"><Icon type="md-time"/> {{imageShowInfo.baseUpdateTime}}</Tag>
              </template>
              <template v-if="imageShowInfo.hasOwnProperty('baseAppVersion')">
                <Tag color="default"><Icon type="md-pricetags"/> {{imageShowInfo.baseAppVersion}}</Tag>
              </template>
              <Button v-if="imageShowInfo.picLocation.indexOf('png') > -1" size="small" type="info" icon="md-refresh"
                      @click="changeBasePicSingle(imageShowInfo)" ghost>更新</Button>
              <Button size="small" :type="imageShowInfo.view ? 'success' : 'warning'"
              @click="updateViewedStatus(imageShowInfo)" >{{ imageShowInfo.view === true ? '已标记' : '未标记'}}
              </Button>
            </Col>
            <Col :md="3">
              <Button icon="ios-cog" size="small" type="primary" @click="openLink(imageShowInfo)" ghost>页面配置</Button>
            </Col>
            <Col :md="3">
              <Button type="primary" icon="md-share-alt" size="small" @click="copyShareLink" ghost>分享链接</Button>
            </Col>
            <Col :md="3">
              <Button type="primary" size="small" icon="ios-bug" @click="shotBug(imageShowInfo)">ShotBug</Button>
            </Col>
          </Row>
        </div>

        <SchemeResultModal :imageUrlPrefix="imageUrl" :randNum="randNum" :imageShowInfo="imageShowInfo" :imageShowSize="imageShowSize" :openZoom="job.plat === 'Web'" :debugMode="debugMode"></SchemeResultModal>
        <AssertionResultModal :imageShowInfo="imageShowInfo" :openZoom="job.plat === 'Web'" :assertionResult="sceneDetailResult" :imageUrlPrefix="imageUrl" :randNum="randNum" :imageShowSize="imageShowSize"></AssertionResultModal>
        <VideoResultModal v-if="imageShowInfo.resultType === 'video'" :videoDetailResult="sceneDetailResult"></VideoResultModal>
        <div slot="footer">
          <Button :disabled="filterIndex === 0" @click="showPrevImage">上一个</Button>
          <Button :disabled="filterIndex === sceneResultList.length - 1" @click="showNextImage">下一个</Button>
          <Button @click="imageShow=false">取消</Button>
        </div>
      </Modal>
      <shot-bug :showBugInfo="showBugInfo" :bugInfo="bugInfo" v-on:closeBugInfo="showBugInfo=false" @bugSaveLabel="bugSaveLabel"/>
      <Modal v-model="showImageLabel" width="470">
        <p slot="header" style="color:#f60;text-align:center">
          <Icon type="md-flag"></Icon>
          <span>图像对比结果标记</span>
        </p>
        <div style="text-align:left;padding-left: 10px">
          <p style="padding-bottom: 10px; font-weight: 800;color: #2d8cf0">{{"标记Scheme数量: "+this.checkAllGroup.length}}</p>
          <p class="cell">
            <span class="title">类别:</span>
            <RadioGroup style="width: 400px;" v-model="imageLabelInfo.category" @on-change="changeImageLabelCategory">
              <Radio v-for="(item, key) in imageLabelItems" :label="key" :key="key"><span>{{ imageLabelItems[key].name }}</span></Radio>
            </RadioGroup>
          </p>
          <p class="cell">
            <span class="title">标注项:</span>
            <Select size="small"  v-model="imageLabelInfo.label" @on-change="updateImageLabel" ref="labelSelect" clearable>
              <Option  v-for="(item, key) in imageLabelSelectData" :value="item" :key="key">{{item}}</Option>
            </Select>
          </p>
          <p class="cell">
            <span class="title">结果备注:</span>
            <Input v-model="imageLabelInfo.desc" clearable></Input>
          </p>
        </div>
        <div slot="footer">
          <Button type="primary" @click="changePicLabel">确认</Button>
        </div>
      </Modal>
      <DiffResultModal v-model="showDiffModal" :imageShowInfo="imageShowInfo"></DiffResultModal>
      <UrlDiffResultModal v-model="showUrlDiffModal" :imageShowInfo="imageShowInfo"></UrlDiffResultModal>
    </div>
</template>

<script>
    /* eslint-disable */
    import VueResizable from 'vue-resizable'
    import ImageView from "./ImageView";
    import event from "@/assets/event_bus";
    import ShotBug from "../baseComponents/ShotBug";
    import { Bus } from '@/global/bus'
    import ClientVideoPlayer from "./ClientVideoPlayer"
    import SchemeResultModal from "./CompatibilitySchemeResultModal"
    import AssertionResultModal from "./CompatibilityAssertionResultModal"
    import VideoResultModal from "./CompatibilityVideoResultModal"
    import AUITestAgentResult from "./UITestAgentResult.vue"
    import DiffResultModal from "./DiffResultModal.vue"
    import DFSTraversalResult from "./DFSTraversalResult.vue"
    import { number } from 'echarts'
    import UrlDiffResultModal from "./UrlDiffResultModal.vue"

    export default {
      components: {ClientVideoPlayer, ShotBug, ImageView, SchemeResultModal, VueResizable, AssertionResultModal, VideoResultModal, AUITestAgentResult, DiffResultModal, DFSTraversalResult, UrlDiffResultModal},
      name: "CompatibilityTask",
      props:["filterIds","viewAll","imageShowOptions", "taskIndex", "taskInfo", "jobs", "snPhoneMap", "userPermission", "statusLabel", "taskStatusOptions"],
      data(){
        return{
          filterIndex:0,
          showBugInfo:false,
          showTooltip: false,
          explainContent:'',
          showDetails: false,
          bugPicId:0,
          initLabel:null,
          indeterminate: false,
          checkAll: false,
          checkAllGroup: [],
          imageShow:false,
          showImageLabel:false,
          imageLabelItems: {
            '业务变更': {
              'name': '业务变更',
              'data': ['需求更新', '字体更新', '布局更新', '构建问题', '其他RD侧问题']
            },
            '测试数据维护': {
              'name': '测试数据维护',
              'data': ['基准图问题', 'mock数据更新', '页面录制问题', '插件配置问题', '其他QA侧问题']
            },
            "平台问题": {
              'name': '平台问题',
              'data': ['未跳转到被测页面', '页面加载不全（模块/图片）', '轻交互失效', '设备问题', '像素差异', '其他平台问题']
            }
          },
          imageLabelInfo: {
            desc: "",
            category: "业务变更",
            label: null
          },
          imageShowInfo: null,
          diffImageShow:'diff',
          showMRN: false,
          modalWidth: 808,
          imageUrl: 'https://msstest-img.sankuai.com/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/',
          asyncImage: new Image(),
          imageShowSize: "236px",
          taskViewContainerHeight: 600,
          debugMode: false,
          randNum: this.getRandomInt(),
          showAllSchemes: this.imageShowOptions.showMode!=='task',
          esUrl: this.env.es_url + '/app_traversal_service/label_node',
          interactionReason: '',
          showReasonInput: false,
          AUITestAgentIsExpanded: true,
          showDiffModal: false,
          showUrlDiffModal: false
        }
      },
      computed:{
        job: function() {
          return this.jobs[0]
        },
        bugInfo: function() {
          return {
            jobBusiness:this.jobs[0].business,
            autoTestType:'compatibility',
            bugDesc:'',
            relationTask:''
          }
        },
        imageLabelSelectData: function() {
          return this.imageLabelItems[this.imageLabelInfo.category]['data']
        },
        sceneResultList() {
          let sceneResults;
          if (this.showAllSchemes === false && this.imageShowOptions.showMode!=='task') {
            sceneResults = []
            for (let i in this.taskInfo.sceneResultList) {
              let sceneResult = this.taskInfo.sceneResultList[i]
              if (sceneResult.type !== 'AUTO_CHECK_PASS') {
                sceneResults.push(sceneResult)
              }
            }
          } else {
            sceneResults = this.taskInfo.sceneResultList
          }
          if (this.filterIds != null) {
            // 处理成{id:label}的形式，便于下面过滤
            const filterIdMap = {};
            this.filterIds.forEach(filter => {
              filterIdMap[filter.id] = { label: filter.label, view: true };
            });
            if (this.viewAll === true) {
              // 遍历 sceneResults，修改满足条件的 sceneResult 的 label，并添加 view 字段
              sceneResults.forEach(sceneResult => {
                  if (filterIdMap.hasOwnProperty(sceneResult.id)) {
                      sceneResult.label = filterIdMap[sceneResult.id].label;
                      sceneResult.view = filterIdMap[sceneResult.id].view;
                  } else {
                      sceneResult.view = false; // 动态添加 view 字段，表示未标记
                  }
              });
            } else {
                // 先通过 filter 过滤 id，然后 map 中更改过滤后 sceneResult 的 label，并添加 view 字段
                sceneResults = sceneResults
                    .filter(sceneResult => filterIdMap.hasOwnProperty(sceneResult.id))
                    .map(sceneResult => {
                        sceneResult.label = filterIdMap[sceneResult.id].label;
                        sceneResult.view = !filterIdMap[sceneResult.id].view;
                        return sceneResult;
                    });
              }
              this.checkAllGroup = []
          }
          return sceneResults
        },
        imageShowStatus:function(){
          return this.imageShowOptions.imageShowStatus;
        },
        picIdCaseNameMapping: function() {
          let mapping = {}
          let imageInfoList = this.taskInfo.sceneResultList;
          for (let i in imageInfoList){
            mapping[imageInfoList[i].id] = imageInfoList[i].name
          }
          return mapping
        },
        imageIdList:function () {
          let imageIdList = [];
          let imageInfoList = this.sceneResultList;
          for(let i in imageInfoList){
            imageIdList.push(imageInfoList[i].id)
          }
          return imageIdList;
        },
        taskViewContainerStyle:function() {
          return "height: "+ this.taskViewContainerHeight +"px"
        },
        sceneDetailResult: function() {
          let picLocation = this.imageShowInfo.picLocation
          let diffLoaction = this.imageShowInfo.result
          if (typeof (diffLoaction) !== 'undefined' && diffLoaction.startsWith('{')) {
            let schemeResult = eval('(' + diffLoaction + ')')
            schemeResult['pic_location'] = picLocation
            this.imageShowInfo.resultType = schemeResult.type
            return schemeResult
          } else {
            this.imageShowInfo.resultType = 'diff'
            return {}
          }
        }
      },
      mounted(){
        this.imageShowSize = this.imageShowOptions.imageShowSize;
        event.$on("taskShow",()=>{
          this.checkAllGroup=[];
          this.checkAll = false;
          this.indeterminate =false;
        });
        event.$on("changeJobScheme",()=>{
          this.checkAllGroup=[];
          this.checkAll = false;
          this.indeterminate =false;
        });
        event.$on("changeImageShow",()=>{
          if (this.diffImageShow === 'diff') {
            this.diffImageShow = 'run'
          } else {
            this.diffImageShow = 'diff'
          }

        });
        event.$emit("collapsePage","page");
        if(this.$route.query.view){
          let viewId = this.$route.query.view;
          for(let i=0;i<this.taskInfo.sceneResultList.length;i++){
            let imageId = this.taskInfo.sceneResultList[i].id
            if(imageId){
              if(imageId.toString() === viewId){
                this.imageShowInfo = this.taskInfo.sceneResultList[i];
                this.imageShow = true
              }
            }
          }
        }
        if(this.$route.query.imageIndex){
          let imageIndex = this.$route.query.imageIndex;
          for(let i=0;i<this.taskInfo.sceneResultList.length;i++){
            if((i+1).toString() === imageIndex){
              this.imageShowInfo = this.taskInfo.sceneResultList[i];
              this.imageShow = true
            }
          }
        }
      },
      methods:{
        jumpToTaskList(taskInfo) {
          let href = '/microscope/taskList?sn=' + taskInfo.sn + '&taskId=' + taskInfo.taskId
          window.open(href, '_blank')
        },
        getTaskResult(taskInfo) {
          let result_json = taskInfo.taskResult
          try {
            return JSON.parse(result_json)
          } catch(e) {
            return []
          }
        },
        bugSaveLabel(newLabelFromChild) {
          this.imageLabelInfo.category = "业务变更"
          this.imageLabelInfo.label = newLabelFromChild;
          this.imageLabelInfo.desc = ""
          this.checkAllGroup = [];
          this.checkAllGroup.push(this.bugPicId);
          this.changePicLabel();
        },
        getJob(jobId) {
          for (let i in this.jobs) {
            let job = this.jobs[i]
            if (job.id == jobId) {
              return job
            }
          }
        },
        getRandomInt() {
          return Math.round(Math.random() * 100)
        },
        getSceneUpdate() {
          let picIds = this.checkAllGroup
          this.$axios({
            method:"get",
            params:{
              "picIds":picIds.toString()
            },
            url:this.env.url+"client/compatibility/getSceneUpdate"
          }).then((res) => {
            let message = res.data;
            if(message.code===0) {
              let newSceneList = message.newSceneList
              this.updateResultList(newSceneList)
            }
          }).catch(function (error) {
            console.log(error)
          })
        },
        updateResultList(newSceneList) {
          if (newSceneList != null) {
            newSceneList.forEach(newScene => {
            // 找到 sceneResultList 中索引匹配的对象
            let index = this.sceneResultList.findIndex(result => result.id === newScene.id);
            if (index !== -1) {
              // 替换需要的信息
              this.sceneResultList[index].baseUpdateTime = newScene.baseUpdateTime;
            }
            });
          }
      },
        changeImageLabelCategory() {
          this.$refs.labelSelect.clearSingleSelect();
          this.imageLabelInfo.desc = ''
        },
        updateImageLabel(value) {
          if(value != undefined) {
            this.imageLabelInfo.label = value
          } else {
            this.imageLabelInfo.label = null
          }
        },
        getTaskStopDisabled() {
          if (!this.userPermission.isAdmin && !this.userPermission.isMember) {return true;}
          return !(this.taskInfo.status === 'Waiting' || this.taskInfo.status === 'Running');
        },
        openDiffModal(image) {
          this.imageShowInfo = image
          this.showDiffModal = true
        },
        openUrlDiffModal(image) {
          this.imageShowInfo = image
          this.showUrlDiffModal = true
        },
        showImage(image,index){
          // 计算当前Job
          let job = this.getJob(image.jobId)
          this.imageShow=true;
          this.filterIndex = index
          console.log('index',this.filterIndex)
          this.imageShowInfo = image;
          if (job.plat === 'Web') {
            this.getModalWidth(this.imageUrl + image.picLocation)
          }
        },
        showPrevImage() {
          if (this.filterIndex > 0) {
              this.filterIndex--
              this.imageShowInfo = this.sceneResultList[this.filterIndex]
          }
        },
        showNextImage() {
          if (this.filterIndex < this.sceneResultList.length - 1) {
            this.filterIndex++
            if (this.viewAll === false && this.imageShowInfo.view === true) {
              this.filterIndex--
            }
            this.imageShowInfo = this.sceneResultList[this.filterIndex]
          }
        },
        picIdsMapJobs(picIds) {
            let jobIds = [];
            let taskIds = [];
            let sns = [];
            console.log('scenList',this.sceneResultList)
            const filteredItems = this.sceneResultList.filter(item => picIds.includes(item.id));
            console.log('Filtered Items:', filteredItems);
            filteredItems.forEach(item => {
              jobIds.push(item.jobId);
              taskIds.push(item.taskId);
              sns.push(item.sn);
            });
            return {
              jobIds,
              taskIds,
              sns
            };
        },
        stopTaskConfirm(){
          let taskID = this.taskInfo.taskId;
          let user = Bus.userInfo.userLogin
          this.$Modal.confirm({
            title: '任务停止确认',
            content: '确定停止第' + this.taskInfo.taskId + '号任务吗？',
            onOk: () => {
              this.$axios({
                method:"post",
                data:{
                  "user":Bus.userInfo.userLogin
                },
                url:this.env.url+"client/compatibility/taskStop/" + taskID
              }).then((res) => {
                let message = res.data;
                if(message.code === 0){
                  this.$Notice.success({
                    title: "第" + taskID + "号Task已停止"
                  });
                  location.reload();
                }
                else {
                  console.log(message);
                  this.$Notice.error({
                    title: "第" + taskID + "号Task停止失败，错误码：" + message.code.toString()
                  });
                }
              }).catch(function (error) {
                console.log(error);
                this.$Notice.error({
                  title: "第" + taskID + "号Task停止失败"
                });
              })
            }
          });
        },
        openLink(imageInfo){
          let pageInfoId = imageInfo.pageInfoId
          // 查询mmcd对应的sceneId
          this.$axios({
            method:"get",
            params:{
              "pageId":pageInfoId
            },
            url:this.env.url+"clientPage/getPageInfoById"
          }).then((res) => {
            let configId = -1
            let parts = imageInfo.mockId.split('_')
            if (parts.length > 1) {
              configId = parts[1]
            }
            let routeData = null
            if ("scene_id" in res.data) {
              routeData = this.$router.resolve(
              {
                path: '/client/scene/'+ res.data.scene_id +'/inspect',
                query: {
                  configId: configId
                }
              });
            } else {
              routeData = this.$router.resolve(
              {
                path: '/client/pageBoard',
                query: {
                  pageId: pageInfoId,
                  configId: configId
                }
              });
            }
            window.open(routeData.href, '_blank');
          }).catch(function (error) {
          })
        },
        showLog(logUrl){
          let routeData = this.$router.resolve({ path: '/client/logView', query: {  logUrl: this.logUrl()+logUrl } });
          window.open(routeData.href, '_blank');
        },
        showImageLink(){
          let imageLink = window.location.href;
          if (this.$route.query.view) {
            imageLink = imageLink.replace(/&view=(\d+)/i, '&view=' + this.imageShowInfo.id)
          } else {
            imageLink += "&view="+this.imageShowInfo.id
          }
          return imageLink;
        },
        copyShareLink() {
          const imageLink = this.showImageLink();
          const input = document.createElement('input');
          input.setAttribute('value', imageLink);
          document.body.appendChild(input);
          input.select();
          document.execCommand('copy');
          document.body.removeChild(input);
          this.$Message.info({
            content: '已复制到剪切板',
            duration: 10,
            closable: true
          });
        },
        shotBug(imageShowInfo){
          let job = this.getJob(imageShowInfo.jobId)
          let viewUrl = this.env.job_url+job.id+'&view='+this.imageShowInfo.id;
          let viewLink = '<a class="ct-link" href="'+ viewUrl +'" data-auto_update="0">'+ viewUrl +'</a>'
          this.bugInfo.bugDesc = '问题描述: \n\n'+ '页面: '+this.imageShowInfo.name+'\n'+'机型: '+this.snPhoneMap[this.imageShowInfo.sn].model+'\n'+ 'App: '+job.category+' '+job.appVersion+'-'+job.buildId+'\n'+ '查看对比图: '+viewLink+'\n'+ '页面Scheme: '+this.imageShowInfo.scheme+'\n';
          this.showBugInfo=true
          this.bugPicId=imageShowInfo.id
        },
        changePics(){
          if(this.checkAllGroup.length>0){
            this.$Modal.confirm({
              title: '确认保存更新',
              content: "更新数量:"+this.checkAllGroup.length,
              onOk: () => {
                this.$axios({
                  method:"get",
                  params:{
                    "pics":this.checkAllGroup.toString(),
                    "changeTime":this.getDate(),
                    "appVersion":this.job.appVersion,
                    "user":Bus.userInfo.userLogin,
                    "business": this.job.business
                  },
                  url:this.env.url+"client/compatibility/changePics"
                }).then((res) => {
                  let message = res.data;
                  if(message.code===0){
                    this.$Notice.success({
                      title: "更新成功"
                    });
                  }
                  this.getSceneUpdate()
                  this.changeBasePicsLabel(null)
                }).catch(function (error) {
                  console.log(error)
                })
              }
            })
          }
          else{
            this.$Notice.info({
              title: "请选择更新的图像"
            });
          }
        },
        changeBasePicSingle(imageShowInfo){
          let picID = imageShowInfo.id
          this.checkAllGroup = []
          this.checkAllGroup.push(picID)
          let job = this.getJob(imageShowInfo.jobId)
          this.$Modal.confirm({
            title: '确认保存更新',
            content: "更新数量: 1",
            onOk: () => {
              this.$axios({
                method:"get",
                params:{
                  "pics":picID.toString(),
                  "changeTime":this.getDate(),
                  "appVersion":job.appVersion,
                  "user":Bus.userInfo.userLogin,
                  "business": job.business
                },
                url:this.env.url+"client/compatibility/changePics"
              }).then((res) => {
                let message = res.data;
                if(message.code===0){
                  this.$Notice.success({
                    title: "更新成功"
                  });
                }
                this.bugPicId = imageShowInfo.id
                this.getSceneUpdate()
                this.bugSaveLabel(null)
              }).catch(function (error) {
                console.log(error)
              })
            }
          })
        },
        updateViewedStatus(imageShowInfo) {
          this.checkAllGroup = [];
          this.checkAllGroup.push(imageShowInfo.id);
          this.showImageLabel = true;
          this.displayLabel()
        },
        displayLabel(){
          if (this.imageShowInfo.label !== undefined && this.imageShowInfo.label !== null) {
            let showlabel = this.imageShowInfo.label;
            let parts = showlabel.split(',', 2); // 按第一个逗号分割字符串

            if (parts[0].includes(':')) {
              let subParts = parts[0].split(':', 2); // 按第一个冒号分割字符串
              this.imageLabelInfo.category = subParts[0];
              this.imageLabelInfo.label = subParts[1];
            } else {
              this.imageLabelInfo.category = parts[0];
              this.imageLabelInfo.label = null;
            }
            this.imageLabelInfo.desc = parts[1] || ""; // 如果没有第二部分，则设置为空字符串
          } else {
            // 如果 label 未定义或为 null，设置默认值
            this.imageLabelInfo.desc = "";
            this.imageLabelInfo.category = "业务变更";
            this.imageLabelInfo.label = null;
          }
        },
        removeBasePic(image){
          this.page=image.scheme;
          this.name=image.name;
          this.model=this.snPhoneMap[image.sn].model;
          let job = this.getJob(image.jobId)
          this.$Modal.confirm({
            title: '确认删除该基准图',
            content:'页面:'+this.name,
            onOk: () => {
              this.$axios({
                method:"get",
                params:{
                  "page":this.page,
                  "model":this.model,
                  "mockId":image.mockId,
                  "user":Bus.userInfo.userLogin,
                  "caseName": this.name,
                  "business": job.business
                },
                url:this.env.url+"client/compatibility/removeBasePic"
              }).then((res) => {
                let message = res.data;
                if(message.code===0){
                  this.$Notice.success({
                    title: "删除成功"
                  });
                }
              }).catch(function (error) {
                console.log(error)
              })
            }
          })
        },
        changeBasePicsLabel(newLabelFromChild) {
          this.imageLabelInfo.category = "业务变更"
          this.imageLabelInfo.label = newLabelFromChild;
          this.imageLabelInfo.desc = ""
          this.changePicLabel();
        },
        changePicLabel(){
          let imageLabelItem = this.imageLabelItems[this.imageLabelInfo.category]
          let label = imageLabelItem.name
          if (this.imageLabelInfo.label !== null && this.imageLabelInfo.label !== undefined && this.imageLabelInfo.label !== '') {
            label = label + ":" + this.imageLabelInfo.label
          }
          let picIds = this.checkAllGroup
          console.log("picIds",picIds)
          // 根据不同picIds查找不同jobId等信息
          let picsInfo = this.picIdsMapJobs(picIds)
          console.log("picsInfo",picsInfo)
          let picNames = []
          for (let i in picIds) {
            let id = picIds[i]
            if (this.picIdCaseNameMapping[id]) {
              picNames.push(this.picIdCaseNameMapping[id]);
            }
          }
          let data = {
            "pics": picIds.toString(),
            "label": label,
            "desc": this.imageLabelInfo.desc,
            "user":Bus.userInfo.userLogin,
            "business": this.job.business,
            "jobId": picsInfo.jobIds.toString(),
            "taskId": picsInfo.taskIds.toString(),
            "sn": picsInfo.sns.toString()
          }
          // Scheme模式下，images数据中不包含name信息
          if (picNames.length > 0) {
            data["picNames"] = picNames
          }
          this.$axios({
            method:"post",
            data:data,
            url:this.env.url+"client/compatibility/changePicLabel"
          }).then((res) => {
            let message = res.data;
            if(message.code===0){
              this.$Notice.success({
                title: "更新成功"
              });
              this.showImageLabel=false;
              this.imageLabel="";
              this.$emit('selectAutoFilter');
              this.imageShowInfo = this.sceneResultList[this.filterIndex]
              if (this.imageShowInfo != null) {
                this.imageShowInfo.view = true
              }
            }
          }).catch(function (error) {
            console.log(error)
          })
        },
        getSchemaShow(schema){
          let schemaShow = schema;
          if(schema.length > 50){
            schemaShow = schema.slice(0,50)+'...'
          }
          return schemaShow;
        },
        getTime(time){
          return (new Date(time)).getTime()
        },
        getDate(){
            function padToTwoDigits(number) {
            return number.toString().padStart(2, '0');
          }
          let date = new Date();
          let year = date.getFullYear();
          let month = padToTwoDigits(date.getMonth() + 1);
          let day = padToTwoDigits(date.getDate());
          let hours = padToTwoDigits(date.getHours());
          let minutes = padToTwoDigits(date.getMinutes());
          let seconds = padToTwoDigits(date.getSeconds());
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        handleCheckAll() {
          if (this.indeterminate) {
            this.checkAll = false;
          } else {
            this.checkAll = !this.checkAll;
          }
          this.indeterminate = false;
          if (this.checkAll) {
            this.checkAllGroup = this.imageIdList;
          } else {
            this.checkAllGroup = [];
          }
          this.$emit("selectChange", this.taskInfo.taskId, this.checkAllGroup)
        },
        checkAllGroupChange(data) {
          console.log(data)
          if (data.length === this.imageIdList.length) {
            this.indeterminate = false;
            this.checkAll = true;
          } else if (data.length > 0) {
            this.indeterminate = true;
            this.checkAll = false;
          } else {
            this.indeterminate = false;
            this.checkAll = false;
          }
          this.$emit("selectChange", this.taskInfo.taskId, this.checkAllGroup)
        },
        logUrl(taskId){
          let url = taskId ? this.env.comp_url+"/static/result/task_"+taskId  : this.env.comp_url+"/static/result/task_"+this.taskInfo.taskId;
          return url
        },
        getMRNBundle(item) {
          let url = this.logUrl(item.taskId)+'/'+'task_'+item.taskId+'_'+item.index+'.mrn_bundles.log'
          if (this.imageShowOptions.showMode === 'task') {
            url = this.logUrl()+'/'+'task_'+this.taskInfo.taskId+'_'+item.index+'.mrn_bundles.log';
          }
          return url
        },
        getAUITestAgentFlag(item) {
          let jobInfo = this.getJob(item.jobId)
          if (jobInfo.jobType === 'UITestAgent') return true
          else return false
        },
        getExtensionResult(item) {
          let exRes = []
          let result_json = item.extensionRes
          try {
            exRes = JSON.parse(result_json)
          } catch(e) {
            exRes = []
          }
          // 将RN的信息放在列表第一个
          exRes.splice(0, 0, {
            "label": 'RN',
            "url": this.getMRNBundle(item),
            "desc": '',
            "success": true
          })
          return exRes
        },
        getTaskStatusLabel(taskInfo) {
          let label = ''
          for (let i in this.taskStatusOptions) {
            let statusOption = this.taskStatusOptions[i]
            if (taskInfo.status === statusOption.name) {
              label = statusOption.label
            }
          }
          return label
        },
        getJobProgress (taskInfo) {
          let percent = Math.round(taskInfo.schemeCount / taskInfo.totalSchemeCount * 100);
          if(percent > 100) {
            percent = 100
          }
          return percent
        },
        getProgressColor(taskInfo) {
          let color = 'success';
          if(taskInfo.status === 'Error') {
            color = "wrong"
          } else if(taskInfo.status === 'Waiting' || taskInfo.status === 'Running') {
            color = "active"
          } else if (taskInfo.status === 'Success') {
            color = 'success'
          } else if (taskInfo.status === 'Suspend') {
            color = 'normal'
          }
          return color;
        },
        getBadgeColor(taskInfo) {
          let color = 'success';
          if(taskInfo.status === 'Error') {
            color = "wrong"
          } else if(taskInfo.status === 'Waiting') {
            color = 'warning'
          } else if (taskInfo.status === 'Running') {
            color = "processing"
          } else if (taskInfo.status === 'Success') {
            color = 'success'
          } else if (taskInfo.status === 'Suspend') {
            color = 'default'
          }
          return color;
        },
        getModalWidth(url) {
          this.asyncImage.src = url
          this.asyncImage.onload = this.imageOnLoad
        },
        imageOnLoad() {
          let imageWidth = this.asyncImage.naturalWidth
          let imageHeight = this.asyncImage.naturalHeight
          let width = 0
          if (imageWidth < imageHeight || this.job.plat !== 'Web') {
            width = 236
          } else {
            width = 500
          }
          this.imageShowSize = width + "px";
          this.modalWidth = width * 3 + 100
        },
        handleResize (data) {
          this.taskViewContainerHeight = data.height;
          console.info(data.height)
        },
        openAUITestAgentResultPage (item) {
          let routeData = this.$router.resolve({
            path: '/microscope/auitestagentResult',
            query: {
              taskId: item.taskId,
              schemeIndex: item.index,
              pageInfoId: item.pageInfoId,
              mockId: item.mockId,
              jobId: item.jobId,
              id: item.id
            }
          });
          window.open(routeData.href, '_blank');
        },
        AUITestAgentToggleExpand() {
          this.AUITestAgentIsExpanded = !this.AUITestAgentIsExpanded;
        },
        checkItemForDFSTraversal(componentResults) {
          if (!Array.isArray(componentResults)) {
            return false;
          }
          return componentResults.some(result => result && result.component === 'DFSNodeTraversal');
        },
        hasDFSTraversal(sceneList) {
          if (!Array.isArray(sceneList)) {
            return false;
          }
          return sceneList.some(item => this.checkItemForDFSTraversal(item.componentResults));
        },
        changeComponentResult(componentResults, item_index) {
          // 只有插件结果为NodeTraversal才弹出Modal
          if (componentResults.length > 0) {
            if (componentResults[0].component === 'NodeTraversal') {
              if (componentResults[0].title === '(无需关注)归属交互无响应区域') {
                this.$Modal.confirm({
                  title: '标注此节点为不可交互节点?',
                  onOk: () => {
                    this.$axios({
                      method: 'post',
                      url: this.esUrl,
                      data: JSON.stringify({
                        'taskid': this.taskInfo.taskId,
                        'index': item_index,
                        'labeled_result': '交互无响应',
                        'labeled_reason': ''
                      }),
                      headers: { 'Content-Type': 'application/json' }
                    }).then((res) => {
                      console.log(res)
                    }).catch(function (error) {
                      console.log(error)
                    })
                  }
                })
              } else if (componentResults[0].title === '交互符合预期'
                        || componentResults[0].title === '交互不符合预期'
                        || componentResults[0].title === '交互后成功响应') {
                this.$Modal.confirm({
                  title: '标记此交互',
                  render: (h) => {
                    return h('div', [
                      h('RadioGroup', {
                        props: {
                          value: '交互无响应'
                        },
                        on: {
                          'on-change': (value) => {
                            this.interactionResult = value
                            if (value !== '交互无响应') {
                              this.showReasonInput = true
                            } else {
                              this.showReasonInput = false
                            }
                          }
                        }
                      }, [
                        h('Radio', { props: { label: '交互无响应' } }),
                        h('Radio', { props: { label: '交互符合预期' } }),
                        h('Radio', { props: { label: '交互不符合预期' } })
                      ]),
                      this.showReasonInput ? h('Input', {
                        props: {
                          placeholder: '输入判断原因'
                        },
                        on: {
                          'on-change': (event) => {
                            this.interactionReason = event.target.value
                          }
                        }
                      }) : null
                    ])
                  },
                  onOk: () => {
                    this.$axios({
                      method: 'post',
                      url: this.esUrl,
                      data: JSON.stringify({
                        'taskid': this.taskInfo.taskId,
                        'index': item_index,
                        'labeled_result': this.interactionResult,
                        'labeled_reason': this.interactionReason
                      }),
                      headers: { 'Content-Type': 'application/json' }
                    }).then((res) => {
                      console.log(res)
                    }).catch(function (error) {
                      console.log(error)
                    })
                  }
                })
              }
            }
          }
        }
      },
    }
</script>

<style scoped>
  .task-view-container{
    white-space: nowrap;
    position: relative;
  }
  .question-icon {
    margin-left: 8px;
    cursor: help;
    font-weight: bold; /* 加粗问号 */
    color: #F5F5DC;
  }
  .image-scroll-item{
    display: flex;
    flex-direction: column;
  }
  .image-view{
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
  }
  .ivu-col-span-md-3 {
    display: block;
    width: 11%;
  }
  .ivu-col-span-md-5 {
    display: block;
    width: 32%;
  }
  .ivu-col-span-md-10 {
    display: block;
    width: 35%;
  }
  .status-label {
    width: 110px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
  }
  .comment-label {
    width: 125px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
  }
  .video-label {
    width: 125px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    cursor: pointer;
  }
  .diff-label {
    width: 125px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    cursor: pointer;
  }
  .text-span {
    word-break: normal;
    width: 230px;
    display: block;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    font-size: 10px;
  }
  .status-label-p {
    overflow: visible;
    font-size: 10px;
    font-weight: normal;
  }

  .aui-expand-button {
  display: flex;
  margin: 0.5em 0; /* 顶部和底部的间距 */
  padding: 0.6em 1.2em; /* 内边距 */
  color: #fff; /* 字体颜色 */
  background-color: #007bff; /* 按钮背景颜色 */
  border: none; /* 无边框 */
  border-radius: 4px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停时显示手型光标 */
  font-size: 1em; /* 字体大小 */
  font-weight: bold;
  text-align: left; /* 左对齐 */
}

.aui-expand-button:hover {
  background-color: #0056b3; /* 悬停时的颜色变化 */
}

.aui-expand-button:focus {
  outline: none; /* 去掉按钮的默认轮廓 */
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* 选中时的阴影效果 */
}
</style>

