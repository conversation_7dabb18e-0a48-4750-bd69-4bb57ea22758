<template>
  <div>
    <div style="padding: 18px 0; display: flex; gap: 15px; justify-content: center;">
      <Button type="dashed" icon="md-add" shape="circle" size="medium" @click="addJobInfo(false)" style="min-width: 160px; height: 40px; padding: 0 20px;">添加配置(简版)</Button>
      <Button type="primary" shape="circle" icon="md-add" size="medium" @click="addJobInfo(true)" style="min-width: 220px; height: 40px; padding: 0 20px;">
        <span style="flex:1">添加持续交付配置</span><span style="font-size:12px;color:#bae7ff">(推荐使用)</span>
      </Button>
    </div>
    <Card>
      <div style="padding: 10px 0;" v-if="userBusinessKeys.size > 0">
        <template>
          <div class="filter-container">
            <div class="filter-item">
              <span class="filter-label">业务线：</span>
              <Select v-model="filtersConf.businessFileter" multiple filterable placeholder="请选择" @on-change="getBusinessUserJob" :max-tag-count="3" size="small" style="margin-right: 10px">
                <Option v-for="userBusiness in userBusinessKeys" :value="userBusiness" :key="userBusiness">{{ userBusiness }}</Option>
              </Select>
            </div>
            <div class="filter-item">
              <span class="filter-label">名称：</span>
              <Input search style="width:180px; margin-right: 10px" v-model="filtersConf.nameFilter" placeholder="请输入任务名称搜索"  size="small" clearable/>
            </div>
            <div class="filter-item">
              <span class="filter-label">misID：</span>
              <Select v-model="filtersConf.misIdFilter" multiple filterable placeholder="请选择"  :max-tag-count="3" size="small" style="margin-right: 10px">
                <Option v-for="user in misIdList" :value="user" :key="user">{{ user }}</Option>
              </Select>
            </div>
            <div class="filter-item">
              <span class="filter-label">平台：</span>
              <Select v-model="filtersConf.categoryFilter" multiple filterable placeholder="请选择"  :max-tag-count="3" size="small" style="margin-right: 10px">
                <Option v-for="category in categoryList" :value="category" :key="category">{{ category }}</Option>
              </Select>
            </div>
            <div class="filter-item">
              <span class="filter-label">类型：</span>
              <Select v-model="filtersConf.typeFilter" multiple filterable placeholder="请选择"  :max-tag-count="3" size="small" style="margin-right: 10px">
                <Option v-for="type in typeList" :value="type" :key="type">{{ type }}</Option>
              </Select>
            </div>
            <Button class="sync-button" type="info" icon="md-sync" size="small" @click="getBusinessUserJob"  >同步配置列表</Button>
          </div>
        </template>
      </div>
      <DataTable :tableColumns="userJobColumns" :tableData="userJobs"></DataTable>
      <Spin size="large" fix v-if="spinShow"></Spin>

    </Card>
    <UserConfigModal ref="userConfigModal" :show="showModal" :isNew="isNew" :jobInfo="jobInfo" pageList="[]" v-on:closeModal="showModal=false" v-on:updateUserJob="updateUserJobList"> </UserConfigModal>
    <UserCronConfigModal :show="showCronModal" :jobInfo="jobInfo" v-on:closeModal="showCronModal=false"> </UserCronConfigModal>
    <CompatibilityPhoneContainer :showPhoneContainer="showPhoneContainer" :appPlat="appPlat" :userJob="jobInfo"
                                 v-on:closePhoneInfo="showPhoneContainer=false"></CompatibilityPhoneContainer>
  </div>
</template>

<script>
    /* eslint-disable */
    import DataTable from "../baseComponents/DataTable";
    import { Bus } from '@/global/bus';
    import CompatibilityPhoneContainer from "./CompatibilityPhoneContainer";
    import vueJsonEditor from 'vue-json-editor'
    import UserConfigModal from "./CompatibilityUserConfigModal"
    import UserCronConfigModal from "./CompatibilityUserCronConfigModal.vue"
    export default {
      name: "CompatibilityUserConfig",
      components: {CompatibilityPhoneContainer, vueJsonEditor, DataTable, UserConfigModal, UserCronConfigModal},
      data(){
        return{
          userJobColumns:[
            {
              title: 'Id',
              type: 'index',
              align: 'center',
              width:80,
              fixed: 'left'
            },
            {
              title:"名称",
              key:"job_name",
              align: 'center',
              width: 200,
              fixed: 'left',
              render:(h, params)=>{
                let display_name = (params.row.job_name === undefined || params.row.job_name === "") ? ('job_' + params.row.id) :  params.row.job_name;
                return h("p", {
                }, display_name)
              }
            },
            {
              title:"平台",
              key:"platform",
              align: 'center',
              width: 100,
              render:(h, params)=>{
                if (JSON.parse(params.row.app_params).runEngine === 'Web') {
                  return h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px'
                    },
                    attrs: {
                      src: '/static/img/web.png',
                      class: 'layout-logo',
                      title: 'Web'
                    }
                  })
                }
                if (params.row.platform.toUpperCase().trim() === 'IOS') {
                  return h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px'
                    },
                    attrs: {
                      title: 'iOS',
                      class: 'layout-logo',
                      src: '/static/img/ios.png'
                    }
                  })
                } else if (params.row.platform.toUpperCase().trim() === 'ANDROID') {
                  return h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px'
                    },
                    attrs: {
                      src: '/static/img/android.png',
                      class: 'layout-logo',
                      title: 'Android'
                    }
                  })
                } else if (params.row.platform.toUpperCase().trim() === 'HARMONY') {
                  return h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px'
                    },
                    attrs: {
                      src: '/static/img/harmony.png',
                      class: 'layout-logo',
                      title: 'Harmony'
                    }
                  })
                } else if (params.row.platform.toUpperCase().trim() === 'WEB') {
                  return h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px'
                    },
                    attrs: {
                      src: '/static/img/web.png',
                      class: 'layout-logo',
                      title: 'Web'
                    }
                  })
                } else {
                  return h('div', [
                    h('img', {
                      style: {
                        textAlign: 'center',
                        height: '24px',
                        width: '24px'
                      },
                      attrs: {
                        class: 'layout-logo',
                        title: 'iOS',
                        src: '/static/img/ios.png'
                      }
                    }),
                    h('img', {
                      style: {
                        textAlign: 'center',
                        height: '24px',
                        width: '24px'
                      },
                      attrs: {
                        title: 'Android',
                        class: 'layout-logo',
                        src: '/static/img/android.png'
                      }
                    }),
                  ])

                }
              },
            },
            {
              title:"App",
              key:"category",
              align: 'center',
              width: 150,
              render: (h, params) => {
                let src = '';
                switch (params.row.category) {
                  case '美团':
                    src = '/static/img/meituan_logo.png';
                    break
                  case '点评':
                    src = '/static/img/dianping_logo.png';
                    break
                  case '微信':
                    src = '/static/img/weixin_logo.png';
                    break
                  case '快手':
                    src = '/static/img/kuaishou_logo.png';
                    break
                  case '百度地图':
                    src = '/static/img/baidumap_logo.png';
                    break
                  case '开店宝':
                    src = '/static/img/kaidianbao_logo.png';
                    break
                  case '优选':
                  case '美团优选':
                    src = '/static/img/youxuan_logo.png';
                    break
                  case '买菜':
                  case '美团买菜':
                    src = '/static/img/maicai_logo.png';
                    break
                  case '商家':
                  case '美团商家':
                  case '美团酒店商家':
                  case '酒店商家':
                    src = '/static/img/jiudianshangjia_logo.png';
                    break
                  default:
                    src = '/static/img/app_logo.png';
                    break
                }
                return h('div', [
                  h('img', {
                    style: {
                      textAlign: 'center',
                      height: '24px',
                      width: '24px',
                      marginLeft: '0',
                      marginRight: '5px',
                    },
                    attrs: {
                      title: params.row.category,
                      class: 'layout-logo',
                      src: src
                    }
                  }),
                  h('span', params.row.category)
                ])
              }
            },
            {
              title:"业务",
              key:"business",
              align: 'center',
              width:150
            },
            {
              title:"任务类型",
              key:"type",
              align: 'center',
              width:130,
              render:(h, params)=>{
                let color = params.row.type === 'compatibility' ? 'blue' :  'green';
                let show = this.getTestItemLabel(params.row.type);
                return h("Tag", {
                  props: {
                    color:  color
                  },
                }, show)
              }
            },
            {
              title:"安装包",
              width: 310,
              align: 'center',
              key:"app_url",
              render:(h, params)=>{
                let app_url = params.row.app_url;
                let show_url = app_url.split('/')[app_url.split('/').length-1];
                if(show_url.length > 45){
                  show_url = show_url.slice(0,45)+'...'
                }
                return h("a",{
                  attrs: {
                    href: app_url,
                    target:"_blank",
                    title: '下载安装包'
                  }
                }, show_url)
              }
            },
            {
              title:"页面数",
              key:"pages",
              align: 'center',
              width: 80,
              render:(h, params)=>{
                let page_count = params.row.pages_len;
                return h("p",{
                }, page_count.toString())
              }
            },
            {
              title:"用户",
              key:"user",
              align: 'center',
              width: 120,
              filterable: true,
              filters: [],
              filterMethod (value, row) {
                return row.user === value;
              }
            },
            {
              title:"MRN包信息",
              key:"action",
              width: 450,
              align: 'left',
              render:(h,params)=> {
                let tagList = [];
                let jData = JSON.parse(params.row.mrn_bundle);
                jData.forEach(function (item, index) {
                  let infoStr = '';
                  for (let k in item) {
                    if (item[k]) {
                      infoStr += k + ': ' + item[k] + ' | '
                    }
                  }
                  infoStr = infoStr.substr(0, infoStr.length-2);
                  if (infoStr) {
                    tagList.push(h('li', infoStr))
                  }
                })
                return h('ul', tagList)
              }
            },
            {
              title:"操作",
              width:200,
              align: 'center',
              fixed: 'right',
              render:(h, params)=>{
                return h('div',[
                  h('Button', {
                    props: {
                      type: 'success',
                      size: 'small',
                      icon: 'ios-play'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    attrs: {
                      title: '启动Job运行'
                    },
                    on: {
                      click: () => {
                        this.getPageByConfigId(params.row,"run")
                      }
                    }
                  }),
                  h('Button',{
                    props: {
                      type:'info',
                      size: 'small',
                      icon: 'md-create'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    attrs: {
                      title: '编辑Job配置'
                    },
                    on: {
                      click: () => {
                        this.getPageByConfigId(params.row,"edit")
                      }
                    }
                  }),
                  h('Button',{
                    props: {
                      type:'info',
                      size: 'small',
                      icon: 'ios-time'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    attrs: {
                      title: '编辑Job定时配置'
                    },
                    on: {
                      click: () => {
                        this.editCronJob(params.row)
                      }
                    }
                  }),
                  h('Button',{
                    props: {
                      type:'info',
                      size: 'small',
                      icon: 'ios-copy'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    attrs: {
                      title: '复制Job配置'
                    },
                    on: {
                      click: () => {
                        this.copyJob(params.row)
                      }
                    }
                  }),
                  h('Button', {
                    props: {
                      size: 'small',
                      icon: 'ios-trash',
                      type: 'error',
                    },
                    attrs: {
                      title: '删除Job配置',
                      disabled: Bus.userInfo.userLogin !== params.row.user
                    },
                    on: {
                      click: () => {
                        this.checkRemoveJob(params.row)
                      }
                    }
                  })
                ])
              }
            },
          ],
          initFilterConf:true,
          userJob:[],
          userJobTemp:[],
          filtersConf: {
              nameFilter: "",
              misIdFilter: [],
              categoryFilter:[],
              typeFilter:[],
              businessFileter:[]
            },
          misIdList:[],
          categoryList:[],
          typeList:[],
          isNew: true,
          jobInfo: null,
          searchText: '',
          showModal: false,
          showCronModal: false,
          userBusinessKeys: new Set(),
          businessSelect: {},
          spinShow: true,
          showPhoneContainer: false,
          appPlat:'iOS',
          testItemLabel: []
        }
        },
        computed: {
          userJobs: function () {
            // 统一视觉测试的多种别名
            let compatibilityAlias = ['compatibility','cap']
            for (let job of this.userJob) {
              if (compatibilityAlias.includes(job.type)) {
                job.type = 'compatibility'
              }
            }
            return this.userJob
          }
        },
        mounted(){
          this.getGroups()
          this.getUserBusinessFromMMCD()
          this.initFilterName()
          this.getBusinessUserJob()
        },
        methods:{
          updateUserJobList() {
            this.showModal=false
            this.getBusinessUserJob()
          },
          initFilterName() {
            if (this.$route.query.name !== undefined && this.$route.query.name !== null) {
              this.filtersConf.nameFilter = this.$route.query.name
              console.log(this.$route.query.business)
              this.filtersConf.businessFileter.push(this.$route.query.business)
            }
          },
          getTestItemLabel(itemName){
            let label = itemName;
            for(let i=0;i<this.testItemLabel.length;i++){
              if(this.testItemLabel[i].name === itemName){
                label = this.testItemLabel[i].label
                break
              }
            }
            return label
          },
          getTestItems(){
            this.$axios({
              method:"get",
              url:this.env.url+"page/getTestItem"
            }).then((res) => {
              let message = res.data;
              if(Array.isArray(message)){
                this.testItemLabel = message
              }
              // console.log(message);
            }).catch(function (error) {
              console.log(error)
            })
          },
          getUserBusiness(groupList){
            groupList.forEach((groupInfo) => {
              if (groupInfo.user.indexOf(Bus.userInfo.userLogin) >= 0) {
                this.userBusinessKeys.add(groupInfo.group_name);
                this.businessSelect[groupInfo.group_name] = false;
              }
            });
          },
          getUserBusinessFromMMCD(){
            this.$axios({
              method:"get",
              params:{
                "misId": Bus.userInfo.userLogin
              },
              url:this.env.url+"autoTestConfig/getAllBusinessName"
            }).then((res) => {
              let businessList = res.data;
              businessList.forEach((businessInfo) => {
                this.userBusinessKeys.add(businessInfo.business);
                this.businessSelect[businessInfo.business] = false;
              });
              if (this.$route.query.business && !this.userBusinessKeys.has(this.$route.query.business)) {
                this.$Modal.confirm({
                title: '提示',
                content: '<p>无权限</p>',
                onOk: () => {
              }})
              this.filtersConf.businessFileter = []
              this.filtersConf.nameFilter = ''
              this.getBusinessUserJob()
              }
            }).catch(function (error) {
              console.log(error)
            })
          },
          getGroups(){
            this.$axios({
              method:"get",
              url:this.env.url+"clientPage/getGroups"
            }).then((res) => {
              let message = res.data;
              this.oldGroups = message;
              this.getUserBusiness(message);
            }).catch(function (error) {
            })
          },
          addJobInfo(isNew){
            this.isNew = isNew
            this.showModal = true
            this.jobInfo = null
          },
          runJob(row) {
            this.appPlat = this.jobInfo.platform
            this.jobConfigNew = row.job_name.indexOf('【MMCD】') === 0;
            typeof(JSON.parse(row.app_params).runEngine) !== "undefined"?
              this.runEngine = JSON.parse(row.app_params).runEngine:'Mobile';
            this.showPhoneContainer = true;
          },
          editJob(row) {
            if (row.job_name.indexOf("【MMCD】") >= 0) {
              this.isNew = true
            } else {
              this.isNew = false
            }
            this.showModal = true
          },
          editCronJob(row) {
            this.jobInfo = row
            this.showCronModal = true
          },
          copyJob(row){
            this.spinShow = true
            let configId = row.id
            this.$axios({
              method: 'post',
              data: {
                  "id": configId.toString(),
                  "user": Bus.userInfo.userLogin,
              },
              url: this.env.url + "client/compatibility/copyUserJob",
            })
            .then((res) => {
              let message = res.data;
              if (message.code === 0) {
                this.$Notice.success({
                  title: "复制配置成功",
                });
              }
              this.getBusinessUserJob();
            })
            .catch(function (error) {});
            },
          getUserJob(){
            this.$axios({
              method:"get",
              params:{
                'user':Bus.userInfo.userLogin,
              },
              url:this.env.url+"client/compatibility/userJob"
            }).then((res) => {
              let message = res.data;
              this.userJob = message;
              this.userJobTemp = message;
              this.spinShow = false;
              this.$Notice.success({title: "数据更新"});
              this.refreshUserFilters(message);
              this.initFilterConf = false
            }).catch(function (error) {
              console.log(error)
            })
          },
          refreshUserFilters(message) {
            if (this.initFilterConf === true) {
              this.userJobColumns[8]["filters"] = [];
              let users = [];
              let categorys = []
              let types = []
              for (let i=0; i<message.length;i++) {
                if (!users.includes(message[i].user)) {
                  users.push(message[i].user);
                }
                if (!categorys.includes(message[i].category)) {
                  categorys.push(message[i].category);
                }
                if (!types.includes(message[i].type)) {
                  types.push(message[i].type);
                }
              }
              this.misIdList = users;
              this.categoryList = categorys;
              this.typeList = types;
              users.forEach((user) => {
                this.userJobColumns[8]["filters"].push({
                  "label": user,
                  "value": user
                })
              });
            }
          },
          getPageByConfigId(row,value) {
            let configId = row.id
            this.$axios({
              method:"get",
              params: {
                'configId':configId
              },
              url:this.env.url+"client/compatibility/getPageByConfigId"
            }).then((res) => {
              let message = res.data;
              if(message.code === 0) {
                row.pages = message.pages;
                this.jobInfo = row
                if (value === "edit") {
                  this.editJob(row)
                } else if (value === "run") {
                  this.runJob(row)
                }
              }
            }).catch(function (error) {
              console.log(error)
            })
          },
          getBusinessUserJob(){
            this.initFilterConf = true
            this.spinShow = true;
            let businessList = [];
            for (let key in this.businessSelect) {
              if (this.businessSelect[key] === true) {
                businessList.push(key)
              }
            }
            let payload = {}
            const hasFilters = this.filtersConf.businessFileter.length > 0 ||
                               this.filtersConf.misIdFilter.length > 0 ||
                               this.filtersConf.categoryFilter.length > 0 ||
                               this.filtersConf.typeFilter.length > 0 ||
                               this.filtersConf.nameFilter.trim() !== '';
            if (hasFilters) {
              payload["business"] = this.filtersConf.businessFileter.join(",")
              payload["user"] = this.filtersConf.misIdFilter.join(",");
              payload["category"] = this.filtersConf.categoryFilter.join(",");
              payload["type"] = this.filtersConf.typeFilter.join(",");
              payload["job_name"] = this.filtersConf.nameFilter
              if (!payload["business"]) {
                this.$Modal.confirm({
                title: '提示',
                content: '<p>请选择业务线</p>',
                onOk: () => {
              }})
              this.spinShow = false;
              return
            }
            } else {
              payload["user"] = Bus.userInfo.userLogin;
            }
            this.$axios({
              method:"get",
              params:payload,
              url:this.env.url+"client/compatibility/userJob"
            }).then((res) => {
              let message = res.data;
              this.userJob = message;
              this.userJobTemp = message;
              this.spinShow = false;
              this.refreshUserFilters(message);
              this.initFilterConf = false;
            }).catch(function (error) {
              console.log(error)
            })
          },
          checkRemoveJob(row){
            this.$Modal.confirm({
              title: '确认删除任务',
              content: '<p>确认删除任务</p>',
              onOk: () => {
                this.removeUserJob(row)
              }
            });
          },
          removeUserJob(row){
            this.$axios({
              method:'get',
              params:{
                'jobId':row.id,
              },
              url:this.env.url+"client/compatibility/removeUserJob"
            }).then((res) => {
              let message = res.data;
              if(message.code === 0){
                this.$Notice.success({
                  title: "删除成功"
                });
                this.getBusinessUserJob()
              }
            }).catch(function (error) {
            })
          },
          filterMethod (data, query) {
            return data.label.indexOf(query) > -1;
          }
        }
    }
</script>

<style scoped>
  .bottom{
    text-align: center;
  }
  .filter-container {
    display: flex;
    align-items: center;
    gap: 10px; /* 调整组件之间的间距 */
  }
  .filter-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }
  .sync-button {
  margin-left: 420px;
  }
  .filter-label {
    margin-right: 8px; /* 标签与输入框之间的间距 */
    font-size: 14px; /* 标签字体大小 */
    color: #333; /* 标签字体颜色 */
    white-space: nowrap; /* 防止文字换行 */
    text-overflow: ellipsis; /* 显示省略号 */
    max-width: 100%; /* 确保标签宽度不超过其父容器 */
  }
  /deep/ .jsoneditor-vue{
    height: 350px;
  }
  .app_param{
    padding-bottom: 10px;
  }
</style>
