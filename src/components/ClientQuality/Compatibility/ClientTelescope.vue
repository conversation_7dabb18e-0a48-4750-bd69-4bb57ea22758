<template>
  <div style="padding-left:28px;padding-right:28px">
    <div>
      <h4 class="title" style="padding: 20px 0" align="left">信息概览</h4>
      <ClientBaseInfo :baseInfo="baseInfo"></ClientBaseInfo>
    </div>
    <div>
      <h4 class="title" style="padding: 20px 0" align="left">覆盖设备</h4>
      <DataTable :tableColumns="tableColumns" :tableData="phoneDistribInfo"></DataTable>
    </div>
    <div v-show="isShow">
        <h4 class="title" style="padding: 20px 0" align="left">Job运行情况</h4>
        <ClientJobImageInfo :basePicData="basePicData" :statusResultDistribInfo='statusResultDistribInfo'></ClientJobImageInfo>
        <div>
          <Row>
            <Col :md="12">
            <div id="pageBase" class="chart">
            </div>
            </Col>
            <Col :md="12">
            <div id="structComparison" class="chart">
            </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
</template>

<script>
    /* eslint-disable */
    import Highcharts from 'highcharts';
    import ClientJobImageInfo from "./ClientJobImageInfo";
    import ClientBaseInfo from "./ClientBaseInfo";
    import DataTable from "../baseComponents/DataTable";

    export default {
      name: "ClientTelescope",
      props: ['reportData'],
      components: {ClientBaseInfo,DataTable,ClientJobImageInfo},
      data(){
          return{
            isRequesting:false,
            isShow:true,
            tableColumns:[
              {"title":"品牌","key":"brand",sortable: true},
              {"title":"手机型号","key":"model",sortable: true},
              {"title":"系统","key":"version",sortable: true},
              {"title":"分辨率","key":"screen",sortable: true},
              {"title":"页面一致率","key":"pageSameRate",sortable: true}
            ]
        }
      },
      mounted() {
        if (this.basePicData) {
          this.pieChart("pageBase", "基准图设置情况", this.basePicData);
        }
        if (this.statusResultDistribInfo) {
          this.pieChart("structComparison", "结构对比情况", this.statusResultDistribInfo)
        }
      },
      computed: {
        baseInfo: function() {
          let jobInfo = this.reportData.jobInfoList[0]
          let phoneScenePassRates = this.reportData.phoneScenePassRate
          let totalScenePassRate = phoneScenePassRates.reduce((total, rateInfo) => {
          // 去掉百分号并转换为数值
          let rateValue = parseFloat(rateInfo.scenePassRate.replace('%', ''))
          return total + rateValue}, 0)
          // 计算平均值
          let averageScenePassRate = phoneScenePassRates.length > 0 ? totalScenePassRate / phoneScenePassRates.length : 0
          let formattedAverageScenePassRate = averageScenePassRate.toFixed(2) + '%';
          console.log('Average Scene Pass Rate:', averageScenePassRate, '%')
          return {
            'sourceType': this.getJobInfoShow(jobInfo),
            'jobInfo': jobInfo,
            'priorityDistribInfo': this.getPriorityDistribInfo(),
            'totalScenePassRate': formattedAverageScenePassRate
          }
        },
        phoneDistribInfo: function() {
          return this.getPhoneDistribInfo()
        },
        basePicData: function() {
          let picData = this.getBasePicData()
          return picData
        },
        statusResultDistribInfo: function() {
          return this.getStatusResultDistribInfo()
        }
      },
      methods:{
        getPriorityDistribInfo: function() {
          let priorityDistribution = new Map()
          let priorityNames = []
          for (let i in this.reportData.jobSchema) {
            let schemeData = this.reportData.jobSchema[i]
            let priority = schemeData.priority
            let count = 0
            if (priorityDistribution.has(priority)) {
              count = priorityDistribution.get(priority)
            } else {
              priorityNames.push(priority)
            }
            priorityDistribution.set(priority, count+1)
          }
          priorityNames.sort()
          let distribInfo = ''
          for (let i in priorityNames) {
            let priorityName = priorityNames[i]
            if (distribInfo !== '') {
              distribInfo += ' / '
            }
            distribInfo += priorityName + ':' + priorityDistribution.get(priorityName)
          }
          return distribInfo
        },
        getStatusResultDistribInfo() {
          let distribInfo = []
          for (let i in this.reportData.resultDistribution) {
            let resultDistrib = this.reportData.resultDistribution[i]
            for (let j in resultDistrib.list) {
              let titleDistrib = resultDistrib.list[j]
              if (titleDistrib.title === '无校验辅助' || titleDistrib.title === '无基准图') {
                continue
              }
              distribInfo.push({
                'name': titleDistrib.title,
                'y': titleDistrib.sceneCount
              })
            }
          }
          return distribInfo
        },
        getBasePicData() {
          let hasBasePicNum = 0
          let notHasBasePicNum = 0
          let noneTestMethodNum = 0
          for (let i in this.reportData.jobTask) {
            let taskData = this.reportData.jobTask[i]
            for (let j in taskData.sceneResultList) {
              let sceneResult = taskData.sceneResultList[j]
              if (sceneResult.diffStatus === 'NONE_TEST_METHOD') {
                noneTestMethodNum += 1
              } else if (sceneResult.diffStatus === 'NOT_HAS_BASE') {
                notHasBasePicNum += 1
              } else {
                hasBasePicNum += 1
              }
            }
          }
          return [
            {'name': '已配基准图', 'y': hasBasePicNum},
            {'name': '可配基准图', 'y': notHasBasePicNum},
            {'name': '无需配置', 'y': noneTestMethodNum}
          ]
        },
        getPhoneDistribInfo() {
          let distribInfo = []
          console.log(this.reportData)
          let snPhoneMap = this.reportData.phoneData
          for (let i in this.reportData.phoneScenePassRate) {
            let passRateInfo = this.reportData.phoneScenePassRate[i]
            let phone = snPhoneMap[passRateInfo.sn]
            distribInfo.push({
              'brand': phone.brand,
              'model': phone.model,
              'version': phone.version,
              'screen': phone.screen,
              'pageSameRate': passRateInfo.scenePassRate
            })
          }
          return distribInfo
        },
        getJobInfoShow(jobInfo){
            let sourceType = jobInfo.sourceType;
            if(jobInfo.sourceType === 'gatedLaunch'){
              sourceType = '发布'
            }else if (jobInfo.sourceType === 'regressionTest'){
              sourceType = '回归'
            }else if (jobInfo.sourceType === 'Microscope'){
              sourceType = '定时任务'
            }
            return jobInfo.appVersion+ '-' + sourceType
          },
        pieChart(containerId, title, data) {
          if(data.length > 0){
            Highcharts.chart(containerId, {
              chart: {
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false,
                type: 'pie'
              },
              title: {
                text: title
              },
              colors:["#7CB5EC","#20B2AA","#FFA500","#81C2D6","#8192D6","#FFCCCC","#FF6666","#99CC66"],
              tooltip: {
                pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
              },
              plotOptions: {
                pie: {
                  size:120,
                  allowPointSelect: true,
                  cursor: "pointer",
                  dataLabels: {
                    enabled: true,
                    format: '<b>{point.name}:</b> {point.y}'
                  }
                }
              },
              credits: {
                enabled: false
              },
              series: [{
                name: 'Percent',
                colorByPoint: true,
                data: data
              }]
            });
          }
          else {
            document.getElementById(containerId).hidden=true;
          }
        }
      }
    }
</script>

<style scoped>
  .chart{
    height: 280px;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
