<template>
    <Modal v-model="modalShow" title="健壮性配置" width="1200" @on-cancel="handleClose">
        <template v-if="!isPagesEmpty">
            <Form class="formArea">
                <span>页面</span>
                <Row>
                    <Col span="20">
                    <el-select v-model="robustCustomConfigForm.pages" class="robustInputArea" multiple filterable clearable
                        default-first-option placeholder="选择页面">
                        <el-option v-for="(page, id) in selectedPages" :key="id" :label="page.name" :value="id">
                        </el-option>
                    </el-select>
                    </Col>
                </Row>
                <template>
                    <span>api接口</span>
                    <Row>
                        <Col span="18">
                        <el-select v-model="robustCustomConfigForm.apis" class="robustInputAreaWithButton"
                            v-if="robustMode === 'ERROR_CODE'" multiple filterable clearable allow-create
                            default-first-option placeholder="选择/输入 要修改的接口" @focus="queryRobustApis(false)"
                            :loading="lyrebirdApiLoading">
                            <el-option v-for="item in robustApiList" :key="item" :label="item" :value="item">
                                <template #default>
                                    {{ truncateText(item, 200) }}
                                </template>
                            </el-option>
                        </el-select>
                        <el-select v-model="robustCustomConfigForm.api" class="robustInputAreaWithButton"
                            v-if="robustMode === 'CHANGE_CUSTOM'" filterable clearable allow-create default-first-option
                            placeholder="选择/输入 要修改的接口" @focus="queryRobustApis(false)" :loading="lyrebirdApiLoading">
                            <el-option v-for="item in robustApiList" :key="item" :label="item" :value="item">
                                <template #default>
                                    {{ truncateText(item, 200) }}
                                </template>
                            </el-option>
                        </el-select>
                        </Col>
                        <Col span="2">
                        <el-tooltip class="item" effect="dark" content="从所有已选择页面中,获取对应Lyrebird数据组中的mock数据" placement="top">
                            <el-button icon="el-icon-refresh-right" style="margin-left: 20px;"
                                @click="queryRobustApis(true)"></el-button>
                        </el-tooltip>
                        </Col>
                    </Row>
                </template>
                <template v-if="robustMode === 'CHANGE_CUSTOM'">
                    <span>修改字段</span>
                    <Row>
                        <Col span="20">
                        <el-select v-model="robustCustomConfigForm.jsonPaths" class="robustInputArea" multiple filterable
                            clearable allow-create default-first-option placeholder="选择/输入 对应字段JsonPath"
                            @focus="getJsonPathList">
                            <el-option v-for="item in robustJsonPathList" :key="item.value" :label="item.value"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        </Col>
                    </Row>
                </template>
                <span>{{ robustMode === 'CHANGE_CUSTOM' ? '修改类型' : '目标错误码' }}</span>
                <Row>
                    <Col span="17">
                    <el-select v-if="robustMode === 'CHANGE_CUSTOM'" v-model="robustCustomConfigForm.robustTypes" clearable
                        class="robustChangeTypeSelector" multiple default-first-option placeholder="选择类型">
                        <el-option v-for="(value, key) in robustCustomChangeType" :key="key" :label="value" :value="key">
                        </el-option>
                    </el-select>
                    <el-select v-if="robustMode === 'ERROR_CODE'" v-model="robustCustomConfigForm.errorCodes" clearable
                        class="robustChangeTypeSelector" multiple filterable allow-create default-first-option
                        placeholder="选择/输入 目标错误码">
                        <el-option v-for="item in robustErrorCodeList" :key="item.value" :label="item.value"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    </Col>
                    <Col span="2">
                    <el-button type="primary" icon="el-icon-plus" class="robustConfigSubmitButton"
                        @click="addRubostConfig">增加</el-button>
                    </Col>
                </Row>
            </Form>

            <el-divider content-position="left">已选择测试项</el-divider>

            <template>

                <el-table :data="tableData" tooltip-effect="dark" style="width: 100%" :span-method="arraySpanMethod">
                    <el-table-column prop="page" label="Page页面" show-overflow-tooltip width="200" fixed>
                    </el-table-column>
                    <el-table-column prop="api" label="api接口" show-overflow-tooltip width="400">
                        <template slot-scope="scope">
                            <div style="word-break: break-all; white-space: normal;">{{ scope.row.api }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="robustMode === 'CHANGE_CUSTOM'" prop="robustTypes" label="修改类型" width="200">
                        <template slot-scope="scope">
                            <el-tag type='primary' disable-transitions style="margin-left: 5px;margin-top: 5px;"
                                v-for="(type, index) in scope.row.robustTypes" :key="index">{{ robustCustomChangeType[type]
                                }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="robustMode === 'CHANGE_CUSTOM'" prop="jsonPaths" label="修改key" width="300">
                        <template slot-scope="scope">
                            <el-tag type='primary' disable-transitions style="margin-left: 5px;margin-top: 5px;"
                                effect="plain" v-for="(jsonpath, index) in scope.row.jsonPaths" :key="index">
                                {{ jsonpath }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="robustMode === 'ERROR_CODE'" prop="errorCode" label="错误码" width="300">
                        <template slot-scope="scope">
                            <el-tag type='primary' disable-transitions style="margin-left: 5px;margin-top: 5px;"
                                effect="plain" v-for="(code, index) in scope.row.errorCodes" :key="index">{{ code
                                }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" type="danger" icon="el-icon-delete-solid"
                                @click="deleteRubostConfig(scope.$index)"></el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </template>
        <template v-if="isPagesEmpty">
            未勾选待测试页面, 请先返回上级页面, 确保『目的列表』不为空
        </template>
        <div slot="footer">
            <Button type="info" size="small" @click="handleClose">确定</Button>
        </div>
    </Modal>
</template>
  
<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import vueJsonEditor from "vue-json-editor";
export default {
    name: "RobustConfigModal",
    components: { vueJsonEditor },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        robustMode: {
            type: String,
            default: 'CHANGE_CUSTOM'
        },
        selectedPages: {
            type: Object,
            default: () => ({})
        },
        oldCustomConfig: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            modalShow: this.show,
            robustCustomConfigForm: {
                "pages": [],
                "api": '',
                "apis": [],
                "jsonPaths": [],
                "robustTypes": [],
                "errorCodes": []
            },
            robustCustomChangeType: {
                "change2Empty": "置为空",
                "change2Null": "置为Null",
                "change2Long": "置为超长",
                "change2Delete": "删除字段",
                "change2DiffType": "值类型切换"
            },
            robustErrorCodeList: [
                { "value": "301" },
                { "value": "302" },
                { "value": "304" },
                { "value": "400" },
                { "value": "401" },
                { "value": "403" },
                { "value": "404" },
                { "value": "500" }
            ],
            robustApiList: [],
            robustDataDict: {},
            robustJsonPathApi: '',
            robustJsonPathList: [],
            robustTreeList: [],
            robustSubmitList: {},
            selectedMockCacheId: [],
            selectedPageCacheLimit: 10,
            focusedSelectedPage: '',
            // 前端展示用
            tableData: [],
            spanArr: [],
            isPagesEmpty: false,
            lyrebirdApiLoading: false,
            isLyrebirdApiNeedLoad: true
        };
    },
    computed: {
    },
    mounted() {
        this.processData()
    },
    watch: {
        show(newVal) {
            this.modalShow = newVal
            if (newVal == true) {
                this.isPagesEmpty = Object.keys(this.selectedPages).length == 0
                this.robustSubmitList = this.oldCustomConfig
                this.processData()
            }
        }
    },
    methods: {
        handleClose() {
            this.robustCustomConfigForm = {
                "pages": [],
                "api": "",
                "apis": [],
                "jsonPaths": [],
                "robustTypes": [],
                "errorCodes": []
            }
            this.$emit('close', this.robustSubmitList)
        },
        generateJsonPaths(obj, currentPath = { "value": '$' }) {
            let paths = [];
            if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        const newPath = { "value": `${currentPath.value}.${key}` };
                        paths.push(newPath);
                        paths = paths.concat(this.generateJsonPaths(obj[key], newPath));
                    }
                }
            } else if (Array.isArray(obj)) {
                obj.forEach((item, index) => {
                    const newPath = { "value": `${currentPath.value}[${index}]` };
                    paths.push(newPath);
                    paths = paths.concat(this.generateJsonPaths(item, newPath));
                });
            }
            return paths;
        },
        getJsonPathList() {
            let api = this.robustCustomConfigForm['api']
            if (api === '') { return }
            if (api === this.robustJsonPathApi) { return }
            this.robustJsonPathApi = api
            let mockDataResponse = {}
            for (const [mockId, flow] of Object.entries(this.robustDataDict)) {
                if (flow.hasOwnProperty(api)) {
                    if (flow[api].hasOwnProperty('data')) {
                        try {
                            let jsonObj = JSON.parse(flow[api]['data']);
                            mockDataResponse = jsonObj
                        } catch (error) {
                            console.error('JSON 解析错误:', error);
                        }
                    }
                    mockDataResponse['headers'] = flow[api].headers
                    break
                }
            }
            this.robustJsonPathList = this.generateJsonPaths(mockDataResponse)
        },
        mergeRobustConfigs(api = '') {
            this.robustCustomConfigForm.pages.forEach(pageId => {
                const pageName = (this.selectedPages[pageId] && this.selectedPages[pageId].name) || "";
                const pageMockId = (this.selectedPages[pageId] && this.selectedPages[pageId].mockId) || "";

                if (!pageName || !pageMockId) {
                    console.log("添加配置出现异常,存在场景信息为空!")
                    console.log(this.selectedPages[pageId] || "pageId无对应页面")
                    return;
                }

                if (!this.robustSubmitList[pageId]) {
                    this.$set(this.robustSubmitList, pageId, {
                        name: pageName,
                        mockId: pageMockId,
                        configs: []
                    });
                }

                let newConfig = {}
                if (this.robustMode === 'CHANGE_CUSTOM') {
                    newConfig = {
                        api: this.robustCustomConfigForm.api,
                        jsonPaths: this.robustCustomConfigForm.jsonPaths,
                        robustTypes: this.robustCustomConfigForm.robustTypes,
                        errorCodes: this.robustCustomConfigForm.errorCodes
                    };
                } else if (this.robustMode === 'ERROR_CODE') {
                    newConfig = {
                        api: api,
                        jsonPaths: this.robustCustomConfigForm.jsonPaths,
                        robustTypes: this.robustCustomConfigForm.robustTypes,
                        errorCodes: this.robustCustomConfigForm.errorCodes
                    };
                }

                const existingConfigIndex = this.robustSubmitList[pageId].configs.findIndex(config => config.api === newConfig.api);

                // 当对应page中api已存在时, 新api配置完全覆盖旧api配置
                if (existingConfigIndex !== -1) {
                    this.$set(this.robustSubmitList[pageId].configs, existingConfigIndex, newConfig);
                } else {
                    this.robustSubmitList[pageId].configs.push(newConfig);
                }
            });
        },
        addRubostConfig() {
            if (this.robustMode === 'ERROR_CODE') {
                for (let i = 0; i < this.robustCustomConfigForm["apis"].length; i++) {
                    this.mergeRobustConfigs(this.robustCustomConfigForm["apis"][i])
                }
            } else if (this.robustMode === 'CHANGE_CUSTOM') {
                this.mergeRobustConfigs()
            }
            this.processData()
            this.robustCustomConfigForm = {
                "pages": [],
                "api": "",
                "apis": [],
                "jsonPaths": [],
                "robustTypes": [],
                "errorCodes": []
            }
        },
        deleteRubostConfig(idx) {
            const item = this.tableData[idx];
            const pageId = item.pageId;
            const api = item.api;

            if (this.robustSubmitList[pageId]) {
                const configIndex = this.robustSubmitList[pageId].configs.findIndex(config => config.api === api);
                if (configIndex !== -1) {
                    this.robustSubmitList[pageId].configs.splice(configIndex, 1);

                    // 如果该页面没有配置了，则删除整个页面
                    if (this.robustSubmitList[pageId].configs.length === 0) {
                        this.$delete(this.robustSubmitList, pageId);
                    }

                    // 更新表格数据
                    this.processData();
                }
            }
        },
        queryRobustApis(queryAll) {
            let pages = []
            if (queryAll == true) {
                pages.push(...this.robustCustomConfigForm['pages'])
            } else if (!this.isLyrebirdApiNeedLoad) {
                return
            } else {
                pages.push(this.robustCustomConfigForm['pages'][0])
            }
            this.robustApiList = []
            for (let pageId of pages) {
                // 无缓存再请求接口查询
                let mockId = this.selectedPages[pageId].mockId
                if (!(mockId in this.robustDataDict)) {
                    this.requestPageFlows(mockId)
                } else {
                    let data = this.robustDataDict[mockId]
                    if (data && Object.keys(data).length > 0) {
                        for (const api in data) {
                            if (!this.robustApiList.includes(api)) {
                                this.robustApiList.push(api)
                            }
                        }
                    }
                }
            }
            this.isLyrebirdApiNeedLoad = false
        },
        requestPageFlows(uuid) {
            this.lyrebirdApiLoading = true
            this.$axios({
                method: "get",
                params: {
                    uuid: uuid
                },
                url: this.env.url+"client/lyrebird/mock/group/activate",
            })
                .then((res) => {
                    if (res.data && res.data.flows && Array.isArray(res.data.flows)) {
                        let robustDataDict = {};
                        res.data.flows.forEach(flow => {
                            if (flow.request && flow.request.url && flow.response) {
                                let url = flow.request.url
                                try{
                                    url = this.getOriginalUrlWithoutQuery(url)
                                }catch(e){
                                    console.error(e)
                                }
                                robustDataDict[url] = flow.response;
                            }
                        });
                        // 更新robustApiList
                        this.pushRobustDataDict(uuid, robustDataDict)
                        if (robustDataDict && Object.keys(robustDataDict).length > 0) {
                            for (const api in robustDataDict) {
                                if (!this.robustApiList.includes(api)) {
                                    this.robustApiList.push(api)
                                }
                            }
                        }
                        this.lyrebirdApiLoading = false
                    }
                })
                .catch((error) => {
                    console.log(error);
                    this.lyrebirdApiLoading = false
                });
        },
        // 实现队列操作，限制缓存长度为selectedPageCacheLimit(10条)
        pushRobustDataDict(mockId, data) {
            this.robustDataDict[mockId] = data
            this.selectedMockCacheId.push(mockId)
            if (this.selectedMockCacheId.length > this.selectedPageCacheLimit) {
                let delId = this.selectedMockCacheId.shift()
                this.$delete(this.robustDataDict, delId)
            }
        },
        // 表格数据生成
        processData() {
            this.tableData = Object.entries(this.robustSubmitList).flatMap(([pageId, pageData]) => {
                return pageData.configs.map(config => ({
                    pageId: pageId,
                    page: pageData.name,
                    api: config.api,
                    jsonPaths: config.jsonPaths,
                    robustTypes: config.robustTypes,
                    errorCodes: config.errorCodes
                }));
            });
            this.getSpanArr(this.tableData);
        },
        // 表格展示第一列Page合并
        getSpanArr(data) {
            this.spanArr = [];
            let pos = 0;
            for (let i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1);
                    pos = 0;
                } else {
                    if (data[i].page === data[i - 1].page) {
                        this.spanArr[pos] += 1;
                        this.spanArr.push(0);
                    } else {
                        this.spanArr.push(1);
                        pos = i;
                    }
                }
            }
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0) {
                const _row = this.spanArr[rowIndex];
                const _col = _row > 0 ? 1 : 0;
                return {
                    rowspan: _row,
                    colspan: _col
                };
            }
        },
        // 下拉框超长截断
        truncateText(text, maxLength) {
            if (text.length <= maxLength) {
                return text;
            }
            return text.slice(0, maxLength) + '...';
        },
        getOriginalUrlWithoutQuery(url) {
            const index = url.indexOf('?');
            return index === -1 ? url : url.slice(0, index);
        }
    }
};
</script>
  
<style scoped>
.robustConfigSubmitButton {
    width: 100px;
    margin-left: 15px;
}

.robustInputArea {
    width: 800px;
    margin-bottom: 10px;
}

.robustInputAreaWithButton {
    width: 735px;
    margin-bottom: 10px;
}

.robustTableFormDetail {
    padding-left: 8%;
}

.robustChangeTypeSelector {
    width: 690px;
}

.formArea {
    margin-left: 200px;
}</style>
  