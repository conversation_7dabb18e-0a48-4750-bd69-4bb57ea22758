<template>
  <div class="client-video-player">
    <video-player  class="video-player vjs-big-play-centered vjs-custom-skin"
                   ref="videoPlayer"
                   :playsinline="true"
                   :options="playerOptions"
    ></video-player>
  </div>

</template>

<script>
import 'video.js/dist/video-js.css'
import { videoPlayer } from 'vue-video-player'
export default {
  name: 'ClientVideoPlayer',
  props: ['videoLocation'],
  components: {videoPlayer},
  data () {
    return {
      playerOptions: {
        playbackRates: [0.5, 1.0, 2.0], // 播放速度
        autoplay: false, // 如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [{
          type: 'video/mp4',
          src: this.getVideoURL()
        }],
        poster: this.getPosterURL(),
        notSupportedMessage: '此视频暂无法播放，请稍后再试',
        controlBar: {
          timeDivider: true,
          durationDisplay: true,
          remainingTimeDisplay: false,
          fullscreenToggle: true  // 全屏按钮
        }
      }
    }
  },
  watch: {
    videoLocation: function () {
      this.playerOptions.sources[0].src = this.getVideoURL()
      this.playerOptions.poster = this.getPosterURL()
    }
  },
  methods: {
    getVideoURL() {
      return 'http://msstest.sankuai.com/v1/' +
        'mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/' + this.videoLocation
    },
    getPosterURL() {
      return 'https://msstest-img.sankuai.com/v1/' +
      'mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/' + this.videoLocation.replace('.mp4', '_poster.png') + '@' +
      this.getRandomInt() + 'w'
    },
    getRandomInt() {
      return 500 + Math.round(Math.random() * 100)
    }
  }
}
</script>

<style scoped>
.client-video-player{
  border-radius: 5px;
  border:1px solid darkgray;
  width: 236px;
}

</style>
