<template>
  <div>
    <Modal
      v-model="showModal"
      width="95%"
      @on-cancel="$emit('closeModal', '')"
      @on-visible-change="modalVisibleChange()"
    >
      <template #header>
        <p style="font-size: 22px;">
          <span>裁剪图片</span>
        </p>
      </template>
      <div class="cropper-content" v-if="showCropper">
        <div class="cropper-box">
          <div class="cropper">
            <template v-if="option.img === '' || option.img.endsWith('.png') === false">
              <span class="notice-span">未配置基准图，请点击“本地上传Image”按钮，选择本地图片进行目标元素裁剪</span>
            </template>
            <vue-cropper
                ref="imgCropper"
                :img="option.img"
                :outputSize="option.outputSize"
                :outputType="option.outputType"
                :info="option.info"
                :canScale="option.canScale"
                :autoCrop="option.autoCrop"
                :autoCropWidth="option.autoCropWidth"
                :autoCropHeight="option.autoCropHeight"
                :fixed="option.fixed"
                :fixedNumber="option.fixedNumber"
                :full="option.full"
                :fixedBox="option.fixedBox"
                :canMove="option.canMove"
                :canMoveBox="option.canMoveBox"
                :original="option.original"
                :centerBox="option.centerBox"
                :height="option.height"
                :infoTrue="option.infoTrue"
                :maxImgSize="option.maxImgSize"
                :enlarge="option.enlarge"
                :mode="option.mode"
                @realTime="realTime"
                @imgLoad="imgLoad">
            </vue-cropper>
          </div>
        </div>
        <div style="width: 55%; padding:0 40px;">
          <!--预览效果图-->
          <div class="show-preview">
            <div :style="previews.div" class="preview">
              <img :src="previews.url" :style="previews.img">
            </div>
            <div v-if="minCropInfo.showNoticeMsg" class="notice-span" v-html=" minCropInfo.noticeMsg"></div>
          </div>
          <div class="show-preview">
            <div>
              <Input v-model="downloadUrl" type="textarea" icon="ios-clock-outline" :rows="4" placeholder="生成URL后预览..." ></Input>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <!--底部操作工具按钮-->
        <div class="footer-btn">
          <div class="scope-btn">
            <Tooltip placement="top" max-width=340>
              <label class="btn" for="uploads">本地上传Image</label>
              <input type="file" id="uploads" style="position:absolute; clip:rect(0 0 0 0);" accept="image/png, image/jpeg, image/gif, image/jpg" @change="selectImg($event)">
              <template #content>
                <p><span>从本地选择图片，用于选取目标元素截图</span></p>
                <p>
                  <i>选取后，裁剪操作区域的底图会被选择图片替换。</i>
                  <i>建议在未配置基准图或基准图中无目标元素时使用</i>
                </p>
              </template>
            </Tooltip>
            <el-button size="mini" type="danger" plain icon="el-icon-zoom-in" @click="changeScale(1)" >放大</el-button>
            <el-button size="mini" type="danger" plain icon="el-icon-zoom-out" @click="changeScale(-1)">缩小</el-button>
            <el-button size="mini" type="danger" plain @click="rotateLeft">↺ 左旋转</el-button>
            <el-button size="mini" type="danger" plain @click="rotateRight">↻ 右旋转</el-button>
          </div>
          <div class="upload-btn">
            <span class="index-label">第一步：</span>
            <el-button size="mini" type="success" @click="downloadImg('blob')" :disabled="minCropInfo.showNoticeMsg">生成裁剪Image</el-button>
            <span class="index-label">第二步：</span>
            <el-button size="mini" type="success" @click="uploadImg('blob')" :disabled="minCropInfo.showNoticeMsg">上传S3生成URL</el-button>
            <span class="index-label">第三步：</span>
            <el-button size="mini" type="success" @click="$emit('closeModal', downloadUrl)" :disabled="minCropInfo.showNoticeMsg">确认更新</el-button>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import { VueCropper } from 'vue-cropper'
import createUpload from '@ai/mss-upload-js'
export default {
  name: 'ImageCropper',
  components: {
    VueCropper, createUpload
  },
  props: ['show', 'imgNamePrefix', 'defaultImg', 'noticeMsg', 'cropOption'],
  data() {
    return {
      // cropper需要在modal完全展示后，再加载，否则image展示位置不对
      showCropper: false,
      name: 'Test',
      previews: {},
      downloadUrl: '',
      tenantId: 'mss_29bc475beb7e4563a9a6f802f29acd83',
      minCropInfo: {
        showNoticeMsg: false,
        noticeMsg: this.noticeMsg
      },
      defaultOption: {
        img: '',
        outputSize: 1,       // 裁剪生成图片的质量(可选0.1 - 1)
        outputType: 'png',  // 裁剪生成图片的格式（jpeg || png || webp）
        info: true,          // 图片大小信息
        canScale: true,      // 图片是否允许滚轮缩放
        autoCrop: true,      // 是否默认生成截图框
        autoCropWidth: 80,  // 默认生成截图框宽度
        autoCropHeight: 80, // 默认生成截图框高度
        fixed: false,         // 是否开启截图框宽高固定比例
        fixedNumber: [1, 1], // 截图框的宽高比例
        full: false,         // false按原比例裁切图片，不失真
        fixedBox: false,      // 固定截图框大小，不允许改变
        canMove: true,      // 上传图片是否可以移动
        canMoveBox: true,    // 截图框能否拖动
        original: true,     // 上传图片按照原始比例渲染
        centerBox: true,    // 截图框是否被限制在图片里面
        height: true,        // 是否按照设备的dpr 输出等比例图片
        infoTrue: true,     // true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 10000,    // 限制图片最大宽度和高度
        enlarge: 1,          // 图片根据截图框输出比例倍数
        mode: '100%'  // 图片默认渲染方式
      },
      imgData: ''
    }
  },
  computed: {
    showModal: {
      get: function () {
        return this.show
      },
      set: function () {
        this.show = this.showModal
      }
    },
    option: function () {
      let newOption = this.defaultOption
      if (typeof (this.cropOption) !== 'undefined') {
        newOption = Object.assign({}, this.defaultOption, this.cropOption)
      }
      return newOption
    }
  },
  watch: {
    show: function (val) {
      this.showModal = val
    },
    noticeMsg: function (val) {
      this.minCropInfo.noticeMsg = val
    }
  },
  methods: {
    // 初始化函数
    imgLoad (msg) {
      console.log('工具初始化函数' + msg)
    },
    // 图片缩放
    changeScale (num) {
      num = num || 1
      this.$refs.imgCropper.changeScale(num)
    },
    // 向左旋转
    rotateLeft () {
      this.$refs.imgCropper.rotateLeft()
    },
    // 向右旋转
    rotateRight () {
      this.$refs.imgCropper.rotateRight()
    },
    // 图片剪切判定标准
    imageJudgmentMode(scale, data) {
      let imgHeight = 0
      let imgWidth = 0
      if (this.option.type === 'minimumImageSize') {
        // 语义交互判定
        imgHeight = data.h * this.option.enlarge
        imgWidth = data.w * this.option.enlarge
        if (imgHeight >= this.option.minCropHeight && imgWidth >= this.option.minCropWidth) {
          this.minCropInfo.showNoticeMsg = false
        } else {
          this.minCropInfo.showNoticeMsg = true
        }
      } else if (this.option.type === 'imageRatioThreshold') {
        // 语义识别判定
        let naturalHeight = parseInt(data.img.height)
        let naturalWidth = parseInt(data.img.width)
        imgHeight = data.h / scale
        imgWidth = data.w / scale
        if ((imgHeight * imgWidth) / (naturalHeight * naturalWidth) >= this.option.imageRatioThreshold) {
          this.minCropInfo.showNoticeMsg = false
        } else {
          this.minCropInfo.showNoticeMsg = true
        }
      }
    },
    // 实时预览函数
    realTime (data) {
      this.previews = data
      if (this.minCropInfo.noticeMsg && typeof (data) !== 'undefined') {
        let scale = parseFloat(/scale\((.*)\)/g.exec(data.img.transform)[1])
        this.imageJudgmentMode(scale, data)
      }
    },
    modalVisibleChange() {
      if (this.showModal) {
        this.option.img = this.defaultImg
        this.showCropper = true
      } else {
        this.showCropper = false
        this.option.img = ''
      }
    },
    // 选择图片
    selectImg (e) {
      let file = e.target.files[0]
      if (!/\.(jpg|jpeg|png|JPG|PNG)$/.test(e.target.value)) {
        this.$message({
          message: '图片类型要求：jpeg、jpg、png',
          type: 'error'
        })
        return false
      }
      // 转化为blob
      let reader = new FileReader()
      let _this = this
      this.$Loading.start()
      reader.onload = (e) => {
        let data
        if (typeof e.target.result === 'object') {
          data = window.URL.createObjectURL(new Blob([e.target.result]))
        } else {
          data = e.target.result
        }
        _this.defaultOption.img = data
        _this.$Loading.finish()
        _this.imgData = data
      }
      // 转化为base64
      reader.readAsDataURL(file)
    },
    downloadImg (type) {
      let _this = this
      if (type === 'blob') {
        // 利用a标签自定义下载文件名
        const link = document.createElement('a')
        // 获取截图的blob数据
        _this.$refs.imgCropper.getCropBlob(async (data) => {
          // 创建Blob对象，设置文件类型
          let blob = new Blob([data], {type: 'image/png'})
          // 创建URL
          let objectUrl = URL.createObjectURL(blob)
          link.href = objectUrl
          // 自定义文件名
          link.download = 'cropperImg.png'
          // 下载文件
          link.click()
          // 释放内存
          URL.revokeObjectURL(objectUrl)
        })
      }
    },
    // 上传图片
    uploadImg (type) {
      let _this = this
      if (type === 'blob') {
        // 获取截图的blob数据
        let uploadInstance = createUpload(
          {
            signatureUrl: this.env.url + 'client/s3/visionImage/uploadSign?imgNamePrefix=' + _this.imgNamePrefix, // 加签接口url
            bucket: 'vision-image', // 桶
            hashMode: false, // Boolean,选传，标示上传后的文件名是否有唯一性，默认为 false：使用传入的key或原始文件名；true: 使用当前时间戳加签生成的字符串做文件名
            tenant_id: _this.tenantId, // tenant_id 为 租户唯一标示
            accept: ['.png', 'image/png'],                           // 限定可选文件类型，不传为全部
            sliceSize: 1024 * 1024 * 100,                                 // 每个分片大小，普通上传时无效
            validateFile(file) {                                          // 验证选择的文件是否符合预期，return true则继续，return false则中止后续流程
              if (file.type !== 'image/png') {
                return false
              } else {
                return true
              }
            },
            onProgress(percent) {                                          // 上传进度回调
            },
            onStart: function() {},                                         // 上传开始回调
            onSuccess(fileUrl) {
              _this.downloadUrl = fileUrl                                    // 上传成功回调,fileUrl为上传文件的S3访问url
              console.info(fileUrl)
            },
            onError(errorMsg) {                                            // 上传失败回调
            }
          },
          1    // 1：普通上传  2：分片上传，需要和加签方法一一对应
        )
        // 启动上传
        uploadInstance.upload()
      }
    }
  }
}
</script>

<style scoped>
  .cropper-content{
    display: flex;
    display: -webkit-flex;
    justify-content: flex-end;
  }
  .cropper-content .cropper-box{ 
    flex: 1;
    width: 765px;
    display: block;
  }
  .cropper-content .cropper-box .cropper{
    width:765px;
    height:600px;
    overflow: scroll;
    text-align: center;
    margin: 0 auto
  }

  .cropper-content .show-preview{
    height: 84%;
    justify-content: center;
  }
  .cropper-content .preview{
    overflow: hidden;
    border:1px solid #67c23a;
    background: #cccccc;
  }

  .footer-btn{
    display: flex;
    display: -webkit-flex;
    justify-content: flex-end;
  }
  .footer-btn .scope-btn{
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    padding-right: 10px;
  }
  .footer-btn .upload-btn{
    flex: 1;
    -webkit-flex: 1;
    display: flex;
    display: -webkit-flex;
    justify-content: right;
    padding-right: 80px;
  }
  .footer-btn .upload-btn .index-label {
    padding-top: 5px;
    margin-left: 30px;
    font-weight: 500;
  }
  .footer-btn .btn {
    outline: none;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    -webkit-appearance: none;
    text-align: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: 0;
    -webkit-transition: .1s;
    transition: .1s;
    font-weight: 500;
    padding: 8px 15px;
    font-size: 12px;
    border-radius: 3px;
    color: #fff;
    background-color: #409EFF;
    border-color: #409EFF;
    margin-right: 10px;
  }
  .notice-span {
    color:#ff9900;
    font-weight: 600;
  }
</style>
