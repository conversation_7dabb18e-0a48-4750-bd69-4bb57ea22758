<template>
  <div style="padding: 30px">
    <div style="text-align: left">
      <div style="font-size: 25px;font-weight: 800;">
        Hyperjump开放能力
      </div>
      <div style="padding-top: 5px;font-size: 12px;max-width: 300px">
        快速构建可选的流程控制和检测能力
      </div>
      <div style="padding-top: 5px;font-size: 12px;padding-bottom: 20px">
        <a href="https://km.sankuai.com/page/859483815" target="_blank">
          开发文档<li class="fa fa-long-arrow-right"></li>
        </a>
      </div>
    </div>
    <div style="text-align: left; padding: 10px">
      <Select v-model="currentType" style="width:150px">
        <Option v-for="item in componentTypeOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
    </div>
    <Row>
      <template v-for="visionComponent in visionComponents">
        <template v-if="currentType.indexOf(visionComponent.type) > -1">
          <Col span="6" style="padding: 10px">
            <Card style="height: 180px">
              <Row style="height: 120px">
                <Col span="15" offset="1">
                  <div style="font-size:16px;font-weight: 600; text-align: left">
                    {{visionComponent.name}}
                  </div>
                  <div style="padding-top: 10px;font-size:12px;text-align: left">
                    {{visionComponent.description}}
                  </div>
                </Col>
                <Col span="6" offset="2">
                  <div style="padding-top: 30px">
                    <template v-if="visionComponent.type === 'image_process'">
                      <i class="fa fa-braille fa-2x fa-cog"></i>
                    </template>
                    <template v-if="visionComponent.type === 'flow_control'">
                      <i class="fa fa-usb fa-2x fa-cog"></i>
                    </template>
                    <template v-if="visionComponent.type === 'mode_enhance'">
                      <i class="fa fa-free-code-camp fa-2x fa-cog"></i>
                    </template>
                  </div>
                </Col>
              </Row>
              <Row style="padding-top: 10px">
                <Col span="10" style="text-align: left">
                  <div class="code-link">
                    {{visionComponent.author}}
                  </div>
                </Col>
                <Col span="6" style="text-align: left">
                  <div class="code-link" @click="openCodeLink(visionComponent.name)">
                    <i class="fa fa-file-code-o"> code-v{{visionComponent.version}}</i>
                  </div>
                </Col>
                <Col span="6" offset="1">
                  <template v-if="visionComponent.hasOwnProperty('imageLink')&&visionComponent.imageLink.indexOf('http') > -1">
                    <div class="code-link" @click="openViewLink(visionComponent.imageLink)">
                      <i class="fa fa-file-text-o"> document</i>
                    </div>
                  </template>
                </Col>
              </Row>
            </Card>
          </Col>
        </template>
      </template>
    </Row>
  </div>

</template>

<script>
  /* eslint-disable */
    export default {
        name: "VisionComponents",
        data(){
          return{
            visionComponents:[],
            componentTypeOptions:[
              {
                value: 'image_process,mode_enhance,flow_control',
                label: '全部'
              },
              {
                value: 'image_process',
                label: '图像检测'
              },
              {
                value: 'mode_enhance',
                label: '模式增强'
              },
              {
                value: 'flow_control',
                label: '流程控制'
              }
            ],
            currentType:'image_process,mode_enhance,flow_control'
          }
        },
        mounted() {
          this.getVisionComponents()
        },
        methods:{
            getVisionComponents(){
              this.$axios({
                method:"get",
                url:this.env.url+"compatibility/actuator/visionComponents"
              }).then((res) => {
                let message = res.data;
                this.visionComponents=message;
              }).catch(function (error) {
                console.log(error)
              })
            },
            openCodeLink(name){
              let link = "http://dev.sankuai.com/code/repo-detail/hbqa/toolchain-client-compatibility/file/detail?" +
                "branch=refs%2Fheads%2Fmaster&path=compatibility%2Fvision%2Fcomponents%2F"+name+".py";
              window.open(link);
            },
            openViewLink(viewLink){
              window.open(viewLink)
            }
          }
    }
</script>

<style scoped>
.fa-cog {
  color: #f97583;
}
.code-link{
  font-size: 12px;
}
.code-link:hover{
  font-size: 12px;
  color: #2d8cf0;
  cursor: pointer;
}
</style>
