<template>
  <div style="margin:0 40px;">
    <Breadcrumb style="text-align: left">
      <BreadcrumbItem to="/microscope/jobList"><Icon type="ios-list" size="20"/>Job列表</BreadcrumbItem>
      <BreadcrumbItem><Icon type="ios-paper-outline" size="20"/>Task列表</BreadcrumbItem>
    </Breadcrumb>
    <Row>
      <Col style="margin-left: 30%;">
        <Input search style="width: 160px" v-model="filter.business" placeholder="业务方向" @on-search="refreshTaskList" @on-blur="refreshTaskList" @on-clear="refreshTaskList" clearable/>
        <Input search style="width: 260px" v-model="filter.sn" placeholder="设备SN" @on-search="refreshTaskList" @on-blur="refreshTaskList" @on-clear="refreshTaskList" clearable/>
        <Button @click="getTaskList" type="primary">刷新</Button>
      </Col>
    </Row>
    <br>
    <Table :loading="loading" max-height="800" :data="taskList" :columns="tableColumns">
      <template slot-scope="{ row }" slot="id">
        <Icon type="md-star" v-if="focusTaskId !== null && focusTaskId === row.id"/><span>{{row.id}}</span>
      </template>
      <template slot-scope="{ row }" slot="user">
        <template v-for="(u, i) in row.user.trim().split(',')">
          <Tag :title="u" :key="'index_' + i" :style="{ fontSize: '10px' ,width:'80px' ,height:'20px'}">{{u}}</Tag>
        </template>
      </template>
      <template  slot-scope="{ row }" slot="triggerNode">
        <Tag :title="row.triggerNode" v-if="row.triggerNode!='' && row.triggerNode!= None" color="blue" :style="{ fontSize: '10px' ,width:'120px' ,height:'20px'}">{{row.triggerNode}}</Tag>
      </template>
      <template slot-scope="{ row }" slot="taskStatus">
          <tag v-if="row.status=='Error'" color="error">失败</tag>
          <tag v-else-if="row.status=='Success'" color="success">成功</tag>
          <tag v-else-if="row.status=='Waiting'" color="warning">排队中</tag>
          <tag v-else-if="row.status=='Running'" :color="getTagColor(row)" style="color: black;">运行中</tag>
          <tag v-else-if="row.status=='Suspend'" color="orange">已挂起</tag>
          <tag v-else-if="row.status=='Warning'" color="purple">异常</tag>
          <tag v-else color="default">已终止</tag>
      </template>
      <template slot-scope="{ row }" slot="source">
          <a :href="row.sourceUrl" target="'_blank'">{{ row.sourceType }}</a>
      </template>
      <template slot-scope="{ row }" slot="appInfo">
          <span> {{row.appName}}  {{ row.appVersion }} </span>
      </template>
      <template slot-scope="{ row }" slot="actions">
        <div class="button-container">
          <Button class="button" type="info" size="small" @click="showJobReport(row)" title="查看报告详情">
          <Icon type="ios-book" />
          </Button>
          <Button class="button" v-if="row.status === 'Waiting' || row.status === 'Running' " type="warning" size="small" @click="changeTaskStatusHandler(row, 'Suspend')"  title="暂停任务">
            <Icon type="ios-pause" />
          </Button>
          <Button class="button" v-if="row.status === 'Suspend'" type="primary" size="small" @click="changeTaskStatusHandler(row, 'Waiting')"  title="运行任务">
            <Icon type="ios-play" />
          </Button>
          <Button class="button" v-if="row.status === 'Waiting' || row.status === 'Suspend' || row.status === 'Running'" size="small" @click="changeTaskStatusHandler(row, 'Stop')"  title="终止任务">
            <Icon type="md-square"  color="#ed4014" />
          </Button>
          <Button class="button" v-if="row.status === 'Success'|| row.status === 'Stop' || row.status === 'Error' || row.status === 'Warning'" type="primary" size="small" @click="changeTaskStatusHandler(row, 'Waiting')" title="重新执行">
            <Icon type="md-refresh" />
          </Button>
        </div>
      </template>
    </Table>
    <div style="float:right; margin-top: 20px;">
        <Page :total="total" :current="filter.pageNumber" :page-size="filter.pageSize" :page-size-opts="pageSizeOpts"
            show-sizer show-total  @on-change="changePageNumber" @on-page-size-change="changePageSize"/>
    </div>
  </div>
</template>

/* eslint-disable */
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'compatibilityTaskTable',
  props: [],
  components: {
  },
  data() {
    return {
      filter: {
        pageSize: 10,
        pageNumber: 1,
        sn: this.sn,
        business: null,
        taskStatus: null
      },
      user: Bus.userInfo.userLogin,
      pageSizeOpts: [10, 15, 30],
      total: 0,
      taskList: [],
      focusTaskId: null,
      loading: false,
      isAdmin: false, // 当前用户是否是管理员
      myBusinessList: [], // 当前用户的业务方向列表
      tableColumns: [
        {
          title: 'TaskID',
          slot: 'id',
          align: 'center',
          fixed: 'left',
          maxWidth: 90
        },
        {
          title: 'APP',
          slot: 'appInfo',
          align: 'center',
          fixed: 'left'
        },
        {
          title: 'JobName',
          key: 'sourceUrl',
          align: 'center',
          fixed: 'left',
          width: 150,
          ellipsis: false,
          render: (h, { row }) => {
            return h('a', {
              attrs: {
                href: row.sourceUrl,
                target: '_blank',
                title: row.name
              }
            }, row.name)
          }
        },
        {
          title: '任务类型',
          key: 'jobType',
          align: 'center'
        },
        {
          title: '任务来源',
          slot: 'source',
          align: 'center'
        },
        {
          title: '业务方向',
          key: 'business',
          maxWidth: 100
        },
        {
          title: '设备型号',
          key: 'model',
          align: 'center'
        },
        {
          title: 'SN',
          key: 'sn',
          align: 'center'
        },
        {
          title: 'user',
          slot: 'user',
          align: 'center'
        },
        {
          title: '触发节点',
          width: 140,
          slot: 'triggerNode',
          align: 'center'
        },
        {
          title: '提交时间',
          key: 'submitTime',
          align: 'center',
          fixed: 'right'
        },
        {
          title: '开始时间',
          key: 'startTime',
          align: 'center',
          fixed: 'right'
        },
        {
          title: '结束时间',
          key: 'endTime',
          align: 'center',
          fixed: 'right'
        },
        {
          title: '状态',
          slot: 'taskStatus',
          fixed: 'right',
          align: 'center',
          maxWidth: 100
        },
        {
          title: '操作',
          slot: 'actions',
          fixed: 'right',
          align: 'center',
          maxWidth: 200
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.sn) {
      this.filter.sn = this.$route.query.sn
    }
    if (this.$route.query.taskId) {
      this.focusTaskId = parseInt(this.$route.query.taskId)
    }
    // 钩子中初始化用户业务方向
    this.getBusinessList(this.user).then((res) => {
      this.myBusinessList = res.data
      this.refreshTaskList()
    })
    this.getTaskList()
  },
  methods: {
    /// 权限判定，包括登录用户是否为任务创建用户、是否是管理员、是否是同一业务方向
    hasPermission(row) {
      let currentUser = Bus.userInfo
      this.hasAdminPermission(row.business) // 判断当前用户是否是超级管理员/同业务管理员
      let isSuperAdmin = this.isAdmin // 当前用户是否是超级管理员
      let isOwner = row.user.includes(currentUser.userLogin) // 当前用户是否是任务创建人
      let isSameBusiness = false // 当前用户是否与任务属于同一业务方向

      this.myBusinessList.forEach(businessInfo => {
        if (businessInfo.business === row.business) {
          isSameBusiness = true
        }
      })

      return isOwner || isSuperAdmin || isSameBusiness
    },
    // 获取当前用户的业务归属
    getBusinessList(misId) {
      return this.$axios({
        method: 'get',
        params: {
          misId: misId
        },
        url: this.env.url + 'autoTestConfig/getAllBusinessName'
      }).catch(function (error) {
        console.log(error)
      })
    },
  // 判断当前用户是否为超级管理员或者该业务方向管理员
    hasAdminPermission(business) {
      let businessId = 490
      if (business) {
        let businessInfo = this.myBusinessList.find(businessInfo => businessInfo.business === business)
        if (businessInfo) {
          businessId = businessInfo.id
        }
      }
      this.isAdmin = false
      let _this = this
      return this.$axios({
        method: 'get',
        params: {
          misId: this.user,
          businessId: businessId
        },
        url: this.env.url + 'autoTestConfig/getUserPermission'
      }).then((res) => {
        // 超级管理员或者业务管理员
        _this.isAdmin = res.data.isAdmin || res.data.permissionLevel === 10
      }).catch(function (error) {
        console.log(error)
      })
    },
    getTagColor(row) {
      if (row.status === 'Running') {
        let progress = (row.completedSceneCount / row.sceneCount) * 100
        if (progress > 100) {
          progress = 100 - 10
        }
        if (isNaN(progress)) {
          // 防止progress出现计算出现NAN的情况导致运行中不显示
          return `linear-gradient(to right, #007bff ${0}%, transparent ${0}%), linear-gradient(to right, lightblue, lightblue)`
        }
        return `linear-gradient(to right, #007bff ${progress}%, transparent ${progress}%), linear-gradient(to right, lightblue, lightblue)`
      }
    },
    changeTaskStatusHandler(row, status) {
      // 限制无权限用户改变状态
      if (!this.hasPermission(row)) {
        this.$Notice.error({
          title: '无权限操作',
          desc: '您只能操作自己创建的任务或同一业务方向的任务。'
        })
        return
      }
      this.updateTaskStatus([row.id], status)
      this.getTaskList()
    },
    refreshTaskList: function() {
      this.filter.pageNumber = 1
      this.getTaskList()
    },
    changePageNumber: function(number) {
      this.filter.pageNumber = number
      this.getTaskList()
    },
    changePageSize: function(pageSize) {
      this.filter.pageSize = pageSize
      this.getTaskList()
    },
    showJobReport: function(task) {
      let href = '/microscope/jobInfo?jobId=' + task.jobId
      window.open(href, '_blank')
    },
    getTaskList: function() {
      this.loading = true
      this.$axios({
        method: 'get',
        params: {
          'sn': this.filter.sn,
          'business': this.filter.business,
          'ps': this.filter.pageSize,
          'pn': this.filter.pageNumber
        },
        url: this.env.url + 'client/task/list'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.taskList = message.data.list
          this.total = message.data.total
        } else {
          this.$Notice.error({
            title: '获取Task列表失败！'
          })
        }
        this.loading = false
      }).catch(function (error) {
        console.log(error)
        this.loading = false
      })
    },
    updateTaskStatus(taskIds, status) {
      const currentUser = Bus.userInfo
      const isAdmin = this.isAdmin
      this.$axios({
        method: 'post',
        data: {
          'taskIds': taskIds,
          'status': status,
          'user': currentUser.userLogin,
          'reason': '',
          'isAdmin': isAdmin
        },
        url: this.env.url + 'client/compatibility/task/status'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({title: '更新Task状态成功！'})
        } else {
          this.$Notice.error({title: '获取Task状态失败！'})
        }
      }).catch(function (error) {
        console.log(error)
      })
    }
  }
}
</script>

<style scoped>
  .item {
    margin: 10px 0px;
    display: flex;
  }
  .item_title {
    font-weight: 600;
    width: 90px;
  }
  .button-container {
    display: flex;
    justify-content: center;
  }
  .button {
    margin: 0 2px;  /* 添加左右间隔 */
  }
</style>
