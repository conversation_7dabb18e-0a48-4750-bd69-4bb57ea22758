<template> 
  <div class="task-view-container" v-if="this.imageShowInfo.resultType === 'diff'">
    <template v-if="imageShowInfo.picLocation.indexOf('mp4') > -1">
      <div class="image-view">
        <div style="text-align: center" >原图</div>
        <ImageView :imagePath="imageShowInfo.picLocation.replace('.mp4', '_poster.png')" :randNum="randNum" :style="{maxWidth:imageShowSize}"></ImageView>
      </div>
      <div class="image-view">
        <div style="text-align: center">解析</div>
        <ImageView :imagePath="imageShowInfo.picLocation.replace('.mp4', '_inspect.png')" :randNum="randNum" :style="{maxWidth:imageShowSize}"></ImageView>
      </div>
    </template>
    <template v-else>
      <div class="image-view" v-if="imageShowInfo.hasOwnProperty('basePicLocation') || imageShowInfo.diffBaseLocation !== ''">
        <template v-if="imageShowInfo.diffBaseLocation !== ''">
          <!-- 新数据 -->
          <template v-if="imageShowInfo.basePicLocation && imageShowInfo.basePicLocation !== imageShowInfo.diffBaseLocation">
            <div style="text-align: center" >
              <span>基准图-已更新</span>
              <RadioGroup v-model="showBaseType" type="button" button-style="solid" size='small' class="base-pic-radio">
                <Radio label="diffBase">比对</Radio>
                <Radio label="newBase">最新</Radio>
              </RadioGroup>
              <a v-if="showBaseType==='newBase'" :href="imageUrl + bigBasePic" target="_blank"><Icon type="ios-expand" /></a>
              <a v-if="showBaseType==='diffBase'" :href="imageUrl + imageShowInfo.diffBaseLocation" target="_blank"><Icon type="ios-expand" /></a>
            </div>
          </template>
          <template v-else>
            <div style="text-align: center" >基准图
              <a v-if="showBaseType==='newBase'" :href="imageUrl + bigBasePic" target="_blank"><Icon type="ios-expand" /></a>
              <a v-if="showBaseType==='diffBase'" :href="imageUrl + imageShowInfo.diffBaseLocation" target="_blank"><Icon type="ios-expand" /></a>
            </div>
          </template>
          
          <template v-if="showBaseType==='newBase'">
            <ImageView v-if="disabledZoom" :imagePath="smallBasePic" :randNum="randNum" :style="{maxWidth:imageShowSize}"></ImageView>
            <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
              <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed" :zoomRelativePos="zoomRelativePos" :img-normal="imageUrl + bigBasePic" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
            </div>
          </template>
          <template v-else>
            <ImageView v-if="disabledZoom" :imagePath="smallDiffBase" :randNum="randNum" :style="{maxWidth:imageShowSize}"></ImageView>
            <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
              <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed" :zoomRelativePos="zoomRelativePos" :img-normal="bigDiffBase" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
            </div>
          </template>
        </template>
        <template v-else>
          <!-- 旧数据或无基准图时：保持原有样式 -->
          <template v-if="imageShowInfo.basePicLocation && (imageShowInfo.basePicLocation === imageShowInfo.picLocation || imageShowInfo.diffStatus === 'NOT_HAS_BASE')">
            <div style="text-align: center" >基准图-已更新到运行图</div>
          </template>
          <template v-else>
            <div style="text-align: center" >基准图<a :href="imageUrl + bigBasePic" target="_blank"><Icon type="ios-expand" /></a></div>
          </template>
          <ImageView v-if="disabledZoom" :imagePath="smallBasePic" :randNum="randNum" :style="{maxWidth:imageShowSize}"></ImageView>
          <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
            <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed" :zoomRelativePos="zoomRelativePos" :img-normal="imageUrl + bigBasePic" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
          </div>
        </template>
      </div>
      <div class="image-view">
        <div style="text-align: center">   
          <Icon :class="{ 'disabled': show_actions.length == 0 }" type="md-arrow-round-back" class="icon-left" @click="showPrePicture()"/>
            <span v-if="this.pictureIndex === 0">运行图</span>
            <span v-else>第 {{ this.pictureIndex}}步操作:{{this.show_actions[this.pictureIndex-1].type}}</span>
          <a :href="imageUrl + bigRunPic" target="_blank"><Icon type="ios-expand" /></a>
          <Icon :class="{ 'disabled': show_actions.length === 0 }" type="md-arrow-round-forward" class="icon-right" @click="showNextPicture()"></Icon>
        </div>
        <ImageView v-if="disabledZoom" :imagePath="smallRunPic" :randNum="randNum" :style="{maxWidth:imageShowSize}" @handleImageError="handleImageError" >
        </ImageView>
        <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
          <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed"  :zoomRelativePos="zoomRelativePos" :img-normal="imageUrl + bigRunPic" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
        </div>
      </div>
    </template>
    <template v-if="imageShowInfo.picLocation.indexOf('mp4') > -1">
      <div class="image-view">
        <div style="text-align: center">视频</div>
        <ClientVideoPlayer :videoLocation="imageShowInfo.picLocation"></ClientVideoPlayer>
      </div>
    </template>
    <template v-else>
      <div class="image-view" v-if="imageShowInfo.diffStatus === 'NOT_SIMILAR' || imageShowInfo.diffStatus === 'SAME_WITH_CROP'">
        <div style="text-align: center">对比图<a :href="imageUrl + imageShowInfo.result" target="_blank"><Icon type="ios-expand" /></a></div>
        <ImageView v-if="disabledZoom" :imagePath="imageShowInfo.result" :randNum="randNum" :style="{maxWidth:imageShowSize}" ></ImageView>
        <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
          <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed" :zoomRelativePos="zoomRelativePos" :img-normal="imageUrl + imageShowInfo.result" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
    /* eslint-disable */
    import ImageView from "./ImageView";
    import ZoomOnHover from './ZoomOnHover'
    import ClientVideoPlayer from "./ClientVideoPlayer"
    export default {
      components: {ClientVideoPlayer, ImageView, ZoomOnHover},
      name: "SchemeResultModal",
      props:["imageShowInfo","imageShowSize","imageUrlPrefix", "randNum", "openZoom", "debugMode"],
      data(){
        return{
          imageUrl: this.imageUrlPrefix,
          show_actions: this.imageShowInfo.showActions,
          zoomedScale: 1,
          allZoomed: false,
          pictureIndex: 0,
          disabledZoom: !this.openZoom,
          zoomRelativePos: null,
          showBaseType: 'diffBase'
        }
      },
      watch: {
      imageShowInfo(newVal, oldVal) {
        // 当 imageShowInfo 发生变化时，执行以下代码
        this.pictureIndex = 0; // 将 index 设置为 0
        this.show_actions = newVal.showActions;
        },
      },
      mounted() {
        console.log(this.imageShowInfo)
      },
      computed: {
        basePicLocation: function() {
          if (this.debugMode) {
            let pic_location = this.imageShowInfo.picLocation
            let idx = pic_location.lastIndexOf('.')
            return pic_location.substring(0, idx) + "_base_debug" + pic_location.substring(idx) + this.randomImageHeight()
          } else {
            return this.imageShowInfo.basePicLocation
          }
        },
        runPicLocation: function() {
          if (this.debugMode) {
            let pic_location = this.imageShowInfo.picLocation
            let idx = pic_location.lastIndexOf('.')
            return pic_location.substring(0, idx) + "_debug" + pic_location.substring(idx) + this.randomImageHeight()
          } else {
            if (this.pictureIndex == 0) {
              return this.imageShowInfo.picLocation
            } else {
              let action = this.show_actions[this.pictureIndex-1]
              return action.path
            }
            
          }
        },
        smallBasePic: function() {
          return this.basePicLocation + this.randomImageWidth()
        },
        bigBasePic: function() {
          return this.basePicLocation + this.randomImageHeight(this.basePicLocation)
        },
        smallRunPic: function() {
          return this.runPicLocation + this.randomImageWidth()
        },
        bigRunPic: function() {
          return this.runPicLocation + this.randomImageHeight(this.runPicLocation)
        },
        smallDiffBase: function() {
          if (this.imageShowInfo.diffBaseLocation) {
            return this.imageShowInfo.diffBaseLocation + this.randomImageWidth()
          } else {
            return ''
          }
        },
        bigDiffBase: function() {
          if (this.imageShowInfo.diffBaseLocation) {
            return this.imageShowInfo.diffBaseLocation + this.randomImageHeight(this.imageShowInfo.diffBaseLocation)
          } else {
            return ''
          }
        }
      },
      methods:{
        randomImageHeight: function(picUrl) {
          if (this.openZoom) {
            // Web 原图查看，避免图片缓存，导致多次执行图片覆盖后，缓存不更新的问题 通过s3接口查询图片信息,保证web端长图可以放大并且展示清晰
            var height = 1080
            if (picUrl) {
              height = parseInt(this.ImageInfoSync(picUrl))
            }
            height = height + this.randNum
            return '@' + height.toString() + 'h' 
          } else {
            return ''
          }
        },
        showPrePicture : function() {
          let index = this.pictureIndex - 1
          if (index < 0) {
            this.pictureIndex = this.show_actions.length
          } else {
            this.pictureIndex = index
          }
          this.runPicLocation + this.randomImageWidth()
        },
        showNextPicture : function() {
          let index = this.pictureIndex + 1
          if (index >this.show_actions.length) {
            this.pictureIndex = 0
          } else {
            this.pictureIndex = index
          }
          this.runPicLocation + this.randomImageWidth()
        },
        randomImageWidth: function() {
          if (this.openZoom) {
            // 缩略图
            let width = 600 + this.randNum
            return '@' + width.toString() + 'w'
          } else {
            return ''
          }
        },
        onZoomOnMove: function(newPos) {
          this.zoomRelativePos = newPos
        },
        onZoomedChange: function(allZoomed) {
          this.allZoomed = allZoomed
        },
        handleImageError: function() {
          this.pictureIndex = 0
        },
        ImageInfoSync: function(picUrl) {
          const xhr = new XMLHttpRequest();
          xhr.open('GET', this.imageUrlPrefix+picUrl+'@infoexif', false); // 第三个参数设置为 false 以发起同步请求
          xhr.send(null);

          if (xhr.status === 200) {
            var responseData = JSON.parse(xhr.responseText);
            return responseData.ImageHeight
          } else {
            // 失败后默认长度为1080
            console.error('请求失败:', xhr.statusText)
            return 1080
          }
        }
      }
    }
</script>
<style scoped>
  .task-view-container{
    white-space: nowrap;
    overflow-x: auto;
    /*新增*/
    overflow-y: auto;
  }
  .image-view{
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
  }
  .icon-left, .icon-right {
  margin: 0 15px; /* 给图标一些边距 */
  cursor: pointer; /* 鼠标悬停时显示为指针 */
  font-size: 14px; /* 设置图标大小 */
  }
  .disabled {
  pointer-events: none; /* 禁用鼠标事件 */
  opacity: 0.5; /* 可以添加一些样式来表示图标被禁用 */
  }
  .ivu-radio-group-button.ivu-radio-group-small .ivu-radio-wrapper {
    height: 16px;
    line-height: 14px;
    padding: 0 12px;
    font-size: 8px;
  }
</style>
