<template>
  <Modal
    v-model="visible"
    title="URL DIFF 对比结果"
    width="1200"
    :footer-hide="!showContent"
    :mask-closable="false"
    class="diff-modal"
  >
    <!-- 自定义标题栏 -->
  <template #header>
    <div class="custom-header">
      <span class="modal-title">URL DIFF 对比结果</span>
      <Button
        type="primary"
        size="small"
        :loading="loading"
        @click.stop="handleQuery"
        class="title-button"
        style="margin-right: 62px"
      >
        <template v-if="!loading"><Icon type="md-search" />重新查询</template>
        <template v-else>正在为您查询...</template>
      </Button>
    </div>
  </template>

    <!-- 1.基础的面板信息汇总 -->
    <div class="meta-info" v-if="showContent">
       <!-- 1.1 第一行内容 -->
      <div class="info-header" style="display: flex; align-items: center; min-width: 0;">
        <span class="scene-tag">场景名称</span>
        <span class="scene-title">{{ name || '未知名称' }}</span>
        <span class="scene-tag">运行环境</span>
        <span class="scene-subtitle">{{ label || '环境未知' }}｜{{ appVersion || '版本未获取' }}</span>
      </div>
      <!-- 1.2 第二行内容 -->
      <div class="info-second-header" style="display: flex; align-items: center; min-width: 0;">
        <span class="scene-tag">匹配结果</span>
        <span class="scene-subtitle">发现 <span class="diff-count">{{ tableData.length || '未知数量' }}</span> 条 跳链匹配失败，可自行勾选一组 base / diff 跳转链接进行参数对比👇  </span>
        <!-- 增加2个按钮样式 -->
        <Button
          type="primary"
          size="small"
          @click="jumpToEsAdress"
          style="margin-left:15px; margin-bottom:1px; background-color: #ff69b4; border-color: #ff69b4;"
        >前往ES地址</Button>
        <Button
            type="primary"
            size="small"
            @click="jumpToSceneAdress"
            style="margin-left: 30px; margin-bottom:1px; background-color: #9370db; border-color: #9370db;"
          >前往页面配置地址</Button>
        <!-- 新增开关 -->
        <div class="switch-wrapper">
          <i-switch
            @on-change="handleSwitchChange"
            size="small"
            style="margin-left: 50px; margin-right: 5px;"
          ></i-switch>
          <span class="switch-label">展示全部组合</span>
        </div>
      </div>
    </div>

    <!-- 3.表格结构 -->
    <div class="result-table" v-if="showContent">
      <table>
        <thead>
          <tr><th style="width: 60px;">序号</th><th>Base跳链</th><th>Proxy跳链</th></tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in combinedTableData"
            :key="index"
            class="selectable-row"
            :class="{ 'hidden-row': index >= tableData.length }"
          >
            <!-- 序号列 -->
            <td>{{ index + 1 }}</td>

            <!-- Base列 -->
            <td
              class="url-cell"
              :class="{ selected: item.diffSelected }"
              @click="handleSelect(index, 'base')"
            >
              <div class="link-wrapper">
                <span v-if="item.isSuccess" class="success-badge">✅</span>
                <span class="radio-indicator"></span>
                {{ item.diffUrl || '无' }}
              </div>
            </td>

            <!-- Proxy列 -->
            <td
              class="url-cell"
              :class="{ selected: item.proxySelected }"
              @click="handleSelect(index, 'proxy')"
            >
              <div class="link-wrapper">
                <span v-if="item.isSuccess" class="success-badge">✅</span>
                <span class="radio-indicator"></span>
                {{ item.proxyUrl || '无' }}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 4.差异摘要 -->
    <div class="diff-summary" v-if="showContent">
      <Alert type="info">
        <template v-if="1">
          当前选中 base<span class="diff-count">{{selectedBaseIndex + 1}}</span>VS proxy<span class="diff-count">{{selectedProxyIndex + 1}}</span>｜ 发现存在<span class="diff-count">{{ diffCategoriesCount }}</span>类对比差异，
          共<span class="diff-count">{{ totalDiffCount }}</span>处参数DIFF，包括 key缺失、数值变更、类型不同。对比详情如下所示：
        </template>
        <template v-else>
          <span class="diff-count">未查询到差异，请选择其他「DIFF参数类型」继续查询</span>
        </template>
      </Alert>
    </div>

    <!-- 5.详细差异文字版 -->
    <div class="diff-details" v-if="showContent">
      <div class="toggle-header" @click="isDetailExpanded = !isDetailExpanded">
        <Icon :type="isDetailExpanded ? 'ios-arrow-down' : 'ios-arrow-forward'" />
        <span class="toggle-text">差异详情（点击展开/收起）👇</span>
      </div>

      <div v-show="isDetailExpanded" class="detail-content">
        <div class="diff-item red" v-if="diffFailInfo.lack_keys.length">
          <div class="diff-title">🔴 缺失的key</div>
          <ul>
            <li v-for="(item, index) in diffFailInfo.lack_keys" :key="'lack_'+index">
            {{ item }}
            </li>
          </ul>
        </div>

        <div class="diff-item yellow" v-if="diffFailInfo.value_unequal.length">
          <div class="diff-title">🟡 数值变更</div>
          <ul>
            <li v-for="(item, index) in diffFailInfo.value_unequal" :key="'value_'+index">
              {{ item }}
            </li>
          </ul>
        </div>

        <div class="diff-item purple" v-if="diffFailInfo.type_unequal.length">
          <div class="diff-title">🟣 类型异常</div>
          <ul>
            <li v-for="(item, index) in diffFailInfo.type_unequal" :key="'type_'+index">
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 6.新增DIFF区域 -->
    <div class="diff-container" v-if="showContent">
      <code-diff
        :old-string="formattedOldData"
        :new-string="formattedNewData"
        output-format="side-by-side"
        language="json"
        :context="10"
      />
    </div>
  </Modal>
</template>

<script>
import CodeDiff from 'vue-code-diff'
export default {
  name: 'UrlDiffResultModal',
  components: {
    CodeDiff
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    imageShowInfo: Object
  },
  computed: {
    visible: { // 初始化获取value给visible展示组件，以及关闭时发送input更新状态。双向绑定写法
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    formattedOldData() {
      return JSON.stringify(this.oldData, null, 2)
    },
    formattedNewData() {
      return JSON.stringify(this.newData, null, 2)
    },
    combinedTableData() {
      return this.showAllGroups
        ? [...this.tableData, ...this.tableDataV2]
        : this.tableData
    },
    selectedBaseIndex() {
      return this.combinedTableData.findIndex(item => Boolean(item.diffSelected))
    },
    selectedProxyIndex() {
      return this.combinedTableData.findIndex(item => Boolean(item.proxySelected))
    },
    diffCategoriesCount() {
      let count = 0
      if (this.diffFailInfo.lack_keys.length) count++
      if (this.diffFailInfo.value_unequal.length) count++
      if (this.diffFailInfo.type_unequal.length) count++
      return count
    },
    totalDiffCount() {
      return (
        (this.getLackKeysCount() || 0) +
        (this.diffFailInfo.value_unequal.length || 0) +
        (this.diffFailInfo.type_unequal.length || 0)
      )
    }
  },
  data() {
    return {
      taskId: 0,
      jobId: 0,
      name: '',
      label: '',
      appVersion: '',
      caseId: 0,
      sceneId: 0,
      isDetailExpanded: false, // 新增展开状态
      selectedGroup: '0', // 确保已定义选择状态
      showContent: false,
      loading: false,
      showAllGroups: false, // 新增开关状态
      // 新增表格数据
      tableData: [
        {
          diffUrl: '',
          proxyUrl: '',
          diffSelected: true,  // 默认选中第一个base
          proxySelected: true  // 默认选中第一个proxy
        }
      ],
      // 匹配成功的数据放在这里
      tableDataV2: [
        {
          diffUrl: '',
          proxyUrl: '',
          diffSelected: false,
          proxySelected: false
        }
      ],
      oldData: {
        name: 'old',
        poiid: '12121'
      },
      newData: {
        name: 'old1',
        poiid: '1212121'
      },
      diffFailInfo: {
        'lack_keys': [],
        'type_unequal': [],
        'value_unequal': []
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetState()
        this.extractParams()
        this.handleQuery() // 自动执行查询
      }
    }
  },
  methods: {
    resetState() {
      this.diffCount = 0        // 重置差异计数
      this.diffPaths = []       // 清空差异路径
      this.diffKey = 0          // 重置diff组件key
      this.tableData = []
      this.tableDataV2 = []
      this.showContent = false  // 隐藏内容区域
    },
    handleQuery() {
      console.log('imageShowInfo:', this.imageShowInfo)
      this.resetState() // 查询之前先进行清空内容
      this.loading = true
      this.fetchDiffData()
    },
    // 提取参数的独立方法
    extractParams() {
      if (!this.imageShowInfo) return
      // 解构赋值
      const { taskId, jobId, name, sceneId } = this.imageShowInfo
      // 赋值给组件状态
      this.taskId = taskId
      this.jobId = jobId
      this.name = name
      this.sceneId = sceneId
      // 调试日志
      console.log('参数已更新:', {
        taskId: this.taskId,
        jobId: this.jobId,
        name: this.name
      })
    },
    handleSwitchChange(val) {
      this.showAllGroups = val

      // 当关闭全部组合显示时自动选中第一对
      if (!val) {
        // eslint-disable-next-line one-var
        let hasBase = false, hasProxy = false
        this.tableData.some(item => {
          hasBase = hasBase || item.diffSelected
          hasProxy = hasProxy || item.proxySelected
          // 提前退出：当两个标记都为true时停止遍历
          return hasBase && hasProxy
        })
      // 如果没有有效选中项才重置
        if (!(hasBase && hasProxy)) {
          this.handleQuery()
        }
      }
    },
    handleSelect(index, type) {
      // 获取当前展示的全量数据
      // 立即获取当前点击项的链接值
      const currentData = this.combinedTableData
      const targetLink = type === 'base'
        ? currentData[index].diffUrl
        : currentData[index].proxyUrl
      // 前置校验（在状态变更前）
      const isEmptyLink = (link) =>
        !link || link.trim() === '' || link === 'null' || link === 'undefined'
      if (isEmptyLink(targetLink)) {
        this.$Message.warning('不能选择内容为空的跳链对比哦')
        return // 直接返回，不执行后续状态变更
      }
      // 生成新数据（深拷贝）
      const newTableData = JSON.parse(JSON.stringify(this.tableData))
      const newTableDataV2 = JSON.parse(JSON.stringify(this.tableDataV2))

      // 遍历所有数据重置选中状态
      currentData.forEach((item, i) => {
        const isV2 = i >= newTableData.length
        const targetArray = isV2 ? newTableDataV2 : newTableData
        const targetIndex = isV2 ? i - newTableData.length : i

        if (type === 'base') {
          targetArray[targetIndex].diffSelected = (i === index)
        } else if (type === 'proxy') {
          targetArray[targetIndex].proxySelected = (i === index)
        }
      })

      // 更新源数据
      this.tableData = newTableData
      this.tableDataV2 = newTableDataV2

      // 使用 $nextTick 确保数据更新完成
      this.$nextTick(() => {
        const updatedData = this.combinedTableData
        const selectedBase = updatedData.find(item => Boolean(item.diffSelected)).diffUrl // 应该是查找第一个具有 truthy diffSelected 属性的对象
        const selectedProxy = updatedData.find(item => Boolean(item.proxySelected)).proxyUrl
        if (selectedBase && selectedProxy) {
          const requestPayload = {
            caseId: this.caseId,      // 从组件状态获取
            diffUrl: selectedBase,    // 当前选中的base
            proxyUrl: selectedProxy   // 当前选中的proxy
          }
          this.fetchDiffDetail(requestPayload)
        }
      })
    },
    fetchDiffData() {
      this.$axios({
        method: 'post',
        url: 'http://client.hotel.test.sankuai.com/autoTestAnalytic/urlDiff/initialMeasureInfo',
        data: {  // POST请求体用data
          'taskId': this.taskId,
          'name': this.name,
          'jobId': this.jobId
        }
      }).then((res) => {
        const hitsInfo = res.data || {}  // 正确获取数据
        console.log('接口响应数据:', hitsInfo)  // 添加日志
        if (Object.keys(hitsInfo).length > 0) {  // 正确判断对象是否为空
          // 确保数据完全更新后再显示内容
          this.transformApiData(hitsInfo)
          this.$nextTick(() => {
            this.showContent = true
            this.loading = false
          })
        } else {
          console.log('接口返回空数据')
          this.showContent = false
          this.loading = false
        }
      }).catch((error) => {
        console.error('接口请求失败:', error)
        this.showContent = false
        this.loading = false
      })
    },
    fetchDiffDetail(payload) {
      this.$axios({
        method: 'post',
        url: 'http://client.hotel.test.sankuai.com/autoTestAnalytic/urlDiff/schemesDiffInfo',
        data: payload
      }).then(res => {
        const hitsInfo = res.data || {}  // 正确获取数据
        this.diffFailInfo = hitsInfo.diffFailInfo
        this.oldData = hitsInfo.diffUrlJson
        this.newData = hitsInfo.proxyUrlJson
        console.log('第二次接口响应数据:', hitsInfo)  // 添加日志
      }).catch((error) => {
        console.error('接口请求失败:', error)
      })
    },
    transformApiData(hitsInfo) {
      console.log('转换接口数据:', hitsInfo)
      // 清空旧数据
      this.tableData = []
      this.tableDataV2 = []
      // 1. 转换失败配对数据 => tableData
      if (hitsInfo.schemePairsInfo.tableDataForFailPairs) {
        this.tableData = hitsInfo.schemePairsInfo.tableDataForFailPairs.map(item => ({
          diffUrl: item.diffUrl, // 注意字段名转换
          proxyUrl: item.proxyUrl,
          diffSelected: item.diffSelected, // 接口返回的选中状态
          proxySelected: item.proxySelected
        }))
      }
      // 2. 转换成功配对数据 => tableDataV2
      if (hitsInfo.schemePairsInfo.tableDataForSuccessPairs) {
        this.tableDataV2 = hitsInfo.schemePairsInfo.tableDataForSuccessPairs.map(item => ({
          diffUrl: item.diffUrl,
          proxyUrl: item.proxyUrl,
          diffSelected: item.diffSelected,
          proxySelected: item.proxySelected,
          isSuccess: true // 新增成功标识字段
        }))
      }
      // 3.更新差异结果数据
      this.diffFailInfo = hitsInfo.diffResult.diffFailInfo
      // 4. 更新环境信息
      this.label = hitsInfo.label
      this.appVersion = hitsInfo.appVersion
      this.caseId = hitsInfo.caseId
      // 5. 更新json diff 数据
      this.oldData = hitsInfo.diffResult.diffUrlJson
      this.newData = hitsInfo.diffResult.proxyUrlJson
    },
    jumpToEsAdress() {
      let encodedName = encodeURIComponent(this.name)
      let url = 'https://lyrebird.sankuai.com/app/kibana/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-12M,to:now))&_a=(columns:!(_source),filters:!' +
        '((\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:\'4fcffc00-fe3f-11ef-a03b-1f2602774bca\',key:diffType,negate:!f,params:(query:diff_execute),type:phrase),query:(match_phrase:(diffType:diff_execute))),' +
        '(\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:\'4fcffc00-fe3f-11ef-a03b-1f2602774bca\',key:taskId,negate:!f,params:(query:\'' + this.taskId + '\'),type:phrase),query:(match_phrase:(taskId:\'' + this.taskId + '\'))),' +
        '(\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:\'4fcffc00-fe3f-11ef-a03b-1f2602774bca\',key:caseName,negate:!f,params:(query:\'' + encodedName + '\'),type:phrase),query:(match_phrase:(caseName:\'' + encodedName + '\')))),index:\'4fcffc00-fe3f-11ef-a03b-1f2602774bca\',interval:auto,query:(language:kuery,query:\'\'),sort:!())'
      window.open(url, '_blank')
    },
    jumpToSceneAdress() {
      let url = `http://qa.sankuai.com/client/scene/${this.sceneId}/case`
      window.open(url, '_blank')
    },
    getLackKeysCount() {
      const lackKeys = this.diffFailInfo.lack_keys || []
      // 统一处理所有数据类型
      const processedString = Array.isArray(lackKeys)
      ? lackKeys.join(',')   // 数组转字符串
      : String(lackKeys)     // 其他类型转字符串

      return processedString
        // eslint-disable-next-line no-useless-escape
      .replace(/[\[\]'"\s]/g, '') // 清理特殊字符和空格
      .split(',')                 // 分割
      .filter(k => k)            // 过滤空值
      .length
    }
  }
}
</script>

<style scoped>
label {
  color: #606266;
  font-size: 14px;
  min-width: 68px;
}

.match-alert >>> .ivu-alert-message {
  font-size: 14px;
}

.diff-modal >>> .ivu-modal-header {
  padding: 8px 16px;
}

.diff-modal >>> .ivu-modal-body {
  max-height: 75vh;
  overflow-y: auto;
  padding: 8px 16px;
}
.diff-modal >>> .ivu-modal-header-inner {
  font-size: 14px;
  line-height: 1.5;
}

/* 新增表格样式 */
.result-table {
  margin: 16px 0;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  table-layout: fixed; /* 需要添加此属性 */
}

th, td {
  padding: 12px 16px;
  border-bottom: 1px solid #e8eaec;
  text-align: left;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.url-cell {
  max-width: 400px;
  word-break: break-all;
  line-height: 1.5;
}

.meta-info {
  background: linear-gradient(145deg, #f8f9fe 0%, #ffffff 100%);
  border: 1px solid #e1e4f0;
  border-radius: 8px;
  padding: 4px 16px;  /* 作用于容器内部 */
  margin: 8px 0px; /* 作用于容器外部 */
  box-shadow: 0 4px 12px rgba(28, 36, 56, 0.08);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性渐变条 */
.meta-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #2d8cf0 0%, #19be6b 100%);
}

.meta-item label {
  color: #515a6e;        /* 调整为antd的深灰色 */
  font-size: 14px;
  overflow: hidden;
}

.meta-item span {
  color: #333;
  font-weight: 400;
  font-size: 14px;
  word-break: break-all; /* 允许值换行 */
}

@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 修正后的样式 */
/* 样式部分 */
.meta-info {
  padding: 16px 16px 8px; /* 上16px 左右16px 下0 */
  background: #f8fafc;
  border-left: 4px solid #1890ff;
  margin: 8px 0;
  border-radius: 4px;
}

.info-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8eaec;
}

.info-second-header {
  padding-bottom: 8px;
}

.scene-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background: #e3f2fd; /* 更亮的背景 */
  color: #1565c0; /* 深蓝色 */
  border-radius: 4px;
  font-size: 12px;
  margin-right: 12px;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.scene-title {
  display: inline;
  font-size: 14px;
  color: #333;
  word-break: break-word;
  margin-right: 50px;
}

.scene-subtitle {
  display: inline;
  font-size: 14px;
  color: #333;
  word-break: break-word;
  margin-right: 10px;
}


.detail-text em {
  color: #1890ff;
  font-style: normal;
  font-weight: 500;
}

.detail-text em::before {
  content: "「";
  color: #ff4d4f;
}

.detail-text em::after {
  content: "」";
  color: #ff4d4f;
}

.diff-summary {
  margin: 4px 0;
}
.diff-count {
  color: #ff4d4f;
  font-weight: 600;
  margin: 0 4px;
}

.diff-container {
  height: 23vh;       /* 代码块整体的高度*/
  border-radius: 4px;
  overflow: auto;     /*  内容超出滚动 */
  background: #f8f8f9; /* 浅灰背景 */
  margin: 10px 0 0 0; /* 上16px 右0 下0 左0 */
  border-bottom: 1px solid #e8e8e8;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  transition: background 0.3s;
}

.switch-label {
  display: inline;
  font-size: 14px;
  color: #333;
  word-break: break-word;
}


/* 调整开关颜色 */
.switch-wrapper >>> .ivu-switch-checked {
  background-color: #81c784;
  border-color: #81c784;
}

.selected-row td {
  background: transparent !important;
}

/* 交互样式 */
.url-cell {
  cursor: pointer;
  padding: 12px !important;
  transition: background 0.3s;
  max-width: 600px;
  word-break: break-all;
}

.url-cell:hover {
  background: #f5f5f5 !important;
}

.url-cell.selected {
  background: #f6ffed !important;
  border-left: 2px solid #52c41a;       /* 配套的绿色边框 */
}

.link-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 单选按钮样式 */
.radio-indicator {
  position: relative;
  flex-shrink: 0;
  width: 18px;  /* 容器稍大 */
  height: 18px;
}

/* 基础圆环 */
.radio-indicator::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  border: 1px solid #d9d9d9; /* 未选中时灰色边框 */
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

/* 选中状态 */
.url-cell.selected .radio-indicator::before {
  border-color: #1565c0; /* 紫色边框 */
  background-image: radial-gradient(
    circle at center, /* 从中心开始的圆形渐变 */
    #1565c0 0%,            /* 中心点100%深蓝色（实心区域开始） */
    #1565c0 35%,           /* 35%位置保持深蓝色（实心区域结束） */
    transparent 36%        /* 36%位置开始完全透明（形成空心圆环） */
  );
  animation: radio-check 0.3s ease-out; /* 应用名为radio-check的动画 */
}

/* 微交互动画：透明度0.5到1，缩小0.8到1 */
@keyframes radio-check {
  0% {
    transform: translateY(-50%) scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
}


/* 新增隐藏行样式 */
.hidden-row td {
  background-color: #fafafa !important;
  border-color: #f0f0f0;
}

/* 序号列样式 */
td:first-child {
  color: #666;
  font-weight: 500;
}

/* 差异详情样式 */
.diff-details {
  margin: 12px 0;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.toggle-header {
  padding: 8px 16px;
  background: #fafafa;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-content {
  padding: 12px 16px;
}

.diff-item {
  margin-bottom: 12px;
}

.diff-title {
  font-weight: 500;
  margin-bottom: 6px;
}

.diff-title.red { color: #ff4d4f; }
.diff-title.yellow { color: #faad14; }
.diff-title.purple { color: #9254de; }

/* 添加通用文字颜色 */
.diff-item {
  color: #333; /* 普通文字颜色 */
}

.diff-item ul {
  margin: 0;
  padding-left: 20px;
}

.diff-item li {
  line-height: 1.8;
  font-size: 13px;
}

.toggle-text {
  font-weight: 500; /* 中等加粗 */
  color: #333;      /* 保持深色 */
}

/* 在style部分添加 */
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 40px; /* 与关闭按钮保持安全距离 */
}

.modal-title {
  font-weight: 500;
  font-size: 14px;
}

.title-button {
  margin-right: -8px !important; /* 抵消iview默认header的padding */
}

/* 调整iview默认header样式 */
.diff-modal >>> .ivu-modal-header {
  padding: 8px 16px;
}

.success-badge {
  color: #52c41a;
  font-size: 14px;
  margin-right: 4px;
}
</style>
