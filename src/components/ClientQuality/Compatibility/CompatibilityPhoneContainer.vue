<template>
  <div>
    <Modal :value="showPhone" :title="'选择'+PlatformAndDeviceType.platform+'设备'" :width="680" @on-cancel="closePhoneInfo">
      <CompatibilityPhone style="padding-left: 10px" :platform="PlatformAndDeviceType.platform" :showPhone="showPhone" :jobType="jobType" :app="category" :business="business" v-on:selectPhone="selectPhone" :deviceType="PlatformAndDeviceType.deviceType">
      </CompatibilityPhone>
      <div slot="footer">
        <Button type="primary" :loading="jobCreating" @click="createPhoneJob">提交</Button>
        <Button  @click="closePhoneInfo" >关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  /* eslint-disable */
  import CompatibilityPhone from "./CompatibilityPhone";
  import { Bus } from '@/global/bus';
  export default {
    name: "CompatibilityPhoneContainer",
    components: {CompatibilityPhone},
    props: ["showPhoneContainer", "appPlat", "userJob"],
    data(){
      return{
        showPhone:false,
        phoneList:[],
        phoneHost:"1",
        jobCreating: false
      }
    },
    watch:{
      showPhoneContainer(val){
        this.showPhone=val
        this.jobCreating = false
      }
    },
    computed: {
      PlatformAndDeviceType: function() {
        let deviceType
        let plat = 'Android'
        console.log(this.userJob)
        if (this.userJob) {
          if (this.userJob.platform === 'Web' || JSON.parse(this.userJob.app_params).runEngine === 'Web') {
            plat = 'Web'
          } else {
            let product_info = JSON.parse(JSON.parse(this.userJob.app_params).product_info)
            if (product_info.type.endsWith('mp') && this.userJob.platform !== 'Android,iOS') {
              plat = this.userJob.platform
            } else {
              if (this.userJob.app_url.indexOf('.apk') > 0) {
                plat='Android'
              } else if (this.userJob.app_url.indexOf('.ipa') > 0 || this.userJob.app_url.indexOf('.zip') > 0) {
                plat='iOS'
                if (this.userJob.app_url.indexOf('.zip') > 0) {
                  // deviceType为1时，表示为云测模拟器
                  deviceType = 1
                } else if (this.userJob.app_url.indexOf('.ipa') > 0) {
                  // deviceType为1时，表示为真机
                  deviceType = 0
                }
              } else {
                plat='Harmony'
              }
            }
          }
        }
        return {
          deviceType: deviceType,
          platform: plat
        }
      },
      productId: function() {
        let product_id = -1
        // 临时处理适配，最佳是修改触发接口取值逻辑
        if (this.userJob && this.userJob.pages.length > 0 && this.userJob.pages[0].scene_id) {
          let product_info = JSON.parse(JSON.parse(this.userJob.app_params).product_info)
          if (product_info) {
            product_id = product_info.id
          }
        }
        return product_id
      },
      category: function() {
        let category = '美团'
        if (this.userJob) {
          // 从app_params中找到product_info
          let product_info = JSON.parse(JSON.parse(this.userJob.app_params).product_info)
          if (product_info) {
            if (product_info.type === "wmp") {
              category = "微信"
            } else if (product_info.type === "ksmp") {
              category = "快手"
            } else if (product_info.type === "bdmp") {
              category = "百度地图"
            } else {
              category = this.userJob.category
            }
          }
        }
        return category
      },
      jobType: function() {
        if (this.userJob) {
          return this.userJob.type
        } else {
          return 'compatibility'
        }
      },
      business: function() {
        if (this.userJob) {
          return this.userJob.business
        } else {
          return ''
        }
      },
    },
    methods:{
      closePhoneInfo(){
        this.$emit("closePhoneInfo", 0)
        this.jobCreating = false
      },
      selectPhone(phoneList,phoneHost){
        this.phoneList = phoneList;
        this.phoneHost = phoneHost;
      },
      createPhoneJob(){
        if(this.phoneList.length>0){
          this.createJob(this.phoneList, this.phoneHost)
          this.$emit("createPhoneJob",this.phoneList,this.phoneHost);
          this.jobCreating = true
        } else {
          this.$Notice.info({
            title: "请选择设备"
          });
        }
      },
      createJob(jobPhone, phoneHost){
        let row = this.userJob
        phoneHost === "1"? row.use_test_devices="0":row.use_test_devices="1";
        let source_type = 'Microscope';
        let source_url = "http://qa.sankuai.com/client";
        if(row.task_url && row.task_url.length > 0){
          source_type = "新需求";
          source_url = row.task_url;
        }
        this.$Message.info("正在处理");
        let realPlat = row.platform;
        if (this.appPlat === 'Web' || JSON.parse(this.userJob.app_params).runEngine === 'Web') {
          realPlat = 'Web';
        }
        this.$axios({
          method:'post',
          data:{
            'plat':realPlat,
            'app_plat':this.appPlat,
            'job_type':row.type,
            'job_name': row.job_name,
            'user':Bus.userInfo.userLogin,
            'business':row.business,
            'category':this.category,
            'product_id': this.productId,
            'devices': jobPhone.toString(),
            'app_url':row.app_url,
            'pages': row.pages,
            'mock_data': row.mock_data,
            'mock_flag': row.mock_flag,
            'ab_key': row.ab_key,
            'env_lock_mrn_bundle':row.env_lock_mrn_bundle,
            'source_url':source_url,
            'source_type': source_type,
            'use_test_device':row.use_test_devices,
            'app_params':row.app_params
          },
          url:this.env.compatibilityUrl+"trigJob"
        }).then((res) => {
          let message = res.data;
          if(message.code === 0){
            this.$Message.success("Job创建成功");
            this.$emit("closePhoneInfo", 0)
          }else {
            this.$Message.warning({
              title: 'Job创建问题',
              content: message.err
            });
          }
        }).catch(function (error) {
        })
      },
    }
  }
</script>

<style scoped>

</style>
