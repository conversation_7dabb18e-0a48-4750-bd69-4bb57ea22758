<template>
  <div>
    <vue-load-image>
      <img class="image-show" slot="image" :src="imageSrc" @error="handleImageError"/>
      <img class="image-show" slot="preloader" src="/static/img/image_loading.gif"/>
      <div slot="error">Image not found</div>
    </vue-load-image>
  </div>
</template>

/* eslint-disable */
<script>
    import VueLoadImage from 'vue-load-image'
    export default {
      name: 'ImageView',
      props: ['imagePath', 'randNum'],
      components: {
        'vue-load-image': VueLoadImage
      },
      data() {
        return {
          imageSize: '@500w',
          imageUrl: 'https://msstest-img.sankuai.com/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/',
          width: 0
        }
      },
      computed: {
        imageSrc: function() {
          if (typeof (this.imagePath) !== 'undefined' && !this.imagePath.startsWith('http')) {
            this.width = 400
            let size = this.width + this.randNum
            this.imageSize = '@' + size.toString() + 'w'
            return this.imageUrl + this.imagePath + this.imageSize
          } else {
            this.width = 800
            let size = this.width + this.randNum
            this.imageSize = '@' + size.toString() + 'w'
            return this.imagePath + this.imageSize
          }
        }
      },
      methods: {
        handleImageError() {
          // 触发自定义事件，通知父组件
          this.$emit('handleImageError')
        }
      }
    }
</script>

<style scoped>
  .image-show{
    border-radius: 5px;
    border:1px solid darkgray;
    width: 100%;
  }
</style>