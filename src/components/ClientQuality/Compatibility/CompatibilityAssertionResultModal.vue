<template>
    <div class="task-view-container" v-show="showModal">
        <div class="image-view">
          <div style="text-align: center">   
            <Icon :class="{ 'disabled': show_actions.length == 0 }" type="md-arrow-round-back" class="icon-left" @click="showPrePicture()"/>
              <span v-if="this.pictureIndex === 0">运行图</span>
              <span v-else>第 {{ this.pictureIndex}}步操作:{{this.show_actions[this.pictureIndex-1].type}}</span>
            <a :href="imageUrl + bigRunPic" target="_blank"><Icon type="ios-expand" /></a>
            <Icon :class="{ 'disabled': show_actions.length === 0 }" type="md-arrow-round-forward" class="icon-right" @click="showNextPicture()"></Icon>
          </div>
          <ImageView v-if="disabledZoom" :imagePath="smallRunPic" :randNum="randNum" :style="{maxWidth:imageShowSize}" @handleImageError="handleImageError" >
          </ImageView>
          <div v-if="!disabledZoom" :style="{maxWidth:imageShowSize}">
            <zoom-on-hover v-on:zoomOnMove="onZoomOnMove" v-on:zoomedChange="onZoomedChange" :allZoomed="allZoomed"  :zoomRelativePos="zoomRelativePos" :img-normal="imageUrl + bigRunPic" :disabled="false" :scale="zoomedScale"> </zoom-on-hover>
          </div>
        </div>
        <div class="assertion-view">
          <div style="text-align: center">
              <span>断言列表</span>
          </div>
          <div class="assertion-container">
            <ul class="assertion-list">
              <li v-for="(item, index) in assertionResult.data" :key="0 + index" class="assertion-item">
                <div class="assertion-row">
                  <div class="form-item number"><span>{{index + 1}}</span></div>
                  <div class="form-item content">
                    <img v-if="item.type !== 'ai'" :src="item.assertion" class="assertion-image"/>
                    <div v-else class="ai-assertion">
                      <p class="assertion-text">{{ item.aiAssertion }}</p>
                      <p class="reason-text"><span class="reason-label">原因：</span>{{ item.reason }}</p>
                    </div>
                  </div>
                  <div class="form-item status">
                    <Tag color="success" v-if="item.label == 'pass'">通过</Tag>
                    <Tag color="error" v-if="item.label == 'fail'">失败</Tag>
                    <Tag color="default" v-if="item.label == 'un_exec'">未执行</Tag>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
    </div>
</template>
  <script>
/* eslint-disable */
import ImageView from "./ImageView";
import ZoomOnHover from './ZoomOnHover';
export default {
  components: { ImageView, ZoomOnHover },
  name: "AssertionResultModal",
  props: ["assertionResult", "imageShowSize", "imageUrlPrefix", "randNum", "openZoom", "debugMode", "imageShowInfo"],
  data() {
    return {
      imageUrl: this.imageUrlPrefix,
      show_actions: this.imageShowInfo.showActions,
      pictureIndex: 0,
      zoomedScale: 1,
      allZoomed: false,
      disabledZoom: !this.openZoom,
      zoomRelativePos: null,
    }
  },
  watch: {
    imageShowInfo(newVal, oldVal) {
      // 当 imageShowInfo 发生变化时，执行以下代码
      this.pictureIndex = 0 // 将 index 设置为 0
      this.show_actions = newVal.showActions
      },
  },
  computed: {
    showModal: function () {
      return this.assertionResult.type === 'assertion'
    },
    runPicLocation: function() {
      if (this.debugMode) {
        let pic_location = this.imageShowInfo.picLocation
        let idx = pic_location.lastIndexOf('.')
        return pic_location.substring(0, idx) + "_debug" + pic_location.substring(idx) + this.randomImageHeight()
      } else {
        if (this.pictureIndex == 0) {
          return this.imageShowInfo.picLocation
        } else {
          let action = this.show_actions[this.pictureIndex-1]
          return action.path
        }
        
      }
    },
    bigRunPic: function() {
      return this.runPicLocation + this.randomImageHeight(this.runPicLocation)
    },
    smallRunPic: function() {
      return this.runPicLocation + this.randomImageWidth()
    }
  },
  methods:{
    randomImageHeight: function(picUrl) {
        if (this.openZoom) {
          // Web 原图查看，避免图片缓存，导致多次执行图片覆盖后，缓存不更新的问题 通过s3接口查询图片信息,保证web端长图可以放大并且展示清晰
          var height = 1080
          if (picUrl) {
            height = parseInt(this.ImageInfoSync(picUrl))
          }
          height = height + this.randNum
          return '@' + height.toString() + 'h' 
        } else {
          return ''
        }
      },
    randomImageWidth: function() {
      if (this.openZoom) {
        // 缩略图
        let width = 600 + this.randNum
        return '@' + width.toString() + 'w'
      } else {
        return ''
      }
    },
    showPrePicture : function() {
      let index = this.pictureIndex - 1
      if (index < 0) {
        this.pictureIndex = this.show_actions.length
      } else {
        this.pictureIndex = index
      }
      this.runPicLocation + this.randomImageWidth()
    },
    showNextPicture : function() {
      let index = this.pictureIndex + 1
      if (index >this.show_actions.length) {
        this.pictureIndex = 0
      } else {
        this.pictureIndex = index
      }
      this.runPicLocation + this.randomImageWidth()
    },
    handleImageError: function() {
      this.pictureIndex = 0
    },
    onZoomedChange: function(allZoomed) {
      this.allZoomed = allZoomed
    },
    ImageInfoSync: function(picUrl) {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', this.imageUrlPrefix+picUrl+'@infoexif', false); // 第三个参数设置为 false 以发起同步请求
      xhr.send(null);

      if (xhr.status === 200) {
        var responseData = JSON.parse(xhr.responseText);
        return responseData.ImageHeight
      } else {
        // 失败后默认长度为1080
        console.error('请求失败:', xhr.statusText)
        return 1080
      }
    },
    onZoomOnMove: function(newPos) {
      this.zoomRelativePos = newPos
    },
    onZoomedChange: function(allZoomed) {
      this.allZoomed = allZoomed
    }
  }
}
</script>
  <style scoped>
  .task-view-container {
      white-space: nowrap;
      height: 600px;
  }
  
  .image-view {
      overflow: hidden;
      display: inline-block;
      vertical-align: top;
  }
  .assertion-view {
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
    margin-left: 40px;
    width: calc(50% - 40px); /* 调整宽度，确保不会超出父容器 */
  }
  .assertion-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .assertion-item {
    margin-bottom: 20px;
  }

  .assertion-header {
    text-align: center;
    margin-bottom: 10px;
  }

  .assertion-container {
    border: 1px solid darkgray;
    border-radius: 5px;
    height: 570px;
    overflow-y: auto;
    padding: 10px;
  }

  .assertion-row {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .form-item {
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
  }

  .form-item.number {
    width: 30px;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }

  .form-item.content {
    flex: 1 1 calc(100% - 130px);
    margin-right: 10px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: calc(100% - 130px); /* 确保内容不会超出容器 */
  }

  .form-item.status {
    width: 80px;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }

  .ai-assertion {
    white-space: normal;
  }

  .assertion-text, .reason-text {
    margin: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .assertion-image {
    max-width: 100%;
    height: auto;
  }

  .icon-left, .icon-right {
  margin: 0 15px; /* 给图标一些边距 */
  cursor: pointer; /* 鼠标悬停时显示为指针 */
  font-size: 14px; /* 设置图标大小 */
  }
  .disabled {
  pointer-events: none; /* 禁用鼠标事件 */
  opacity: 0.5; /* 可以添加一些样式来表示图标被禁用 */
  }

  </style>
  