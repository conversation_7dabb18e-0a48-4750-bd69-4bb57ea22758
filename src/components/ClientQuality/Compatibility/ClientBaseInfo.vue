<template>
  <div style="padding-bottom: 28px;padding-top: 10px">
    <Row>
      <Col :md="12">
        <div class="content-title-container">
          <Row>
            <Col :md="6" class="content-title">APP版本:</Col>
            <Col :md="18">
              <div align="left">
                <a :href="baseInfo.jobInfo.packageUrl">
                {{baseInfo.jobInfo.appVersion}}
                </a>
              </div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">平台信息:</Col>
            <Col :md="18">
              <div align="left">
                {{baseInfo.jobInfo.category}} {{baseInfo.jobInfo.plat}}
              </div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col :md="12">
        <div class="content-title-container">
          <Row>
            <!-- TODO:提测触发：MRN包的信息：包名、包版本 -->
            <Col :md="6" class="content-title">运行阶段:</Col>
            <Col :md="18">
              <div align="left">
               {{baseInfo.sourceType}}
              </div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">Scheme数:</Col>
            <Col :md="18">
              <div align="left">
                {{baseInfo.priorityDistribInfo}}
              </div>
            </Col>
          </Row>
          <Row>
            <Col :md="6" class="content-title">总通过率:</Col>
            <Col :md="18">
              <div align="left">
                {{baseInfo.totalScenePassRate}}
              </div>
            </Col>
          </Row>
        </div>
      </Col>
    </Row>
  </div>
</template>


<script>
    export default {
      name: 'ClientBaseInfo',
      props: ['baseInfo']
    }
</script>

<style scoped>
  .content-title{
    text-align: left;
    font-size: small;
    font-weight: bold;
    padding-left: 16px;
  }
  .content-title-container{
    border-left: 3px solid #42b983
  }
</style>
