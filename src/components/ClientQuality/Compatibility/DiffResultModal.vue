<template>
  <Modal
    v-model="visible"
    title="JSON对比结果"
    width="1200"
    :footer-hide="!showContent"
    :mask-closable="false"
    class="diff-modal"
  >
    <!-- 1.第一行过滤区域 -->
    <div class="filter-bar">
      <div class="filter-items" style="display: flex; align-items: baseline;">
        <div class="filter-label">选择DIFF参数类型</div>
        <RadioGroup v-model="selectedType">
          <Radio label="query"><span class="option-text">请求query</span></Radio>
          <Radio label="body"><span class="option-text">请求body</span></Radio>
          <Radio label="bizReq"><span class="option-text">数据源入参bizReq</span></Radio>
        </RadioGroup>

      <Button
        type="primary"
        style="margin-left: 66px; padding: 0 12px"
        :loading="loading"
        @click="handleQuery"
        >
       <template v-if="!loading"><Icon type="md-search" />确认查询</template>
       <template v-else>正在为您查询...</template>
      </Button>
      </div>
    </div>

    <!-- 2.基础的面板信息汇总 -->
    <div class="meta-info" v-if="showContent && currentData.length>0">
      <div class="meta-row">
        <div class="meta-item">
          <label>url_path：</label>
          <span class="value">{{ currentMeta.urlPath }}</span>
        </div>
        <div class="meta-item">
          <label>运行环境&场景名称：</label>
          <span class="value">{{ currentMeta.env }}</span>
        </div>
      </div>
      <div class="meta-row">
        <div class="meta-item">
          <label>diff_data_id：</label>
          <span >{{ currentMeta.baseDataId }}</span>
        </div>
        <div class="meta-item">
          <label>刷新&对比app版本：</label>
          <span >{{ currentMeta.appVersion }}</span>
        </div>
      </div>
    </div>

    <!-- 3.差异摘要 -->
    <div class="diff-summary" v-if="showContent">
      <Alert type="info" style="display: flex; align-items: center;">
        <template v-if="computeFinalDiffLength > 0">
          <!-- 主内容容器 -->
          <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
            <!-- 文本描述 -->
            <span>
              发现<span class="diff-count">{{computeFinalDiffLength }}</span>个接口DIFF结果异常，
              当前为第<span class="diff-count">{{ currentPage }}</span>个接口信息，
              发现存在<span class="diff-count">{{ diffCount }}</span>处差异。
              Base&Proxy的详细差异如下
            </span>

            <!-- 操作按钮组 -->
            <div style="display: flex; align-items: center; gap: 15px;">
              <Button
                type="primary"
                size="small"
                @click="copyDiffInfo"
                style="background-color: #9370db; border-color: #9370db;"
              >复制差异信息</Button>

              <Button
                type="primary"
                size="small"
                @click="jumpToEsAdress"
                style="background-color: #ff69b4; border-color: #ff69b4;"
              >前往ES地址</Button>

              <div style="display: flex; align-items: center; gap: 20px;">
                <i-switch
                  v-model="showOriginData"
                  size="small"
                ></i-switch>
                <span>展示过滤前全部参数</span>
              </div>
            </div>
          </div>
        </template>

        <template v-else>
          <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
          <span class="diff-count">未查询到差异，请选择其他「DIFF参数类型」继续查询</span>
            <div style="display: flex; align-items: center; gap: 5px;">
              <i-switch
                  v-model="showOriginData"
                  size="small"
                ></i-switch>
              <span>展示过滤前全部参数</span>
            </div>
          </div>
        </template>
      </Alert>
    </div>
    <!-- 3.5 差异信息 -->
    <div class="diff-paths" v-if="diffPaths.length">
      <div
        v-for="(path, index) in diffPaths"
        :key="index"
        class="path-item"
      >
        <Tag color="warning">差异 {{ index + 1 }}</Tag>
        {{ path }}
      </div>
    </div>

    <!-- 4.代码对比 -->
    <div class="diff-container" v-if="showContent">
      <code-diff
        :key="diffKey"
        :old-string="formattedOldData"
        :new-string="formattedNewData"
        output-format="side-by-side"
        language="json"
        :context="10"
      />
    </div>

    <!-- 5.修改后的底部区域 -->
    <div slot="footer" class="custom-footer" v-if="showContent">
        <div class="pagination-wrapper">
          <span>共{{computeFinalDiffLength}}条</span>
        <Page
          :current="currentPage"
          :total= "computeFinalDiffLength"
          :page-size="pageSize"
          simple
          @on-change="handlePageChange"
        />
      </div>
    </div>

  </Modal>
</template>

<script>
import CodeDiff from 'vue-code-diff'
import * as Jsondiffpatch from 'jsondiffpatch'

export default {
  name: 'DiffResultModal',
  components: {
    CodeDiff
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    imageShowInfo: Object
  },
  data() {
    return {
      taskId: 0,
      jobId: 0,
      name: '',
      diffKey: 0, // 新增
      currentPage: 1,
      pageSize: 1,    // 每页显示1个接口
      diffCount: 0, // 记录某个接口DIFF的差异点个数
      delta: {}, // 记录JsonDiffPatch输出的差异结果
      // 分组存储不同参数类型的有效差异数据
      groupedData: {query: [], body: [], bizReq: []},
      groupedDataFilter: {query: [], body: [], bizReq: []},
      // 从上面groupData中抽离出来，当前筛选项目的差异数据
      currentData: {},
      currentDataFilter: {},
      // 当前筛选参数类型的有效数据集合
      currentMeta: { env: '未知环境', appVersion: '未知版本', urlPath: '未知路径', baseDataId: '未知基准数据ID' },
      oldData: {}, // 初始化空对象
      newData: {}, // 初始化空对象
      // 其他状态
      diffPaths: [],
      selectedType: 'query',
      loading: false,
      showContent: false,
      currentPageIndex: 0,
      showOriginData: false
    }
  },
  computed: {
    visible: { // 初始化获取value给visible展示组件，以及关闭时发送input更新状态
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    formattedOldData() {
      console.log('当前数据:', {
        old: JSON.parse(JSON.stringify(this.oldData)),
        new: JSON.parse(JSON.stringify(this.newData))
      })
      return JSON.stringify(this.oldData, null, 2)
    },
    formattedNewData() {
      return JSON.stringify(this.newData, null, 2)
    },
    computeFinalDiffLength() {
      return this.showOriginData
      ? Number(this.currentData.length)
      : Number(this.currentDataFilter.length)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetState()
        this.extractParams()
        this.handleQuery() // 自动执行查询
      }
    },
    // 点击按钮的时候切换选择展示全部参数还是去掉忽略参数
    showOriginData(val) {
      this.showOriginData = val
      // 当开关变化时重新加载当前页数据
      this.loadPageData(this.currentPageIndex + 1)
    }
  },
  methods: {
    // 创建接口调用方法
    fetchDiffData() {
      this.$axios({
        method: 'post',
        url: 'https://shortlink.sankuai.com/storage/es/index/lyrebird-*/search',
        data: {  // POST请求体用data
          query: {
            bool: {
              must: [
                { match: { channel: 'notice' } },
                { match: { 'event.sender.function': 'diff_request_with_diff_data' } },
                { term: { 'env.runner.caseName.keyword': this.name } },
                { match: { 'env.runner.taskId': this.taskId } },
                { match: { 'env.runner.jobId': this.jobId } }
              ]
            }
          }
        }
      }).then((res) => {
        const hitsInfo = (((res.data || {}).res || {}).hits || {}).hits || []
        if (hitsInfo.length > 0) {
          console.log('获取到ES数据:', hitsInfo)
          // 转换接口数据为groupedData结构
          this.transformApiData(hitsInfo)
          // 根据当前查询参数类型在groupedData中获取有效数据
          this.currentData = this.groupedData[this.selectedType]
          this.currentDataFilter = this.groupedDataFilter[this.selectedType]
          console.log('当前DIFF数据加载成功：', JSON.stringify(this.currentData, null, 2))
        } else {
          console.log('未查询到相关数据')
          this.showContent = false
        }
      }).catch((error) => {
        console.error('接口请求失败:', error)
        this.showContent = false
        this.loading = false
      })
    },
    // 数据转换方法
    transformApiData(hitsInfo) {
      // 循环处理数据之前，清空旧数据
      this.groupedData = { query: [], body: [], bizReq: [] }
      this.groupedDataFilter = { query: [], body: [], bizReq: [] }
      hitsInfo.forEach(item => {
        const source = item._source || {}
        const event = source.event || {}
        const extra = event.extra || {}
        const diffData = extra.diff_api_request_with_diff_data || {}

        // 提取基础数据
        const baseItem = {
          meta: {
            env: '[' + (diffData.label || '未知环境') + ']' + ' ' + this.name,
            appVersion: (diffData.version_info) || '未知版本',
            urlPath: (diffData.url_path) || '未知路径',
            baseDataId: (diffData.diff_api_id) || '未知基准数据ID'
          }
        }

        // 预处理所有参数类型的差异
        const processed = {
          query: this.processDiffType('query', diffData),
          body: this.processDiffType('body', diffData),
          bizReq: this.processDiffType('bizReq', diffData)
        }

        // 按类型存储有效差异数据
        Object.keys(processed).forEach(type => {
          if (processed[type].hasDiff) {
            this.groupedData[type].push({
              meta: baseItem.meta,
              old: processed[type].old,
              new: processed[type].new,
              oldFilter: processed[type].oldFilter,
              newFilter: processed[type].newFilter
            })
          }
          if (processed[type].hasDiffFilter) {
            this.groupedDataFilter[type].push({
              oldFilter: processed[type].oldFilter,
              newFilter: processed[type].newFilter
            })
          }
        })
      })
    },
    // 差异处理方法
    processDiffType(type, diffData) {
      // eslint-disable-next-line no-unused-vars
      let oldData, newData, oldDataFilter, newDataFilter
      switch (type) {
        case 'query':
          oldData = (diffData.diff_url) || {}
          newData = (diffData.proxy_url) || {}
          oldDataFilter = (diffData.diff_url_filter) || {}
          newDataFilter = (diffData.proxy_url_filter) || {}
          break
        case 'body':
          oldData = (diffData.diff_data) || {}
          newData = (diffData.proxy_data) || {}
          oldDataFilter = (diffData.diff_data_filter) || {}
          newDataFilter = (diffData.proxy_data_filter) || {}
          break
        case 'bizReq':
          oldData = (diffData.diff_biz_req) || {}
          newData = (diffData.proxy_biz_req) || {}
          oldDataFilter = (diffData.diff_biz_req_filter) || {}
          newDataFilter = (diffData.proxy_biz_req_filter) || {}
          break
      }
      // 计算差异，判断下当前类型是否存在差异
      const delta = Jsondiffpatch.diff(oldData, newData)
      const deltaFilter = Jsondiffpatch.diff(oldDataFilter, newDataFilter)
      return {
        hasDiff: !!delta && Object.keys(delta).length > 0,
        hasDiffFilter: !!deltaFilter && Object.keys(deltaFilter).length > 0,
        old: this.deepParse(oldData),
        new: this.deepParse(newData),
        oldFilter: this.deepParse(oldDataFilter),
        newFilter: this.deepParse(newDataFilter)
      }
    },
    // 深度解析方法（处理字符串类型的JSON）
    deepParse(data) {
      try {
        if (typeof data === 'string') {
          return JSON.parse(data)
        }
        return data || {}
      } catch (e) {
        console.error('JSON解析失败:', e)
        return {}
      }
    },
    // 提取参数的独立方法
    extractParams() {
      if (!this.imageShowInfo) return
      // 解构赋值
      const { taskId, jobId, name } = this.imageShowInfo
      // 赋值给组件状态
      this.taskId = taskId
      this.jobId = jobId
      this.name = name
      // 调试日志
      console.log('参数已更新:', {
        taskId: this.taskId,
        jobId: this.jobId,
        name: this.name
      })
      // 如果需要，可以在这里调用接口
      // this.fetchData()
    },
    resetState() {
      this.currentPage = 1      // 重置分页
      this.oldData = {}         // 清空旧数据
      this.newData = {}         // 清空新数据
      this.diffCount = 0        // 重置差异计数
      this.diffPaths = []       // 清空差异路径
      this.diffKey = 0          // 重置diff组件key
      this.showContent = false  // 隐藏内容区域
    },
    // 分页切换处理方法
    handlePageChange(page) {
      console.log('当前page：', page)
      this.pageSize = 1
      this.currentPage = page
      this.loadPageData(page)
      // 将页面滚动条设置为顶部
      this.$nextTick(() => {
        const container = this.$el.querySelector('.diff-modal .ivu-modal-body')
        if (container) {
          container.scrollTop = 0 // 将滚动条位置设置为顶部
        }
      })
    },
    // ！！加载渲染的数据方法
    loadPageData(page) {
      const index = page - 1
      this.currentPageIndex = index // 记录当前页索引
      if (index >= 0 && index < this.currentData.length) {
        const currentItem = this.currentData[index]
        if (this.showOriginData) {
          this.oldData = JSON.parse(JSON.stringify(currentItem.old || {}))
          this.newData = JSON.parse(JSON.stringify(currentItem.new || {}))
          this.currentMeta = currentItem.meta  // 更新当前元信息
        } else {
          this.oldData = JSON.parse(JSON.stringify(currentItem.oldFilter || {}))
          this.newData = JSON.parse(JSON.stringify(currentItem.newFilter || {}))
          this.currentMeta = currentItem.meta  // 更新当前元信息
        }
      }
      // 强制重新渲染
      this.$nextTick(() => {
        this.analyzeDifferences()
        this.diffKey++ // 强制重新渲染diff组件
        const container = document.querySelector('.diff-container')
        if (container) container.scrollTop = 0
      })
    },
    handleQuery() {
      this.resetState() // 查询之前先进行清空内容
      this.loading = true
      this.fetchDiffData()
      setTimeout(() => {
        this.loadPageData(this.currentPage) // 改为调用分页加载方法
        this.loading = false
        this.showContent = true
      }, 800)
    },
    analyzeDifferences() {
      this.diffPaths = []
      this.delta = Jsondiffpatch.diff(this.oldData, this.newData)
      console.log('delta计算完成：')
      this.traverseDelta(this.delta, '$')
      this.diffCount = this.diffPaths.length
    },
    traverseDelta(delta, path) {
      // 递归输出差异的jsonpath
      if (!delta) return

      if (Array.isArray(delta)) {
        this.diffPaths.push(path)
        return
      }

      Object.keys(delta).forEach(key => {
        const currentPath = `${path}.${key}`
        const value = delta[key]

        if (Array.isArray(value)) {
          this.diffPaths.push(currentPath)
        } else if (typeof value === 'object') {
          this.traverseDelta(value, currentPath)
        }
      })
    },
    copyDiffInfo() {
      const text = `发现${this.computeFinalDiffLength}个接口DIFF结果异常，当前为第${this.currentPage}个接口信息，发现存在${this.diffCount}处差异：\n${this.diffPaths.join('\n')}`

      if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
          .then(() => {
            this.$Message.success('差异信息已复制')
          })
          .catch(() => {
            this.fallbackCopy(text)
          })
      } else {
        this.fallbackCopy(text)
      }
    },
    fallbackCopy(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      document.body.appendChild(textarea)
      textarea.select()
      try {
        document.execCommand('copy')
        this.$Message.success('差异信息已复制')
      } catch (error) {
        this.$Message.error('请手动复制')
      } finally {
        document.body.removeChild(textarea)
      }
    },
    handleClose() {
      this.visible = false
      this.resetState()
    },
    jumpToEsAdress() {
      let encodedName = encodeURIComponent(this.name)
      let url = 'https://lyrebird.sankuai.com/app/kibana/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1y,to:now))&_a=(columns:!(_source),filters:!' +
        '((\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:event.sender.function,negate:!f,params:(query:diff_request_with_diff_data),type:phrase),query:(match_phrase:(event.sender.function:diff_request_with_diff_data))),' +
        '(\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:channel,negate:!f,params:(query:notice),type:phrase),query:(match_phrase:(channel:notice))),' +
        '(\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:env.runner.caseName,negate:!f,params:(query:\'' + encodedName + '\'),type:phrase),query:(match_phrase:(env.runner.caseName:\'' + encodedName + '\'))),' +
        '(\'$state\':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:env.runner.taskId,negate:!f,params:(query:\'' + this.taskId + '\'),type:phrase),query:(match_phrase:(env.runner.taskId:\'' + this.taskId + '\')))),' +
        'index:ba0f10f0-8d90-11ed-b7dd-43270782279d,interval:auto,query:(language:kuery,query:\'\'),sort:!())'
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped>
/* 优化后的样式 */
.diff-modal >>> .ivu-modal-header {
  padding: 8px 16px;
}

.diff-modal >>> .ivu-modal-body {
   max-height: 75vh;
   overflow-y: auto;
   padding: 8px 16px;
}
.diff-modal >>> .ivu-modal-header-inner {
  font-size: 14px;
  line-height: 1.5;
}

.filter-bar {
  background: #ffffff;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 8px 16px;   /* 上下内边距减少50% */
  margin-bottom: 4px;
  box-shadow: 0 2px 8px rgba(28, 36, 56, 0.05);
}

.filter-items {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meta-info {
  background: linear-gradient(145deg, #f8f9fe 0%, #ffffff 100%);
  border: 1px solid #e1e4f0;
  border-radius: 8px;
  padding: 4px 16px;  /* 作用于容器内部 */
  margin: 8px 0px; /* 作用于容器外部 */
  box-shadow: 0 4px 12px rgba(28, 36, 56, 0.08);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性渐变条 */
.meta-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #2d8cf0 0%, #19be6b 100%);
}

.meta-row {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr)); /* 强制等分两列 */
  gap: 16px; /* 列间距 */
  margin-bottom: 3px; /* 行间距 */
}

.meta-item {
  display: grid;
  grid-template-columns: 160px 1fr; /* 固定标签宽度+自适应内容 */
  align-items: baseline; /* 基线对齐 */
}

.meta-item label {
  color: #515a6e;        /* 调整为antd的深灰色 */
  font-size: 14px;
  white-space: nowrap; /* 禁止标签换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 过长标签显示省略号 */
}

.meta-item span {
  color: #333;
  font-weight: 400;
  font-size: 14px;
  word-break: break-all; /* 允许值换行 */
}

.diff-summary {
  margin: 4px 0;
}

.diff-count {
  color: #ff4d4f;
  font-weight: 600;
  margin: 0 4px;
}

.diff-paths {
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 2px 0;
  max-height: 105px;   /* 最大高度 */
  overflow-y: auto;   /* 垂直滚动 */
  padding: 2px 12px;
}

.path-item {
  padding: 4px 0;
  font-family: 'Source Code Pro', monospace; /* 等宽字体 */
  font-size: 12px;
  line-height: 1.4;
}

.diff-container {
  height: 43vh;       /* 代码块整体的高度*/
  border-radius: 4px;
  overflow: auto;     /*  内容超出滚动 */
  background: #f8f8f9; /* 浅灰背景 */
  margin: 10px 0 0 0; /* 上16px 右0 下0 左0 */
  border-bottom: 1px solid #e8e8e8;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  transition: background 0.3s;
}

.switch-label {
  display: inline;
  font-size: 14px;
  color: #333;
  word-break: break-word;
}


/* 调整开关颜色 */
.switch-wrapper >>> .ivu-switch-checked {
  background-color: #81c784;
  border-color: #81c784;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;    /* 半粗体 */
  letter-spacing: 0.3px; /* 字间距 */
  margin-right: 24px;  /* 右间距 */
  position: relative;
  top: 0px;  /* 微调垂直位置 */
}

.filter-label::after {
  content: ":";
  position: absolute;
  right: 12px;
  color: #808695; /* 中灰色 */
}

/* 调整分页样式 */
.pagination-wrapper {
  margin: 0px 0;
  display: flex;
  justify-content: flex-end;
}

</style>
