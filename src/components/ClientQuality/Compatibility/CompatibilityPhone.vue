<template>
  <div>
    <div style="padding-bottom: 16px; display: flex;">
      <div style="display: flex;">
        <span style="font-weight: 600;padding-right: 10px;">接入方式:  </span>
        <CheckboxGroup v-model="filter.connectTypes.values"  @on-change="checkAllGroupChange()">
          <Checkbox label="local">本地</Checkbox>
          <Checkbox label="remote">云测</Checkbox>
        </CheckboxGroup>
      </div>
      <div style="display: flex;">
        <span style="font-weight: 600;padding-right: 10px;padding-left: 40px;">设备标签: </span>
        <CheckboxGroup v-model="filter.labels.values"  @on-change="checkAllGroupChange()">
          <Checkbox v-for="label in filter.labels.all" v-bind:key="label" :label="label"></Checkbox>
        </CheckboxGroup>
      </div>
    </div>
    <div style="padding-bottom: 10px">
      已选择{{checkDevices.length}}台设备
      <Badge style="padding-left: 30px" status="success" /> 空闲
      <Badge style="padding-left: 30px" status="processing" /> 使用中-需排队
    </div>
    <div>
      <CheckboxGroup v-model="checkDevices" @on-change="checkAllGroupChange()">
        <template v-for="phone in filterPhoneList">
          <Row v-bind:key="phone.sn">
            <Col :span="20">
              <Checkbox :label="phone.sn">
                <Badge></Badge>
                <Badge v-if="phone.status === 'Idle'" status="success" />
                <Badge v-else status="processing" />
                <Tag color="green" v-if="phone.status === 'Idle'">{{getPhoneLabel(phone)}}</Tag>
                  <Tag color="blue" v-else>{{getPhoneLabel(phone)}}</Tag>
                {{phone.model}} - {{phone.version}}
              </Checkbox>
            </Col>
          </Row>
        </template>
      </CheckboxGroup>
    </div>
  </div>
</template>

<script>
  /* eslint-disable */
  export default {
      name: "CompatibilityPhone",
      props: ["platform","showPhone","jobType", "app", "business", "deviceType"],
      data(){
        return{
          phoneHost:"1",
          checkDevices:[],
          phoneList:[],
          phoneIdList:[],
          filter: {
            connectTypes: {values: ["local", "remote"]},
            labels: {values: [], all: []}
          },
          filterPhoneList: []
        }
      },
      watch:{
        showPhone(val){
          if(val){
            this.phoneHost="1";
            this.checkDevices = []
            this.filterPhoneList = []
            this.getPhoneInfo()
          }
        }
      },
      methods:{
        selectPhone(){
          this.$emit("selectPhone",this.checkDevices,this.phoneHost);
        },
        checkAllGroupChange () {
          this.filterPhones()
          this.selectPhone()
        },
        filterPhones() {
          let phones = []
          for (let i in this.phoneList) {
            let phone = this.phoneList[i]
            if (!this.filter.connectTypes.values.includes(phone.connectType)) {
              continue
            }
            if (phone.scopeOfUse.includes('public')) {
              if (this.filter.labels.values.includes("公用")) {
                phones.push(phone)
              }
              continue
            }
            if (this.filter.labels.values.includes(phone.label)) {
              phones.push(phone)
            }
          }
          this.filterPhoneList = phones
        },
        getPhoneLabel(phone) {
          let label = []
          if (phone.connectType === 'local') {
            label.push('本地')
          } else {
            label.push('云测')
          }
          if (phone.scopeOfUse.includes('public')) {
            label.push('公用')
          } else {
            label.push(phone.label)
          }
          return label.join('+')
        },
        getPhoneInfo(){
          this.phoneIdList=[];
          this.checkAllGroup=[];
          this.$axios({
            method:"get",
            params:{
              "business": this.business,
              "jobType": this.jobType,
              "platform": this.platform,
              "sourceType": 'Microscope',
              "category": this.app,
              "filterRule": 'ALL',
              "usePublic": true,
              "deviceType": this.deviceType
            },
            url:this.env.url+"compatibilityPhone/available"
          }).then((res) => {
            let code = res.data.code;
            if (code !== 0) {
              this.$Notice.error({
                    title: '获取设备列表失败'
                  });
            } else {
              this.phoneList = res.data.data.devices;
              this.filterPhoneList = this.phoneList
              let labels = new Set()
              for(let i=0;i<this.phoneList.length;i++){
                this.phoneIdList.push(this.phoneList[i].sn);
                labels.add(this.phoneList[i].label)
              };
              labels.add("公用")
              this.allLabel = labels
              this.filter.labels.values = Array.from(labels)
              this.filter.labels.values = this.filter.labels.values.filter(label => label !== '临时')
              this.filter.labels.all = Array.from(labels)
              this.filterPhones()
              this.$nextTick(function(){
                this.selectPhone();
              });
            }
          }).catch(function (error) {
            console.log(error)
          })
        }
      }
  }
</script>

<style scoped>

</style>
