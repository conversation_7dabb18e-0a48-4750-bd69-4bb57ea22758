<template>
  <div id="app">
    <div class="page-info">
      <div class="section-title">页面信息</div>
      <div class="page-name info-item">
        <span class="info-label">页面名称：</span>
        <span class="info-value">{{ PageName }}</span>
      </div>
      <div class="page-description info-item">
        <span class="info-label">页面描述：</span>
        <span class="info-value">{{ pageDescription }}</span>
      </div>
      <div class="page-knowledge info-item">
        <span class="info-label">父知识页面名称：</span>
        <span class="info-value">{{ pageKnowledge }}</span>
      </div>
      <Button
          v-if="pageKnowledge"
          icon="ios-cog"
          size="medium"
          type="primary"
          class="annotation-button"
          @click="openCaseAnnotation(pageKnowledge)"
          ghost
        >
          跳转至父页面
      </Button>
    </div>
    <div class="screenshot-container">
      <img :src="imageSrc" alt="screenshot" class="screenshot-image" ref="screenshotImage" @load="calculateImageOffset">
      <div v-for="(button, index) in buttons" 
           :key="index" 
           :style="getButtonStyle(button)" 
           class="screenshot-button" 
           @click="handleButtonClick(button, index)">
      </div>
    </div>
    <div class="elem-info">
      <div class="section-title">元素信息</div>
      <div class="elem-text info-item">
        <span class="info-label">元素文本：</span>
        <span class="info-value">{{ elemText }}</span>
      </div>
      <div class="elem-bounds info-item">
        <span class="info-label">元素坐标：</span>
        <span class="info-value">{{ elemBounds }}</span>
      </div>
      <div class="elem-description info-item">
        <span class="info-label">元素描述：</span>
        <input class="input-text" type="text" v-model="elemDescription">
        <button @click="updateLabel('elemDescription')">更新元素描述</button>
      </div>
      <div class="elem-function info-item">
        <span class="info-label">元素功能：</span>
        <input class="input-text" type="text" v-model="elemFunction">
        <button @click="updateLabel('elemFunction')">更新元素功能</button>
      </div>
      <div class="elem-interaction-record info-item">
        <span class="info-label">元素交互记录：</span>
        <span class="info-value">{{ elemInteractionRecord }}</span>
      </div>
      <div class="elem-next-page info-item">
        <span class="info-label">下级页面：</span>
        <Button
          v-if="elemNextPage"
          icon="ios-cog"
          size="medium"
          type="primary"
          class="annotation-button"
          @click="openCaseAnnotation(elemNextPage)"
          ghost
        >
          跳转至下级页面
        </Button>
      </div>
    </div>
    <div v-for="(toast, index) in toasts" :key="toast.id" class="toast" :style="{ top: `${50 + index * 60}px`, opacity: toast.visible ? 1 : 0 }">
      {{ toast.message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'UIKnowledgeAnnotation',
  components: {},
  data() {
    return {
      PageName: '',
      pageInfo: {},
      pageDocId: '',
      esSearchUrl: this.env.es_url + '/es_service/search',
      esUpdateUrl: this.env.es_url + '/es_service/update',
      widgetMatchUrl: this.env.es_url + '/auitest/match_widget',
      imageSrc: '',
      pageDescription: '',
      pageKnowledge: '',
      buttons: {},
      imageWidth: 0,
      imageHeight: 0,
      imageOffsetLeft: 0,
      imageOffsetTop: 0,
      elemInfo: {},
      elemIndex: '',
      elemText: '',
      elemBounds: [],
      elemDescription: '',
      elemInteractionRecord: '',
      elemNextPage: null,
      elemFunction: '',
      knowledgePageInfo: {},
      knowledgePageDocId: '',
      knowledgeWidgets: [],
      updateMessage: '',
      toastVisible: false,
      toasts: []
    }
  },
  computed: {

  },
  mounted() {
    this.getKnowledgeByPageName()
  },
  watch: {

  },
  methods: {
    async getKnowledgeByPageName() {
      this.PageName = this.$route.query.pageInfo
      let ESPageResult = await this.getPageResultsFromES(this.PageName)
      this.pageInfo = ESPageResult.source
      this.pageDocId = ESPageResult.id
      if ('knowledge_page_name' in this.pageInfo && this.pageInfo.knowledge_page_name !== '') {
        ESPageResult = await this.getPageResultsFromES(this.pageInfo.knowledge_page_name)
        this.knowledgePageInfo = ESPageResult.source
        this.knowledgePageDocId = ESPageResult.id
        this.knowledgeWidgets = this.widgetsToButtons(this.knowledgePageInfo)
      }
      this.imageSrc = this.pageInfo.image_url
      this.pageDescription = this.pageInfo.description
      this.pageKnowledge = this.pageInfo.knowledge_page_name
      this.buttons = this.widgetsToButtons(this.pageInfo)
      this.calculateImageOffset()
      this.$forceUpdate()
    },
    async getPageResultsFromES(PageName) {
      try {
        let pageIndex = 'cluster_center_page'
        let requestData = {
          'index': pageIndex,
          'query': {
            'query': {
              'bool': {
                'must': [
                  { 'match': { 'name.keyword': PageName } }
                ],
                'must_not': [],
                'should': [],
                'filter': []
              }
            },
            'from': 0,
            'size': '100',
            'sort': [],
            'profile': true
          }
        }
        let res = await this.$axios({
          method: 'post',
          url: this.esSearchUrl,
          data: JSON.stringify(requestData),
          headers: {
            'Content-Type': 'application/json'
          }
        })
        if (res.data.hits.hits.length === 0) {
          pageIndex = 'knowledge_page_index'
          requestData.index = pageIndex
          res = await this.$axios({
            method: 'post',
            url: this.esSearchUrl,
            data: JSON.stringify(requestData),
            headers: {
              'Content-Type': 'application/json'
            }
          })
          return {
            'source': res.data.hits.hits[0]._source,
            'id': res.data.hits.hits[0]._id
          }
        }
        return {
          'source': res.data.hits.hits[0]._source,
          'id': res.data.hits.hits[0]._id
        }
      } catch (error) {
        console.log(error)
      }
    },
    async updatePageInfoFromES(type) {
      try {
        let pageIndex = 'knowledge_page_index'
        let pageDocId = this.pageDocId
        let updateBody = {
          'doc': this.pageInfo
        }
        if (!('knowledge_page_name' in this.pageInfo) || (this.pageInfo.knowledge_page_name === '')) { // 如果父页面为空，则是聚类中心page
          pageIndex = 'cluster_center_page'
        }
        if (type === 'knowledgePage') {
          pageDocId = this.knowledgePageDocId
          updateBody = {
            'doc': this.knowledgePageInfo
          }
          pageIndex = 'cluster_center_page'
        }
        let requestData = {
          'index': pageIndex,
          'id': pageDocId,
          'body': updateBody
        }
        let res = await this.$axios({
          method: 'post',
          url: this.esUpdateUrl,
          data: JSON.stringify(requestData),
          headers: {
            'Content-Type': 'application/json'
          }
        })
        return res.data
      } catch (error) {
        console.log(error)
        return null
      }
    },
    widgetsToButtons(pageInfo) {
      let widgetsStr = pageInfo.widgets
      // 正则表达式模式
      let newWidgets = {}
      let widgetsList = widgetsStr.split('\n')
      for (let widget of widgetsList) {
        if (widget === '') {
          continue
        }
        let idPattern = /id:\[(\d+)\]/
        let descriptionPattern = /description:\[([^\]]+)\]/
        let functionPattern = /function:\[([^\]]+)\]/
        let textPattern = /text:\[([^\]]+)\]/
        let boundsPattern = /bounds:\[([^\]]+)\]/
        let hashValuePattern = /hash_value:\[([a-f0-9]+)/
        let pathPattern = /path:\[([^\]]+)\]/
        let namePattern = /name:\[([^\]]+)\]/
        let tracePattern = /trace:\[(\[.*?\])\]/
        let interactionRecordPattern = /interaction_record:\[(.*?)\]/
        let nextPagePattern = /next_page:\[(.*?)\]/
        let widgetId = widget.match(idPattern)[1]
        let description
        try {
          description = widget.match(descriptionPattern)[1]
        } catch (err) {
          description = null
        }
        let func = widget.match(functionPattern)[1]
        let text
        try {
          text = widget.match(textPattern)[1]
        } catch (err) {
          text = ''
        }
        let bounds = '[' + widget.match(boundsPattern)[1] + ']'
        try {
          bounds = JSON.parse(bounds)
        } catch (err) {
          bounds = widget.match(boundsPattern)[1] + ']]'
          bounds = JSON.parse(bounds)
        }
        let hashValue = widget.match(hashValuePattern)[1]
        let pathStr = widget.match(pathPattern)[1] + ']'
        let path = pathStr.trim('[]').replace(/'/g, '').split(', ')
        if (pathStr.trim('[]') === '') {
          path = []
        }
        let trace
        try {
          let traceStr = widget.match(tracePattern)[1]
          let traceStrHtml = traceStr.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#039;')
          let decodedData = traceStrHtml
          trace = JSON.parse(decodedData)
        } catch (err) {
          trace = null
        }
        let name
        try {
          name = widget.match(namePattern)[1]
        } catch (err) {
          name = null
        }
        let interactionRecord
        try {
          let interactionRecordStr = widget.match(interactionRecordPattern)
          interactionRecord = JSON.parse(interactionRecordStr[1])
        } catch (err) {
          interactionRecord = null
        }
        let nextPage
        try {
          let nextPageStr = widget.match(nextPagePattern)
          nextPage = JSON.parse(nextPageStr[1])
        } catch (err) {
          nextPage = null
        }
        newWidgets[widgetId] = {
          'name': name,
          'description': description,
          'function': func,
          'text': text,
          'bounds': bounds,
          'hash_value': hashValue,
          'path': path,
          'interaction_record': interactionRecord,
          'next_page': nextPage
        }
        if (trace !== null) {
          newWidgets[widgetId]['trace'] = trace
        }
      }
      return newWidgets
    },
    widgetsToStr(buttons) {
      let widgetsStr = ''
      for (const [widgetId, widgetInfo] of Object.entries(buttons)) {
        const description = widgetInfo.description
        const func = widgetInfo.function
        const text = widgetInfo.text
        const bounds = widgetInfo.bounds
        const hashValue = widgetInfo.hash_value
        const path = widgetInfo.path
        const name = widgetInfo.name
        widgetsStr += `id:[${widgetId}]/description:[${description}]/function:[${func}]/text:[${text}]/bounds:[${bounds}]/hash_value:[${hashValue}]/path:[${path}]/name:[${name}]`
        if (widgetInfo.interaction_record) {
          const interactionRecord = JSON.stringify(widgetInfo.interaction_record)
          widgetsStr += `/interaction_record:[${interactionRecord}]`
        }
        if (widgetInfo.next_page) {
          const nextPage = JSON.stringify(widgetInfo.next_page)
          widgetsStr += `/next_page:[${nextPage}]`
        }
        if (widgetInfo.trace) {
          const traces = widgetInfo.trace
          const traceStr = JSON.stringify(traces, null, 2)
          widgetsStr += `/trace:[${traceStr}]`
        }
        widgetsStr += '\n'
      }
      return widgetsStr
    },
    async matchKnowledgePageWidgetAndUpdate() {
      let matchResult = await this.matchKnowledgePageWidget()
      if (matchResult.error) {
        return false
      }
      if (matchResult.ifMatch) {
        console.log('更新父页面ES')
        this.knowledgeWidgets[matchResult.id] = this.elemInfo
        let newWidgetsStr = this.widgetsToStr(this.knowledgeWidgets)
        this.knowledgePageInfo.widgets = newWidgetsStr
      } else {
        console.log('更新父页面ES')
        let kvCount = Object.keys(this.knowledgeWidgets).length
        this.knowledgeWidgets[kvCount] = this.elemInfo
        let newWidgetsStr = this.widgetsToStr(this.knowledgeWidgets)
        this.knowledgePageInfo.widgets = newWidgetsStr
      }
      return true
    },
    async matchKnowledgePageWidget() {
      try {
        let requestData = {
          'widget_need_match': this.elemInfo,
          'widgets_list': this.knowledgeWidgets
        }
        let res = await this.$axios({
          method: 'post',
          url: this.widgetMatchUrl,
          data: JSON.stringify(requestData),
          headers: {
            'Content-Type': 'application/json'
          }
        })
        if (res.data.code === 0) {
          return {
            'ifMatch': res.data.if_match,
            'id': res.data.widget_id,
            'error': false
          }
        } else {
          return {
            'ifMatch': false,
            'id': '-1',
            'error': true
          }
        }
      } catch (error) {
        console.log(error)
        return {
          'ifMatch': false,
          'id': '-1',
          'error': true
        }
      }
    },
    calculateImageOffset() {
      const imageElement = this.$refs.screenshotImage
      this.imageWidth = imageElement.clientWidth
      this.imageHeight = imageElement.clientHeight
      this.imageScaleWidth = this.imageWidth / imageElement.naturalWidth
      this.imageScaleHeight = this.imageHeight / imageElement.naturalHeight
      this.imageOffsetLeft = imageElement.offsetLeft
      this.imageOffsetTop = imageElement.offsetTop
    },
    getButtonStyle(button) {
      const x = (button.bounds[0] - this.imageOffsetLeft) * this.imageScaleWidth
      const y = (button.bounds[1] - this.imageOffsetTop) * this.imageScaleHeight
      let w = (button.bounds[2] - button.bounds[0]) * this.imageScaleWidth
      let h = (button.bounds[3] - button.bounds[1]) * this.imageScaleHeight
      return {
        position: 'absolute',
        left: `${x}px`,
        top: `${y}px`,
        width: `${w}px`,
        height: `${h}px`,
        color: '#fff',
        border: 'none',
        cursor: 'pointer'
      }
    },
    handleButtonClick(button, index) {
      this.elemInfo = button
      this.elemIndex = index
      this.elemText = button.text
      this.elemBounds = button.bounds
      this.elemDescription = button.description
      this.elemInteractionRecord = button.interaction_record
      this.elemNextPage = button.next_page
      this.elemFunction = button.function
    },
    async updateLabel(type) {
      if (type === 'elemDescription') { // 更新节点描述
        // 1.1 更新当前widget信息的描述信息
        this.elemInfo.description = this.elemDescription
        this.updateWidgetInfo()
      } else if (type === 'elemFunction') {
        // 1.1 更新当前widget信息的功能信息
        this.elemInfo.function = this.elemFunction
        this.updateWidgetInfo()
      }
    },
    async updateWidgetInfo() {
      // 1 将widget信息转换为str
      this.buttons[this.elemIndex] = this.elemInfo
      let widgetsStr = this.widgetsToStr(this.buttons)
      // 2 更新到当前page中
      this.pageInfo.widgets = widgetsStr
      let updateResult = await this.updatePageInfoFromES('')
      let updateResultStr = ''
      if (updateResult.result === 'updated' || updateResult.result === 'noop') {
        updateResultStr = '更新成功'
      } else {
        updateResultStr = '更新失败'
      }
      // 更新当前此widgets到父页面的知识中（如果有父页面的话）
      if ('knowledge_page_name' in this.pageInfo && this.pageInfo.knowledge_page_name !== '') {
        // 匹配是否为已有节点
        console.log('更新父页面')
        let updateFlag = await this.matchKnowledgePageWidgetAndUpdate()
        if (updateFlag) {
          // 更新至ES
          let knowledgeUpdateResult = await this.updatePageInfoFromES('knowledgePage')
          if (knowledgeUpdateResult.result === 'updated' || knowledgeUpdateResult.result === 'noop') {
            updateResultStr = '更新成功'
          } else {
            updateResultStr = '更新失败'
          }
        }
      }
      this.showToast(updateResultStr)
    },
    showToast(msg) {
      const id = Date.now() // 使用时间戳作为唯一 ID
      this.toasts.push({ id, message: msg, visible: true })

      // 设置定时器在 2 秒后隐藏 toast
      setTimeout(() => {
        this.hideToast(id)
      }, 2000)
    },
    hideToast(id) {
      const index = this.toasts.findIndex(toast => toast.id === id)
      if (index !== -1) {
        this.toasts[index].visible = false
        // 在过渡时间后完全移除 toast
        setTimeout(() => {
          this.removeToast(id)
        }, 300) // 300ms 与 CSS 过渡时间一致
      }
    },
    removeToast(id) {
      const index = this.toasts.findIndex(toast => toast.id === id)
      if (index !== -1) {
        this.toasts.splice(index, 1)
      }
    },
    openCaseAnnotation(pageInfo) {
      let pageName = ''
      if (typeof pageInfo === 'string') {
        pageName = pageInfo
      } else if (typeof pageInfo === 'object') {
        pageName = pageInfo.name
      }
      if (pageInfo !== null) {
        let routeData = this.$router.resolve(
          {
            path: '/microscope/UIKnowledgeAnnotation',
            query: {
              pageInfo: pageName
            }
          }
        )
        window.open(routeData.href, '_blank')
      }
    }
  }
}
</script>

<style scoped>
#app {
  display: flex;
  justify-content: center;
  position: relative;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.screenshot-container {
  position: relative;
  display: inline-block;
  justify-content: center;
  align-items: center;
}

.screenshot-image {
  max-width: 100%;
  max-height: 100vh;
}

.screenshot-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.elem-info {
  position: relative;
  margin-left: 50px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-width: 500px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  position: relative;
  margin-right: 50px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-width: 500px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
}

.info-value {
  margin-left: 5px;
  color: #333;
  word-wrap: break-word;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.input-text {
  width: 300px;
}

.toast {
  font-size: 16px;
  position: fixed;
  right: 50px;
  max-width: 300px;
  padding: 12px 20px;
  background-color: rgba(0, 92, 229, 0.9);
  color: #fff;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: opacity 0.3s ease-in-out; /* 过渡时间设置为 300ms */
}

.toast-enter, .toast-leave-to {
  opacity: 0;
}

.toast-enter-active, .toast-leave-active {
  transition: opacity 0.3s ease-in-out;
}
</style>