<template>
    <div class="task-view-container" v-show="showModal">
        <div class="image-view">
            <div style="text-align: center">
                <span>视频</span>
                <a :href="videoDetailResult.videoUrl" target="_blank">
                    <Icon type="md-download" />
                </a>
            </div>
            <ClientVideoPlayer :videoLocation="videoDetailResult.pic_location"></ClientVideoPlayer>
        </div>
        <div class="list-view">
            <div style="text-align: center;">
                <span>异常信息列表</span>
            </div>
            <div style="height:570px;overflow-y: auto;">
                <ul class="item-list">
                    <li v-for="(item, index) in videoDetailResult.exceptions" :key="0 + index">
                      <div class="form-item">
                        <a :href="item.screenshot" target="_blank"><img :src="item.screenshot" style="max-width:80px;"/></a>
                      </div>
                      <div class="form-item" style="width:400px; text-align:left;">
                        <json-viewer :value="getJsonViewerData(item.info)" :expand-depth=1 name="code" style="border-radius: 5px;border: 1px solid darkgray;width: 370px;"></json-viewer>
                      </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
  <script>
/* eslint-disable */
import ImageView from "./ImageView";
import ClientVideoPlayer from "./ClientVideoPlayer"
import JsonViewer from 'vue-json-viewer'
export default {
  components: { ImageView, ClientVideoPlayer, JsonViewer },
  name: "VideoResultModal",
  props: ["videoDetailResult"],
  data() {
    return {
    }
  },
  computed: {
    showModal: function () {
      return this.videoDetailResult.type === 'video'
    }
  },
  methods: {
    getJsonViewerData(infoStr) {
      let info = ""
      try {
        info = eval('(' + infoStr + ')')
      } catch (error) {
        info = infoStr
      }
      return info
    }
  }
}
</script>
  <style scoped>
  .task-view-container {
      white-space: nowrap;
      height: 600px;
  }
  
  .image-view {
      overflow: hidden;
      display: inline-block;
      vertical-align: top;
  }
  .list-view {
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
    margin-left: 30px;
    width: 520px;
  }
  .item-list {
    list-style: none;
  }
  .item-list li {
    display: flex;
    margin-top: 10px;
  }
  .form-item {
    display: inline-grid;
    margin-left: 20px;
  }
  .jv-container >>> .jv-code {
    overflow: auto;
    padding: 5px 5px;
  }
  .client-video-player {
    height: 590px;
  }
  .client-video-player >>> .video-player, .client-video-player >>> .jv-container, .client-video-player >>> .video-js.vjs-fluid {
    height: 590px;
  }

  </style>
  