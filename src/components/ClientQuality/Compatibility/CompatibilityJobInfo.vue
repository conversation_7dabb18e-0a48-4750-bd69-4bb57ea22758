<template>
  <div style="width: 100%;">
    <Row style="margin-top: 10px;">
      <Col span="2" offset="1" style="margin-top: 7px">
        <Checkbox v-model="filter.onlyMe" @on-change="refreshJobList"><Icon type="ios-hand-outline"></Icon> 只看我</Checkbox>
      </Col>
      <Col span="4" style="display: flex; justify-content: center">
        <DatePicker type="daterange" :options="dateSelectOptions" v-model="filter.dateRange" placement="bottom-end" placeholder="选择日期"
                    style="width: 250px" @on-change="refreshJobList"></DatePicker>
      </Col>
      <Col span="4" offset="1" style="display: flex; align-items: center">
        <Select v-model="filter.business" placeholder="业务方向" style="width: 250px" clearable @focus="getBusinessList" @on-change="refreshJobList">
          <Option v-for="item in businessList" :key="item.value" :value="item.label">{{ item.label }}</Option>
        </Select>
      </Col>
      <Col span="1">
        <Button @click="refreshJobList" type="primary" style="margin-left: 25px">查询</Button>
      </Col>
      <Col span="8" style="text-align: right;">
        <Badge status="success" text="任务完成" style="margin-right: 8px;"/>
        <Badge status="warning" text="等待机器" style="margin-right: 8px;"/>
        <Badge status="processing" text="任务执行中" style="margin-right: 8px;"/>
        <Badge status="error" text="任务报错" style="margin-right: 8px;"/>
        <Badge status="default" text="任务暂停"/>
      </Col>
    </Row>
    <br>
    <Table :loading="loading" max-height="800" :data="this.jobList" :columns="jobTableColumns" @on-filter-change="columnFilterChange">
      <template slot-scope="{ row, index }" slot="taskStatus">
        <template v-for="status in row.taskStatus">
          <Badge :status="getStatusColor(status)" style="margin-right: 5px;"></Badge>
        </template>
      </template>
      <template slot-scope="{ row, index }" slot="progress">
        <Progress :percent="getJobProgress(row)" :stroke-width="5" :status="getProgressColor(row)" />
      </template>
      <template slot-scope="{ row, index }" slot="job_source">
        <a target="_blank" :href="row.sourceUrl">
          <Tag type="border" :color="getSourceTagColor(row.sourceType)">
            {{getSourceShow(row.sourceType,row.appVersion)}}
          </Tag>
        </a>
      </template>
      <template slot-scope="{ row, index }" slot="job_trace">
        <a target="_blank" :href="row.traceInfo.url">{{getTraceInfoShow(row)}}</a>
      </template>
      <template slot-scope="{ row, index }" slot="category">
        <img class="layout-logo" :src="getAppIcon(row.category)" style="height: 24px; width: 24px;" />{{row.category}}
      </template>
      <template slot-scope="{ row, index }" slot="plat">
        <img class="layout-logo" :src="getPlatIcon(row.plat)" style="height: 24px; width: 24px;" :title="row.plat"/>
      </template>
      <template slot-scope="{ row, index }" slot="action">
        <Button style="width: 70px" :type="getActionButtonColor(row)" size="small" title="查看Job详情" @click="getTask(row.jobId)">
          {{getActionButtonShow(row)}}
        </Button>
        <Button :disabled="getJobRerunDisabled(row)" icon="md-refresh" type="info" size="small" title="重跑Job" @click="rerunJobConfirm(row)" ghost></Button>
        <Button :disabled="getJobDeleteDisabled(row)" icon="md-trash" type="error" size="small" title="删除Job" @click="deleteJobConfirm(row)" ghost></Button>
      </template>
    </Table>
    <div style="margin: 10px; overflow: hidden" v-if="jobList.length >= 1">
      <div style="float: right;">
        <Page :total="total" :current="filter.pageNumber" :page-size="filter.pageSize" :page-size-opts="pageSizeOpts"
              show-sizer show-total @on-change="changePageNumber" @on-page-size-change="changePageSize"/>
      </div>
    </div>
  </div>
</template>

<script>
import { Bus } from '@/global/bus'
import moment from 'moment-timezone'
export default {
  name: 'CompatibilityJobInfo',
  data() {
    return {
      loading: false,
      filter: {
        pageSize: 15,
        pageNumber: 1,
        business: null,
        onlyMe: false,
        jobName: '',
        dateRange: [],
        end: null,
        start: null,
        currentUser: Bus.userInfo.userLogin
      },
      businessList: [],
      jobList: [],
      total: 0,
      jobTagColors: {},
      jobTableColumns: [
        { title: 'jobId', key: 'jobId', width: 90, fixed: 'left' },
        { title: 'App', slot: 'category', width: 130, fixed: 'left' },
        { title: '平台', slot: 'plat', width: 80 },
        { title: '方向', key: 'business', width: 100 },
        { title: '类型', key: 'jobType', width: 120, align: 'center' },
        { title: '提交时间', key: 'submitTime', width: 110 },
        { title: '结束时间', key: 'endTime', width: 110 },
        { title: '平均时间', key: 'duration', width: 100 },
        { title: '来源', slot: 'job_source', width: 120 },
        { title: '任务标题', slot: 'job_trace', width: 150 },
        { title: '版本', key: 'appVersion', width: 110, sortable: true },
        { title: 'user', key: 'user', width: 135, ellipsis: true, tooltip: true },
        { title: '状态', slot: 'taskStatus', width: 110, fixed: 'right' },
        { title: '进度', slot: 'progress', width: 110, fixed: 'right' },
        { title: '操作', slot: 'action', width: 170, fixed: 'right' }
      ],
      pageSizeOpts: [15, 50, 100],
      dateSelectOptions: {
        shortcuts: [
          { text: '1 week', value() { let end = new Date(); let start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 7); return [start, end] } },
          { text: '1 month', value() { let end = new Date(); let start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); return [start, end] } },
          { text: '3 months', value() { let end = new Date(); let start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 90); return [start, end] } }
        ]
      }
    }
  },
  mounted() {
    this.refreshJobList()
    this.getBusinessList()
  },
  methods: {
    refreshJobList() {
      if (this.filter.dateRange[0] && this.filter.dateRange[1] && !isNaN(new Date(this.filter.dateRange[1]).getTime())) {
        let endCopy = moment(this.filter.dateRange[1]).tz('Asia/Shanghai').endOf('day')
        this.filter.end = endCopy.format('YYYY-MM-DD HH:mm:ss')
        this.filter.start = moment(this.filter.dateRange[0]).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.filter.start = null
        this.filter.end = null
      }
      this.getJobList()
      this.filter.pageNumber = 1
    },
    changePageNumber(number) {
      this.filter.pageNumber = number
      this.getJobList()
    },
    changePageSize(pageSize) {
      this.filter.pageSize = pageSize
      this.getJobList()
    },
    getJobList() {
      this.loading = true
      let params = {
        pageSize: this.filter.pageSize,
        pageNumber: this.filter.pageNumber,
        business: this.filter.business,
        onlyMe: this.filter.onlyMe,
        currentUser: this.filter.currentUser,
        start: this.filter.start,
        end: this.filter.end
      }
      this.$axios({
        method: 'get',
        params: params,
        url: this.env.url + 'client/compatibility/jobListPage'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.jobList = message.data.list
          this.total = message.data.total
          this.loading = false
        } else {
          this.$Notice.error({
            title: '获取Job列表失败！'
          })
        }
      }).catch((error) => {
        console.log(error)
        this.loading = false
      })
    },
    getBusinessList() {
      if (this.businessList.length === 0) {
        this.$axios({
          method: 'get',
          url: this.env.url + 'package/getBusinessList'
        }).then((res) => {
          let message = res.data
          this.businessList = message.map(item => ({
            value: item.id,
            label: item.business
          }))
        }).catch((error) => {
          console.log(error)
        })
      }
    },
    getStatusColor(status) {
      let color = 'success'
      if (status === '2') { color = 'warning' }
      if (status === '3') { color = 'processing' }
      if (status === '0') { color = 'error' }
      if (status === '4') { color = 'default' }
      return color
    },
    getJobProgress(row) {
      let percent = Math.round(row.jobCompleteSchemeCount / row.allSchemaCount * 100)
      if (percent > 100) {
        percent = 100
      }
      return percent
    },
    getProgressColor(row) {
      let color = 'active'
      if ((row.taskStatus.indexOf('2') < 0 && row.taskStatus.indexOf('3') < 0 && row.taskStatus.indexOf('4') < 0) || this.getJobProgress(row) === 100) {
        color = 'success'
      }
      return color
    },
    rerunJobConfirm(row) {
      this.$Modal.confirm({
        title: '任务重跑确认',
        content: '确定重跑第' + row.jobId + '条任务吗？',
        onOk: () => {
          this.$axios({
            method: 'post',
            url: this.env.url + 'client/compatibility/jobRerun/' + row.jobId
          }).then((res) => {
            let message = res.data
            if (message.code === 0) {
              this.$Notice.success({
                title: '第' + row.jobId + '号Job下的所有Task已进入重跑状态'
              })
              this.refreshJobList()
            } else {
              console.log(message)
              this.$Notice.error({
                title: '第' + row.jobId + '号Job重跑失败，错误码：' + message.code.toString()
              })
            }
          }).catch(function (error) {
            console.log(error)
            this.$Notice.error({
              title: '第' + row.jobId + '号Job重跑失败'
            })
          })
        }
      })
    },
    deleteJobConfirm(row) {
      this.$Modal.confirm({
        title: '任务删除确认',
        content: '确定删除第' + row.jobId + '条任务吗？',
        onOk: () => {
          this.$axios({
            method: 'delete',
            url: this.env.url + 'client/compatibility/jobInfo/' + row.jobId
          }).then((res) => {
            let message = res.data
            if (message.code === 0) {
              this.$Notice.success({
                title: '第' + row.jobId + '号Job已成功删除'
              })
              this.refreshJobList()
            } else {
              console.log(message)
              this.$Notice.error({
                title: '第' + row.jobId + '号Job删除失败，错误码：' + message.code.toString()
              })
            }
          }).catch(function (error) {
            console.log(error)
            this.$Notice.error({
              title: '第' + row.jobId + '号Job删除失败'
            })
          })
        }
      })
    },
    getActionButtonShow(row) {
      let show = row.jobCompleteSchemeCount + '/' + row.allSchemaCount
      if (row.taskStatus.includes('2') || row.taskStatus.includes('3')) { show = 'View' }
      return show
    },
    getActionButtonColor(row) {
      let color = row.errorSchemaCount > 0 ? 'info' : 'success'
      if (row.taskStatus.includes('2')) { color = 'warning' }
      if (row.taskStatus.includes('3')) { color = 'primary' }
      return color
    },
    getTask(jobId) {
      let routeData = this.$router.resolve({ path: '/microscope/jobInfo', query: { jobId: jobId } })
      window.open(routeData.href, '_blank')
    },
    getSourceTagColor(sourceType) {
      return this.jobTagColors[sourceType]
    },
    getSourceShow(sourceType, appVersion) {
      let sourceTypeShow = sourceType
      if (appVersion === undefined) {
        appVersion = 'unknown'
      }
      if (sourceType === 'regressionTest') {
        sourceTypeShow = '回归-' + appVersion
      } else if (sourceType === 'gatedLaunch') {
        sourceTypeShow = '发布-' + appVersion
      } else if (sourceType === 'periodically') {
        sourceTypeShow = '定时任务'
      } else if (sourceType === 'Microscope') {
        sourceTypeShow = '手动触发'
      }
      return sourceTypeShow
    },
    getTraceInfoShow(row) {
      let traceInfoShow = ''
      if (row.sourceType === '新需求') {
        traceInfoShow = 'ones'
      } else if (row.hasOwnProperty('traceInfo')) {
        traceInfoShow = row.traceInfo.title
      }
      if (traceInfoShow.length > 16) {
        traceInfoShow = traceInfoShow.substring(0, 16) + '...'
      }
      return traceInfoShow
    },
    getAppIcon(category) {
      switch (category) {
        case '美团':
          return '/static/img/meituan_logo.png'
        case '点评':
          return '/static/img/dianping_logo.png'
        case '微信':
          return '/static/img/weixin_logo.png'
        case '快手':
          return '/static/img/kuaishou_logo.png'
        case '百度地图':
          return '/static/img/baidumap_logo.png'
        case '开店宝':
          return '/static/img/kaidianbao_logo.png'
        case '优选':
        case '美团优选':
          return '/static/img/youxuan_logo.png'
        case '买菜':
        case '美团买菜':
          return '/static/img/maicai_logo.png'
        case '商家':
        case '美团商家':
        case '美团酒店商家':
        case '酒店商家':
          return '/static/img/jiudianshangjia_logo.png'
        default:
          return '/static/img/app_logo.png'
      }
    },
    getPlatIcon(plat) {
      if (plat.toUpperCase() === 'ANDROID') {
        return '/static/img/android.png'
      } else if (plat.toUpperCase() === 'HARMONY') {
        return '/static/img/harmony.png'
      } else if (plat.toUpperCase() === 'WEB') {
        return '/static/img/web.png'
      } else {
        return '/static/img/ios.png'
      }
    },
    jobShowFilterDebounce() {
      this.debounce('refreshJobList', 500)
    },
    debounce(fnName, time) {
      let timeout = null
      if (timeout) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        this[fnName]()
      }, time)
    },
    columnFilterChange() {
      this.filter.pageSize = 100
    },

    // 判断重跑按钮是否禁用
    getJobRerunDisabled(row) {
      return !(row.user.includes(this.filter.currentUser) && !row.taskStatus.includes('2') && !row.taskStatus.includes('3'))
    },

    // 判断删除按钮是否禁用
    getJobDeleteDisabled(row) {
      const adminList = ['jinhailiang', 'yuanrenna', 'longhui05'] // 从第一个代码中引入的管理员列表
      if (row.taskStatus.includes('3')) {
        return true
      }
      if (adminList.includes(this.filter.currentUser)) {
        return false
      }
      return !row.user.includes(this.filter.currentUser)
    }
  }
}
</script>

<style scoped>
  .layout-logo {
    height: 35px;
    width: 28px;
  }
</style>
