<template>
  <div style="padding-bottom: 28px;padding-top: 10px">
    <Row>
      <Col :md="12">
        <div style="padding-bottom: 10px">
          <div class="content-title-container">
            <p class="content-title">基准图配置统计</p>
          </div>
        </div>
        <Row>
            <Col :md="6" v-for="item in basePicData" :key="item.name">
              <span>{{item.name}}:</span>
              <span class="content-text">
                 {{item.y}}
              </span>
            </Col>
          </Row>
      </Col>
      <Col :md="12">
        <div style="padding-bottom: 10px">
          <div class="content-title-container">
            <p class="content-title">图像对比统计</p>
          </div>
        </div>
        <Row>
            <Col :md="6" v-for="item in statusResultDistribInfo" :key="item.name">
              <span>{{item.name}}:</span>
              <span class="content-text">
                {{item.y}}
              </span>
            </Col>
          </Row>
      </Col>
    </Row>
  </div>
</template>


<script>
    export default {
      name: 'ClientJobImageInfo',
      props: ['basePicData', 'statusResultDistribInfo']
    }
</script>

<style scoped>
  .content-title{
    text-align: left;
    font-size: small;
    font-weight: bold;
    padding-left: 16px;
  }
  .content-title-container{
    border-left: 3px solid #42b983
  }
  .content-text{
    font-size: 15px;
    font-weight: bold;
    color: #2b85e4;
  }
</style>
