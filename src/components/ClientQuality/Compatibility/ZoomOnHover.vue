<template>
    <div class="zoom-on-hover" v-bind:class="{zoomed}" @touchstart="touchzoom"
    @mousemove="move" @mouseenter="zoom" @mouseleave="unzoom">
        <img class="normal" ref="normal" :src="imgPath"/>
        <img class="zoom" ref="zoom" :src="imgZoom || imgPath"/>
    </div>
</template>

<script>

export default {
  name: 'ZoomOnHover',
  props: ['imgNormal', 'imgZoom', 'scale', 'disabled', 'allZoomed', 'zoomRelativePos'],
  data: function() {
    return {
      scaleFactor: 1,
      zoomed: this.allZoomed,
      resizeCheckInterval: null,
      imageUrl: 'https://msstest-img.sankuai.com/v1/mss_29bc475beb7e4563a9a6f802f29acd83/compatibility/'
    }
  },
  watch: {
    zoomRelativePos: {
      deep: true,
      handler(newVal, oldVal) {
        if (this.disabled || !this.zoomed) return 0
        var relativeX = newVal.x
        var relativeY = newVal.y
        var zoom = this.$refs.zoom
        var normal = this.$refs.normal
        var normalFactorX = relativeX / normal.offsetWidth
        var normalFactorY = relativeY / normal.offsetHeight
        var x = normalFactorX * (zoom.offsetWidth * this.scaleFactor - normal.offsetWidth)
        var y = normalFactorY * (zoom.offsetHeight * this.scaleFactor - normal.offsetHeight)
        zoom.style.left = -x + 'px'
        zoom.style.top = -y + 'px'
      }
    },
    allZoomed: {
      handler(newVal, oldVal) {
        this.zoomed = newVal
      }
    }
  },
  computed: {
    imgPath: function() {
      if (typeof (this.imgNormal) !== 'undefined' && !this.imgNormal.startsWith('http')) {
        return this.imageUrl + this.imgNormal
      } else {
        return this.imgNormal
      }
    }
  },
  methods: {
    touchzoom: function(event) {
      if (this.disabled) return
      this.move(event)
      this.zoomed = !this.zoomed
      this.$emit('zoomedChange', this.zoomed)
    },
    zoom: function() {
      if (!this.disabled) this.zoomed = true
      this.$emit('zoomedChange', this.zoomed)
    },
    unzoom: function() {
      if (!this.disabled) this.zoomed = false
      this.$emit('zoomedChange', this.zoomed)
    },
    pageOffset: function(el) {
      var rect = el.getBoundingClientRect()
      var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop
      return {
        y: rect.top + scrollTop,
        x: rect.left + scrollLeft
      }
    },
    move: function(event) {
      if (this.disabled || !this.zoomed) return
      var offset = this.pageOffset(this.$el)
      var relativeX = event.clientX - offset.x + window.pageXOffset
      var relativeY = event.clientY - offset.y + window.pageYOffset
      this.$emit('zoomOnMove', {x: relativeX, y: relativeY})
    },
    initEventLoaded: function() {
      var promises = [this.$refs.zoom, this.$refs.normal].map(function(image) {
        return new Promise(function(resolve, reject) {
          image.addEventListener('load', resolve)
          image.addEventListener('error', reject)
        })
      })
      var component = this
      Promise.all(promises).then(function() {
        component.$emit('loaded')
      })
    },
    initEventResized: function() {
      var normal = this.$refs.normal
      var previousWidth = normal.offsetWidth
      var previousHeight = normal.offsetHeight
      var component = this
      this.resizeCheckInterval = setInterval(function() {
        if ((previousWidth !== normal.offsetWidth) || (previousHeight !== normal.offsetHeight)) {
          previousWidth = normal.offsetWidth
          previousHeight = normal.offsetHeight
          component.$emit('resized', {
            width: normal.width,
            height: normal.height,
            fullWidth: normal.naturalWidth,
            fullHeight: normal.naturalHeight
          })
        }
      }, 1000)
    }
  },
  mounted: function() {
    if (this.$props.scale) {
      this.scaleFactor = parseInt(this.$props.scale)
      this.$refs.zoom.style.transform = 'scale(' + this.scaleFactor + ')'
    }
    this.initEventLoaded()
    this.initEventResized()
  },
  updated: function() {
    this.initEventLoaded()
  },
  beforeDestroy: function() {
    this.resizeCheckInterval && clearInterval(this.resizeCheckInterval)
  }
}
</script>

<style scoped>
.zoom-on-hover {
  position: relative;
  overflow: hidden;
  cursor: zoom-in;
}
.zoom-on-hover .normal {
  width: 100%;
}
.zoom-on-hover .zoom {
  position: absolute;
  opacity: 0;
  transform-origin: top left;
}
.zoom-on-hover.zoomed .zoom {
  opacity: 1;
}
.zoom-on-hover.zoomed .normal {
  opacity: 0;
}
</style>
