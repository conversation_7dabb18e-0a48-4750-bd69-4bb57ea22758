<template>
    <div>
      <Row>
        <Col :md="2" v-show="row.cstBugList.length > 0">兼容性平台:</Col>
        <Col :md="3" v-for="bugKey in row.cstBugList" :key="bugKey.bug_key">
          <a :href="getBugTask(bugKey)" target="_blank">{{bugKey.bug_key}}</a>
        </Col>
      </Row>
      <Row style="padding-top: 5px">
        <Col :md="2" v-show="row.compBugList.length > 0">手工发现:</Col>
        <Col :md="3" v-for="bugKey in row.compBugList" :key="bugKey.bug_key">
          <a :href="getBugTask(bugKey)" target="_blank">{{bugKey.bug_key}}</a>
        </Col>
      </Row>
      <Row style="padding-top: 5px">
        <Col :md="2">机型:</Col>
        <Col :md="20">
          {{row.phoneList}}
        </Col>
      </Row>
    </div>
</template>

<script>
    export default {
      name: 'ClientTableBug',
      props: {
        row: Object
      },
      methods: {
        getBugTask (bugKey) {
          return 'https://flow.sankuai.com/browse/' + bugKey.bug_key
        }
      }
    }
</script>

<style scoped>

</style>
