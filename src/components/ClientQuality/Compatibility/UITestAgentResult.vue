<template>
  <div>
    <div class="filter-container">
      <label>选择场景</label>
      <div>
        <input type="checkbox" id="selectAll" v-model="isSelectAll" @change="toggleSelectAll">
        <label class="selectAllInput" for="selectAll">全选</label>
      </div>
      <div v-for="(sceneResult, key) in sceneGroupResult" :key="key" class="filter-item">
        <input type="checkbox" :id="key" :value="key" v-model="selectedScenes">
        <label :for="key">{{ key }}</label>
        <button :class="{'scenePass': checkIfPass(sceneResult), 'sceneFail': !checkIfPass(sceneResult)}">
          {{ checkIfPass(sceneResult) ? '通过' : '不通过' }}
        </button>
      </div>
    </div>

    <div v-for="(sceneResult, key) in filteredSceneGroupResult" :key="key" class="scene-group">
      <div class="bg-frame scene-frame">
        <div class="title">
          <span>场景名称：{{ key }}</span>
        </div>
        <div class="functionButton">
          <Button icon="ios-cog" size="small" type="primary" @click="openMMCDPageLink(sceneResult)" ghost>页面配置</Button>
        </div>
        <div v-for="result in sceneResult" :key="result.id" class="result-container">
          <div class="bg-frame result-frame">
            <div class="main-content">
              <div class="center-box">
                <div class="result-box">
                  <div class="result-item">
                    <span class="label">case名称</span>
                    <span class="value">{{ result.caseResult.scheme_case_name }}</span>
                  </div>
                  <hr>
                  <div class="result-item">
                    <span class="label">case详情</span>
                    <span class="value scroll-box">{{ result.caseResult.scheme_case_info }}</span>
                  </div>
                  <hr>
                  <div class="result-item">
                    <span class="label">操作次数</span>
                    <span class="value">{{ result.caseResult.process_action_info.length }}</span>
                  </div>
                  <hr>
                  <div class="result-item">
                    <span class="label">执行结果</span>
                    <button :class="{'pass': result.caseResult.agent_result.pass, 'fail': !result.caseResult.agent_result.pass}">
                      {{ result.caseResult.agent_result.pass ? '通过' : '不通过' }}
                    </button>
                  </div>
                  <hr>
                  <div class="result-item">
                    <span class="label">结果详情</span>
                    <span class="value scroll-box">{{ result.caseResult.agent_result.reason }}</span>
                  </div>
                  <hr>
                  <div class="result-item">
                    <span class="label">执行模式</span>
                    <div class="trace-info">
                      <span class="value">
                        {{ (result.caseResult.replay_info && result.caseResult.replay_info.use_template) ? '基于历史 Trace 回放' : '常规执行' }}
                      </span>
                      <div class="trace-button-group">
                        <template v-if="result.caseResult.replay_info && result.caseResult.replay_info.use_template">
                          <Button
                            size="small"
                            type="warning"
                            @click="showCancelConfirm(result)"
                            class="trace-button"
                          >
                            取消历史 Trace
                          </Button>
                        </template>
                        <template v-else>
                          <Button
                            size="small"
                            type="success"
                            @click="showSetAsTraceConfirm(result)"
                            class="trace-button"
                          >
                            设为 Trace 模板
                          </Button>
                          <Button
                            size="small"
                            type="warning"
                            @click="showCancelConfirm(result)"
                            class="trace-button"
                          >
                            取消 Trace 模板
                          </Button>
                        </template>
                        <a href="https://km.sankuai.com/collabpage/2709076691" target="_blank" class="help-icon">?</a>
                      </div>
                    </div>
                  </div>
                </div>
                <Button icon="ios-cog" size="small" type="primary" class="case-lyrebird" @click="openCaseLyrebirdESLink(result)" ghost>lyrebird ES上报</Button>
              </div>
              <div class="screenshot-container">
                <div class="screenshot-wrapper">
                  <div v-for="(group, index) in result.screenshot" :key="index + result.id" class="screenshot-group">
                    <div class="description-and-button">
                      <div class="screenshot-description">
                        <hr>
                        <div>交互{{ index }}</div>
                        <hr>
                        <div class="interaction-info">{{ group.description }}</div>
                        <hr>
                      </div>
                    </div>
                    <div v-for="(screenshot, i) in group.screenshots" :key="i + '-' + result.id" class="screenshot-item">
                      <a :href="screenshot" target="_blank">
                        <img :src="screenshot" alt="Screenshot" class="screenshot-image" />
                      </a>
                      <div v-if="i === 0">交互前</div>
                      <div v-else-if="i === 1">交互后</div>
                    </div>
                    <div v-if="index < screenshotGroups.length - 1" class="arrow">
                      ➜
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="separator"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AUITestAgentResult',
  props: ['sceneResultList'],
  components: {},
  data() {
    return {
      caseName: '',
      caseDetail: '',
      operationCount: 0,
      executionTime: '',
      executionResultLabel: '',
      executionResult: '',
      esUrl: this.env.es_url + '/es_service/search',
      esUpdateUrl: this.env.es_url + '/es_service/update',
      esTestUrl: this.env.es_test_url + '/es_service/search',
      esTestUpdateUrl: this.env.es_test_url + '/es_service/update',
      screenshotGroups: [],
      es_info: {},
      sceneGroupResult: {},
      selectedScenes: [],
      isSelectAll: true,
      debounceTimer: null
    }
  },
  computed: {
    filteredSceneGroupResult() {
      if (!this.selectedScenes.length) {
        return {}
      } else {
        let filteredResult = {}
        this.selectedScenes.forEach(scene => {
          if (this.sceneGroupResult[scene]) {
            filteredResult[scene] = this.sceneGroupResult[scene]
          }
        })
        return filteredResult
      }
    }
  },
  mounted() {
    this.debounceSplitGroupOfScene()
  },
  watch: {
    sceneResultList: {
      handler(newVal, oldVal) {
        this.sceneGroupResult = {}
        this.screenshotGroups = []
        this.debounceSplitGroupOfScene()
      },
      deep: true
    }
  },
  methods: {
    async getResultsFromES(caseInfo) {
      const requestPayload = {
        'accesskey': 'Y29tLnNhbmt1YWkuYWllbmdpbmVlcmluZy5sbG1hcHAuZXM',
        'index': 'mtrekker',
        'query': {
          'query': {
            'bool': {
              'must': [
                { 'term': { 'task_id': String(caseInfo.taskId) } },
                { 'term': { 'scheme_index': String(caseInfo.index) } }
              ],
              'must_not': [],
              'should': [],
              'filter': []
            }
          },
          'from': 0,
          'size': '100',
          'sort': [],
          'profile': true
        }
      }
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Authorization': 'Basic Y29tLnNhbmt1YWkudGVzdGFiaWxpdHkubGxtOkJEMkQyM0IyREJDMTVDNzc3NTZFRkMzM0EyNjVCNTMz'
      }
      try {
        const res = await this.$axios({
          method: 'post',
          url: this.esUrl,
          data: JSON.stringify(requestPayload),
          headers: requestHeaders
        })
        if (res.data && res.data.hits && res.data.hits.hits && res.data.hits.hits[0] && res.data.hits.hits[0]._source) {
          // 标记来源
          res.data.hits.hits[0]._source._esSourceType = 'prod'
          return res.data.hits.hits[0]._source
        }
      } catch (error) {
        console.warn(`Request to ${this.esUrl} failed. Retrying with ${this.esTestUrl}. Error:`, error)
      }
      try {
        const resFallback = await this.$axios({
          method: 'post',
          url: this.esTestUrl, // 使用备用 URL
          data: JSON.stringify(requestPayload),
          headers: requestHeaders
        })
        if (resFallback.data && resFallback.data.hits && resFallback.data.hits.hits && resFallback.data.hits.hits[0] && resFallback.data.hits.hits[0]._source) {
          // 标记来源
          resFallback.data.hits.hits[0]._source._esSourceType = 'test'
          return resFallback.data.hits.hits[0]._source
        }
      } catch (fallbackError) {
        console.error(`Request to ${this.esUpdateUrl} also failed. Error:`, fallbackError)
        // 如果两次尝试都失败，则行为与原始的单个 catch 块一致（记录错误，方法将隐式返回 undefined）
      }
    },
    async splitGroupOfScene() {
      let caseResultList = []

      // 创建一个包含所有请求的Promise数组
      let allRequests = this.sceneResultList.map(sceneResult => this.getResultsFromES(sceneResult))

      // 等待所有请求完成
      try {
        let allResults = await Promise.all(allRequests)

        // 处理所有请求结果
        allResults.forEach((caseDetail, index) => {
          if (caseDetail) { // 检查请求是否成功返回数据
            let screenshotGroups = caseDetail.process_action_info.map(info => {
              return {
                description: info.action_info,
                screenshots: [
                  info.action_image,
                  info.result_image
                ]
              }
            })
            let singleCaseDetail = {
              caseResult: caseDetail,
              screenshot: screenshotGroups,
              pageInfoId: this.sceneResultList[index].pageInfoId,
              mockId: this.sceneResultList[index].mockId,
              jobId: this.sceneResultList[index].jobId,
              id: this.sceneResultList[index].id
            }
            caseResultList.push(singleCaseDetail)
          }
        })

        // 分组操作
        caseResultList.forEach(item => {
          const sceneName = (item.caseResult.autotest_info && item.caseResult.autotest_info.scene_name) || item.caseResult.scene_name
          if (!this.sceneGroupResult.hasOwnProperty(sceneName)) {
            this.$set(this.sceneGroupResult, sceneName, [item])
          } else {
            this.sceneGroupResult[sceneName].push(item)
          }
        })
        this.toggleSelectAll()
      } catch (error) {
        console.error('Error in processing requests:', error)
      }
    },
    debounceSplitGroupOfScene() {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = setTimeout(() => {
        this.splitGroupOfScene()
      }, 300)
    },
    toggleSelectAll() {
      if (this.isSelectAll) {
        this.selectedScenes = Object.keys(this.sceneGroupResult)
      } else {
        this.selectedScenes = []
      }
    },
    updateSelectAllStatus() {
      // Check if all scenes are selected
      this.isSelectAll = this.selectedScenes.length === Object.keys(this.sceneGroupResult).length
    },
    checkIfPass(sceneResults) {
      let flag = false
      for (let result of sceneResults) {
        if (result.caseResult.agent_result.pass) {
          flag = true
          break
        }
      }
      return flag
    },
    openMMCDPageLink(result) {
      let pageInfoId = result[0].pageInfoId
      this.$axios({
        method: 'get',
        params: {
          'pageId': pageInfoId
        },
        url: this.env.url + 'clientPage/getPageInfoById'
      }).then((res) => {
        let configId = -1
        let parts = result[0].mockId.split('_')
        if (parts.length > 1) {
          configId = parts[1]
        }
        let routeData = null
        if ('scene_id' in res.data) {
          routeData = this.$router.resolve(
            {
              path: '/client/scene/' + res.data.scene_id + '/inspect',
              query: {
                configId: configId
              }
            }
          )
        } else {
          routeData = this.$router.resolve(
            {
              path: '/client/pageBoard',
              query: {
                pageId: pageInfoId,
                configId: configId
              }
            }
          )
        }
        window.open(routeData.href, '_blank')
      }).catch(function (error) {
        console.log(error)
      })
    },
    openCaseLyrebirdESLink(result) {
      let taskId = result.caseResult.task_id
      let mockId = result.mockId
      let lyrebirdEsUrl = `https://lyrebird.sankuai.com/app/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-90d,to:now))&_a=(columns:!(event.flow.request.url),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:channel,negate:!f,params:(query:flow),type:phrase),query:(match_phrase:(channel:flow))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:env.runner.caseId,negate:!f,params:(query:'${mockId}'),type:phrase),query:(match_phrase:(env.runner.caseId:'${mockId}'))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:env.runner.taskId,negate:!f,params:(query:'${taskId}'),type:phrase),query:(match_phrase:(env.runner.taskId:'${taskId}'))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:ba0f10f0-8d90-11ed-b7dd-43270782279d,key:event.flow.request.url,negate:!t,params:!('https://lyrebird.sankuai.com/api/report/testability'),type:phrases,value:'https://lyrebird.sankuai.com/api/report/testability'),query:(bool:(minimum_should_match:1,should:!((match_phrase:(event.flow.request.url:'https://lyrebird.sankuai.com/api/report/testability'))))))),index:ba0f10f0-8d90-11ed-b7dd-43270782279d,interval:auto,query:(language:kuery,query:''),sort:!())`
      window.open(lyrebirdEsUrl, '_blank')
    },
    openCaseAnnotation(result, index) {
      console.log(result['caseResult']['process_action_info'][index]['page_info'])
      let routeData = this.$router.resolve(
        {
          path: '/microscope/UIKnowledgeAnnotation',
          query: {
            pageInfo: result['caseResult']['process_action_info'][index]['page_info']
          }
        }
      )
      window.open(routeData.href, '_blank')
    },
    // 新增方法
    async cancelTraceReplay(result) {
      try {
        // 根据来源选择 update url
        const esSourceType = result.caseResult._esSourceType
        const updateUrl = esSourceType === 'prod' ? this.esUpdateUrl : this.esTestUpdateUrl
        const res = await this.$axios({
          method: 'post',
          url: updateUrl,
          data: {
            'index': 'knowledge_trace_data',
            'id': result.caseResult.replay_info._id,
            'body': {
              'doc': {
                'usable': false
              }
            }
          },
          headers: {
            'Content-Type': 'application/json'
          }
        })
        if (res.data && (res.data.result === 'updated' || res.data.result === 'noop')) {
          this.$Message.success('已取消使用历史 Trace')
        } else {
          this.$Message.error('取消失败，请重试')
        }
      } catch (error) {
        console.error('取消 Trace 失败:', error)
        this.$Message.error('操作失败：' + error.message)
      }
    },
    async setAsTraceKnowledge(result) {
      try {
        // 根据来源选择 update url
        const esSourceType = result.caseResult._esSourceType
        const updateUrl = esSourceType === 'prod' ? this.esUpdateUrl : this.esTestUpdateUrl
        const res = await this.$axios({
          method: 'post',
          url: updateUrl,
          data: {
            'index': 'knowledge_trace_data',
            'id': result.caseResult.replay_info._id,
            'body': {
              'doc': {
                'usable': true
              }
            }
          },
          headers: {
            'Content-Type': 'application/json'
          }
        })
        if (res.data && (res.data.result === 'updated' || res.data.result === 'noop')) {
          this.$Message.success('已设置为 Trace 知识')
        } else {
          this.$Message.error('设置失败，请重试')
        }
      } catch (error) {
        console.error('设置 Trace 知识失败:', error)
        this.$Message.error('操作失败：' + error.message)
      }
    },
    // 新增确认弹窗方法
    showCancelConfirm(result) {
      this.$Modal.confirm({
        title: '确认取消',
        content: '确定要取消使用该历史 Trace 吗？取消后将不再基于此 Trace 进行回放。',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.cancelTraceReplay(result)
        }
      })
    },
    showSetAsTraceConfirm(result) {
      this.$Modal.confirm({
        title: '确认设置',
        content: '确定要将此记录设置为 Trace 知识吗？设置后将可以基于此 Trace 进行回放。',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.setAsTraceKnowledge(result)
        }
      })
    }
  }
}
</script>

<style scoped>
.title {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 10px;
}

.result-container {
  margin-bottom: 30px;
}

.filter-container {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  width: 600px;
  margin: 20px auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.selectAllInput {
  padding-left: 10px;
  padding-top: 10px;
}

.filter-container label {
  font-weight: bold;
  font-size: 16px;
  display: block;
  color: #333;
}

.filter-container > div {
  display: flex;
  align-items: center;
}

.filter-container .filter-item {
  padding: 5px 0;
}

.filter-container .filter-item:last-child {
  padding-bottom: 0;
}

.filter-container .filter-item input[type="checkbox"] {
  margin-right: 10px;
}

.filter-container label[for="selectAll"] {
  cursor: pointer;
}

.filter-container .filter-item label {
  cursor: pointer;
}

.filter-container input[type="checkbox"]:checked + label {
  font-weight: bold;
  color: #4caf50;
}

.bg-frame {
  border: 1px solid #ccc;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
}

.scene-frame {
  margin-bottom: 30px;
}

.result-frame {
  margin-bottom: 10px;
  border: 3px solid #9dbad8;
}

.main-content {
  display: flex;
  gap: 30px;
  align-items: center;
  margin-top: 20px;
}

.center-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 45vh;
  width: 33.33vw;
}

.description-and-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.result-box {
  border: 1px solid #464747;
  padding: 20px;
  width: 100%;
  text-align: left;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

.result-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

.scroll-box {
  max-height: 100px;
  overflow-y: scroll;
}

/* 自定义滚动条的样式 */
.scroll-box::-webkit-scrollbar {
  width: 8px;
}

.scroll-box::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

.label {
  background-color: #5c84b2;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: bold;
  font-size: 1.2em;
  margin-right: 10px;
  white-space: normal;
}

.value {
  flex: 1;
  text-align: left;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

hr {
  border: none;
  border-top: 1px solid #ececec;
  margin: 10px 0;
}

.screenshot-container {
  flex: 2;
  overflow-x: scroll;
  white-space: nowrap;
  padding: 20px;
  height: 100%;
  background-color: #f8f9fa;
  border: 1px solid #2e2e2e;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条的样式 */
.screenshot-container::-webkit-scrollbar {
  width: 8px;
}

.screenshot-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
}

.screenshot-wrapper {
  display: flex;
  align-items: center;
}

.screenshot-group {
  display: inline-flex;
  align-items: center;
  margin-right: 20px;
}

.screenshot-item {
  text-align: center;
  margin-right: 10px;
}

.screenshot-description {
  border: 2px solid #5c8585;
  border-radius: 10px;
  margin-bottom: 10px;
  margin-right: 10px;
  font-weight: bold;
  text-align: center;
  width: 120px;
  white-space: normal;
}

.screenshot-image {
  width: 210px;
  height: auto;
  border: 2px solid #ccc;
  border-radius: 4px;
}

.arrow {
  font-size: 2em;
  margin: 0 20px;
}

.functionButton {
  margin-bottom: 20px;
}

.pass {
  background-color: rgb(23, 114, 23);
  color: white;
  border: none;
  padding: 5px;
  border-radius: 3px;
}

.fail {
  background-color: rgb(205, 26, 26);
  color: white;
  border: none;
  padding: 5px;
  border-radius: 3px;
}

.scenePass {
  background-color: rgb(79, 170, 79);
  color: white;
  border: none;
  padding: 5px;
  border-radius: 5px;
  font-weight: bold;
  margin-bottom: 5px;
  margin-left: 10px;
}

.sceneFail {
  background-color: rgb(205, 26, 26);
  color: white;
  border: none;
  padding: 3px;
  border-radius: 5px;
  font-weight: bold;
  margin-bottom: 5px;
  margin-left: 10px;
}

.separator {
  height: 3px;
  background-color: #eb2121; /* 可以根据需要更改颜色 */
  margin: 20px 0;
  position: relative;
  overflow: visible;
  text-align: center;
}

.case-lyrebird {
  margin-top: 10px; /* 设置顶部外边距，给result-box内的内容留出空间 */
  align-self: center;
}

.trace-info {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  gap: 10px;
  flex: 1;
}

.trace-button-group {
  display: flex;  /* 新增按钮组容器 */
  gap: 10px;
  align-items: center;
}

.trace-button {
  margin-left: 0; /* 移除原来的 margin */
}

.help-icon {
  display: inline-flex; /* 改为 inline-flex 以获得更好的对齐效果 */
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #5c84b2;
  color: white;
  font-size: 12px;
  text-decoration: none;
  margin-top: 1px; /* 微调垂直对齐 */
}

.help-icon:hover {
  background: #4a6b94;
}
</style>