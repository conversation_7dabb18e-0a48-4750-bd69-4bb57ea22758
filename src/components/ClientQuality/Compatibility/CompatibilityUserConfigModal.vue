<template>
  <div>
    <Modal v-model="modalShow" :title="modalTitle" width="800" @on-cancel="exitJonConfigEditor">
      <Form :label-width="85" :rules="ruleValidate">
        <Form-item label="名称">
          <Row>
            <Col span="19">
              <Input v-model="jobName" clearable></Input>
            </Col>
            <Col span="10" v-if="jobConfigNew === true">
              <div style="color: orangered">
                <Icon type="ios-alert-outline" /> 请不要删除【MMCD】名称前缀
              </div>
            </Col>
          </Row>
        </Form-item>
        <Form-item label="业务信息">
          <Row v-if="jobConfigNew === false">
            <Col span="6">
              <Form-item prop="category">
                <Select
                  v-model="category"
                  filterable
                  clearable
                  placeholder="请选择平台"
                  @on-change="getPage"
                >
                  <Option
                    v-for="appName in groupAppNames"
                    :key="appName.value"
                    :value="appName.value"
                    >{{ appName.label }}</Option
                  >
                </Select>
              </Form-item>
            </Col>
            <Col span="2" style="text-align: center">-</Col>
            <Col span="7">
              <Cascader
                :data="groupData"
                trigger="hover"
                v-model="groupInfo"
                placeholder="请选择页面属性"
                @on-change="getPage"
              ></Cascader>
            </Col>
            <Col span="2" style="text-align: center">-</Col>
            <Col span="7">
              <Form-item prop="jobType">
                <Select
                  v-model="jobType"
                  style="padding-right: 10px"
                  placeholder="请选择任务类型"
                  @on-change="getPage"
                >
                  <Option
                    v-for="item in jobTypeOption"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row v-else>
            <Col span="6">
              <Form-item prop="business">
                <Select
                  v-model="business"
                  filterable
                  clearable
                  placeholder="请选择业务线"
                  @on-change="getPageNew"
                >
                  <Option
                    v-for="businessInfo in businessListNew"
                    :key="businessInfo.business"
                    :value="businessInfo.business"
                    >{{ businessInfo.label }}</Option
                  >
                </Select>
              </Form-item>
            </Col>
            <Col span="2" style="text-align: center">-</Col>
            <Col span="8">
              <Form-item prop="jobGroupInfo">
                <Cascader
                  :data="jobGroupData"
                  trigger="hover"
                  v-model="jobGroupInfo"
                  placeholder="请选择产品信息"
                  @on-change="getPageNew"
                ></Cascader>
              </Form-item>
            </Col>
            <Col span="2" style="text-align: center">-</Col>
            <Col span="5">
              <Form-item prop="jobType">
                <Select
                  v-model="jobType"
                  style="padding-right: 10px"
                  placeholder="请选择任务类型"
                  @on-change="getPageNew"
                >
                  <Option
                    v-for="item in jobTypeOption"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </Form-item>
            </Col>
          </Row>
        </Form-item>
        <Form-item
          label="执行引擎"
          prop="runEngine"
          v-if="
            jobGroupInfo !== undefined &&
            jobGroupInfo.length > 0 &&
            jobGroupInfo[1] === 'H5'
          "
        >
          <Row>
            <Col span="5">
              <Badge text="beta" type="error">
                <RadioGroup v-model="runEngine" type="button">
                  <Radio v-for="en in engines" :label="en" :key="en"></Radio>
                </RadioGroup>
              </Badge>
            </Col>
            <Col span="5">
              <a href="https://km.sankuai.com/page/1143358435" target="_blank"
                ><Icon type="md-help-circle" /> 说明文档</a
              >
            </Col>
          </Row>
        </Form-item>
        <Form-item prop="appUrl" v-if="!isWeb()">
          <span slot="label">
            <span class="span-box">
              <span>安装包</span>
              <a href="https://km.sankuai.com/page/1286645363" target="_blank"
                ><Icon type="md-help-circle"
              /></a>
            </span>
          </span>
          <Row>
            <Col span="19">
              <Select
                v-model="appUrl"
                clearable
                multiple
                filterable
                @on-query-change="getAppsByKey"
                placeholder="支持多个安装包，右侧下拉选择，可切换成Android或iOS或鸿蒙安装包列表"
              >
                <Option
                  v-for="(item, index) in appList"
                  :value="item.value"
                  :key="index"
                  >{{ item.label }}</Option
                >
              </Select>
            </Col>
            <Col span="1" style="text-align: center">-</Col>
            <Col span="4">
              <Select v-model="appPlat" on-change="changePlatform()">
                  <Option v-for="item in ['Android', 'iOS', 'Harmony']" :value="item" :key="item">{{ item }}</Option>
              </Select>
            </Col>
          </Row>
        </Form-item>
        <Form-item label="基础配置">
          <Row>
            <Col span="4" v-if="!isWMP()">
              <Checkbox v-model="mrnEnv">MRN-Test</Checkbox>
            </Col>
            <Col span="4">
              <Checkbox v-model="mKey" :disabled="jobType == 'robust'"
                >使用Mock</Checkbox
              >
            </Col>
            <Col span="5">
              <template v-if="mKey">
                <Checkbox v-model="debugLink"> 使用DebugLink</Checkbox>
              </template>
            </Col>
            <Col v-if="isAndroid() && !isRobust()" span="3">
              <Checkbox v-model="detectShadow"
                >显影
                <span>
                  <a
                    href="https://km.sankuai.com/page/530910868"
                    target="_blank"
                  >
                    <Icon type="md-help-circle" />
                  </a>
                </span>
              </Checkbox>
            </Col>
            <Col v-if="!isWeb() && !isWMP() && !isRobust()" span="6">
              <Checkbox v-model="privacyMode"
                >开启隐私浏览模式
                <span>
                  <a
                    href="https://km.sankuai.com/page/1333451518#id-%E5%8F%AF%E6%8E%A7"
                    target="_blank"
                  >
                    <Icon type="md-help-circle" title="了解隐私浏览模式"
                  /></a>
                </span>
              </Checkbox>
            </Col>
          </Row>
          <Collapse style="width: 665px">
            <panel name="其他配置">
              其他配置
              <Form slot="content" :label-width="68">
                <template v-if="isWMP()">
                  <!--小程序二维码为开发版地址-->
                  <Form-item class="app_param" label="小程序">
                    <Input
                      prefix="ios-browsers"
                      v-model="appLogin"
                      clearable
                      placeholder="Talos小程序二维码路径"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item class="app_param" label="Key">
                    <Input
                      prefix="ios-analytics"
                      v-model="abKey"
                      clearable
                      placeholder="a:1,b:2"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item
                    v-if="jobType === 'traversal'"
                    class="app_param"
                    label="schemes"
                  >
                    <vueJsonEditor
                      label="schemes"
                      v-model="taskSchemes"
                      style="
                        width: 600px;
                        height: 390;
                        padding-left: 70px;
                        padding-top: 10px;
                      "
                      :mode="'code'"
                      @json-change="onJsonChange"
                      @json-save="onJsonSave"
                      @has-error="onJsonError"
                    >
                    </vueJsonEditor>
                  </Form-item>
                </template>
                <template v-else-if="category === '优选'">
                  <div style="padding-bottom: 10px">
                    <vue-json-editor
                      v-model="configJson"
                      :mode="'code'"
                    ></vue-json-editor>
                  </div>
                </template>
                <template v-else-if="jobType === 'traversal'">
                  <Form-item class="app_param" label="需求">
                    <Input
                      prefix="md-code-working"
                      v-model="taskUrl"
                      clearable
                      placeholder="请输入需求task"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item class="app_param" label="Key">
                    <Input
                      prefix="ios-analytics"
                      v-model="abKey"
                      clearable
                      placeholder="a:1,b:2"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item class="app_param" label="登录信息">
                    <Input
                      v-if="isWeb()"
                      prefix="md-person"
                      v-model="appLogin"
                      clearable
                      placeholder="如需Cookie登录请填写：登录用户名/用户密码/cookie-key/cookie-domain"
                      style="width: 542px"
                    />
                    <Input
                      v-else
                      prefix="md-person"
                      v-model="appLogin"
                      clearable
                      placeholder="App登录用户名/用户密码"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="SSO登录">
                    <Input
                      prefix="md-person"
                      v-model="ssoLoginUser"
                      clearable
                      placeholder="如需sso免密登录请填写：misID"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="前端泳道">
                    <Input
                      prefix="ios-options"
                      v-model.trim="clientSwimlane"
                      clearable
                      placeholder="Client swimlane"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="后端泳道">
                    <Input
                      prefix="ios-options"
                      v-model.trim="serverSwimlane"
                      clearable
                      placeholder="Server swimlane"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item
                    class="app_param"
                    v-for="(item, index) in mrnBundle"
                    :key="index"
                    :label="'MRN包' + (index + 1)"
                  >
                    <Row>
                      <Col span="10">
                        <Form-item prop="bundle_name">
                          <Select
                            v-if="jobConfigNew === false"
                            v-model="item.bundle_name"
                            clearable
                            filterable
                            @on-query-change="searchMRNBundle"
                          >
                            <Option
                              v-for="item in bundleList"
                              :value="item.value"
                              :key="item.value"
                              >{{ item.label }}</Option
                            >
                          </Select>
                          <Select
                            v-else
                            v-model="item.bundle_name"
                            clearable
                            filterable
                            @on-query-change="searchMRNBundle"
                          >
                            <Option
                              v-for="mrnItem in mrnListNew"
                              :value="mrnItem.name"
                              :key="mrnItem.name"
                              >{{ mrnItem.name }}</Option
                            >
                          </Select>
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="3">
                        <Form-item prop="version">
                          <Input
                            v-model="item.version"
                            clearable
                            placeholder="版本"
                          />
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="3">
                        <Form-item prop="env">
                          <Select
                            v-model="item.env"
                            clearable
                            placeholder="环境"
                          >
                            <Option
                              v-for="(item, index) in bundleEnvList"
                              :value="item.value"
                              :key="index"
                              >{{ item.label }}</Option
                            >
                          </Select>
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="2">
                        <template v-if="mrnBundle.length > 1">
                          <Button
                            type="primary"
                            size="small"
                            @click="handleRemove(index)"
                            >删除</Button
                          >
                        </template>
                        <template v-if="mrnBundle.length === 1">
                          <Button
                            type="primary"
                            size="small"
                            @click="handleClear"
                            >清空</Button
                          >
                        </template>
                      </Col>
                    </Row>
                  </Form-item>
                  <Form-item class="app_param">
                    <Row>
                      <Col span="6" offset="3">
                        <Button
                          type="dashed"
                          long
                          @click="handleAdd"
                          icon="md-add"
                          >新增MRN包信息</Button
                        >
                      </Col>
                    </Row>
                  </Form-item>
                  <Form-item class="app_param" label="schemes">
                    <vueJsonEditor
                      label="schemes"
                      v-model="taskSchemes"
                      style="
                        width: 600px;
                        height: 390;
                        padding-left: 70px;
                        padding-top: 10px;
                      "
                      :mode="'code'"
                      @json-change="onJsonChange"
                      @json-save="onJsonSave"
                      @has-error="onJsonError"
                    >
                    </vueJsonEditor>
                  </Form-item>
                </template>
                <template v-else>
                  <Form-item class="app_param" label="需求">
                    <Input
                      prefix="md-code-working"
                      v-model="taskUrl"
                      clearable
                      placeholder="请输入需求task"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item class="app_param" label="Key">
                    <Input
                      prefix="ios-analytics"
                      v-model="abKey"
                      clearable
                      placeholder="a:1,b:2"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item class="app_param" label="登录信息">
                    <Input
                      v-if="isWeb()"
                      prefix="md-person"
                      v-model="appLogin"
                      clearable
                      placeholder="如需Cookie登录请填写：登录用户名/用户密码/cookie-key/cookie-domain"
                      style="width: 542px"
                    />
                    <Input
                      v-else
                      prefix="md-person"
                      v-model="appLogin"
                      clearable
                      placeholder="App登录用户名/用户密码"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="SSO登录">
                    <Input
                      prefix="md-person"
                      v-model="ssoLoginUser"
                      clearable
                      placeholder="如需sso免密登录请填写：misID"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="前端泳道">
                    <Input
                      prefix="ios-options"
                      v-model.trim="clientSwimlane"
                      clearable
                      placeholder="Client swimlane"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item v-if="isWeb()" class="app_param" label="后端泳道">
                    <Input
                      prefix="ios-options"
                      v-model.trim="serverSwimlane"
                      clearable
                      placeholder="Server swimlane"
                      style="width: 542px"
                    />
                  </Form-item>
                  <Form-item
                    class="app_param"
                    v-for="(item, index) in mrnBundle"
                    :key="index"
                    :label="'MRN包' + (index + 1)"
                  >
                    <Row>
                      <Col span="10">
                        <Form-item prop="bundle_name">
                          <Select
                            v-if="jobConfigNew === false"
                            v-model="item.bundle_name"
                            clearable
                            filterable
                            @on-query-change="searchMRNBundle"
                          >
                            <Option
                              v-for="item in bundleList"
                              :value="item.value"
                              :key="item.value"
                              >{{ item.label }}</Option
                            >
                          </Select>
                          <Select
                            v-else
                            v-model="item.bundle_name"
                            clearable
                            filterable
                            @on-query-change="searchMRNBundle"
                          >
                            <Option
                              v-for="mrnItem in mrnListNew"
                              :value="mrnItem.name"
                              :key="mrnItem.name"
                              >{{ mrnItem.name }}</Option
                            >
                          </Select>
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="3">
                        <Form-item prop="version">
                          <Input
                            v-model="item.version"
                            clearable
                            placeholder="版本"
                          />
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="3">
                        <Form-item prop="env">
                          <Select
                            v-model="item.env"
                            clearable
                            placeholder="环境"
                          >
                            <Option
                              v-for="(item, index) in bundleEnvList"
                              :value="item.value"
                              :key="index"
                              >{{ item.label }}</Option
                            >
                          </Select>
                        </Form-item>
                      </Col>
                      <Col span="1" style="text-align: center">-</Col>
                      <Col span="2">
                        <template v-if="mrnBundle.length > 1">
                          <Button
                            type="primary"
                            size="small"
                            @click="handleRemove(index)"
                            >删除</Button
                          >
                        </template>
                        <template v-if="mrnBundle.length === 1">
                          <Button
                            type="primary"
                            size="small"
                            @click="handleClear"
                            >清空</Button
                          >
                        </template>
                      </Col>
                    </Row>
                  </Form-item>
                  <Form-item class="app_param">
                    <Row>
                      <Col span="6" offset="3">
                        <Button
                          type="dashed"
                          long
                          @click="handleAdd"
                          icon="md-add"
                          >新增MRN包信息</Button
                        >
                      </Col>
                    </Row>
                  </Form-item>
                </template>
                <template v-if="this.jobGroupInfo[1] === 'MP'">
                  <Form-item class="app_param" label="MMP">
                    <Input
                      prefix="md-code-working"
                      v-model="mmp"
                      clearable
                      placeholder="targetPath@appId"
                      style="width: 542px"
                    />
                  </Form-item>
                </template>
                <Form-item class="app_param" label="Plugin">
                  <Row>
                    <Col span="20">
                      <Select
                        size="small"
                        v-model="visionComponentNameList"
                        style="width: 100%"
                        multiple
                        filterable
                        placeholder="请选择插件"
                      >
                        <Option
                          v-for="(item, index) in visionComponentOptionList"
                          :value="item.value"
                          :key="index"
                        >
                          {{ item.label }}
                        </Option>
                      </Select>
                    </Col>
                    <a
                      href="http://qa.sankuai.com/visionComponents"
                      target="_blank"
                    >
                      <Icon type="md-help-circle" />
                    </a>
                  </Row>
                </Form-item>
                <Form-item class="app_param" label="checker">
                  <Row>
                    <Col span="20">
                      <Select
                        size="small"
                        v-model="checkerNameList"
                        style="width: 100%"
                        multiple
                        filterable
                        @on-query-change="getCheckerName"
                        placeholder="请输入checker脚本名,使用前请看使用文档"
                      >
                        <Option
                          v-for="(item, index) in checkerOptionList"
                          :value="item.value"
                          :key="index"
                          >{{ item.label }}</Option
                        >
                      </Select>
                    </Col>
                    <a
                      href="https://km.sankuai.com/page/532754508"
                      target="_blank"
                    >
                      <Icon type="md-help-circle" />
                    </a>
                  </Row>
                </Form-item>
                <Form-item class="app_param" label="接口环境">
                  <Row>
                    <Col span="20">
                      <Select
                      size="small"
                      v-model="graytestSelectedItems"
                      clearable
                      multiple>
                        <Option v-for="(item, index) in graytestOptions" :value="item.type" :key="item.desc">
                          {{ item.desc }}
                        </Option>
                      </Select>
                    </Col>
                  </Row>

                </Form-item>

              </Form>
            </panel>
          </Collapse>
        </Form-item>
        
        <Form-item label="选择页面" prop="pages">
          <Row>
            <Col span="6">
              <div style="text-align: left; padding-bottom: 15px" class="top">
                <Poptip trigger="hover" placement="bottom">
                  <div slot="content">
                    先指定MRN包，点击按钮后手动增减页面信息
                  </div>
                  <Button
                    type="primary"
                    size="small"
                    shape="circle"
                    @click="getMRNPage"
                    ><Icon type="md-refresh" />自动选择MRN关联页面</Button
                  >
                </Poptip>
              </div>
            </Col>
            <Col span="13" v-if="jobConfigNew === true">
              <Select
                style="margin-left: 20px"
                v-model="priorityFilter"
                clearable
                multiple
                filterable
                @on-change="changePriorityFilter"
                placeholder="请选择页面优先级，默认选择所有"
              >
                <Option
                  v-for="(item, index) in priorityListNew"
                  :value="item"
                  :key="index"
                >
                  P{{ item }}
                </Option>
              </Select>
            </Col>
            <Col span="4" v-if="jobConfigNew === false">
              <div
                style="
                  text-align: right;
                  padding-bottom: 15px;
                  padding-right: 15px;
                "
                class="top"
              >
                根据页面组选择
              </div>
            </Col>
            <Col span="13" v-if="jobConfigNew === false">
              <Select
                v-model="selectedPageGroups"
                clearable
                multiple
                filterable
                @on-change="getPagesByGroupIDs"
                placeholder="支持多选"
              >
                <Option
                  v-for="pageG in pageGroups"
                  :value="pageG.id"
                  :key="pageG.id"
                  :label="pageG.group_name"
                >
                  <span>{{ pageG.group_name }}</span>
                  <span style="float: right; color: #ccc"
                    >{{ pageG.pages_id.split(",").length }}/{{
                      pageG.user
                    }}</span
                  >
                </Option>
              </Select>
            </Col>
          </Row>
        </Form-item>
        <div style="padding: 0 3px 0 3px">
          <Transfer
            :list-style="listStyle"
            :data="pages"
            :target-keys="pageKeys"
            filterable
            :render-format="renderPageSelectTip"
            :filter-method="filterMethod"
            @on-change="pageSelectChange"
          ></Transfer>
        </div>

        <template v-if="jobType == 'robust'">
          <Form-item style="margin-top: 20px; margin-bottom: 10px;" label="健壮性">
            <Row>
              <Col :span="3">
                <Poptip trigger="hover" placement="top">
                  <div slot="content">
                    <a
                      href="https://km.sankuai.com/page/364112112"
                      target="_blank"
                      >修改模式说明</a
                    >
                  </div>
                  <Select v-model="robustMode" @on-change="robustModeChanging">
                    <Option value="ERROR_CODE">错误状态码</Option>
                    <Option value="CHANGE_CUSTOM">精确修改</Option>
                  </Select>
                </Poptip>
              </Col>
            </Row>
          </Form-item>
          <RobustConfigModal
            :show="robustView"
            :robustMode="robustMode"
            :selectedPages="robustPageInfos"
            :oldCustomConfig="robustCustomConfig"
            @close="closeRobustView"
            @change-data="changeRobustConfig"
          >
          </RobustConfigModal>
          <Collapse style="width:768px;" value="robustConfig">
            <panel name="robustConfig">
              健壮性配置
              <div slot="content">
              <el-divider content-position="left">
                <Button type="primary" shape="circle" @click="openRobustView">修改/查看详情</Button>
              </el-divider>

              <template>
                  <el-table :data="robustConfigSum" tooltip-effect="dark" show-summary>
                    <el-table-column prop="page" label="场景页面"></el-table-column>
                    <el-table-column prop="api" label="已选择Api数量"></el-table-column>
                    <el-table-column prop="count" label="总跳转次数"></el-table-column>
                  </el-table>
              </template>
            </div>
            </panel>
          </Collapse>
        </template>
      </Form>
      <div slot="footer">

				<Button v-if="temporaryMode" type="primary" size="small" @click="toRun" :disabled="isLoading"
          >下一步</Button>
        <Button v-else type="primary" size="small" @click="addUserJob"
          >保存配置</Button
        >
        <Button type="info" size="small" @click="exitJonConfigEditor"
          >取消</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import { Bus } from "@/global/bus";
import vueJsonEditor from "vue-json-editor";
import RobustConfigModal from "./CompatibilityRobustUserConfigModal.vue"
export default {
  name: "UserConfigModal",
  components: { vueJsonEditor, RobustConfigModal },
  props: ["show", "isNew", "jobInfo", "pageList", "temporaryMode"],
  data() {
    return {
      isLoading: true,
      modalTitle: "",
      mrnEnv: false,
      jobConfigNew: false,
      detectShadow: false,
      userJobId: "",
      appLogin: "",
      mmp: "",
      ssoLoginUser: "",
      clientSwimlane: "",
      serverSwimlane: "",
      configJson: [
        {
          type: "signin",
          info: {
            htmOffline: 0,
            account: "",
            password: "",
            countryCode: "86",
          },
        },
        {
          type: "mmp_package",
          info: {
            mmpEnv: "prod",
            mmpVersionId: "",
            mmp_urlScheme: "",
          },
        },
        {
          type: "scheme",
          info: {
            value: "${URL_SCHEME}",
          },
        },
      ],
      visionComponentNameList: [],
      visionComponentOptionList: [],
      checkerNameList: [],
      checkerOptionList: [],
      mrnBundles: [],
      bundleList: [],
      mrnListNew: [],
      mrnBundle: [
        {
          bundle_name: "",
          version: "",
          env: "",
        },
      ],
      bundleEnvList: [
        {
          value: "prod",
          label: "prod",
        },
        {
          value: "test",
          label: "test",
        },
      ],
      appUrl: [],
      defaultWebUrl: "https://s3plus.sankuai.com/compatibility/local/aimeituan-release_12.13.200-253501-aarch64.apk",
      appList: [],
      mKey: true,
      useTestID: false,
      debugLink: false,
      privacyMode: false,
      useTestDevices: false,
      abKey: "",
      taskUrl: "",
			action_filter: {},
      taskSchemes: {
        schemes: ["imeituan://www.meituan.com(举例)", ""],
      },
      ifSchemesJsonCorrect: true,
      appPlat: "iOS",
      pageKeys: [],
      priorityData: [
        {
          value: "p0",
          label: "p0",
        },
        {
          value: "p1",
          label: "p1",
        },
        {
          value: "p2",
          label: "p2",
        },
      ],
      priorityList: ["p1", "p2"],
      priorityListNew: [1, 2, 3, 4],
      priorityFilter: [1, 2, 3, 4],
      jobTypeOption: [
        {
          value: "compatibility",
          label: "视觉测试",
        },
        {
          value: "robust",
          label: "健壮性测试",
        },
      ],
      jobType: "compatibility",
      // robust
      robustMode: "CHANGE_CUSTOM",
      oldGroups: [],
      groupInfo: [],
      groupData: [
        {
          value: "Android,iOS",
          label: "MRN/H5",
          children: [],
        },
        {
          value: "Android",
          label: "Android",
          children: [],
        },
        {
          value: "iOS",
          label: "iOS",
          children: [],
        },
        {
          value: "Web",
          label: "Web",
          children: [],
        },
      ],
      jobGroupInfo: [],
      jobGroupData: [],
      pages: [],
      category: "美团",
      categoryNew: "美团",
      business: "",
      jobName: "",
      groupAppNames: [],
      userJob: [],
      pageGroups: [],
      selectedPageGroups: [],
      selectedGroupPages: [],
      selectedGroupPageKeys: [],
      productListNew: [],
      businessListNew: [],
      jobProductID: 0,
      engines: ["Mobile", "Web"],
      runEngine: "Mobile",
      mmcdBusinesses: [],
      listStyle: {
        width: "350px",
        height: "300px",
      },
      ruleValidate: {
        appUrl: [
          {
            required: true,
            type: "array",
            min: 1,
            message: "安装包不能为空",
            trigger: "blur",
          },
        ],
        pages: [
          {
            required: true,
            type: "array",
            min: 1,
            message: "页面不能为空",
            trigger: "blur",
          },
        ],
      },
      testItemLabel: [],
      modalShow: false,
      graytestSelectedItems: [],
      graytestOptions: [
        {
            "type": "graytest",
            "info": {"value": 1},
            "desc":"门票灰度环境"
        },
        {

            "type": "htptest",
            "info": {"value": 1},
            "desc":"住宿ptest链路"
          }
        ],
      service_env: [],
      robustView: false,
      robustCustomConfig: {},
      robustConfigSum: [],
      robustPageInfos: {}
    };
  },
  computed: {
  },
  mounted() {
    this.getMMCDBusinesses();
    this.getMRNBundleList();
    this.getMRNListNew();
    this.getGroups();
    this.getBusinessesNew();
    this.getProductsNew();
    this.getVisionComponents();
    this.getTestItems();
  },
  watch: {
    jobType(val) {
      if (val === "robust") {
        this.mKey = true;
      }
    },
    mKey(val) {
      if (val === false) {
        this.debugLink = false;
      }
    },
    mmcdBusinesses(val) {
      if (val.length <= 0) {
        return;
      }
      let validGroups = [];
      for (let i = 0; i < this.oldGroups.length; i++) {
        if (val.indexOf(this.oldGroups[i].value) < 0) {
          // 旧入口去掉mmcd已迁移的业务线
          validGroups.push(this.oldGroups[i]);
        }
      }
      this.groupData[0].children = validGroups;
      this.groupData[1].children = validGroups;
      this.groupData[2].children = validGroups;
      this.groupData[3].children = validGroups;
      let validGroupsNew = [];
      for (let i = 0; i < this.businessListNew.length; i++) {
        if (val.indexOf(this.businessListNew[i].business) >= 0) {
          // 新入口只保留mmcd已迁移的业务线
          validGroupsNew.push(this.businessListNew[i]);
        }
      }
      this.businessListNew = validGroupsNew;
    },
    show: function (newVal, oldVal) {
      this.modalShow = newVal;
      if (newVal === false) {
        return;
      }
      if (this.jobInfo) {
        this.editJob(this.jobInfo);
      } else {
        this.addJobInfo(this.isNew);
      }
    },
  },
  methods: {
    toRun() {
      // 进入设备选择页
      let data = this.genUserJob()
      // 通过pageIds查询SceneIds并将SceneId传入pages中
      this.getSceneIdsByPageIds(data.pages).then((res) => {
        let sceneIds = res.data.resultMap
        for (let i = 0; i < data.pages.length; i++) {
          data.pages[i]["scene_id"] = sceneIds[data.pages[i].id]
        }
      })
      console.log('toRun', data)
			this.$emit("toRun", data)
			this.$emit("closeModal")
		},
    async getSceneIdsByPageIds(pages){
      let pageIds = []
      // 循环获取pageId，组成数组pageIds
      for (let i = 0; i < pages.length; i++) {
        pageIds.push(pages[i].id)
      }
      return await this.$axios({
      method:"get",
      params:{
        "pageIds": String(pageIds)
      },
      url:this.env.url+"page/getSceneIdsByPageIds"
      }).catch(function (error) {
        console.log(error)
      })
    },
    renderPageSelectTip(item) {
      return '<span title="' + item.label + '">' + item.label + "</span>";
    },
    getMMCDBusinesses() {
      this.$axios({
        method: "get",
        url: "http://client.hotel.test.sankuai.com/client/lyrebird/mock/group/business",
      })
        .then((res) => {
          this.mmcdBusinesses = res.data;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    mrnInfo(row) {
      this.$Modal.info({
        width: "500",
        title: "MRN信息",
        content: row.mrn_bundle,
      });
    },
    changePlatform() {
      this.appUrl = [];
      this.appList = [];
    },
    getVisionComponents() {
      this.$axios({
        method: "get",
        url: this.env.url + "compatibility/actuator/visionComponents",
      })
        .then((res) => {
          let message = res.data;
          let visionComponents = message;
          for (let i = 0; i < visionComponents.length; i++) {
            visionComponents[i].label = visionComponents[i].name;
            visionComponents[i].value = visionComponents[i].name;
          }
          this.visionComponentOptionList = visionComponents;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getCheckerName(value) {
      let v = value.replace(" ", "");
      this.checkerOptionList = [
        {
          label: v,
          value: v,
        },
      ];
    },
    addJobInfo(isNew) {
      this.selectedGroupPages = [];
      this.pageKeys = [];
      this.appUrl = [];
      this.appPlat = "iOS";
      this.runEngine = "Mobile";
      this.appLogin = "";
      this.ssoLoginUser = "";
      this.appList = [];
      this.pageGroups = [];
      this.pages = [];
      this.selectedGroupPageKeys = [];
      this.selectedPageGroups = [];
      this.selectedGroupPages = [];
      this.groupInfo = [];
      this.jobConfigNew = isNew;
      this.jobGroupInfo = [];
      this.modalTitle = "添加任务";
      this.userJobId = "";
      this.debugLink = false;
      this.privacyMode = false;
      this.detectShadow = false;
      this.mrnEnv = false;
      this.mKey = true;
      this.useTestID = false;
      this.jobName = isNew ? "【MMCD】" : "";
      this.business = null;
      this.jobProductID = 0;
			this.action_filter = {};
      this.graytestSelectedItems = [];
      this.service_env = [];
    },
    editJob(row) {
      this.business = row.business;
      this.jobConfigNew = row.job_name.indexOf("【MMCD】") === 0;
			if (this.temporaryMode) {
				this.modalTitle = "确认任务配置";
			} else {
				this.modalTitle = "更新任务配置";
			}
      this.category = row.category;
      this.jobName = row.job_name;
      this.mKey = row.mock_flag === 1;
      this.jobType = row.type;
      this.appList = this.getAppUrl(row);
      this.appUrl = row.app_url.split(",");
      let key_index = this.appUrl.indexOf(this.defaultWebUrl);
      if (key_index > -1) {
        this.appUrl.splice(key_index, 1);
        this.appList.splice(key_index, 1);
      }
      this.abKey = row.ab_key;
      this.mrnBundle = this.editMRNBundle(JSON.parse(row.mrn_bundle));
      this.taskUrl = row.task_url;
      this.jobGroupInfo = [];
      this.groupInfo = [];
      if (this.jobConfigNew) {
        let jobProduct = this.getJobProduct(row);
        this.jobGroupInfo[0] = jobProduct.product_id;
        this.jobGroupInfo[1] = jobProduct.impl;
        this.jobGroupInfo = [this.jobGroupInfo[0], this.jobGroupInfo[1]];
        typeof JSON.parse(row.app_params).runEngine !== "undefined"
          ? (this.runEngine = JSON.parse(row.app_params).runEngine)
          : "Mobile";
        this.getPageNew(this.jobGroupInfo);
				this.appPlat = row.platform
      } else {
        this.groupInfo[1] = row.business;
        this.groupInfo[0] = row.platform;
        this.groupInfo = [this.groupInfo[0], this.groupInfo[1]];
        if (this.pageList && this.pageList.length > 0) {
          this.pages = this.pageList;
        } else {
          this.pages = this.getPage(this.groupInfo);
        }
				row.app_url.indexOf(".apk") > 0 ? (this.appPlat = "Android") : (this.appPlat = "iOS");
      }
      this.pageKeys = this.getPageKeys(row);
      this.userJobId = row.id;
      this.useTestDevices = row.use_test_devices === "1";
			let app_params = JSON.parse(row.app_params)
      this.debugLink = app_params.debug_link === true;
      this.privacyMode = app_params.privacyMode === true;
      this.mrnEnv = app_params.env_mrnEvaTest === 1;
      this.detectShadow = app_params.detectShadow === true;
      this.useTestID = app_params.use_testID === true;
      typeof app_params.robustMode != "undefined"
        ? (this.robustMode = app_params.robustMode)
        : 1;
      typeof app_params.taskSchemes != "undefined"
        ? (this.taskSchemes = app_params.taskSchemes)
        : {
            schemes: ["imeituan://www.meituan.com(举例)", ""],
          };
      if(typeof app_params.service_env != "undefined"){
        this.graytestSelectedItems = app_params.service_env.map(option => option.type)
      }
      else{
        this.graytestSelectedItems = []
      }
      if (typeof app_params.checkers != "undefined") {
        let checkers = app_params.checkers.split(",");
        for (let i = 0; i < checkers.length; i++) {
          this.checkerOptionList.push({
            label: checkers[i],
            value: checkers[i],
          });
        }
        this.checkerNameList = checkers;
      }
      if (typeof app_params.visionComponents != "undefined") {
				let visionComponents = app_params.visionComponents
				if (typeof app_params.visionComponents === 'string') {
					visionComponents = app_params.visionComponents.split(",");
				}
				for (let i = 0; i < visionComponents.length; i++) {
					this.visionComponentOptionList.push({
						label: visionComponents[i],
						value: visionComponents[i],
					});
				}
        this.visionComponentNameList = visionComponents;
      }
      if (this.jobType === 'robust' && typeof app_params.robustMode != "undefined") {
        this.robustMode = app_params.robustMode
      }
      if (this.jobType === 'robust' && typeof app_params.robustCustomConfig != "undefined") {
        if(this.robustMode === 'CHANGE_CUSTOM' || this.robustMode === 'ERROR_CODE'){
          this.robustCustomConfig = app_params.robustCustomConfig
          this.pageKeys = Object.keys(this.robustCustomConfig)
          this.robustConfigSum = this.robustConfigSumCalculate()
        }
      }
      typeof app_params.appLogin != "undefined"
        ? (this.appLogin = app_params.appLogin)
        : "";
      typeof app_params.mmp != "undefined"
        ? (this.mmp = app_params.mmp)
        : "";
      typeof app_params.ssoLoginUser != "undefined" || app_params.ssoLoginUser !== ""
        ? (this.ssoLoginUser = app_params.ssoLoginUser)
        : "";
      typeof app_params.configJson != "undefined"
        ? (this.configJson = app_params.configJson)
        : this.configJson;
      typeof app_params.clientSwimlane != "undefined"
        ? (this.clientSwimlane = app_params.clientSwimlane)
        : this.clientSwimlane;
        typeof app_params.serverSwimlane != "undefined"
        ? (this.serverSwimlane = app_params.serverSwimlane)
        : this.serverSwimlane;
			typeof app_params.action_filter != "undefined"
				? (this.action_filter = app_params.action_filter) : this.action_filter
    },
    getPageKeys(row) {
      let page = JSON.stringify(row.pages);
      let jsonObj = JSON.parse(page);
      let pageKeys = [];
      for (let i = 0; i < jsonObj.length; i++) {
        pageKeys[i] = jsonObj[i].id.toString();
      }
      return pageKeys;
    },
    getAppUrl(row) {
      let appList = [];
      let apps = row.app_url.toString().split(",");
      for (let i = 0; i < apps.length; i++) {
        let appURL = apps[i];
        let appName = appURL.split("/")[appURL.split("/").length - 1];
        appList.push({
          value: appURL,
          label: appName,
        });
      }
      return appList;
    },
    getAppsByKey(key) {
      this.appList = [{"value": key, "label": key}];
      if (key.trim().length === 0) {
        this.appList = []
        return;
      }
      this.$axios({
        method: "get",
        params: {
          key: key.trim(),
          platform: this.appPlat,
          productId: this.jobProductID,
        },
        url: this.env.url + "clientSwitchboard/getAppByKey",
      })
        .then((res) => {
          let message = res.data;
          this.appList = message;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    handleAdd() {
      this.mrnBundle.push({
        bundle_name: "",
        version: "",
        env: "",
      });
    },
    handleRemove(index) {
      this.mrnBundle.splice(index, 1);
    },
    handleClear() {
      this.mrnBundle = [
        {
          bundle_name: "",
          version: "",
          env: "",
        },
      ];
    },
    getPageGroup(app, business, os) {
      this.$axios({
        method: "post",
        data: {
          app: app,
          business: business,
          os: os,
        },
        url: this.env.url + "clientPage/getPageGroup",
      })
        .then((res) => {
          let message = res.data;
          this.pageGroups = message;
        })
        .catch(function (error) {});
    },
    getPage(value) {
      if (value === undefined) {
        return;
      }
      if (typeof value === "string") {
        value = this.groupInfo;
      }
      if (value.length > 1 && this.priorityList.length > 0) {
        let appMap = {};
        for (let i = 0; i < this.groupAppNames.length; i++) {
          appMap[this.groupAppNames[i].label] = this.groupAppNames[i].app_name;
        }
        if (value[0] === "MRN/H5") {
          value[0] = "Android,iOS";
        }
        this.getPageGroup(appMap[this.category], value[1], value[0]);
        this.$axios({
          method: "get",
          params: {
            app: appMap[this.category],
            business: value[1],
            platform: value[0],
            priority: this.priorityList.toString(),
          },
          url: this.env.url + "clientPage/getOnlinePageByGroup",
        })
          .then((res) => {
            let message = res.data;
            this.pages = message;
            if (!this.modalShow) {
              this.pageKeys = [];
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      } else {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择业务信息",
        });
      }
    },
    getPageNew(value) {
      if (value === undefined || value.length === 0) {
        return;
      }
      if (typeof value === "string") {
        value = this.jobGroupInfo;
      }
      if (value === undefined || value.length === 0) {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择产品信息",
        });
        return;
      }
      let productInfo = value[0].split("&");
      let pageImpl = value[1];
      this.jobProductID = parseInt(productInfo[0]);
      let productLabel = productInfo[1].split("-");
      this.category = productLabel[0];
      if (productLabel.length > 1) {
        this.appPlat = productLabel[1];
      }
      if (this.appPlat !== "Web" && pageImpl !== "H5") {
        this.runEngine = "Mobile";
      }
      if (this.business === undefined) {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择业务信息",
        });
        return;
      }
      let params = {
        business: this.business,
        productId: this.jobProductID,
        priority: this.priorityFilter,
        testItem: this.jobType,
      };
      if (pageImpl !== "All") {
        params["implement"] = [pageImpl];
      }
      if (pageImpl !== "MP") {
        this.mmp = "";
      }
      this.$axios({
        method: "post",
        data: params,
        url: this.env.url + "page/getPageObject",
      })
        .then((res) => {
          let message = res.data;
          this.pages = message;
          if (!this.modalShow) {
            this.pageKeys = [];
          }
          for (let i = 0; i < this.pages.length; i++) {
            this.pages[i]["label"] =
              "[" + this.pages[i].priority + "] " + this.pages[i].name;
            this.pages[i]["key"] = this.pages[i].id.toString();
          }
          this.isLoading = false
          this.pageSelectChange(this.pageKeys)
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getPageNewWithMRN() {
      if (
        this.business === undefined &&
        (this.jobGroupInfo === undefined || this.jobGroupInfo.length === 0)
      ) {
        return;
      }
      if (this.business === undefined) {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择业务信息",
        });
        return;
      }
      if (this.jobGroupInfo === undefined || this.jobGroupInfo.length === 0) {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择产品信息",
        });
        return;
      }
      let productInfo = this.jobGroupInfo[0].split("&");
      let pageImpl = this.jobGroupInfo[1];
      this.jobProductID = parseInt(productInfo[0]);
      let productLabel = productInfo[1].split("-");
      this.category = productLabel[0];
      if (productLabel.length > 1) {
        this.appPlat = productLabel[1];
      }
      let params = {
        business: this.business,
        productId: this.jobProductID,
        priority: this.priorityFilter,
        testItem: this.jobType,
      };
      if (pageImpl !== "All") {
        params["implement"] = [pageImpl];
      }
      let mrnList = [];
      for (let i = 0; i < this.mrnBundle.length; i++) {
        if (
          this.mrnBundle[i].bundle_name !== undefined &&
          this.mrnBundle[i].bundle_name.length > 0
        ) {
          mrnList.push(this.mrnBundle[i].bundle_name);
        }
      }
      if (mrnList.length > 0) {
        params["category"] = mrnList;
      }
      this.$axios({
        method: "post",
        data: params,
        url: this.env.url + "page/getPageObject",
      })
        .then((res) => {
          let message = res.data;
          this.pages = message;
          if (!this.modalShow) {
            this.pageKeys = [];
          }
          for (let i = 0; i < this.pages.length; i++) {
            this.pages[i]["label"] =
              "[" + this.pages[i].priority + "] " + this.pages[i].name;
            this.pages[i]["key"] = this.pages[i].id.toString();
            this.pageKeys.push(this.pages[i]["key"]);
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    clearSelectedGroupPages() {
      // 将属于所选组的页面清空
      let nowPageKeys = this.pageKeys;
      this.pageKeys = [];
      nowPageKeys.forEach((pageID) => {
        if (!this.selectedGroupPageKeys.includes(pageID)) {
          this.pageKeys.push(pageID);
        }
      });
      this.selectedGroupPageKeys = [];
    },
    getPagesByGroupIDs() {
      this.clearSelectedGroupPages();
      if (this.selectedPageGroups.length === 0) {
        return;
      }
      this.$axios({
        method: "get",
        params: {
          pageGroupIDs: this.selectedPageGroups.join(","),
          priority: "p1,p2",
        },
        url: this.env.url + "clientPage/getPagesByPageGroupIDs",
      })
        .then((res) => {
          this.selectedGroupPages = res.data;
          this.selectedGroupPages.forEach((pageObj) => {
            this.selectedGroupPageKeys.push(pageObj.id.toString());
            if (!this.pageKeys.includes(pageObj.id.toString())) {
              this.pageKeys.push(pageObj.id.toString());
            }
          });
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getMRNPage() {
      if (this.jobConfigNew) {
        return this.getPageNewWithMRN();
      }
      if (this.groupInfo.length > 1 && this.priorityList.length > 0) {
        if (this.mrnBundle[0].bundle_name) {
          this.mrnBundles = [];
          for (let i = 0; i < this.mrnBundle.length; i++) {
            this.mrnBundles[i] = this.mrnBundle[i].bundle_name;
          }
          let appMap = {
            美团: "meituan",
            点评: "dianping",
            美团商家: "mhotel",
          };
          this.$axios({
            method: "get",
            params: {
              MRNBundles: this.mrnBundles.toString(),
              business: this.groupInfo[1],
              app: appMap[this.category],
            },
            url: this.env.url + "client/getPagesByMRNBundlesAndBusiness",
          })
            .then((res) => {
              this.mrnPages = res.data;
              this.pageKeys = [];
              for (let j = 0; j < this.mrnPages.length; j++) {
                this.pageKeys[j] = this.mrnPages[j].id.toString();
              }
            })
            .catch(function (error) {
              console.log(error);
            });
        } else
          this.$Message.warning({
            title: "提示",
            content: "您还没有指定正确的MRN包,请指定MRN后再次尝试！",
            duration: 3,
          });
      } else {
        this.$Notice.warning({
          title: "提示",
          desc: "请选择业务信息",
        });
      }
    },
		genUserJob() {
			if (this.pageKeys.length === 0) {
        this.$Notice.warning({
          title: "提示",
          desc: "请完善配置信息，选择被测页面",
        });
        return;
      }
      if (!this.ifSchemesJsonCorrect) {
        this.$Notice.warning({
          title: "提示",
          desc: "urlschemes填入有误，请修改",
        });
        return;
      }
      if (JSON.stringify(this.taskSchemes).length >= 50000) {
        this.$Notice.warning({
          title: "提示",
          desc: "urlschemes超长，请减少urlschemes数量",
        });
        return;
      }
      if (this.isWeb() && this.appUrl.length === 0) {
        this.appUrl = [this.defaultWebUrl];
      }
			if (this.isWMP() && this.appUrl.length <= 0) {
				this.appUrl = this.defaultWebUrl
			}
      if (this.appUrl.length <= 0
			|| this.appUrl.toString().indexOf("http") == -1
			|| (this.appUrl.toString().indexOf("apk") == -1 && this.appUrl.toString().indexOf(".ipa") == -1 && this.appUrl.toString().indexOf(".hap") == -1)) {
        this.$Notice.warning({
          title: "提示",
          desc: "请完善配置信息，手动填入测试包需完整路径",
        });
			}
			let pageList = [];
			for (let i = 0; i < this.pageKeys.length; i++) {
				for (let j = 0; j < this.pages.length; j++) {
					if (this.pageKeys[i] === this.pages[j].id.toString()) {
						pageList.push(this.pages[j]);
					}
				}
			}
			let mrnBundleInfo = JSON.stringify(this.mrnBundle);
			if (mrnBundleInfo.indexOf("bundle_name") < 0) {
				mrnBundleInfo = JSON.stringify(
					(this.mrnBundle = [{ bundle_name: "", version: "", env: "" }])
				);
			}
			let jobBusiness = this.groupInfo[1];
			let jobPlat = this.groupInfo[0];
			let pageImpl = "All";
			let runEngine = "Mobile";
			if (this.jobConfigNew) {
				jobBusiness = this.business;
				let productInfo = this.jobGroupInfo[0].split("&");
				let productLabel = productInfo[1].split("-");
				this.category = productLabel[0];
				if (productLabel.length > 1) {
					jobPlat = productLabel[1];
				} else {
					jobPlat = this.appPlat
				}
				pageImpl = this.jobGroupInfo[1];
				jobPlat === "Web"
					? (runEngine = "Web")
					: (runEngine = this.runEngine);
			}
			this.priorityFilter = this.priorityListNew;
      let env_lock_mrn_bundle = 'NA'
      if (this.mrnBundle) {
        let testEnvBundles = []
        let prodEnvBundles = []
        this.mrnBundle.forEach(bundle => {
          if (bundle['env'] === 'test') {
            testEnvBundles.push({"bundleName": bundle["bundle_name"], "version": bundle["version"]})
          } else {
            prodEnvBundles.push({"bundleName": bundle["bundle_name"], "version": bundle["version"]})
          }
        })
        env_lock_mrn_bundle = JSON.stringify({"plat":jobPlat,"bundle_list_test_env":testEnvBundles, "bundle_list_prod_env": prodEnvBundles})
      }
      if (this.graytestSelectedItems){
        this.service_env = this.graytestSelectedItems.map(type => {
            return this.graytestOptions.find(option => option.type === type)})
      }
			return {
				platform: jobPlat,
				plat: jobPlat,
				job_name: this.jobName,
				user: Bus.userInfo.userLogin,
				business: jobBusiness,
				category: this.category,
				type: this.jobType,
				app_url: this.appUrl.toString(),
				pages: pageList,
				mock_data: this.mKey,
				mock_flag: this.mKey ? 1 : 0,
				ab_key: this.abKey,
				task_url: this.taskUrl,
				use_test_devices: this.useTestDevices,
				mrn_bundle: mrnBundleInfo,
				id: this.userJobId,
				env_lock_mrn_bundle: env_lock_mrn_bundle,
				app_params: JSON.stringify({
					runEngine: runEngine,
					pageImpl: pageImpl,
					visionComponents: this.visionComponentNameList.toString(),
					detectShadow: this.detectShadow,
					appLogin: this.appLogin,
					mmp: this.mmp,
					clientSwimlane: this.clientSwimlane,
          serverSwimlane: this.serverSwimlane,
					ssoLoginUser: this.ssoLoginUser,
					debug_link: this.debugLink,
					privacyMode: this.privacyMode,
					use_testID: this.useTestID,
					env_mrnEvaTest: this.mrnEnv === true ? 1 : 0,
					robustMode: this.robustMode,
          robustCustomConfig: this.robustCustomConfig,
					checkers: this.checkerNameList.toString(),
					configJson: this.configJson,
					product_info: this.getJobProductInfo(),
					taskSchemes: this.taskSchemes,
					action_filter: this.action_filter,
          service_env:this.service_env,
				})
			}
		},
    addUserJob() {
			let data = this.genUserJob()
			this.$axios({
				method: "post",
				data: data,
				url: this.env.url + "client/compatibility/userJob",
			})
			.then((res) => {
				let message = res.data;
				if (message.code === 0) {
					this.$Notice.success({
						title: "保存配置成功",
					});
					this.$emit("updateUserJob");
				}
			})
			.catch(function (error) {});
    },
    exitJonConfigEditor() {
      this.priorityFilter = this.priorityListNew;
      this.$emit("closeModal");
    },
    filterMethod(data, query) {
      return data.label.indexOf(query) > -1;
    },
    getGroups() {
      this.getGroupAppNames();
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getGroups",
      })
        .then((res) => {
          let message = res.data;
          this.oldGroups = message;
          this.getUserBusiness(this.oldGroups);
          this.getUserBusinessFromMMCD();
          if (this.mmcdBusinesses.length > 0) {
            let validGroups = [];
            for (let i = 0; i < message.length; i++) {
              if (this.mmcdBusinesses.indexOf(message[i].value) < 0) {
                // 旧入口去掉mmcd已迁移的业务线
                validGroups.push(message[i]);
              }
            }
            this.oldGroups = validGroups;
          }
          this.groupData[0].children = this.oldGroups;
          this.groupData[1].children = this.oldGroups;
          this.groupData[2].children = this.oldGroups;
          this.groupData[3].children = this.oldGroups;
        })
        .catch(function (error) {});
    },
    getGroupAppNames() {
      this.$axios({
        method: "get",
        url: this.env.url + "clientPage/getGroupAppNames",
      })
        .then((res) => {
          let message = res.data;
          this.groupAppNames = message;
        })
        .catch(function (error) {});
    },
    pageSelectChange(newTargetKeys) {
      this.pageKeys = newTargetKeys;
      if (this.jobType === 'robust') {
        this.robustPageInfos = this.pages.reduce((acc, item) => {
          // 如果对应page在目标列表中被删除,同步删除该page对应的健壮性配置
          for (const key in this.robustCustomConfig) {
            if (!newTargetKeys.includes(key)) {
              delete this.robustCustomConfig[key];
            }
          }
          // 获取已勾选page的详情
          if (this.pageKeys.includes(item.key)) {
            acc[item.key] = item;
          }
          return acc;
        }, {});
        this.robustConfigSum = this.robustConfigSumCalculate();
      }
    },
    getBusinessesNew() {
      this.$axios({
        method: "get",
        url: this.env.url + "autoTestConfig/getAllBusinessName",
      })
        .then((res) => {
          let message = res.data;
          this.businessListNew = message;
          if (this.mmcdBusinesses.length > 0) {
            let validGroups = [];
            for (let i = 0; i < message.length; i++) {
              if (this.mmcdBusinesses.indexOf(message[i].business) >= 0) {
                // 新入口只保留mmcd已迁移的业务线
                validGroups.push(message[i]);
              }
            }
            this.businessListNew = validGroups;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getTestItemLabel(itemName) {
      let label = itemName;
      for (let i = 0; i < this.testItemLabel.length; i++) {
        if (this.testItemLabel[i].name === itemName) {
          label = this.testItemLabel[i].label;
          break;
        }
      }
      return label;
    },
    getTestItems() {
      this.$axios({
        method: "get",
        url: this.env.url + "page/getTestItem",
      })
        .then((res) => {
          let message = res.data;
          if (Array.isArray(message)) {
            this.testItemLabel = message;
            let testItemOptions = [];
            for (let i = 0; i < message.length; i++) {
              if (message[i].frame !== 1) {
                testItemOptions.push({
                  label: message[i].label,
                  value: message[i].name,
                });
              }
            }
            if (testItemOptions.length > 0) {
              this.jobTypeOption = testItemOptions;
            }
          }
          // console.log(message);
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getProductsNew() {
      this.$axios({
        method: "get",
        url: this.env.url + "page/getProductList",
      })
        .then((res) => {
          let message = res.data;
          this.productListNew = message;
          this.jobGroupData = this.productListNew.map((item) => {
            let chs = [
              {
                value: "All",
                label: "All",
              },
            ];
            chs = chs.concat(
              item.implList.map((impl) => {
                return {
                  value: impl.toString(),
                  label: impl.toString(),
                };
              })
            );
            return {
              value: item.id.toString() + "&" + item.label.toString(),
              label: item.label.toString(),
              children: chs,
            };
          });
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getMRNListNew() {
      this.$axios({
        method: "get",
        url: this.env.url + "page/getMRNList",
      })
        .then((res) => {
          let message = res.data;
          message.push({
            name: "rn_travel_ticketchannel-client-page",
            id: 1170,
            type: "MRN",
          });
          let filter = new Set();
          message.forEach((mrnInfo) => {
            if (filter.has(mrnInfo.name) == false) {
              this.mrnListNew.push(mrnInfo);
              filter.add(mrnInfo.name);
            }
          });
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    getMRNBundleList() {
      this.$axios({
        method: "get",
        url: this.env.url + "client/MRNBundleList",
      })
        .then((res) => {
          let message = res.data;
          let filter = new Set();
          message.forEach((mrnInfo) => {
            if (filter.has(mrnInfo.label) == false) {
              this.bundleList.push(mrnInfo);
              filter.add(mrnInfo.label);
            }
          });
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    searchMRNBundle(value) {
      let isExist = false;
      for (let i = 0; i < this.bundleList.length; i++) {
        if (this.jobConfigNew) {
          if (this.mrnListNew[i].name.includes(value)) {
            isExist = true;
          }
        } else {
          if (this.bundleList[i].label.includes(value)) {
            isExist = true;
          }
        }
      }
      if (!isExist) {
        if (this.jobConfigNew) {
          this.mrnListNew.push({name: value, id: value})
        } else {
          this.bundleList.push({ value: value, label: value });
        }
      }
    },
    editMRNBundle(MRNBundleList) {
      for (let j = 0; j < MRNBundleList.length; j++) {
        let isExist = false;
        if (this.jobConfigNew) {
          for (let k = 0; k < this.mrnListNew.length; k++) {
            if (this.mrnListNew[k].name.includes(MRNBundleList[j].bundle_name)) {
              isExist = true;
            }
          }
          if (!isExist) {
            this.mrnListNew.push({
              name: MRNBundleList[j].bundle_name,
              id: MRNBundleList[j].bundle_name
            });
          }
        } else {
          for (let k = 0; k < this.bundleList.length; k++) {
            if (this.bundleList[k].label.includes(MRNBundleList[j].bundle_name)) {
              isExist = true;
            }
          }
          if (!isExist) {
            this.bundleList.push({
              value: MRNBundleList[j].bundle_name,
              label: MRNBundleList[j].bundle_name
            });
          }
        }
      }
      return MRNBundleList;
    },
    getJobProduct(row) {
      let productLabel = row.category;
      let appParams = JSON.parse(row.app_params)
      let pageImpl = appParams.pageImpl;
      if (pageImpl === undefined) {
        pageImpl = "All";
      }
      let app_type = JSON.parse(appParams.product_info).type
      if (row.platform !== "Android,iOS" && this.isWMP(app_type) === false) {
        productLabel += "-" + row.platform;
      }
			let target_product_id = row.product_id
      for (let j = 0; j < this.productListNew.length; j++) {
        if (this.productListNew[j].label === productLabel || this.productListNew[j].id === target_product_id) {
          return {
            product_id:
              this.productListNew[j].id.toString() +
              "&" +
              this.productListNew[j].label,
            impl: pageImpl,
          };
        }
      }
      return {
        product_id: undefined,
        impl: undefined,
      };
    },
    getJobProductID(row) {
      if (row.job_name.indexOf("【MMCD】") < 0) {
        return -1;
      }
      let productLabel = row.category;
      if (row.platform !== "Android,iOS") {
        productLabel += "-" + row.platform;
      }
      for (let j = 0; j < this.productListNew.length; j++) {
        if (this.productListNew[j].label === productLabel) {
          return this.productListNew[j].id;
        }
      }
      return -1;
    },
    getJobProductInfo() {
      if (!this.jobConfigNew) {
        return null;
      }
      let productInfo = this.jobGroupInfo[0].split("&");
      this.jobProductID = parseInt(productInfo[0]);
      for (let j = 0; j < this.productListNew.length; j++) {
        if (this.productListNew[j].id === this.jobProductID) {
          return JSON.stringify(this.productListNew[j]);
        }
      }
      return null;
    },
    isWMP(appType) {
      if (this.jobConfigNew) {
        if (this.jobGroupInfo && this.jobGroupInfo[0]) {
          let jobProductID = this.jobGroupInfo[0].split("&")[0];
          for (let i = 0; i < this.productListNew.length; i++) {
            if (this.productListNew[i].id.toString() === jobProductID) {
              appType = this.productListNew[i].type
              break
            }
          }
        }
        if (appType) {
          return (
             appType === "mp" ||
              appType === "wmp" ||
              appType === "ksmp" ||
              appType === "MMP" ||
              appType === "bdmp"
            )
        } else {
           return false
        }
      } else {
        return (
          this.category === "微信" ||
          this.category === "快手" ||
          this.category === "百度地图"
        );
      }
    },
    changePriorityFilter(value) {
      this.getPageNew(this.jobGroupInfo);
    },
    isRobust() {
      return this.jobType === 'robust'
    },
    isWeb() {
      if (this.jobConfigNew) {
        if (this.jobGroupInfo.length === 0) {
          return false;
        }
        let jobProductLabel = this.jobGroupInfo[0].split("&")[1];
        let jobLabelInfo = jobProductLabel.split("-");
        let jobPlat = "";
        if (jobLabelInfo.length > 1) {
          jobPlat = jobLabelInfo[1];
        }
        return jobPlat === "Web" || this.runEngine === "Web";
      } else {
        if (this.groupInfo.length === 0) {
          return false;
        }
        return this.groupInfo[0] === "Web";
      }
    },
    isAndroid() {
      if (this.isWMP()) {
        return this.appPlat.toUpperCase().indexOf("ANDROID") > -1;
      }
      if (this.jobConfigNew) {
        if (this.jobGroupInfo.length === 0) {
          return false;
        }
        let jobProductLabel = this.jobGroupInfo[0].split("&")[1];
        let jobLabelInfo = jobProductLabel.split("-");
        let jobPlat = "Android,iOS";
        if (jobLabelInfo.length > 1) {
          jobPlat = jobLabelInfo[1];
        }
        return jobPlat.toUpperCase().indexOf("ANDROID") > -1;
      } else {
        if (this.groupInfo.length === 0) {
          return false;
        }
        return (
          this.groupInfo[0].toUpperCase() === "ANDROID" ||
          this.appPlat.toUpperCase().indexOf("ANDROID") > -1
        );
      }
    },
    onJsonChange(value) {
      this.onJsonSave(value);
    },
    onJsonSave(value) {
      // console.log('保存value:', value);
      this.taskSchemes = value;
      this.ifSchemesJsonCorrect = true;
    },
    onJsonError(value) {
      this.ifSchemesJsonCorrect = false;
    },
    openRobustView() {
      this.robustView = true
    },
    closeRobustView(data) {
      this.changeRobustConfig(data)
      this.robustView = false
    },
    changeRobustConfig(data) {
      this.robustCustomConfig = data
      this.robustConfigSum = this.robustConfigSumCalculate()
      this.robustView = false
    },
    robustConfigSumCalculate() {
      let result = [];
      for (const [pageId, pageData] of Object.entries(this.robustCustomConfig)) {
        const pageResult = {
          page: pageData.name,
          api: pageData.configs.length,
          count: pageData.configs.reduce((sum, config) => {
            if (this.robustMode === "CHANGE_CUSTOM") {
              return sum + config.jsonPaths.length * config.robustTypes.length;
            }else if (this.robustMode === "ERROR_CODE") {
              return sum + config.errorCodes.length;
            }
          }, 0)
        };
        result.push(pageResult);
      }
      return result
    },
    robustModeChanging(mode) {
      this.robustCustomConfig = {}
      this.robustConfigSum = this.robustConfigSumCalculate()
    }
  },
};
</script>

<style scoped>
.bottom {
  text-align: center;
}
/deep/ .jsoneditor-vue {
  height: 350px;
}
.app_param {
  padding-bottom: 10px;
}
</style>
