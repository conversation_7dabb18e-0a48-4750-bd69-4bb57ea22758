<template>
    <div class="dfs-traversal-result-container">
        <div class="filter-controls">
            <RadioGroup v-model="filterStatus" type="button" @on-change="handleFilterChange">
                <Radio label="all">全部 ({{ traversalData.length }})</Radio>
                <Radio label="success">成功 ({{ successCount }})</Radio>
                <Radio label="error">失败 ({{ errorCount }})</Radio>
            </RadioGroup>
            <Select v-if="availableResultReasons.length > 1" v-model="resultReasonFilter" clearable placeholder="筛选最终结果" style="width: 200px; margin-left: 16px;">
                <Option v-for="reason in availableResultReasons" :key="reason" :value="reason">{{ reason || '未知' }}</Option>
            </Select>
        </div>

        <div v-if="!filteredData || filteredData.length === 0" class="no-data">
            {{ filterStatus === 'all' ? '暂无遍历数据或数据加载中...' : '没有符合筛选条件的数据' }}
        </div>

        <div v-else>
            <Spin v-if="isLoading" size="large" fix>
                <Icon type="ios-loading" size="24" class="spin-icon-load"></Icon>
                <div>加载中...</div>
            </Spin>
            <div class="steps-timeline">
                <div v-for="(item, index) in paginatedData" :key="index" class="step-card" :class="{
                    'step-error': item.result && (item.result.result_class === 1 || item.result.result_class === 2) && item.result.result_reason !== '交互无响应', 
                    'step-success': item.result && (item.result.result_class === 0 || item.result.result_reason === '交互无响应')
                  }">
                    <div class="step-header">
                        <div class="step-number">步骤 {{ item.case_index }}</div>
                        <div class="step-time">{{ new Date(item['@timestamp']).toLocaleString() }}</div>
                        <Tag :color="item.result && (item.result.result_class === 0 || item.result.result_reason === '交互无响应') ? 'success' : (item.result ? 'error' : 'default')" class="step-result">
                            {{ item.result ? item.result.result_reason || '未知' : '未知' }}
                        </Tag>
                    </div>
                    
                    <div class="step-content">
                        <div class="step-action">
                            <h3>操作信息</h3>
                            <div><strong>类型:</strong> {{ item.action_info.action }}</div>
                            <div><strong>元素:</strong> {{ item.action_info.elem_info.elem_detail_info }}</div>
                        </div>
                        
                        <div class="step-screenshots">
                            <div class="screenshot-container">
                                <h3>操作前</h3>
                                <div class="image-wrapper">
                                    <div v-if="!imageSrcs[`${item.case_index}_before`]" class="loading-placeholder">
                                        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
                                        <div>加载中...</div>
                                    </div>
                                    <img 
                                        v-if="imageSrcs[`${item.case_index}_before`]"
                                        :src="imageSrcs[`${item.case_index}_before`]" 
                                        alt="操作前" 
                                        class="screenshot" 
                                        @click="showImagePreview(item.screenshot_before)" 
                                        @error="onImageError"
                                    />
                                </div>
                            </div>
                            
                            <div class="screenshot-container">
                                <h3>操作后</h3>
                                <div class="image-wrapper">
                                    <div v-if="!imageSrcs[`${item.case_index}_after`]" class="loading-placeholder">
                                        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
                                        <div>加载中...</div>
                                    </div>
                                    <img 
                                        v-if="imageSrcs[`${item.case_index}_after`]"
                                        :src="imageSrcs[`${item.case_index}_after`]" 
                                        alt="操作后" 
                                        class="screenshot" 
                                        @click="showImagePreview(item.screenshot_after)" 
                                        @error="onImageError"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="pagination-container">
                <Page 
                    :total="filteredData.length" 
                    :current="currentPage" 
                    :page-size="pageSize"
                    @on-change="handlePageChange" 
                    show-total 
                    show-elevator
                    :page-size-opts="[10, 20, 50, 100]"
                    show-sizer
                    @on-page-size-change="handlePageSizeChange"
                />
            </div>
        </div>

        <Modal v-model="imagePreviewVisible" title="图片预览" footer-hide :styles="{top: '50px'}">
            <img :src="previewImageUrl" style="width: 100%" alt="预览" />
        </Modal>
    </div>
</template>

<script>
export default {
  name: 'DFSTraversalResult',
  props: {
    sceneResultList: {
      type: Array,
      required: true
    },
    taskId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      esUrl: this.env.es_url + '/es_service/search',
      esTestUrl: this.env.es_test_url + '/es_service/search',
      traversalData: [],
      filterStatus: 'all', // 'all', 'success', 'error'
      resultReasonFilter: '',
      imagePreviewVisible: false,
      previewImageUrl: '',
      currentPage: 1,
      pageSize: 20,
      isLoading: false,
      imageSrcs: {} // Store image srcs with keys
    }
  },
  computed: {
    availableResultReasons() {
      // 统计当前大项下所有result_reason类型
      const set = new Set()
      this.filteredDataRaw.forEach(item => {
        set.add(item.result ? (item.result.result_reason || '未知') : '未知')
      })
      return Array.from(set)
    },
    filteredDataRaw() {
      // 只做大项筛选，不做最终结果细分
      let filtered = this.traversalData
      if (this.filterStatus === 'success') {
        filtered = this.traversalData.filter(item =>
          item.result && (item.result.result_class === 0 || item.result.result_reason === '交互无响应')
        )
      } else if (this.filterStatus === 'error') {
        filtered = this.traversalData.filter(item =>
          item.result && (item.result.result_class === 1 || item.result.result_class === 2) &&
          item.result.result_reason !== '交互无响应'
        )
      }
      return [...filtered].sort((a, b) => a.case_index - b.case_index)
    },
    filteredData() {
      // 在大项筛选基础上，增加最终结果细分筛选
      let data = this.filteredDataRaw
      if (this.resultReasonFilter) {
        data = data.filter(item => (item.result ? (item.result.result_reason || '未知') : '未知') === this.resultReasonFilter)
      }
      return data
    },
    paginatedData() {
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.filteredData.slice(startIndex, endIndex)
    },
    successCount() {
      return this.traversalData.filter(item =>
        item.result && (item.result.result_class === 0 || item.result.result_reason === '交互无响应')
      ).length
    },
    errorCount() {
      return this.traversalData.filter(item =>
        item.result && (item.result.result_class === 1 || item.result.result_class === 2) &&
        item.result.result_reason !== '交互无响应'
      ).length
    }
  },
  mounted() {
    console.log('DFSTraversalResult mounted. Task ID:', this.taskId)
    this.fetchData()
  },
  watch: {
    taskId(newTaskId, oldTaskId) {
      console.log(`DFSTraversalResult Task ID changed from ${oldTaskId} to ${newTaskId}`)
      this.filterStatus = 'all' // Reset filter when taskId changes
      this.resultReasonFilter = ''
      this.currentPage = 1 // Reset to first page
      this.imageSrcs = {} // Clear all image srcs
      this.fetchData()
    },
    filterStatus() {
      this.resultReasonFilter = '' // 切换大项时重置细分筛选
      this.currentPage = 1
    },
    paginatedData: {
      handler(newData) {
        // Clear current image srcs
        this.imageSrcs = {}
        // Load images after a brief delay to ensure the UI updates
        setTimeout(() => {
          // For each item in the new page, load its images
          newData.forEach(item => {
            if (item.screenshot_before) {
              const imgBefore = new Image()
              imgBefore.onload = () => {
                this.$set(this.imageSrcs, `${item.case_index}_before`, item.screenshot_before)
              }
              imgBefore.src = item.screenshot_before
            }
            if (item.screenshot_after) {
              const imgAfter = new Image()
              imgAfter.onload = () => {
                this.$set(this.imageSrcs, `${item.case_index}_after`, item.screenshot_after)
              }
              imgAfter.src = item.screenshot_after
            }
          })
        }, 50)
      },
      immediate: true
    }
  },
  methods: {
    async fetchData() {
      if (!this.taskId) {
        console.warn('fetchData called with no taskId.')
        return
      }
      console.log(`Fetching data for taskId: ${this.taskId}`)
      this.traversalData = [] // Clear previous data
      try {
        const results = await this.getTraversalDataFromES(this.taskId)
        // Ensure results is an array before mapping
        if (Array.isArray(results)) {
          this.traversalData = results.map(hit => hit._source)
          console.log('Fetched Traversal Data:', JSON.stringify(this.traversalData, null, 2)) // Log processed data
        } else {
          console.error('getTraversalDataFromES did not return an array:', results)
          this.traversalData = []
        }
      } catch (error) {
        console.error('Error fetching traversal data:', error)
        this.$Message.error('获取遍历数据失败: ' + error.message)
        this.traversalData = []
      }
    },

    async getTraversalDataFromES(taskId) {
      const requestPayload = {
        'accesskey': 'Y29tLnNhbmt1YWkuYWllbmdpbmVlcmluZy5sbG1hcHAuZXM',
        'index': 'dfstraverse_log',
        'query': {
          'query': {
            'bool': {
              'must': [
                { 'term': { 'taskId': String(taskId) } }
              ],
              'must_not': [],
              'should': [],
              'filter': []
            }
          },
          'from': 0,
          'size': '1000',
          'sort': [
            { 'case_index': 'asc' }
          ],
          'profile': true
        }
      }
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Authorization': 'Basic Y29tLnNhbmt1YWkudGVzdGFiaWxpdHkubGxtOkJEMkQyM0IyREJDMTVDNzc3NTZFRkMzM0EyNjVCNTMz'
      }
      try {
        console.log(`Querying ES for taskId: ${taskId}`)
        const res = await this.$axios({
          method: 'post',
          url: this.esUrl,
          data: JSON.stringify(requestPayload),
          headers: requestHeaders
        })
        console.log('Raw ES Response:', JSON.stringify(res.data, null, 2))
        const hits = res.data && res.data.hits && res.data.hits.hits ? res.data.hits.hits : []
        console.log(`ES query returned ${hits.length} hits.`)
        return hits
      } catch (error) {
        console.warn(`Request to ${this.esUrl} failed. Retrying with ${this.esTestUrl}. Error:`, error)
      }
      // fallback
      try {
        const resFallback = await this.$axios({
          method: 'post',
          url: this.esTestUrl,
          data: JSON.stringify(requestPayload),
          headers: requestHeaders
        })
        console.log('Raw ES Test Response:', JSON.stringify(resFallback.data, null, 2))
        const hits = resFallback.data && resFallback.data.hits && resFallback.data.hits.hits ? resFallback.data.hits.hits : []
        console.log(`ES test query returned ${hits.length} hits.`)
        return hits
      } catch (fallbackError) {
        console.error(`Request to ${this.esTestUrl} also failed. Error:`, fallbackError)
        throw fallbackError
      }
    },

    getResultColor(resultClass) {
      if (resultClass === 0) return 'success'
      // Need to check result_reason in the component template since we only have resultClass here
      if (resultClass === 1 || resultClass === 2) return 'error'
      return 'default'
    },

    onImageError(event) {
      console.warn('Image failed to load:', event.target.src)
      event.target.style.display = 'none'
      const placeholder = document.createElement('span')
      placeholder.textContent = '无法加载'
      placeholder.className = 'image-error-placeholder'
      event.target.parentNode.appendChild(placeholder)
    },

    showImagePreview(url) {
      this.previewImageUrl = url
      this.imagePreviewVisible = true
    },

    handleFilterChange() {
      console.log('Filter changed to:', this.filterStatus)
      this.isLoading = true
      this.currentPage = 1 // Reset to first page when filter changes
      this.imageSrcs = {} // Clear all image srcs
      // Add a small delay to ensure loading state is visible
      setTimeout(() => {
        this.isLoading = false
      }, 300)
    },
    handlePageChange(page) {
      this.isLoading = true
      this.currentPage = page
      this.imageSrcs = {} // Clear all image srcs
      // Scroll to top when page changes
      window.scrollTo({
        top: this.$el.offsetTop - 60, // Subtract header height
        behavior: 'smooth'
      })
      // Add a small delay to ensure loading state is visible
      setTimeout(() => {
        this.isLoading = false
      }, 300)
    },
    handlePageSizeChange(size) {
      this.isLoading = true
      this.pageSize = size
      this.currentPage = 1 // Reset to first page when changing page size
      this.imageSrcs = {} // Clear all image srcs
      // Add a small delay to ensure loading state is visible
      setTimeout(() => {
        this.isLoading = false
      }, 300)
    }
  }
}
</script>

<style scoped>
.dfs-traversal-result-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  padding-top: 60px; /* Make space for the sticky header */
}

.filter-controls {
  margin-bottom: 20px;
  position: sticky;
  top: 0;
  background-color: #fff;
  padding: 16px;
  z-index: 10;
  border-bottom: 1px solid #e8eaec;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-left: -16px;
  margin-right: -16px;
  border-radius: 0;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 40px;
  font-size: 14px;
  background: #f8f8f9;
  border-radius: 4px;
}

.steps-timeline {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.step-card {
  border: 1px solid #e8eaec;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  background-color: #fff;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.step-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.step-success {
  border-left: 4px solid #19be6b;
}

.step-error {
  border-left: 4px solid #ed4014;
}

.step-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f9;
  border-bottom: 1px solid #e8eaec;
}

.step-number {
  font-weight: bold;
  font-size: 16px;
  margin-right: 15px;
}

.step-time {
  color: #808695;
  font-size: 14px;
  margin-right: 15px;
  flex-grow: 1;
}

.step-result {
  min-width: 60px;
  text-align: center;
}

.step-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-action {
  background-color: #f8f8f9;
  border-radius: 4px;
  padding: 12px;
}

.step-action h3 {
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #17233d;
}

.step-action div {
  margin-bottom: 8px;
  line-height: 1.5;
  word-break: break-word;
}

.step-screenshots {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.screenshot-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.image-wrapper {
  position: relative;
  width: 150px;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-container h3 {
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #17233d;
  align-self: center;
}

.screenshot {
  max-width: 150px;
  max-height: 250px;
  height: auto;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease;
  object-fit: contain;
}

.screenshot:hover {
  transform: scale(1.05);
}

.image-error-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #c5c8ce;
  font-size: 12px;
  border: 1px dashed #e8eaec;
  border-radius: 4px;
  width: 100%;
  height: 200px;
  box-sizing: border-box;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
  padding: 16px 0;
}

.spin-icon-load {
  animation: spin-rotate 1s linear infinite;
}

@keyframes spin-rotate {
  from { transform: rotate(0deg);}
  to { transform: rotate(360deg);}
}

@media (max-width: 992px) {
  .steps-timeline {
    grid-template-columns: 1fr;
  }
  
  .step-screenshots {
    flex-direction: column;
  }
  
  .screenshot-container {
    width: 100%;
  }
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px dashed #e8eaec;
  border-radius: 4px;
}
</style> 