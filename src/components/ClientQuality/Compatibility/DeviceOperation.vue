<template>
  <div style="min-width:800px;">
    <div style="width:100%;">
      <Input search style="width: 260px" v-model="filter.scopeOfUse" placeholder="输入业务方向，查询业务可用设备" @on-search="refreshPhoneList" @on-blur="refreshPhoneList" @on-clear="refreshPhoneList" clearable/>
      <Input search style="width: 160px" v-model="filter.business" placeholder="所属业务方向" @on-search="refreshPhoneList" @on-blur="refreshPhoneList" @on-clear="refreshPhoneList" clearable/>
      <Input search style="width: 260px" v-model="filter.sn" placeholder="查询指定SN的设备" @on-search="refreshPhoneList" @on-blur="refreshPhoneList" @on-clear="refreshPhoneList" clearable/>
      <Button @click="getPhoneList" type="primary">刷新</Button>
      <Button @click="addDeviceHandler" type="primary">添加新设备</Button>
    </div>
    <br>
    <Table :loading="loading" max-height="700" :data="phoneList" :columns="tableColumns" >
      <template slot-scope="{ row }" slot="connectType">
        <Tag v-if="row.connectType === 'local'">本地</Tag>
        <Tag v-else>云测</Tag>
      </template>
      <template slot-scope="{ row }" slot="deviceLabel">
        <Tag v-if="row.scopeOfUse.includes('public')">公用</Tag>
        <Tag v-else>{{ row.label }}</Tag>
      </template>
      <template slot-scope="{ row }" slot="deviceStatus">
        <tag v-if="row.status=='Idle'" color="success">空闲</tag>
        <tag v-else-if="row.status=='Fail'" color="error" :title="row.comment">故障</tag>
        <tag v-else-if="row.status=='Busy'" color="warning" :title="row.comment">忙碌</tag>
        <tag v-else color="default">下线</tag>
      </template>
      <template slot-scope="{ row }" slot="scopeOfUse">
        <template v-for="u in row.scopeOfUse.trim().split(',')">
          <Tag v-if="u" :title="u" :key="u">{{u}}</Tag>
        </template>
      </template>
      <template slot-scope="{ row }" slot="actions">
        <Button type="info" size="small" @click="editDeviceInfo(row)" title="编辑设备信息">
          <Icon type="md-create" />
        </Button>
        <Button type="error" size="small" @click="deleteDeviceHandler(row)" title="删除设备" :disabled="row.isAdmin==false">
          <Icon type="md-trash" />
        </Button>
        <Button type="success" v-if="row.status == 'Fail' || row.status == 'Busy'" size="small" @click="releaseDeviceHandler(row)" title="恢复设备状态为空闲" :disabled="isAdmin==false">
          <Icon type="md-heart" />
        </Button>
        <Button type="error" v-if="row.status != 'Offline'" size="small" @click="changeWorkStatus(row, 'Offline')" title="下线设备" :disabled="row.isAdmin==false">
          <Icon type="md-cloud-download" />
        </Button>
        <Button type="success" v-if="row.status == 'Offline'" size="small" @click="changeWorkStatus(row, 'Idle')" title="上线设备" :disabled="row.isAdmin==false">
          <Icon type="md-cloud-upload" />
        </Button>
        <Button type="success" size="small" @click="showDeviceTaskList(row)" title="设备的任务列表">
          <Icon type="logo-tumblr" />
        </Button>
      </template>
    </Table>
    <div style="margin-left: 50%; margin-top: 20px;">
        <Page :total="total" :current="filter.pageNumber" :page-size="filter.pageSize" :page-size-opts="pageSizeOpts"
            show-sizer show-total  @on-change="changePageNumber" @on-page-size-change="changePageSize"/>
    </div>
    <Modal
      v-model="showDeviceInfoModal"
      width="900"
      :mask-closable="false"
      :closable="false"
    >
      <p slot="header">
          <span v-if="showCreateModal">添加新设备</span>
          <span v-if="showEditModal">编辑设备信息</span>
      </p>
      <div style="text-align:left;padding-left: 10px">
        <div style="display: flex;">
          <div style="width:45%">
              <p class="item">
                  <span class="item_title">设备类型: </span>
                  <RadioGroup v-model="deviceInfo.platform">
                  <Radio  v-for="plat in platformOptions" :label="plat.name" :key="plat.name" :disabled="showEditModal">
                      <span>{{ plat.label }}</span>
                  </Radio>
                  </RadioGroup>
              </p>
              <p class="item">
                  <span class="item_title">设备label: </span>
                  <RadioGroup v-model="deviceInfo.label">
                  <Radio  v-for="devicelabel in ['专用', '临时']" :label="devicelabel" :key="devicelabel">
                      <span>{{ devicelabel }}</span>
                  </Radio>
                  </RadioGroup>
              </p>
              <p class="item">
                  <span class="item_title">品牌:</span>
                  <Input v-model="deviceInfo.brand" clearable placeholder="必填，设备品牌" :disabled="showEditModal"></Input>
              </p>
              <p class="item">
                  <span class="item_title">型号:</span>
                  <Input v-model="deviceInfo.model" clearable placeholder="必填，设备型号" :disabled="showEditModal"></Input>
              </p>
              <p class="item">
                  <span class="item_title">版本:</span>
                  <Input v-model="deviceInfo.version" clearable placeholder="必填，设备系统版本"></Input>
              </p>
              <p class="item">
                  <span class="item_title">SN:</span>
                  <Input v-model="deviceInfo.sn" clearable placeholder="必填，设备SN" :disabled="showEditModal"></Input>
              </p>
              <p class="item">
                  <span class="item_title">分辨率:</span>
                  <Input v-model="deviceInfo.screen" clearable placeholder="必填，设备分辨率：宽x高" :disabled="showEditModal"></Input>
              </p>
          </div>
          <div style="width:45%;margin-left: 40px;">
              <p class="item">
                  <span class="item_title">连接方式: </span>
                  <RadioGroup v-model="deviceInfo.connectType">
                  <Radio  v-for="connectType in connectTypeOptions" :label="connectType.value" :key="connectType.value">
                      <span>{{ connectType.label }}</span>
                  </Radio>
                  </RadioGroup>
              </p>
              <p class="item">
                  <span class="item_title">slave:</span>
                  <Input v-model="deviceInfo.slave" clearable placeholder="必填，执行机IP"></Input>
              </p>
              <p v-if="deviceInfo.platform=='iOS'" class="item">
                  <span class="item_title">port:</span>
                  <Input v-model="deviceInfo.WDAPort" clearable placeholder="iOS WDA转发端口，例如：4950"></Input>
              </p>
              <p class="item">
                  <span class="item_title">运维负责人:</span>
                  <Select v-model="deviceInfo.userList" multiple filterable placeholder="请选择" :max-tag-count="3" size="small">
                    <Option v-for="user in userList" :value="user" :key="user">{{ user }}</Option>
                  </Select>

              </p>
          </div>
        </div>
        <div>
          <p class="item" style="width:45%">
              <span class="item_title">关联设备:</span>
              <Input v-model="deviceInfo.extension" clearable placeholder="用于关联不同型号同分辨率的设备，共享基准图"></Input>
          </p>
          <p class="item" style="width:45%">
              <span class="item_title">归属业务:</span>
              <Select v-model="deviceInfo.business" filterable clearable placeholder="必填，设备归属业务方向，与MMCD保持一致" @on-change="changeDeviceBusiness">
                <Option v-for="businessInfo in businessList" :key="businessInfo.business" :value="businessInfo.business">{{businessInfo.business}}</Option>
              </Select>
          </p>
          <p class="item">
            <span class="item_title">可使用范围:</span>
            <div style="display: flex; flex-wrap: wrap;">
              <Tag v-for="scope in scopeOfUseList" :key="scope" :name="scope" closable @on-close="handleScopeTagClose" >{{scope}}</Tag>
            </div>
            <Select v-model="useScopeOption.business" size="small" style="width: 100px;" filterable clearable placeholder="业务线">
              <Option v-for="businessInfo in businessUseList" :key="businessInfo.business" :value="businessInfo.business">{{businessInfo.label}}</Option>
            </Select>
            <Select v-model="useScopeOption.jobTriggerType" size="small" placeholder="触发类型" clearable style="width: 100px" >
              <Option v-for="option in jobTriggerTypeOptions" :key="option.value" :value="option.value" :label="option.value">{{ option.label }}</Option>
            </Select>
            <Select v-model="useScopeOption.jobType" size="small"  placeholder="测试类型" clearable style="width: 100px;">
              <Option v-for="option in jobTypeOption" :key="option.label" :value="option.value" :label="option.value">{{ option.label }}</Option>
            </Select>
            <Select v-model="useScopeOption.deviceCategoryType" size="small" placeholder="被测对象宿主" clearable style="width: 150px">
              <Option v-for="option in deviceCategoryOptions" :key="option.value" :value="option.value" :label="option.value">{{ option.label }}</Option>
            </Select>
            <Select v-model="useScopeOption.priority" size="small" placeholder="优先级" clearable style="width: 100px;">
              <Option v-for="option in priorityOptions" :key="option.key" :value="option.key" :label="option.key">{{ option.value }} </Option>
            </Select>
            <Button style="margin-left:10px;" icon="ios-add" size="small" type="dashed" @click="addScope2">添加</Button>
            <a href="https://km.sankuai.com/page/1426471346" target="_blank">
              <Icon type="md-help-circle" />
            </a>
          </p>
        </div>
      </div>
      <p slot="footer">
        <Button type="text" size="large" @click="cancelModal">取消</Button>
        <Button v-if="showCreateModal" type="primary" size="large" @click="addDeviceInfo" :disabled="deviceInfo.isAdmin==false">确定</Button>
        <Button v-if="showEditModal" type="primary" size="large" @click="updateDeviceInfo" :disabled="deviceInfo.isAdmin==false">保存</Button>
      </p>
    </Modal>
    <Modal
        v-model="deviceStatusModal"
        title="修改设备状态"
        @on-cancel="deviceStatusModal=false">
        <p v-if="deviceStatus === 'Idle'">
          <span class="item_title">确定要将故障设备恢复为空闲吗？</span><br/>
          <br/>
          <span>型号：{{deviceInfo.model}}</span><br/>
          <span>SN：{{deviceInfo.sn}}</span><br/>
          <br/>
          <span style="color: red;">恢复前，请务必确认正在运行的任务已终止</span><br/>
          <span class="item_title">故障恢复备注:</span>
          <Input v-model="comment" clearable type="textarea" placeholder="必填，标记原因，方便追踪"></Input>
        </p>
        <p v-else-if="deviceStatus === 'Offline'">
          <span class="item_title">确定要下线该设备吗？</span><br/>
          <br/>
          <span>型号：{{deviceInfo.model}}</span><br/>
          <span>SN：{{deviceInfo.sn}}</span><br/>
          <br/>
          <span class="item_title">下线原因:</span>
          <Input v-model="comment" clearable type="textarea" placeholder="必填，标记原因，方便追踪"></Input>
        </p>
        <p v-else-if="deviceStatus === 'Online'">
          <span class="item_title">确定要上线该设备吗？</span><br/>
          <br/>
          <span>型号：{{deviceInfo.model}}</span><br/>
          <span>SN：{{deviceInfo.sn}}</span><br/>
          <br/>
        </p>
        <p slot="footer">
        <Button type="text" size="large" @click="deviceStatusModal=false">取消</Button>
        <Button type="primary" size="large" @click="saveDeviceStatus" :disabled="deviceInfo.writePermission==false">确定</Button>
      </p>
    </Modal>
  </div>
</template>

/* eslint-disable */
<script>
import { Bus } from '@/global/bus'
export default {
  name: 'deviceOperation',
  props: [],
  components: {},
  data() {
    return {
      filter: {
        scopeOfUse: null,
        pageSize: 10,
        pageNumber: 1,
        sn: null,
        slave: null,
        business: null
      },
      useScopeOption: {
        business: null,
        jobTriggerType: null,
        jobType: null,
        deviceCategoryType: null,
        priority: 4
      },
      connectTypeOptions: [{label: '本地自建', value: 'local'}, {label: '云测', value: 'remote'}],
      platformOptions: [],
      jobTriggerTypeOptions: [],
      jobTypeOption: [],
      deviceCategoryOptions: [],
      priorityOptions: [
        { key: '1', value: '1-低优任务，设备拆借' },
        { key: '2', value: '2-较低优任务，设备拆借' },
        { key: '3', value: '3-日常任务，普通优先级任务' },
        { key: '4', value: '4-较高优任务，默认' },
        { key: '5', value: '5-临时高优先级任务，突发性高优任务' }
      ],
      pageSizeOpts: [10, 30, 100],
      total: 0,
      phoneList: [],
      businessUseList: [],
      loading: false,
      tableColumns: [
        {
          title: 'SN',
          key: 'sn',
          fixed: 'left',
          align: 'center'
        },
        {
          title: '型号',
          key: 'model',
          fixed: 'left',
          align: 'center',
          filters: [],
          filterMethod (value, row) {
            return row.model === value
          }
        },
        {
          title: '版本',
          key: 'version',
          align: 'center'
        },
        {
          title: '分辨率',
          key: 'screen',
          align: 'center'
        },
        {
          title: '归属业务',
          key: 'business',
          align: 'center'
        },
        {
          title: '接入方式',
          slot: 'connectType',
          align: 'center',
          width: 120,
          filters: [{label: '本地自建', value: 'local'}, {label: '云测', value: 'remote'}],
          filterMethod (value, row) {
            return row.connectType === value
          }
        },
        {
          title: '设备标签',
          slot: 'deviceLabel',
          align: 'center',
          width: 120,
          filters: [{label: '专用', value: '专用'}, {label: '临时', value: '临时'}, {label: '公用', value: '公用'}],
          filterMethod (value, row) {
            let label = row.label
            if (row.scopeOfUse.includes('public')) {
              label = '公用'
            }
            return value === label
          }
        },
        {
          title: '执行机IP',
          key: 'slave',
          align: 'center',
          filters: [],
          filterMethod (value, row) {
            return row.slave === value
          }
        },
        {
          title: '可用业务方向',
          slot: 'scopeOfUse',
          align: 'center',
          fixed: 'right',
          width: 300
        },
        {
          title: '状态',
          slot: 'deviceStatus',
          fixed: 'right',
          align: 'center',
          width: 100
        },
        {
          title: '操作',
          slot: 'actions',
          fixed: 'right',
          align: 'center',
          width: 250
        }
      ],
      user: Bus.userInfo.userLogin,
      defaultDeviceInfo: {
        platform: 'android',
        brand: '',
        model: '',
        version: '',
        sn: '',
        screen: '',
        extension: '',
        connectType: 'local',
        label: '临时',
        slave: '',
        port: 0,
        business: '',
        userList: [Bus.userInfo.userLogin]
      },
      deviceInfo: {},
      scopeOfUseList: [],
      businessListNew: [],
      label: '',
      inputScope: false,
      newScope: null,
      showDeviceInfoModal: false,
      showEditModal: false,
      showCreateModal: false,
      businessList: [],
      business: null,
      myBusiessList: [],
      isAdmin: false,
      deviceStatusModal: false,
      deviceStatus: null,
      comment: '',
      userList: [Bus.userInfo.userLogin],
      taskFilter: {
        sn: null
      },
      adminPermisBusiness: new Map()
    }
  },
  mounted: function() {
    this.getTestItems()
    this.deviceInfo = this.defaultDeviceInfo
    this.getBusinessList(this.user).then((res) => {
      this.myBusinessList = res.data
      this.refreshPhoneList()
    })

    this.getBusinessList().then((res) => {
      this.businessList = res.data
      this.businessUseList = [{
        label: '公共设备',
        business: 'public'
      }].concat(this.businessList)
    })
    // 从搜索框读入URL，并读取SN
    let param = new URLSearchParams(window.location.search)
    let sn = param.get('sn')
    // 判断读取SN是否为空
    if (sn) {
      this.filter.sn = sn
    }
  },
  methods: {
    refreshPhoneList: function() {
      this.filter.pageNumber = 1
      this.getPhoneList()
    },
    editDeviceInfo: function(phoneInfo) {
      let _this = this
      this.deviceInfo = JSON.parse(JSON.stringify(phoneInfo))
      if (!phoneInfo.isAdmin) {
        let desc = '无权限编辑保存未标记归属业务方向的设备信息，仅超级管理员和设备运维人员可操作'
        if (phoneInfo.business !== '') {
          desc = '无权限编辑保存【' + phoneInfo.business + '】业务方向设备信息，仅超级管理员或业务方向管理员可操作'
        }
        _this.$Notice.warning({title: '无编辑权限，仅供查看', desc: desc, duration: 10})
      }
      _this.scopeOfUseList = _this.deviceInfo.scopeOfUse.split(',')
      _this.showEditModal = true
      _this.showDeviceInfoModal = true
      if (phoneInfo.userList.includes(_this.user)) {
        _this.userList = phoneInfo.userList
      } else {
        _this.userList = phoneInfo.userList.concat([this.user])
      }
      _this.getBusinessByName(phoneInfo.business).then((res) => {
        let businessId = res.data.id
        _this.getUsersByBusinessName(businessId)
      })
    },
    releaseDeviceHandler: function(phoneInfo) {
      let _this = this
      if (!phoneInfo.isAdmin) {
        let desc = '无权限释放未标记归属业务方向的设备信息，仅超级管理员可操作'
        if (phoneInfo.business !== '') {
          desc = '无权限释放【' + phoneInfo.business + '】业务方向设备，仅超级管理员或业务方向管理员可操作'
        }
        _this.$Notice.error({title: '无权限编辑设备状态', desc: desc, duration: 8})
        return false
      }
      _this.deviceStatusModal = true
      _this.deviceStatus = 'Idle'
      _this.deviceInfo = JSON.parse(JSON.stringify(phoneInfo))
    },
    changeWorkStatus: function(phoneInfo, status) {
      let _this = this
      if (!phoneInfo.isAdmin) {
        let desc = '无权上/下线未标记归属业务方向的设备信息，仅超级管理员和设备运维人员可操作'
        if (phoneInfo.business !== '') {
          desc = '无权限上线或下线【' + phoneInfo.business + '】业务方向设备，仅超级管理员或业务方向管理员可操作'
        }
        _this.$Notice.error({title: '无权限上线或下线设备', desc: desc, duration: 8})
        return false
      }
      _this.deviceStatusModal = true
      _this.deviceStatus = status
      _this.deviceInfo = JSON.parse(JSON.stringify(phoneInfo))
    },
    changePageNumber: function(number) {
      this.filter.pageNumber = number
      this.getPhoneList()
    },
    changePageSize: function(pageSize) {
      this.filter.pageSize = pageSize
      this.getPhoneList()
    },
    showDeviceTaskList: function(phoneInfo) {
      let href = '/microscope/taskList?sn=' + phoneInfo.sn
      window.open(href, '_blank')
    },
    async getBusinessByName(business) {
      return this.$axios({
        method: 'get',
        params: {
          businessName: business
        },
        url: this.env.url + 'autoTestConfig/getBusinessByBusinessName'
      }).catch(function (error) {
        console.log(error)
      })
    },
    getUsersByBusinessName: function(businessId) {
      this.$axios({
        method: 'get',
        params: {
          'businessId': businessId
        },
        url: this.env.url + 'autoTestConfig/getUsersByBusinessId/'
      }).then((res) => {
        let message = res.data
        this.testItemLabel = message
        let testItemOptions = new Set()
        for (let i = 0; i < message.managers.length; i++) {
          testItemOptions.add(message.managers[i].misId)
        }
        for (let i = 0; i < message.members.length; i++) {
          testItemOptions.add(message.members[i].misId)
        }
        testItemOptions.add(Bus.userInfo.userLogin)
        this.userList = [...testItemOptions]
      }).catch(function (error) {
        console.log(error)
      })
    },
    getTestItems: function() {
      this.$axios({
        method: 'get',
        url: this.env.url + 'page/getTestItem'
      }).then((res) => {
        let message = res.data
        if (Array.isArray(message)) {
          this.testItemLabel = message
          let testItemOptions = []
          for (let i = 0; i < message.length; i++) {
            if (message[i].frame !== 1) {
              testItemOptions.push({'label': message[i].label, 'value': message[i].name})
            }
          }
          if (testItemOptions.length > 0) {
            this.jobTypeOption = testItemOptions
          }
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    getPhoneList: function() {
      this.loading = true
      let _this = this
      this.$axios({
        method: 'get',
        params: {
          'sn': this.filter.sn,
          'slave': this.filter.slave,
          'business': this.filter.business,
          'scopeOfUse': this.filter.scopeOfUse,
          'ps': this.filter.pageSize,
          'pn': this.filter.pageNumber
        },
        url: this.env.url + 'compatibilityPhone/phone/list'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          let phoneList = message.data.list
          this.total = message.data.total
          let modelKeyFilter = new Set()
          let slaveKeyFilter = new Set()
          let modelFilter = []
          let slaveFilter = []
          phoneList.forEach((phone) => {
            if (modelKeyFilter.has(phone.model) === false) {
              modelKeyFilter.add(phone.model)
              modelFilter.push({
                label: phone.model,
                value: phone.model
              })
            }
            if (slaveKeyFilter.has(phone.slave) === false) {
              slaveKeyFilter.add(phone.slave)
              slaveFilter.push({
                label: phone.slave,
                value: phone.slave
              })
            }
            phone.writePermission = this.hasWritePermission(phone)
            phone.isAdmin = false
            if (phone.userList.includes(Bus.userInfo.userLogin)) {
              phone.isAdmin = true
            } else {
              if (_this.adminPermisBusiness.has(phone.business)) {
                phone.isAdmin = _this.adminPermisBusiness.get(phone.business)
              } else {
                _this.hasAdminPermission(phone.business).then(function() {
                  phone.isAdmin = _this.isAdmin
                  _this.adminPermisBusiness.set(phone.business, _this.isAdmin)
                })
              }
            }
          })
          this.tableColumns.forEach((tableColumn) => {
            if (tableColumn.key) {
              if (tableColumn.key === 'slave') {
                tableColumn.filters = slaveFilter
              } else if (tableColumn.key === 'model') {
                tableColumn.filters = modelFilter
              }
            }
          })
          this.phoneList = phoneList
          this.platformOptions = message.data.platformOptions
          this.jobTriggerTypeOptions = message.data.jobTriggerOptions.map(item => ({
            label: item.label,
            value: item.name
          }))
          this.deviceCategoryOptions = message.data.deviceCategoryOptions.map(item => ({
            label: item.label,
            value: item.name
          }))
        } else {
          this.$Notice.error({
            title: '获取设备信息失败！'
          })
        }
        this.loading = false
      }).catch((error) => {
        console.log(error)
        this.loading = false
      })
    },
    saveDeviceStatus: function() {
      if (this.deviceStatus === 'Idle' || this.deviceStatus === 'Offline') {
        if (this.comment === '') {
          this.$Notice.error({title: '原因不能为空'})
          return false
        }
      }
      if (this.deviceStatus === 'Idle' || this.deviceStatus === 'Offline') {
        this.updateDeviceStatus(this.deviceInfo.id, this.deviceStatus)
      } else {
        console.log('不能将设备状态更改为' + this.deviceStatus)
      }
    },
    updateDeviceStatus: function(id, status) {
      this.$axios({
        method: 'post',
        data: {
          phoneId: id,
          status: status,
          reason: this.comment,
          user: this.user
        },
        url: this.env.url + 'compatibilityPhone/status/' + id
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: '更新设备状态成功！'
          })
          this.deviceStatusModal = false
          this.getPhoneList()
        } else {
          this.$Notice.error({
            title: '更新设备状态失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    checkDeviceInfo: function() {
      if (this.deviceInfo.brand === '') {
        this.$Notice.error({title: '请填写设备品牌信息！'})
        return false
      }
      if (this.deviceInfo.model === '') {
        this.$Notice.error({title: '请填写设备型号信息！'})
        return false
      }
      if (this.deviceInfo.version === '') {
        this.$Notice.error({title: '请填写设备系统版本信息！'})
        return false
      }
      if (this.deviceInfo.sn === '') {
        this.$Notice.error({title: '请填写设备SN信息！'})
        return false
      }
      if (this.deviceInfo.screen === '' || !/\d+x\d+/.test(this.deviceInfo.screen)) {
        this.$Notice.error({title: '请正确填写设备的屏幕分辨率！'})
        return false
      }
      if (this.deviceInfo.platform === 'iOS' && !/\d+/.test(this.deviceInfo.WDAPort)) {
        this.$Notice.error({title: 'iOS需要填写WDA的转发接口！'})
        return false
      }
      if (!/\d+\.\d+\.\d+\.\d+/.test(this.deviceInfo.slave)) {
        this.$Notice.error({title: '请正确填写执行机IP！'})
        return false
      }
      let ipCheck = true
      this.deviceInfo.slave.split('.').forEach((item) => {
        let val = parseInt(item)
        if (val < 0 || val > 255) {
          ipCheck = false
        }
      })
      if (!ipCheck) {
        this.$Notice.error({title: '请正确填写执行机IP！'})
        return false
      }
      if (this.deviceInfo.business === '') {
        this.$Notice.error({title: '请选择设备归属业务！'})
        return false
      }
      if (this.scopeOfUseList.length <= 0) {
        this.$Notice.error({title: '请填写设备可用的业务范围！'})
        return false
      }
      if (this.deviceInfo.userList.length <= 0) {
        this.$Notice.error({title: '请填写设备运维人员信息！'})
        return false
      }
      if (this.deviceInfo.label === '') {
        this.$Notice.error({title: '请选择设备的标签！'})
        return false
      }
      this.deviceInfo.WDAPort = parseInt(this.deviceInfo.WDAPort)
      this.deviceInfo.scopeOfUse = this.scopeOfUseList.join(',')
    },
    addDeviceInfo: function() {
      if (this.checkDeviceInfo() === false) {
        return false
      }
      this.$axios({
        method: 'post',
        data: this.deviceInfo,
        url: this.env.url + 'compatibilityPhone/phone'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: '添加设备成功！'
          })
          this.showCreateModal = false
          this.showDeviceInfoModal = false
          this.filter.sn = this.deviceInfo.sn
          this.refreshPhoneList()
        } else {
          this.$Notice.error({
            title: '添加设备失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    updateDeviceInfo: function() {
      if (this.checkDeviceInfo() === false) {
        return false
      }
      this.$axios({
        method: 'post',
        data: this.deviceInfo,
        url: this.env.url + 'compatibilityPhone/setPhone'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: message.message
          })
          this.showEditModal = false
          this.showDeviceInfoModal = false
          this.getPhoneList()
        } else {
          this.$Notice.error({
            title: '更新设备失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    deleteDevice: function(id, sn) {
      this.$axios({
        method: 'delete',
        params: {
          phoneId: id,
          user: this.user,
          comment: 'SN is ' + sn
        },
        url: this.env.url + 'compatibilityPhone/phone/' + id
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: '删除设备成功！'
          })
          this.deviceStatusModal = false
          this.getPhoneList()
        } else {
          this.$Notice.error({
            title: '删除设备失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    getBusinessList: function(misId) {
      return this.$axios({
        method: 'get',
        params: {
          misId: misId
        },
        url: this.env.url + 'autoTestConfig/getAllBusinessName'
      }).catch(function (error) {
        console.log(error)
      })
    },
    cancelModal: function() {
      this.showEditModal = false
      this.showCreateModal = false
      this.showDeviceInfoModal = false
    },
    addDeviceHandler: function() {
      this.showCreateModal = true
      this.showDeviceInfoModal = true
      this.deviceInfo = JSON.parse(JSON.stringify(this.defaultDeviceInfo))
      this.scopeOfUseList = []
    },
    hasAdminPermission: function(business) {
      let businessId = 490
      if (business) {
        let businessInfo = this.businessList.find(businessInfo => businessInfo.business === business)
        if (businessInfo) {
          businessId = businessInfo.id
        }
      }
      this.isAdmin = false
      let _this = this
      return this.$axios({
        method: 'get',
        params: {
          misId: this.user,
          businessId: businessId
        },
        url: this.env.url + '/autoTestConfig/getUserPermission'
      }).then((res) => {
        // 超级管理员或者业务管理员
        _this.isAdmin = res.data.isAdmin || res.data.permissionLevel === 10
      }).catch(function (error) {
        console.log(error)
      })
    },
    hasWritePermission: function(phoneInfo) {
      if (phoneInfo.isAdmin) {
        return true
      } else {
        let business = phoneInfo.business
        let writePermission = false
        this.myBusinessList.forEach(businessInfo => {
          if (businessInfo.business === business) {
            writePermission = true
          }
        })
        return writePermission
      }
    },
    deleteDeviceHandler: function(phoneInfo) {
      if (!phoneInfo.isAdmin) {
        let desc = '无权限删除未标记归属业务方向的设备信息，仅超级管理员可操作'
        if (phoneInfo.business !== '') {
          desc = '无权限删除【' + phoneInfo.business + '】业务方向设备，仅超级管理员或业务方向管理员可操作'
        }
        this.$Notice.error({title: '无权限删除设备', desc: desc, duration: 8})
        return false
      }
      this.$Modal.confirm({
        title: '确定要删除该设备吗？',
        content: '<p><span>型号：' + phoneInfo.model + '</span><br/><span>SN：' + phoneInfo.sn + '</span></p>',
        onOk: () => {
          this.deleteDevice(phoneInfo.id)
        }
      })
    },
    handleScopeTagClose: function(event, name) {
      let index = this.scopeOfUseList.indexOf(name)
      this.scopeOfUseList.splice(index, 1)
    },
    addScope2() {
      if (!this.useScopeOption.business || this.useScopeOption.business === '') {
        this.$Notice.error({title: '添加的业务方向不能为空'})
      }
      if (!this.useScopeOption.jobTriggerType || this.useScopeOption.jobTriggerType === '') {
        this.$Notice.error({title: '添加的触发类型不能为空'})
      }
      if (!this.useScopeOption.jobType || this.useScopeOption.jobType === '') {
        this.$Notice.error({title: '添加的测试类型不能为空'})
      }
      if (!this.useScopeOption.deviceCategoryType || this.useScopeOption.deviceCategoryType === '') {
        this.$Notice.error({title: '添加的被测对象宿主不能为空'})
      }
      if (!this.useScopeOption.priority || this.useScopeOption.priority === '') {
        this.$Notice.error({title: '添加的优先级不能为空'})
      }
      let newScope = [this.useScopeOption.business, this.useScopeOption.jobTriggerType, this.useScopeOption.jobType, this.useScopeOption.deviceCategoryType, this.useScopeOption.priority]
      newScope = newScope.join('-')
      this.scopeOfUseList.push(newScope)
      this.useScopeOption.business = null
      this.useScopeOption.jobTriggerType = null
      this.useScopeOption.jobType = null
      this.useScopeOption.deviceCategoryType = null
      this.useScopeOption.priority = null
    },

    closeDeviceInfoModal: function() {
      this.showCreateModal = false
      this.showEditModal = false
      this.showDeviceInfoModal = false
    },
    changeDeviceBusiness: function(business) {
      if (!this.deviceInfo.isAdmin) {
        let desc = '无权限修改未标记归属业务方向的设备信息，仅超级管理员和设备运维人员可操作'
        if (business !== '') {
          desc = '无权限修改【' + business + '】业务方向设备，仅超级管理员或业务方向管理员可操作'
        }
        this.$Notice.error({title: '无权限修改设备', desc: desc, duration: 8})
        return false
      }
      this.getBusinessByName(business).then((res) => {
        let businessId = res.data.id
        this.getUsersByBusinessName(businessId)
      })
    }
  }
}
</script>

<style scoped>
  .item {
    margin: 10px 0px;
    display: flex;
    flex-wrap:wrap;
  }
  .item_title {
    font-weight: 600;
    width: 90px;
  }
</style>
