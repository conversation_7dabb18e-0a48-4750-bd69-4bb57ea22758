<template>
  <div>
    <Modal v-model="modalShow" :title="modalTitle" width="800" @on-cancel="exitJonConfigEditor">
      <Form :label-width="85">
        <Form-item label="名称：">
          <Row>
            <Col span="22">
               <p>{{ jobInfo.job_name }}</p>
            </Col>
          </Row>
        </Form-item>
        <Form-item >
          <span slot="label">
            <span class="span-box">
              <span>是否开启：</span>
            </span>
          </span>
          <Row>
            <Col span="2">
              <label>
                <input type="radio" value="yes" v-model="isEnable"> 是
              </label>
            </Col>
            <Col span="2">
              <label>
                <input type="radio" value="no" v-model="isEnable"> 否
              </label>
            </Col>
          </Row>
        </Form-item>
        <Form-item v-show="isEnable === 'yes'" label="推送人员：">
          <el-input
            v-model="notifyList"
            placeholder="请输入通知人的mis号，用','分割"
            style="width: 400px; margin-right: 40px;">
          </el-input>
        </Form-item>
        <Form-item v-show="isEnable === 'yes'" label="触发周期：">
          <div>
            <el-form ref="scheduleForm" :model="scheduleForm" label-width="100px">
              <!-- 星期选择 -->
              <el-form-item label="触发日期：">
                <el-checkbox-group v-model="scheduleForm.days">
                  <el-checkbox
                    v-for="day in days"
                    :key="day.value"
                    :label="day.value">
                    {{ day.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <!-- 时间范围选择（小时） -->
              <el-form-item label="触发时间：">
                <el-select
                  v-model="scheduleForm.selectedTimes"
                  multiple
                  filterable
                  placeholder="请选择触发时间，可搜索选择时间"
                  style="width: 500px;">
                  <el-option
                    v-for="time in times"
                    :key="time.key"
                    :label="time.key"
                    :value="time.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </Form-item>
        <Form-item v-if="isEnable === 'yes'" label="策略配置：">
          <div class="strategy-selector">
            <el-form :model="strategyForm" label-width="120px">
             
              <!-- 设备触发策略 select -->
              <el-form-item label="设备触发策略：">
                <!-- 使用flex布局将el-select和el-input放在同一行 -->
                <div style="display: flex; align-items: center;">
                  <el-select
                    v-model="strategyForm.deviceSelect"
                    placeholder="请选择设备触发策略"
                    style="width: 200px; margin-right: 10px;">
                    <el-option
                      v-for="option in deviceStrategies"
                      :key="option.name"
                      :label="option.label"
                      :value="option.name">
                    </el-option>
                  </el-select>
                  <!-- 选择按照型号触发显示的输入框 -->
                  <el-input
                    v-if="strategyForm.deviceSelect === 'SPECIFIC_MODEL'"
                    v-model="strategyForm.device_model"
                    placeholder="请输入设备型号,用','分割"
                    style="width: 300px; margin-right: 40px;">
                  </el-input>
                  <!-- 选择自己勾选设备触发，显示下拉框，触发获取信息的函数 -->
                  <el-select
                    v-if="strategyForm.deviceSelect === 'SPECIFIC_SN'"
                    multiple
                    filterable
                    v-model="strategyForm.device_sn"
                    placeholder="请选择定时触发设备，可搜索选择"
                    style="width: 300px; margin-right: 40px;">
                    <el-option
                      v-for="option in deivce_info"
                      :key="option.name"
                      :label="option.label"
                      :value="option.value">
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>

              <!-- 页面选择策略 select -->
              <el-form-item label="页面选择策略：">
                <div style="display: flex; align-items: center;">
                  <el-select v-model="strategyForm.pageStrategy" placeholder="请选择页面选择策略" style="width: 200px; margin-right: 10px;">
                    <el-option
                      v-for="option in pageStrategies"
                      :key="option.name"
                      :label="option.label"
                      :value="option.name">
                    </el-option>
                  </el-select>
                  <el-input
                    v-if="strategyForm.pageStrategy === 'KEY_WORD'"
                    v-model="strategyForm.page_keyword"
                    placeholder="请输入页面匹配的关键字"
                    style="width: 300px; margin-right: 40px;">
                  </el-input>
                  <el-input
                    v-if="strategyForm.pageStrategy === 'PAGE_ID'"
                    v-model="strategyForm.page_pageIds"
                    placeholder="请输入pageId，用','分割"
                    style="width: 300px; margin-right: 40px;">
                  </el-input>
                </div>
                <div v-if="strategyForm.pageStrategy === 'SPECIFIC'" class="strategy-hint">
                    <i class="el-icon-warning"></i>按照任务配置中的页面选择信息进行触，如果想要修改需要在任务配置管理中进行
                </div>
                <div v-if="strategyForm.pageStrategy === 'KEY_WORD'" class="strategy-hint">
                  <i class="el-icon-warning"></i>关键字的输入必须是页面名称中出现的，如果无法命中，会按照任务配置中的页面选择信息进行触发
                </div>
                <div v-if="strategyForm.pageStrategy === 'PAGE_ID'" class="strategy-hint">
                  <i class="el-icon-warning"></i>pageId必须在配置中的业务方向中存在
                </div>
              </el-form-item>

              <!-- 安装包选择策略 select -->
              <el-form-item label="安装包选择：">
                <el-select v-model="strategyForm.pkgSelect" placeholder="请选择安装包选择策略">
                  <el-option
                    v-for="option in packageStrategies"
                    :key="option.name"
                    :label="option.label"
                    :value="option.name">
                  </el-option>
                </el-select>
                <div v-if="strategyForm.pkgSelect === 'SPECIFIC'" class="strategy-hint">
                  <i class="el-icon-warning"></i>自定义测试包，需要在任务配置管理中设置
                </div>
              </el-form-item>
            </el-form>
          </div>
        </Form-item>
      </Form>
      <div slot="footer">
        <Button type="primary" size="small" @click="addUserJob"
          >保存配置</Button
        >
        <Button type="info" size="small" @click="exitJonConfigEditor"
          >取消</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import vueJsonEditor from "vue-json-editor";
export default {
  name: "UserCronConfigModal",
  components: { vueJsonEditor },
  props: ["show", "jobInfo"],
  data() {
    return {
      isget: null, // 用于判断是否拿到对应定时任务信息
      isEnable: 'no',
      notifyList: "",
      scheduleForm: {
        days: [], // 选中的星期
        selectedTimes: [], // 选中的时间点
      },
      strategyForm: {
        device_sn: [],
        device_model: '',
        page_keyword: '',
        page_pageIds: '',
        deviceSelect: 'SPECIFIC_SN',      // 设备触发策略选中的值
        pageStrategy: 'SPECIFIC',        // 页面选择策略选中的值
        pkgSelect: 'SPECIFIC',    // 安装包选择策略选中的值
      },
      times: [],
      days: [
        { value: '1', label: '周一' },
        { value: '2', label: '周二' },
        { value: '3', label: '周三' },
        { value: '4', label: '周四' },
        { value: '5', label: '周五' },
        { value: '6', label: '周六' },
        { value: '7', label: '周日' },
      ],
      deivce_info: [],
      deviceStrategies: [],
      pageStrategies: [],
      packageStrategies: [],
      modalTitle: "编辑定时触发配置",
      modalShow: false,
    };
  },
  computed: {
  },
  mounted() {
  },
  watch: {
    isget: function (newVal, oldVal){
      this.initTimeJobInfo()
    },
    show: function (newVal, oldVal) {
      this.modalShow = newVal
      if (newVal) {
        // 方式数据不刷新
        this.isget = null
        this.initTimes()
        this.getDeviceInfo(this.jobInfo)
        this.getTimeJobInfo(this.jobInfo.id)
      }
    },
    immediate: true // 如果需要在组件创建时立即执行
  },
  methods: {
    exitJonConfigEditor() {
      this.$emit("closeModal");
    },
    addUserJob() {
      const scheduleForm = this.scheduleForm
      const strategyForm = this.strategyForm
      if (this.isget === false) {
        // 新增数据
        // 检测数据是否正常
        const checkResult= this.checkData(scheduleForm,strategyForm)
        // 调用新增函数函数
        if (checkResult){
          let data = this.assemblyData(scheduleForm, strategyForm)
          this.insertTimeJobInfo(data)
        }
      } else if(this.isget === true){
        // 更新数据
        //检测数据是否正常并返回组装好的数据
        const checkResult = this.checkData(scheduleForm,strategyForm)
        //调用更新函数
        if (checkResult){
          let data = this.assemblyData(scheduleForm, strategyForm)
          this.updateTimeJobInfo(data)
        }
      }
    },
    assemblyData(scheduleForm, strategyForm) {
      return {
        caseSelect: strategyForm.pageStrategy,
        configuration: "{"+this.converConfigration(strategyForm)+"}",
        deviceSelect: strategyForm.deviceSelect,
        enabled: this.isEnable === 'yes' ? true : false,
        id: this.id,
        notifyList: this.notifyList,
        pkgSelect: strategyForm.pkgSelect,
        triggerTime: this.converTime(scheduleForm),
        userJobId: this.jobInfo.id
      }
    },
    checkData(scheduleForm, strategyForm) {
      if (this.checkDeviceSelect(strategyForm) && this.checkpageSelect(strategyForm) && this.checkDate(scheduleForm)){
        return true
      }else{
        this.$Notice.error({
                title: '数据出现问题，请检查!'
              });
        return false
      }
    },
    converTime(scheduleForm) {
      // 转换日期
      let days = scheduleForm.days
      let days_str = days.join(',')
      // 转换触发时间
      let hours = []
      let minutes = new Map()
      let selectTimes = scheduleForm.selectedTimes
      for (let i = 0; i < selectTimes.length; i++) {
        let time = selectTimes[i]
        let time_split = time.split(",")
        let value = minutes.get(time_split[0]) || []
        value.push(time_split[1])
        minutes.set(time_split[0],value)
      }
      // 遍历key
      for (let key of minutes.keys()){
        let value = minutes.get(key)
        value = value.join(",")
        hours.push(key)
        minutes.set(key, value)
      }
      let hours_str = hours.join(",")
      let minutes_str = Array.from(minutes)
      .map(([key, value]) => `"${key}":"${value}"`)
      .join(',');
      // 拼接cron表达式
      let triggerTime = "{"+minutes_str + "} " + hours_str+ " * * "+days_str
      return triggerTime
    },
    converConfigration(strategyForm) {
      let configuration = new Map()
      configuration.set("device_sn", strategyForm.device_sn)
      configuration.set("device_model", strategyForm.device_model)
      configuration.set("pageIds", strategyForm.page_pageIds)
      configuration.set("keyword", strategyForm.page_keyword)
      let configJson = Array.from(configuration)
      .map(([key, value]) => `"${key}":"${value}"`)
      .join(',');
      return configJson
    },
    checkDate(scheduleForm) {
      if (scheduleForm.days.length === 0){
        this.$Notice.error({
                title: '请选择触发日期！'
              });
        return false
      }
      if (scheduleForm.selectedTimes.length === 0){
        this.$Notice.error({
                title: '请选择触发时间！'
              });
        return false
      }
      return true
    },
    checkDeviceSelect(strategyForm) {
      let deviceSelect = strategyForm.deviceSelect
      if (deviceSelect === 'SPECIFIC_SN'){
        let checkResult = strategyForm.device_sn.length === 0 ? false : true
        if (!checkResult) {
          this.$Notice.error({
                title: '请选择设备'
              });
        }
        return checkResult
      }else if (deviceSelect === 'SPECIFIC_MODEL'){
        let checkResult = strategyForm.device_model === "" ? false : true
        if (!checkResult) {
          this.$Notice.error({
                title: '设备类型为空，请输入设备类型'
              });
        }
        return checkResult
      }
    },
    checkpageSelect(strategyForm) {
      let pageStrategy = strategyForm.pageStrategy
      if (pageStrategy === 'SPECIFIC') {
        return true
      } else if (pageStrategy === 'KEY_WORD'){
        let checkResult = strategyForm.page_keyword === "" ? false : true
        if (!checkResult) {
          this.$Notice.error({
                title: '关键字为空，请输入关键字'
              });
        }
        return checkResult
      } else if (pageStrategy === 'PAGE_ID'){
        let checkResult = strategyForm.page_pageIds === "" ? false : true
        if (!checkResult) {
          this.$Notice.error({
                title: 'pageIds为空，请输入pageIds'
              });
        }
        return checkResult
      }
    },
    updateTimeJobInfo(data) {
      this.$axios({
        method:"post",
        data: data,
        url:this.env.url+"/client/compatibility/updateTimeJobInfo"
      }).then((res) => {
        let code = res.data.code;
        if (code !== 0) {
          this.$Notice.error({
                title: '定时配置更新失败！'
              });
        } else {
          this.$Notice.success({
						title: "更新配置成功！",
					});
					this.$emit("closeModal");
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    insertTimeJobInfo(data) {
      this.$axios({
        method:"post",
        data: data,
        url:this.env.url+"/client/compatibility/addTimeJobInfo"
      }).then((res) => {
        let code = res.data.code;
        if (code !== 0) {
          this.$Notice.error({
                title: '定时配置保存失败！'
              });
        } else {
          this.$Notice.success({
						title: "保存配置成功！",
					});
					this.$emit("closeModal");
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    initTimes() {
      //初始化时间选项
      if (this.times.length === 0) {
        for(let i = 0 ; i < 24; i++) {
          this.times.push({
            value: String(i)+',0',
            key: String(i).padStart(2, '0')+':00'
          })
          this.times.push({
            value: String(i)+',30',
            key: String(i).padStart(2, '0')+':30'
          })
        }
      }
    },
    initTimeJobInfo() {
      // 定时任务信息配置
      if (this.isget) {
        // 显示时间选择策略
        let parts = this.triggerTime.split(" ")
        this.selectTimesConver(parts[0],parts[1])
        let selectDays = parts[4]
        this.scheduleForm.days = selectDays.split(",")
        // 显示任务配置
        let configuration = JSON.parse(this.configuration)
        this.configuration = configuration
        this.strategyForm.device_sn = (configuration.device_sn || '').split(",")
        this.strategyForm.device_model = configuration.device_model || ''
        this.strategyForm.page_keyword = configuration.keyword || ''
        this.strategyForm.page_pageIds = configuration.pageIds || ''
      } else {
        this.isEnable = 'no',
        this.notifyList = "",
        this.scheduleForm = {
          days: [], // 选中的星期
          selectedTimes: [], // 选中的时间点
        }
        this.strategyForm =  {
          device_sn: [],
          device_model: '',
          page_keyword: '',
          page_pageIds: '',
          deviceSelect: 'SPECIFIC_SN',  // 设备触发策略选中的值
          pageStrategy: 'SPECIFIC', // 页面选择策略选中的值
          pkgSelect: 'SPECIFIC' // 安装包选择策略选中的值
        }
      }
    },
    selectTimesConver(minute, hour) {
      // 时间格式转换
      let minutes = JSON.parse(minute)
      let hours = hour.split(",")
      this.scheduleForm.selectedTimes = []
      for (let index = 0; index < hours.length; index++) {
        const tempHour = hours[index]
        const tempMinute = minutes[tempHour].split(",")
        for (let i = 0; i < tempMinute.length; i++) {
          const element = tempMinute[i];
          const selectTime = tempHour+","+element
          this.scheduleForm.selectedTimes.push(selectTime)
        }       
      }
    },
    getTimeJobInfo(userJobId) {
      this.$axios({
        method:"get",
        params:{
          userJobId: userJobId
        },
        url:this.env.url+"/client/compatibility/getTimeJobInfo"
      }).then((res) => {
        let code = res.data.code;
        if (code !== 0) {
          this.$Notice.error({
                title: '获取定时任务信息失败'
              });
        } else {
          let timeJobInfo = res.data.timeJobInfo
          if (timeJobInfo !== undefined) {
            let timeJobInfo = res.data.timeJobInfo
            this.strategyForm.pkgSelect = timeJobInfo.pkgSelect
            this.strategyForm.deviceSelect = timeJobInfo.deviceSelect
            this.strategyForm.pageStrategy =  timeJobInfo.caseSelect
            this.isEnable = timeJobInfo.enabled ? "yes" : "no"
            this.notifyList = timeJobInfo.notifyList
            this.id = timeJobInfo.id
            this.triggerTime = timeJobInfo.triggerTime
            this.userJobId = timeJobInfo.userJobId
            this.configuration = timeJobInfo.configuration
            this.isget = true
          } else {
            this.isget = false
            this.id = null
            this.strategyForm.pkgSelect = res.data.packageSelectMode[0].name
            this.strategyForm.deviceSelect = res.data.deviceSelectMode[0].name
            this.strategyForm.pageStrategy = res.data.sceneSelectMode[0].name
          }
          this.deviceStrategies = res.data.deviceSelectMode
          this.pageStrategies = res.data.sceneSelectMode
          this.packageStrategies = res.data.packageSelectMode
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    getDeviceInfo(jobInfo) {
      this.deivce_info = []
      this.$axios({
        method:"get",
        params:{
          "business": jobInfo.business,
          "jobType": jobInfo.type,
          "platform": jobInfo.platform,
          "sourceType": 'Microscope',
          "category": jobInfo.category,
          "filterRule": 'ALL',
          "usePublic": true
        },
        url:this.env.url+"compatibilityPhone/available"
      }).then((res) => {
        let code = res.data.code;
        if (code !== 0) {
          this.$Notice.error({
                title: '获取设备列表失败'
              });
        } else {
          let phoneList = res.data.data.devices;
          for(let i = 0; i < phoneList.length; i++){
            let device = phoneList[i]
            if (device['connectType']==='remote') {
              this.deivce_info.push({
                value: device['sn'], 
                label: '【云测+'+device['label']+'】'+device['model']+' '+device['version']
              })
            }else if (device['connectType']==='local') {
              this.deivce_info.push({
                value: device['sn'], 
                label: '【本地+'+device['label']+'】'+device['model']+' '+device['version']
              })
            }
          }
        }
      }).catch(function (error) {
        console.log(error)
      })
    }
  },
};
</script>

<style scoped>
.bottom {
  text-align: center;
}
/deep/ .jsoneditor-vue {
  height: 350px;
}
.app_param {
  padding-bottom: 10px;
}
.strategy-hint {
  margin-left: 10px;
  font-size: 12px;
  color: #2b7ef2; 
}
</style>
