<template>
    <Modal 
      v-model="showModal" 
      width="700"
      :closable="false"
      :mask-closable="false"
    >
      <p slot="header" style="color:#f60;text-align:center">
          <Icon type="md-flag"></Icon>
          <span>Job运营记录</span>
      </p>
      <div style="text-align:left;padding-left: 10px">
          <p class="item">
              <span class="item_title">关联需求:</span>
              <Input v-model="jobConmentInfo.requirements" clearable type="textarea" placeholder="可填写需求标题......"></Input>
          </p>
          <p class="item">
              <span class="item_title">运营阶段: </span>
              <RadioGroup v-model="jobConmentInfo.stage">
                <Radio  v-for="stage in stageList" :label="stage.name" :key="stage.value">
                  <span>{{ stage.label }}</span>
                </Radio>
              </RadioGroup>
          </p>
          <p class="item">
              <span class="item_title">是否通过: </span>
              <RadioGroup v-model="jobConmentInfo.status">
                <Radio  v-for="status in jobStatusList" :label="status.name" :key="status.value">
                  <span>{{ status.label }}</span>
                </Radio>
              </RadioGroup>
          </p>
          <p class="item">
            <span class="item_title">评论:</span>
            <Input v-model="jobConmentInfo.comment" clearable type="textarea" placeholder="填写用于标记不一致的页面是否是bug，以及其他运营信息。不通过时，此项必填"></Input>
          </p>
      </div>
      <p slot="footer">
        <Button type="text" size="large" @click="$emit('cancelModal')">取消</Button>
        <Button type="primary" size="large" @click="saveJobComment">确定</Button>
      </p>
    </Modal>
</template>
<script>
import { Bus } from '@/global/bus'
export default {
  components: { Bus },
  name: 'JobCommentModal',
  props: ['showJobCommentModal', 'jobId', 'business'],
  data() {
    return {
      showModal: false,
      stageList: [],
      jobStatusList: [],
      jobConmentInfo: {
        id: -1,
        requirements: '',
        business: this.business,
        user: Bus.userInfo.userLogin,
        stage: '',
        status: '',
        comment: ''
      }
    }
  },
  mounted() {
    this.initJobComment()
  },
  watch: {
    jobId: function(newJobId) {
      if (newJobId) {
        this.initJobComment()
      }
    },
    showJobCommentModal: function(newVal) {
      this.showModal = newVal
    }
  },
  methods: {
    initJobComment: function() {
      this.$axios({
        method: 'get',
        params: {
          'jobId': this.jobId
        },
        url: this.env.url + 'client/compatibility/jobComment'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.stageList = message.data.stageList
          this.jobStatusList = message.data.statusList
          this.jobConmentInfo = message.data.jobComment
        } else {
          this.$Notice.error({
            title: '获取运营信息失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    saveJobComment: function() {
      this.jobConmentInfo.user = Bus.userInfo.userLogin
      this.jobConmentInfo.business = this.business
      this.jobConmentInfo.jobId = this.jobId
      if (!this.jobConmentInfo.comment) {
        this.jobConmentInfo.comment = ''
      }
      if (this.jobConmentInfo.status === 'Pass' || this.jobConmentInfo.comment.trim().length > 0) {
        if (this.jobConmentInfo.id !== -1) {
          this.updateJobComment(this.jobConmentInfo.id)
        } else {
          this.addJobComment()
        }
        this.$emit('cancelModal')
      } else {
        this.$Notice.error({
          title: 'Job不通过时，需要填写原因！'
        })
        return false
      }
    },
    addJobComment: function() {
      this.$axios({
        method: 'post',
        data: this.jobConmentInfo,
        url: this.env.url + 'client/compatibility/jobComment'
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: '添加运营信息成功～'
          })
        } else {
          this.$Notice.error({
            title: '添加运营信息失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
        this.$Notice.error({
          title: '添加运营信息失败！'
        })
      })
    },
    updateJobComment: function(id) {
      this.$axios({
        method: 'post',
        data: this.jobConmentInfo,
        url: this.env.url + 'client/compatibility/jobComment/' + id
      }).then((res) => {
        let message = res.data
        if (message.code === 0) {
          this.$Notice.success({
            title: '更新运营信息成功～'
          })
        } else {
          this.$Notice.error({
            title: '更新运营信息失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
        this.$Notice.error({
          title: '更新运营信息失败！'
        })
      })
    }
  }
}
</script>
<style scoped>
  .item {
    margin: 10px 0px;
  }
  .item_title {
    font-weight: 600;
  }
</style>

