<template>
  <div>
    <div :style="{padding: '1px',background: '#fff'}">
      <Layout :style="{minHeight: '100vh'}">
        <div :style="{padding: '5px',minHeight: '50px', background: '#fff'}">
          <Row>
            <div style="text-align: left;margin-left: 40px;margin-top: -5px;margin-bottom:15px">
                <a style="display: inline-block;margin-bottom:15px;font-size:25px;color:#333333;">
                  <span>自动化度量详情</span>
                </a>
                <span style="margin-left: 5px;">
                  <Icon @click="showDataExplain = true" type="ios-help-circle-outline" size="20" />
                </span>
                <Drawer width="640" title="度量数据说明" placement="right" :closable="false" v-model="showDataExplain">
                  <span style="font-weight:bolder;font-size:14px;line-height:22px">
                    详情可查看:
                    <a href="https://km.sankuai.com/page/185406389">客户端⾃动化测试及持续交付度量体系</a>
                  </span>
                  <br /><br />
                  <span style="font-weight:bolder;font-size:14px;line-height:22px">运⾏稳定性：</span>
                  <br />
                  <span style="font-weight:bolder;line-height:22px">1.Job成功率</span> : 成功Job数/触发Job总数;
                  <br />
                  <span style="font-weight:bolder;line-height:22px">2.Case成功率</span> : 成功case数 / (成功case数+失败case数+跳过case数);
                  <br />
                  <span style="font-weight:bolder;line-height:22px">3.页⾯⼀致率</span>: 页⾯⼀致数/运⾏页⾯总数 （统计范围：回归及发布任务）
                  <br />
                  <span style="font-weight:bolder;line-height:22px">4.Case跳过率</span> : 跳过Case数/ (成功Case数+失败Case数+跳过Case数）
                  <br />
                  <span style="font-weight:bolder;line-height:22px">5.Case⾮业务原因代码导致失败率</span> = 1 -（成功Case + 业务Bug导致Fail的Case）/ （成功
                  Case + 所有失败Case）
                  <br />
                  <span style="font-weight:bolder;line-height:22px">6.Case TOP3失败原因及占⽐</span>: 取占⽐前3的Case失败原因
                  <br />
                  <br />
                  <br />
                  <span style="font-weight:bolder;font-size:14px;margin-top: 5px;">运⾏效率：</span>
                  <br />
                  <span style="font-weight:bolder;line-height:22px">1.Job运⾏耗时</span>：筛选时间范围内，各Job运⾏耗时集合，取90分位数;
                  <br />
                  <span style="font-weight:bolder;line-height:22px">2.Case运⾏耗时</span>：筛选时间范围内，各条Case运⾏耗时集合，取90分位数
                  <br />
                </Drawer>
            </div>
          </Row>
          <Row>
              <div>
                <ConfigSelect class="config-select" :tab="tab" v-show="tab === 'compatibility'"  @selectInfo="getTableData"></ConfigSelect>
                <ConfigSelect class="config-select" :tab="tab" v-show="tab === 'shortlink'" @selectInfo="getTableData"></ConfigSelect>
                <ConfigSelect class="config-select" :tab="tab" v-show="tab === 'videoPlayer'" @selectInfo="getTableData"></ConfigSelect>
                <ConfigSelect class="config-select" :tab="tab" v-show="tab === 'apiDiff'" @selectInfo="getTableData"></ConfigSelect>
              </div>
          </Row>
        </div>
        <Menu class="client-menu" mode="horizontal" theme="light" :active-name="tab" @on-select="handleSelect">
          <MenuItem name="compatibility">
            视觉自动化
          </MenuItem>
          <MenuItem name="shortlink">
            短链路自动化
          </MenuItem>
          <MenuItem name="videoPlayer">
            视频自动化
          </MenuItem>
          <MenuItem name="apiDiff">
            API DIFF
          </MenuItem>
        </Menu>
        <AutotestHyperJumpRunEffectTable v-show="tab === 'compatibility'" ref="AutotestHyperJumpRunEffectTable" ></AutotestHyperJumpRunEffectTable>
        <AutotestShortlinkRunEffectTable v-show="tab === 'shortlink'" ref="AutotestShortlinkRunEffectTable"></AutotestShortlinkRunEffectTable>
        <AutotestVideoPlayerRunEffectTable v-show="tab === 'videoPlayer'" ref="AutotestVideoPlayerRunEffectTable"></AutotestVideoPlayerRunEffectTable>
        <AutotestApiDiffRunEffectTable v-show="tab === 'apiDiff'" ref="AutotestApiDiffRunEffectTable"></AutotestApiDiffRunEffectTable>
      </Layout>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import AutotestRunSituationTable from "../ClientAutotest/AutotestRunSituationTable";
import AutotestHyperJumpRunEffectTable from "../ClientAutotest/AutotestHyperJumpRunEffectTable";
import AutotestShortlinkRunEffectTable from "../ClientAutotest/AutotestShortlinkRunEffectTable";
import AutotestVideoPlayerRunEffectTable from "../ClientAutotest/AutotestVideoPlayerRunEffectTable";
import AutotestRunEffectTable from "../ClientAutotest/AutotestRunEffectTable";
import AutotestCcdRunTimeTable from "../ClientAutotest/AutotestCcdRunTimeTable";
import AutotestCcdRunEffectTable from "../ClientAutotest/AutotestCcdRunEffectTable";
import AutotestApiDiffRunEffectTable from "../ClientAutotest/AutotestApiDiffRunEffectTable";
import ConfigSelect from "../baseComponents/ConfigSelect";
import { time } from "highcharts";
export default {
  name: "AutotestMeasureInfo",
  components: {
    AutotestRunSituationTable,
    AutotestHyperJumpRunEffectTable,
    AutotestShortlinkRunEffectTable,
    AutotestVideoPlayerRunEffectTable,
    AutotestApiDiffRunEffectTable,
    AutotestRunEffectTable,
    AutotestCcdRunTimeTable,
    AutotestCcdRunEffectTable,
    ConfigSelect
  },
  data() {
    return {
      isCollapsed: false,
      showDataExplain: false,
      channel: "",
      tab: 'compatibility', // 默认显示视觉自动化的表格
      nowDate: [],
      timeRange: [],
      business: "", //默认业务方向
      businessList: [],
      triggerNodeList: [], //触发节点筛选
      autoTestBusinessFilter: ["hotel","travel", "overseahotel"], //业务方向默认筛选
      businessFilter: [],
      triggerNodeFilter: [], // 触发节点默认筛选为空
      ccdRunEffectTimeFilter: [] //筛选时间
    };
  },
  computed: {
    menuitemClasses: function() {
      return ["menu-item", this.isCollapsed ? "collapsed-menu" : ""];
    },
    rotateIcon() {
      return ["menu-icon", this.isCollapsed ? "rotate-icon" : ""];
    }
  },
  mounted() {
    let endTime = this.formateTime(new Date());
    let startTime = this.formateTime(
      new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
    );
    this.nowDate = [startTime, endTime];
    //支持进入页面携带一个方向参数 或者 不携带方向参数
    if (this.$route.query.channel) {
      this.channel = this.$route.query.channel;
    }

    this.getBusiness();
    this.getTriggerNode();
  },
  methods: {
    collapsedSider() {
      this.$refs.side1.toggleCollapse();
    },
    formateTime: function(time) {
      return (
        time.getFullYear() +
        "-" +
        (time.getMonth() >= 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        "-" +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate())
      );
    },
    handleSelect(name) {
      this.tab = name; // 当选择菜单项时，更新 activeTab 的值
    },
    getAutotestRunEffectAndSituation(timeRange) {
      this.getShortLinkRunSituation(timeRange,businessFilter);
      this.getAutotestRunEffectTable(timeRange,businessFilter);
    },
    getAutotestRunEffectTable(timeRange,businessFilter) {
      (this.timeRange = timeRange),
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          timeRange,
          businessFilter
        );
    },
    getCcdRunEffectTable(timeRange, businessFilter, triggerNodeIdFilter) {
      ((this.timeRange = timeRange),
      (this.businessFilter = businessFilter),
      (this.triggerNodeIdFilter = triggerNodeIdFilter)),
        this.$refs.AutotestCcdRunEffectTable.showCcdRunEffectInfo(
          timeRange,
          businessFilter,
          triggerNodeIdFilter
        );
    },
    //持续交付筛选时间
    jobCcdRunEffectTime(timeRange) {
      this.ccdRunEffectTimeFilter = timeRange;
      this.getCcdRunEffectTable(
        timeRange,
        this.businessFilter,
        this.triggerNodeFilter
      );
    },
    //自动化度量筛选时间
    autoTestFilterTime(timeRange) {
        this.$refs.AutotestRunSituationTable.showRunsituationInfo(
          timeRange,
          this.autoTestBusinessFilter
        );
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          timeRange,
          this.autoTestBusinessFilter
        );
    },
    //持续交付筛选方向
    jobBusinessFilter: async function(value) {
      await this.$nextTick();
      this.businessFilter = value;
      this.getCcdRunEffectTable(this.timeRange, value, this.triggerNodeFilter);
    },

    //自动化度量报告筛选方向
    autoTestBussinessFilter: async function(value) {
        await this.$nextTick();
        this.autoTestBusinessFilter = value;
        this.$refs.AutotestRunSituationTable.showRunsituationInfo(
          this.timeRange,
          this.autoTestBusinessFilter
        );
        this.$refs.AutotestRunEffectTable.showAutotestRunEffectInfo(
          this.timeRange,
          this.autoTestBusinessFilter
        );
    },
    //筛选触发节点
    jobTriggerNodeIdFilter: async function(value) {
      await this.$nextTick();
      this.triggerNodeFilter = value;
      this.getCcdRunEffectTable(this.timeRange, this.businessFilter, value);
    },
    getBusiness() {
      this.$axios({
        method: "get",
        url: this.env.url + "autoTestConfig/getAllBusinessName"
      })
        .then(res => {
          let message = res.data;
          this.businessList = message;
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    getTriggerNode() {
      this.$axios({
        params: {
          nodeType: "flow,time"
        },
        method: "get",
        url: this.env.url + "autoTestConfig/getNodeList"
      })
        .then(res => {
          let message = res.data;
          this.triggerNodeList = message;
          console.log(this.triggerNodeList);
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    getTableData(
      nowDate,
      unitId,
      groupId,
      businessId,
      productFilter,
      nodeFilter,
      misIDFilter,
      pageNameFilter
    ) {
      if (nowDate.length === 0 || (!businessId && !groupId && !unitId)) {
        this.$Message.info('请选择日期以及方向')
      } else if (this.tab === 'compatibility') {
        this.$refs.AutotestHyperJumpRunEffectTable.getTableData(nowDate,
      unitId,
      groupId,
      businessId,
      productFilter,
      nodeFilter,
      misIDFilter,
      pageNameFilter
      );
      } else if (this.tab === 'shortlink') {
        this.$refs.AutotestShortlinkRunEffectTable.getTableData(nowDate,
      unitId,
      groupId,
      businessId,
      productFilter,
      nodeFilter);
      }else if(this.tab === 'videoPlayer'){
        this.$refs.AutotestVideoPlayerRunEffectTable.getTableData(nowDate,
      unitId,
      groupId,
      businessId,
      productFilter,
      nodeFilter);
      }else if(this.tab === 'apiDiff'){
        this.$refs.AutotestApiDiffRunEffectTable.getTableData(nowDate,
          unitId,
          groupId,
          businessId,
          productFilter,
          nodeFilter,
          misIDFilter,
          pageNameFilter);
      }
    },
  }
};
</script>

<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #dadada;
  position: relative;
  border-radius: 1px;
  overflow: hidden;
  margin-top: 15px;
  margin-left: 5px;
}
.layout-logo {
  width: 200px;
  height: 30px;
  /*background: #5b6270;*/
  border-radius: 3px;
  float: left;
  position: relative;
  top: 15px;
  left: 20px;
}
.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width 0.2s ease 0.2s;
  margin-left: -5px;
}
.menu-item i {
  transform: translateX(0px);
  transition: font-size 0.2s ease, transform 0.2s ease;
  vertical-align: middle;
  font-size: 10px;
}
.config-select {
  position: relative;
  z-index: 1000;
}
.collapsed-menu span {
  width: 50px;
  transition: width 0.2s ease;
}
.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size 0.2s ease 0.2s, transform 0.2s ease 0.2s;
  vertical-align: middle;
  font-size: 22px;
}
.menu-icon {
  transition: all 0.3s;
  margin-left: 5px;
  float: left;
}
.rotate-icon {
  transform: rotate(-90deg);
  margin-left: 5px;
  float: left;
}
.layout-header-bar {
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  height: 35px;
}
.tab {
    margin-top: 15px;
    margin-left: 1.5%;
    margin-right: 1.5%;
    width: auto
  }
</style>
