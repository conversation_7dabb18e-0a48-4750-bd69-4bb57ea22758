<template>
  <div>
    <Tool class="tool" :pageId="pageId"></Tool>
    <div class="info" v-for="url in urlList" :key="url.id">
      <URLInfo :url="url" :pageId="pageId" :pageInfo="pageInfo" ></URLInfo>
    </div>
    <div v-if="!edit" class="new">
      <Button type="dashed" long @click="add" v-if="permission">
        <Icon type="md-add" />新增URL模型
      </Button>
    </div>
    <div v-else class="edit">
      <URLEdit @cancel="toView"></URLEdit>
    </div>
  </div>
</template>
<script>
import Tool from './TestTool'
import URLInfo from './URLInfo'
import URLEdit from './URLEdit'
export default {
  name: 'UrlModel',
  props: ['tab', 'pageInfo'],
  components: {Tool, URLInfo, URLEdit},
  data() {
    return {
      edit: false,
      pageId: this.$route.params.pageId
    }
  },
  mounted() {
    this.$store.commit('setCurrentTab', this.tab)
    this.$store.dispatch('getUrlList', this.pageId)
  },
  computed: {
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    },
    urlList() {
      return this.$store.state.clientQuality.urlList
    }
  },
  methods: {
    permission() {
      let permission =
        this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    },
    add() {
      this.edit = true
    },
    toView() {
      this.edit = false
    }
  }
}
</script>
<style scoped>
.tool {
  margin-bottom: 20px;
}
.title {
  font-size: 24px;
  color: #464c5b;
  font-weight: bold;
  padding: 8px 16px;
}
.info {
  padding-left: 20px;
  padding-right: 30px;
  margin-bottom: 20px;
}
.new {
  margin-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
}
.edit {
  margin-top: 20px;
}
</style>