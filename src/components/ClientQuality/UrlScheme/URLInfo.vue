<template>
  <Row>
    <Col span="20" v-if="!edit">
      <span class="path">{{ url.path }}</span>
      <Tag color="volcano" v-for="pro in url.productList" :key="pro.id">{{ pro.label }}</Tag>
      <span class="coverage"><strong style="color: #464c5b; font-size: small">参数覆盖率：</strong>{{ url.coverageRate }}%</span>
      <p>&nbsp;&nbsp;{{ url.remark }}</p>
      <div v-if="showSelect" style="display: flex; align-items: center">
        <Select v-model="editInfo.pageId" style="max-height: 100px">
          <Option v-for="item in parentList" :value="item.id" :key="item.id">{{ item.label }}{{ item.name }}</Option>
        </Select>
        <Button @click.native="submit" :type="color" style="margin-left: 10px">确定</Button>
      </div>
      <div v-for="pro in url.productList" :key="pro.id">
        <Badge color="green" :text="pro.scheme + '://' + (pro.authority ? pro.authority : '') + url.path" />
      </div>
    </Col>
    <Col span="20" v-else>
      <URLEditor :url="url" @cancel="toView"></URLEditor>
    </Col>
    <Col span="3" offset="1" v-if="permission">
      <Button type="primary" icon="md-create" ghost size="small" @click="toEdit" :disabled="edit"></Button>
      <Button type="error" icon="md-trash" ghost size="small" @click="toDelete" :disabled="edit"></Button>
      <Button size="small" type="info" ghost @click="toggleSelect" :disabled="edit">一键迁移</Button>
    </Col>
    <Col span="23" v-if="!edit">
      <Table class="query" :columns="columns" :data="url.query" size="small">
        <template #key="{ row }">
          <el-tooltip v-if="row.operator === 'MMCD'" content="自动添加的可测性上报参数" placement="top">
            <strong>{{ row.param }}</strong>
          </el-tooltip>
          <strong v-else>{{ row.param }}</strong>
          <Badge
            v-if="row.required === 1 || row.operator === 'MMCD'"
            :status="row.required === 1 ? 'error' : row.operator === 'MMCD' ? 'warning' : ''"
          />
        </template>
        <template #type="{ row }">
          <div style="display: flex; align-items: center">
            <span v-if="row.type === 1"
              >枚举类：<span class="enum-value">{{ row.value }}</span></span
            >
            <span v-else-if="row.type === 2">布尔类</span>
            <span v-else-if="row.type === 3">JSON对象类</span>
            <span v-else-if="row.type === 4"
              >日期时间类：<span class="enum-value">{{ row.value }}</span></span
            >
            <span v-else-if="row.type === 5">经纬度类</span>
            <span v-else-if="row.type === 0">其他</span>
            <el-tooltip v-if="row.markType === 1" content="LLM自动标识的参数类型" placement="top">
              <Badge :color="row.markType === 1 ? 'blue' : ''" style="margin-left: 10px" />
            </el-tooltip>
          </div>
        </template>
        <template #coveredState="{ row }">
          <span v-if="row.isCoverageIncluded === 0">--</span>
          <span v-else>
            <Tag v-if="row.isCovered === undefined" color="red">未覆盖</Tag>
            <Tag v-else-if="row.type === 1 || row.type === 2" color="green">{{ row.isCovered }}</Tag>
            <Tag v-else color="green">已覆盖</Tag>
          </span>
        </template>
      </Table>
    </Col>
  </Row>
</template>
<script>
import URLEditor from './URLEdit'
import * as api from '../state/api'
export default {
  name: 'URLInfo',
  props: ['url', 'pageId', 'pageInfo'],
  components: { URLEditor },
  data() {
    return {
      edit: false,
      showSelect: false,
      color: 'primary',
      editInfo: {
        id: this.url.id,
        pageId: this.pageId
      },
      columns: [
        {
          type: 'index',
          width: 50,
          align: 'center'
        },
        {
          title: '参数Key',
          slot: 'key',
          width: 200
        },
        {
          title: '参数类型',
          slot: 'type',
          width: 400
        },
        {
          title: '描述',
          key: 'label',
          width: 400
        },
        {
          title: '匹配规则（正则）',
          key: 'pattern',
          width: 200
        },
        {
          title: '是否覆盖',
          slot: 'coveredState'
        }
      ]
    }
  },
  computed: {
    permission() {
      let permission = this.$store.state.clientQuality.configCenterUserPermission
      if (permission.isAdmin || permission.permissionLevel < 100) {
        return true
      } else {
        return false
      }
    },
    parentList() {
      let parentList = []
      parentList = this.$store.state.clientQuality.pageList.pageList
      return parentList
    }
  },
  methods: {
    toEdit() {
      this.edit = true
    },
    toView() {
      this.edit = false
    },
    async toggleSelect() {
      this.showSelect = true
      let query = {
        isOnline: 1,
        pageSize: 500,
        pageCount: 1,
        isDetail: 0,
        businessId: this.pageInfo.businessId
      }
      await this.$store.commit('setPageFilter', query)
      await this.$store.dispatch('getPageListById')
    },
    async submit() {
      await this.$store.dispatch('setUrlInfo', this.editInfo)
      await this.$store.dispatch('getUrlList', this.pageId)
    },
    async toDelete() {
      await api.deleteUrl(this.url.id).then((res) => {
        let message = res.data
        if (message.code && message.code === 1) {
          this.$Message.error('error')
        } else {
          this.$store.commit('setUrlList', message)
          this.$Message.success('success')
        }
      })
    }
  }
}
</script>
<style scoped>
.path {
  font-size: 24px;
  font-weight: 500;
  vertical-align: middle;
}
.coverage {
  margin-right: 20px;
  float: right;
  color: #2db7f5;
  font-size: x-large;
  font-weight: 500;
}
.query {
  margin-top: 20px;
}
.button {
  margin-left: 20px;
}
.enum-value {
  color: #101010; /* 你可以根据需要更改颜色 */
  font-weight: bold;
}
div >>> .ivu-badge-status-text {
  color: #9ea7b4;
  font-size: small;
}
</style>