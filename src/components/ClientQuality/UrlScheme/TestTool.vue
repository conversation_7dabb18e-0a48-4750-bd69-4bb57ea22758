<template>
  <Row>
    <Col span="20">
    <Input v-model="urlDemo" prefix="ios-arrow-forward" placeholder="输入一个URL以试验一下能否正确匹配" style="margin-left:10px" />
    </Col>
    <Col span="3" offset="1">
    <Button :type="color" :loading="load" @click="testDemo" style="margin-left:15px">{{result}}</Button>
    </Col>
    <div class="error">
      <p>{{error}}</p>
    </div>
  </Row>
</template>
<script>
import * as api from '../state/api'
export default {
  name: 'Tool',
  props: ['pageId', 'model'],
  data() {
    return {
      urlDemo: '',
      result: '试验一下',
      color: 'primary',
      error: '',
      load: false
    }
  },
  methods: {
    async testDemo() {
      this.urlDemo.trim()
      if (this.urlDemo === '') {
        this.error = 'url不可以为空'
      } else {
        this.load = true
        let param = {
          targetUrl: this.urlDemo,
          pageId: this.pageId
        }
        if (this.model !== undefined) {
          let queryList = []
          for (let query of this.model.query) {
            if (!query.delete) {
              queryList.push(query)
            }
          }
          this.model.query = queryList
          param['model'] = this.model
        }
        await api.checkUrl(param).then((res) => {
          let message = res.data
          if (message.code === 0) {
            this.result = '匹配成功'
            this.color = 'success'
            this.error = null
          } else {
            this.error = message.data
            this.result = '匹配失败'
            this.color = 'error'
          }
        })
        this.load = false
      }
    }
  }
}
</script>
<style scoped>
.error {
  margin-left: 20px;
}
</style>