<template>
  <div>
    <head-component id="head"></head-component>
    <div>
      <h3 style="text-align:center; margin:20px" v-text="title" ></h3>
      <div class="ivu-checkbox">
        <Checkbox-group v-model="tableColumnsChecked" @on-change="changeTableColumns">
          <Checkbox label="type">需求类型</Checkbox>
          <Checkbox label="version">版本</Checkbox>
          <Checkbox label="devPd">开发时长</Checkbox>
          <Checkbox label="jointDebugPd">联调时长</Checkbox>
          <Checkbox label="jointDebugRatio">联调占比</Checkbox>
          <Checkbox label="testPd">测试时长</Checkbox>
          <Checkbox label="testDevRatio">测试效率</Checkbox>
          <Checkbox label="beAheadStatus">后台先行</Checkbox>
          <Checkbox label="testStage">提测阶段</Checkbox>
          <Checkbox label="iterationRatio">迭代测试比例</Checkbox>
          <checkbox label="validBugCnt">Bug数(整体)</checkbox>
          <checkbox label="nonCheckerValidBugCnt">Bug数(除checker)</checkbox>
          <Checkbox label="averageBugPerDay">提测质量(整体)</Checkbox>
          <Checkbox label="averageBugPerDayWithoutChecker">提测质量(除checker)</Checkbox>
          <Checkbox label="pmOwner">PM负责人</Checkbox>
          <Checkbox label="rdOwner">RD负责人</Checkbox>
          <Checkbox label="qaOwner">QA负责人</Checkbox>
          <Checkbox label="delay">是否提测delay</Checkbox>
          <Checkbox label="delayPd">提测delay时长</Checkbox>
          <Checkbox label="delayReason">提测delay原因</Checkbox>
          <Checkbox label="pmRejectTimes">PM打回次数</Checkbox>
          <Checkbox label="qaRejectTimes">QA打回次数</Checkbox>
        </Checkbox-group>
      </div>
      <div style="padding-left:10px; padding-right:10px; margin-left:10px; margin-right:10px">
        <div v-if="display5" id="clientDetail">
          <Table :height="600" border :row-class-name="rowClassName" :columns="columns5" :data="detail"></Table>
        </div>
        <div v-else>
          <Table border :row-class-name="rowClassName" :columns="columns5" :data="detail"></Table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { BusClient } from '@/global/bus'
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import axios from 'axios'
  import { analyticsbaseAPI } from '@/global/variable'

  Vue.component('head-component', Head)

  BusClient.$on('refreshClientDetailDataOnes', function (data) {
    BusClient.ClientDetailObject.detail = data
    BusClient.ClientDetailObject.display5 = BusClient.ClientDetailObject.adjustTableHight(data.length)
    if (data.length > 0) {
      this.ClientDetailObject.title = data[0]['projectKey'] + '项目     ' +
        data[0]['appType'] + '客户端     ' +
        data[0]['version'] + '版本    '
    }
  })

  export default {
    // components: {Layout},
    name: 'client-ones-detail-report',
    data: function () {
      return {
        display5: false,
        columns5: [],
        detail: [],
        title: '',
        tableColumnsChecked: ['index', 'taskSummary', 'devPd', 'jointDebugRatio', 'testDevRatio', 'nonCheckerValidBugCnt', 'averageBugPerDayWithoutChecker', 'delay', 'delayPd', 'pmRejectTimes', 'qaRejectTimes', 'qaOwner']
      }
    },
    methods: {
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0) {
          return 'demo-stripe'
        } else {
          return ''
        }
      },
      transferBoolean: function (data) {
        if (data === true) {
          return '是'
        } else {
          return '否'
        }
      },
      transferEmpty: function (data, emptyString) {
        if (data === emptyString) {
          return 'N/A'
        } else {
          return data
        }
      },
      transferBeAheadStatus: function (status) {
        if (status === 0) {
          return '否'
        } else if (status === 1) {
          return '是'
        } else {
          return '不涉及后台'
        }
      },
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      cellClassName: function (data) {
        if (data >= 0.5) {
          return 'table-error-data'
        } else return ''
      },
      changeTableColumns () {
        this.columns5 = this.getTable2Columns()
      },
      getTable2Columns () {
        const table2ColumnList = {
          index: {
            title: '序号',
            type: 'index',
            align: 'center',
            width: 80
          },
          taskSummary: {
            title: '需求',
            key: 'taskSummary',
            width: 200,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['taskUrl']
                  },
                  on: {
                    click: () => {
                      window.open(params.row['taskUrl'])
                    }
                  }
                }, params.row['taskSummary'])
              ])
            }
          },
          type: {
            title: '需求类型',
            key: 'type',
            sortable: true
          },
          devPd: {
            title: '开发时长',
            key: 'devPd',
            sortable: true
          },
          jointDebugPd: {
            title: '联调时长',
            key: 'jointDebugPd',
            sortable: true
          },
          jointDebugRatio: {
            title: '联调占比',
            key: 'jointDebugRatio',
            sortable: true
          },
          testPd: {
            title: '测试时长',
            key: 'testPd',
            sortable: true
          },
          testDevRatio: {
            title: '测试效率',
            key: 'testDevRatio',
            sortable: true
          },
          version: {
            title: '版本',
            key: 'version',
            sortable: true
          },
          beAheadStatus: {
            title: '后台先行',
            key: 'beAheadStatus',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {}, this.transferBeAheadStatus(params.row['beAheadStatus'])
                )
              ])
            }
          },
          testStage: {
            title: '提测阶段',
            key: 'testStage',
            sortable: true
          },
          iterationRatio: {
            title: '迭代测试比例',
            key: 'iterationRatio',
            sortable: true
          },
          validBugCnt: {
            title: 'Bug数',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, 'Bug数'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'validBugCnt',
            sortable: true
          },
          averageBugPerDay: {
            title: '提测质量',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'averageBugPerDay',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  class: this.cellClassName(params.row['averageBugPerDay'])}, params.row['averageBugPerDay'])
              ])
            }
          },
          pmOwner: {
            title: 'PM负责人',
            key: 'pmOwner',
            sortable: true
          },
          rdOwner: {
            title: 'RD负责人',
            key: 'rdOwner',
            sortable: true
          },
          qaOwner: {
            title: 'QA负责人',
            key: 'qaOwner',
            sortable: true
          },
          delay: {
            title: '提测delay?',
            key: 'delay',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {}, this.transferBoolean(params.row['delay'])
                )
              ])
            }},
          delayPd: {
            title: '提测delay时长',
            key: 'delayPd',
            sortable: true
          },
          delayReason: {
            title: '提测delay原因',
            key: 'delayReason',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {}, this.transferEmpty(params.row['delayReason'], 'None')
                )
              ])
            }
          },
          pmRejectTimes: {
            title: 'PM打回次数',
            key: 'pmRejectTimes',
            sortable: true
          },
          qaRejectTimes: {
            title: 'QA打回次数',
            key: 'qaRejectTimes',
            sortable: true
          },
          averageBugPerDayWithoutChecker: {
            title: '提测质量(除checker)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'averageBugPerDayWithoutChecker',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  class: this.cellClassName(params.row['averageBugPerDayWithoutChecker'])}, params.row['averageBugPerDayWithoutChecker'])
              ])
            }
          },
          nonCheckerValidBugCnt: {
            title: 'Bug数',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, 'Bug数'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'nonCheckerValidBugCnt',
            sortable: true
          }
        }
        let data = []
        this.tableColumnsChecked.forEach(col => data.push(table2ColumnList[col]))
        // console.log('tableColumnsDisplayed', data)
        return data
      }
    },

    mounted: function () {
      BusClient.ClientDetailObject = this
      const self = this
      BusClient.ClientDetailObject.$Spin.show()
      let params = {
        projectKey: '',
        version: '',
        bizLine: '',
        appType: '',
        platformName: '',
        component: ''
      }
      if (BusClient.ClientDetailObject.$route.query.projectKey) {
        params.projectKey = BusClient.ClientDetailObject.$route.query.projectKey
      }
      if (BusClient.ClientDetailObject.$route.query.version) {
        params.version = BusClient.ClientDetailObject.$route.query.version
      }
      if (BusClient.ClientDetailObject.$route.query.bizLine) {
        params.bizLine = BusClient.ClientDetailObject.$route.query.bizLine
      }
      if (BusClient.ClientDetailObject.$route.query.appType) {
        params.appType = BusClient.ClientDetailObject.$route.query.appType
      }
      if (BusClient.ClientDetailObject.$route.query.platformName) {
        params.platformName = BusClient.ClientDetailObject.$route.query.platformName
      }
      if (BusClient.ClientDetailObject.$route.query.component) {
        params.component = BusClient.ClientDetailObject.$route.query.component
      }
      axios.get(analyticsbaseAPI + '/client/detail', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          BusClient.detailData = []
          BusClient.detailData = message['data']['data']
          BusClient.$emit('refreshClientDetailDataOnes', BusClient.detailData)
        } else {
          alert(message['data']['msg'])
        }
        self.$Spin.hide()
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
      BusClient.ClientDetailObject = this
      this.changeTableColumns()
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }

  .ivu-checkbox {
    padding: 10px;
    white-space: normal;
  }

  .table-error-data {
    color: #ff3336;
    font-weight:bold
  }


</style>
