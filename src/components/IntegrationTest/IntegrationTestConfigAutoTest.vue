<template>
  <div>
    <Modal v-model="addIntegrationTestAutoTestRepo" title="集成测试Pipeline新增仓库配置" @on-ok="saveAddItPipeline" @on-cancel="getItPipelineConfig" :mask-closable="false" width="60%" :styles="{top: '50px', height: '90%', overflow: 'auto'}">
      <TimelineItem>
        <div class="time">RD仓库信息
        </div>
        <Row :gutter="16" style="margin-top: 10px;">
          <Col style="margin-left: 2%;margin-right: 2%" v-for="(item, index) in repoConfigList" :key="index">
            <Card style="width:100%; height: 240px; overflow: auto; margin-top: 15px">
              <div slot="title">
                <div style="margin-left: 0;margin-top: 0; width: 90%">
                  <span style="font-weight: bolder; margin-right: 5px">仓库：</span>
                  <Select v-model="item.repo" v-if="!item.repo" style="width:80%" @on-change="getAddConfig(index, item.repo)" filterable clearable>
                    <Option v-for="(unit, id) in item.option" :value="unit" :key="id">{{ unit }}</Option>
                  </Select>
                  <Tag closable @on-close="removeRepoConfig(index)" v-else style="height: 30px; margin-top: -0.5px;padding-top: 4px;"><span style="font-weight: bolder;">{{item.repo}}</span></Tag>
                </div>
              </div>
              <div style="margin-top: 3px" slot="extra">
                <Button type="primary" size="small" shape="circle" style="padding-left: 5px; padding-right: 5px" @click="addRepoConfig()"  v-if="index === repoConfigList.length - 1 && item.repo">
                  <Icon type="md-add"></Icon>
                </Button>
                <Button type="error" size="small" shape="circle" style="padding-left: 5px; padding-right: 5px" @click="removeRepoConfig(index)" v-if="repoConfigList.length !== 1">
                  <Icon type="md-remove"></Icon>
                </Button>
              </div>
              <div style="margin-left: 0;font-weight: bolder;margin-top: 10px;">配置:</div>
              <Row span="24" style="margin-top: 5px;" justify="start" type="flex" v-for="(release, idx) in item.releaseNameList" :key="idx">
                <div style="width: 85%; display: flex">
                  <Select v-model="release.releasename" v-if="!release.releasename" :transfer="true" filterable clearable>
                    <Option v-for="(releaseName, id) in release.releaseOption" :value="releaseName" :key="id">{{ releaseName }}</Option>
                  </Select>
                  <Tag closable @on-close="removeReleaseName(index, idx)" v-else style="width: 49%;height: 30px; margin-top: -0.5px;padding-top: 4px">{{release.releasename}}</Tag>
                  <Select v-model="release.config" :transfer="true" style="margin-left: 5px; margin-right: 5px" filterable clearable>
                    <Option v-for="(at, id) in item.atConfig" :value="at" :key="id">{{ at }}</Option>
                  </Select>
                </div>
                <div style="width: 15%; display: inline; margin-top: 5px">
                  <Button type="primary" size="small" shape="circle" style="padding-left: 5px; padding-right: 5px" v-if="(item.releaseNameList.length !== item.releaseList.length && idx === item.releaseNameList.length - 1) && release.releasename && release.config" @click="addReleaseName(index)">
                    <Icon type="md-add"></Icon>
                  </Button>
                  <Button type="error" size="small" shape="circle" style="padding-left: 5px; padding-right: 5px" v-if="item.releaseNameList.length !== 1" @click="removeReleaseName(index, idx)">
                    <Icon type="md-remove"></Icon>
                  </Button>
                </div>
              </Row>
            </Card>
          </Col>
        </Row>
      </TimelineItem>
    </Modal>
    <Modal v-model="addIntegrationTestAutoTestConfig" title="集成测试Pipeline新增自动化配置" :mask-closable="false" width="60%" :styles="{top: '50px', height: '90%', overflow: 'auto'}" @on-ok="saveItPipeline" @on-cancel="getItPipelineConfig">
      <Row :gutter="16" style="margin-top: 10px;">
        <Col style="margin-left: 2%;margin-right: 2%">
          <Card style="width:100%; height: 240px; overflow: auto; margin-top: 15px">
            <div slot="title">
              <div style="margin-left: 0;margin-top: 0; width: 90%">
                <span style="font-weight: bolder; margin-right: 5px">仓库：</span>
                <span style="font-weight: bolder">{{createAutoTestCfg.repo}}</span>
              </div>
            </div>
            <div style="margin-left: 0;font-weight: bolder;">配置:</div>
            <Row span="24" style="margin-top: 5px;" justify="start" type="flex" v-for="(release, idx) in createAutoTestCfg.releaseNameList" :key="idx">
              <div style="width: 85%; display: flex">
                <Select v-model="release.releasename" v-if="!release.releasename" :transfer="true" filterable clearable>
                  <Option v-for="(releaseName, id) in release.releaseOption" :value="releaseName" :key="id">{{ releaseName }}</Option>
                </Select>
                <Tag v-else style="width: 49%;height: 30px; margin-top: -0.5px;padding-top: 4px">{{release.releasename}}</Tag>
                <Select v-model="release.config"  :transfer="true" style="margin-left: 5px; margin-right: 5px" filterable clearable>
                  <Option v-for="(at, id) in createAutoTestCfg.atConfig" :value="at" :key="id">{{ at }}</Option>
                </Select>
              </div>
              <!--<div style="width: 15%; display: inline; margin-top: 5px">-->
                <!--<Button type="primary" size="small" shape="circle" v-if="(createAutoTestCfg.releaseNameList.length !== createAutoTestCfg.releaseList.length && idx === createAutoTestCfg.releaseNameList.length - 1) && release.releasename && release.config" @click="addReleaseName(index)">-->
                  <!--<Icon type="md-add"></Icon>-->
                <!--</Button>-->
                <!--<Button type="error" size="small" shape="circle" v-if="createAutoTestCfg.releaseNameList.length !== 1" @click="removeReleaseName(index, idx)">-->
                  <!--<Icon type="md-remove"></Icon>-->
                <!--</Button>-->
              <!--</div>-->
            </Row>
          </Card>
        </Col>
      </Row>
    </Modal>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <div class="time">仓库信息<Button type="primary" style="margin-left: 5px" size="small" @click="showAddRepoModal()"><Icon type="md-add" style="padding-right: 5px"></Icon>新增</Button></div>
        <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
          <Col span="12" v-for="(item, index) in config" :key="index">
            <Card style="width:100%; height: 240px; overflow: auto; margin-top: 10px">
              <div slot="title" style="width: 70%;" :title="item.repo">
                <div style="margin-left: 0;margin-top: 0; width: 90%; overflow: hidden;white-space: nowrap;text-overflow:ellipsis;">
                  <span style="font-weight: bolder; margin-right: 5px">仓库:</span>
                  <span>{{getRepoName(item.repo)}}</span>
                </div>
              </div>
              <ButtonGroup slot="extra" size="small" style="margin-top: -5px">
                <Button v-if="!item.editMode" type="error" @click="deleteConfirm(index)"><Icon type="md-remove" style="padding-right: 5px"></Icon>删除</Button>
              </ButtonGroup>
              <Row>
                配置:
                <Button v-if="item.releaseNameList.length < item.releaseConfig.length" type="primary" style="margin-left: 5px" size="small" @click="addAutoTestConfig(index)">
                  <Icon type="md-add" style="padding-right: 5px"></Icon>新增
                </Button>
              </Row>
              <Row type="flex" v-for="(config, idx) in item.releaseNameList" :key="idx" style="margin-top: 5px">
                <span style="margin-top: 3px;margin-right: 5px">发布项: </span><Tag color="primary">{{config.releasename}}</Tag>
                <span v-if="!config.enable_edit" style="margin-top: 3px;margin-left: 5px;margin-right: 5px">配置: </span><Tag v-if="!config.enable_edit" color="warning">{{config.config}}</Tag>
                <Select v-if="config.enable_edit" v-model="config.config" style="width:200px" placement="top" :transfer="true" filterable clearable>
                  <Option v-for="(at, id) in item.atConfig" :value="at" :key="id">{{ at }}</Option>
                </Select>
                <ButtonGroup size="small" style="margin-top:1px; margin-left: 5px">
                  <Button v-if="!config.enable_edit && item.releaseNameList.length > 1" type="primary" @click="deleteConfigConfirm(index, idx)"><Icon type="md-remove" style="padding-right: 5px"></Icon>删除</Button>
                  <Button v-if="!config.enable_edit" type="primary" @click="editConfirm(index, idx)"><i class="fa fa-pencil" style="padding-right: 5px" aria-hidden="true"></i>修改</Button>
                  <Button v-if="config.enable_edit"  type="default" @click="cancelConfirm(index, idx)"><i class="fa fa-ban" style="padding-right: 5px" aria-hidden="true"></i>取消</Button>
                  <Button v-if="config.enable_edit"  type="primary" @click="saveConfirm(index, idx)"><i class="fa fa-floppy-o" style="padding-right: 5px" aria-hidden="true"></i>保存</Button>
                </ButtonGroup>
              </Row>
            </Card>
          </Col>
        </Row>
      </TimelineItem>
    </Timeline>
  </div>
</template>

<script>
  import axios from 'axios'
  // import { Bus } from '@/global/bus'

  export default {
    name: 'integration-test-config-autotest',
    data: function () {
      return {
        name: '',
        id: this.$route.params.id,
        config: [],
        backupData: {
          config: []
        },
        repoList: [],
        repoOption: [],
        createAutoTestCfg: {},
        addIntegrationTestAutoTestRepo: false,
        addIntegrationTestAutoTestConfig: false,
        repoConfigList: [
          {
            repo: '',
            option: this.repoOption,
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ],
        addAutoTestConfigIndex: -1
      }
    },
    methods: {
      showAddRepoModal: function () {
        this.repoConfigList = [
          {
            repo: '',
            option: this.repoOption,
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ]
        this.addIntegrationTestAutoTestRepo = true
      },
      addRepoConfig: function () {
        let option = JSON.parse(JSON.stringify(this.repoOption))
        for (let cfg of this.repoConfigList) {
          let repo = cfg.repo
          let id = option.indexOf(repo)
          option.splice(id, 1)
        }
        this.repoConfigList.push({
          repo: '',
          option: option,
          releaseList: [],
          atConfig: [],
          releaseNameList: [
            {
              releasename: '',
              config: '',
              releaseOption: []
            }
          ]
        })
      },
      removeRepoConfig: function (index) {
        if (index !== 0) {
          this.repoConfigList.splice(index, 1)
        } else {
          this.repoConfigList.splice(index, 1)
          if (this.repoConfigList.length === 0) {
            let option = JSON.parse(JSON.stringify(this.repoOption))
            this.repoConfigList.push({
              repo: '',
              option: option,
              releaseList: [],
              atConfig: [],
              releaseNameList: [
                {
                  releasename: '',
                  config: '',
                  releaseOption: []
                }
              ]
            })
          }
        }
      },
      getRepoName: function (repo) {
        if (repo) {
          let temp = repo.toString().split('/')
          return temp[temp.length - 1].split('.git')[0]
        } else {
          return ''
        }
      },
      deleteConfirm: function (index) {
        this.$Modal.confirm({
          title: '确认',
          content: '<p> 确认要删除仓库' + this.getRepoName(this.config[index].repo) + '的配置吗？</p>',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.config.splice(index, 1)
            setTimeout(() => {
              this.saveItPipeline()
            }, 500)
          }
        })
      },
      deleteConfigConfirm: function (index, idx) {
        this.$Modal.confirm({
          title: '确认',
          content: '<p> 确认要删除配置吗？</p>',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.config[index].releaseNameList.splice(idx, 1)
            setTimeout(() => {
              this.saveItPipeline()
            }, 500)
          }
        })
      },
      editConfirm: function (index, idx) {
        this.backupData.config = JSON.parse(JSON.stringify(this.config))
        this.config[index].releaseNameList[idx].enable_edit = true
      },
      cancelConfirm: function (index, idx) {
        this.config[index].releaseNameList[idx].enable_edit = false
        this.config[index] = this.backupData.config[index]
      },
      saveConfirm: function (index, idx) {
        this.config[index].releaseNameList[idx].enable_edit = false
        this.saveItPipeline()
      },
      saveAddItPipeline: function () {
        this.config = this.config.concat(this.repoConfigList)
        for (let cfg of this.repoConfigList) {
          let idx = this.repoOption.indexOf(cfg.repo)
          if (idx !== -1) {
            this.repoOption.splice(idx, 1)
          }
        }
        this.repoConfigList = [
          {
            repo: '',
            option: [],
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ]
        this.repoConfigList[0].option = this.repoOption
        this.saveItPipeline()
      },
      saveItPipeline: function () {
        let config = this.filterObject(JSON.parse(JSON.stringify(this.config)))
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config'
        axios.post(url, {
          id: Number(self.$route.params.id),
          gitInfo: config
        }).then(function (message) {
          let data = message.data
          if (data.result) {
            self.$Message.success('修改成功！')
            self.getItPipelineConfig()
          } else {
            self.$Modal.error({
              title: '修改失败',
              content: '<P>' + message.data.info.toString() + '</P>'
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '修改失败',
            content: '<P>服务器内部错误</P>'
          })
        })
      },
      getItPipelineConfig: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config?id=' + this.$route.params.id.toString()
        axios.get(url).then(function (message) {
          let data = message.data
          self.config.length = 0
          if (data.result) {
            let direction = data.info.direction
            self.getRepoList(direction)
            let gitInfo = data.info.gitInfo

            let idx = 0
            for (let git of gitInfo) {
              self.config.push({
                repo: git.repo,
                releaseNameList: []
              })
              for (let cfg of git.releaseNameList) {
                let item = self.config[self.config.length - 1].releaseNameList
                item.push({
                  config: cfg.config,
                  releasename: cfg.releasename,
                  enable_edit: false
                })
              }
              self.getConfig(idx, git.repo)
              idx += 1
            }
          }
        })
      },
      getRepoList: function (value) {
        let self = this
        let url = ''
        if (typeof (value) === 'string') {
          url = this.getDomain('config') + '/get_git_list_new?group_id=' + value
        } else {
          let direction = ''
          if (value) {
            if (value[value.length - 1] !== '全部') {
              direction = value[value.length - 1]
            } else {
              direction = value[value.length - 2]
            }
          }
          url = this.getDomain('config') + '/get_git_list?name=' + direction
        }
        axios.get(url).then(function (message) {
          let data = message.data
          self.repoList = []
          for (let repo of data) {
            self.repoList.push(repo.git_addr)
          }
          self.repoOption = self.getOption(self.repoList)
          self.repoConfigList[0].option = JSON.parse(JSON.stringify(self.repoOption))
        })
      },
      getReleaseNameOption: function (idx) {
        let config = this.config[idx]
        config.releaseOption = [].concat(config.releaseConfig)
        for (let cfg of config.releaseNameList) {
          let idx = config.releaseOption.indexOf(cfg.releasename)
          if (idx !== -1) {
            config.releaseOption.splice(idx, 1)
          }
        }
        this.$set(this.config, idx, config)
      },
      getConfig: function (idx, repo) {
        let self = this
        let url = this.getDomain('config') + '/api/autotest/autotest_config?git_addr=' + repo
        axios.get(url).then(function (message) {
          let data = message.data
          if (data['result']) {
            self.config[idx].releaseConfig = []
            for (let info of data.info) {
              self.config[idx].releaseConfig.push(info.plus_name)
            }
            self.getReleaseNameOption(idx)
          }
        })
        url = this.getDomain('config') + '/autoTest/getTypeList'
        axios.post(url, {
          rdRepo: repo
        }).then(function (message) {
          let data = message.data
          if (data.status === 'success') {
            self.config[idx].atConfig = []
            for (let info of data.data) {
              self.config[idx].atConfig.push(info.type)
            }
          }
        })
      },
      filterObject: function (configList) {
        let result = [].concat(configList)
        for (let cfg of result) {
          delete cfg.atConfig
          delete cfg.releaseConfig
          delete cfg.releaseOption
          let releaseList = cfg.releaseNameList
          for (let release of releaseList) {
            delete release.enable_edit
          }
        }
        return result
      },
      getOption: function (repoList) {
        let repos = [].concat(repoList)
        for (let config of this.config) {
          let idx = repos.indexOf(config.repo)
          if (idx !== -1) {
            repos.splice(idx, 1)
          }
        }
        return repos
      },
      getAddConfig: function (idx, repo) {
        this.repoConfigList[idx].releaseNameList = [
          {
            releasename: '',
            config: '',
            releaseOption: []
          }
        ]
        let self = this
        let url = this.getDomain('config') + '/api/autotest/autotest_config?git_addr=' + repo
        axios.get(url).then(function (message) {
          let data = message.data
          if (data['result']) {
            self.repoConfigList[idx].releaseList = []
            for (let info of data.info) {
              self.repoConfigList[idx].releaseList.push(info.plus_name)
            }
            self.repoConfigList[idx].releaseNameList[0].releaseOption = [].concat(self.repoConfigList[idx].releaseList)
          }
        })
        url = this.getDomain('config') + '/autoTest/getTypeList'
        axios.post(url, {
          rdRepo: repo
        }).then(function (message) {
          let data = message.data
          if (data.status === 'success') {
            self.repoConfigList[idx].atConfig = []
            for (let info of data.data) {
              self.repoConfigList[idx].atConfig.push(info.type)
            }
          }
        })
      },
      addReleaseName: function (index) {
        let configOrigin = JSON.parse(JSON.stringify(this.repoConfigList[index].releaseList))
        let config = this.repoConfigList[index].releaseNameList
        for (let cfg of config) {
          let index = configOrigin.indexOf(cfg.releasename)
          configOrigin.splice(index, 1)
        }
        this.repoConfigList[index].releaseNameList.push({
          releasename: '',
          config: '',
          releaseOption: configOrigin
        })
      },
      removeReleaseName: function (index, idx) {
        if (idx !== 0) {
          this.repoConfigList[index].releaseNameList.splice(idx, 1)
        } else {
          this.repoConfigList[index].releaseNameList.splice(idx, 1)
          if (this.repoConfigList[index].releaseNameList.length === 0) {
            let configOrigin = JSON.parse(JSON.stringify(this.repoConfigList[index].releaseList))
            this.repoConfigList[index].releaseNameList.push({
              releasename: '',
              config: '',
              releaseOption: configOrigin
            })
          }
        }
      },
      addAutoTestConfig: function (index) {
        this.addAutoTestConfigIndex = index
        let option = JSON.parse(JSON.stringify(this.config[index].releaseConfig))
        for (let cfg of this.config[index].releaseNameList) {
          let idx = option.indexOf(cfg.releasename)
          option.splice(idx, 1)
        }
        this.createAutoTestCfg = {
          repo: this.config[index].repo,
          releaseNameList: this.config[index].releaseNameList,
          releaseConfig: this.config[index].releaseConfig,
          atConfig: this.config[index].atConfig
        }
        this.config[index].releaseNameList.push({
          config: '',
          releasename: '',
          releaseOption: option
        })
        this.addIntegrationTestAutoTestConfig = true
      }
    },
    mounted: function () {
      this.getItPipelineConfig()
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }
</style>
