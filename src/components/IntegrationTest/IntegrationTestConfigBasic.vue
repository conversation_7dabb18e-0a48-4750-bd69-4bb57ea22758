<template>
  <div>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <div class="time">名称
          <Button v-if="!nameDisplayMode" type="dashed" size="small" @click="editName" style="margin-left: 5px"><i class="fa fa-pencil" style="padding-right: 5px" aria-hidden="true"></i>修改</Button>
          <ButtonGroup v-if="nameDisplayMode">
            <Button type="dashed" size="small" @click="cancelName" style="margin-left: 5px"><i class="fa fa-ban" style="padding-right: 5px" aria-hidden="true"></i>取消</Button>
            <Button type="dashed" size="small" @click="saveName"><i class="fa fa-floppy-o" style="padding-right: 5px" aria-hidden="true"></i>保存</Button>
          </ButtonGroup>
        </div>
        <Row v-if="nameDisplayMode" type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
          <div style="padding-left:8px;padding-top: 5px">
            <Input v-model="name" style="width: 600px">
              <span slot="prepend">Pipeline名称</span>
            </Input>
          </div>
        </Row>
        <Row v-if="!nameDisplayMode" type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
          <div style="padding-left:8px;padding-top: 5px">
            {{name}}
          </div>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <div class="time">编排ID
          <Button v-if="!stackDisplayMode" type="dashed" size="small" @click="editStack" style="margin-left: 5px"><i class="fa fa-pencil" style="padding-right: 5px" aria-hidden="true"></i>修改</Button>
          <ButtonGroup v-if="stackDisplayMode">
            <Button type="dashed" size="small" @click="cancelStack" style="margin-left: 5px"><i class="fa fa-ban" style="padding-right: 5px" aria-hidden="true"></i>取消</Button>
            <Button type="dashed" size="small" @click="saveStack"><i class="fa fa-floppy-o" style="padding-right: 5px" aria-hidden="true"></i>保存</Button>
          </ButtonGroup>
        </div>
        <Row v-if="stackDisplayMode" type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
          <div style="padding-left:8px;padding-top: 5px">
            <Input v-model="stackId" style="width: 600px">
              <span slot="prepend">编排ID</span>
            </Input>
          </div>
        </Row>
        <Row v-if="!stackDisplayMode" type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
          <div style="padding-left:8px;padding-top: 5px">
            {{stackId}}
          </div>
        </Row>
      </TimelineItem>
    </Timeline>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'

  export default {
    name: 'integration-test-config-basic',
    data: function () {
      return {
        name: '',
        id: this.$route.params.id,
        stackId: '',
        direction: [],
        nameDisplayMode: false,
        stackDisplayMode: false,
        backupData: {}
      }
    },
    methods: {
      getItPipelineConfig: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config?id=' + this.$route.params.id.toString()
        axios.get(url).then(function (message) {
          let data = message.data
          if (data.result) {
            self.stackId = data.info.stackId
            self.direction = data.info.direction
            self.enable = true
            self.config = data.info.gitInfo
            self.name = data.info.name
            self.backupData = {
              stackId: data.info.stackId,
              enable: true,
              name: data.info.name
            }
          }
        })
      },
      editName: function () {
        this.nameDisplayMode = true
      },
      cancelName: function () {
        this.name = this.backupData.name
        this.nameDisplayMode = false
      },
      saveName: function () {
        if (this.name) {
          this.saveItPipeline()
          this.nameDisplayMode = false
        } else {
          this.$Message.info('存在参数未填写！')
        }
      },
      editStack: function () {
        this.stackDisplayMode = true
      },
      cancelStack: function () {
        this.stackId = this.backupData.stackId
        this.stackDisplayMode = false
      },
      saveStack: function () {
        if (this.stackId) {
          this.saveItPipeline()
          this.stackDisplayMode = false
        } else {
          this.$Message.info('存在参数未填写！')
        }
      },
      saveItPipeline: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config'
        axios.post(url, {
          id: Number(self.$route.params.id),
          stackId: self.stackId,
          name: self.name
        }).then(function (message) {
          let data = message.data
          if (data.result) {
            self.$Message.success('修改成功！')
            self.getItPipelineConfig()
            Bus.$emit('refreshItPipelineConfig')
          } else {
            self.$Modal.error({
              title: '修改失败',
              content: '<P>' + message.data.info.toString() + '</P>'
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '修改失败',
            content: '<P>服务器内部错误</P>'
          })
        })
      }
    },
    mounted: function () {
      this.getItPipelineConfig()
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }
</style>
