<template>
  <div>
    <head-component></head-component>
    <div v-if="exists">
      <Card :bordered="true" :style="noticeStyle">
        <Row type="flex">
          <Col span="14">
            <Row style="display: flex">
              <span :style="{'fontWeight':'bolder'}"><i class="fa fa-list-ul" style="padding-right: 5px;font-size: 24px"></i><span style="font-weight: bolder;font-size: 24px; color: #2b85e4">{{name}}</span></span>
            </Row>
            <Row>
              <Tag color="primary" v-for="(item, index) in directionName" :key="index">{{item}}</Tag>
            </Row>
          </Col>
          <Col span="10">
          </Col>
        </Row>
      </Card>
      <Card :style="cardStyle">
        <div :style="cardTitleStyle" class="tool-tab">
          <Tabs :value="tabName">
            <TabPane :label="basicLabel" name="basic">
              <integration-test-config-basic></integration-test-config-basic>
            </TabPane>
            <TabPane :label="atLabel" name="at">
              <integration-test-config-autotest></integration-test-config-autotest>
            </TabPane>
          </Tabs>
        </div>
      </Card>
    </div>
    <div v-else>
      <content-not-found-component></content-not-found-component>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import Vue from 'vue'
  import { Bus } from '@/global/bus'
  import notFound from '@/components/Common/contentNotFound'
  import Head from '@/components/Common/Head'
  import IntegrationTestConfigBasic from './IntegrationTestConfigBasic'
  import IntegrationTestConfigAutotest from './IntegrationTestConfigAutoTest'
  Bus.$on('refreshItPipelineConfig', function () {
    Bus.integrationTestPipelineDetail.getItPipelineConfig()
  })
  Vue.component('content-not-found-component', notFound)
  Vue.component('head-component', Head)
  let screeHeight = window.screen.height
  let noticeStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '30px'
  }

  let cardStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '15px',
    'minHeight': (screeHeight - 325).toString() + 'px',
    'marginBottom': '20px'
  }

  let cardTitleStyle = {
    'minHeight': (screeHeight - 415).toString() + 'px',
    'overflowY': 'auto'
  }

  export default {
    name: 'integration-test-detail-page',
    components: {IntegrationTestConfigAutotest, IntegrationTestConfigBasic},
    data: function () {
      return {
        tabName: 'basic',
        exists: true,
        noticeStyle: noticeStyle,
        cardStyle: cardStyle,
        cardTitleStyle: cardTitleStyle,
        basicLabel: (h) => {
          return h('div', [
            h('span', {
              attrs: {
                class: 'ivu-tag-dot-inner'
              },
              style: {
                backgroundColor: '#2d8cf0'
              }
            }),
            h('span', '基础配置')
          ])
        },
        atLabel: (h) => {
          return h('div', [
            h('span', {
              attrs: {
                class: 'ivu-tag-dot-inner'
              },
              style: {
                backgroundColor: '#2d8cf0'
              }
            }),
            h('span', '自动化配置')
          ])
        },
        name: '',
        id: '',
        stackId: '',
        direction: '',
        directionName: []
      }
    },
    methods: {
      getItPipelineConfig: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config?id=' + this.$route.params.id.toString()
        axios.get(url).then(function (message) {
          let data = message.data
          if (data.result) {
            self.id = data.info.id
            self.stackId = data.info.stackId
            self.direction = data.info.direction
            self.enable = true
            self.config = data.info.gitInfo
            self.name = data.info.name
            self.getDirectionName(data.info.direction)
          }
        })
      },
      getDirectionName: function (direction) {
        if (typeof (direction) === 'string') {
          this.direction = direction
          let self = this
          axios.get(this.getDomain('config') + '/api/direction/get_list?get_data_by_id=&group_id=' + direction).then(function (message) {
            let result = []
            if (JSON.stringify(message.data) !== '{}') {
              if (message.data.name) {
                if (message.data.grandfather && message.data.grandfather !== '未分类') {
                  result.push(message.data.grandfather)
                }
                if (message.data.parent && message.data.grandfather !== '未分类') {
                  result.push(message.data.parent)
                }
                result.push(message.data.name)
                self.directionName = result
              } else {
                result.push(direction.toString() + '(过期方向)')
                self.directionName = result
              }
            } else {
              result.push(direction.toString() + '(过期方向)')
              self.directionName = result
            }
          })
        } else {
          // 旧版本方向
          this.directionName = direction
        }
      }
    },
    mounted: function () {
      this.getItPipelineConfig()
      Bus.integrationTestPipelineDetail = this
    }
  }
</script>

<style scoped>
</style>
