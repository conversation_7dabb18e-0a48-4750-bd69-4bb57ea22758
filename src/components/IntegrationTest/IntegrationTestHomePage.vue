<template>
  <div>
    <Modal v-model="addIntegrationTestPipelineModal" title="新增集成测试Pipeline配置" @on-ok="saveItPipeline" :mask-closable="false" width="90%" :styles="{top: '50px', height: '90%', overflow: 'auto'}">
      <Timeline style="padding-top: 15px">
        <TimelineItem>
          <div class="time">名称
          </div>
          <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
            <div style="padding-left:8px;padding-top: 5px">
              <Input v-model="name" style="width: 600px">
                <span slot="prepend">Pipeline名称</span>
              </Input>
            </div>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <div class="time">方向
          </div>
          <Row style="margin-top: 15px">
            <Cascader :data="directionList" placeholder="请选择方向" style="width:600px;padding-left: 0px;display: inline-block" @on-change="getRepoList"></Cascader>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <div class="time">编排ID</div>
          <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
            <div style="padding-left:8px;padding-top: 5px">
              <Input v-model="stackID" style="width: 600px">
              <span slot="prepend">编排ID</span>
              </Input>
            </div>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <div class="time">RD仓库信息
          </div>
          <Row :gutter="16" style="margin-top: 10px;">
            <Col span="12" v-for="(item, index) in repoConfigList" :key="index">
              <Card style="width:100%; height: 240px; overflow: auto; margin-top: 15px">
                <div slot="title">
                  <div style="margin-left: 0;margin-top: 0; width: 90%">
                    <span style="font-weight: bolder; margin-right: 5px">仓库：</span>
                    <Select v-model="item.repo" v-if="!item.repo" style="width:80%" @on-change="getConfig(index, item.repo)">
                      <Option v-for="(unit, id) in item.option" :value="unit" :key="id">{{ unit }}</Option>
                    </Select>
                    <Tag v-else style="height: 30px; margin-top: -0.5px;padding-top: 4px;"><span style="font-weight: bolder;">{{item.repo}}</span></Tag>
                  </div>
                </div>
                <div style="margin-top: 3px" slot="extra">
                  <Button type="primary" size="small" style="padding-left: 5px; padding-right: 5px" shape="circle" @click="addRepoConfig()"  v-if="index === repoConfigList.length - 1 && item.repo">
                    <Icon type="md-add"></Icon>
                  </Button>
                  <Button type="error" size="small" style="padding-left: 5px; padding-right: 5px" shape="circle" @click="removeRepoConfig(index)" v-if="repoConfigList.length !== 1">
                    <Icon type="md-remove"></Icon>
                  </Button>
                </div>
                <div style="margin-left: 0;font-weight: bolder;margin-top: 10px;">配置:</div>
                <Row span="24" style="margin-top: 5px;" justify="start" type="flex" v-for="(release, idx) in item.releaseNameList" :key="idx">
                  <div style="width: 85%; display: flex">
                    <Select v-model="release.releasename" v-if="!release.releasename" :transfer="true">
                      <Option v-for="(releaseName, id) in release.releaseOption" :value="releaseName" :key="id">{{ releaseName }}</Option>
                    </Select>
                    <Tag v-else style="width: 49%;height: 30px; margin-top: -0.5px;padding-top: 4px">{{release.releasename}}</Tag>
                    <Select v-model="release.config" :transfer="true" style="margin-left: 5px; margin-right: 5px">
                      <Option v-for="(at, id) in item.atConfig" :value="at" :key="id">{{ at }}</Option>
                    </Select>
                  </div>
                  <div style="width: 15%; display: inline; margin-top: 5px">
                    <Button type="primary" size="small" style="padding-left: 5px; padding-right: 5px" shape="circle" v-if="(item.releaseNameList.length !== item.releaseList.length && idx === item.releaseNameList.length - 1) && release.releasename && release.config" @click="addReleaseName(index)">
                      <Icon type="md-add"></Icon>
                    </Button>
                    <Button type="error" size="small" style="padding-left: 5px; padding-right: 5px" shape="circle" v-if="item.releaseNameList.length !== 1" @click="removeReleaseName(index, idx)">
                      <Icon type="md-remove"></Icon>
                    </Button>
                  </div>
                </Row>
              </Card>
            </Col>
          </Row>
        </TimelineItem>
      </Timeline>
    </Modal>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
        <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
          <div slot="title">
            <Row type="flex" style="width: 98.8%">
              <Col style="width: 85%;display: flex">
                <div>
                  <span :style="{fontWeight:'bolder'}">集成测试Pipeline</span>
                </div>
              </Col>
            </Row>
          </div>
          <div>
            <Alert show-icon closable banner style="margin-top: 20px">
              <template slot="desc">
                <span style="font-weight: bolder">Demo版本页面会有一些bug，请谅解。</span>
                <div><span style="font-weight: bolder">后续会针对使用情况排期进行优化。</span></div>
                <div><span style="font-weight: bolder">相关使用上的反馈请联系刘振舟（liuzhenzhou）、张淼（zhangmiao08）、孙博（sunbo09）。</span></div>
              </template>
            </Alert>
            <Row type="flex">
              <Col span="6">
                <Card dis-hover :bordered="true" style="height: 250px; cursor: pointer; margin-left: 10px; margin-right: 0; width:auto; margin-top: 15px; padding: 0; border-width: 0;">
                  <Button class="tool-it-button" @click="addIntegrationTestPipeline">
                    <div><Icon type="ios-add-circle-outline" style="margin-left: 0; font-size: 40px; margin-top: 10px; color: #2b85e4;"></Icon></div>
                    <div style="text-align: center; font-weight: bolder; color: #2b85e4;padding-top: 5px;font-size: 14px">新增集成测试Pipeline</div>
                  </Button>
                </Card>
              </Col>
              <Col span="6" v-for="(item, index) in displayList" :key="index">
                <Card dis-hover :bordered="true" style="height: 250px; cursor: pointer; margin-left: 10px; margin-right: 0; width:auto; margin-top: 15px; padding: 0; border-width: 0">
                  <Button class="tool-it-button" :style="getStyle(item)">
                    <Row style="font-weight: bolder;font-size: 18px;width: 100%;overflow: hidden;white-space: nowrap;text-overflow:ellipsis;">
                      <a target="_blank" :href="item.buildurl" v-if="item.buildurl">
                        <Icon type="md-link" style="padding-right: 5px"></Icon>Job
                      </a>
                      <span v-else><Icon type="md-link" style="padding-right: 5px"></Icon>Job</span>
                      ●
                      <a target="_blank" :href="getUrl(index)">{{item.name}}</a>
                    </Row>
                    <Row style="padding-left: 15px;padding-top: 5px"><Tag color="primary" v-for="(unit,idx) in item.direction" :key="idx" v-if="enableDirection(idx, item.direction.length)">{{unit}}</Tag></Row>
                    <Row style="padding-left: 15px; font-weight: bolder; padding-top: 5px;font-size: 14px">
                      <Icon class="tool-item" type="md-square"></Icon>启用状态：
                      <Tag color="success" v-if="item.enable">启用</Tag>
                    </Row>
                    <div v-if="item.enable_result">
                      <Row style="padding-left: 15px; font-weight: bolder; padding-top: 5px;font-size: 14px">
                        <span style="padding-right: 5px"><Icon type="md-square" class="tool-item"></Icon>仓库数量: </span>
                        <span style="color: #2b85e4; padding-right: 5px">{{item.repo.length}}</span>个, 有自动化测试的
                        <span style="color: #19be6b; padding-right: 5px">{{item.repo.size}}</span>个。
                      </Row>
                      <Row style="padding-left: 15px; font-weight: bolder; padding-top: 5px;font-size: 14px">
                        <Icon class="tool-item" type="md-square"></Icon>Case：<Tag color="primary">{{item.total.count.size}}个 / {{item.total.count.length}}个</Tag><Tag color="primary">{{item.total.count.per}}%</Tag>
                      </Row>
                      <Row style="padding-left: 15px; font-weight: bolder; padding-top: 5px;font-size: 14px">
                        <Icon class="tool-item" type="md-square"></Icon>行覆盖率：<Tag color="primary">{{item.total.cov.size}}行 / {{item.total.cov.length}}行</Tag><Tag color="primary">{{item.total.cov.per}}%</Tag>
                      </Row>
                      <Row style="padding-left: 15px; font-weight: bolder; padding-top: 5px;font-size: 14px">
                        <Icon class="tool-item" type="md-square"></Icon>触发信息：<Tag color="primary">{{item.vtag}}</Tag>
                      </Row>
                    </div>
                    <div v-else style="padding-bottom: 110px"></div>
                  </Button>
                </Card>
              </Col>
            </Row>
          </div>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  export default {
    name: 'integration-test-homepage',
    data: function () {
      return {
        addIntegrationTestPipelineModal: false,
        stackID: '',
        direction: '',
        directionName: [],
        name: '',
        repoList: [],
        directionList: [],
        repoConfigList: [
          {
            repo: '',
            option: [],
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ],
        displayList: []
      }
    },
    methods: {
      addIntegrationTestPipeline: function () {
        this.name = ''
        this.direction = ''
        this.repoConfigList = [
          {
            repo: '',
            option: [],
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ]
        this.repoConfigList[0].option = [].concat(this.repoList)
        this.addIntegrationTestPipelineModal = true
      },
      getDirectionList: function () {
        let self = this
        axios.get(this.getDomain('config') + '/api/direction/get_org_list?display_disable=0').then(function (message) {
          self.directionList.length = 0
          if (message.data.result) {
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      getRepoList: function (value, selectedData) {
        let self = this
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = item
        let direction = item.key
        this.direction = item.key
        let url = this.getDomain('config') + '/get_git_list_new?group_id=' + direction
        axios.get(url).then(function (message) {
          let data = message.data
          self.repoList = []
          for (let repo of data) {
            self.repoList.push(repo.git_addr)
          }
          self.repoConfigList[0].option = JSON.parse(JSON.stringify(self.repoList))
        })
        this.repoConfigList = [
          {
            repo: '',
            option: [],
            releaseList: [],
            atConfig: [],
            releaseNameList: [
              {
                releasename: '',
                config: '',
                releaseOption: []
              }
            ]
          }
        ]
      },
      addRepoConfig: function () {
        let option = JSON.parse(JSON.stringify(this.repoList))
        for (let cfg of this.repoConfigList) {
          let repo = cfg.repo
          let id = option.indexOf(repo)
          option.splice(id, 1)
        }
        this.repoConfigList.push({
          repo: '',
          option: option,
          releaseList: [],
          atConfig: [],
          releaseNameList: [
            {
              releasename: '',
              config: '',
              releaseOption: []
            }
          ]
        })
      },
      removeRepoConfig: function (index) {
        this.repoConfigList.splice(index, 1)
      },
      addReleaseName: function (index) {
        let configOrigin = JSON.parse(JSON.stringify(this.repoConfigList[index].releaseList))
        let config = this.repoConfigList[index].releaseNameList
        for (let cfg of config) {
          let index = configOrigin.indexOf(cfg.releasename)
          configOrigin.splice(index, 1)
        }
        this.repoConfigList[index].releaseNameList.push({
          releasename: '',
          config: '',
          releaseOption: configOrigin
        })
      },
      removeReleaseName (index, idx) {
        this.repoConfigList[index].releaseNameList.splice(idx, 1)
      },
      getConfig: function (idx, repo) {
        this.repoConfigList[idx].releaseNameList = [
          {
            releasename: '',
            config: '',
            releaseOption: []
          }
        ]
        let self = this
        let url = this.getDomain('config') + '/api/autotest/autotest_config?git_addr=' + repo
        axios.get(url).then(function (message) {
          let data = message.data
          if (data['result']) {
            self.repoConfigList[idx].releaseList = []
            for (let info of data.info) {
              self.repoConfigList[idx].releaseList.push(info.plus_name)
            }
            self.repoConfigList[idx].releaseNameList[0].releaseOption = [].concat(self.repoConfigList[idx].releaseList)
          }
        })

        url = this.getDomain('config') + '/autoTest/getTypeList'
        axios.post(url, {
          rdRepo: repo
        }).then(function (message) {
          let data = message.data
          if (data.status === 'success') {
            self.repoConfigList[idx].atConfig = []
            for (let info of data.data) {
              self.repoConfigList[idx].atConfig.push(info.type)
            }
          }
        })
      },
      saveItPipeline: function () {
        if (this.stackID && this.direction && this.name) {
          let self = this
          let url = this.getDomain('envmonitor') + '/api/integrate/config'
          axios.post(url, {
            stackId: this.stackID,
            direction: this.direction,
            gitInfo: this.filterObject(this.repoConfigList),
            name: this.name
          }).then(function (message) {
            let data = message.data
            if (data.result) {
              self.$Message.success('新建成功！')
              self.repoConfigList = [
                {
                  repo: '',
                  option: [],
                  releaseList: [],
                  atConfig: [],
                  releaseNameList: [
                    {
                      releasename: '',
                      config: '',
                      releaseOption: []
                    }
                  ]
                }
              ]
              self.stackID = ''
              self.direction = ''
              self.name = ''
              self.getItPipelineConfig()
            } else {
              self.$Modal.error({
                title: '新建失败',
                content: '<P>' + message.data['info'].toString() + '</P>'
              })
            }
          }).catch(function () {
            self.$Modal.error({
              title: '新建失败',
              content: '<P>服务器内部错误</P>'
            })
          })
        } else {
          this.$Message.info('存在参数未填写！')
          setTimeout(() => {
            this.addIntegrationTestPipelineModal = true
          }, 500)
        }
      },
      filterObject: function (configList) {
        let result = [].concat(configList)
        for (let config of result) {
          delete config.atConfig
          delete config.option
          delete config.releaseList
          let releaseNameList = config.releaseNameList
          for (let unit of releaseNameList) {
            delete unit.releaseOption
          }
        }
        return result
      },
      getItPipelineConfig: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate/config_all'
        axios.get(url).then(function (message) {
          let data = message.data
          if (data.result) {
            self.displayList = []
            for (let item of data.info) {
              if (typeof (item.integrate_config.direction) !== 'string') {
                let unit = {
                  id: item.id,
                  direction: item.integrate_config.direction,
                  enable: true,
                  enable_result: false,
                  name: item.integrate_config.name
                }
                self.displayList.push(unit)
              } else {
                let unit = {
                  id: item.id,
                  direction: [],
                  enable: true,
                  enable_result: false,
                  name: item.integrate_config.name
                }
                self.displayList.push(unit)
                self.getDirectionName(item.integrate_config.direction, self.displayList.length - 1)
              }
            }
            self.getItPipelineResult()
          }
        })
      },
      enableDirection: function (idx, directionLength) {
        if (directionLength === 0) {
          return false
        } else {
          if (directionLength <= 2) {
            return true
          } else {
            if (idx === 0 || idx === 1 || idx === directionLength - 1) {
              return true
            } else {
              return false
            }
          }
        }
      },
      getItPipelineResult: function () {
        let self = this
        let url = this.getDomain('envmonitor') + '/api/integrate_result_table?build_url=all'
        axios.get(url).then(function (message) {
          let status = message.data.result
          if (status) {
            let data = message.data.info
            let ids = Object.keys(data)
            for (let pipeline of self.displayList) {
              self.$set(pipeline, 'status', 'NOT_START')
              if (ids.indexOf(pipeline.id.toString()) !== -1) {
                self.$set(pipeline, 'enable_result', true)
                console.log(pipeline)
                self.$set(pipeline, 'status', data[pipeline.id.toString()].result_status)
                self.$set(pipeline, 'vtag', data[pipeline.id.toString()].vtag_info)
                self.$set(pipeline, 'buildurl', '')
                pipeline.total = {
                  count: {
                    length: 0,
                    size: 0,
                    per: 0
                  },
                  cov: {
                    length: 0,
                    size: 0,
                    per: 0
                  }
                }
                pipeline.repo = {
                  length: 0,
                  size: 0
                }
              }
              if (data[pipeline.id.toString()]) {
                self.$set(pipeline, 'buildurl', data[pipeline.id.toString()].build_url)
                if (data[pipeline.id.toString()].result_info !== null) {
                  pipeline.total.count = {
                    length: data[pipeline.id.toString()].result_info.git_cov_autotest.all.autotest_info.total,
                    size: data[pipeline.id.toString()].result_info.git_cov_autotest.all.autotest_info.passes,
                    per: data[pipeline.id.toString()].result_info.git_cov_autotest.all.autotest_info.passingRate
                  }
                  pipeline.total.cov = {
                    length: data[pipeline.id.toString()].result_info.git_cov_autotest.all.cov_info.line_total,
                    size: data[pipeline.id.toString()].result_info.git_cov_autotest.all.cov_info.line_covered,
                    per: data[pipeline.id.toString()].result_info.git_cov_autotest.all.cov_info.line_percentage
                  }
                }
                pipeline.repo = {
                  length: data[pipeline.id.toString()].total_git,
                  size: data[pipeline.id.toString()].autotest_git
                }
              }
            }
          }
        })
      },
      getStyle: function (item) {
        let color = '#dcdee2'
        if (item.status === 'SUCCESS') {
          color = '#19be6b'
        } else if (item.status === 'FAILURE') {
          color = '#ed4014'
        } else if (item.status === 'UNSTABLE') {
          color = '#ff9900'
        } else {
          color = '#dcdee2'
        }

        return {
          textAlign: 'left',
          borderLeftColor: color,
          borderLeftWidth: '4px'
        }
      },
      getUrl: function (index) {
        return '/homepage/cd/it/' + this.displayList[index].id
      },
      getDirectionName: function (directionId, index) {
        let self = this
        axios.get(this.getDomain('config') + '/api/direction/get_list?get_data_by_id=&group_id=' + directionId).then(function (message) {
          let result = []
          if (JSON.stringify(message.data) !== '{}') {
            if (message.data.name) {
              if (message.data.grandfather && message.data.grandfather !== '未分类') {
                result.push(message.data.grandfather)
              }
              if (message.data.parent && message.data.grandfather !== '未分类') {
                result.push(message.data.parent)
              }
              result.push(message.data.name)
              self.$set(self.displayList[index], 'direction', result)
            } else {
              result.push(directionId.toString() + '(过期方向)')
              self.$set(self.displayList[index], 'direction', result)
            }
          } else {
            result.push(directionId.toString() + '(过期方向)')
            self.$set(self.displayList[index], 'direction', result)
          }
        })
      }
    },
    mounted: function () {
      this.getDirectionList()
      this.getItPipelineConfig()
    }
  }
</script>

<style scoped>
  .tool-it-button{
    margin-left: -16px;
    margin-top: -16px;
    height: 251px;
    width: 108%;
    background-color: #ffffff
  }

  .tool-it-button:hover{
    color: #495060
  }

  .tool-item{
    padding-right: 5px;
    font-size: 10px;
    margin-top: -2px;
    color: #2b85e4
  }
</style>
