<template>
  <Row class="toolchain-content">
    <h4 class="title" style="margin-left: 15px">
      <Row type="flex" style="width: 100%">
        <Col style="width: 85%; display: flex">
          我的订阅
          <div style="margin-top: -2px">
            <Button icon="md-settings" size="small" class="tool-button" to="/rss/config">订阅管理</Button>
          </div>
          <!--<div style="display: flex; margin-left: 5px; margin-top: -6px">-->
            <!--<Cascader style="width: 400px; margin-left: 5px" :data="filterList" v-model="filterSelect" v-if="cqGitList.length > 0" placeholder="请选择要筛选的方向" @on-change="filterDirection"></Cascader>-->
          <!--</div>-->
        </Col>
        <Col style="text-align: right; width: 15%">
          <Input v-model="search" icon="md-search" placeholder="搜索" style="margin-top: -6px"/>
        </Col>
      </Row>
      <Row>
        <div style="font-size:13px; margin-top: 10px; font-weight: bold">当前订阅的方向：
          <Button style="border-color: #ffd591;color: #fa8c16!important; background: #fff7e6;font-weight: bolder; margin-right: 5px" size="small" :to="'/direction/detail/config/' + direction.key" v-for="direction in rssDirection" :key="direction.key">{{direction.value}}</Button>
        </div>
      </Row>
    </h4>
    <div v-if="rssDirection.length === 0">
      <span style="padding-left: 15px; font-size: 12px;font-weight: bolder">未订阅、或订阅信息为空</span>
    </div>
    <div v-else>
      <div v-if="!isLoading">
        <div v-if="cqGitList.length === 0">
          <span style="padding-left: 15px; font-size: 12px;font-weight: bolder">未找到符合条件的项目</span>
        </div>
        <div style="min-height: 620px" v-if="cqGitList.length !== 0">
          <Row type="flex" style="margin-top: 10px">
            <Col span="4" v-for="(item, index) in cqGitList" :key="index">
              <direction-analytics-card :project="item" :callback_message="'refreshRssPageData'" :deleteAuth="false"></direction-analytics-card>
            <!--<repo-card :repo="repo" :star_list=starList :callback_message="'refreshRssPageData'" :index="index"></repo-card>-->
            </Col>
          </Row>
          <Row style="text-align: right; margin-top: 15px">
            <Page :total="gitTotal" :page-size='pageSize' @on-page-size-change="changePageSize" @on-change="changePage" :current='currentPage'></Page>
          </Row>
        </div>
      </div>
      <Spin v-else style="margin-top: 5px">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
    </div>
  </Row>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '../../global/bus'
  import DirectionAnalyticsCard from '../Direction/DirectionAnalytics/DirectionAnalyticsCard'
  // import RepoCard from '../Homepage/RepoCard'

  Bus.$on('refreshRssPageData', function () {
    Bus.rss.getThumbnails()
  })

  export default {
    name: 'my-rss',
    components: {DirectionAnalyticsCard},

    data: function () {
      return {
        self: this,
        mis: Bus.userInfo.userLogin,
        filterSelect: [],
        desc: false,
        filterPriority: [],
        filterPriorityList: [],
        filterPriorityListSelect: [],
        filterStage: [],
        filterStageList: [],
        filterStageListSelect: [],
        cqGitList: [],
        isRss: true,
        rssDirection: [],
        originThumbnailsData: [],
        backupTemporaryThumbnailsData: [],
        countTemporaryThumbnailsData: [],
        isLoading: false,
        gitTotal: 0,
        pageSize: 24,
        currentPage: 1,
        search: '',
        starList: []
      }
    },
    methods: {
      getThumbnails: function () {
        let self = this
        self.isLoading = true
        let rssDirectionList = this.getActDirectionList()
        if (rssDirectionList.length > 0) {
          // 查询订阅列表
          axios.get(this.getDomain('cq') + '/star/query?mis=' + this.mis).then(function (message) {
            let response = message.data
            if (response.status === 'success') {
              let data = response.data
              if (data.length > 0) {
                self.starList = data[0].star_list
                self.starList = []
              }
            }
            self.getThumbnailsCore(rssDirectionList)
          }).catch(function () {
            self.starList = []
            self.isLoading = false
          })
        } else {
          self.isLoading = false
        }
      },
      getThumbnailsCore: function (rssDirectionList) {
        let self = this
        self.originThumbnailsData = []
        let projectListData = []
        let i = 0
        for (let each in rssDirectionList) {
          let directionList = []
          axios.get(this.getDomain('config') + '/mcd/org/analytics/get?direction_id=' + rssDirectionList[each]).then(function (message) {
            if (message.data.result) {
              axios.get(self.getDomain('config') + '/mcd/org/basic_info?direction_id=' + rssDirectionList[each]).then(function (mes) {
                if (mes.data.result) {
                  for (const direction of mes.data.info.parent_list) {
                    if (directionList.indexOf(direction.direction_name) === -1) {
                      directionList.push(direction.direction_name)
                    }
                  }
                }
              })
              let thumbnailsData = message.data.info
              for (let item in thumbnailsData) {
                if (projectListData.indexOf(thumbnailsData[item].projectId) === -1) {
                  projectListData.push(thumbnailsData[item].projectId)
                  let temp = {
                    projectId: thumbnailsData[item].projectId,
                    projectName: thumbnailsData[item].projectName,
                    directionId: rssDirectionList[each],
                    direction: directionList,
                    if_owner: thumbnailsData[item].if_owner,
                    star: self.starList.indexOf(thumbnailsData[item].projectId) !== -1
                  }
                  self.originThumbnailsData.push(temp)
                }
              }
            }
            i += 1
            if (i === rssDirectionList.length) {
              self.isLoading = false
              self.setTemporaryThumbnailsData()
              // self.setFilterList()
              self.setPageDefault()
            }
          })
        }
      },
      getRssConfig: function () {
        let self = this
        axios.post(this.getDomain('cq') + '/rss/query', JSON.stringify({
          'mis': this.mis
        })).then(function (message) {
          if (message.data.status === 'success') {
            self.rssDirection = []
            let rssDirection = []
            if (message.data.data.length > 0) {
              rssDirection = message.data.data[0]['direction_list']
            }
            for (let direction of rssDirection) {
              self.rssDirection.push({
                key: direction.toString(),
                label: '',
                value: ''
              })
            }
            for (let idx in self.rssDirection) {
              let currentItem = self.rssDirection[idx]
              axios.get(self.getDomain('config') + '/mcd/org/basic_info?direction_id=' + currentItem.key).then(function (message) {
                if (JSON.stringify(message.data.result)) {
                  self.$set(currentItem, 'label', message.data.info.direction_name)
                  self.$set(currentItem, 'value', message.data.info.direction_name)
                } else {
                  self.$set(currentItem, 'label', currentItem.key.toString() + '(过期方向)')
                  self.$set(currentItem, 'value', currentItem.key.toString() + '(过期方向)')
                }
              })
            }
            self.getThumbnails()
          }
        }).catch(function () {
          self.$Modal.error({
            title: '查询订阅配置失败',
            content: '<P>未知错误</P>'
          })
          setTimeout(function () {
            self.$Modal.remove()
            self.batchDraver = true
          }, 1000)
        })
      },
      getActDirectionList: function () {
        let result = []
        for (let direction of this.rssDirection) {
          result.push(direction.key)
        }
        return result
      },
      setTemporaryThumbnailsData: function () {
        this.temporaryThumbnailsData = []
        this.temporaryThumbnailsData = this.originThumbnailsData
        this.backupTemporaryThumbnailsData = this.temporaryThumbnailsData
        this.countTemporaryThumbnailsData = this.temporaryThumbnailsData
        this.gitTotal = this.temporaryThumbnailsData.length
      },
      setFilterList: function () {
        if (this.temporaryThumbnailsData.length > 0) {
          this.filterList = [{
            value: '全部',
            label: '全部',
            children: []
          }]
          let rootNode = this.filterList
          for (let each of this.temporaryThumbnailsData) {
            let currentNode = rootNode
            let directions = each.direction
            for (let index in directions) {
              let obj = {
                value: directions[index],
                label: directions[index],
                children: []
              }
              currentNode = this.insertNode(obj, currentNode)
            }
          }
        }
      },
      insertNode: function (obj, currentNode) {
        let keys = []
        for (let i in currentNode) {
          keys.push(currentNode[i].value)
        }

        if (keys.indexOf(obj.value) === -1) {
          if (keys.indexOf('全部')) {
            currentNode.push({
              value: '全部',
              label: '全部',
              children: []
            })
          }
          currentNode.push(obj)
        }
        for (let node of currentNode) {
          if (node.value === obj.value) {
            currentNode = node.children
          }
        }
        return currentNode
      },
      setPageDefault: function () {
        if (this.temporaryThumbnailsData) {
          this.cqGitList = []
          let count = this.gitTotal > this.pageSize ? this.pageSize : this.gitTotal
          for (let i = 0; i < count; i++) {
            this.cqGitList.push(this.temporaryThumbnailsData[i])
          }
        }
      },
      changeDesc: function () {
        let originDate = this.temporaryThumbnailsData
        this.temporaryThumbnailsData = []
        let sortedObjKeys = []
        if (this.desc) {
          // 降序
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[b].score - originDate[a].score
          })
        } else {
          // 升序
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[a].score - originDate[b].score
          })
        }
        for (let key of sortedObjKeys) {
          this.temporaryThumbnailsData.push(originDate[key])
        }
        this.gitTotal = this.temporaryThumbnailsData.length
        this.setPageDefault()
        this.desc = !this.desc
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage()
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage()
      },
      changeCurrentPage: function () {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.cqGitList = []
        let count = this.gitTotal > pageSize * currentPage ? pageSize * currentPage : this.gitTotal
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.cqGitList.push(this.temporaryThumbnailsData[i])
        }
      },
      filterDirection: function (values) {
        let key = ''
        let isNotLeaf = false
        if (values.indexOf('全部') !== -1) {
          isNotLeaf = true
        }
        for (let value in values) {
          if (Number(value) === 0) {
            key += values[value]
          } else {
            if (values[value] !== '全部') {
              key += values[value]
            }
          }
        }
        if (!key) {
          this.setTemporaryThumbnailsData()
          this.setFilterList()
          this.setPageDefault()
        } else {
          if (key === '全部') {
            this.setTemporaryThumbnailsData()
            this.setFilterList()
            this.setPageDefault()
          } else {
            let originThumbnailsData = []
            this.temporaryThumbnailsData = []
            for (let git of this.originThumbnailsData) {
              let originDirections = git.direction
              let originKey = ''
              for (let direction of originDirections) {
                originKey += direction
              }
              if (isNotLeaf) {
                if (originKey.indexOf(key) !== -1) {
                  originThumbnailsData.push(git)
                }
              } else {
                if (originKey.toString() === key.toString()) {
                  originThumbnailsData.push(git)
                }
              }
            }
            let sortedObjKeys = Object.keys(originThumbnailsData).sort(function (a, b) {
              return originThumbnailsData[b].score - originThumbnailsData[a].score
            })
            for (let key of sortedObjKeys) {
              this.temporaryThumbnailsData.push(originThumbnailsData[key])
            }
            this.backupTemporaryThumbnailsData = this.temporaryThumbnailsData
            this.countTemporaryThumbnailsData = this.temporaryThumbnailsData
            this.gitTotal = this.temporaryThumbnailsData.length
            this.setPageDefault()
          }
        }
      },
      getRepo: function (url) {
        let repo = ''
        if (url) {
          repo = url.split('/')[url.split('/').length - 1].split('.')[0]
        }
        return repo
      }
    },
    mounted: function () {
      Bus.rss = this
      this.getRssConfig()
      // this.setStageFilter()
      // this.setPriorityFilter()
    },
    watch: {
      search: function () {
        if (this.search) {
          this.temporaryThumbnailsData = []
          for (let key of this.originThumbnailsData) {
            if (key.projectId.indexOf(this.search) !== -1 || key.projectName.indexOf(this.search.toUpperCase()) !== -1) {
              this.temporaryThumbnailsData.push(key)
            }
          }
          this.backupTemporaryThumbnailsData = this.temporaryThumbnailsData    // 对于筛选标签位置会有所影响 因为筛选标签的数据来自于backupTemporaryThumbnailsData
          this.gitTotal = this.temporaryThumbnailsData.length
          this.setPageDefault()
        } else {
          this.setTemporaryThumbnailsData()
          this.setPageDefault()
        }
      }
    }
  }
</script>

<style scoped>
  .toolchain-content {
    background-color: #ffffff;
    margin: 10px 15px 20px 15px;
    padding: 15px 10px 20px 10px;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }

  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
