<template>
  <div>
    <head-component></head-component>
    <div style=" margin-left: 15px; margin-right: 15px; margin-top: 15px">
      <Breadcrumb style="margin-top: 15px; margin-left: 15px">
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/rss/config">订阅管理</BreadcrumbItem>
      </Breadcrumb>
      <div>
        <Row class="toolchain-content" :style=style>
          <h4 class="title" style="margin-left: 15px">订阅管理</h4>
          <Timeline style="margin-left: 15px; margin-top: 15px">
            <TimelineItem>
              <div class="title">当前已经订阅的方向：</div>
              <Row v-if="rssDirection.length > 0" style="margin-top: 5px">
                <Tag closable @on-close="deleteChooseDirection(index)" color="primary" v-for="(item, index) of rssDirection" :key="item.key">{{item.value}}</Tag>
              </Row>
              <div v-else style="font-weight: bolder;margin-top: 5px">
                当前还没有订阅任何方向！
              </div>
            </TimelineItem>
            <TimelineItem>
              <div class="title">新增订阅方向：</div>
              <div style="padding-top: 5px">
                <Icon type="md-help-circle" style="font-weight: bolder; color: #ffa500; margin-top: -2px"></Icon>
                <span style="padding-left: 5px;font-weight: bolder;color:#c5c8ce">除集团虚拟节点外，其他所有节点，包括非叶子节点，都可以选择订阅。</span>
                <span style="font-weight: bolder;color:#c5c8ce">如订阅<span style="font-weight: bolder; color: #1a1a1a">集团/酒店旅游/住宿/酒店服务端</span>方向，只需点击选择级联选择框到 <span style="font-weight: bolder; color: #1a1a1a">酒店服务端</span>方向，然后点击<span style="font-weight: bolder; color: #1a1a1a">订阅已选方向</span>按钮即可。</span>
                <span style="font-weight: bolder;color:#c5c8ce">方向订阅<span style="font-weight: bolder; color: #1a1a1a">支持多选</span>。</span>
              </div>
              <Row type="flex" style="margin-top: 5px">
                <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" filterable :transfer="false" style="width: 25%" change-on-select @on-change="getCurrentChooseDirection"></Cascader>
                <Button v-if="direction.length !== 0" type="primary" size="small" style="margin-left: 5px" @click="addCurrentDirection()"><Icon type="md-add" />订阅</Button>
              </Row>
            </TimelineItem>
          </Timeline>
        </Row>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '../../global/bus'
  export default {
    name: 'rss-homepage',
    components: {},
    data: function () {
      return {
        mis: Bus.userInfo.userLogin,
        name: Bus.userInfo.userName,
        style: {},
        rssDirection: [],
        directionList: [],
        currentChooseDirection: -1,
        direction: []
      }
    },
    methods: {
      getRssConfig: function () {
        let self = this
        axios.post(this.getDomain('cq') + '/rss/query', JSON.stringify({
          'mis': this.mis
        })).then(function (message) {
          if (message.data.status === 'success') {
            self.rssDirection = []
            let rssDirection = []
            if (message.data.data.length > 0) {
              rssDirection = message.data.data[0]['direction_list']
            }
            for (let direction of rssDirection) {
              self.rssDirection.push({
                key: direction.toString(),
                label: '',
                value: ''
              })
            }
            for (let idx in self.rssDirection) {
              let currentItem = self.rssDirection[idx]
              axios.get(self.getDomain('config') + '/mcd/org/basic_info?direction_id=' + currentItem.key).then(function (message) {
                if (JSON.stringify(message.data.result)) {
                  self.$set(currentItem, 'label', message.data.info.direction_name)
                  self.$set(currentItem, 'value', message.data.info.direction_name)
                } else {
                  self.$set(currentItem, 'label', currentItem.key.toString() + '(过期方向)')
                  self.$set(currentItem, 'value', currentItem.key.toString() + '(过期方向)')
                }
              })
            }
            Bus.rssDirection = self.rssDirection
          }
        }).catch(function () {
          self.$Modal.error({
            title: '查询订阅配置失败',
            content: '<P>未知错误</P>'
          })
          setTimeout(function () {
            self.$Modal.remove()
            self.batchDraver = true
          }, 1000)
        })
      },
      deleteChooseDirection: function (index) {
        let self = this
        let direction = this.rssDirection[index].value
        this.$Modal.confirm({
          title: '确认',
          content: '<P>确认取消订阅 ' + direction + ' 方向吗？</P>',
          onOk: function () {
            self.rssDirection.splice(index, 1)
            self.saveRssConfig()
          }
        })
      },
      getDirections: function () {
        let self = this
        axios(this.getDomain('config') + '/mcd/org/basic?direction_id=1&disable=1').then(function (message) {
          if (message.data.result) {
            self.directionList.length = 0
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      getActDirectionList: function () {
        let result = []
        for (let direction of this.rssDirection) {
          result.push(direction.key)
        }
        return result
      },
      getCurrentChooseDirection: function (value, selectedData) {
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = {
          key: item.direction_id.toString(),
          label: item.label,
          value: item.value
        }
      },
      addCurrentDirection: function () {
        let temp = []
        temp.push(this.currentChooseDirection)
        this.rssDirection = this.arrayUniqueConcat(this.rssDirection, temp, 'key')
        this.direction = []
        this.saveRssConfig()
      },
      saveRssConfig: function () {
        let self = this
        axios.post(this.getDomain('cq') + '/rss/add', JSON.stringify({
          mis: this.mis,
          name: this.name,
          direction_list: this.getActDirectionList()
        })).then(function (message) {
          if (message.data.status === 'success') {
            self.rssCq = false
            self.getRssConfig()
            self.$Message.success('订阅成功')
          } else {
            self.$Modal.error({
              title: '订阅失败',
              content: '<P>' + message.data.reason.toString() + '</P>'
            })
            setTimeout(function () {
              self.$Modal.remove()
            }, 1000)
          }
        }).catch(function () {
          self.$Modal.error({
            title: '订阅失败',
            content: '<P>未知错误</P>'
          })
          setTimeout(function () {
            self.$Modal.remove()
          }, 1000)
        })
      }
    },
    mounted: function () {
      this.style = {
        'minHeight': this.getToolchainScreeHeight()
      }
      this.getRssConfig()
      this.getDirections()
    }
  }
</script>

<style>
  .toolchain-content {
    background-color: #ffffff;
    margin: 10px 10px 20px 10px;
    padding: 15px 10px 20px 10px;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }
  .title {
    font-weight: bolder;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }
</style>
