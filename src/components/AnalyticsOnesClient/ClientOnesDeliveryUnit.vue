<template>
  <div>
    <div v-if="display5" id="quality">
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="onScheduleRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="rejectReqDevRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table highlight-row :height="600" border class="ivu-table-customize" :row-class-name="rowClassName" :columns="columns5" :data="deliveryStats"></Table>
    </div>
    <div v-else>
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="onScheduleRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="rejectReqDevRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table border :row-class-name="rowClassName" :columns="columns5" :data="deliveryStats"></Table>
    </div>
  </div>
</template>

<script>
  // import axios from 'axios'
  import { BusClient } from '@/global/bus'
  // import router from '@/router'
  import clientDeliveryExpend from './ClientOnesDeliveryExpendUnit'
  // import { analyticsbaseAPI } from '@/global/variable'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  HighchartsMore(Highcharts)

  BusClient.$on('refreshClientStatsOnes', function (data) {
    BusClient.clientDeliveryUnitObject.deliveryStats = data
    // BusClient.$emit('showProcessMetricsTab')
    // console.log('busProcess', BusClient.clientProcessUnitObject.deliveryStats)
    BusClient.clientDeliveryUnitObject.setHighChartData(data)
  })

  BusClient.$on('refreshClientTableLengthOnes', function (data) {
    BusClient.clientDeliveryUnitObject.display5 = BusClient.clientDeliveryUnitObject.adjustTableHight(data)
  })
  export default {
    // components: {Layout},
    components: {clientDeliveryExpend},
    name: 'client-ones-delivery-unit',
    data: function () {
      return {
        display5: false,
        columns5: [
          {
            type: 'expand',
            width: 50,
            render: (h, params) => {
              return h(clientDeliveryExpend, {
                props: {
                  row: params.row
                }
              })
            }
          },
          {
            title: '项目',
            key: 'bizLine',
            // width: 20,
            sortable: true
          },
          {
            title: '方向',
            key: 'appType',
            // width: 20,
            sortable: true
          },
          {
            title: '版本',
            // width: 110,
            key: 'version'
          },
          {
            title: '总开发Task个数',
            key: 'taskCnt',
            sortable: true
            // width: 120,
          },
          {
            title: '如期提测需求占比',
            key: 'onScheduleRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '提测打回需求占比',
            key: 'rejectReqDevRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    target: '_blank',
                    to: this.jump(params.row)
                  }
                  // style: {
                  //   marginRight: '5px'
                  // },
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                }, '查看详情')
              ])
            }
          }
        ],
        deliveryStats: [],
        directories: [],
        onScheduleRatio: [],
        rejectReqDevRatio: []
      }
    },

    methods: {
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0) {
          return 'demo-stripe'
        } else {
          return ''
        }
      },
      jump: function (data) {
        //   let self = this
        //   // console.log('查询详情：', data)
        //   self.$Spin.show()
        //   // router.push({ path: '/client/detail' })
        //   let projectKey, version, bizLine, appType, platformName, component
        //   projectKey = data['projectKey']
        //   version = data['version']
        //   bizLine = data['bizLine']
        //   appType = data['appType']
        //   platformName = data['platformName']
        //   component = data['component']
        //   let params = {
        //     projectKey: projectKey,
        //     version: version,
        //     bizLine: bizLine,
        //     appType: appType,
        //     platformName: platformName,
        //     component: component
        //   }
        //   // console.log(params)
        //   axios.get(analyticsbaseAPI + '/client/detail', {
        //     params: params,
        //     timeout: 50000000,
        //     dataType: 'json'
        //   }
        //   ).then(function (message) {
        //     if (message['data']['status'] === 0) {
        //       // console.log(message)
        //       // console.log('保存的查询条件', BusClient.metricsdata)
        //       // router.push({ path: '/detail' })
        //       BusClient.detailData = []
        //       BusClient.detailData = message['data']['data']
        //       console.log('detailData: ' + BusClient.detailData)
        //       BusClient.$emit('refreshClientDetailData', BusClient.detailData)
        //       // console.log('BusClient-detailData', BusClient.detailData)
        //     } else {
        //       alert(message['data']['msg'])

        //     }
        //     self.$Spin.hide()
        //   }).catch(error => {
        //     self.$Spin.hide()
        //     console.log(error)
        //   })
        // }
        const dict = {
          projectKey: data['projectKey'],
          version: data['version'],
          bizLine: data['bizLine'],
          appType: data['appType'],
          platformName: data['platformName'],
          component: data['component']
        }
        let temp = ''
        temp += '/client/detail/ones?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        // console.log('temp: ' + temp)
        return temp
      },
      setHighChartData: function (data) {
        // 提取横轴各方向数据
        const self = this
        self.directories = []
        self.onScheduleRatio = []
        self.rejectReqDevRatio = []
        for (var i in data) {
          if (self.directories.indexOf(data[i].bizLine) === -1) {
            self.directories.push(data[i].bizLine)
          }
        }
        console.log('directoriesdelivery: ', self.directories)
        // 提取如期提测需求数据
        let andOnScheduleRatio = []
        let iOSOnScheduleRatio = []
        let andRejectReqRatio = []
        let iOSRejectReqRatio = []
        for (var each in self.directories) {
          for (var y in data) {
            if (data[y].bizLine === self.directories[each] && data[y].appType === 'Android') {
              andOnScheduleRatio.push(parseFloat(data[y].onScheduleRatio))
              andRejectReqRatio.push(parseFloat(data[y].rejectReqDevRatio))
            } else if (data[y].bizLine === self.directories[each] && data[y].appType === 'iOS') {
              iOSOnScheduleRatio.push(parseFloat(data[y].onScheduleRatio))
              iOSRejectReqRatio.push(parseFloat(data[y].rejectReqDevRatio))
            } else {
              continue
            }
          }
        }
        let tempAnd = {}
        tempAnd = {
          name: 'Android',
          data: andOnScheduleRatio
        }
        let tempiOS = {}
        tempiOS = {
          name: 'iOS',
          data: iOSOnScheduleRatio
        }
        self.onScheduleRatio.push(tempiOS)
        self.onScheduleRatio.push(tempAnd)
        console.log('onScheduleRatio: ', self.onScheduleRatio)
        // 提取提测打回需求数据
        let tempAndRejectReqRatio = {
          name: 'Android',
          data: andRejectReqRatio
        }
        let tempIosRejectReqRatio = {
          name: 'iOS',
          data: iOSRejectReqRatio
        }
        self.rejectReqDevRatio.push(tempIosRejectReqRatio)
        self.rejectReqDevRatio.push(tempAndRejectReqRatio)
        console.log('rejectReqRatio: ', self.rejectReqDevRatio)

        setTimeout(function () {
          self.onScheduleRatioHighchart()
          self.rejectReqDevRatioHighchart()
        }, 0)
      },
      onScheduleRatioHighchart: function () {
        Highcharts.chart('onScheduleRatio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '如期提测需求占比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + '%'
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.onScheduleRatio,
          credits: {
            enabled: false
          }
        })
      },
      rejectReqDevRatioHighchart: function () {
        Highcharts.chart('rejectReqDevRatio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '提测打回需求占比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: true
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + '%'
                }
              },
              pointPadding: 0.25,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.rejectReqDevRatio,
          credits: {
            enabled: false
          }
        })
      }
    },
    mounted: function () {
      BusClient.clientDeliveryUnitObject = this
      // console.log('begin', BusClient.qualityMetricsUnitObject)
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }

  /*选中某一行高亮*/
  .ivu-table-row-highlight td {
    background-color: #cfd2d4a1 !important;
  }

  .ivu-table-customize th{
    color:#0f1115;
    font-weight: bold;
    background-color: #cfd2d4a1;
  }


</style>
