<!--suppress ALL -->
<template>
  <div>
    <div v-if="display5" id="quality">
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="devtestratio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="devquality" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table highlight-row :height="600" border class="ivu-table-customize" :row-class-name="rowClassName" :columns="columns5" :data="resourceStats"></Table>
    </div>
    <div v-else>
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="devtestratio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
        <div id="devquality" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table border :row-class-name="rowClassName" :columns="columns5" :data="resourceStats"></Table>
    </div>
  </div>
</template>

<script>
  import { BusClient } from '@/global/bus'
  import clientResourceExpend from './ClientOnesResourceExpendUnit'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  HighchartsMore(Highcharts)

  BusClient.$on('refreshClientStatsOnes', function (data) {
    BusClient.clientResourceUnitObject.resourceStats = data
    // BusClient.$emit('showProcessMetricsTab')
    // console.log('resourceStats: ', BusClient.clientResourceUnitObject.resourceStats)
    BusClient.clientResourceUnitObject.setHighChartData(data)
  })

  BusClient.$on('refreshClientTableLengthOnes', function (data) {
    BusClient.clientResourceUnitObject.display5 = BusClient.clientResourceUnitObject.adjustTableHight(data)
  })
  export default {
    // components: {Layout},
    components: {clientResourceExpend},
    name: 'client-ones-resource-unit',
    data: function () {
      return {
        display5: false,
        columns5: [
          {
            type: 'expand',
            width: 50,
            render: (h, params) => {
              return h(clientResourceExpend, {
                props: {
                  row: params.row
                }
              })
            }
          },
          {
            title: '项目',
            key: 'bizLine',
            // width: 30,
            sortable: true
          },
          {
            title: '方向',
            key: 'appType',
            // width: 20,
            sortable: true
          },
          {
            title: '版本',
            width: 140,
            key: 'version'
          },
          {
            title: '需求耗时',
            key: 'devPd'
            // width: 120,
          },
          {
            title: '联调时长',
            key: 'jointDebugPd'
            // width: 120,
          },
          {
            title: '联调占比',
            key: 'jointDebugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '提测质量(不含checker)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'averageBugPerDayWithoutChecker',
            sortable: true
          },
          {
            title: '有效Bug(不含checker)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '有效Bug'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'nonCheckerValidBugCnt',
            sortable: false
          },
          {
            title: '提测质量',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'averageBugPerDay',
            // width: 140,
            sortable: true
          },
          {
            title: '有效Bug',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '有效Bug'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'validBugCnt',
            sortable: false
          },
          {
            title: '产品Bug',
            key: 'validPmBugCnt',
            // width: 140,
            sortable: true
          },
          {
            title: '测试效率',
            key: 'testDevRatio',
            // width: 140,
            sortable: true,
            render: (h, params) => {
              return h('div', {}, this.transferEmpty(params.row['testDevRatio']) + ' : 1')
            }
          },
          {
            title: '迭代Bug占比',
            key: 'iterationBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '正式提测质量',
            key: 'nonIterationBugPerDay',
            // width: 140,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank'
                  }
                }, '查看详情')
              ])
            }
          }
        ],
        resourceStats: [],
        directories: [],
        devtestratio: [],
        devquality: []
      }
    },

    methods: {
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0) {
          return 'demo-stripe'
        } else {
          return ''
        }
      },
      transferEmpty: function (data) {
        if (data === 0) {
          return 'N/A'
        } else {
          return data
        }
      },
      jump: function (data) {
        const dict = {
          projectKey: data['projectKey'],
          version: data['version'],
          bizLine: data['bizLine'],
          appType: data['appType'],
          platformName: data['platformName'],
          component: data['component']
        }
        let temp = ''
        temp += '/client/detail/ones?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      },
      setHighChartData: function (data) {
        // 提取横轴各方向数据
        const self = this
        self.directories = []
        self.devtestratio = []
        self.devquality = []
        for (var i in data) {
          if (self.directories.indexOf(data[i].bizLine) === -1) {
            self.directories.push(data[i].bizLine)
          }
        }
        console.log('directoriesResource: ', self.directories)
        // 提取测试效率数据
        let andDevRatio = []
        let iOSDevRatio = []
        let andDevQuality = []
        let iOSDevQuality = []
        for (var each in self.directories) {
          for (var y in data) {
            if (data[y].bizLine === self.directories[each] && data[y].appType === 'Android') {
              andDevRatio.push(data[y].testDevRatio)
              andDevQuality.push(parseFloat(data[y].averageBugPerDay))
            } else if (data[y].bizLine === self.directories[each] && data[y].appType === 'iOS') {
              iOSDevRatio.push(data[y].testDevRatio)
              iOSDevQuality.push(parseFloat(data[y].averageBugPerDay))
            } else {
              continue
            }
          }
        }
        let tempAnd = {}
        tempAnd = {
          name: 'Android',
          data: andDevRatio
        }
        let tempiOS = {}
        tempiOS = {
          name: 'iOS',
          data: iOSDevRatio
        }
        self.devtestratio.push(tempiOS)
        self.devtestratio.push(tempAnd)
        console.log('devtestratio: ', self.devtestratio)
        // 提取开发质量数据
        let tempAndQuality = {}
        tempAndQuality = {
          name: 'Android',
          data: andDevQuality
        }
        let tempiOSQuality = {}
        tempiOSQuality = {
          name: 'iOS',
          data: iOSDevQuality
        }
        self.devquality.push(tempiOSQuality)
        self.devquality.push(tempAndQuality)
        console.log('devquality: ', self.devquality)

        setTimeout(function () {
          self.devTestRatioHighchart()
          self.devQualityHighchart()
        }, 0)
      },
      devTestRatioHighchart: function () {
        Highcharts.chart('devtestratio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '开发测试比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: 6,
              width: 2,
              label: {
                text: '6:1',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -10
              }
            }],
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + ':1'
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.devtestratio,
          credits: {
            enabled: false
          }
        })
      },
      devQualityHighchart: function () {
        Highcharts.chart('devquality', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '提测质量(有效bug/需求耗时）'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: 0.5,
              width: 2,
              label: {
                text: '0.5',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -10
              }
            }],
            allowDecimals: true
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000'
              },
              pointPadding: 0.25,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.devquality,
          credits: {
            enabled: false
          }
        })
      }

    },
    mounted: function () {
      BusClient.clientResourceUnitObject = this
      // console.log('begin', BusClient.qualityMetricsUnitObject)
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }

  /*选中某一行高亮*/
  .ivu-table-row-highlight td {
    background-color: #cfd2d4a1 !important;
  }

  .ivu-table-customize th{
    color:#0f1115;
    font-weight: bold;
    background-color: #cfd2d4a1;
  }

  .highcharts {
    width: 50%
  }


</style>
