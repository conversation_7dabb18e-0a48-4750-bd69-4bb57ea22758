<template>
  <Table highlight-row :columns="expendcolumn" :data="row.dataList"></Table>
</template>

<script>
  export default {
    name: 'client-ones-resource-expend-unit',
    props: {
      row: Object
    },
    data () {
      return {
        expendcolumn: [
          {
            title: '版本',
            key: 'version'
          },
          {
            title: '开发时长',
            key: 'devPd'
            // width: 120,
          },
          {
            title: '联调时长',
            key: 'jointDebugPd'
            // width: 120,
          },
          {
            title: '联调占比',
            key: 'jointDebugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '提测质量(不含checker)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'averageBugPerDayWithoutChecker',
            sortable: true
          },
          {
            title: '有效Bug(不含checker)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '有效Bug'),
                h('div', {}, '(除checker)')
              ])
            },
            key: 'nonCheckerValidBugCnt',
            sortable: false
          },
          {
            title: '提测质量',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '提测质量'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'averageBugPerDay',
            // width: 140,
            sortable: true
          },
          {
            title: '有效Bug',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '有效Bug'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'validBugCnt',
            sortable: false
          },
          {
            title: '产品Bug',
            key: 'validPmBugCnt',
            // width: 140,
            sortable: true
          },
          {
            title: '测试效率',
            key: 'testDevRatio',
            // width: 140,
            sortable: true,
            render: (h, params) => {
              return h('div', {}, this.transferEmpty(params.row['testDevRatio']) + ' : 1')
            }
          },
          {
            title: '迭代Bug占比',
            key: 'iterationBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '正式提测质量',
            key: 'nonIterationBugPerDay',
            // width: 140,
            sortable: true
          }
        ]
      }
    },
    methods: {
      transferEmpty: function (data) {
        if (data === 0) {
          return 'N/A'
        } else {
          return data
        }
      }
    }
  }
</script>

<style scoped>

  .ivu-table-header th{
    color:#FFD3B4;
    font-weight: bold;
    background-color: #f8f8f9;
    border: none;
  }

</style>
