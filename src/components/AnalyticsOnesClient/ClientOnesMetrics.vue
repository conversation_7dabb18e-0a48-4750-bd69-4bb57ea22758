<template>
  <div>
    <head-component id="head"></head-component>

    <div>
      <Card class="title" style="padding-top: 0px" aria-hidden="true">
        <Row>
          <Col order="1" span="10" style="display:flex">
          <div style="padding-top: 6px">
            <Tooltip placement="bottom" content="点击查看指标说明及统计规则">
              <!--<Button style="color: red;" @click="link">数据说明</Button>-->
              <!--<Icon type="ios-help-empty" style="padding-left:13px;padding-top: 4px;size: 50px;font-size: larger;font-weight: bolder" color="red"></Icon>-->
              <Icon type="help-circled" style="padding-left:13px;padding-top: 4px;font-size: larger;font-weight: bolder" color="red"></Icon>
              <a href="https://123.sankuai.com/km/page/34689019" target="_blank">数据说明</a>
            </Tooltip>
          </div>
          <h4 style="padding-left: 100px">平台</h4>
          <Select :transfer=true style="margin-left:15px;padding-top: 4px;max-width: 150px;min-width: 150px;text-align: left" v-model="value1" :label-in-value="true" @on-change="v=>{ setOption(v)}">
            <Option v-for="item in platformList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          </Col>
          <Col order="2" span="10" style="text-align: right;display:flex">
          <h4 style="width: 100px">版本</h4>
          <Select :transfer=true multiple style="margin-left:15px;padding-top: 4px;max-width: 400px;min-width: 150px;text-align: left" v-model="value2" :label-in-value="true">
            <Option v-for="item in versionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <div style="margin-left:5px;padding-top: 4px">
            <Button type="primary" @click='postData(value1,value2)'>查看</Button>
          </div>
          </Col>
        </Row>
      </Card>
    </div>
    <div v-show="display3" style="padding-right: 25px;padding-left: 25px">
      <Tabs>
        <TabPane label="项目消耗">
          <client-ones-resource-unit></client-ones-resource-unit>
        </TabPane>
        <TabPane label="如期交付">
          <client-ones-delivery-unit></client-ones-delivery-unit>
        </TabPane>
        <TabPane label="流程效果">
          <client-ones-process-unit></client-ones-process-unit>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Vue from 'vue'
import Head from '@/components/Common/Head'
import { BusClient } from '@/global/bus'
import { analyticsbaseAPI } from '@/global/variable'
// import router from '@/router'
import Layout from 'view-design/src/components/layout/layout'
import ClientOnesResourceUnit from './ClientOnesResourceUnit'
import ClientOnesDeliveryUnit from './ClientOnesDeliveryUnit'
import ClientOnesProcessUnit from './ClientOnesProcessUnit'
Vue.component('head-component', Head)

BusClient.$on('showClientMetricsTabOnes', function () {
  BusClient.processMetricsObject.display3 = true
  BusClient.processMetricsObject.display5 = true
})
BusClient.$on('refreshDistributionOnes', function () {
  BusClient.processMetricsObject.distribution = true
})
export default {
  components: {
    ClientOnesProcessUnit,
    ClientOnesDeliveryUnit,
    ClientOnesResourceUnit,
    Layout
  },
  name: 'client-ones-metrics',
  data: function () {
    return {
      modal1: false,
      pattern: '',
      modal6: false,
      display2: false,
      display3: false,
      display5: false,
      distribution: false,
      value1: '',
      value2: '',
      platformList: [],
      versionList: [],
      versionMap: {},
      length: 0
    }
  },

  methods: {
    setOption(value) {
      this.versionList = []
      for (let index in this.versionMap[value.value]) {
        let version = {}
        version['value'] = this.versionMap[value.value][index]
        version['label'] = this.versionMap[value.value][index]
        this.versionList.push(version)
      }
    },
    getVersionData(self) {
      console.log('function', self)
      axios
        .get(analyticsbaseAPI + '/common/client/version', {
          timeout: 10000,
          dataType: 'json'
        })
        .then(function (message) {
          if (message['data']['status'] === 0) {
            self.versionMap = message['data']['data']

            for (let platformName in self.versionMap) {
              let platform = {}
              platform['value'] = platformName
              platform['label'] = platformName
              self.platformList.push(platform)
            }
          } else {
            console.log(message['data']['msg'])
          }
          // BusClient.$emit('refreshTable', message['data']['data'], self)
          // return message['data']['data']
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    postData(value1, value2) {
      let self = this
      self.display2 = false
      let status = 1
      // console.log('value1:', value1)
      // console.log('value2:', value2)
      if (value1 === '') {
        self.$Message.info('请选择平台！')
      } else if (value2 === '') {
        self.$Message.info('请选择客户端版本！')
      } else {
        self.$Spin.show()
        status = 0
        var value3 = value2.join(',')
        // console.log('value3:', value3)
        let params = {
          platformName: value1,
          version: value3
        }
        BusClient.metricsdata = []
        BusClient.metricsdata = {
          value1: self.value1,
          value2: self.value3
        }
        if (status === 0) {
          axios
            .get(analyticsbaseAPI + '/client/stats', {
              params: params,
              timeout: 10000000,
              dataType: 'json'
            })
            .then(function (message) {
              // console.log(message)
              if (message['data']['status'] === 0) {
                let clientStats = message['data']['data']
                self.length = clientStats.length
                // console.log('length', self.length)
                BusClient.$emit('refreshClientStats', clientStats)
                BusClient.$emit('refreshClientTableLength', self.length)
                BusClient.$emit('showClientMetricsTab')
              } else {
                alert(message['data']['msg'])
              }
              self.$Spin.hide()
            })
            .catch((error) => {
              self.$Spin.hide()
              console.log(error)
            })
        } else {
          self.$Spin.hide()
          self.$Message.info('存在未选择的参数！')
        }
      }
    }
  },
  mounted: function () {
    console.log('start page!!!')
    BusClient.processMetricsObject = this
    let self = this
    let click = true
    self.display3 = false
    if (BusClient.metricsdata) {
      click = true
      self.value1 = BusClient.metricsdata.value1
      self.value2 = BusClient.metricsdata.value2
    } else {
      click = false
    }
    if (click) {
      if (self.value1 && self.value2) {
        self.postData(self.value1, self.value2)
      }
    }
    self.getVersionData(self)
  }
}
</script>

<style scoped>
</style>
