<template>
    <div>
        <el-dialog title="经验知识抽取测试" :visible.sync="visible">
            <el-form ref="form" label-width="120px">
                <el-form-item label="规则描述">
                    <el-input v-model="query" placeholder="请输入规则" type="textarea" :rows="5"></el-input>
                </el-form-item>
                <el-divider></el-divider>
                <el-form-item label="规则类型">
                    <el-select v-model="rule_type" placeholder="请选择规则类型" class="form-item">
                        <el-option label="叠加规则" value="stacking"></el-option>
                        <el-option label="佣金规则" value="佣金规则"></el-option>
                        <el-option label="门槛规则" value="门槛规则"></el-option>
                        <el-option label="详细设计" value="详细设计"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-divider></el-divider>
            <div v-for="message in data">
                <pre class="col-item">{{ message }}</pre>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="doRetrive" type="primary">获取</el-button>
                <el-button @click="visible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import api from './store/api'
export default {
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  data() {
    return {
      query: '',
      rule_type: '',
      data: []
    }
  },
  methods: {
    doRetrive() {
      const payload = {
        text: this.query,
        rule_type: this.rule_type
      }
      api.retriveExperience(payload).then(res => {
        this.data = res.data.answer
        console.log(res)
        console.log(this.data)
      }).catch(e => {
        this.$notify({ title: '获取失败', message: `入参${payload}\n` + e, type: 'error' })
      })
    }
  }
}
</script>

<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    width: 100%;
    line-height: 1.42857143;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}

.drawer-title {
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.custom-form-item {
    margin-bottom: 0px;
}
</style>