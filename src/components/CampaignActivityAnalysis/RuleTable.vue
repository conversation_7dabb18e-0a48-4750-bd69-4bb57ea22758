<template>
    <div>
        <el-drawer :visible.sync="visible" :modal="false">
            <div slot="title">
                <span class="drawer-title">规则列表</span>
                <el-tooltip class="operation-button" effect="dark" content="刷新" placement="top">
                    <el-button icon="el-icon-refresh" plain circle size="mini" @click="sync"></el-button>
                </el-tooltip>
            </div>
            <el-table v-loading="loading" :data="ruleList" style="width: 100%">
                <el-table-column v-for="header in headers" :key="header.value" :label="header.text" :prop="header.value"
                    sortable>
                    <template slot-scope="scope">
                        {{ formatText(scope.row[header.value]) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" content="详情" placement="top">
                            <el-button size="mini" icon="el-icon-setting" circle type="primary"
                                @click="onDetail(scope.row)"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
        <SaveRuleDialog :visible.sync="isDialog" :data="current"></SaveRuleDialog>
    </div>
</template>
    
<script>
import api from './store/api'
import SaveRuleDialog from './SaveRuleDialog.vue'
export default {
  components: {
    SaveRuleDialog
  },
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  data() {
    return {
      headers: [
                { text: '规则名称', value: 'short_name' },
                { text: '规则详情', value: 'rule' },
                { text: '类型', value: 'type' },
                { text: '修改时间', value: 'edit_date' }
      ],
      detailHeaders: [
                { text: '规则名称', value: 'short_name' },
                { text: '规则详情', value: 'rule' },
                { text: '类型', value: 'type' }
      ],
      loading: false,
      isDialog: false,
      current: {},
      entityNameList: []
    }
  },
  mounted() {
    this.sync()
  },
  computed: {
    ruleList() {
      return this.$store.state.CampaignStore.ruleList
    },
    entityList() {
      const entityDict = this.$store.state.CampaignStore.entityDict
      return Object.values(entityDict)
    }
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    sync() {
      this.loading = true
      this.$store.dispatch('campaignFetchRuleList', () => { this.loading = false })
    },
    doSave() {
      console.log('doSave', this.current)
      this.loading = true
      const entityDict = this.$store.state.CampaignStore.entityDict
      const entityIdList = this.entityNameList.map(name => {
        const entity = Object.values(entityDict).find(item => item.entity_name === name)
        return entity ? entity.id : null
      }).filter(id => id !== null)
      const payload = this.current
      payload['entity_id_list'] = entityIdList
      api.updateRule(payload).then(() => {
        this.isDialog = false
        this.sync()
      })
    },
    doDelete() {
      console.log('doSave', this.current)
      this.loading = true
      const entityDict = this.$store.state.CampaignStore.entityDict
      const entityIdList = this.entityNameList.map(name => {
        const entity = Object.values(entityDict).find(item => item.entity_name === name)
        return entity ? entity.id : null
      }).filter(id => id !== null)
      const payload = this.current
      payload['entity_id_list'] = entityIdList
      payload['is_deleted'] = true
      api.updateRule(payload).then(() => {
        this.isDialog = false
        this.sync()
      })
    },
    onDetail(item) {
      this.current = item
      this.isDialog = true
    }
  }
}
</script>

<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    width: 100%;
    line-height: 1.42857143;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}

.drawer-title {
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.custom-form-item {
    margin-bottom: 0px;
}
</style>