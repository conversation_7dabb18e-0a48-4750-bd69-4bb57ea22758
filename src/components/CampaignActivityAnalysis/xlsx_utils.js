import { utils, write } from 'xlsx'
function dfs(node, history, data) {
  console.log(node, data)
  if (node.children) {
    // 如果不是叶子节点
    let current = node.data.content
    for (let i = 0; i < node.children.length; i++) {
      if (node.type === 'root' || node.type === 'middle') {
        dfs(node.children[i], [...history], data)
      } else {
        dfs(node.children[i], [...history, current], data)
      }
    }
  } else {
    // 如果是叶子节点
    let current = node.data.content
    data.push([...history, current])
  }
}

function writeTree(root) {
  let data = []

  dfs(root, [], data)
  console.log(data)
  const maxLength = data.reduce((max, arr) => Math.max(max, arr.length), 0)
  data.forEach(arr => {
    if (arr.length < maxLength) {
      const diff = maxLength - arr.length
      // 这里使用 splice 方法将 undefined 插入到子数组的倒数第二个位置，第一个参数为插入位置，第二个参数为删除元素个数，这里为 0，第三个参数为插入的元素，使用 Array 构造函数和 fill 方法生成一个长度为 diff 的 ' ' 数组，然后将其展开插入到子数组中即可。
      arr.splice(arr.length - 1, 0, ...Array(diff).fill(' '))
    }
  })
  const header = Array(maxLength).fill('')
  header[header.length - 1] = '结果'
  const prefix = '前置条件'
  header.slice(0, -1).forEach((_, ind) => {
    header[ind] = `${prefix}${ind + 1}`
  })
  data.unshift(header)

  return data
}

function downloadXLSX(data) {
  let sheetName = 'sheet1'
  let sheet = utils.aoa_to_sheet(data)
  var workbook = {
    SheetNames: [sheetName],
    Sheets: {}
  }
  workbook.Sheets[sheetName] = sheet // 生成excel的配置项
  var wopts = {
    bookType: 'xlsx', // 要生成的文件类型
    bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
    type: 'binary'
  }
  var wbout = write(workbook, wopts)
  var blob = new Blob([s2ab(wbout)], {
    type: 'application/octet-stream'
  }) // 字符串转ArrayBuffer
  function s2ab(s) {
    var buf = new ArrayBuffer(s.length)
    var view = new Uint8Array(buf)
    for (var i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF
    return buf
  }
  return blob
}

function openDownloadDialog(url, saveName) {
  if (typeof url === 'object' && url instanceof Blob) {
    url = URL.createObjectURL(url) // 创建blob地址
  }
  var aLink = document.createElement('a')
  aLink.href = url
  aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
  var event
  if (window.MouseEvent) event = new MouseEvent('click')
  else {
    event = document.createEvent('MouseEvents')
    event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
  }
  aLink.dispatchEvent(event)
}

export default{
  writeTree,
  downloadXLSX,
  openDownloadDialog
}
