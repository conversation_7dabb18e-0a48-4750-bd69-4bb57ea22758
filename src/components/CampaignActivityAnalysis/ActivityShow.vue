<template>
    <div>
        <el-menu default-active="1-4-1" class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose"
            @select="handleSelect" :collapse="true">
            <el-menu-item index="task">
                <i class="el-icon-picture-outline-round"></i>
                <span slot="title">思维导图</span>
            </el-menu-item>
            <el-menu-item index="entity">
                <i class="el-icon-dish"></i>
                <span slot="title">活动实体</span>
            </el-menu-item>
            <el-menu-item index="rule">
                <i class="el-icon-wind-power"></i>
                <span slot="title">规则管理</span>
            </el-menu-item>
            <el-menu-item index="exp">
                <i class="el-icon-ship"></i>
                <span slot="title">经验知识</span>
            </el-menu-item>
        </el-menu>
        <div class="el-content-demo">
            <MindGraph v-if="task" :nodes="task ? task.tree_result : {}" :taskId="taskId" @update="updateTask"
                @create="createTask" :socket="socket"></MindGraph>
            <div v-else class="campaign-main-placeholder">
                <span>你需要选择一张思维导图</span>
                <el-divider></el-divider>
                <span>没有思维导图？<a @click="createTask">点我创建</a></span><br>
                <span>思维导图没有加载？<a @click="syncTask">重新加载</a></span>
            </div>
        </div>
        <el-dialog title="创建思维导图" :visible.sync="createTaskDialogVisible">
            <el-form ref="createTaskForm" :model="createTaskFormV2" label-width="120px">
                <el-form-item label="名称">
                    <el-input v-model="createTaskFormV2.task_name"></el-input>
                </el-form-item>
                <el-form-item label="首要活动">
                    <el-input v-model="createTaskFormV2.activity_name">
                        <el-button slot="append" icon="el-icon-search" @click="searchRow"></el-button>
                    </el-input>
                </el-form-item>
            </el-form>
            <el-form>
                <el-form-item label="活动叠加规则">
                    <el-table :data="createTaskFormV2.rule_items" style="width: 100%">
                        <el-table-column label="规则">
                            <template slot-scope="scope">
                                <pre class="rule-item">{{ scope.row.short_name }}</pre>
                            </template>
                        </el-table-column>
                        <el-table-column label="类型" width="150">
                            <template slot-scope="scope">
                                <pre class="rule-item">{{ scope.row.type }}</pre>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button size="mini" type="text" icon="el-icon-search"
                                    @click="saveRow(scope.$index, createTaskFormV2.rule_items)"></el-button>
                                <el-button size="mini" type="text" icon="el-icon-delete"
                                    @click="deleteRow(scope.$index, createTaskFormV2.rule_items)"></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-button type="text" @click="newRuleDialog">添加活动叠加规则</el-button>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="createTaskDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitCreateTask">创建</el-button>
            </span>
        </el-dialog>
        <!-- <el-dialog title="创建思维导图" :visible.sync="createTaskDialogVisible">
            <el-form ref="createTaskForm" :model="createTaskForm" label-width="120px">
                <el-form-item label="名称">
                    <el-input v-model="createTaskForm.task_name"></el-input>
                </el-form-item>
                <el-form-item label="首要活动">
                    <el-input v-model="createTaskForm.activity_name" suffix-icon="el-icon-search" @suffix-click="searchRules"></el-input>
                    
                </el-form-item>
                <el-form-item label="活动叠加规则">
                    <el-table :data="createTaskForm.activity_stack_rule_map" style="width: 100%">
                        <el-table-column label="活动名" width="180">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.activity_name" placeholder="请选择活动名">
                                    <el-option v-for="item in entityList" :key="item.id" :label="item.entity_name"
                                        :value="item.entity_name"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="叠加规则" width="180">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.stack_rule" placeholder="请输入叠加规则"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button size="mini" type="text" icon="el-icon-search"
                                    @click="searchRow(scope.$index)"></el-button>
                                <el-button size="mini" type="text" icon="el-icon-delete"
                                    @click="deleteRow(scope.$index, createTaskForm.activity_stack_rule_map)"></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-button type="text" @click="addRow">添加活动叠加规则</el-button>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="createTaskDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitCreateTask">创建</el-button>
            </span>
        </el-dialog> -->
        <el-dialog title="继续提问" :visible.sync="continueQuestionDialogVisible">
            <el-form ref="continueQuestionForm" :model="continueQuestionForm" label-width="120px">
                <el-form-item label="活动名列表">
                    <el-select v-model="continueQuestionForm.activityNames" multiple placeholder="请选择活动名">
                        <el-option v-for="item in entityList" :key="item.id" :label="item.entity_name"
                            :value="item.entity_name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规则">
                    <el-input v-model="continueQuestionForm.rule" placeholder="请输入规则"></el-input>
                </el-form-item>
                <el-form-item label="场景">
                    <el-select v-model="continueQuestionForm.type" placeholder="请选择活动名">
                        <el-option label="叠加" value="stacking"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="continueQuestionDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitContinueQuestion">创建</el-button>
            </span>
        </el-dialog>
        <NewRuleDialog :visible.sync="new_rule_dialog" @created="syncEntityList"></NewRuleDialog>
        <SaveRuleDialog :visible.sync="save_rule_dialog" :data="current_rule"></SaveRuleDialog>
        <ExperienceTable :visible.sync="exp_drawer"></ExperienceTable>
        <EntityTable :visible.sync="entity_drawer"></EntityTable>
        <RuleTable :visible.sync="rule_drawer"></RuleTable>
        <GraphTable @create="createTask" @detail="handleDetailGraph" :visible.sync="task_drawer"></GraphTable>
    </div>
</template>

<script>
import io from 'socket.io-client'
import api from './store/api'
import MindGraph from './MindMap.vue'
import ExperienceTable from './ExperienceTable.vue'
import EntityTable from './EntityTable.vue'
import RuleTable from './RuleTable.vue'
import GraphTable from './GraphTable.vue'
import { Loading } from 'element-ui'
import NewRuleDialog from './NewRuleDialog.vue'
import SaveRuleDialog from './SaveRuleDialog.vue'

export default {
  components: {
    MindGraph, ExperienceTable, EntityTable, RuleTable, GraphTable, NewRuleDialog, SaveRuleDialog
  },
  data() {
    return {
      task: null,
      taskId: '145373667271508061950687994169134158114',
      createdTask: null,
      createTaskDialogVisible: false,
      createTaskForm: {
        task_name: 'test',
        activity_name: '团购联合立减',
        activity_stack_rule_map: [{ 'activity_name': '平台立减', 'stack_rule': '​两个活动的优惠无法叠加' }]
      },
      createTaskFormV2: {
        task_name: 'test',
        activity_name: '团购联合立减',
        rules: [],
        rule_items: []
      },
      continueQuestionDialogVisible: false,
      continueQuestionForm: {
        activityNames: [],
        rule: '',
        type: ''
      },
      current_rule: {},
      exp_drawer: false,
      entity_drawer: false,
      task_drawer: false,
      rule_drawer: false,
      new_rule_dialog: false,
      save_rule_dialog: false,
      socket: {}
    }
  },
  computed: {
    entityList() {
      const entityDict = this.$store.state.CampaignStore.entityDict
      return Object.values(entityDict)
    }
  },
  mounted() {
    this.syncEntityList()
    this.socket = io.connect(api.baseUrl)
    this.socket.on('task/create/done', message => {
      console.log('done')
      this.taskId = message.task_id
      this.syncTask()
    })
  },
  methods: {
    sync() {
      this.syncTask()
      this.syncEntityList()
    },
    newRuleDialog() {
      this.new_rule_dialog = true
    },
    saveRow(index, rows) {
      console.log(index, rows[index])
      this.current_rule = rows[index]
      this.save_rule_dialog = true
    },
    searchRow(index) {
            //   api.findRuleByEntites([this.createTaskForm.activity_name,
            //     this.createTaskForm.activity_stack_rule_map[index].activity_name]).then(result => {
            //       console.log(result.data)
            //       this.createTaskForm.activity_stack_rule_map[index].stack_rule = result.data.rule
            //     })
      api.findRuleByEntites([this.createTaskFormV2.activity_name]).then(result => {
        const ruleList = result.data.rule_list
        this.createTaskFormV2.rule_items = ruleList
      })
    },
    syncTask() {
      console.log('syncTask')
      let loadingInstance = Loading.service({})
      api.getTask(this.taskId).then(result => {
        this.task = result.data.task
        this.createTaskDialogVisible = false
        loadingInstance.close()
      }).catch(error => {
        this.$notify({ title: '思维导图加载失败', message: `当前id=${this.taskId}\n` + error, type: 'error' })
        loadingInstance.close()
      })
    },
    updateTask(nodeId) {
      const root = this.task.tree_result
      this.updateNode(root, nodeId).then((node) => {
        this.task.tree_result = node
        this.task = JSON.parse(JSON.stringify(this.task))
      })
    },
    async updateNode(node, nodeId) {
      if (node.id === nodeId) {
        let loadingInstance = Loading.service({})
        try {
          const result = await api.getDeepNodeResult(nodeId, node)
          node = result.data.tree_node
        } catch (error) {
          this.$notify({ title: '思维导图更新失败', message: `当前node_id=${nodeId}\n` + error, type: 'error' })
        } finally {
          loadingInstance.close()
        }
        return node
      }
      if (node.children && node.children.length > 0) {
        for (let i = 0; i < node.children.length; i++) {
          node.children[i] = await this.updateNode(node.children[i], nodeId)
        }
      }
      return node
    },
    syncEntityList() {
      this.$store.dispatch('campaignFetchEntityDict', null)
    },
    submitContinueQuestion() {
      if (this.continueQuestionForm.label === 'root') {
        api.chatByNode({
          task_id: this.taskId,
          node_id: this.continueQuestionForm.node_id,
          entity_list: this.continueQuestionForm.activityNames,
          content: this.continueQuestionForm.rule,
          type: this.continueQuestionForm.type
        }).then(result => {
          this.syncTask()
        })
      }
    },
    append(node, data) {
            // 动态添加节点
      this.syncEntityList()
      this.continueQuestionForm.node_id = data.id
      this.continueQuestionForm.label = data.label
      this.continueQuestionDialogVisible = true
    },
        // submitCreateTask() {
        //     let ruleMap = {}
        //     this.createTaskForm.activity_stack_rule_map.forEach(item => {
        //         ruleMap[item.activity_name] = item.stack_rule
        //     })
        //     let payload = {
        //         activity_name: this.createTaskForm.activity_name,
        //         task_name: this.createTaskForm.task_name,
        //         activity_stack_rule_map: ruleMap
        //     }
        //     console.log('提交创建思维导图表单数据：', payload)

        //     api.createTask(payload).then(result => {
        //         this.createdTask = result.data.task
        //         this.createTaskDialogVisible = false
        //         this.taskId = this.createdTask.id
        //         this.$store.dispatch('campaignFetchTaskList', () => { })
        //         this.syncTask()
        //     })
        // },
    submitCreateTask() {
      this.createTaskFormV2.rules = this.createTaskFormV2.rule_items.map(item => item.id)
      this.socket.emit('message', {
        path: 'task/create',
        data: this.createTaskFormV2,
        header: api.localHeaders
      })
      this.createTaskDialogVisible = false
    },
    createTask() {
      this.syncEntityList()
      this.createTaskDialogVisible = true
    },
    addRow() {
      this.createTaskForm.activity_stack_rule_map.push({
        activity_name: '',
        stack_rule: ''
      })
    },
    deleteRow(index, rows) {
      rows.splice(index, 1)
    },
    handleSelect(e) {
      console.log('select', e)
      if (e === 'exp') {
        this.exp_drawer = true
      } else if (e === 'entity') {
        this.entity_drawer = true
      } else if (e === 'rule') {
        this.rule_drawer = true
      } else if (e === 'task') {
        this.task_drawer = true
      }
    },
    handleOpen(key, keyPath) {
      console.log('open')
      console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      console.log('close')
      console.log(key, keyPath)
    },
    handleDetailGraph(taskId) {
      this.taskId = taskId
      this.syncTask()
    }
  }
}
</script>

<style>
.el-tree-node__content {
    word-wrap: break-word;
    white-space: pre-wrap;
    height: auto;
}

.node-label {
    font-weight: bold;
    color: #333;
    font-size: 14px;
    border: 1px solid #ccc;
    padding: 2px 4px;
    border-radius: 4px;
}

.el-menu-vertical-demo {
    position: fixed;
    float: left;
    width: 60px;
    height: 100%;
}

.el-content-demo {
    margin-left: 65px;
}

.el-drawer__header {
    margin-bottom: 4px;
}

.el-drawer__body {
    -webkit-box-flex: 1;
    flex: 1;
    overflow: auto;
}


.campaign-main-placeholder {
    padding-top: 40vh;
    text-align: center;
    font-weight: bold;
}
.rule-item {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>