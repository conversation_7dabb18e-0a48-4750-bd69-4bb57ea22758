<template>
    <div>
        <el-button @click="exportSVG">导出为 SVG</el-button>
        <el-button @click="exportXLSX">导出为 XLSX</el-button>
        <el-button @click="createNewGraph">新建思维导图</el-button>
        <div class="d3Chart"></div>
        <div ref="tooltip" v-show="isShowTooltip" :style="tooltipStyle" class="box-card">
            <el-card>
                <el-button v-loading="btnLoading" type="primary" icon="el-icon-edit" circle plain size="mini" @click="isEdit = true"></el-button>
                <el-button v-loading="btnLoading" icon="el-icon-plus" circle size="mini" type="warning" plain @click="addNewNode"></el-button>
                <span v-if="currentNode.data ? !['result', 'root', 'middle'].
                            includes(currentNode.data.type) : false" class="button-span">
                <el-button v-loading="btnLoading" type="warning" 
                :icon="`el-icon-star-${currentNode.data ? currentNode.data.is_marked ? 'on' : 'off' : 'off'}`" 
                circle plain size="mini" @click="markNode"></el-button>
                </span>
                <el-button v-loading="btnLoading" type="danger" icon="el-icon-delete" circle plain size="mini" @click="removeNode"></el-button>
            </el-card>
        </div>
        <el-dialog title="编辑节点" :visible.sync="isEdit">
            <el-row>
                <el-col class="col-title" :span="8">节点ID</el-col>
                <el-col class="col-item" :span="16">{{ currentNode.data ? currentNode.data.id : '' }}</el-col>
            </el-row>
            <el-divider></el-divider>
            <el-row>
            <el-col class="col-title" :span="24">内容</el-col>
            <el-input class="col-item" v-if="currentNode.data ? currentNode.data.type === 'result' || currentNode.data.type === 'middle' : false"
                v-model="currentNode.data[contentName]" type="textarea" :rows="10"></el-input>
            <pre v-else class="col-item" :span="24">{{ currentNode.data ? currentNode.data[contentName].trim() : ''
            }}</pre>
            </el-row>
            <el-divider></el-divider>
            <!-- <el-row> -->
                <!-- <el-col class="col-title" v-if="currentNode.data && answerMessageDict[currentNode.data.id]" :span="24">回答</el-col> -->
                <!-- <pre class="col-item" v-if="currentNode.data && answerMessageDict[currentNode.data.id]" >{{ answerMessageDict[currentNode.data.id] }}</pre> -->
            <!-- </el-row> -->
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="isEdit = false">取消</el-button>
                <el-button
                    v-if="currentNode.data ? currentNode.data.type === 'result' || currentNode.data.type === 'middle' : false"
                    type="primary" @click="saveNodeResult" v-loading="isNewResultLoading">保存</el-button>
                <!-- <el-button v-if="currentNode.data ? currentNode.data.type === 'query' : false" type="primary"
                    @click="">重新执行</el-button> -->
            </span>
        </el-dialog>
        <el-dialog title="新增节点" :visible.sync="isNewResult">
            <el-form>
                <el-form-item label="名称">
                    <el-input class="col-item" width="90%" v-model="appendResultNodeInput.short_name"></el-input>
                </el-form-item>
                <el-form-item label="内容">
                    <el-input class="col-item" type="textarea" :rows="10" width="90%" v-model="appendResultNodeInput.content"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isEdit = false">取消</el-button>
                <el-button type="primary" @click="doAddNodeResult" v-loading="isNewResultLoading">添加</el-button>
            </span>
        </el-dialog>
        <el-dialog title="新增节点" :visible.sync="isNew">
            <el-form label-width="120px">
                <el-form-item label="规则名">
                    <el-input class="col-item" width="90%" v-model="addNewNodeInput.ruleShortName"></el-input>
                </el-form-item>
                <el-form-item label="活动名称">
                    <el-select class="col-item" width="90%" v-model="addNewNodeInput.entity_name_list" multiple placeholder="请选择活动名">
                        <el-option v-for="item in entityList" :key="item.id" :label="item.entity_name"
                            :value="item.entity_name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规则类型">
                    <el-select v-model="addNewNodeInput.rule_type" placeholder="请选择活动名" class="col-item">
                        <el-option label="叠加规则" value="stacking"></el-option>
                        <el-option label="佣金规则" value="佣金规则"></el-option>
                        <el-option label="门槛规则" value="门槛规则"></el-option>
                        <el-option label="详细设计" value="详细设计"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规则内容">
                    <el-input type="textarea" :rows="6" v-model="addNewNodeInput.condition" placeholder="请输入规则内容"></el-input>
                </el-form-item>
            </el-form>
            <el-divider></el-divider>
            <div class="col-title">规则内容</div>
            <div class="col-item">{{ addNewNodeInput.query }}</div>
            <el-divider></el-divider>
            <el-col class="col-title" :span="8">回答</el-col>
            <pre class="col-item" v-if="currentNode.data && currentNode.data.id in answerMessageDict" >{{ answerMessageDict[currentNode.data.id] }}</pre>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isNew = false">取消</el-button>
                <el-button type="primary" @click="doAddNodeChat">提问</el-button>
            </span>
        </el-dialog>
    </div>
</template>
  
  
<script>
import * as d3 from 'd3'
import api from './store/api'
import xlsxUtils from './xlsx_utils'

export default {
  props: {
    nodes: Object,
    taskId: String,
    socket: Object
  },
  data() {
    return {
      isShowTooltip: false,
      tooltipStyle: {},
      currentNode: {},
      shortName: 'short_name',
      contentName: 'label',
      mapWidth: 600,
      mapHeight: 600,
      isEdit: false,
      isNew: false,
      entityList: [],
      nodeTemplates: ['next_step', 'first_step', 'last_step'],
      socket: null,
      addNewNodeInput: {
        ruleShortName: '',
        nodeTemplate: 'next_step',
        nodeType: 'node',
        entity_name_list: [],
        prompt: '',
        query: '',
        condition: '',
        rule_type: ''
      },
      appendResultNodeInput: {
        need_embedding: false,
        content: '',
        task_id: '',
        parent_id: '',
        type: '',
        short_name: ''
      },
      isNewResult: false,
      isNewResultLoading: false,
      answerMessageDict: {},
      btnLoading: false
    }
  },
  watch: {
    nodes() {
      console.log(this.nodes)
      if (Object.keys(this.nodes) === 0) {
        return
      }
      this.updateMapSize()
    },
    'addNewNodeInput.condition'(newVal, oldVal) {
      console.log(this.addNewNodeInput.condition)
      if (this.addNewNodeInput.query && this.addNewNodeInput.query.length !== 0) {
        if (this.addNewNodeInput.condition && this.addNewNodeInput.condition.length !== 0) {
          this.addNewNodeInput.query = this.addNewNodeInput.prompt.replace('{condition}', this.addNewNodeInput.condition)
        } else {
          this.addNewNodeInput.query = this.addNewNodeInput.prompt
        }
      }
    }
  },
  mounted() {
    this.socket.on('chat/create', message => {
      console.log('create ' + message)
      let nodeId = message.node_id
      this.answerMessageDict[nodeId] = ''
      this.$emit('update', nodeId)
    })
    this.socket.on('chat/new_token', message => {
      let nodeId = message.node_id
      this.answerMessageDict[nodeId] += message.token
      this.socket.emit('message', {
        path: 'chat/ack',
        data: {
          node_id: nodeId
        },
        header: api.localHeaders
      })
    })
    this.socket.on('chat/end', message => {
      let nodeId = message.node_id
      this.$emit('update', nodeId)
    })
    this.syncEntityList()
    document.addEventListener('click', this.hideTooltip)
    window.addEventListener('resize', this.updateMapSize)
    if (Object.keys(this.nodes) !== 0) {
      this.updateMapSize()
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideTooltip)
    window.removeEventListener('resize', this.updateMapSize)
    this.socket.close()
  },
  methods: {
    removeNode() {
      this.btnLoading = true
      api.removeNode(this.currentNode.data.id).then(result => {
        this.btnLoading = false
        this.$emit('update', this.currentNode.parent.data.id)
      })
    },
    doAddNodeChat() {
      let data = {
        task_id: this.taskId,
        node_id: this.currentNode.data.id,
        condition: this.addNewNodeInput.condition,
        query: this.addNewNodeInput.query,
        entities: this.addNewNodeInput.entity_name_list,
        short_name: this.addNewNodeInput.ruleShortName,
        template: this.addNewNodeInput.nodeTemplate,
        rule_type: this.addNewNodeInput.rule_type
      }
      console.log(data)
      this.socket.emit('message', {
        path: 'chat/node/ask',
        data: data,
        header: api.localHeaders
      })
      this.isNew = false
    },
    saveNodeResult() {
      let currentNode = this.currentNode.data.data
      currentNode.content = this.currentNode.data[this.contentName]
      api.updateNodeResult(currentNode).then(result => {
        this.isNewResultLoading = false
        this.isEdit = false
      }).catch(e => {
        this.isNewResultLoading = false
        this.$notify({title: '修改失败', message: `入参${currentNode}\n` + e, type: 'error'})
      })
    },
    doAddNodeResult() {
      let children = this.currentNode.children[0].data.data
      this.appendResultNodeInput.parent_id = this.currentNode.data.id
      this.appendResultNodeInput.task_id = this.currentNode.data.data.task_id
      this.appendResultNodeInput.type = children.type
      this.isNewResultLoading = true
      api.addNodeResult(this.appendResultNodeInput).then(result => {
        this.isNewResultLoading = false
        this.$emit('update', this.currentNode.data.id)
        this.isNewResult = false
      }).catch(e => {
        this.isNewResultLoading = false
        this.$notify({title: '加入结果失败', message: `入参${this.appendResultNodeInput}\n` + e, type: 'error'})
      })
    },
    markNode() {
      const marked = this.currentNode.data.is_marked
      this.currentNode.data.is_marked = !marked
      this.btnLoading = true
      api.markNode(this.currentNode.data.id, !marked).then(result => {
        this.btnLoading = false
      })
    },
    addNewNode() {
      if (this.currentNode.data.type === 'root') {
        this.addNewNodeInput.nodeTemplate = 'first_step'
        this.isNew = true
        this.updateQuery()
      } else if (this.currentNode.data.type === 'query') {
        let children = this.currentNode.children[0].data
        this.appendResultNodeInput.content = children[this.contentName]
        this.isNewResult = true
      } else {
        this.addNewNodeInput.nodeTemplate = 'next_step'
        this.isNew = true
        this.updateQuery()
      }
    },
    updateQuery() {
      api.getNodePrompt(this.currentNode.data.id, this.addNewNodeInput.nodeType, this.addNewNodeInput.nodeTemplate).then(result => {
        this.addNewNodeInput.query = result.data.prompt
        this.addNewNodeInput.prompt = result.data.prompt
        if (this.addNewNodeInput.condition && this.addNewNodeInput.condition.length !== 0) {
          this.addNewNodeInput.query = this.addNewNodeInput.query.replace('{condition}', this.addNewNodeInput.condition)
        }
      })
    },
    updateMapSize() {
      this.mapWidth = window.innerWidth * 0.6
      this.mapHeight = window.innerHeight * 0.6
      this.render_tree()
    },
    syncEntityList() {
      api.findEntityList().then(result => {
        this.entityList = result.data.entity
      })
    },
    checkHeight(treeData, minHeight) {
      const leafNodes = treeData.leaves()

      for (let i = 0; i < leafNodes.length - 1; i++) {
        const currentNode = leafNodes[i]
        const nextNode = leafNodes[i + 1]
        const heightDifference = Math.abs(currentNode.x - nextNode.x)

        if (currentNode.depth === nextNode.depth && heightDifference <= minHeight) {
          return false
        }
      }
      return true
    },
    modifyWidth(treeData, yMargin, fontsize, mapPadding) {
      let totalWidth = treeData.data[this.shortName].length * fontsize
      treeData.each((node) => {
        if (node.parent) {
          node.y = node.parent.y + node.parent.data[this.shortName].length * fontsize + yMargin
          totalWidth = Math.max(node.y + node.data[this.shortName].length * fontsize + mapPadding * 2, totalWidth)
        }
      })
      return { treeData, totalWidth }
    },
    getTreeData(data, mapHeight, mapWidth, mapPadding) {
      return d3.tree()
                // 设置树状图的尺寸
                .size([mapHeight - 3 * mapPadding, mapWidth - 8 * mapPadding])
                // 设置树状图节点之间的间隔
                .separation((a, b) => {
                  return (a.parent === b.parent ? 1 : 2)
                })(
                    // 创建层级布局，对源数据进行数据转换
                    d3.hierarchy(data).sum((d) => {
                      return d.value
                    })
                )
    },
    render_tree() {
      let mapWidth = 2000
      let mapHeight = this.mapHeight
      let mapPadding = 30
      let btnHeight = 25
      let minHeight = 30
      let yMargin = 100
      let fontsize = 12
            // 源数据
      let data = this.nodes
            // 将源数据转换为可以生成树状图的数据(有节点 nodes 和连线 links )
      let treeData
      for (let i = 0; i < 100; i++) {
        treeData = this.getTreeData(data, mapHeight, mapWidth, mapPadding)
        if (this.checkHeight(treeData, minHeight)) break
        else mapHeight += minHeight
      }
      let modifiedResult = this.modifyWidth(treeData, yMargin, fontsize, mapPadding)
      treeData = modifiedResult.treeData
      mapWidth = modifiedResult.totalWidth + mapPadding * 2

            // 删除已存在的svg元素
      d3.select('.d3Chart').select('svg').remove()
            // 定义画布—— 外边距 10px
      let svgMap = d3.select('.d3Chart')
                    .append('svg')
                    .attr('width', mapWidth)
                    .attr('height', mapHeight)
                    .style('margin', '10px')
                    .style('overflow-x', 'scroll')
                    .style('overflow-y', 'scroll')
            // 定义树状图画布
      let treeMap = svgMap.append('g').attr('transform', 'translate(' + mapPadding + ',' + mapPadding + ')')

            // 贝塞尔曲线生成器
      let BCG = d3.linkHorizontal()
                .x((d) => {
                  return d.y
                })
                .y((d) => {
                  return d.x
                })
            // 绘制边
      treeMap.selectAll('path')
                .data(treeData.links())
                .enter()
                .append('path')
                .attr('d', (d) => {
                    // 根据name值的长度调整连线的起点
                  var start = { x: d.source.x, y: d.source.y + d.source.data[this.shortName].length * fontsize + fontsize * 2 }
                  var end = { x: d.target.x, y: d.target.y }
                  return BCG({ source: start, target: end })
                })
                .attr('fill', 'none')
                .attr('stroke', 'lightblue')
                .attr('stroke-width', 1)
            // 创建分组——节点+文字
      let groups = treeMap.selectAll('g')
                .data(treeData.descendants())
                .enter()
                .append('g')
                .attr('transform', (d) => {
                  var cx = d.x
                  var cy = d.y
                  return 'translate(' + cy + ',' + cx + ')'
                })
      groups.append('foreignObject')
                .attr('width', d => d.data[this.shortName].length * fontsize + fontsize * 3) // 根据节点名称长度动态设置宽度
                .attr('height', d => btnHeight) // 根据节点名称长度动态设置高度
                //   .attr('x', 8)
                .attr('y', d => -btnHeight / 2) // 根据高度调整y值
                .html((d) => {
                  return `<button class="el-button el-button--mini" style="white-space: normal; height: 100%;">${d.data[this.shortName]}</button>`
                })
                .on('click', (event, d) => {
                  this.handleButtonClick(d)
                  this.$emit('node-click', d.data)
                }).on('contextmenu', (event, d) => {
                  event.preventDefault() // 阻止默认的右键菜单
                    // 在这里处理右键点击事件
                  console.log('右键被点击:', d)
                  this.showTooltip(event, d)
                })
    },
    showTooltip(event, d) {
      this.tooltipData = d.data
      this.currentNode = d
      this.tooltipStyle = {
        position: 'absolute',
        left: (event.pageX + 10) + 'px',
        top: (event.pageY + 10) + 'px'
      }
      this.isShowTooltip = true
    },
    hideTooltip(e) {
      if (!this.$refs.tooltip.contains(e.target)) this.isShowTooltip = false
    },
    handleButtonClick(data) {
      this.currentNode = data
      this.isEdit = true
      console.log('按钮被点击:', data)
    },
    exportSVG() {
      const svgElement = d3.select('.d3Chart').select('svg').node()
      const serializer = new XMLSerializer()
      let source = serializer.serializeToString(svgElement)

      source = '<?xml version="1.0" standalone="no"?>\r\n' + source
      const svgBlob = new Blob([source], { type: 'image/svg+xml;charset=utf-8' })
      const svgUrl = URL.createObjectURL(svgBlob)

      const downloadLink = document.createElement('a')
      downloadLink.href = svgUrl
      downloadLink.download = 'tree_map.svg'
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
    },
    exportXLSX() {
      let data = xlsxUtils.writeTree(this.nodes)
      xlsxUtils.openDownloadDialog(xlsxUtils.downloadXLSX(data), 'data.xlsx')
    },
    createNewGraph() {
      this.$emit('create')
    }
  }
}
</script>
<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas,Menlo,Courier,monospace;
    font-size: 1em;
    line-height: 1.42857143;
    width: 100%;
}

.el-button+.button-span{
    margin-left: 10px;
}

.button-span+.el-button{
    margin-left: 10px;
}

.d3Chart{
    overflow: scroll;
}

</style>