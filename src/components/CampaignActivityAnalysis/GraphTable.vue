<template>
    <div>
        <el-drawer :visible.sync="visible" :modal="false">
            <div slot="title">
                <span class="drawer-title">思维导图</span>
                <el-tooltip class="operation-button" effect="dark" content="新增思维导图" placement="top">
                    <el-button icon="el-icon-plus" plain circle size="mini" @click="onCreate"></el-button>
                </el-tooltip>
                <el-tooltip class="operation-button" effect="dark" content="刷新" placement="top">
                    <el-button icon="el-icon-refresh" plain circle size="mini" @click="sync"></el-button>
                </el-tooltip>
            </div>
            <el-table v-loading="loading" :data="taskList" style="width: 100%">
                <el-table-column v-for="header in headers" :key="header.value" :label="header.text" :prop="header.value"
                    sortable>
                    <template slot-scope="scope">
                        {{ formatText(scope.row[header.value]) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" content="详情" placement="top">
                            <el-button size="mini" icon="el-icon-view" circle type="primary"
                                @click="onDetail(scope.row)"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
    </div>
</template>
    
<script>
import api from './store/api'
export default {
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  data() {
    return {
      headers: [
            { text: '名称', value: 'task_name' },
            { text: '根节点', value: 'root_name' },
            { text: '创建人', value: 'user_name' },
            { text: '更新时间', value: 'edit_date' }
      ],
      loading: false,
      entityName: ''
    }
  },
  mounted() {
    this.sync()
  },
  computed: {
    taskList() {
      return this.$store.state.CampaignStore.taskList
    }
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    sync() {
      this.loading = true
      this.$store.dispatch('campaignFetchTaskList', () => { this.loading = false })
    },
    doAdd() {
      console.log('doAdd', this.entityName)
      api.addEntity(this.entityName).then(result => {
        const id = result.data.id
        this.sync()
        this.$notify({title: '新增活动成功', message: id, type: 'success'})
      }).catch(error => {
        this.$notify({title: '新增活动失败', message: error, type: 'error'})
      })
    },
    onCreate() {
      this.$emit('create')
    },
    onDetail(taskItem) {
      this.$emit('detail', taskItem.id)
    }
  }
}
</script>
    
<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    line-height: 1.42857143;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}

.drawer-title{
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.custom-form-item{
    margin-bottom: 0px;
}
</style>