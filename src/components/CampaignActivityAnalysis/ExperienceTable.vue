<template>
    <div>
        <el-drawer :visible.sync="visible" :modal="false">
            <div slot="title">
                <span class="drawer-title">经验知识</span>
                <el-tooltip class="operation-button" effect="dark" content="刷新" placement="top">
                    <el-button icon="el-icon-refresh" plain circle size="mini" @click="syncExp"></el-button>
                </el-tooltip>
                <el-tooltip class="operation-button" effect="dark" content="测试" placement="top">
                    <el-button icon="el-icon-search" plain circle size="mini" @click="retriveDialogVisible=true"></el-button>
                </el-tooltip>
            </div>
            <el-table v-loading="loading" :data="experiences" style="width: 100%">
                <el-table-column v-for="header in tableHeaders" :key="header.value" :label="header.text"
                    :prop="header.value" sortable>
                    <template slot-scope="scope">
                        {{ formatText(scope.row[header.value]) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" content="详情" placement="top">
                            <el-button size="mini" icon="el-icon-setting" circle type="primary"
                                @click="onExpDetail(scope.row)"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
        <el-dialog title="查看具体经验知识" :visible.sync="isDialog">
            <div v-for="header in formHeaders">
                <div>
                    <div class="col-title" :span="8">{{ header.text }}</div>
                    <pre v-if="header.type === 'pre'" class="col-item">{{ currentExp[header.value] }}</pre>
                    <el-input v-else-if="header.type === 'input'" class="col-item" type="textarea"
                        v-model="formData[header.value]"></el-input>
                    <el-select v-else-if="header.type === 'select'" 
                        v-model="formData[header.value]" placeholder="请选择活动名" class="col-item">
                        <el-option v-for="item in header.select_items" :key="item.label" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </div>
                <el-divider></el-divider>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="doUpdate" type="primary">保存</el-button>
                <el-button @click="isDialog = false">取消</el-button>
                <el-button type="danger" @click="doDelete">删除</el-button>
            </span>
        </el-dialog>
        <ExperienceRetrive :visible.sync="retriveDialogVisible"></ExperienceRetrive>
    </div>
</template>

<script>
import api from './store/api'
import ExperienceRetrive from './ExperienceRetrive.vue'
export default {
  components: {
    ExperienceRetrive
  },
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  data() {
    return {
      tableHeaders: [
        { text: '名称', value: 'short_name' },
        { text: '规则类型', value: 'rule_type' },
        { text: '提问', value: 'content' },
        { text: '回答', value: 'answer_message' }
      ],
      formHeaders: [
        { text: '名称', value: 'short_name', type: 'pre' },
        {
          text: '规则类型',
          value: 'rule_type',
          type: 'select',
          select_items: [
            { label: '叠加规则', value: 'stacking' },
            { label: '佣金规则', value: '佣金规则' },
            { label: '门槛规则', value: '门槛规则' },
            { label: '详细设计', value: '详细设计' }
          ]
        },
        { text: '提问', value: 'content', type: 'input' },
        { text: '回答', value: 'answer_message', type: 'pre' },
        { text: '修改时间', value: 'edit_date', type: 'pre' }
      ],
      formData: {
        content: '',
        rule_type: ''
      },
      loading: false,
      currentExp: {},
      isDialog: false,
      retriveDialogVisible: false
    }
  },
  computed: {
    experiences() {
      return this.$store.state.CampaignStore.expList
    }
  },
  mounted() {
    this.syncExp()
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    syncExp() {
      console.log('syncExp')
      this.loading = true
      this.$store.dispatch('campaignFetchExpList', () => { this.loading = false })
    },
    onExpDetail(item) {
      this.currentExp = item
      this.formData.content = item.content
      this.formData.rule_type = item.rule_type
      this.isDialog = true
    },
    doUpdate() {
      this.currentExp.rule_type = this.formData.rule_type
      this.currentExp.content = this.formData.content
      console.log('updateQueryNode', this.currentExp)
      this.isDialog = false
      api.updateQueryNode(this.currentExp).then(result => {
                // this.syncExp()
      }).catch(e => {
        this.$notify({ title: '更新失败', message: `入参${this.currentExp}\n` + e, type: 'error' })
      })
    },
    doDelete() {
      console.log('doDelete', this.currentExp.id, false)
      this.isDialog = false
      api.markNode(this.currentExp.id, false).then(result => {
                // this.syncExp()
      }).catch(e => {
        this.$notify({ title: '删除失败', message: `入参${this.currentExp}\n` + e, type: 'error' })
      })
    }
  }
}
</script>

<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    width: 100%;
    line-height: 1.42857143;
}

.drawer-title {
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}
</style>