import api from './api'

export default {
  state: {
    entityDict: {},
    taskList: [],
    ruleList: [],
    expList: []
  },
  mutations: {
    setEntityDict(state, entityDict) {
      state.entityDict = entityDict
    },
    setTaskList(state, taskList) {
      state.taskList = taskList
    },
    setRuleList(state, ruleList) {
      state.ruleList = ruleList
    },
    setExpList(state, expList) {
      state.expList = expList
    }
  },
  actions: {
    campaignFetchTaskList({ commit }, callback) {
      api.findAllTask().then(result => {
        commit('setTaskList', result.data.task_list)
        if (callback instanceof Function) {
          callback()
        }
      }).catch(error => {
        this.$notify({title: '思维导图列表同步失败', message: error, type: 'error'})
      })
    },
    campaignFetchRuleList({ commit }, callback) {
      api.findAllRule().then(result => {
        commit('setRuleList', result.data.rule_list)
        if (callback instanceof Function) {
          callback()
        }
      }).catch(error => {
        this.$notify({title: '思维导图列表同步失败', message: error, type: 'error'})
      })
    },
    campaignFetchExpList({ commit }, callback) {
      api.getExp().then(result => {
        commit('setExpList', result.data.experiences)
        if (callback instanceof Function) {
          callback()
        }
      }).catch(error => {
        this.$notify({title: '经验知识同步失败', message: error, type: 'error'})
      })
    },
    campaignFetchEntityDict({ commit }, callback) {
      api.findEntityList().then(result => {
        commit('setEntityDict', result.data.entity)
        if (callback instanceof Function) {
          callback()
        }
      }).catch(error => {
        this.$notify({title: '活动列表同步失败', message: error, type: 'error'})
      })
    }
  }
}
