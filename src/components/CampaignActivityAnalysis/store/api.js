import axios from 'axios'
import urlConf from '../../../assets/user_env'

import { Bus } from '@/global/bus'

let localHeaders = {
  userInfo: JSON.stringify({
    userId: '',
    userName: 'Dev',
    userLogin: 'Dev',
    userUrl: ''
  })
}

Bus.$on('refreshUserInfo', (userInfo) => {
  localHeaders.userInfo = encodeURI(JSON.stringify(userInfo))
})

let baseUrl = urlConf.campaign_base_url

const getTask = (taskId) => {
  return axios({
    url: baseUrl + '/task/result?task_id=' + taskId,
    method: 'GET',
    headers: localHeaders
  })
}

const createTask = (payload) => {
  return axios({
    url: baseUrl + '/task/start',
    method: 'POST',
    data: payload,
    headers: localHeaders
  })
}

const findEntityList = () => {
  return axios({
    url: baseUrl + '/entity/get',
    method: 'GET',
    headers: localHeaders
  })
}

const chatByNode = (payload) => {
  return axios({
    url: baseUrl + '/task/ask/by_node',
    method: 'POST',
    data: payload,
    headers: localHeaders
  })
}

const getNodePrompt = (nodeId, type, template) => {
  return axios({
    url: baseUrl + `/chat/node/prompt?node_id=${nodeId}&type=${type}&template=${template}`,
    method: 'GET',
    headers: localHeaders
  })
}

const findRuleByEntites = (entityList) => {
  return axios({
    url: baseUrl + '/rule/find_by_entities',
    method: 'POST',
    data: {
      entities: entityList
    },
    headers: localHeaders
  })
}

const removeNode = (nodeId) => {
  return axios({
    url: baseUrl + '/node/remove?node_id=' + nodeId,
    method: 'GET',
    headers: localHeaders
  })
}

const markNode = (nodeId, isMarked) => {
  return axios({
    url: baseUrl + '/node/mark?node_id=' + nodeId + '&is_marked=' + isMarked,
    method: 'GET',
    headers: localHeaders
  })
}

const getExp = () => {
  return axios({
    url: baseUrl + '/node/experiences',
    method: 'GET',
    headers: localHeaders
  })
}

const addEntity = (entityName) => {
  return axios({
    url: baseUrl + '/entity/create',
    method: 'POST',
    data: {entity_name: entityName},
    headers: localHeaders
  })
}

const findAllTask = () => {
  return axios({
    url: baseUrl + '/task/findall',
    method: 'GET',
    headers: localHeaders
  })
}

const findAllRule = () => {
  return axios({
    url: baseUrl + '/rule/findall',
    method: 'GET',
    headers: localHeaders
  })
}

const getDeepNodeResult = (nodeId) => {
  return axios({
    url: baseUrl + '/node/get_deep_node?node_id=' + nodeId,
    method: 'GET',
    headers: localHeaders
  })
}

const addNodeResult = (payload) => {
  return axios({
    url: baseUrl + '/node/add_answer_node',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const updateNodeResult = (payload) => {
  return axios({
    url: baseUrl + '/node/update_answer_node',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const updateQueryNode = (payload) => {
  return axios({
    url: baseUrl + '/node/update_answer_node',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const updateRule = (payload) => {
  return axios({
    url: baseUrl + '/node/update_rule',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const createRule = (payload) => {
  return axios({
    url: baseUrl + '/node/create_rule',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const retriveExperience = (payload) => {
  return axios({
    url: baseUrl + '/embedding/retrive',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

export default {
  getTask,
  createTask,
  findEntityList,
  chatByNode,
  getNodePrompt,
  findRuleByEntites,
  baseUrl,
  removeNode,
  markNode,
  getExp,
  addEntity,
  findAllTask,
  findAllRule,
  getDeepNodeResult,
  updateNodeResult,
  addNodeResult,
  updateRule,
  localHeaders,
  createRule,
  updateQueryNode,
  retriveExperience
}
