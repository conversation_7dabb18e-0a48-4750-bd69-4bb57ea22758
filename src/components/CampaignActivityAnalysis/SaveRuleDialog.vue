<template>
    <div>
        <el-dialog title="查看具体规则信息" :visible.sync="visible">
            <div v-for="header in detailHeaders">
                <div>
                    <div class="col-title" :span="8">{{ header.text }}</div>
                    <el-input v-model="current[header.value]" type="textarea" :rows="3" placeholder="请输入"
                        clearable></el-input>
                </div>
                <el-divider></el-divider>
            </div>
            <div>
                <el-select v-model="current.type" placeholder="请选择活动名" class="col-item">
                    <el-option label="叠加规则" value="stacking"></el-option>
                    <el-option label="佣金规则" value="佣金规则"></el-option>
                    <el-option label="门槛规则" value="门槛规则"></el-option>
                    <el-option label="详细设计" value="详细设计"></el-option>
                </el-select>
                <el-divider></el-divider>
            </div>
            <div>
                <div class="col-title" :span="8">相关活动</div>
                <el-select class="col-item" v-model="entityNameList" multiple placeholder="请选择活动名">
                    <el-option v-for="item in entityList" :key="item.id" :label="item.entity_name"
                        :value="item.entity_name"></el-option>
                </el-select>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" v-loading="loading" @click="doSave">保存</el-button>
                <el-button type="danger" v-loading="loading" @click="doDelete">删除</el-button>
                <el-button @click="visible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
    
<script>
import api from './store/api'
export default {
  props: {
    visible: Boolean,
    data: Object
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
      this.sync()
    }
  },
  data() {
    return {
      detailHeaders: [
                { text: '规则名称', value: 'short_name' },
                { text: '规则详情', value: 'rule' }
      ],
      loading: false,
      current: {
        'short_name': '',
        'rule': '',
        'type': '',
        'entity_id_list': []
      },
      entityNameList: []
    }
  },
  mounted() {
    this.sync()
  },
  computed: {
    ruleList() {
      return this.$store.state.CampaignStore.ruleList
    },
    entityList() {
      const entityDict = this.$store.state.CampaignStore.entityDict
      return Object.values(entityDict)
    }
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    sync() {
      console.log(this.data)
      this.entityNameList = []
      this.current = this.data
      this.loading = true
      this.$store.dispatch('campaignFetchRuleList', () => { this.loading = false })
      const entityIdList = this.current['entity_id_list']
      const entityDict = this.$store.state.CampaignStore.entityDict
      this.entityNameList = entityIdList.map(id => entityDict[id].entity_name)
      console.log(entityDict, this.entityNameList)
    },
    doSave() {
      console.log('doSave', this.current)
      this.loading = true
      const entityDict = this.$store.state.CampaignStore.entityDict
      const entityIdList = this.entityNameList.map(name => {
        const entity = Object.values(entityDict).find(item => item.entity_name === name)
        return entity ? entity.id : null
      }).filter(id => id !== null)
      const payload = this.current
      payload['entity_id_list'] = entityIdList
      api.updateRule(payload).then(() => {
        this.visible = false
        this.sync()
      })
    },
    doDelete() {
      console.log('doSave', this.current)
      this.loading = true
      const entityDict = this.$store.state.CampaignStore.entityDict
      const entityIdList = this.entityNameList.map(name => {
        const entity = Object.values(entityDict).find(item => item.entity_name === name)
        return entity ? entity.id : null
      }).filter(id => id !== null)
      const payload = this.current
      payload['entity_id_list'] = entityIdList
      payload['is_deleted'] = true
      api.updateRule(payload).then(() => {
        this.visible = false
        this.sync()
      })
    }
  }
}
</script>

<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    width: 100%;
    line-height: 1.42857143;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}

.drawer-title {
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.custom-form-item {
    margin-bottom: 0px;
}
</style>