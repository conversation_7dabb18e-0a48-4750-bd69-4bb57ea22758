<template>
    <div>
        <el-drawer :visible.sync="visible" :modal="false">
            <div slot="title">
                <span class="drawer-title">活动实体</span>
                <el-tooltip class="operation-button" effect="dark" content="新增实体" placement="top">
                    <el-popover>
                        <el-button slot="reference" icon="el-icon-plus" plain circle size="mini"></el-button>
                        <el-form>
                            <el-form-item class="custom-form-item" label="活动名称">
                                <el-input v-model="entityName" placeholder="请输入活动名称"></el-input>
                            </el-form-item>
                            <el-form-item class="custom-form-item">
                                <el-button size="mini" type="primary" @click="doAdd">新增</el-button>
                            </el-form-item>
                        </el-form>
                    </el-popover>
                </el-tooltip>
                <el-tooltip class="operation-button" effect="dark" content="刷新" placement="top">
                    <el-button icon="el-icon-refresh" plain circle size="mini" @click="sync"></el-button>
                </el-tooltip>
            </div>
            <el-table v-loading="loading" :data="entityList" style="width: 100%">
                <el-table-column v-for="header in headers" :key="header.value" :label="header.text" :prop="header.value"
                    sortable>
                    <template slot-scope="scope">
                        {{ formatText(scope.row[header.value]) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" content="详情" placement="top">
                            <el-button size="mini" icon="el-icon-setting" circle type="primary"
                                @click="onExpDetail(scope.row)"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column> -->
            </el-table>
        </el-drawer>
    </div>
</template>
    
<script>
import api from './store/api'
export default {
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  computed: {
    entityList() {
      const entityDict = this.$store.state.CampaignStore.entityDict
      return Object.values(entityDict)
    }
  },
  data() {
    return {
      headers: [
                { text: '活动实体名称', value: 'entity_name' },
                { text: '修改时间', value: 'edit_date' }
      ],
      loading: false,
      entityName: ''
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    sync() {
      this.loading = true
      this.$store.dispatch('campaignFetchEntityDict', () => { this.loading = false })
    },
    doAdd() {
      console.log('doAdd', this.entityName)
      api.addEntity(this.entityName).then(result => {
        const id = result.data.id
        this.sync()
        this.$emit('update')
        this.$notify({title: '新增活动成功', message: id, type: 'success'})
      }).catch(error => {
        this.$notify({title: '新增活动失败', message: error, type: 'error'})
      })
    }
  }
}
</script>
    
<style scoped>
.col-title {
    white-space: pre-wrap;
    align-items: center;
    margin-bottom: 10px;
    font-size: larger;
}

.col-item {
    white-space: pre-wrap;
    align-items: center;
    font-family: Consolas, Menlo, Courier, monospace;
    font-size: 1em;
    line-height: 1.42857143;
}

.operation-button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
}

.drawer-title{
    float: left;
    margin: 10px;
    font-weight: bold;
    font-size: 20px;
}

.custom-form-item{
    margin-bottom: 0px;
}
</style>