<template>
    <div>
        <el-dialog title="创建规则" :visible.sync="visible">
            <el-form ref="form" :model="form" label-width="120px">
                <el-form-item label="规则名称">
                    <el-input v-model="form.short_name" type="textarea" placeholder="请输入规则" class="form-item"></el-input>
                </el-form-item>
                <el-form-item label="活动名列表">
                    <el-select v-model="form.entities" multiple placeholder="请选择活动名" class="form-item">
                        <el-option v-for="item in entityList" :key="item.id" :label="item.entity_name"
                            :value="item.entity_name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="规则">
                    <el-input v-model="form.rule" type="textarea" placeholder="请输入规则" class="form-item"></el-input>
                </el-form-item>
                <el-form-item label="类型">
                    <el-select v-model="form.type" placeholder="请选择活动名" class="form-item">
                        <el-option label="叠加规则" value="stacking"></el-option>
                        <el-option label="佣金规则" value="佣金规则"></el-option>
                        <el-option label="门槛规则" value="门槛规则"></el-option>
                        <el-option label="详细设计" value="详细设计"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button v-loading="loading" type="primary" @click="submit">创建</el-button>
                <el-button v-loading="loading" @click="visible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
    
<script>
import api from './store/api'
export default {
  props: {
    visible: Boolean
  },
  watch: {
    visible() {
      this.$emit('update:visible', this.visible)
    }
  },
  data() {
    return {
      form: {
        rule: '',
        entities: [],
        short_name: '',
        type: ''
      },
      loading: false
    }
  },
  computed: {
    entityList() {
      const entityDict = this.$store.state.CampaignStore.entityDict
      return Object.values(entityDict)
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    formatText(text) {
      const reg = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (reg.test(text)) {
        return text.slice(0, 10) + ' ' + text.slice(11, 19)
      }
      if (text && text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    sync() {
      this.loading = true
      this.$store.dispatch('campaignFetchEntityDict', () => { this.loading = false })
    },
    submit() {
      api.createRule(this.form).then(
                this.$emit('created')
            )
    }
  }
}
</script>

<style scoped>
.form-item{
    width: 100%;
}
</style>