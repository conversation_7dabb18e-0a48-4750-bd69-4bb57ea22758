<template>
  <div >
    <i-form :model="deleteModel" inline>
      <form-item>
        <i-input v-model.trim="deleteModel.partnerId" placeholder="请填写供应商ID">
          <span slot="prepend">供应商ID</span>
        </i-input>
      </form-item>
      <form-item>
        <i-input v-model.trim="deleteModel.poiId" placeholder="请填写POI ID">
          <span slot="prepend">POI ID</span>
        </i-input>
      </form-item>
      <form-item>
        <i-input v-model.trim="deleteModel.goodsId" placeholder="请填写产品ID">
          <span slot="prepend">产品ID</span>
        </i-input>
      </form-item>
      <form-item>
        <i-button type="warning" @click="del">销毁</i-button>
      </form-item>
    </i-form>
  </div >
</template>

<script>
    export default {
      name: 'goodsDataDestroy',
      data: function () {
        return {
          page: 'delete',
          deleteModel: {
            partnerId: '4047691',
            poiId: '141015101',
            goodsId: ''
          }
        }
      },
      methods: {
        del: function () {
          let self = this
          if (this.deleteModel.partnerId && this.deleteModel.poiId && this.deleteModel.goodsId) {
            self.$Spin.show()
            $.ajax({
              url: this.getDomain('goodsData') + '/delete',
              type: 'POST',
              timeout: 50000,
              data: {
                'partnerId': this.deleteModel.partnerId,
                'poiId': this.deleteModel.poiId,
                'goodsId': this.deleteModel.goodsId
              },
              success: function (message) {
                self.$Spin.hide()
                if (message['status'] === '1') {
                  console.log('message', message)
                  self.$Message.success('删除成功！')
                  self.$Modal.success({
                    title: '删除成功'
                  })
                } else if (message['status'] === '0') {
                  console.log('message', message)
                  self.$Message.success('删除执行失败！')
                  self.$Modal.success({
                    title: '删除执行失败'
                  })
                } else {
                  self.$Message.error('未知错误！')
                }
              },
              error: function (message) {
                self.$Spin.hide()
                self.$Message.error('删除执行失败！')
                self.$Modal.error({
                  title: '删除执行失败！',
                  content: '<P>错误原因：' + message['message'] + '</P>'
                })
              }
            })
          } else {
            self.$Message.info('存在必填参数未填写！')
          }
        }
      }
    }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 20px;
    padding-right: 20px;
    font-weight: bold;
  }
</style>
