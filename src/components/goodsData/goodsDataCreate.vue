<template>
  <div>
    <Card :bordered="true" id="notice-card">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> MTA平台-默认产品，所属POI:小豆豆宾馆（北京站）（id=141015101）</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 同舟平台-默认产品，所属POI:小豆豆宾馆（望京店）（id=141013951），所属房型：哈士奇（roomId:4940405）</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找 <span class="ciTip">李真（lizhen18）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/71639463" target="_blank">数据生成FAQ</a></span></p>
    </Card>
    <i-form :model="createModel" :label-width="100">
      <form-item label="产品平台">
        <i-select placeholder="请选择产品平台" v-model="createModel.platform">
          <i-option value="1">MTA</i-option>
          <i-option value="2">同舟</i-option>
          <i-option value="3">海川</i-option>
        </i-select>
      </form-item>
      <form-item label="产品类型">
        <i-select placeholder="请选择产品类型" v-model="createModel.goodsType">
          <i-option value="1">预付全日房</i-option>
          <template v-if="createModel.platform == 1">
            <i-option value="2">预付小时房</i-option>
            <i-option value="3">现付产品</i-option>
            <i-option value="4">活动-打包产品</i-option>
            <i-option value="5">活动-限时特惠</i-option>
            <i-option value="6">活动-团队房（3人团）</i-option>
            <i-option value="7">活动-预付报销</i-option>
          </template>
        </i-select>
      </form-item>
      <form-item label="定制类型">
        <radio-group v-model="createModel.ruleType">
          <radio :label="'1'">
            <span>默认产品</span>
          </radio>
          <radio :label="'2'" v-if="createModel.platform !=2 && createModel.platform !=3 && createModel.goodsType != 3  && createModel.goodsType != 5 && createModel.goodsType != 6 && createModel.goodsType != 7">
            <span>按VPOI定制</span>
          </radio>
          <radio :label="'3'" v-if="createModel.platform !=2 && createModel.platform !=3 && createModel.goodsType != 3 && createModel.goodsType != 2 && createModel.goodsType != 4 && createModel.goodsType != 5 && createModel.goodsType != 6 && createModel.goodsType != 7">
            <span>按销售规则定制</span>
          </radio>
        </radio-group>
      </form-item>
      <template v-if="createModel.ruleType == 2">
        <form-item label="供应商ID">
          <i-input v-model.trim="createModel.partnerId" placeholder="请填写供应商ID"></i-input>
        </form-item>
        <form-item label="POI ID">
          <i-input v-model.trim="createModel.poiId" placeholder="请填写POI ID"></i-input>
        </form-item>
        <form-item label="合同ID">
          <i-input v-model.trim="createModel.contractId" placeholder="请填写合同ID"></i-input>
        </form-item>
        <form-item label="Room ID">
          <i-input v-model.trim="createModel.roomId" placeholder="请填写Room ID"></i-input>
        </form-item>
        <form-item label="Room Name">
          <i-input v-model.trim="createModel.roomName" placeholder="请填写Room Name"></i-input>
        </form-item>
      </template>
      <template v-if="createModel.ruleType == 3">
        <form-item>
          <i-input type="textarea" v-model.trim="createModel.saleRules"></i-input>
        </form-item>
      </template>
      <form-item label="是否同步缓存">
        <radio-group v-model="createModel.rsOption">
          <radio :label='"false"'>否</radio>
          <radio :label='"true"'>是</radio>
        </radio-group>
      </form-item>
      <form-item>
        <i-button type="primary" @click="submit">创建</i-button>
      </form-item>
    </i-form>
  </div >
</template>

<script>
  import axios from 'axios'

  const Platform = {}
  Platform.MTA = 1
  Platform.AGENT = 2
  Platform.OVERSEA = 3

  const GoodsType = {}

  GoodsType.PREPAY = 1
  GoodsType.PANTMENT = 3
  GoodsType.PACKING = 4
  GoodsType.HOUR = 2
  GoodsType.Limit = 5
  GoodsType.GROUP = 6
  GoodsType.BAOXIAO = 7

  const RuleType = {}
  RuleType.DEFAULT = 1
  RuleType.VPOI = 2
  RuleType.CUSTOM = 3

  const Url = {}
  Url[Platform.MTA] = {}
  Url[Platform.MTA][GoodsType.PREPAY] = {}
  Url[Platform.MTA][GoodsType.PREPAY][RuleType.DEFAULT] = '/create/prepay/simple'
  Url[Platform.MTA][GoodsType.PREPAY][RuleType.VPOI] = '/create/prepay/vpoi'
  Url[Platform.MTA][GoodsType.PREPAY][RuleType.CUSTOM] = '/create/prepay/rp'

  Url[Platform.MTA][GoodsType.PANTMENT] = {}
  Url[Platform.MTA][GoodsType.PANTMENT][RuleType.DEFAULT] = '/create/payment/simple'

  Url[Platform.MTA][GoodsType.PACKING] = {}
  Url[Platform.MTA][GoodsType.PACKING][RuleType.DEFAULT] = '/create/packing/simple'
  Url[Platform.MTA][GoodsType.PACKING][RuleType.VPOI] = '/create/packing/vpoi'

  Url[Platform.MTA][GoodsType.HOUR] = {}
  Url[Platform.MTA][GoodsType.HOUR][RuleType.DEFAULT] = '/create/hourgoods/simple'
  Url[Platform.MTA][GoodsType.HOUR][RuleType.VPOI] = '/create/hourgoods/vpoi'

  Url[Platform.MTA][GoodsType.Limit] = {}
  Url[Platform.MTA][GoodsType.Limit][RuleType.DEFAULT] = '/create/limit-hour-sale/simple'

  Url[Platform.MTA][GoodsType.GROUP] = {}
  Url[Platform.MTA][GoodsType.GROUP][RuleType.DEFAULT] = '/create/team-house/simple'

  Url[Platform.MTA][GoodsType.BAOXIAO] = {}
  Url[Platform.MTA][GoodsType.BAOXIAO][RuleType.DEFAULT] = '/create/baoxiao/simple'

  Url[Platform.AGENT] = {}
  Url[Platform.AGENT][GoodsType.PREPAY] = {}
  Url[Platform.AGENT][GoodsType.PREPAY][RuleType.DEFAULT] = '/create/agentgoods/simple'

  Url[Platform.OVERSEA] = {}
  Url[Platform.OVERSEA][GoodsType.PREPAY] = {}
  Url[Platform.OVERSEA][GoodsType.PREPAY][RuleType.DEFAULT] = '/create/oversea/simple'

  function getParams (data) {
    var params = {}
    if (parseInt(data.createModel.ruleType) === RuleType.DEFAULT) {
      params.rsOption = data.createModel.rsOption
    } else if (parseInt(data.createModel.ruleType) === RuleType.VPOI) {
      params.rsOption = data.createModel.rsOption
      params.partnerId = data.createModel.partnerId
      params.poiId = data.createModel.poiId
      params.roomId = data.createModel.roomId
      params.roomName = data.createModel.roomName
      params.contractId = data.createModel.contractId
    } else if (parseInt(data.createModel.ruleType) === RuleType.CUSTOM) {
      params.rsOption = data.createModel.rsOption
      params.rp = data.createModel.saleRules
    }
    // let qs = require('qs');
    // params =qs.stringify(getParams())
    return params
  }
  export default {
    name: 'goodsDataCreate',
    data () {
      return {
        page: 'create',
        createModel: {
          platform: '',
          goodsType: '',
          ruleType: 1,
          partnerId: '',
          poiId: '',
          goodsId: '',
          contractId: '',
          roomId: '',
          roomName: '',
          saleRules: '',
          rsOption: 'false'
        }
      }
    },
    computed: {
      goodsTypeList () {
        const base = [
          { id: 1, name: '预付全日房产品' },
          { id: 2, name: '现付产品' },
          { id: 3, name: '活动-打包产品' },
          { id: 4, name: '预付小时房产品' },
          { id: 5, name: '活动-限时特惠' },
          { id: 6, name: '活动-团队房' },
          { id: 7, name: '预付包销' }
        ]
        return ['', 1].includes(this.createModel.platform) ? base : base.splice(0, 1)
      },
      ruleTypeDisabled () {
        const disabled = [3, 4].includes(this.createModel.goodsType)
        if (disabled) this.createModel.ruleType = 1
        return disabled
      }
    },
    watch: {
      'createModel.platform' () {
        this.createModel.goodsType = ''
      },
      'createModel.ruleType' (val, oldVal) {
        if (val === oldVal) return
        switch (oldVal) {
          case 2:
            this.createModel.partnerId = ''
            this.createModel.poiId = ''
            this.createModel.goodsId = ''
            this.createModel.contractId = ''
            this.createModel.roomId = ''
            this.createModel.roomName = ''
            break
          case 3:
            this.createModel.saleRules = ''
            break
          default:
            break
        }
      }
    },
    methods: {
      // 创建
      submit () {
        let self = this
        // this.$Message.info('创建')
        var url = this.getDomain('goodsData') + Url[this.createModel.platform][this.createModel.goodsType][this.createModel.ruleType]
        // var url = 'http://0.0.0.0:8124' + Url[this.createModel.platform][this.createModel.goodsType][this.createModel.ruleType]
        self.$Spin.show()
        console.log(url)
        if (!url) {
          self.$Spin.hide()
          this.message.info('规则不正确')
        }
        let qs = require('qs')
        console.log('params', getParams(this))
        axios.post(url, qs.stringify(getParams(this)))  // 使用form data
        // axios.post(url, getParams(this))
          .then(function (message) {
            self.$Spin.hide()
            if (message.data['status'] === '1') {
              console.log('message', message.data)
              // self.$Message.success('创建成功！')
              self.$Modal.success({
                title: '创建成功 goodsId：' + message.data['goodsId']
              })
            } else if (message.data['status'] === '0') {
              console.log('message', message.data)
              // self.$Message.success('创建执行失败！')
              self.$Modal.success({
                title: '创建执行失败'
              })
            } else {
              // self.$Message.error('未知错误！')
            }
          }).catch(function (error) {
            console.log(error)
          // self.$Message.error('内部服务错误！')
          })
        // console.log(message.data)
        // console.log(message.data['goodsid'])
      }
    }
  }
</script>

<style scoped>
  #notice-card{
    margin-bottom: 24px;
  }
</style>
