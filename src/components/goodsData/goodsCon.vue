<template>
    <div>
        <head-component></head-component>
        <Tabs class="tab">
            <TabPane label="数据生成" name="create">
                <goods-data-create></goods-data-create>
            </TabPane>
            <TabPane label="数据销毁" name="destroy">
                <goods-data-destroy></goods-data-destroy>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
  import GoodsDataDestroy from './goodsDataDestroy'
  import GoodsDataCreate from './goodsDataCreate'
  export default {
    name: 'goodsCon',
    components: {GoodsDataCreate, GoodsDataDestroy}
  }
</script>

<style scoped>
  .tab{
    margin-top: 30px;
    margin-left: 1%;
    margin-right: 1%;
    width: auto
  }
</style>
