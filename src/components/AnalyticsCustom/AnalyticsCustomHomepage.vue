<template>
  <div>
    <head-component></head-component>
    <Modal v-model="addModal" title="过程度量自定义方向接入配置" style="width: 600px;" @on-ok="addNewDirection()">
      <Timeline style="margin-left: 5px">
        <TimelineItem>
          <div class="time"><span style="color:red">*</span>请填写需要接入的方向Key：（此key将作为唯一查询方向的唯一标识，不允许修改，<span style="color: red;font-weight: bolder">只支持字母</span>）
          </div>
          <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
            <div style="padding-left:8px;padding-top: 5px">
              <Input v-model="key" style="width: 430px">
              <span slot="prepend">key:</span>
              </Input>
            </div>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <div class="time"><span style="color:red">*</span>请填写方向名称：
          </div>
          <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
            <div style="padding-left:8px;padding-top: 5px">
              <Input v-model="name" style="width: 430px">
              <span slot="prepend">name:</span>
              </Input>
            </div>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <div class="time"><span style="color:red">*</span>请填写接口域名：( <span style="color: red;font-weight: bolder">http开头，结尾没有/</span> )
          </div>
          <Row type="flex" :gutter="16" style="margin-top: 10px;font-weight: bolder">
            <div style="padding-left:8px;padding-top: 5px">
              <Input v-model="domain" style="width: 430px" placeholder="http://jira.hotel.test.sankuai.com/analytics">
              <span slot="prepend">域名:</span>
              </Input>
            </div>
          </Row>
        </TimelineItem>
      </Timeline>
    </Modal>
    <Row type="flex">
      <Col span="24" order="1">
      <Card :bordered="true" :style="noticeStyle" dis-hover="">
        <Row type="flex">
          <Col span="14">
          <Row style="display: flex">
            <i class="fa fa-list-ul" style="padding-right: 15px;font-size: 24px; margin-top:2px"></i>
            <span style="font-weight: bolder;font-size: 20px">自定义过程度量方向</span>
          </Row>
          </Col>
          <Col span="10">
          </Col>
        </Row>
      </Card>
      <Card :style="cardStyle" dis-hover>
        <Alert show-icon closable banner style="margin-top: 15px;margin-left: 50px;margin-right: 50px">
          <template slot="desc">
            <span style="font-weight: bolder">本页面支持为特定方向配置自定义接口，适用场景如下：</span>
            <div><span style="font-weight: bolder">1.使用Ones作为项目管理工具</span></div>
            <div><span style="font-weight: bolder">2.使用自定义模版（不是从到店标准Ones模版中创建的项目）</span></div>
            <div><span style="font-weight: bolder">3.已按照规定接口形式提供后台接口并可调用，<span style="color: red">请务必确保所有参数均存在</span>。具体接口格式可参照文档  <a href="https://km.sankuai.com/page/142413917">接口定义</a></span></div>
            <div><span style="font-weight: bolder">4.具体数据说明请参考文档  <a href="https://km.sankuai.com/page/140955020">数据说明</a></span></div>
            <div><span style="font-weight: bolder">注：配置完自定义接口后，该方向的查询数据将会调用配置后的接口链接，请确保接口可用&格式正确。如需替换成默认接口，将该配置删除即可</span></div>
            <div><span style="font-weight: bolder">相关使用上的反馈请联系张淼（zhangmiao07）、贾晨宇（jiachenyu02）。</span></div>
          </template>
        </Alert>
        <div style="margin-top: 20px">
          <Row type="flex" style="width: 100%;">
            <Col style="width: 85%; display: flex">
            <span style="font-weight: bolder;font-size: 18px">自定义方向列表</span>
            <div style="margin-top: 2px;margin-left: 3px">
              <Button icon="md-settings" size="small" class="tool-button" @click="addCustomDirection">新增自定义方向</Button>
            </div>
            <div style="display: flex; margin-left: 5px; margin-top: -6px">
              <!--<Cascader style="width: 400px; margin-left: 5px" :data="filterList" v-model="filterSelect" v-if="cqGitList.length > 0" placeholder="请选择要筛选的方向" @on-change="filterDirection"></Cascader>-->
            </div>
            </Col>
            <!--<Col style="text-align: right; width: 15%">-->
            <!--<Input v-model="search" icon="md-search" placeholder="搜索" style="margin-top: 0px"/>-->
            <!--</Col>-->
          </Row>
          <div v-if="displayDirectionList.length === 0">
            <span style="padding-left: 15px;font-size: 12px;font-weight: bolder">未接入、或接入方向为空</span>
          </div>
          <div v-else>
            <div v-if="!isLoading">
              <div style="min-height: 800px">
                <Row type="flex" style="margin-top: 15px">
                  <Col span="6" v-for="(item, index) in displayDirectionList" :key="index">
                    <analytics-direction-card :keyword="item.keyword" :name="item.display_name" :host="item.host" :if_delete="haveDeleteAuth()"></analytics-direction-card>
                  <!--<direction-pipeline-card :id="item.id" :name="item.name" :direction_list="item.direction" :stackId="item.stackId" :enable="item.enable" :url="item.stackUrl"></direction-pipeline-card>-->
                  </Col>
                </Row>
                <!--<Row style="text-align: right; margin-top: 15px">-->
                  <!--<Page :total="Total" :page-size='pageSize' @on-page-size-change="changePageSize" @on-change="changePage" :current='currentPage'></Page>-->
                <!--</Row>-->
              </div>
            </div>
            <Spin v-else style="margin-top: 5px">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>Loading</div>
            </Spin>
          </div>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import {Bus} from '@/global/bus'
  import axios from 'axios'
  import {customanalyticsbaseAPI} from '@/global/variable'
  import AnalyticsDirectionCard from './AnalyticsDirectionCard'

  let screeHeight = window.screen.height
  let noticeStyle = {
    'marginLeft': '30px',
    'marginRight': '30px',
    'marginTop': '15px'
  }
  let cardStyle = {
    'marginLeft': '30px',
    'marginRight': '30px',
    'marginTop': '15px',
    'minHeight': (screeHeight - 285).toString() + 'px',
    'marginBottom': '20px'
  }

  export default {
    components: {AnalyticsDirectionCard},
    name: 'analytics-custom-homepage',
    data: function () {
      return {
        key: '',
        name: '',
        domain: '',
        mis: Bus.userInfo.userLogin,
        displayDirectionList: [],
        isLoading: false,
        addModal: false,
        noticeStyle: noticeStyle,
        cardStyle: cardStyle
      }
    },
    methods: {
      addCustomDirection: function () {
        this.addModal = true
        this.key = ''
        this.name = ''
        this.domain = ''
      },
      getDirectionList: function () {
        let self = this
        self.displayDirectionList = []
        axios.get(customanalyticsbaseAPI + '/search_config').then(function (message) {
          if (message.data.status === 0) {
            if (message.data.data) {
              for (const each of message.data.data) {
                self.displayDirectionList.push(each)
              }
            }
          } else {
            self.$Modal.error({
              title: '获取方向失败',
              message: message.data.msg
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '获取方向失败',
            message: '服务器内部错误'
          })
        })
      },
      addNewDirection: function () {
        let self = this
        if (this.name && this.key && this.domain) {
          if (this.getLegalkey(this.key)) {
            if (this.getLegalDomain(this.domain)) {
              let params = {
                keyword: this.key,
                display_name: this.name,
                host: this.domain
              }
              axios.post(customanalyticsbaseAPI + '/set_config', params).then(function (message) {
                if (message.data.status === 0) {
                  self.$message.success('添加成功')
                  self.getDirectionList()
                } else {
                  self.$Modal.error({
                    title: '添加失败',
                    message: message.data.msg
                  })
                }
              }).catch(function () {
                self.$Modal.error({
                  title: '添加失败',
                  message: '服务器内部错误'
                })
              })
            } else {
              self.$message.warning('请确保域名输入正确')
            }
          } else {
            self.$message.warning('请确保输入key只包含字母')
          }
        } else {
          self.$message.warning('存在未填写参数')
        }
      },
      getLegalkey: function (key) {
        let result = /^[a-zA-Z]+$/.test(key)
        if (result) {
          return true
        } else {
          return false
        }
      },
      getLegalDomain: function (domain) {
        let result = 'http://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&=_][^/]$'
        let re = new RegExp(result)
        if (re.test(domain)) {
          return true
        } else {
          return false
        }
      },
      haveDeleteAuth: function () {
        return ['sunbo09', 'liuzhenzhou', 'chenchaoyi', 'wangpeng54', 'liyonggang', 'zhangmiao07'].indexOf(this.mis) !== -1
      }
    },
    mounted: function () {
      this.getDirectionList()
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }
</style>
