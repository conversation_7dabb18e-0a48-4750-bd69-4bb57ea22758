<template>
  <Card :bordered="true" :style="cardStyle[true]" class="repo-card" style="border-left-width: 3px;">
    <Row type="flex">
      <Col span="24">
        <Row style="display: flex">
          <Col span="20">
            <div style="display: flex">
              <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                <a :title="name" target="_blank" style="color: #2d8cf0;font-size: 16px;font-weight: bold" :href="getUrl(keyword)">
                  <span style="font-weight: bold;text-overflow:ellipsis;overflow:hidden">{{name}}</span>
                </a>
              </div>
            </div>
          </Col>
          <Col span="4">
            <div style="margin-top: 8px;font-weight: bolder;padding-left: 2px; text-align: right">
              <div style="display: flex">
                <i class="el-icon-edit" style="color: gray;margin-left: 0px"></i>
                <i class="el-icon-delete" v-if="if_delete" style="color:gray;margin-left: 12px"></i>
                <!--<Tag color="cyan" class="repo-card-tag" style="text-align: center">编辑</Tag>-->
                <!--<Tag color="cyan" class="repo-card-tag" v-if="if_delete" style="text-align: center">删除</Tag>-->
              </div>
            </div>
          </Col>
        </Row>
        <Row type="flex">
          <span style="font-size: 12px;font-weight: bolder;margin-top: 2px">后台服务：</span>
          <!--<a target="_blank" style="color: #495060;font-size: 13px;font-weight: bolder" :href="getStackUrl(url)">-->
          <span style="color: #495060;font-size: 13px;font-weight: bolder">{{host}}</span>
          <!--</a>-->
        </Row>
        <!--<Row type="flex">-->
          <!--<div style="padding-top: 6px;text-align: left;overflow-x: hidden" v-if="direction_list.length > 0 && direction_list.length < 2" v-for="item in direction_list">-->
            <!--<Tag v-if="item" color="primary" style="cursor: initial">{{item.direction_name}}</Tag>-->
          <!--</div>-->
          <!--<div style="padding-top: 6px;text-align: left;overflow-x: hidden" v-if="direction_list.length >= 2" v-for="(item, index) in direction_list">-->
            <!--<Tag v-if="item && index === (direction_list.length - 2)" color="primary" style="cursor: initial">{{item.direction_name}}</Tag>-->
            <!--<Tag v-if="item && index === (direction_list.length - 1)" color="primary" style="cursor: initial">{{item.direction_name}}</Tag>-->
          <!--</div>-->
        <!--</Row>-->
      </Col>
    </Row>
  </Card>
</template>

<script>
  export default {
    props: {
      // stackId: {
      //   type: String,
      //   default: ''
      // },
      host: {
        type: String,
        default: ''
      },
      keyword: {
        type: String,
        default: ''
      },
      // direction_list: {
      //   type: Array,
      //   default: () => []
      // },
      name: {
        type: String,
        default: ''
      },
      if_delete: {
        type: Boolean,
        default: false
      }
    },
    name: 'analytics-direction-card',
    data: function () {
      return {
        cardStyle: {
          true: {
            'border-left-color': '#19be6b'
          },
          false: {
            'border-left-color': '#dcdee2'
          }
        }
      }
    },
    methods: {
      getUrl: function (keyword) {
        console.log('/homepage/analytics/custom/' + keyword + '/ones/analyticshome')
        return '/homepage/analytics/custom/' + keyword + '/ones/analyticshome'
      }
    }
  }
</script>

<style scoped>
  .repo-card-tag {
    margin-top: -16px;
    min-width: 50px;
    text-align: right;
  }
  .repo-card {
    margin-left: 15px;
    margin-right: 0;
    width: auto;
    height: auto;
    margin-bottom: 15px;
  }
</style>
