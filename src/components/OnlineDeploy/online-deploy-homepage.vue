<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
        <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}"
              dis-hover>
          <div slot="title">
            <Row type="flex" style="width: 98.8%">
              <Col style="width: 85%;display: flex">
                <div>
                  <span :style="{fontWeight:'bolder'}">线上发布质量</span>
                </div>
                <div style="margin-left: 5px;margin-top: -8px">
                  <DatePicker :value="dateRange" type="daterange" :clearable=false placement="bottom-end"
                              placeholder="请选择时间间隔" style="width: 180px" @on-change="filterByTime"></DatePicker>
                  <!--<Button @click="setOriginDeployData" type="primary">切换</Button>-->
                </div>
                <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" :transfer="false"
                          style="width: 410px;margin-top: -8px;padding-left: 10px" change-on-select
                          @on-change="getCurrentDirection">
                </Cascader>

                <div style="margin-left: 5px;margin-top: -8px">
                  <!--<Button @click="setOriginDeployData" type="primary">切换</Button>-->
                  <Button @click="setChangeAll" type="primary">切换</Button>
                </div>
              </Col>
              <Col style="text-align: right;width: 15%;">
              </Col>
            </Row>
          </div>
          <div>
            <Spin v-if="onlineLoading">
              <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
              <div>Loading</div>
            </Spin>
            <Row v-if="!onlineLoading" type="flex">
              <Col span="24">
                <Alert show-icon closable banner style="margin-top: 20px">
                  <template slot="desc">
                    <span style="font-weight: bolder">页面中存放所选方向的线上发布质量情况</span>
                    <div><span style="font-weight: bolder">需要注意的几个问题:</span></div>
                    <div><span style="font-weight: bolder; padding-left: 20px">a.时间: </span>时间筛选器初始日期0点到结束日期0点;</div>
                    <div><span style="font-weight: bolder; padding-left: 20px">b.rTag发布: </span>持续交付合并主分支后所打tag发布;</div>
                    <div><span style="font-weight: bolder; padding-left: 20px">c.其它发布: </span>使用非主分支或其它tag发布;</div>
                    <div><span style="font-weight: bolder; padding-left: 20px">d.发布失败占比: </span>发布失败次数 ／（发布成功次数 +
                      发布失败次数）;
                    </div>
                    <div><span style="font-weight: bolder; padding-left: 20px">e.回滚占比: </span>回滚总次数 / 发布成功次数;</div>
                    <div><span style="font-weight: bolder; padding-left: 20px">f.日均发布: </span>（发布成功次数 + 发布失败次数 + 回滚次数）／
                      所选的时间天数;
                    </div>
                    <div><span style="font-weight: bolder; padding-left: 20px">g.hotfix合入数: </span>hotfix分支合入主分支次数。
                    </div>
                  </template>
                </Alert>
                <Timeline style="padding-top: 15px;padding-left: 2px">
                  <!-- 修改这一部分变成两个时间表-->
                  <TimelineItem>
                    <span>Summary: </span>
                    <Row style="display: flex">
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'master发布'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.releaseSucceedMaster,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'其它发布'"
                          :needDescLine="false"
                          :data="[{
                          number: this.releaseSucceedOther,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'rTag发布'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.releaseSucceedRtag,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'commit发布'"
                          :needDescLine="false"
                          :data="[{
                          number: this.releaseSucceedCommit,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'master回滚'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.rollbackMaster,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'其它回滚'"
                          :needDescLine="false"
                          :data="[{
                          number: this.rollbackOther,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'rTag回滚'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.rollbackRtag,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="false"
                          :title_text="'commit回滚'"
                          :needDescLine="false"
                          :data="[{
                          number: this.rollbackCommit,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                    </Row>
                    <Row style="display: flex">
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'发布失败'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.releaseFailedDeploy,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'回滚失败'"
                          :needDescLine="false"
                          :data="[{
                          number: this.releaseFailedRollback,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'日均发布'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.averageCount,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'失败占比'"
                          :needDescLine="false"
                          :data="[{
                          number: this.failedProportion,
                          unit: '%'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'回滚占比'"
                          :needDescLine="false"
                          :number_auto_transfer="true"
                          :data="[{
                          number: this.rollbackProportion,
                          unit: '%'
                          }]">
                        </deploy-data-component>
                      </Col>
                      <Col span="3">
                        <deploy-data-component
                          :need_right_border="true"
                          :title_text="'hotfix合入主分支'"
                          :needDescLine="false"
                          :data="[{
                          number: this.hotfixCount,
                          unit: '次'
                          }]">
                        </deploy-data-component>
                      </Col>
                    </Row>
                  </TimelineItem>
                  <TimelineItem>
                    <span>线上发布趋势图</span>
                    <Row style="display: flex">
                      <Col span="8">
                        <div style="font-weight: bolder;padding-top: 5px">
                          <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                          <span style="font-size: 16px">发布成功情况统计（次）</span></div>
                        <div style="padding-top: 5px">
                          <Icon type="md-help-circle"
                                style="font-weight: bolder; color: #ffa500; margin-top: -2px"></Icon>
                          <span
                            style="padding-left: 5px;font-weight: bolder;color:#c5c8ce">维度: 发布方式。 统计范围: 方向下发布项。</span>
                        </div>
                        <div id="deployImage"
                             style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                      </Col>
                      <Col span="8">
                        <div style="font-weight: bolder;padding-top: 5px">
                          <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                          <span style="font-size: 16px">回滚成功情况统计（次）</span></div>
                        <div style="padding-top: 5px">
                          <Icon type="md-help-circle"
                                style="font-weight: bolder; color: #ffa500; margin-top: -2px"></Icon>
                          <span
                            style="padding-left: 5px;font-weight: bolder;color:#c5c8ce">维度: 发布方式。 统计范围: 方向下发布项。</span>
                        </div>
                        <div id="rollbackImage"
                             style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                      </Col>
                      <Col span="8">
                        <div style="font-weight: bolder;padding-top: 5px">
                          <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                          <span style="font-size: 16px">发布失败情况统计（次）</span></div>
                        <div style="padding-top: 5px">
                          <Icon type="md-help-circle"
                                style="font-weight: bolder; color: #ffa500; margin-top: -2px"></Icon>
                          <span
                            style="padding-left: 5px;font-weight: bolder;color:#c5c8ce">维度: 任务类型。 统计范围: 方向下发布项。</span>
                        </div>
                        <div id="failedImage"
                             style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                      </Col>
                    </Row>
                    <Row style="display: flex">
                      <Col span="2">
                      </Col>
                      <Col span="3">
                        <div class="ivu-tag-dot-inner" style="background-color: #00CD66"></div>
                        <span style="font-size: 10px">master发布</span>
                      </Col>
                      <Col span="5">
                        <!--<div class="ivu-tag-dot-inner" style="background-color: #405ac1"></div>-->
                        <!--<span style="font-size: 10px">rTag发布</span>-->
                        <div class="ivu-tag-dot-inner" style="background-color: #FFA500"></div>
                        <span style="font-size: 10px">其它发布</span>

                      </Col>
                      <Col span="3">
                        <div class="ivu-tag-dot-inner" style="background-color: #00CD66"></div>
                        <span style="font-size: 10px">master回滚</span>
                      </Col>
                      <Col span="7">
                        <div class="ivu-tag-dot-inner" style="background-color: #FFA500"></div>
                        <span style="font-size: 10px">其它回滚</span>
                        <!--<div class="ivu-tag-dot-inner" style="background-color: #405ac1"></div>-->
                        <!--<span style="font-size: 10px">rTag回滚</span>-->
                      </Col>
                      <Col span="4">
                        <div class="ivu-tag-dot-inner" style="background-color: #ff1e43"></div>
                        <span style="font-size: 10px">发布失败</span>
                      </Col>
                    </Row>
                    <Row style="display: flex">
                      <Col span="2">
                      </Col>
                      <Col span="3">
                        <div class="ivu-tag-dot-inner" style="background-color: #405ac1"></div>
                        <span style="font-size: 10px">rTag发布</span>

                      </Col>
                      <Col span="5">
                        <div class="ivu-tag-dot-inner" style="background-color: #c581d6"></div>
                        <span style="font-size: 10px">commit发布</span>
                      </Col>
                      <Col span="3">
                        <div class="ivu-tag-dot-inner" style="background-color: #405ac1"></div>
                        <span style="font-size: 10px">rTag回滚</span>
                      </Col>
                      <Col span="7">
                        <div class="ivu-tag-dot-inner" style="background-color: #c581d6"></div>
                        <span style="font-size: 10px">commit回滚</span>
                      </Col>
                      <Col span="4">
                        <div class="ivu-tag-dot-inner" style="background-color: #FFA500"></div>
                        <span style="font-size: 10px">回滚失败</span>
                      </Col>
                    </Row>
                  </TimelineItem>
                  <!--线上发布质量方向汇总图表-->
                  <TimelineItem v-if="isDirectionTable">
                    <span>线上发布方向汇总</span>
                    <Row style="display: flex">
                      <Col style="text-align: left;width: 70%;">
                        <div style="display: flex;padding-top: 5px;padding-left: 1px">
                          <div style="padding-left: 0px;">
                            <Button icon="ios-download-outline" type="dashed" size="small" class="tool-button"
                                    @click="exportSubCsv">导出
                            </Button>
                          </div>
                          <div style="padding-left: 10px;padding-top: 8px;">
                            <Cascader v-model="subTotalSort" :data="subSortList" :clearable=false
                                      placeholder="请选择要排序的指标"
                                      style="width: 200px;margin-top: -8px;padding-left: 10px"
                                      trigger="hover"></Cascader>
                          </div>
                          <div style="padding-left: 10px;padding-top: -5px;">
                            <Button @click="changeSubDesc" type="primary">降序</Button>
                          </div>
                          <div style="padding-left: 10px;padding-top: -5px;padding-right: 10px;">
                            <Button @click="changeSubAsc" type="primary">升序</Button>
                          </div>
                        </div>
                      </Col>
                    </Row>
                    <Row style="display: flex;padding-top: 10px">
                      <Col span="24">
                        <Table stripe border :columns='subColumns5' :data='subData5' ref="table"
                               id="direction_info_table"
                               class="direction-info-table">
                        </Table>

                        <div style='margin-top: 10px;padding-bottom: 0px;overflow: hidden;float: right'>
                          <Page :total='subTotal' :page-size='10' @on-change='changeSubPage'
                                :current='subCurrentPage'></Page>
                        </div>
                      </Col>
                    </Row>
                  </TimelineItem>
                  <!--线上发布质量仓库图表-->
                  <TimelineItem>
                    <span>线上发布质量仓库</span>

                    <Row style="display: flex">
                      <Col style="text-align: left;width: 70%;">
                        <div style="display: flex;padding-top: 5px;padding-left: 1px">
                          <div style="padding-left: 0px;">
                            <Button icon="ios-download-outline" type="dashed" size="small" class="tool-button"
                                    @click="exportCsv">导出
                            </Button>
                          </div>
                          <div style="padding-left: 10px;padding-top: 8px;">
                            <Cascader v-model="totalSort" :data="sortList" :clearable=false placeholder="请选择要排序的指标"
                                      style="width: 200px;margin-top: -8px;padding-left: 10px"
                                      trigger="hover"></Cascader>
                          </div>
                          <div style="padding-left: 10px;padding-top: -5px;">
                            <Button @click="changeDesc" type="primary">降序</Button>
                          </div>
                          <div style="padding-left: 10px;padding-top: -5px;padding-right: 10px;">
                            <Button @click="changeAsc" type="primary">升序</Button>
                          </div>
                        </div>
                      </Col>
                      <Col style="text-align: right; width: 30%; padding-top: 5px">
                        <Input v-model="search" placeholder="搜索" icon="md-search"
                               style="width: 225px;padding-bottom: 10px;"></Input>
                      </Col>
                    </Row>
                    <Table stripe border :columns='columns5' :data='data5' ref="table"
                           id="service_info_table"
                           class="service-info-table">
                    </Table>
                    <Row style="display: flex">
                      <div style='margin-top: 10px;padding-bottom: 120px;overflow: hidden;float: right'>
                        <Page :total='total' :page-size='20' @on-change='changePage' :current='currentPage'></Page>
                      </div>
                    </Row>
                  </TimelineItem>
                </Timeline>
              </Col>
            </Row>
          </div>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import Highcharts from 'highcharts/highstock'
  import {Bus} from '@/global/bus'
  import HighchartsMore from 'highcharts/highcharts-more'
  import axios from 'axios'
  import DeployDataComponent from '../OnlineDeploy/DeployDataComponent'

  HighchartsMore(Highcharts)

  export default {
    name: 'online-deploy-homepage',
    components: {DeployDataComponent},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 7)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      return {
        mis: Bus.userInfo.userLogin,
        start: start,
        end: end,
        direction: [],
        directionList: [],
        dateRange: dateRange,
        colorSet: ['#00CD66', '#20B2AA', '#FFA500', '#8192D6', '#c581d6', '#c1405a', '#405ac1'],
        onlineLoading: true,
        // 线上发布质量展示数据
        releaseSucceedMaster: 0,
        releaseSucceedRtag: 0,
        releaseSucceedCommit: 0,
        releaseSucceedOther: 0,
        releaseFailedDeploy: 0,
        releaseFailedRollback: 0,
        rollbackMaster: 0,
        rollbackRtag: 0,
        rollbackCommit: 0,
        rollbackOther: 0,
        rollbackProportion: 0,
        failedProportion: 0,
        averageCount: 0,
        hotfixCount: 0,
        isDirectionTable: false,
        // 线上发布质量展示数据
        total: 0,
        currentPage: 1,
        pageSize: 20,
        data5: [],
        columns5: [
          {
            title: 'Git仓库',
            key: 'git',
            width: 200,
            align: 'left',
            fixed: 'left',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    fade: true,
                    size: 'small'
                  },
                  style: {
                    color: '#4a9eff',
                    backgroundColor: '#e5f6ff',
                    borderColor: '#98d4ff'
                  },
                  on: {
                    click: function () {
                      window.open(params.row.url)
                    }
                  }
                }, params.row.git)
              ])
            }
          },
          {
            title: '发布项',
            key: 'appKey',
            width: 240,
            align: 'left',
            fixed: 'left',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    fade: true,
                    size: 'small'
                  },
                  style: {
                    color: '#08b7c5',
                    backgroundColor: '#d8fffd',
                    borderColor: '#43eeee'
                  },
                  on: {
                    click: function () {
                      window.open(params.row.release)
                    }
                  }
                }, params.row.appKey)
              ])
            }
          },
          {
            title: '日均发布数',
            key: 'deployAverage',
            width: 110,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Tag', {
                  props: {
                    color: 'green',
                    fade: true,
                    size: 'small'
                  }
                }, params.row.deployAverage)
              ])
            }
          },
          {
            title: '回滚占比',
            key: 'rollbackRate',
            align: 'center',
            width: 110,
            render: (h, params) => {
              return h('Tooltip', {
                props: {
                  placement: 'top'
                }
              }, [
                h('Tag', {
                  props: {
                    color: 'orange',
                    fade: false
                  }
                }, params.row.rollbackRate + '%'),
                h('div', {
                  slot: 'content',
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all'
                  }
                }, '回滚占比：计算公式为（回滚次数 / 发布成功次数),可能超过100%')
              ])
            }
          },
          {
            title: '失败占比',
            key: 'failedRate',
            align: 'center',
            width: 110,
            render: (h, params) => {
              return h('div', [
                h('Tag', {
                  props: {
                    color: 'sliver',
                    fade: false,
                    size: 'small'
                  },
                  style: {
                    color: '#858585',
                    backgroundColor: '#cbcbcb',
                    borderColor: '#a6a6a6'
                  }
                }, params.row.failedRate + '%')
              ])
            }
          },
          {
            title: 'hotfix合入数',
            key: 'hotfixCount',
            align: 'center',
            width: 130,
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.hotfixCount > 0
              } else if (value === 2) {
                return row.hotfixCount === 0
              }
            },
            render: (h, params) => {
              return h('Tooltip', {
                props: {
                  placement: 'top'
                }
              }, [
                h('Tag', {
                  props: {
                    color: 'red',
                    fade: false
                  }
                }, params.row.hotfixCount),
                h('div', {
                  slot: 'content',
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all'
                  }
                }, 'hotfix合入数：每行显示为该仓库hotfix合入主分支总数')
              ])
            }
          },
          {
            title: 'master发布',
            key: 'deployMaster',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0 or equals 5',
                value: 1
              },
              {
                label: 'less than 5',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployMaster >= 5
              } else if (value === 2) {
                return row.deployMaster < 5
              }
            }
          },
          {
            title: 'rTag发布',
            key: 'deployRTag',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployRTag > 0
              } else if (value === 2) {
                return row.deployRTag === 0
              }
            }
          },
          {
            title: 'commit发布',
            key: 'deployCommit',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployCommit > 0
              } else if (value === 2) {
                return row.deployCommit === 0
              }
            }
          },
          {
            title: '其它发布',
            key: 'deployOther',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployOther > 0
              } else if (value === 2) {
                return row.deployOther === 0
              }
            }
          },
          {
            title: 'master回滚',
            key: 'rollbackMaster',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackMaster > 0
              } else if (value === 2) {
                return row.rollbackMaster === 0
              }
            }
          },
          {
            title: 'rTag回滚',
            key: 'rollbackRTag',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackRtag > 0
              } else if (value === 2) {
                return row.rollbackRtag === 0
              }
            }
          },
          {
            title: 'commit回滚',
            key: 'rollbackCommit',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackCommit > 0
              } else if (value === 2) {
                return row.rollbackCommit === 0
              }
            }
          },
          {
            title: '其它回滚',
            key: 'rollbackOther',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackOther > 0
              } else if (value === 2) {
                return row.rollbackOther === 0
              }
            }
          },
          {
            title: '发布失败',
            key: 'deployFailed',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployFailed > 0
              } else if (value === 2) {
                return row.deployFailed === 0
              }
            }
          },
          {
            title: '回滚失败',
            key: 'rollbackFailed',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackFailed > 0
              } else if (value === 2) {
                return row.rollbackFailed === 0
              }
            }
          }
        ],
        tableData: [],
        tableDataBackup: [],
        // 表格总体排序
        sortList: [
          {
            label: 'Git仓库',
            value: 'git'
          },
          {
            label: '发布项',
            value: 'appKey'
          },
          {
            label: '日均发布数',
            value: 'deployAverage'
          },
          {
            label: '失败占比',
            value: 'failedRate'
          },
          {
            label: '回滚占比',
            value: 'rollbackRate'
          },
          {
            label: 'hotfix合入数',
            value: 'hotfixCount'
          },
          {
            label: 'master发布',
            value: 'deployMaster'
          },
          {
            label: 'rTag发布',
            value: 'deployRTag'
          },
          {
            label: 'commit发布',
            value: 'deployCommit'
          },
          {
            label: '其它发布',
            value: 'deployOther'
          },
          {
            label: 'master回滚',
            value: 'rollbackMaster'
          },
          {
            label: 'rTag回滚',
            value: 'rollbackRTag'
          },
          {
            label: 'commit回滚',
            value: 'rollbackCommit'
          },
          {
            label: '其它回滚',
            value: 'rollbackOther'
          },
          {
            label: '发布失败',
            value: 'deployFailed'
          },
          {
            label: '回滚失败',
            value: 'rollbackFailed'
          }
        ],
        totalSort: [],
        // 表格数据
        data: [],
        // 数据存储在data里
        search: '',
        // 线上发布质量子方向表格
        subTotal: 0,
        subCurrentPage: 1,
        subPageSize: 10,
        subData5: [],
        subColumns5: [
          {
            title: '方向',
            key: 'direction',
            width: 170,
            align: 'center',
            fixed: 'left',
            render: (h, params) => {
              return h('div', [
                h('Tag', {
                  props: {
                    color: '#2d8cf0',
                    fade: false
                  }
                }, params.row.direction)
              ])
            }
          },
          {
            title: '日均发布数',
            key: 'deployAverage',
            width: 110,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Tag', {
                  props: {
                    color: '#00CD66',
                    fade: false
                  }
                }, params.row.deployAverage)
              ])
            }
          },
          {
            title: '回滚占比',
            key: 'rollbackRate',
            align: 'center',
            width: 110,
            render: (h, params) => {
              return h('Tooltip', {
                props: {
                  placement: 'top'
                }
              }, [
                h('Tag', {
                  props: {
                    color: '#f09245',
                    fade: false
                  }
                }, params.row.rollbackRate + '%'),
                h('div', {
                  slot: 'content',
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all'
                  }
                }, '回滚占比：计算公式为（回滚次数 / 发布成功次数),可能超过100%')
              ])
            }
          },
          {
            title: '失败占比',
            key: 'failedRate',
            align: 'center',
            width: 110,
            render: (h, params) => {
              return h('div', [
                h('Tag', {
                  props: {
                    color: '#C0C0C0',
                    fade: false
                  }
                }, params.row.failedRate + '%')
              ])
            }
          },
          {
            title: 'hotfix合入数',
            key: 'hotfixCount',
            align: 'center',
            width: 130,
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equal 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.hotfixCount > 0
              } else if (value === 2) {
                return row.hotfixCount === 0
              }
            },
            render: (h, params) => {
              return h('Tooltip', {
                props: {
                  placement: 'top'
                }
              }, [
                h('Tag', {
                  props: {
                    color: '#ff1e43',
                    fade: false
                  }
                }, params.row.hotfixCount),
                h('div', {
                  slot: 'content',
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all'
                  }
                }, 'hotfix合入数：每行显示为该方向下hotfix合入主分支总数')
              ])
            }

          },
          {
            title: 'master发布',
            key: 'deployMaster',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 10 or equals 10',
                value: 1
              },
              {
                label: 'less than 10',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployMaster >= 10
              } else if (value === 2) {
                return row.deployMaster < 10
              }
            }
          },
          {
            title: 'rTag发布',
            key: 'deployRTag',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployRTag > 0
              } else if (value === 2) {
                return row.deployRTag === 0
              }
            }
          },
          {
            title: 'commit发布',
            key: 'deployCommit',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployCommit > 0
              } else if (value === 2) {
                return row.deployCommit === 0
              }
            }
          },
          {
            title: '其它发布',
            key: 'deployOther',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployOther > 0
              } else if (value === 2) {
                return row.deployOther === 0
              }
            }
          },
          {
            title: 'master回滚',
            key: 'rollbackMaster',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackMaster > 0
              } else if (value === 2) {
                return row.rollbackMaster === 0
              }
            }
          },
          {
            title: 'rTag回滚',
            key: 'rollbackRTag',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackRtag > 0
              } else if (value === 2) {
                return row.rollbackRtag === 0
              }
            }
          },
          {
            title: 'commit回滚',
            key: 'rollbackCommit',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackCommit > 0
              } else if (value === 2) {
                return row.rollbackCommit === 0
              }
            }
          },
          {
            title: '其它回滚',
            key: 'rollbackOther',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackOther > 0
              } else if (value === 2) {
                return row.rollbackOther === 0
              }
            }
          },
          {
            title: '发布失败',
            key: 'deployFailed',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.deployFailed > 0
              } else if (value === 2) {
                return row.deployFailed === 0
              }
            }
          },
          {
            title: '回滚失败',
            key: 'rollbackFailed',
            width: 120,
            align: 'center',
            filters: [
              {
                label: 'Greater than 0',
                value: 1
              },
              {
                label: 'Equals 0',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.rollbackFailed > 0
              } else if (value === 2) {
                return row.rollbackFailed === 0
              }
            }
          }
        ],
        subTableData: [],
        subTableDataBackup: [],
        subSortList: [
          {
            label: '日均发布数',
            value: 'deployAverage'
          },
          {
            label: '失败占比',
            value: 'failedRate'
          },
          {
            label: '回滚占比',
            value: 'rollbackRate'
          },
          {
            label: 'hotfix合入数',
            value: 'hotfixCount'
          },
          {
            label: 'master发布',
            value: 'deployMaster'
          },
          {
            label: 'rTag发布',
            value: 'deployRTag'
          },
          {
            label: 'commit发布',
            value: 'deployCommit'
          },
          {
            label: '其它发布',
            value: 'deployOther'
          },
          {
            label: 'master回滚',
            value: 'rollbackMaster'
          },
          {
            label: 'rTag回滚',
            value: 'rollbackRTag'
          },
          {
            label: 'commit回滚',
            value: 'rollbackCommit'
          },
          {
            label: '其它回滚',
            value: 'rollbackOther'
          },
          {
            label: '发布失败',
            value: 'deployFailed'
          },
          {
            label: '回滚失败',
            value: 'rollbackFailed'
          }
        ],
        subTotalSort: [],
        subData: [],
        subSearch: '',
        // 图表数据
        timeList: [],
        // 发布趋势
        deployMasterList: [],
        deployCommitList: [],
        deployRTagList: [],
        deployOtherList: [],
        // 回滚趋势
        rollbackMasterList: [],
        rollbackCommitList: [],
        rollbackRTagList: [],
        rollbackOtherList: [],
        // 发布失败趋势
        deployFailedList: [],
        rollbackFailedList: [],
        // 测试数据
        detailSummaryData: [],
        listForTest: [],
        deployDailyData: [],
        // 测试数据
        saveDeployDaily: [],
        savePlusData: [],
        timeChange: false
      }
    },
    methods: {
      getCurrentDirection: function (value, selectedData) {
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = item
      },
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
        let self = this
        // this.onlineLoading = true
        self.timeChange = true
        self.setOriginDeployData()
        // this.onlineLoading = false
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      setDayDiff: function () {
        let stringTimeStart = Date.parse(new Date(this.start))
        let stringTimeEnd = Date.parse(new Date(this.end))
        if (stringTimeStart > stringTimeEnd) {
          stringTimeStart = Date.parse(new Date(this.end))
          stringTimeEnd = Date.parse(new Date(this.start))
        }
        if (Date.parse(new Date(this.start)) > Date.parse(new Date())) {
          stringTimeStart = Date.parse(new Date())
          stringTimeEnd = Date.parse(new Date())
        } else if (Date.parse(new Date(this.end)) > Date.parse(new Date())) {
          stringTimeEnd = Date.parse(new Date())
        }
        this.start = this.getTimeString(new Date(stringTimeStart))
        this.end = this.getTimeString(new Date(stringTimeEnd))
        let timeDiff = stringTimeEnd - stringTimeStart
        let dayDiff = 1
        if (timeDiff !== 0) {
          dayDiff = timeDiff / (24 * 60 * 60 * 1000) + 1
        }
        return dayDiff
      },
      getPercent: function (cover, total) {
        if (total !== 0) {
          return (cover / total * 100).toFixed(0)
        } else {
          return 0
        }
      },
      generateProportion: function (dayDiff) {
        let failCount = this.releaseFailedDeploy + this.releaseFailedRollback
        let successCount = this.releaseSucceedMaster + this.releaseSucceedRtag + this.releaseSucceedCommit + this.releaseSucceedOther
        let rollbackCount = this.rollbackMaster + this.rollbackRtag + this.rollbackCommit + this.rollbackOther
        let temp = Math.round(rollbackCount / successCount * 1000) / 10
        this.rollbackProportion = temp
        temp = Math.round(failCount / (successCount + failCount) * 1000) / 10
        this.failedProportion = temp
        this.averageCount = Math.round((failCount + successCount + rollbackCount) / dayDiff)
      },
      getDirections: function () {
        let self = this
        let url = this.getDomain('config') + '/mcd/org/basic?direction_id=1'
        axios.get(url).then(function (message) {
          self.directionList = []
          self.directionList.push(message.data.info)
        })
      },
      // 最开始的数据接口
      setOriginDeployData: function () {
        // 设置原始数据
        let dayDiff = this.setDayDiff()
        this.onlineLoading = true
        this.initializeToZero()
        let self = this
        let url = 'http://qa.sankuai.com/data/plus/deploy_summary' + '?from=' + this.start + '&to=' + this.end + '&direction=0'
        axios.get(url).then(function (message) {
          self.detailSummaryData = message.data.集团
          self.saveDetailSummary = message.data.集团
          if (self.timeChange === true) {
            self.initImageData()
          } else {
            self.originInit(dayDiff)
          }
        })
      },
      originInit: function (dayDiff) {
        let self = this
        self.operateDisplayData(self.detailSummaryData)
        self.generateProportion(dayDiff)
        self.data = []
        self.setGitDetail(self.detailSummaryData.children)
        self.subData = self.directionSubData(self.detailSummaryData.children)
        self.isDirectionTable = true
        self.initSubTableData()
        self.initTableData()
        self.changeDeployData()
      },
      // 填充线上发布质量数据
      setGitDetail: function (messageData) {
        let self = this
        for (let one in messageData) {
          let oneItem = messageData[one]
          let oneLeaf = oneItem.if_leaf
          if (oneLeaf === true) {
            let gitInfoData = self.gitInfoData(oneItem)
            for (let key of gitInfoData) {
              self.data.push(key)
            }
          } else {
            for (let two in messageData[one].children) {
              let twoItem = messageData[one].children[two]
              let twoLeaf = twoItem.if_leaf
              if (twoLeaf === true) {
                let gitInfoData = self.gitInfoData(twoItem)
                for (let key of gitInfoData) {
                  self.data.push(key)
                }
              } else {
                for (let three in twoItem.children) {
                  let threeItem = twoItem.children[three]
                  let threeLeaf = threeItem.if_leaf
                  if (threeLeaf === true) {
                    let gitInfoData = self.gitInfoData(threeItem)
                    for (let key of gitInfoData) {
                      self.data.push(key)
                    }
                  } else {
                    for (let four in threeItem.children) {
                      let fourItem = threeItem.children[four]
                      let fourLeaf = fourItem.if_leaf
                      if (fourLeaf === true) {
                        let gitInfoData = self.gitInfoData(fourItem)
                        for (let key of gitInfoData) {
                          self.data.push(key)
                        }
                      } else {
                        for (let five in fourItem.children) {
                          let fiveItem = fourItem.children[five]
                          let fiveLeaf = fiveItem.if_leaf
                          if (fiveLeaf === true) {
                            let gitInfoList = this.gitInfoData(fiveItem)
                            for (let key of gitInfoList) {
                              self.data.push(key)
                            }
                          } else {
                            for (let six in fiveItem.children) {
                              let sixItem = fiveItem.children[six]
                              let sixLeaf = sixItem.if_leaf
                              if (sixLeaf === true) {
                                let gitInfoList = this.gitInfoData(sixItem)
                                for (let key of gitInfoList) {
                                  self.data.push(key)
                                }
                              } else {
                                for (let seven in sixItem.children) {
                                  let sevenItem = sixItem.children[seven]
                                  let gitInfoList = this.gitInfoData(sevenItem)
                                  for (let key of gitInfoList) {
                                    self.data.push(key)
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      setTableData: function (directionItem, dayDiff) {
        let self = this
        if (directionItem != null) {
          self.operateDisplayData(directionItem)
          self.changeImageData(self.direction)
          self.generateProportion(dayDiff)
          self.data = []
          if (directionItem.if_leaf === true) {
            self.isDirectionTable = false
            let gitInfoData = self.gitInfoData(directionItem)
            for (let key of gitInfoData) {
              self.data.push(key)
            }
          } else {
            self.isDirectionTable = true
            self.setGitDetail(directionItem.children)
          }
          self.subData = self.directionSubData(directionItem.children)
          self.initTableData()
          self.initSubTableData()
        } else {
          this.setAllZero()
        }
      },
      originDeployData: function (dayDiff) {
        // 设置原始数据
        this.onlineLoading = true
        this.initializeToZero()
        let self = this
        self.originInit(dayDiff)
        this.onlineLoading = false
      },
      initializeToZero: function () {
        this.releaseSucceedMaster = 0
        this.releaseSucceedRtag = 0
        this.releaseSucceedCommit = 0
        this.releaseSucceedOther = 0
        this.releaseFailedDeploy = 0
        this.releaseFailedRollback = 0
        this.rollbackMaster = 0
        this.rollbackRtag = 0
        this.rollbackCommit = 0
        this.rollbackOther = 0
        this.rollbackProportion = 0
        this.failedProportion = 0
        this.averageCount = 0
        this.hotfixCount = 0
      },
      setAllZero: function (dayDiff) {
        let self = this
        this.initializeToZero()
        this.timeList = []
        this.imageDataToZero()
        this.generateTimeList()
        self.pushImageZeroData()
        self.isDirectionTable = false
        self.data = []
        self.subData = []
        self.initTableData()
        setTimeout(function () {
          // 防止highcharts报错 #13
          self.setDeploy()
          self.setRollback()
          self.setFailed()
        }, 500)
      },
      setChangeAll: function () {
        let self = this
        let dayDiff = self.setDayDiff()
        self.detailSummaryData = self.saveDetailSummary
        if (self.detailSummaryData === null) {
          self.setAllZero()
        } else if (self.direction.length === 1 || self.direction.length === 0) {
          self.changeImageData(self.direction)
          self.originDeployData(dayDiff)
        } else if (self.direction.length === 2) {
          let directionItem = self.detailSummaryData.children[self.direction[1]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 3) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 4) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]].children[self.direction[3]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 5) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]].children[self.direction[3]].children[self.direction[4]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 6) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]].children[self.direction[3]].children[self.direction[4]].children[self.direction[5]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 7) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]].children[self.direction[3]].children[self.direction[4]].children[self.direction[5]].children[self.direction[6]]
          self.setTableData(directionItem, dayDiff)
        } else if (self.direction.length === 8) {
          let directionItem = self.detailSummaryData.children[self.direction[1]].children[self.direction[2]].children[self.direction[3]].children[self.direction[4]].children[self.direction[5]].children[self.direction[6]].children[self.direction[7]]
          self.setTableData(directionItem, dayDiff)
        } else {
          self.setAllZero()
        }
      },
      // 子方向数据操作
      changeData: function (data) {
        let temp
        if (data > 1000) {
          temp = Math.round(data / 1000 * 100) / 100 + 'k'
        } else {
          temp = data
        }
        return temp
      },
      changeDeployData: function () {
        let self = this
        self.releaseSucceedMaster = self.changeData(self.releaseSucceedMaster)
        self.releaseSucceedRtag = self.changeData(self.releaseSucceedRtag)
        self.releaseSucceedCommit = self.changeData(self.releaseSucceedCommit)
        self.releaseSucceedOther = self.changeData(self.releaseSucceedOther)
        self.rollbackMaster = self.changeData(self.rollbackMaster)
        self.rollbackRtag = self.changeData(self.rollbackRtag)
        self.rollbackCommit = self.changeData(self.rollbackCommit)
        self.rollbackOther = self.changeData(self.rollbackOther)
        self.releaseFailedDeploy = self.changeData(self.releaseFailedDeploy)
        self.releaseFailedRollback = self.changeData(self.releaseFailedRollback)
      },
      // 子方向表
      directionSubData: function (messageData) {
        let dayDiff = this.setDayDiff()
        let self = this
        self.listForTest = messageData
        let directionList = []
        for (let direction in self.listForTest) {
          let subUnitData = {}
          subUnitData['deployMaster'] = self.listForTest[direction].deploy_master
          subUnitData['deployRTag'] = self.listForTest[direction].deploy_rtag
          subUnitData['deployCommit'] = self.listForTest[direction].deploy_commitId
          subUnitData['deployOther'] = self.listForTest[direction].deploy_not_master
          subUnitData['rollbackMaster'] = self.listForTest[direction].rollback_master
          subUnitData['rollbackRTag'] = self.listForTest[direction].rollback_rtag
          subUnitData['rollbackCommit'] = self.listForTest[direction].rollback_commitId
          subUnitData['rollbackOther'] = self.listForTest[direction].rollback_not_master
          subUnitData['deployFailed'] = self.listForTest[direction].deploy_failed
          subUnitData['rollbackFailed'] = self.listForTest[direction].rollback_failed
          subUnitData['hotfixCount'] = self.listForTest[direction].hotfix
          subUnitData['direction'] = direction
          let failCount = self.listForTest[direction].deploy_failed + self.listForTest[direction].rollback_failed
          let successCount = self.listForTest[direction].deploy_master + self.listForTest[direction].deploy_rtag + self.listForTest[direction].deploy_commitId + self.listForTest[direction].deploy_not_master
          let rollbackCount = self.listForTest[direction].rollback_master + self.listForTest[direction].rollback_rtag + self.listForTest[direction].rollback_commitId + self.listForTest[direction].rollback_not_master
          let failedRate = 0
          let rollbackRate = 0
          let deployAverage = 0
          if (failCount === 0 && successCount === 0) {
            failedRate = 0
          } else if (failCount !== 0 && successCount === 0) {
            failedRate = 100
          } else {
            failedRate = Math.round(failCount / (successCount + failCount) * 1000) / 10
          }
          subUnitData['failedRate'] = failedRate
          if (rollbackCount === 0 && successCount === 0) {
            rollbackRate = 0
          } else if (rollbackCount !== 0 && successCount === 0) {
            rollbackRate = 100
          } else {
            rollbackRate = Math.round(rollbackCount / successCount * 1000) / 10
          }
          subUnitData['rollbackRate'] = rollbackRate
          deployAverage = Math.round((failCount + successCount + rollbackCount) / dayDiff * 100) / 100
          subUnitData['deployAverage'] = deployAverage
          if (subUnitData['deployMaster'] == null) {
            subUnitData['deployMaster'] = 0
            subUnitData['deployRTag'] = 0
            subUnitData['deployCommit'] = 0
            subUnitData['deployOther'] = 0
            subUnitData['rollbackMaster'] = 0
            subUnitData['rollbackRTag'] = 0
            subUnitData['rollbackCommit'] = 0
            subUnitData['rollbackOther'] = 0
            subUnitData['deployFailed'] = 0
            subUnitData['rollbackFailed'] = 0
            subUnitData['hotfixCount'] = 0
            subUnitData['failedRate'] = 0
            subUnitData['rollbackRate'] = 0
            subUnitData['deployAverage'] = 0
          }
          directionList.push(subUnitData)
        }
        return directionList
      },
      initSubTableData: function () {
        // 降序
        let value = this.subData
        let self = this
        let sortedObjKeys = Object.keys(value).sort(function (a, b) {
          return value[b].deployAverage - value[a].deployAverage
        })
        self.subTableData = []
        self.subTableDataBackup = []
        for (let key of sortedObjKeys) {
          self.subTableData.push(value[key])
          self.subTableDataBackup.push(value[key])
        }
        self.subTotal = value.length
        self.subSearch = ''
        self.initSubData(self.subTableData, self.subPageSize)
        self.changeSubPage(self.subCurrentPage)
      },
      changeSubCurrentPage: function (data) {
        let currentPage = this.subCurrentPage
        let pageSize = this.subPageSize
        this.subData5 = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.subData5.push(data[i])
        }
      },
      initSubData: function (MessageData, pageSize) {
        let dataList = []
        if (MessageData) {
          if (pageSize < MessageData.length) {
            for (let i = 0; i < pageSize; i++) {
              let item = MessageData[i]
              let keys = Object.keys(item)
              if (keys.length) {
                for (let j = 0; j < keys.length; j++) {
                  if (item[keys[j]]) {
                    item[keys[j]].toString()
                  }
                }
                dataList.push(item)
              }
            }
          } else {
            for (let i = 0; i < MessageData.length; i++) {
              let item = MessageData[i]
              let keys = Object.keys(item)
              if (keys.length) {
                for (let j = 0; j < keys.length; j++) {
                  if (item[keys[j]]) {
                    item[keys[j]].toString()
                  }
                }
                dataList.push(item)
              }
            }
          }
        } else {
          dataList = []
        }
        return dataList
      },
      changeSubPage: function (page) {
        this.subCurrentPage = page
        this.changeSubCurrentPage(this.subTableData)
      },
      // 仓库数据
      gitInfoData: function (messageData) {
        let dayDiff = this.setDayDiff()
        let self = this
        self.listForTest = messageData.children
        let gitInfoDataList = []
        for (let git in self.listForTest) {
          let temp = self.listForTest[git].children
          if (temp.deploy_master === null) {
            continue
          }
          for (let appkey in self.listForTest[git].children) {
            let subUnitData = {}
            subUnitData['deployMaster'] = self.listForTest[git].children[appkey].deploy_master
            subUnitData['deployRTag'] = self.listForTest[git].children[appkey].deploy_rtag
            subUnitData['deployCommit'] = self.listForTest[git].children[appkey].deploy_commitId
            subUnitData['deployOther'] = self.listForTest[git].children[appkey].deploy_not_master
            subUnitData['rollbackMaster'] = self.listForTest[git].children[appkey].rollback_master
            subUnitData['rollbackRTag'] = self.listForTest[git].children[appkey].rollback_rtag
            subUnitData['rollbackCommit'] = self.listForTest[git].children[appkey].rollback_commitId
            subUnitData['rollbackOther'] = self.listForTest[git].children[appkey].rollback_not_master
            subUnitData['deployFailed'] = self.listForTest[git].children[appkey].deploy_failed
            subUnitData['rollbackFailed'] = self.listForTest[git].children[appkey].rollback_failed
            subUnitData['hotfixCount'] = self.listForTest[git].hotfix
            subUnitData['appKey'] = appkey
            let configUrl = git.split('/')
            let repo = configUrl[4]
            subUnitData['url'] = self.listForTest[git].url
            subUnitData['git'] = repo
            subUnitData['release'] = 'https://plus.mws.sankuai.com/#/release/job/list?releaseId=' + self.listForTest[git].children[appkey].releaseId
            let failCount = self.listForTest[git].children[appkey].deploy_failed + self.listForTest[git].children[appkey].rollback_failed
            let successCount = self.listForTest[git].children[appkey].deploy_master + self.listForTest[git].children[appkey].deploy_rtag + self.listForTest[git].children[appkey].deploy_commitId + self.listForTest[git].children[appkey].deploy_not_master
            let rollbackCount = self.listForTest[git].children[appkey].rollback_master + self.listForTest[git].children[appkey].rollback_rtag + self.listForTest[git].children[appkey].rollback_commitId + self.listForTest[git].children[appkey].rollback_not_master
            let failedRate = 0
            let rollbackRate = 0
            let deployAverage = 0
            if (failCount === 0 && successCount === 0) {
              failedRate = 0
            } else if (failCount !== 0 && successCount === 0) {
              failedRate = 100
            } else {
              failedRate = Math.round(failCount / (successCount + failCount) * 1000) / 10
            }
            subUnitData['failedRate'] = failedRate
            if (rollbackCount === 0 && successCount === 0) {
              rollbackRate = 0
            } else if (rollbackCount !== 0 && successCount === 0) {
              rollbackRate = 100
            } else {
              rollbackRate = Math.round(rollbackCount / successCount * 1000) / 10
            }
            subUnitData['rollbackRate'] = rollbackRate
            deployAverage = Math.round((failCount + successCount + rollbackCount) / dayDiff * 100) / 100
            subUnitData['deployAverage'] = deployAverage
            gitInfoDataList.push(subUnitData)
          }
        }
        return gitInfoDataList
      },
      operateDisplayData: function (messageData) {
        let self = this
        self.releaseSucceedMaster = messageData.deploy_master
        self.releaseSucceedRtag = messageData.deploy_rtag
        self.releaseSucceedCommit = messageData.deploy_commitId
        self.releaseSucceedOther = messageData.deploy_not_master
        self.hotfixCount = messageData.hotfix
        self.rollbackMaster = messageData.rollback_master
        self.rollbackRtag = messageData.rollback_rtag
        self.rollbackCommit = messageData.rollback_commitId
        self.rollbackOther = messageData.rollback_not_master
        self.releaseFailedDeploy = messageData.deploy_failed
        self.releaseFailedRollback = messageData.rollback_failed
        if (self.releaseSucceedMaster === null) {
          this.initializeToZero()
        }
      },
      generateTimeList: function () {
        this.timeList = []
        let stringTimeStart = Date.parse(new Date(this.start))
        let stringTimeEnd = Date.parse(new Date(this.end))
        let timeDiff = stringTimeEnd - stringTimeStart
        let dayDiff = 1
        if (timeDiff !== 0) {
          dayDiff = timeDiff / (24 * 60 * 60 * 1000) + 1
        }
        for (let i = 0; i < dayDiff; i++) {
          let nowTime = stringTimeStart + i * (24 * 60 * 60 * 1000)
          let nowDate = new Date(nowTime)
          this.timeList.push(this.getTimeString(nowDate))
        }
      },
      pushImageZeroData: function () {
        let self = this
        this.imageDataToZero()
        for (let i = 0; i < self.timeList.length; i++) {
          self.deployMasterList.push(0)
          self.deployRTagList.push(0)
          self.deployCommitList.push(0)
          self.deployOtherList.push(0)
          self.rollbackMasterList.push(0)
          self.rollbackRTagList.push(0)
          self.rollbackCommitList.push(0)
          self.rollbackOtherList.push(0)
          self.deployFailedList.push(0)
          self.rollbackFailedList.push(0)
        }
      },
      getDailyData: function (messageData) {
        let self = this
        this.imageDataToZero()
        for (let date of self.timeList) {
          self.deployMasterList.push(messageData.deploy_master[date])
          self.deployRTagList.push(messageData.deploy_rtag[date])
          self.deployCommitList.push(messageData.deploy_commitId[date])
          self.deployOtherList.push(messageData.deploy_not_master[date])
          self.rollbackMasterList.push(messageData.rollback_master[date])
          self.rollbackRTagList.push(messageData.rollback_rtag[date])
          self.rollbackCommitList.push(messageData.rollback_commitId[date])
          self.rollbackOtherList.push(messageData.rollback_not_master[date])
          self.deployFailedList.push(messageData.deploy_failed[date])
          self.rollbackFailedList.push(messageData.rollback_failed[date])
        }
      },
      initImageData: function () {
        this.imageDataToZero()
        let url = 'http://qa.sankuai.com/data/plus/deploy_daily' + '?from=' + this.start + '&to=' + this.end + '&direction=0'
        let self = this
        self.generateTimeList()
        axios.get(url).then(function (message) {
          // todo saveDeployDaily
          self.saveDeployDaily = message.data.集团
          self.deployDailyData = message.data.集团
          self.getDailyData(self.deployDailyData)
          if (self.timeChange === true) {
            self.setChangeAll()
            self.timeChange = false
          }
          setTimeout(function () {
            // 防止highcharts报错 #13
            self.setDeploy()
            self.setRollback()
            self.setFailed()
          }, 500)
          self.onlineLoading = false
        })
      },
      imageDataToZero: function () {
        let self = this
        self.deployMasterList = []
        self.deployRTagList = []
        self.deployCommitList = []
        self.deployOtherList = []
        self.rollbackMasterList = []
        self.rollbackRTagList = []
        self.rollbackCommitList = []
        self.rollbackOtherList = []
        self.deployFailedList = []
        self.rollbackFailedList = []
      },
      // 变换趋势图数据
      changeImageData: function (list) {
        let self = this
        self.generateTimeList()
        self.deployDailyData = self.saveDeployDaily
        self.imageDataToZero()
        // todo 修正dataList
        if (list.length === 1 || list.length === 0) {
          self.getDailyData(self.deployDailyData)
        } else if (list.length === 2) {
          self.getDailyData(self.deployDailyData.children[list[1]])
        } else if (list.length === 3) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]])
        } else if (list.length === 4) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]].children[list[3]])
        } else if (list.length === 5) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]].children[list[3]].children[list[4]])
        } else if (list.length === 6) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]].children[list[3]].children[list[4]].children[list[5]])
        } else if (list.length === 7) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]].children[list[3]].children[list[4]].children[list[5]].children[list[6]])
        } else if (list.length === 8) {
          self.getDailyData(self.deployDailyData.children[list[1]].children[list[2]].children[list[3]].children[list[4]].children[list[5]].children[list[6]].children[list[7]])
        } else {
          self.pushImageZeroData()
        }
        setTimeout(function () {
          // 防止highcharts报错 #13
          self.setDeploy()
          self.setRollback()
          self.setFailed()
        }, 500)
      },
      // 表格函数
      initTableData: function () {
        // 降序
        let value = this.data
        let self = this
        let sortedObjKeys = Object.keys(value).sort(function (a, b) {
          return value[b].deployAverage - value[a].deployAverage
        })
        self.tableData = []
        self.tableDataBackup = []
        for (let key of sortedObjKeys) {
          self.tableData.push(value[key])
          self.tableDataBackup.push(value[key])
        }
        self.total = value.length
        self.search = ''
        self.initData(self.tableData, self.pageSize)
        self.changePage(self.currentPage)
      },
      initData: function (MessageData, pageSize) {
        if (MessageData) {
          if (pageSize < MessageData.length) {
            for (let i = 0; i < pageSize; i++) {
              let item = MessageData[i]
              let keys = Object.keys(item)
              if (keys.length) {
                for (let j = 0; j < keys.length; j++) {
                  if (item[keys[j]]) {
                    item[keys[j]].toString()
                  }
                }
                this.data5.push(item)
              }
            }
          } else {
            for (let i = 0; i < MessageData.length; i++) {
              let item = MessageData[i]
              let keys = Object.keys(item)
              if (keys.length) {
                for (let j = 0; j < keys.length; j++) {
                  if (item[keys[j]]) {
                    item[keys[j]].toString()
                  }
                }
                this.data5.push(item)
              }
            }
          }
        } else {
          this.data5 = []
        }
      },
      changeCurrentPage: function (data) {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.data5 = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.data5.push(data[i])
        }
      },
      getSortObjKeys: function (originDate, sortValue) {
        let sortedObjKeys = []
        //  let sortValue = this.totalSort[0]
        if (sortValue === 'git') {
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[b].git.localeCompare(originDate[a].git)
          })
        } else if (sortValue === 'appKey') {
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[b].appKey.localeCompare(originDate[a].appKey)
          })
        } else if (sortValue === undefined) {
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[b].deployAverage - originDate[a].deployAverage
          })
        } else {
          sortedObjKeys = Object.keys(originDate).sort(function (a, b) {
            return originDate[b][sortValue] - originDate[a][sortValue]
          })
        }
        return sortedObjKeys
      },
      changeDesc: function () {
        let originDate = this.tableData
        let sortedObjKeys = this.getSortObjKeys(originDate, this.totalSort[0])
        this.tableData = []
        for (let key of sortedObjKeys) {
          this.tableData.push(originDate[key])
        }
        this.data5 = this.initData(this.tableData, 20)
        this.changePage(this.currentPage)
      },
      changeAsc: function () {
        let originDate = this.tableData
        let sortedObjKeys = this.getSortObjKeys(originDate, this.totalSort[0])
        sortedObjKeys.reverse()
        this.tableData = []
        for (let key of sortedObjKeys) {
          this.tableData.push(originDate[key])
        }
        this.data5 = this.initData(this.tableData, 20)
        this.changePage(this.currentPage)
      },
      changeSubDesc: function () {
        let originDate = this.subTableData
        let sortedObjKeys = this.getSortObjKeys(originDate, this.subTotalSort[0])
        this.subTableData = []
        for (let key of sortedObjKeys) {
          this.subTableData.push(originDate[key])
        }
        this.subData5 = this.initSubData(this.subTableData, 10)
        this.changeSubPage(this.subCurrentPage)
      },
      changeSubAsc: function () {
        let originDate = this.subTableData
        let sortedObjKeys = this.getSortObjKeys(originDate, this.subTotalSort[0])
        sortedObjKeys.reverse()
        this.subTableData = []
        for (let key of sortedObjKeys) {
          this.subTableData.push(originDate[key])
        }
        this.subData5 = this.initSubData(this.subTableData, 10)
        this.changeSubPage(this.subCurrentPage)
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage(this.tableData)
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage(this.tableData)
      },
      // 趋势图函数
      setDeploy: function () {
        Highcharts.chart('deployImage', {
          chart: {
            type: 'spline'
          },
          title: {
            text: null
          },
          legend: {
            enabled: false
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true
              },
              // 关闭鼠标跟踪，对应的提示框、点击事件会失效
              enableMouseTracking: true
            }
          },
          xAxis: {
            categories: this.timeList
          },
          yAxis: {
            title: {
              text: ''
            }
          },
          credits: {
            enabled: false
          },
          series: [
            {
              name: 'master发布',
              data: this.deployMasterList,
              color: '#00CD66'
            },
            {
              name: '其它发布',
              data: this.deployOtherList,
              color: '#FFA500'
            },
            {
              name: 'rTag发布',
              data: this.deployRTagList,
              color: '#405ac1'
            },
            {
              name: 'commit发布',
              data: this.deployCommitList,
              color: '#c581d6'
            }
          ]
        })
      },
      setRollback: function () {
        Highcharts.chart('rollbackImage', {
          chart: {
            type: 'spline'
          },
          title: {
            text: null
          },
          legend: {
            enabled: false
          },
          plotOptions: {
            series: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              },
              enableMouseTracking: true
            }
          },
          xAxis: {
            categories: this.timeList
          },
          yAxis: {
            title: {
              text: ''
            }
          },
          credits: {
            enabled: false
          },
          series: [
            {
              name: 'master回滚',
              data: this.rollbackMasterList,
              color: '#00CD66'
            },
            {
              name: 'rTag回滚',
              data: this.rollbackRTagList,
              color: '#405ac1'
            },
            {
              name: 'commit回滚',
              data: this.rollbackCommitList,
              color: '#c581d6'
            },
            {
              name: '其它回滚',
              data: this.rollbackOtherList,
              color: '#FFA500'
            }
          ]
        })
      },
      setFailed: function () {
        Highcharts.chart('failedImage', {
          chart: {
            type: 'spline'
          },
          title: {
            text: null
          },
          legend: {
            enabled: false
          },
          plotOptions: {
              // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            series: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              },
              enableMouseTracking: true
            }
          },
          xAxis: {
            categories: this.timeList
          },
          yAxis: {
            title: {
              text: ''
            }
          },
          credits: {
            enabled: false
          },
          series: [
            {
              name: '发布失败',
              data: this.deployFailedList,
              color: '#ff1e43'
            },
            {
              name: '回滚失败',
              data: this.rollbackFailedList,
              color: '#FFA500'
            }
          ]
        })
      },
      exportSubCsv: function () {
        this.$refs.table.exportCsv({
          filename: '子方向服务详情',
          columns: this.subColumns5,
          data: this.subTableData
        })
      },
      exportCsv: function () {
        this.$refs.table.exportCsv({
          filename: '服务详情',
          columns: this.columns5,
          data: this.tableData
        })
      }
    },
    watch: {
      search: function () {
        if (this.search) {
          this.tableData = []
          let tableDataBackup = this.tableDataBackup
          let sortedObjKeys = Object.keys(tableDataBackup).sort(function (a, b) {
            return tableDataBackup[b].deployMaster - tableDataBackup[a].deployMaster
          })
          for (let key of sortedObjKeys) {
            let item = this.tableDataBackup[key]
            let keys = Object.keys(item)
            for (let unit of keys) {
              if (item[unit].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) {
                this.tableData.push(this.tableDataBackup[key])
                break
              }
            }
          }
        } else {
          this.tableData = this.tableDataBackup
        }
        this.total = this.tableData.length
        this.changeCurrentPage(this.tableData)
        if (this.data5.length === 0) {
          this.currentPage = 1
        }
      }
    },
    mounted: function () {
      this.setOriginDeployData()
      this.getDirections()
      this.initImageData()
      this.setFailed()
      this.setRollback()
      this.setDeploy()
    }
  }
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }

  @keyframes ani-demo-spin {
    from {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  a:link {
    text-decoration: none;
  }

  a:active {
    text-decoration: none;
  }

  　a:hover {
    text-decoration: none;
  }

  　a:visited {
    text-decoration: none;
  }
</style>
