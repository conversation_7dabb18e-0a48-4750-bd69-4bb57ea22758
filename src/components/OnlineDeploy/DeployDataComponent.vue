<template>
  <div :style="style">
    <div v-if="title_type==='text'">
      <span style="color:#808695; font-weight: bolder; font-size: 16px">{{title_text}}<Icon v-if="title_star" class="star" type="md-star" /></span>
    </div>
    <div v-if="title_type==='pop'">
      <Poptip trigger="hover" :transfer="true">
        <span style="color:#808695; font-weight: bolder; font-size: 16px">{{title_text}}<Icon v-if="title_star" class="star" type="md-star" /></span>
        <div class="api" slot="title">
          <Icon type="ios-help-circle-outline" style="margin-right: 5px"/>{{title_data.content_title}}
        </div>
        <div class="api" slot="content" v-for="(content, index) in title_data.content_data">
          {{content.title}}：<Tag color="blue" v-if="content.type==='tag'">{{content.content}}</Tag>
        </div>
      </Poptip>
    </div>
    <div style="display: inline-flex" v-if="data_type === 'text' && number_auto_transfer">
      <div v-if="item.number !== null && item.number !== 'N/A'" v-for="(item, index) in data" :key="index" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px">
            {{getNumber(item.number)}}
          </span>
        </span>
        <span style="color: #808695; font-size: 16px">{{getUnit(item.number, item.unit)}}</span>
      </div>
      <div v-else style="margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px;">
            -
          </span>
        </span>
      </div>
    </div>
    <div style="display: inline-flex" v-if="data_type === 'text' && !number_auto_transfer">
      <div v-if="item.number !== null && item.number !== 'N/A'" v-for="(item, index) in data" :key="index" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px">
            {{item.number}}
          </span>
        </span>
        <span style="color: #808695; font-size: 16px">{{item.unit}}</span>
      </div>
      <div v-else style="margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px">
            -
          </span>
        </span>
      </div>
    </div>
    <div style="display: inline-flex" v-if="data_type === 'rate'">
      <div v-for="(item, index) in data" :key="index" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
        <Rate allow-half disabled class="rateRadar" v-model="item.star" style="margin-top: 15px"/>
      </div>
    </div>
    <div style="display: inline-flex" v-if="data_type === 'tag'">
      <div v-for="(item, index) in data" :key="index" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
        <Tag style="margin-top: 20px; margin-bottom: 1px; cursor: initial; font-weight: bolder" color="cyan">{{item.text}}</Tag>
      </div>
    </div>
    <div v-if="data_type === 'time'">
      <div v-if="item.time !== null && item.time !== 'N/A'" v-for="(item, index) in data" :key="index" style="padding-left: 10px; padding-right: 10px; margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px">
            <Time :time="item.time" />
          </span>
        </span>
      </div>
      <div v-else style="margin-top: 5px; margin-bottom: 10px">
        <span :style="{fontWeight:'border', color: '#17233d'}">
          <span style="font-weight: bolder; font-size: 32px">
            -
          </span>
        </span>
      </div>
    </div>
    <div v-if="data_type === 'list' && !number_auto_transfer">
      <div style="margin: auto; width: 95%; display: inline-flex;" v-for="(item, index) in data" :key="index">
        <div style="text-align: left; color: #515a6e; font-weight: bolder; font-size: 15px; margin-top: 8px"><span>{{item.key}}</span></div>
        <div style="text-align: right; color: #17233d; font-weight: bolder; font-size: 25px">{{item.value}}<span style="color: #808695; font-size: 16px">{{getUnit(item.value, item.unit)}}</span></div>
      </div>
    </div>
    <div v-if="data_type === 'list' && number_auto_transfer">
      <div style="margin: auto; width: 95%; display: inline-flex;" v-for="(item, index) in data" :key="index">
        <div style="text-align: left; color: #515a6e; font-weight: bolder; font-size: 15px; margin-top: 8px; width: 50%">
          <div><span>{{item.key}}</span></div>
        </div>
        <div style="text-align: right; color: #17233d; font-weight: bolder; font-size: 25px; width: 50%">
          <div> {{getNumber(item.value)}} <span style="color: #808695; font-size: 16px">{{getUnit(item.value, item.unit)}}</span></div>
        </div>
      </div>
    </div>
    <div v-if="data_type === 'chart'">
      <div style="margin: auto; width: 95%;margin-top:5px; margin-bottom: 10px" v-for="(item, index) in data" :key="index">
        <Row type="flex">
          <Col style="width: 20%; text-align: right; max-width: 70px">
            <span style="margin-right: 5px; font-weight: bolder">{{item.key}}</span>
          </Col>
          <Col style="width: 80%; color: #17233d; font-weight: bolder">
            <Progress :percent="Math.round(item.value)" style="width: 100%"/>
          </Col>
        </Row>
      </div>
    </div>
    <div v-if="data_type === 'bool'">
      <div style="width: 100%; text-align: center;" >
        <div style="margin-right: 5px; font-weight: bolder; margin-top: 15px" v-for="(item, index) in data" :key="index">
          <span v-if="item.status"><Icon type="md-checkmark-circle" style="color: #19be6b; font-size: 50px"/></span>
          <span v-if="!item.status"><Icon type="md-close-circle" style="color: #ed4014; font-size: 50px"/></span>
        </div>
      </div>
    </div>
    <div v-if="data_type === 'trend_chart'">
      <slot name="chart"></slot>
    </div>
    <div v-if="needDescLine && desc_mode === 'text'" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" :color="descLineConfig.color">{{descLineConfig.title}}: {{descLineConfig.number}} {{descLineConfig.unit}}</Tag>
    </div>
    <div v-if="needDescLine && desc_mode === 'time' && number_auto_transfer" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) < descLineConfig.target" :color="descLineConfig.color[0]">
        {{descLineConfig.title}}: {{getTimeString(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}}
      </Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) > descLineConfig.target" :color="descLineConfig.color[1]">
        {{descLineConfig.title}}: {{getTimeString(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}}
      </Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) === descLineConfig.target" :color="descLineConfig.color[2]">
        {{descLineConfig.title}}: {{getTimeString(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}}
      </Tag>
    </div>
    <div v-if="needDescLine && desc_mode === 'compare' && !number_auto_transfer" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="descLineConfig.number < descLineConfig.target" :color="descLineConfig.color[0]">
        {{descLineConfig.title}}: {{descLineConfig.number}} {{descLineConfig.unit}}
      </Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="descLineConfig.number > descLineConfig.target" :color="descLineConfig.color[1]">
        {{descLineConfig.title}}: {{descLineConfig.number}} {{descLineConfig.unit}}
      </Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="descLineConfig.number === descLineConfig.target" :color="descLineConfig.color[2]">
        {{descLineConfig.title}}: {{descLineConfig.number}} {{descLineConfig.unit}}
      </Tag>
    </div>
    <div v-if="needDescLine && desc_mode === 'compare' && number_auto_transfer" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(descLineConfig.number) < getNumber(descLineConfig.target) && getLevel(descLineConfig.number) === getLevel(descLineConfig.target) " :color="descLineConfig.color[0]">{{descLineConfig.title}}: {{descLineConfig.number}} {{getUnit(descLineConfig.unit), descLineConfig.unit}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(descLineConfig.number) > getNumber(descLineConfig.target) && getLevel(descLineConfig.number) === getLevel(descLineConfig.target) " :color="descLineConfig.color[1]">{{descLineConfig.title}}: {{descLineConfig.number}} {{getUnit(descLineConfig.unit, descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(descLineConfig.number) === getNumber(descLineConfig.target) && getLevel(descLineConfig.number) === getLevel(descLineConfig.target) " :color="descLineConfig.color[2]">{{descLineConfig.title}}: {{descLineConfig.number}} {{getUnit(descLineConfig.unit, descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getLevel(descLineConfig.number) > getLevel(descLineConfig.target) " :color="descLineConfig.color[1]">{{descLineConfig.title}}: {{descLineConfig.number}} {{getUnit(descLineConfig.unit, descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getLevel(descLineConfig.number) < getLevel(descLineConfig.target) " :color="descLineConfig.color[2]">{{descLineConfig.title}}: {{descLineConfig.number}} {{getUnit(descLineConfig.unit, descLineConfig.unit)}}</Tag>
    </div>
    <div v-if="needDescLine && desc_mode === 'dict' && !number_auto_transfer" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) < descLineConfig.target" :color="descLineConfig.color[0]">{{descLineConfig.title}}: {{getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)}} {{descLineConfig.unit}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) > descLineConfig.target" :color="descLineConfig.color[1]">{{descLineConfig.title}}: {{getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)}} {{descLineConfig.unit}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option) === descLineConfig.target" :color="descLineConfig.color[2]">{{descLineConfig.title}}: {{getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)}} {{descLineConfig.unit}}</Tag>
    </div>
    <div v-if="needDescLine && desc_mode === 'dict' && number_auto_transfer" style="text-align: center">
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) < getNumber(descLineConfig.target) && getLevel(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) === getLevel(descLineConfig.target) " :color="descLineConfig.color[0]">{{descLineConfig.title}}: {{getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}} {{getUnit(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option), descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) > getNumber(descLineConfig.target) && getLevel(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) === getLevel(descLineConfig.target) " :color="descLineConfig.color[1]">{{descLineConfig.title}}: {{getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}} {{getUnit(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option), descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) === getNumber(descLineConfig.target) && getLevel(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) === getLevel(descLineConfig.target) " :color="descLineConfig.color[2]">{{descLineConfig.title}}: {{getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}} {{getUnit(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option), descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getLevel(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) > getLevel(descLineConfig.target) " :color="descLineConfig.color[1]">{{descLineConfig.title}}: {{getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}} {{getUnit(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option), descLineConfig.unit)}}</Tag>
      <Tag style="margin-top: -2px; cursor: initial; font-weight: bolder" v-if="getLevel(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option)) < getLevel(descLineConfig.target) " :color="descLineConfig.color[2]">{{descLineConfig.title}}: {{getNumber(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option))}} {{getUnit(getDictNumber(descLineConfig.obj, descLineConfig.key, descLineConfig.option), descLineConfig.unit)}}</Tag>
    </div>
    <div v-if="!needDescLine" style="height: 22px"></div>
  </div>
</template>

<script>
  export default {
    name: 'deploy-data-component',
    props: {
      height: {
        type: String,
        default: '86px'
      },
      need_right_border: {
        type: Boolean,
        default: false
      },
      title_type: {
        type: String,
        default: 'text'
      },
      title_star: {
        type: Boolean,
        default: false
      },
      title_text: {
        type: String,
        default: ''
      },
      title_data: {
        type: Object,
        default: () => {}
      },
      number_auto_transfer: {
        type: Boolean,
        default: false
      },
      data_type: {
        type: String,
        default: 'text'
      },
      data: {
        type: Array,
        default: () => []
      },
      needDescLine: {
        type: Boolean,
        default: false
      },
      desc_mode: {
        type: String,
        default: 'text'
      },
      descLineConfig: {
        type: Object,
        default: () => {}
      }
    },
    data: function () {
      return {
        style: {},
        timeSecond: 0
      }
    },
    mounted: function () {
      this.style = {
        minHeight: this.height,
        textAlign: 'center',
        marginTop: '5px',
        marginBottom: '5px'
      }
      if (this.need_right_border) {
        this.style.borderRightStyle = 'dotted'
        this.style.borderRightColor = '#dcdee2'
        this.style.borderRightWidth = '1px'
      }
    },
    methods: {
      getUnit: function (target, unit) {
        if (Number(target) > 1000000) {
          return 'M'
        } else {
          if (Number(target) > 1000) {
            return 'K'
          } else {
            return unit
          }
        }
      },
      getNumber: function (target) {
        if (Number(target) > 1000000) {
          return Math.round(target / 1000000)
        } else {
          if (Number(target) > 1000) {
            return Math.round(target / 1000)
          } else {
            return target
          }
        }
      },
      getLevel: function (target) {
        if (Number(target) > 1000000) {
          return 2
        } else {
          if (Number(target) > 1000) {
            return 1
          } else {
            return 0
          }
        }
      },
      getDictNumber: function (obj, key, option) {
        if (Object.keys(obj).length > 0 && Object.keys(obj).indexOf(key) !== -1) {
          if (Object.keys(obj[key]).length && Object.keys(obj[key]).indexOf(option) !== -1) {
            return Number(obj[key][option])
          } else {
            return 0
          }
        } else {
          return 0
        }
      },
      getTimeString: function (timeStamp) {
        let date = new Date(timeStamp * 1000)
        return this.getFormatTime(date)['YMDhms']
      }
    }
  }
</script>

<style>
  div.rateRadar.ivu-rate.ivu-rate-disabled > div {
    margin-right: 2px !important;
  }
  .star{
    margin-top: -3px;
    color: #2db7f5;
  }
</style>
