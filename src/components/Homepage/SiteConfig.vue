<template>
  <div :style="style" style="background-color: #FFFFFF">
    <div style="margin-left: 15px; margin-right: 15px; padding-top: 15px" v-if="haveRootAuth()">
      <Timeline>
        <TimelineItem>
          顶部通知栏
          <Row style="margin-top: 5px" type="flex">
            <ButtonGroup size="small" style="margin-top: 5px; margin-right: 5px;">
              <Button type="primary" @click="saveTopNotice()">保存文案</Button>
            </ButtonGroup>
            <i-switch size="large" v-model="topNoticeEnable" style="margin-top: 6px; margin-right: 5px" @on-change="saveTopNotice()">
              <span slot="open">启用</span>
              <span slot="close">停用</span>
            </i-switch>
            <Input v-model="topNoticeText" style="width:80%">
              <span slot="prepend">通知文案</span>
            </Input>
          </Row>
        </TimelineItem>
      </Timeline>
    </div>
    <div v-else>
      <content-not-auth></content-not-auth>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '../../global/bus'
  import ContentNotAuth from '../Common/contentNotAuth'
  export default {
    name: 'SiteConfig',
    components: {ContentNotAuth},
    data: function () {
      return {
        topNoticeText: '',
        topNoticeEnable: false,
        topNoticeId: -1,
        mis: Bus.userInfo.userLogin,
        style: {}
      }
    },
    methods: {
      getTopNotice () {
        let self = this
        let url = this.getDomain('cq') + '/portal/config/query'
        axios.post(url, JSON.stringify({
          type: 'top_notice'
        })).then(function (message) {
          if (message.data.status === 'success') {
            let data = message.data.data
            if (data.length > 0) {
              self.topNoticeText = data[0].config.data
              self.topNoticeEnable = data[0].config.enable
              self.topNoticeId = data[0].id
            } else {
              self.topNoticeId = -1
              self.topNoticeEnable = false
              self.topNoticeText = ''
            }
          }
        })
      },
      saveTopNotice: function () {
        let self = this
        let url = this.getDomain('cq') + '/portal/config/add'
        let payload = ''
        if (this.topNoticeId !== -1) {
          payload = {
            type: 'top_notice',
            config: {
              enable: this.topNoticeEnable,
              data: this.topNoticeText
            },
            id: this.topNoticeId,
            editor: this.mis
          }
        } else {
          payload = {
            type: 'top_notice',
            config: {
              enable: this.topNoticeEnable,
              data: this.topNoticeText
            },
            editor: this.mis
          }
        }
        axios.post(url, JSON.stringify(payload)).then(function (message) {
          if (message.data.status === 'success') {
            self.getTopNotice()
            self.$Message.success('修改成功')
          } else {
            self.$Modal.error({
              title: '修改失败',
              content: '<P>' + message.data.reason.toString() + '</P>'
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '操作失败',
            content: '<P>未知错误</P>'
          })
        })
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    },
    mounted: function () {
      this.getTopNotice()
      this.style = {
        'minHeight': this.getToolchainScreeHeight('home')
      }
    }
  }
</script>

<style scoped>

</style>
