<template>
  <div style="min-width: 100%; max-width: 100%; background-color: #FFFFFF">
    <Modal v-model="documentModal" title="新增文档" @on-ok="addDocument">
      <Input style="margin-top: 10px;min-width: 100%;max-height: 300px;" v-model="documentTitle" placeholder="请输入文档标题">
      <span slot="prepend">文档标题</span>
      </Input>
      <Input style="margin-top: 10px;min-width: 100%;max-height: 300px;" v-model="documentUrl" placeholder="请输入文档链接">
      <span slot="prepend">文档链接</span>
      </Input>
    </Modal>
    <Row type="flex" :style="{paddingTop:'0',minHeight:'120px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div slot="title" :style="{fontWeight:'bolder'}">
          文档中心
          <Button icon="md-add" type="dashed" size="small" class="tool-button" @click="showDocumentModal">新增</Button>
        </div>
        <Row v-if="documentList.length !== 0">
          <Col span="4" v-for="item in documentList" :key="item.name" style="min-width: 197px; text-align: center">
          <div style="margin-bottom: 15px; margin-left: 35px">
            <a :href="item.url" target="_blank"
               style="font-weight: bolder;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">{{item.name}}</a>
          </div>
          </Col>
        </Row>
        <Row type="flex" v-else>
          <span style="font-weight: bolder">暂未查询到文档信息</span>
        </Row>
      </Card>
      </Col>
    </Row>
    <Row type="flex" :style="{paddingTop:'5px',minHeight:'240px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div slot="title" :style="{fontWeight:'bolder'}">
          服务端
        </div>
        <Row>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/custom/Analytics" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_12.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">工具组</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">自定义过程度量</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/json-editor" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">工具组</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">Json在线编辑器</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/json-2-json-schema" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_7.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">工具组</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">JSON Schema生成</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/report" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_6.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">工具组</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">报告生成服务</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/push-service" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_9.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">工具组</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">推送服务</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
            <Card style="width:100%" :bordered="false" dis-hover>
              <router-link to="/goods-data" tag="div">
                <div style="text-align:center;cursor: pointer">
                  <img src="../../../static/atlassian_10.png" style="height: 80px; width: 60%; text-align: center;">
                  <div>
                    <Tag color="primary">住宿</Tag>
                    <span style="padding-top: 15px;font-weight: bolder">数据生成工具</span>
                  </div>
                </div>
              </router-link>
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/data-synchronization" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_11.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">住宿</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">数据同步服务</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
           <Card style="width:100%" :bordered="false" dis-hover >
            <router-link to="/testenv-management" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_2.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">住宿</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">测试环境管理</span>
                </div>
              </div>
            </router-link>
           </Card>
         </Col>
         <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover >
           <router-link to="/security-check" tag="div">
            <div style="text-align:center;cursor: pointer">
              <img src="../../../static/atlassian_13.png" style="height: 80px; width: 60%; text-align: center;">
              <div>
                <Tag color="primary">住宿</Tag>
                 <span style="padding-top: 15px;font-weight: bolder">安全检查</span>
              </div>
            </div>
           </router-link>
          </Card>
         </Col>
        <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover >
            <a :href="'http://qatool.travel.test.sankuai.com'" target="_blank" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_14.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">CRM</Tag>
                  <span style="padding-top: 15px;font-weight: bolder;color: #515a6e">质量保障平台</span>
                </div>
              </div>
            </a>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
          <Card style="width:100%" :bordered="false" dis-hover >
            <a :href="'http://qa.nibmsc.test.sankuai.com'" target="_blank" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_8.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">结算</Tag>
                  <span style="padding-top: 15px;font-weight: bolder;color: #515a6e">质量保障平台</span>
                </div>
              </div>
            </a>
          </Card>
          </Col>
          <Col span="4" style="min-width: 196px">
            <Card :bordered="false" dis-hover style="width:100%" >
              <router-link tag="div" to="/code-analysis">
                <div style="text-align:center;cursor: pointer">
                  <img src="../../../static/atlassian_11.png" style="height: 80px; width: 60%; text-align: center;">
                  <div>
                    <Tag color="primary">住宿</Tag>
                    <span style="padding-top: 15px;font-weight: bolder">代码变更分析</span>
                  </div>
                </div>
              </router-link>
            </Card>
          </Col>
        </Row>
      </Card>
      </Col>
    </Row>
    <Row type="flex" :style="{paddingTop:'5px',minHeight:'240px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div slot="title" :style="{fontWeight:'bolder'}">
          客户端
        </div>
        <Row>
          <Col span="4" style="min-width: 197px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/machine-manage" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_3.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">外链</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">测试设备管理平台</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 197px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/analytics/client" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_5.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">客户端</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">客户端过程度量</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
          <Col span="4" style="min-width: 197px">
          <Card style="width:100%" :bordered="false" dis-hover>
            <router-link to="/homepage/cd/client" tag="div">
              <div style="text-align:center;cursor: pointer">
                <img src="../../../static/atlassian_4.png" style="height: 80px; width: 60%; text-align: center;">
                <div>
                  <Tag color="primary">客户端</Tag>
                  <span style="padding-top: 15px;font-weight: bolder">客户端质量监控平台</span>
                </div>
              </div>
            </router-link>
          </Card>
          </Col>
        </Row>
      </Card>
      </Col>
    </Row>
    <Row type="flex" :style="{paddingTop:'0',minHeight:'120px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div slot="title" :style="{fontWeight:'bolder'}">
          小工具
        </div>
        <Timeline style="padding-top: 15px;padding-left: 2px">
          <TimelineItem>
            <div class="time">编排复制小工具</div>
            <div style="display: flex;padding-top: 15px">
              <div>
                <Input v-model="stackCopyOldStack" placeholder="e.g.eae3decf-7ca0-485e-9c19-9568063e55a6" clearable
                       style="width: 500px;">
                <span slot="prepend">编排ID</span>
                </Input>
              </div>
              <div style="margin-left: 15px">
                <Input v-model="stackCopyName" placeholder="请输入编排名称" clearable style="width: 500px;">
                <span slot="prepend">编排名称</span>
                </Input>
              </div>
              <div>
                <Button type="primary" style="margin-left: 15px" @click="copyStack">开始复制</Button>
              </div>
            </div>
            <div style="font-weight: bolder;padding-top:15px">
              <Tag color="primary">初始化部署</Tag>
              <i-switch v-model="initDeploy" size="large" style="margin-left: 5px">
                <span slot="open">开启</span>
                <span slot="close">关闭</span>
              </i-switch>
            </div>
          </TimelineItem>
          <TimelineItem>
            <div class="time">Moka数据同步小工具</div>
            <p style="color: #ffa500;margin-top: 10px;font-size: 12px"><i class="fa fa-question-circle"
                                                                          aria-hidden="true"></i>请确认数据源编排和目标编排均处于正常运行状态，否则数据同步会失败
            </p>
            <div style="display: flex;margin-top: 0">
              <Input v-model="mockCopyOldStack" placeholder="e.g.eae3decf-7ca0-485e-9c19-9568063e55a6" clearable>
              <span slot="prepend">数据源编排ID</span>
              </Input>
              <Button type="primary" style="margin-left: 15px" shape="circle" @click="changeStackId">
                <Icon type="md-swap" style="font-size: 18px"></Icon>
              </Button>
              <Input v-model="mockCopyNewStack" placeholder="e.g.eae3decf-7ca0-485e-9c19-9568063e55a6"
                     style="margin-left: 15px" clearable>
              <span slot="prepend">目标编排ID</span>
              </Input>
              <Button type="primary" style="margin-left: 15px" @click="copyMockData">数据同步</Button>
            </div>
          </TimelineItem>
        </Timeline>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  import {Bus} from '@/global/bus'

  export default {
    name: 'homepage-openLab',
    data: function () {
      return {
        documentList: [],
        documentModal: false,
        documentTitle: '',
        documentUrl: '',
        stackCopyOldStack: '',
        initDeploy: true,
        mockCopyOldStack: '',
        mockCopyNewStack: '',
        stackCopyName: ''
      }
    },
    methods: {
      getDocumentList: function () {
        let self = this
        self.showRefreshCiMetrics = false
        let url = this.getDomain('cq') + '/document/search'
        axios.get(url).then(function (message) {
          if (message.data.status) {
            self.documentList = []
            let data = message.data.data
            for (let unit of data) {
              self.documentList.push(unit)
            }
          }
        })
      },
      addDocument: function () {
        let self = this
        let url = this.getDomain('cq') + '/document/add'
        if (self.documentUrl && self.documentTitle) {
          self.$Spin.show()
          axios.post(url, JSON.stringify({
            'url': self.documentUrl,
            'name': self.documentTitle,
            'editor': Bus.userInfo.userLogin
          })).then(function (messege) {
            if (messege.data.status) {
              self.$Message.success('新建成功')
              self.$Spin.hide()
              self.documentTitle = ''
              self.documentUrl = ''
              self.getDocumentList()
            } else {
              self.$Modal.error({
                title: '新建失败',
                content: '<P>' + messege.data.reason.toString() + '</P>'
              })
              self.documentModal = true
            }
          }).catch(function () {
            self.$Spin.hide()
            self.$Message.error('新建失败！')
            self.$Modal.error({
              title: '新建失败',
              content: '<P>服务器内部错误</P>'
            })
          })
        } else {
          self.$Message.info('存在必填参数未填写')
        }
      },
      showDocumentModal: function () {
        this.documentModal = true
      },
      changeStackId: function () {
        this.mockCopyOldStack = [this.mockCopyNewStack, this.mockCopyNewStack = this.mockCopyOldStack][0]
      },
      copyStack: function () {
        let self = this
        if (this.stackCopyOldStack) {
          let payload = {
            'stackId': this.stackCopyOldStack,
            'name': this.stackCopyName,
            'autoDeploy': this.initDeploy
          }
          self.$Modal.info({
            title: '编排复制',
            content: '<p>开始进行编排复制</P>'
          })
          axios.post(this.getDomain('cq') + '/stack/copyStack', JSON.stringify(payload)).then(function (message) {
            if (message.data.status === 'success') {
              self.stackCopyOldStack = ''
              self.$Message.success('复制成功！')
              self.$Modal.success({
                title: '复制成功',
                content: '<P>新的泳道编排ID为：<a target="_blank" href="http://cargo.sankuai.com/stack/detail?stack_uuid=' + message.data.uuid + '">http://cargo.sankuai.com/stack/detail?stack_uuid=' + message.data.uuid + '</a>' + '</P>'
              })
            } else {
              self.$Message.error('复制失败！')
              self.$Modal.error({
                title: '复制失败',
                content: '<P>请检查输入的泳道编排ID是否正确！</P>'
              })
            }
          }).catch(function () {
            self.$Modal.error({
              title: '复制失败',
              content: '<P>服务器内部错误</P>'
            })
          })
        } else {
          self.$Message.info('请输入泳道编排ID！')
        }
      },
      copyMockData: function () {
        let self = this
        if (this.mockCopyOldStack && this.mockCopyNewStack) {
          let payload = {
            'oldStackId': this.mockCopyOldStack,
            'newStackId': this.mockCopyNewStack
          }
          self.$Modal.info({
            title: 'Moka数据同步',
            content: '<p>开始进行Moka数据同步</P>'
          })
          axios.post(this.getDomain('cq') + '/stack/copyMockData', JSON.stringify(payload)).then(function (message) {
            if (message.data.status) {
              self.mockCopyOldStack = ''
              self.mockCopyNewStack = ''
              self.$Message.success('复制成功！')
            } else {
              self.$Message.error('复制失败！')
              self.$Modal.error({
                title: '复制失败',
                content: '<P>失败原因:' + message.data.info + '</P>'
              })
            }
          }).catch(function () {
            self.$Modal.error({
              title: '复制失败',
              content: '<P>服务器内部错误</P>'
            })
          })
        } else {
          self.$Message.info('请输入泳道编排ID！')
        }
      }
    },
    mounted: function () {
      this.getDocumentList()
    }
  }
</script>

<style scoped>
  .tool-button {
    margin-left: 5px;
    color: #2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }

  .tool-button:hover {
    margin-left: 5px;
    color: #1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }
</style>
