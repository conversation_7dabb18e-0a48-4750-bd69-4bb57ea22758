<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/siteConfig">站点管理</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="tab">
      <SiteConfig></SiteConfig>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import SiteConfig from './SiteConfig'
  import CommonFooter from '../Common/Footer'
  export default {
    name: 'SiteConfigHomepage',
    components: {CommonFooter, SiteConfig},
    data: function () {
      return {
        tab: 'siteConfig',
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    }
  }
</script>

<style scoped>
  .tab {
    margin-top: 15px;
    margin-left: 1.5%;
    margin-right: 1.5%;
    width: auto
  }
</style>
