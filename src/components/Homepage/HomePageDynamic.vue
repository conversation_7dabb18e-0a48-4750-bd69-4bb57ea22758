<template>
  <div style="background-color: #f5f7f9">
    <head-component></head-component>
    <div style=" margin-left: 15px; margin-right: 15px; margin-top: 15px">
      <Breadcrumb style="margin-top: 15px; margin-left: 15px">
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
      </Breadcrumb>
      <div :style=style style="margin-top: 15px">
        <Row style="display: inline-flex; width: 100%">
          <Col style="width: 34%;">
          <div class="toolchain-content-homepage" style="margin-top: -15px; margin-left: 15px; margin-right: 7px">
            <h4 class="title" style="margin-left: 15px; margin-bottom: 0">过程度量项目管理页</h4>
            <Row style="margin-left: 15px; margin-right: 15px; width:-webkit-fill-available; display: inline-flex">
              <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" :transfer="false" style="width: -webkit-fill-available" filterable change-on-select @on-change="getCurrentChooseDirection"></Cascader>
              <Button v-if="direction.length !== 0" type="primary" style="margin-left: 10px;" @click="jumpToCurrentDirection()" icon="md-arrow-round-forward"> 跳转</Button>
            </Row>
            <div style="margin-left: 15px; margin-right: 15px; width:-webkit-fill-available; margin-top: 10px">
              <Tag color="blue" style="cursor: default">过程度量项目配置页</Tag>
              <Tag color="blue" style="cursor: default">非标准模版接入页</Tag>
            </div>
          </div>
          </Col>
        </Row>
        <Row type="flex" style="margin: 0 0 20px 14px;">
          <!--<Col span="3">-->
          <!--<menu-card title="快速接入" url="/access/fast" color="#fabc16" icon="ivu-icon ivu-icon-md-switch"></menu-card>-->
          <!--</Col>-->
          <Col span="3">
          <menu-card title="过程度量大盘" url="/homepage/analytics/ones/analyticshome" color="rgb(149, 222, 100)" icon="ivu-icon ivu-icon-md-podium"></menu-card>
          </Col>
<!--          <Col span="3">-->
<!--          <menu-card title="测试环境大盘" url="/homepage/dashboard" color="rgb(149, 222, 100)" icon="ivu-icon ivu-icon-md-podium"></menu-card>-->
<!--          </Col>-->
          <Col span="3">
          <menu-card title="Raptor主干监控大盘" url="/homepage/raptorMonitor" color="rgb(149, 222, 100)" icon="ivu-icon ivu-icon-md-podium"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="MCD" url="http://cd.sankuai.com" color="#fabc16" icon="ivu-icon ivu-icon-md-pricetags"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="TestX" url="http://testx.sankuai.com" color="#fabc16" icon="ivu-icon ivu-icon-md-pricetags"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="MDO" url="http://mdo.sankuai.com" color="#fabc16" icon="ivu-icon ivu-icon-md-pricetags"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="MMCD" url="/client" color="rgb(255, 156, 110)" icon="ivu-icon ivu-icon-md-mail"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="开放平台" url="/homepage/open" color="rgb(255, 156, 110)" icon="ivu-icon ivu-icon-md-mail"></menu-card>
          </Col>
          <Col span="3">
          <menu-card title="站点设置" url="/homepage/siteConfig" color="#fabc16" icon="ivu-icon ivu-icon-md-switch"></menu-card>
          </Col>
        </Row>
        <!--<my-star :style=style style="margin-top: 0"></my-star>-->
        <my-rss :style=style></my-rss>
      </div>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
import axios from 'axios'
import { Bus } from '../../global/bus'
import CommonFooter from '../Common/Footer'
// import MyStar from '../Rss/MyStar'
import MenuCard from './MenuCard'
import MyRss from '../Rss/MyRss'
export default {
  name: 'home-dynamic',
  components: {
    MyRss,
    CommonFooter,
    MenuCard
  },
  data: function () {
    return {
      mis: Bus.userInfo.userLogin,
      style: {},
      repo: '',
      haveAuth: false,
      direction: [],
      directionList: [],
      currentChooseDirection: {},
      commonKeyList: [],
      commonKey: 2
    }
  },
  methods: {
    jumperToRepoCq: function () {
      let repo = this.getRepoSSHAddressUtil(this.repo)
      if (repo.length > 0) {
        let sshInfo = this.getRepoInfoFromSSHAddr(repo)
        this.$router.push(
          '/cq/' +
            sshInfo.server +
            '/project/' +
            sshInfo.project.toUpperCase() +
            '/repo/' +
            sshInfo.name
        )
      } else {
        this.$Notice.info({
          title: '错误',
          desc: '无法解析填写的链接，请检查链接的正确性'
        })
      }
    },
    getCurrentChooseDirection: function (value, selectedData) {
      let item = selectedData[selectedData.length - 1]
      this.currentChooseDirection = {
        key: item.direction_id.toString(),
        label: item.label,
        value: item.value
      }
    },
    getDirections: function () {
      let self = this
      axios(
        this.getDomain('config') + '/mcd/org/basic?direction_id=1&disable=1'
      )
        .then(function (message) {
          if (message.data.result) {
            self.directionList.length = 0
            self.directionList.push(message.data.info)
          }
        })
        .catch(function () {
          self.directionList.length = 0
        })
    },
    getCommonKeyList: function () {
      let self = this
      axios
        .get(
          this.getDomain('config') + '/mcd/org/common_project_id?direction_id=1'
        )
        .then(function (message) {
          if (message.data.result) {
            self.commonKeyList.length = 0
            self.commonKeyList = message.data.info.common_project_id
          }
        })
        .catch(function () {
          self.commonKeyList.length = 0
        })
    },
    jumpToCurrentDirection: function () {
      if (Object.keys(this.currentChooseDirection).indexOf('key') !== -1) {
        let key = this.currentChooseDirection.key
        this.$router.push('/direction/detail/config/' + key.toString())
      }
    },
    jumpToCurrentCommonKey: function () {
      if (this.commonKey !== 2) {
        this.$router.push(
          '/direction/pipeline/tice/' + this.commonKey.toString()
        )
      }
    }
  },
  mounted: function () {
    this.style = {
      minHeight: this.getToolchainScreeHeight('home')
    }
    this.isHaveRootAuth(this, this.mis)
    this.getDirections()
    this.getCommonKeyList()
  }
}
</script>

<style>
.toolchain-content-homepage {
  background-color: #ffffff;
  margin: 10px 15px 20px 15px;
  padding: 15px 10px 20px 10px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.title {
  font-weight: bolder;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.toolchain-step-content {
  margin-top: 5px;
}

.tool-button {
  margin-left: 5px;
  color: #2d8cf0;
  border-color: #2d8cf0;
  font-weight: bolder;
}

.button {
  margin-bottom: 5px;
  margin-right: 3px;
  font-weight: bolder;
  font-size: 13px;
  height: 30px;
  padding: 4px 8px !important;
}

.button-gold {
  border-color: #fa8c16;
  background-color: #fff7e6;
  color: #fa8c16;
}

.button-gold:hover {
  border-color: #fa8c16;
  background-color: #fff7e6;
  color: #fa8c16;
  opacity: 0.85;
}

.button-blue {
  border-color: #1890ff;
  background-color: #e6f7ff;
  color: #1890ff;
}

.button-blue:hover {
  border-color: #1890ff;
  background-color: #e6f7ff;
  color: #1890ff;
  opacity: 0.85;
}

.button-green {
  border-color: #52c41a;
  background-color: #f6ffed;
  color: #52c41a;
}

.button-green:hover {
  border-color: #52c41a;
  background-color: #f6ffed;
  color: #52c41a;
  opacity: 0.85;
}
</style>
