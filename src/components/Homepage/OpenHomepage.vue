<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/open">开放平台</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="tab">
      <HomepageOpenLab></HomepageOpenLab>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import HomepageOpenLab from './Open'
  import CommonFooter from '../Common/Footer'
  export default {
    name: 'OpenHomepage',
    components: {CommonFooter, HomepageOpenLab},
    data: function () {
      return {
        tab: 'open',
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    }
  }
</script>
git
<style scoped>
  .tab {
    margin-left: 1.5%;
    margin-top: 15px;
    margin-right: 1.5%;
    margin-bottom: 10px;
    width: auto
  }
</style>
