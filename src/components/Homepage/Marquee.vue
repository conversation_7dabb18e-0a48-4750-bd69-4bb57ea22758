<template>
  <div style="height: 720px">
    <Carousel autoplay v-model="value2" :autoplay-speed="5000" :style="{width:'99%',cursor: 'pointer'}">
      <CarouselItem>
        <div class="banner" style="cursor: hand">
          <div class="jumbotron style_2 masthead"  style="height: 700px">
            <div class="container" style="padding-top: 150px">
              <h1>质量保障平台</h1>
              <h2>首页订阅内容正在规划中，敬请期待！</h2>
            </div>
          </div>
        </div>
      </CarouselItem>
    </Carousel>
  </div>
</template>
<script>
  export default {
    data () {
      return {
        value2: 0
      }
    }
  }
</script>

<style scoped>
  .jumbotron {
    position: relative;
    padding: 100px 0;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0,0,0,.4), 0 0 30px rgba(0,0,0,.075);
  }

  .style_3{
    background: #37425e;
    background: -webkit-gradient(linear,left bottom,right top,color-stop(0,#37425e),color-stop(100%,#445275));
    background: -webkit-linear-gradient(45deg,#37425e 0,#445275 100%);
    background: -o-linear-gradient(45deg,#37425e 0,#445275 100%);
    background: linear-gradient(45deg,#37425e 0,#445275 100%);
    -webkit-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
    box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
  }

  .style_2{
    background: #020031;
    background: -webkit-gradient(linear,left bottom,right top,color-stop(0,#020031),color-stop(100%,#6d3353));
    background: -webkit-linear-gradient(45deg,#020031 0,#6d3353 100%);
    background: -o-linear-gradient(45deg,#020031 0,#6d3353 100%);
    background: linear-gradient(45deg,#020031 0,#6d3353 100%);
    -webkit-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
    box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
  }

  .style_1{
    background: #2f2742;
    background: -webkit-gradient(linear,left bottom,right top,color-stop(0,#2f2742),color-stop(100%,#37425e));
    background: -webkit-linear-gradient(45deg,#2f2742 0,#37425e 100%);
    background: -o-linear-gradient(45deg,#2f2742 0,#37425e 100%);
    background: linear-gradient(45deg,#2f2742 0,#37425e 100%);
    -webkit-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
    box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
  }
</style>
