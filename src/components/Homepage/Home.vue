<template>
  <div id="homepage_tab" style="background-color: #f0f0f0">
    <head-component></head-component>
    <div>
      <Row style="height: 430px; background-color: #ffffff; color:#17233d">
        <Col offset="6" span="8" style="font-weight: bolder;margin-top: 120px">
          <div style="font-weight: bolder; font-size: 45px">质量保障平台(MQP)</div>
          <div style="font-size: 20px">我们致力于为RD、QA、PM提供一站式的质量保障工具和解决方案</div>
          <Row style="margin-top: 15px">
            <Button target="_self" to="/homepage/analytics/ones/analyticshome" style="margin-top: 5px" type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px"/>过程度量大盘</Button>
<!--            <Button target="_self" to="/homepage/dashboard" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />测试环境大盘</Button>-->
            <Button target="_self" to="/homepage/raptorMonitor" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />Raptor主干监控大盘</Button>
            <Button target="_self" to="/homepage/cd" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />持续交付</Button>
            <Button target="_self" to="/client" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />CQP</Button>
            <Button target="_self" to="/homepage/open" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />开放平台</Button>
            <Button v-if="haveRootAuth()" target="_self" to="/homepage/siteConfig" style="margin-top: 5px"  type="primary" size="small"><Icon type="md-arrow-round-forward" style="padding-right: 5px" />站点设置</Button>
          </Row>
        </Col>
        <Col span="4" style="margin-top: 120px; margin-left: 50px">
          <img src="../../../static/qa.png" style="height: 200px; width: 80%; text-align: center;">
        </Col>
      </Row>
      <Row style="width: 100%; font-size: 30px; margin-top: 15px; margin-bottom: 15px">
        <Col style="text-align: center">
          <div>
            <span :style="{fontWeight:'bolder'}">解决方案</span>
          </div>
        </Col>
      </Row>
      <Row style="margin-left: 5%; margin-right: 5%; color:#17233d">
        <Col span="8" style="text-align: center">
          <Card style="margin-left: 2%; margin-right: 2%; height: 320px;">
            <div>
              <img src="../../../static/atlassian_12.png" style="height: 200px; width: 60%; text-align: center;margin-top: 25px">
              <h4 style="margin-top: 25px">持续交付</h4>
            </div>
          </Card>
        </Col>
        <Col span="8" style="text-align: center">
          <Card style="margin-left: 2%; margin-right: 2%; height: 320px;">
            <div>
              <img src="../../../static/atlassian_13.png" style="height: 200px; width: 55%; text-align: center;margin-top: 25px">
              <h4 style="margin-top: 25px">Code Quality</h4>
            </div>
          </Card>
        </Col>
        <Col span="8" style="text-align: center">
          <Card style="margin-left: 2%; margin-right: 2%; height: 320px;">
            <div>
              <img src="../../../static/atlassian_14.png" style="height: 200px; width: 55%; text-align: center;margin-top: 25px">
              <h4 style="margin-top: 25px">敏捷开发</h4>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import CommonFooter from '../Common/Footer'
  export default {
    components: {CommonFooter},
    data () {
      return {
        tab: 'homepage',
        style: {
          height: 'auto'
        },
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
</style>
