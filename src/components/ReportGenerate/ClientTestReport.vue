<template>
  <div>
    <head-component id="head"></head-component>
    <Tabs value="client_test_report" style='margin-top: 30px;margin-left: 5%;margin-right: 5%;width: auto;'>
      <TabPane label="测试报告" name="test_report">
        <test-report-unit></test-report-unit>
      </TabPane>
      <TabPane label="客户端版本报告" name="client_version_report">
        <client-version-report-unit></client-version-report-unit>
      </TabPane>
      <TabPane label="服务端质量报告" name="server_report">
        <server-report-unit></server-report-unit>
      </TabPane>
      <TabPane label="客户端测试报告" name="client_test_report">
        <client-test-report-unit></client-test-report-unit>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import ClientTestReportUnit from '@/components/ReportGenerate/ClientTestReportUnit'
  export default {
    name: 'test-report-page',
    data: function () {
      return {}
    }
  }
  Vue.component('head-component', Head)
  Vue.component('client-test-report-unit', ClientTestReportUnit)
</script>

<style scoped>
</style>
