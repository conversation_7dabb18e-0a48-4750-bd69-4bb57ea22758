<template>
  <div>
    <Col span="24" order="1">
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">段陈辰（duanchenchen）</span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a
          href="https://km.sankuai.com/page/235755234" target="_blank">部署时长报告FAQ</a></span></p>
      </Card>
      <Timeline style="padding-top: 15px">
        <TimelineItem>
          <p class="time">方向:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" filterable :transfer="false"
                        style="width: 50%" change-on-select @on-change="getCurrentChooseDirection"></Cascader>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">报告起止时间（最多可选一个月）:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="20" style="width: 98.8%">
            <Col span="30" order="1">
              <DatePicker style="width: 200px;" :value="dateRange" type="daterange"
                          :clearable=false placement="bottom-end" placeholder="请选择时间间隔" @on-change="filterByTime"
                          transfer></DatePicker>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">是否生成wiki:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-select placeholder="请选择是否生成wiki" v-model="isWiki">
                <i-option value="1">是</i-option>
                <i-option value="0">否</i-option>
              </i-select>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem v-if="isWiki =='1' ">
          <p class="time">部署时长报告父页面ID:<span style="color: #ff0000;">*</span></p>
          <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 235650717
            for https://km.sankuai.com/page/235650717</p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-input v-model.trim="parent_id" placeholder="部署时长报告parent ID"></i-input>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">是否发送邮件:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-select placeholder="请选择是否发送邮件" v-model="isEmail">
                <i-option value="1">是</i-option>
                <i-option value="0">否</i-option>
              </i-select>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem v-if="isEmail =='1' ">
          <p class="time">请填写收件人邮箱,多收件人以","分隔:</p>
          <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i><EMAIL>
          </p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-input v-model.trim="mailto" placeholder="请填写收件人邮箱"></i-input>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">报表数据范围:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-select placeholder="percentile" v-model="amount">
                <i-option value="3">TOP3</i-option>
                <i-option value="10">TOP10</i-option>
                <i-option value="20">TOP20</i-option>
                <i-option value="100">所有数据</i-option>
              </i-select>
            </Col>
          </Row>
        </TimelineItem>
        <TimelineItem>
          <p class="time">数据统计百分位:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <i-select placeholder="percentile" v-model="percentile">
                <i-option value="90%">90百分位</i-option>
                <i-option value="80%">80百分位</i-option>
                <i-option value="50%">50百分位</i-option>
              </i-select>
            </Col>
          </Row>
        </TimelineItem>
      </Timeline>
      <Row type="flex" :gutter="2" justify="center">
        <Col span="2" order="1">
          <i-button type="primary" @click="submit()">生成部署时长报告</i-button>
        </Col>
      </Row>
    </Col>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    name: 'deploy-report-unit',
    components: {},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 7)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      return {
        style: {},
        directionList: [],
        currentChooseDirection: -1,
        direction: [],
        business: '',
        isWiki: '',
        isEmail: '',
        isHtml: '0',
        mailto: '',
        percentile: '',
        fmt: 'custom',
        parent_id: '235650717',
        current: '-1',
        amount: '',
        dateRange: dateRange,
        start: start,
        end: end
      }
    },
    methods: {
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      submit: function () {
        let self = this
        if (self.business && self.isWiki && self.isEmail && self.percentile && self.fmt && self.amount) {
          self.run()
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      },
      run: function () {
        this.url = this.getDomain('env-monitor') + '/api/report/deploy?business=' + this.business + '' +
          '&is_wiki=' + this.isWiki + '&is_email=' + this.isEmail + '&is_html=' + this.isHtml + '&mailto=' + this.mailto + '' +
          '&percentile=' + this.percentile + '&fmt=' + this.fmt + '&parent_id=' + this.parent_id + '' +
          '&current=' + this.current + '&amount=' + this.amount + '&start=' + this.start + '&end=' + this.end
        if (this.isHtml === '1') {
          this.$Spin.show()
          window.location.href = this.url
          return
        }
        let self = this
        self.$Spin.show()
        axios.get(this.url).then(function (message) {
          try {
            self.$Spin.hide()
            console.log(message)
            if (message.data.message) {
              self.$Modal.success({
                title: '返回结果',
                content: message.data.message
              })
            } else {
              self.$Spin.hide()
              self.$Message.error('服务错误')
            }
          } catch (e) {
            self.$Spin.hide()
            self.$Message.error('服务错误')
          }
        }).catch(function (error) {
          // 由网络或者服务器抛出的错误
          self.$Spin.hide()
          self.$Message.error(error.toString())
        })
      },
      getDirections: function () {
        let self = this
        axios(this.getDomain('config') + '/mcd/org/basic?direction_id=1&disable=1').then(function (message) {
          if (message.data.result) {
            self.directionList.length = 0
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      getCurrentChooseDirection: function (value, selectedData) {
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = {
          key: item.direction_id.toString(),
          label: item.label,
          value: item.value
        }
        this.business = item.direction_id.toString()
      }
    },
    mounted: function () {
      this.style = {
        'minHeight': this.getToolchainScreeHeight()
      }
      this.getDirections()
    }
  }
</script>


<style scoped>
  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
