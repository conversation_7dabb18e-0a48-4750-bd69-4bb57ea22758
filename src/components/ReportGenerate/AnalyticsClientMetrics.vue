<template>
  <div>
    <head-component id="head"></head-component>

    <div>
      <Card class="title" style="padding-top: 0px" aria-hidden="true">
        <Row>
          <Col order="1" span="10" style="display:flex">
          <div style="padding-top: 6px">
            <Tooltip placement="bottom" content="点击查看指标说明及统计规则">
              <!--<Button style="color: red;" @click="link">数据说明</Button>-->
              <!--<Icon type="ios-help-empty" style="padding-left:13px;padding-top: 4px;size: 50px;font-size: larger;font-weight: bolder" color="red"></Icon>-->
              <Icon type="help-circled" style="padding-left:13px;padding-top: 4px;font-size: larger;font-weight: bolder" color="red"></Icon>
              <a href="https://123.sankuai.com/km/page/34689019" target="_blank">数据说明</a>
            </Tooltip>
          </div>
          <h4 style="padding-left: 100px">平台</h4>
          <Select :transfer=true style="margin-left:15px;padding-top: 4px;max-width: 150px;min-width: 150px;text-align: left" v-model="value1" :label-in-value="true" @on-change="v=>{ setOption(v)}">
            <Option v-for="item in platformList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          </Col>
          <Col order="2" span="10" style="text-align: right;display:flex">
          <h4 style="width: 100px">版本</h4>
          <Select :transfer=true multiple style="margin-left:15px;padding-top: 4px;max-width: 400px;min-width: 150px;text-align: left" v-model="value2" :label-in-value="true">
            <Option v-for="item in versionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <div style="margin-left:5px;padding-top: 4px">
            <Button type="primary" @click='postData(value1,value2)'>查看</Button>
          </div>
          </Col>
        </Row>
      </Card>
    </div>
    <div v-show="display3" style="padding-right: 25px;padding-left: 25px">
      <Tabs>
        <TabPane label="项目消耗">
          <client-resource-unit></client-resource-unit>
        </TabPane>
        <TabPane label="如期交付">
          <client-delivery-unit></client-delivery-unit>
        </TabPane>
        <TabPane label="流程效果">
          <client-process-unit></client-process-unit>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Vue from 'vue'
import ClientDeliveryUnit from './ClientDeliveryUnit'
import ClientProcessUnit from './ClientProcessUnit'
import ClientResourceUnit from './ClientResourceUnit'
import Head from '@/components/Common/Head'
import { BusClient } from '@/global/bus'
import { analyticsbaseAPI } from '@/global/variable'
// import router from '@/router'
import Layout from 'view-design/src/components/layout/layout'
Vue.component('head-component', Head)
Vue.component('client-process-unit', ClientProcessUnit)
Vue.component('client-resource-unit', ClientResourceUnit)
Vue.component('client-delivery-unit', ClientDeliveryUnit)

BusClient.$on('showClientMetricsTab', function () {
  BusClient.processMetricsObject.display3 = true
  BusClient.processMetricsObject.display5 = true
})
BusClient.$on('refreshDistribution', function () {
  BusClient.processMetricsObject.distribution = true
})
export default {
  components: { Layout },
  name: 'analyticsClient',
  data: function () {
    return {
      modal1: false,
      pattern: '',
      modal6: false,
      display2: false,
      display3: false,
      display5: false,
      distribution: false,
      value1: '',
      value2: '',
      platformList: [],
      versionList: [],
      versionMap: {},
      length: 0
    }
  },

  methods: {
    setOption(value) {
      this.versionList = []
      for (let index in this.versionMap[value.value]) {
        let version = {}
        version['value'] = this.versionMap[value.value][index]
        version['label'] = this.versionMap[value.value][index]
        this.versionList.push(version)
      }
    },
    getVersionData(self) {
      console.log('function', self)
      axios
        .get(analyticsbaseAPI + '/common/client/version', {
          timeout: 10000,
          dataType: 'json'
        })
        .then(function (message) {
          if (message['data']['status'] === 0) {
            self.versionMap = message['data']['data']

            for (let platformName in self.versionMap) {
              let platform = {}
              platform['value'] = platformName
              platform['label'] = platformName
              self.platformList.push(platform)
            }
          } else {
            console.log(message['data']['msg'])
          }
          // BusClient.$emit('refreshTable', message['data']['data'], self)
          // return message['data']['data']
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    postData: function (value1, value2) {
      let self = this
      self.display2 = false
      let status = 1
      // console.log('value2:', value2)
      // console.log('value1:', value1)

      // 判断筛选版本是否符合可搜索的条件
      var oldVersion = [
        '9.3',
        '9.4',
        '9.5',
        '9.5.5',
        '9.6',
        '9.6.5',
        '9.7',
        '9.8.2',
        '9.8.4',
        '9.8.6',
        '9.8.8',
        '9.9.2',
        '9.9.4',
        '9.9.6',
        '9.9.8',
        '9.10.200',
        '9.10.400',
        '9.10.600',
        '9.10.800',
        '9.11.200',
        '9.11.400',
        '9.11.600',
        '9.11.800',
        '9.12.200',
        '9.12.400',
        '9.12.600',
        '9.12.800',
        '9.13.200',
        '9.13.400',
        '9.13.600',
        '9.13.800',
        '9.14.200',
        '9.14.400',
        '9.14.600',
        '9.14.800',
        '9.15.200',
        '9.15.400',
        '9.15.600',
        '9.15.800',
        '10.0.200',
        '10.0.400',
        '10.0.600',
        '10.0.800',
        '10.1.200',
        '10.1.400',
        '10.2',
        '10.3',
        '10.4',
        '10.5',
        '10.6',
        '10.7',
        '10.8',
        '10.9',
        '10.10',
        '10.11',
        '10.12',
        '10.13',
        '10.14',
        '10.15',
        '10.16',
        '10.17'
      ]
      if (value2.length >= 2) {
        var isOldVersion = oldVersion.indexOf(value2[0])
        var search1 = true
        var search2 = true

        if (isOldVersion > -1) {
          for (let i of value2) {
            if (oldVersion.indexOf(i) > -1) {
              search1 = true
              continue
            } else {
              search1 = false
              break
            }
          }
        } else if (isOldVersion === -1) {
          for (let j of value2) {
            if (oldVersion.indexOf(j) === -1) {
              search2 = true
              continue
            } else {
              search2 = false
              break
            }
          }
        }
        // console.log('search1final:' + search1)
        // console.log('search2final:' + search2)
      }

      if (value1 === '') {
        self.$Message.info('请选择平台！')
      } else if (value2.length === 0) {
        self.$Message.info('请选择客户端版本！')
      } else if (search1 === false || search2 === false) {
        self.$Message.info({
          content:
            '多个版本查询时，不能合并查询低于美团10.2.200和高于等于10.2.200版本。点评10.18同理',
          duration: 3
        })
      } else {
        self.$Spin.show()
        status = 0
        var value3 = value2.join(',')
        // console.log('value3:', value3)
        let params = {
          platformName: value1,
          version: value3
        }
        BusClient.metricsdata = []
        BusClient.metricsdata = {
          value1: self.value1,
          value2: self.value3
        }
        if (status === 0) {
          axios
            .get(analyticsbaseAPI + '/client/stats', {
              params: params,
              timeout: 10000000,
              dataType: 'json'
            })
            .then(function (message) {
              // console.log(message)
              if (message['data']['status'] === 0) {
                let clientStats = message['data']['data']
                self.length = clientStats.length
                // console.log('length', self.length)
                BusClient.$emit('refreshClientStats', clientStats)
                BusClient.$emit('refreshClientTableLength', self.length)
                BusClient.$emit('showClientMetricsTab')
              } else {
                alert(message['data']['msg'])
              }
              self.$Spin.hide()
            })
            .catch((error) => {
              self.$Spin.hide()
              console.log(error)
            })
        } else {
          self.$Spin.hide()
          self.$Message.info('存在未选择的参数！')
        }
      }
    }
  },
  mounted: function () {
    console.log('start page!!!')
    BusClient.processMetricsObject = this
    let self = this
    let click = true
    self.display3 = false
    if (BusClient.metricsdata) {
      click = true
      self.value1 = BusClient.metricsdata.value1
      self.value2 = BusClient.metricsdata.value2
    } else {
      click = false
      self.value2 = []
    }
    if (click) {
      if (self.value1 && self.value2) {
        self.postData(self.value1, self.value2)
      }
    }
    self.getVersionData(self)
  }
}
</script>

<style scoped>
</style>
