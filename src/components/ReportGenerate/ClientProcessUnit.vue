<template>
  <div>
    <div v-if="display5" id="quality">
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
          <div id="aheadJointDevRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
          <div id="jointDebugRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table highlight-row :height="600" border class="ivu-table-customize" :row-class-name="rowClassName" :columns="columns5" :data="processStats"></Table>
    </div>
    <div v-else>
      <Row>
        <Col span="8" style="width:45%; margin-top: 15px">
          <div id="aheadJointDevRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
        <Col span="8" style="width:45%; margin-top: 15px">
          <div id="jointDebugRatio" style="padding-top:10px; margin-left:15px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </Col>
      </Row>
      <Table border :row-class-name="rowClassName" :columns="columns5" :data="processStats"></Table>
    </div>
  </div>
</template>

<script>
  import { BusClient } from '@/global/bus'
  import clientProcessExpend from './ClientProcessExpendUnit'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  HighchartsMore(Highcharts)

  BusClient.$on('refreshClientStats', function (data) {
    BusClient.clientProcessUnitObject.processStats = data
    // BusClient.$emit('showProcessMetricsTab')
    // console.log('processStats', BusClient.clientProcessUnitObject.processStats)
    BusClient.clientProcessUnitObject.setHighChartData(data)
  })

  BusClient.$on('refreshClientTableLength', function (data) {
    BusClient.clientProcessUnitObject.display5 = BusClient.clientProcessUnitObject.adjustTableHight(data)
  })
  export default {
    // components: {Layout},
    components: {clientProcessExpend},
    name: 'client-process-unit',
    data: function () {
      return {
        display5: false,
        columns5: [
          {
            type: 'expand',
            width: 50,
            render: (h, params) => {
              return h(clientProcessExpend, {
                props: {
                  row: params.row
                }
              })
            }
          },
          {
            title: '项目',
            key: 'bizLine',
            width: 100,
            sortable: true
          },
          {
            title: '方向',
            key: 'appType',
            width: 100,
            sortable: true
          },
          {
            title: '版本',
            width: 140,
            key: 'version'
          },
          {
            title: '后台先行情况',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '后台先行情况（需求耗时）'),
                h('div', {}, '后台先行/ 非后台先行/ 不涉及后台')
              ])
            },
            key: 'beAheadTaskCnt',
            render: (h, params) => {
              return h('div', {}, params.row['beAheadDevPd'] + ' / ' + params.row['nonAheadDevPd'] + ' / ' + params.row['normalDevPd'])
            },
            width: 230
            // render: (h, params) => {
            //   return h('div', [
            //     h('ul', [
            //       h('li', {},
            //         '后台先行需求开发时长： ' + params.row['beAheadDevPd']
            //       )]
            //     ),
            //     h('ul', [
            //       h('li', {},
            //         '非后台先行需求开发时长： ' + params.row['nonAheadDevPd']
            //       )]
            //     ),
            //     h('ul', [
            //       h('li', {},
            //         '不涉及后台需求开发时长：' + params.row['normalDevPd']
            //       )]
            //     )
            //   ])
            // }
            // width: 120,
          },
          {
            title: '后台先行需求占比',
            key: 'aheadJointDevRatio',
            width: 150,
            sortable: true
          },
          {
            title: '联调占比(整体)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '联调占比'),
                h('div', {}, '(整体)')
              ])
            },
            key: 'jointDebugRatio',
            width: 120,
            sortable: true
          },
          {
            title: '联调占比(后台先行)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '联调占比'),
                h('div', {}, '(后台先行需求)')
              ])
            },
            key: 'aheadJointDebugRatio',
            width: 130,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {}, this.transferEmpty(params.row['aheadJointDebugRatio'])
                )
              ])
            }
          },
          {
            title: '联调占比(非后台先行)',
            renderHeader: (h, params) => {
              return h('div', [
                h('div', {}, '联调占比'),
                h('div', {}, '(非后台先行需求)')
              ])
            },
            key: 'nonAheajointDebugRatio',
            width: 130,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {}, this.transferEmpty(params.row['nonAheajointDebugRatio'])
                )
              ])
            }

          },
          {
            title: '迭代测试比例',
            key: 'iterationTestRatio',
            width: 120,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank'
                  }
                }, '查看详情')
              ])
            }
          }
        ],
        processStats: [],
        directories: [],
        aheadJointDevRatio: [],
        jointDebugRatio: []
      }
    },

    methods: {
      transferEmpty: function (data) {
        if (data === null) {
          return 'N/A'
        } else {
          return data
        }
      },
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0) {
          return 'demo-stripe'
        } else {
          return ''
        }
      },
      jump: function (data) {
        const dict = {
          projectKey: data['projectKey'],
          version: data['version'],
          bizLine: data['bizLine'],
          appType: data['appType'],
          platformName: data['platformName'],
          component: data['component']
        }
        let temp = ''
        temp += '/client/detail?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      },
      setHighChartData: function (data) {
        // 提取横轴各方向数据
        const self = this
        self.directories = []
        self.aheadJointDevRatio = []
        self.jointDebugRatio = []
        for (var i in data) {
          if (self.directories.indexOf(data[i].bizLine) === -1) {
            self.directories.push(data[i].bizLine)
          }
        }
        // 提取后台先行占比数据
        let andAheadJointDevRatio = []
        let iOSAheadJointDevRatio = []
        let mRNAheadJointDevRatio = []
        let andJointDebugRatio = []
        let iOSJointDebugRatio = []
        let mRNJointDebugRatio = []
        for (var each in self.directories) {
          for (var y in data) {
            if (data[y].bizLine === self.directories[each] && data[y].appType === 'Android') {
              andAheadJointDevRatio.push(parseFloat(data[y].aheadJointDevRatio))
              andJointDebugRatio.push(parseFloat(data[y].jointDebugRatio))
            } else if (data[y].bizLine === self.directories[each] && data[y].appType === 'iOS') {
              iOSAheadJointDevRatio.push(parseFloat(data[y].aheadJointDevRatio))
              iOSJointDebugRatio.push(parseFloat(data[y].jointDebugRatio))
            } else if (data[y].bizLine === self.directories[each] && data[y].appType === 'MRN') {
              mRNAheadJointDevRatio.push(parseFloat(data[y].aheadJointDevRatio))
              mRNJointDebugRatio.push(parseFloat(data[y].jointDebugRatio))
            } else {
              continue
            }
          }
        }
        let tempAnd = {
          name: 'Android',
          data: andAheadJointDevRatio
        }
        let tempiOS = {
          name: 'iOS',
          data: iOSAheadJointDevRatio
        }
        let tempMRN = {
          name: 'MRN',
          data: mRNAheadJointDevRatio
        }
        self.aheadJointDevRatio.push(tempiOS)
        self.aheadJointDevRatio.push(tempAnd)
        self.aheadJointDevRatio.push(tempMRN)
        // 提取开发质量数据
        let tempAndDebug = {
          name: 'Android',
          data: andJointDebugRatio
        }
        let tempiOSDebug = {
          name: 'iOS',
          data: iOSJointDebugRatio
        }
        let tempMRNDebug = {
          name: 'MRN',
          data: mRNJointDebugRatio
        }
        self.jointDebugRatio.push(tempiOSDebug)
        self.jointDebugRatio.push(tempAndDebug)
        self.jointDebugRatio.push(tempMRNDebug)
        setTimeout(function () {
          self.aheadJointDevRatioHighchart()
          self.jointDebugRatioHighchart()
        }, 0)
      },
      aheadJointDevRatioHighchart: function () {
        Highcharts.chart('aheadJointDevRatio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '后台先行需求占比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: 80,
              width: 2,
              label: {
                text: '80%',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -10
              }
            }],
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + '%'
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.aheadJointDevRatio,
          credits: {
            enabled: false
          }
        })
      },
      jointDebugRatioHighchart: function () {
        Highcharts.chart('jointDebugRatio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '联调时间占比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            max: 100,
            allowDecimals: true
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + '%'
                }
              },
              pointPadding: 0.25,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.jointDebugRatio,
          credits: {
            enabled: false
          }
        })
      }
    },
    mounted: function () {
      BusClient.clientProcessUnitObject = this
      // console.log('begin', BusClient.qualityMetricsUnitObject)
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }

  /*选中某一行高亮*/
  .ivu-table-row-highlight td {
    background-color: #cfd2d4a1 !important;
  }

  .ivu-table-customize th{
    color:#0f1115;
    font-weight: bold;
    background-color: #cfd2d4a1;
  }
</style>
