<template>
  <div>
    <Col span="24" order="1">
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">李可欣（likexin06）</span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 方向仓库较多时，点击生成按钮，请稍作等待</p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/216893259" target="_blank">自动化报告FAQ</a></span></p>
      </Card>
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}"
            dis-hover>
        <div slot="title">
          <Row type="flex" style="width: 98.8%">
            <Col span="18" style="display: flex">
              <div>
                <span :style="{fontWeight:'bolder'}">查询条件</span>
              </div>
              <DatePicker style="margin-top: -8px;width: 200px;margin-left: 15px" :value="dateRange" type="daterange"
                          :clearable=false placement="bottom-end" placeholder="请选择时间间隔" @on-change="filterByTime"
                          transfer></DatePicker>
              <div style="margin-top: -8px;width: 300px;margin-left: 200px">
                <direction :needDefault="true" :width="width"></direction>
              </div>
            </Col>
          </Row>
        </div>
        <div>
          <div>
            <Timeline style="padding-top: 15px">
              <TimelineItem>
                <p class="time">自动化报告parent ID:<span style="color: #ff0000;">*</span></p>
                <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 136323902 for https://km.sankuai.com/page/136323902</p>
                <Row type="flex" :gutter="16">
                  <Col span="24" order="1">
                    <Input v-model="parentPage" placeholder="e.g:136323902"/>
                  </Col>
                </Row>
              </TimelineItem>
            </Timeline>
            <Row type="flex" :gutter="2" justify="center">
              <Col span="2" order="1">
                <Button type="primary" @click = 'save()'>生成</Button>
              </Col>
            </Row>
          </div>
        </div>
      </Card>
    </Col>

  </div>
</template>

<script>
  import Direction from '../../components/AnalyticsOnes/multipleDirectionComponent'
  import axios from 'axios'
  export default {
    name: 'auto-test-unit',
    components: {Direction},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 7)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      // 数据初始化
      return {
        start: start,
        end: end,
        dateRange: dateRange,
        directionList: [],
        width: 500,
        parentPage: ''
      }
    },
    methods: {
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      save: function () {
        let self = this
        if (this.parentPage) {
          self.searchResult()
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      },
      searchResult: function () {
        let self = this
        var params = []
        for (let item of this.$store.state.directionIdList) {
          let temp = {
            value: '',
            key: item
          }
          params.push(temp)
        }
        axios.post('http://qa.sankuai.com/data/autoTest/mutiReport?from=' + this.start + '&to=' + this.end + '&page=' + this.parentPage, params).then(function (message) {
          let wiki = 'https://km.sankuai.com/page/'
          if (message.data.status === 0) {
            self.$Message.success('生成成功！')
            self.$Modal.success({
              title: '生成成功',
              content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + wiki + message.data.data.toString() + '">' + message.data.data.toString() + '</a></P>'
            })
          } else {
            self.$Spin.hide()
            self.$Message.error('生成失败！')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：' + message.data.msg + '</P>'
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
