<template>
  <div>
    <Col span="24" order="1">
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span
          class="ciTip">李可欣（likexin06）、韩玉玺（hanyuxi）</span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i>  仓库地址和PR ID仅用于获取本次PR的接口覆盖情况 <span class="ciTip"></span>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip">
        <a href="https://km.sankuai.com/page/1216991742" target="_blank">使用说明</a>
      </span>
        </p>
      </Card>
      <Card :bordered="true" :style="{borderWidth:0,marginTop:'10px',marginBottom:'10px'}">
        <Timeline style="padding-top: 15px">
          <TimelineItem>
            <p class="time">仓库地址:<span style="color: #ff0000;">*</span></p>
            <Row type="flex" :gutter="16">
              <Col span="24" order="1">
                <Input v-model="repo" placeholder="请输入仓库地址"/>
              </Col>
            </Row>
          </TimelineItem>
          <TimelineItem>
            <p class="time">PR ID:<span style="color: #ff0000;">*</span></p>
            <Row type="flex" :gutter="16">
              <Col span="24" order="1">
                <Input v-model="prId" placeholder="请输入prId"/>
              </Col>
            </Row>
          </TimelineItem>
          <TimelineItem>
            <p class="time">方向:<span style="color: #ff0000;">*</span></p>
            <Row type="flex" :gutter="16">
              <Col span="24" order="1">
                <direction :needDefault="false" :width="width"></direction>
              </Col>
            </Row>
          </TimelineItem>
        </Timeline>
      </Card>
      <Row type="flex" :gutter="2" justify="center">
        <Col span="2" order="1">
          <i-button type="primary" @click='submit()'>生成自动化覆盖报告</i-button>
        </Col>
      </Row>
    </Col>
  </div>
</template>

<script>
import Direction from '../../components/AnalyticsOnes/directionComponent'
import axios from 'axios'
import {Bus} from '@/global/bus'

export default {
  name: 'auto_cover_report-unit',
  components: {Direction},
  data: function () {
    return {
      repo: '',
      prId: '',
      width: 500,
      misId: Bus.userInfo.userLogin
    }
  },
  methods: {
    submit: function () {
      let self = this
      if (this.repo && this.prId && this.$store.state.directionIdList[0]) {
        this.createReport()
      } else {
        self.$Message.info('存在必填参数未填写！')
      }
    },
    createReport: function () {
      let self = this
      this.url = 'https://qareport.hotel.test.sankuai.com/query/autoCover/report'
      axios.post(this.url, {
        repo: this.repo,
        prId: this.prId,
        directionIds: this.$store.state.directionIdList,
        misId: this.misId
      }).then(function (message) {
        if (message.data.code === 200) {
          // self.$Message.success('生成成功！无需审核，直接生效！')
          self.$Modal.success({
            title: message.data.message
          })
        } else {
          self.$Spin.hide()
          // self.$Message.error('生成失败！')
          self.$Modal.error({
            title: '生成失败',
            content: '<P>错误原因：' + message.data.msg + '</P>'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.ciTip {
  color: #337ab7;
  padding-left: 2px;
  padding-right: 2px;
  font-weight: bold;
}
</style>
