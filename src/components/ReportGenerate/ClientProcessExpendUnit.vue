<template>
  <Table highlight-row :columns="expendcolumn" :data="row.dataList"></Table>
</template>

<script>
    export default {
      name: 'ClientProcessExpend',
      props: {
        row: Object
      },
      data () {
        return {
          expendcolumn: [
            {
              title: '序号',
              type: 'index',
              width: 100,
              align: 'center'
            },
            {
              title: '版本',
              key: 'version'
            },
            {
              title: '后台先行情况',
              key: 'beAheadTaskCnt',
              render: (h, params) => {
                return h('div', {}, params.row['beAheadDevPd'] + ' / ' + params.row['nonAheadDevPd'] + ' / ' + params.row['normalDevPd'])
              }
            },
            {
              title: '后台先行需求占比',
              key: 'aheadJointDevRatio',
              sortable: true
            },
            {
              title: '整体联调占比',
              key: 'jointDebugRatio',
              sortable: true
            },
            {
              title: '后台先行需求联调占比',
              key: 'aheadJointDebugRatio',
              sortable: true,
              render: (h, params) => {
                return h('div', [
                  h('span', {}, this.transferEmpty(params.row['aheadJointDebugRatio'])
                  )
                ])
              }
            },
            {
              title: '非后台先行需求联调占比',
              key: 'nonAheajointDebugRatio',
              sortable: true,
              render: (h, params) => {
                return h('div', [
                  h('span', {}, this.transferEmpty(params.row['nonAheajointDebugRatio'])
                  )
                ])
              }

            },
            {
              title: '迭代测试比例',
              key: 'iterationTestRatio',
              sortable: true
            }
          ]
        }
      }
    }
</script>

<style scoped>

</style>
