<template>
  <div>
    <Card :bordered="true">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 测试报告生成目前已经迁移至TestX，欢迎使用<span class="ciTip"><a href="https://testx.sankuai.com/testactivity/activity" target="_blank">TestX-测试活动</a></span>生成测试报告</p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用方式：<span class="ciTip"><a href="https://km.sankuai.com/page/1316642841" target="_blank">测试活动使用文档</a></span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 如有问题，欢迎提TT到<span class="ciTip"><a href="https://tt.sankuai.com/ticket/create?cid=43&tid=824&iid=21718" target="_blank">测试报告团队</a></span></p>

    </Card>
    <!-- <Timeline style="padding-top: 15px">
      <TimelineItem>
        <p class="time">测试设计ID:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 168588004 for https://km.sankuai.com/page/168588004</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="testDesign" placeholder="e.g:168588004，不需要填入链接"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">提测TaskID:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 4540861 for https://ones.sankuai.com/ones/product/2111/testdetail/4540861</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="jiraId" placeholder="e.g:4540861，不需要填入链接"/>
          </Col>
        </Row>
      </TimelineItem> -->
      <!-- <TimelineItem>
        <p class="time">覆盖率泳道:</p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 非必填字段,使用JacocoLive时填写,如 1938-kybix</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="swimlane" placeholder="e.g:14866"/>
          </Col>
        </Row>
      </TimelineItem>  <TimelineItem>
        <p class="time">覆盖率发布项:</p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 非必填字段,使用JacocoLive时填写,如 meituan.travel.dsg.tripext,meituan.travel.dsg.hubble(多个发布项逗号分割)</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="releaseNames" placeholder="e.g:14866"/>
          </Col>
        </Row>
      </TimelineItem>  <TimelineItem>
        <p class="time">测试用例ID:</p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 非必填字段,如使用Aden用例管理系统,可填写用例ID</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="suiteId" placeholder="e.g:14866"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">测试报告人:</p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> misId,非必填字段，如需区分Bug报告人，请填写该字段，如不需要，忽略</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <Input v-model="creator" placeholder="e.g:huonina"/>
          </Col>
        </Row>
      </TimelineItem> -->

    <!-- </Timeline> -->
    <!-- <Row type="flex" :gutter="2" justify="center">
      <Col span="2" order="1">
        <Button type="primary" @click = 'save()'>生成</Button>
      </Col>
    </Row> -->
  </div>
</template>

<script>
  import { agileReport } from '@/global/variable'
  export default {
    name: 'test-report-unit',
    data: function () {
      return {
        testDesign: '',
        jiraId: '',
        creator: '',
        swimlane: '',
        releaseNames: '',
        suiteId: ''
      }
    },
    methods: {
      save: function () {
        let self = this
        if (this.testDesign && this.jiraId) {
          // console.log('show')
          self.$Spin.show()
          $.ajax({
            url: agileReport + '/testreport',
            type: 'POST',
            timeout: 5000000,
            data: {
              'pageId': this.testDesign,
              'issueId': this.jiraId,
              'reporters': this.creator,
              'suiteId': this.suiteId,
              'swimlane': this.swimlane,
              'releaseNames': this.releaseNames
            },
            success: function (message) {
              // console.log(message)
              // let msg = JSON.parse(message)
              // console.log('hide')
              self.$Spin.hide()
              if (message['status'] === 1) {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>' + message['msg'].toString() + '</P>'
                })
              } else if (message['status'] === 3) {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>请参考测试设计模板：<a style="word-break: break-all" target="_blank" href="' + message['data'].toString() + '">' + message['data'].toString() + '</a></P>'
                })
              } else if (message['status'] === 0) {
                console.log('mes', message)
                self.testDesign = ''
                self.jiraId = ''
                self.creator = ''
                self.$Message.success('生成成功！')
                self.$Modal.success({
                  title: '生成成功',
                  content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + message['data'].toString() + '">' + message['data'].toString() + '</a></P>'
                })
              } else {
                self.$Message.error('未知错误！')
              }
            },
            error: function (message) {
              self.$Spin.hide()
              self.$Message.error('生成失败！')
              self.$Modal.error({
                title: '生成失败',
                content: '<P>错误原因：' + message['statusText'] + '</P>'
              })
            }
          })
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
