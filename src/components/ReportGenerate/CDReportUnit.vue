<template>
  <div>
    <Col span="24" order="1">
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">段陈辰（duanchenchen）</span></p>
      </Card>
      <Timeline style="padding-top: 15px">
        <TimelineItem>
          <p class="time">方向:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <Cascader :data="directionList" v-model="direction" placeholder="请选择方向" filterable :transfer="false"
                        style="width: 50%" change-on-select @on-change="getCurrentChooseDirection"></Cascader>
            </Col>
          </Row>
        </TimelineItem>
         <TimelineItem>
          <p class="time">优先级(支持多选):<span style="color: #ff0000;">*</span>
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 5px 0;"></div>
          <el-checkbox-group v-model="checkedModules" @change="handleCheckedModulesChange">
          <el-checkbox v-for="city in modules" :label="city" :key="city">{{city}}</el-checkbox>
          </el-checkbox-group>
          </TimelineItem>
        <TimelineItem>
          <p class="time">报告起止时间:<span style="color: #ff0000;">*</span></p>
          <Row type="flex" :gutter="20" style="width: 98.8%">
            <Col span="30" order="1">
              <DatePicker style="width: 200px;" :value="dateRange" type="daterange"
                          :clearable=false placement="bottom-end" placeholder="请选择时间间隔" @on-change="filterByTime"
                          transfer></DatePicker>
            </Col>
          </Row>
        </TimelineItem>
       </Timeline>
      <Row type="flex" :gutter="2" justify="center">
        <Col span="2" order="1">
          <i-button type="primary" @click="submit()">生成持续交付运营报告</i-button>
        </Col>
      </Row>
    </Col>
  </div>
</template>

<script>
  import axios from 'axios'
  const moduleOptions = ['P0', 'P1', 'P2', 'P3']
  export default {
    name: 'cd-report-unit',
    components: {},
    data: function () {
      let date = new Date()
      let dateRange = []
      // 设置初始时间
      let end = this.getTimeString(date)
      date.setDate(date.getDate() - 7)
      let start = this.getTimeString(date)
      dateRange.push(start)
      dateRange.push(end)
      return {
        style: {},
        directionList: [],
        currentChooseDirection: -1,
        direction: [],
        business: '',
        businessName: '',
        isHtml: '1',
        parent_id: '235650717',
        dateRange: dateRange,
        start: start,
        end: end,
        checkAll: false,
        checkedModules: [],
        modules: moduleOptions,
        isIndeterminate: true,
        priority: ''
      }
    },
    methods: {
      handleCheckAllChange (val) {
        this.checkedModules = val ? moduleOptions : []
        this.isIndeterminate = false
      },
      handleCheckedModulesChange (value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.modules.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.modules.length
      },
      filterByTime: function (start) {
        this.dateRange = start
        this.start = start[0]
        this.end = start[1]
      },
      getTimeString: function (obj) {
        let result = ''
        let year = obj.getFullYear()
        let month = obj.getMonth() + 1
        let day = obj.getDate()
        result += year.toString() + '-'

        if (month >= 1 && month <= 9) {
          result = result + '0' + month
        } else {
          result += month
        }

        result += '-'

        if (day >= 0 && day <= 9) {
          result = result + '0' + day
        } else {
          result += day
        }
        return result
      },
      submit: function () {
        let self = this
        self.priority = self.checkedModules.join(',')
        if (self.business && self.priority && self.start && self.end) {
          self.run()
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      },
      run: function () {
        this.url = this.getDomain('cd') + '/report?priority=' + this.priority + '&direction=' + this.business + '' + '&from=' + this.start + '&to=' + this.end
        if (this.isHtml === '1') {
          this.$Spin.show()
          console.log(this.url)
          window.location.href = this.url
          return
        }
        let self = this
        self.$Spin.show()
        axios.get(this.url).then(function (message) {
          try {
            self.$Spin.hide()
            console.log(message)
            if (message.data.message) {
              self.$Modal.success({
                title: '返回结果',
                content: message.data.message
              })
            } else {
              self.$Spin.hide()
              self.$Message.error('服务错误')
            }
          } catch (e) {
            self.$Spin.hide()
            self.$Message.error('服务错误')
          }
        }).catch(function (error) {
          // 由网络或者服务器抛出的错误
          self.$Spin.hide()
          self.$Message.error(error.toString())
        })
      },
      getDirections: function () {
        let self = this
        axios(this.getDomain('config') + '/mcd/org/basic?direction_id=1&disable=1').then(function (message) {
          if (message.data.result) {
            self.directionList.length = 0
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      getCurrentChooseDirection: function (value, selectedData) {
        let item = selectedData[selectedData.length - 1]
        this.currentChooseDirection = {
          key: item.direction_id.toString(),
          label: item.label,
          value: item.value
        }
        console.log(item.value.toString())
        this.business = item.direction_id.toString()
        this.businessName = item.value.toString()
      }
    },
    mounted: function () {
      this.style = {
        'minHeight': this.getToolchainScreeHeight()
      }
      this.getDirections()
    }
  }
</script>


<style scoped>
  .ciTip {
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
