<template>
  <div>
    <Card :bordered="true">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">杨宇航（yangyuhang03）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 生成报告前，请先
        <a class="ciTip" href="http://ci2.sankuai.com/job/Tools/job/client-data/" target="_blank">点这里</a>
        同步兼容性自动化jira数据
      </p>
    </Card>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <p class="time">方向:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <Select v-model="direction">
              <Option v-for="item in directionItems" :value="item.value" :key="item.key">{{item.value}}</Option>
            </Select>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">父页面:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 1047560410 for https://wiki.sankuai.com/pages/viewpage.action?pageId=1047560410</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <Input v-model="wikiKey" placeholder="e.g:1047560410"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">版本:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <Input v-model="version" placeholder="e.g:9.13.600"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">起止时间:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <DatePicker v-model="time" type="daterange" placement="bottom-start" placeholder="请选择时间" style="width: 100% " format="yyyy-MM-dd"></DatePicker>
          </Col>
        </Row>
      </TimelineItem>

    </Timeline>
    <Row type="flex" :gutter="2" justify="center">
      <Col span="2" order="1">
        <Button type="primary" @click = 'save()'>生成</Button>
      </Col>
    </Row>
  </div>
</template>

<script>
  import { agileReport } from '@/global/variable'
  export default {
    name: 'client-test-report-unit',
    data: function () {
      return {
        direction: '',
        directionItems: [
          {key: 1, value: '美团国内酒店'},
          {key: 2, value: '点评国内酒店'},
          {key: 3, value: '美团海外酒店'},
          {key: 4, value: '点评海外酒店'},
          {key: 5, value: '美团境内度假'},
          {key: 6, value: '点评境内度假'}
        ],
        wikiKey: '',
        version: '',
        time: ''
      }
    },
    methods: {
      getTime (date) {
        console.log(date)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let result = ''
        result += year + '-'
        if (month >= 10) {
          result += month + '-'
        } else {
          result += '0' + month + '-'
        }
        if (day >= 10) {
          result += day
        } else {
          result += '0' + day
        }
        return result
      },
      save: function () {
        let self = this
        if (self.direction && self.wikiKey && self.version && self.time) {
          self.$Spin.show()
          $.ajax({
            url: agileReport + '/clienttestreport',
            type: 'POST',
            timeout: 180000,
            data: {
              'direction': self.direction,
              'parentPage': self.wikiKey,
              'version': self.version,
              'startTime': self.getTime(self.time[0]),
              'endTime': self.getTime(self.time[1])
            },
            dataType: 'json',
            success: function (msg) {
              self.$Spin.hide()
              if (msg['status'] === 0) {
                self.direction = ''
                self.parentPage = ''
                self.version = ''
                self.startTime = ''
                self.endTime = ''
                self.$Message.success('生成成功！')
                self.$Modal.success({
                  title: '生成成功',
                  content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + msg['data'].toString() + '">' + msg['data'].toString() + '</a></P>'
                })
              } else {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>错误原因：' + msg['msg'].toString() + '</P>'
                })
              }
            },
            error: function (msg) {
              self.$Spin.hide()
              self.$Message.error('生成失败！')
              self.$Modal.error({
                title: '生成失败',
                content: '<P>错误原因：' + msg + '</P>'
              })
            }
          })
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
