<template>
  <Table highlight-row :columns="expendcolumn" :data="row.dataList"></Table>
</template>

<script>
    export default {
      name: 'ClientDeliveryExpend',
      props: {
        row: Object
      },
      data () {
        return {
          expendcolumn: [
            {
              title: '版本',
              // width: 110,
              key: 'version'
            },
            {
              title: '总开发Task个数',
              key: 'taskCnt',
              sortable: true
              // width: 120,
            },
            {
              title: '如期提测需求占比',
              key: 'onScheduleRatio',
              // width: 140,
              sortable: true
            },
            {
              title: '提测打回需求占比',
              key: 'rejectReqDevRatio',
              // width: 140,
              sortable: true
            }
          ]
        }
      }
    }
</script>

<style scoped>

</style>
