<template>
  <div>
    <Card :bordered="true">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">董雅璇（dongyaxuan）</span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/341170028" target="_blank">质量报告FAQ&&问题反馈</a></span></p>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 生成线上问题必填<span class="ciTip">方向</span>,生成其余模块必填<span class="ciTip">projectKey</span></p>
    </Card>
    <Timeline style="padding-top: 15px">
    <TimelineItem>
        <p class="time">请选择报告包含的模块:<span style="color: #ff0000;">*</span>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
  <div style="margin: 5px 0;"></div>
  <el-checkbox-group v-model="checkedModules" @change="handleCheckedModulesChange">
    <el-checkbox v-for="city in modules" :label="city" :key="city">{{city}}</el-checkbox>
  </el-checkbox-group>
  </TimelineItem>
      <TimelineItem>
        <p class="time">标题:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="title" placeholder="e.g:营销平台-质量月报-201712"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">起止时间:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
            <DatePicker v-model="time" type="daterange" placement="bottom-start" placeholder="请选择时间" style="width: 100% " format="yyyy-MM-dd"></DatePicker>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">MCD方向id(用于生成<span style="color: #ff0000;">发布质量</span>/<span style="color: #ff0000;">需求统计数据</span>/<span style="color: #ff0000;">需求详细数据</span>/<span style="color: #ff0000;">自动化发现的Bug</span>/<span style="color: #ff0000;">质量数据分析图</span>):</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="jiraKey" placeholder="e.g:114"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">父页面:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 1047560410 for https://wiki.sankuai.com/pages/viewpage.action?pageId=1047560410</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="wikiKey" placeholder="e.g:1047560410"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">方向(用于生成<span style="color: #ff0000;">线上问题</span>):</p>
        <Row type="flex" :gutter="16">
            <Col span="24" order="1">
              <Cascader :data="subDirectionTree" v-model="direction" placeholder="请选择方向" filterable :transfer="false"
                        style="width: 100%" change-on-select @on-change="getCurrentChooseDirection"></Cascader>
            </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">misId(不填写代表不过滤，逗号隔开，用于过滤线下统计数据中指派给<span style="color: #ff0000;">misId</span>的提测):</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="misId" placeholder="e.g:dongyaxuan,gumin03"/>
          </Col>
        </Row>
      </TimelineItem>
    </Timeline>
    <Row type="flex" :gutter="2" justify="center">
      <Col span="2" order="1">
      <Button type="primary" @click = 'save()'>生成</Button>
      </Col>
    </Row>
    <Row>
      <Col>
        <div style="height: 15px;"></div>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  import { agileReport } from '@/global/variable'
  const moduleOptions = ['线上问题', '发布质量', '需求统计数据', '需求详细数据', '自动化发现的Bug', '质量数据分析图']
  export default {
    name: 'server-report-unit',
    data: function () {
      return {
        title: '',
        time: '',
        jiraKey: '',
        wikiKey: '',
        business: '',
        misId: '',
        subDirectionTree: [],
        checkAll: false,
        checkedModules: [],
        modules: moduleOptions,
        isIndeterminate: true
      }
    },
    methods: {
      handleCheckAllChange (val) {
        this.checkedModules = val ? moduleOptions : []
        this.isIndeterminate = false
      },
      handleCheckedModulesChange (value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.modules.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.modules.length
      },
      getCurrentChooseDirection: function (value, selectedData) {
        console.log('selectedData:' + value + selectedData)
        let item = selectedData[selectedData.length - 1]
        console.log('item:' + item)
        this.currentChooseDirection = {
          key: item.direction_id.toString(),
          label: item.label,
          value: item.value
        }
        this.business = item.label.toString()
      },
      getsubDirectionList: function () {
        let self = this
        axios.get('https://pm.sankuai.com/ones/get_ones_direction_field').then(function (message) {
          self.subDirectionTree.length = 0
          if (message.data.status === 0) {
            self.subDirectionTree = message.data.data
            // self.getinitialdata()
          }
        }).catch(function () {
          self.subDirectionTree.length = 0
        })
      },
      getTime (date) {
        console.log(date)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let result = ''
        result += year + '-'
        if (month >= 10) {
          result += month + '-'
        } else {
          result += '0' + month + '-'
        }
        if (day >= 10) {
          result += day
        } else {
          result += '0' + day
        }
        return result
      },
      save: function () {
        let self = this
        let state = false
        if (self.checkedModules.join('').includes('线上问题') && self.checkedModules.length > 1 && self.title && self.time && self.jiraKey && self.wikiKey && self.business) {
          state = true
        } else if (self.checkedModules.join('').includes('线上问题') && self.checkedModules.length === 1 && self.title && self.time && self.wikiKey && self.business) {
          state = true
        } else if (!self.checkedModules.join('').includes('线上问题') && self.checkedModules.length > 0 && self.title && self.time && self.wikiKey && self.jiraKey) {
          state = true
        }
        if (state) {
          self.$Spin.show()
          $.ajax({
            url: agileReport + '/qualityreport',
            type: 'POST',
            timeout: 180000,
            data: {
              'title': self.title,
              'startDate': self.getTime(self.time[0]),
              'endDate': self.getTime(self.time[1]),
              'projectKey': self.jiraKey,
              'parentPage': self.wikiKey,
              'business': self.business,
              'checkedModules': self.checkedModules.join(','),
              'misId': self.misId
            },
            dataType: 'json',
            success: function (msg) {
              self.$Spin.hide()
              if (msg['status'] === 0) {
                self.title = ''
                self.time = ''
                self.jiraKey = ''
                self.wikiKey = ''
                self.business = ''
                self.misId = ''
                self.checkedModules = []
                self.$Message.success('生成成功！')
                self.$Modal.success({
                  title: '生成成功',
                  content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + msg['data'].toString() + '">' + msg['data'].toString() + '</a></P>'
                })
              } else {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>错误原因：' + msg['msg'].toString() + '</P>'
                })
              }
            },
            error: function (message) {
              self.$Spin.hide()
              self.$Message.error('生成失败！')
              self.$Modal.error({
                title: '生成失败',
                content: '<P>错误原因：' + message + '</P>'
              })
            }
          })
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      }
    },
    mounted: function () {
      this.style = {
        'minHeight': this.getToolchainScreeHeight()
      }
      this.getsubDirectionList()
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
