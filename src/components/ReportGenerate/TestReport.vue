<template>
  <div>
    <head-component id="head"></head-component>
    <Tabs value="test_report" style='margin-top: 30px;margin-left: 5%;margin-right: 5%;width: auto;'>
      <TabPane label="测试报告" name="test_report">
        <test-report-unit></test-report-unit>
      </TabPane>
      <TabPane label="HyperJump视觉测试报告" name="Cqp_report">
        <Cqp-report-unit></Cqp-report-unit>
      </TabPane>
      <TabPane label="自动化数据报告" name="auto_test_report">
        <auto-test-unit></auto-test-unit>
      </TabPane>
      <TabPane label="服务端质量报告" name="server_report">
        <server-report-unit></server-report-unit>
      </TabPane>
      <TabPane label="部署时长报告" name="deploy_report">
        <deploy-report-unit></deploy-report-unit>
      </TabPane>
      <TabPane label="持续交付运营报告" name="cd_report">
        <cd-report-unit></cd-report-unit>
      </TabPane>
      <TabPane label="自动化覆盖报告" name="auto_cover_report">
        <auto-cover-report-unit></auto-cover-report-unit>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import TestReportUnit from '@/components/ReportGenerate/TestReportUnit'
  import CqpReportUnit from '@/components/ReportGenerate/CqpReportUnit'
  import AutoTestUnit from '@/components/ReportGenerate/AutoTestUnit'
  import DeployReportUnit from '@/components/ReportGenerate/DeployReportUnit'
  import CDReportUnit from '@/components/ReportGenerate/CDReportUnit'
  import AutoCoverReportUnit from '@/components/ReportGenerate/AutoCoverReportUnit'
  export default {
    name: 'test-report-page',
    data: function () {
      return {}
    }
  }
  Vue.component('head-component', Head)
  Vue.component('test-report-unit', TestReportUnit)
  Vue.component('Cqp-report-unit', CqpReportUnit)
  Vue.component('auto-test-unit', AutoTestUnit)
  Vue.component('deploy-report-unit', DeployReportUnit)
  Vue.component('cd-report-unit', CDReportUnit)
  Vue.component('auto-cover-report-unit', AutoCoverReportUnit)
</script>

<style scoped>
</style>
