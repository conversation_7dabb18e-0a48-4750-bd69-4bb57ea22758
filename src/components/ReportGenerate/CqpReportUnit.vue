<template>
  <div>
    <Card :bordered="true">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">董雅璇（dongyaxuan）</span></p>
    </Card>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <p class="time">jobId:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 38147 for http://qa.sankuai.com/microscope/jobInfo?jobId=38147</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="jobKey" placeholder="e.g:38147"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">父页面:<span style="color: #ff0000;">*</span></p>
        <p style="color: #ffa500;padding-top: 5px"><i class="fa fa-question-circle" aria-hidden="true"></i> 587900033 for https://km.sankuai.com/page/587900033</p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="wikiKey" placeholder="e.g:587900033"/>
          </Col>
        </Row>
      </TimelineItem>

    </Timeline>
    <Row type="flex" :gutter="2" justify="center">
      <Col span="2" order="1">
        <Button type="primary" @click = 'save()'>生成</Button>
      </Col>
    </Row>
  </div>
</template>

<script>
  import { agileReport } from '@/global/variable'
  export default {
    name: 'Cqp-report-unit',
    data: function () {
      return {
        jobKey: '',
        wikiKey: ''
      }
    },
    methods: {
      save: function () {
        let self = this
        if (this.jobKey && this.wikiKey) {
          // console.log('show')
          self.$Spin.show()
          $.ajax({
            url: agileReport + '/CQPResultReport',
            type: 'POST',
            timeout: 50000,
            data: {
              'parentPage': this.wikiKey,
              'jobId': this.jobKey
            },
            success: function (message) {
              // console.log(message)
              // let msg = JSON.parse(message)
              // console.log('hide')
              self.$Spin.hide()
              if (message['status'] === 1) {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>' + message['msg'].toString() + '</P>'
                })
              } else if (message['status'] === 3) {
                self.$Message.error('生成失败！')
                self.$Modal.error({
                  title: '生成失败',
                  content: '<P>' + message['msg'].toString() + '</P>'
                })
              } else if (message['status'] === 0) {
                console.log('mes', message)
                self.wikiKey = ''
                self.jobKey = ''
                self.creator = ''
                self.$Message.success('生成成功！')
                self.$Modal.success({
                  title: '生成成功',
                  content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + message['data'].toString() + '">' + message['data'].toString() + '</a></P>'
                })
              } else {
                self.$Message.error('未知错误！')
              }
            },
            error: function (message) {
              self.$Spin.hide()
              self.$Message.error('生成失败！')
              self.$Modal.error({
                title: '生成失败',
                content: '<P>错误原因：' + message['statusText'] + '</P>'
              })
            }
          })
        } else {
          self.$Message.info('存在必填参数未填写！')
        }
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
