<template>
  <div>
    <Card :bordered="true">
      <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
      <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">靳海亮（jinhailiang）</span>、<span class="ciTip">贾晨宇（jiachenyu02）</span></p>
    </Card>
    <Timeline style="padding-top: 15px">
      <TimelineItem>
        <p class="time">标题:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="title" placeholder="e.g:客户端8.3版本报告"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">起止时间:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <DatePicker v-model="time" type="datetimerange" format="yyyy-MM-dd HH:mm" placement="bottom-start" placeholder="请选择时间" style="width: 100% "></DatePicker>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">iOSKey:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="iosKey" placeholder="e.g:HI"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">AndroidKey:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="androidKey" placeholder="e.g:HA"/>
          </Col>
        </Row>
      </TimelineItem>
      <TimelineItem>
        <p class="time">版本:<span style="color: #ff0000;">*</span></p>
        <Row type="flex" :gutter="16">
          <Col span="24" order="1">
          <Input v-model="version" placeholder="e.g:8.4"/>
          </Col>
        </Row>
      </TimelineItem>
    </Timeline>
    <Row type="flex" :gutter="2" justify="center">
      <Col span="2" order="1">
      <Button type="primary" @click = 'generate()'>生成</Button>
      </Col>
    </Row>
    <Row>
      <Col>
      <div style="height: 15px;"></div>
      </Col>
    </Row>
    <Modal v-model="modal2" title="请确认生成的JQL语句" @on-ok="submit" width="80%">
      <Input v-model="iOSDocJQL" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 5px">
        <span slot="prepend">iOSDocJQL</span>
      </Input>
      <Input v-model="androidDocJQL" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">androidDocJQL</span>
      </Input>
      <Input v-model="iOSValidBugJQL" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">iOSValidBugJQL</span>
      </Input>
      <Input v-model="androidValidBugJQL" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">androidValidBugJQL</span>
      </Input>
      <Input v-model="iOSPmBugJql" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">iOSPmBugJql</span>
      </Input>
      <Input v-model="androidPMBugJql" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">androidPMBugJql</span>
      </Input>
      <Input v-model="iOSTotalBugJql" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">iOSTotalBugJql</span>
      </Input>
      <Input v-model="androidTotalBugJql" placeholder="请输入正确的JQL" style="width: 100%;padding-top: 10px">
        <span slot="prepend">androidTotalBugJql</span>
      </Input>
    </Modal>
  </div>
</template>

<script>
  import axios from 'axios'
  export default {
    name: 'client-version-report-unit',
    data: function () {
      return {
        modal2: false,
        title: '',
        time: '',
        iosKey: '',
        androidKey: '',
        version: '',
        iOSDocJQL: '',
        androidDocJQL: '',
        iOSValidBugJQL: '',
        androidValidBugJQL: '',
        iOSPmBugJql: '',
        androidPMBugJql: '',
        iOSTotalBugJql: '',
        androidTotalBugJql: ''
      }
    },
    methods: {
      getTime (date) {
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let hour = date.getHours()
        let minute = date.getMinutes()
        let result = ''
        result += year + '-'
        if (month >= 10) {
          result += month + '-'
        } else {
          result += '0' + month + '-'
        }
        if (day >= 10) {
          result += day + ' '
        } else {
          result += '0' + day + ' '
        }
        if (hour >= 10) {
          result += hour + ':'
        } else {
          result += '0' + hour + ':'
        }
        if (minute >= 10) {
          result += minute
        } else {
          result += '0' + minute
        }
        return result
      },
      generate () {
        if (this.title && this.time.length !== 0 && this.iosKey && this.androidKey && this.version) {
          this.modal2 = true
          let startTime = this.getTime(this.time[0])
          let endTime = this.getTime(this.time[1])
          this.iOSDocJQL = 'project = ' + this.iosKey + ' AND issuetype in (Requirement, 技术需求) AND fixVersion = ' + this.version
          this.androidDocJQL = 'project =  ' + this.AndroidKey + ' AND issuetype in (Requirement, 技术需求) AND fixVersion = ' + this.version
          this.iOSValidBugJQL = 'project = ' + this.iosKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND resolution in (Unresolved, Fixed, "Temporarily Fixed") AND affectedVersion = ' + this.version + ' AND Bug归属方向 = 客户端 AND created >= "' + startTime + '" AND created <=  "' + endTime + '"'
          this.androidValidBugJQL = 'project = ' + this.AndroidKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND resolution in (Unresolved, Fixed, "Temporarily Fixed") AND affectedVersion = ' + this.version + ' AND Bug归属方向 = 客户端 AND created >= "' + startTime + '" AND created <= "' + endTime + '"'
          this.iOSPmBugJql = 'project = ' + this.iosKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND resolution in (Unresolved, Fixed, "Temporarily Fixed") AND affectedVersion = ' + this.version + ' AND Bug归属方向 = PM AND created >= "' + startTime + ' " AND created <= "' + endTime + '"'
          this.androidPMBugJql = 'project = ' + this.AndroidKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND resolution in (Unresolved, Fixed, "Temporarily Fixed") AND affectedVersion = ' + this.version + ' AND Bug归属方向 = PM AND created >= "' + startTime + '"AND created <= "' + endTime + '"'
          this.iOSTotalBugJql = 'project = ' + this.iosKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND affectedVersion = ' + this.version + ' AND Bug归属方向 = 客户端 AND created >= "' + startTime + '" AND created <= "' + endTime + '"'
          this.androidTotalBugJql = 'project = ' + this.AndroidKey + ' AND issuetype in (产品Bug, Bug, Sub-Bug, 子Bug) AND affectedVersion = ' + this.version + ' AND Bug归属方向 = 客户端 AND created >= "' + startTime + '" AND created <= "' + endTime + '"'
        } else {
          this.$Message.info('存在必填参数未填写！')
        }
      },
      submit () {
        let self = this
        self.$Spin.show()
        let instance = axios.create({
          timeout: 90000,
          responseType: 'json',
          headers: {'Content-type': 'application/json; charset=utf-8'}
        })
        instance.post('http://10.4.240.80:9090/client/report', JSON.stringify({
          'title': self.title,
          'iOSDocJql': self.iOSDocJQL,
          'androidDocJql': self.androidDocJQL,
          'iOSValidBugJql': self.iOSValidBugJQL,
          'androidValidBugJql': self.androidValidBugJQL,
          'iOSPMBugJql': self.iOSPmBugJql,
          'androidPMBugJql': self.androidPMBugJql,
          'iOSTotalBugJql': self.iOSTotalBugJql,
          'androidTotalBugJql': self.androidTotalBugJql
        })).then(function (message) {
          console.log(message)
          let msg = message['data']
          setTimeout(() => {
            self.$Message.success('生成成功')
            self.$Spin.hide()
            if (msg['status'] === 0) {
              self.$Message.success('生成成功！')
              self.$Modal.success({
                title: '生成成功',
                content: '<P>报告链接：<a style="word-break: break-all" target="_blank" href="' + msg['WikiUrl'].toString() + '">' + msg['WikiUrl'].toString() + '</a></P>' +
                '<P>Exceptions: ' + msg['Exceptions'].toString() + '</P>'
              })
            } else {
              self.$Message.error('生成失败！')
              self.$Modal.error({
                title: '生成失败',
                content: '<P>错误原因：' + msg['Message'].toString() + '</P>'
              })
            }
          }, 1500)
        }).catch(function (msg) {
          console.log(msg)
          setTimeout(() => {
            self.$Spin.hide()
            self.$Message.error('生成失败')
            self.$Modal.error({
              title: '生成失败',
              content: '<P>错误原因：服务器内部错误</P>'
            })
          }, 1500)
        })
      }
    }
  }
</script>

<style scoped>
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
