<template>
  <div>
    <head-component id="head"></head-component>
    <Tabs value="Cqp_report" style='margin-top: 30px;margin-left: 5%;margin-right: 5%;width: auto;'>
      <TabPane label="测试报告" name="test_report">
        <test-report-unit></test-report-unit>
      </TabPane>
      <TabPane label="CQP视觉测试报告" name="Cqp_report">
        <Cqp-report-unit></Cqp-report-unit>
      </TabPane>
      <TabPane label="自动化数据报告" name="auto_test_report">
        <auto-test-unit></auto-test-unit>
      </TabPane>
      <TabPane label="客户端版本报告" name="client_version_report">
        <client-version-report-unit></client-version-report-unit>
      </TabPane>
      <TabPane label="服务端质量报告" name="server_report">
        <server-report-unit></server-report-unit>
      </TabPane>
      <TabPane label="客户端测试报告" name="client_test_report">
        <client-test-report-unit></client-test-report-unit>
      </TabPane>
      <TabPane label="部署时长报告" name="deploy_report">
        <deploy-report-unit></deploy-report-unit>
      </TabPane>
      <TabPane label="持续交付运营报告" name="cd_report">
        <cd-report-unit></cd-report-unit>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import DeployReportUnit from '@/components/ReportGenerate/CqpReportUnit'
  export default {
    name: 'Cqp_report',
    data: function () {
      return {}
    }
  }

  Vue.component('head-component', Head)
  Vue.component('Cqp-report-unit', CqpReportUnit)
</script>

<style scoped>
</style>
