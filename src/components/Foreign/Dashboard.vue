<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/foreign/dashboard">Cargo可用性/稳定性大盘</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <dashboard-availability style="margin-left: 1%; margin-right: 1%;max-width: 98%; overflow-x: hidden"></dashboard-availability>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import DashboardAvailability from '../ConfigurationCenter/availabilityStability'
  import CommonFooter from '../Common/Footer'
  export default {
    name: 'forrign-dashboard',
    components: {CommonFooter, DashboardAvailability},
    data: function () {
      return {
        tab: 'dashboard',
        style: {
          'width': '100%',
          'height': (window.innerHeight - 55).toString() + 'px'
        }
      }
    },
    methods: {
    }
  }
</script>

<style scoped>
</style>
