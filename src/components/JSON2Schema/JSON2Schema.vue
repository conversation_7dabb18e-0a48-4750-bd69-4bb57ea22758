<template>
  <div>
    <head-component id="head"></head-component>
    <div style="padding-top:20px;margin-left: 5%;margin-right: 5%;width: auto">
      <div style="text-align:center;">
        <button type="button" class="btn btn-success" @click="changeMode()">切换模式</button>
      </div>
      <br/>
      <div style="display: flex; width: 100%">
        <div :id='json_id' style="height: 675px; width:49.5%; margin-right: 0.5%"></div>
        <div style="align-items: center; display: flex">
          <Button @click="changeSchemaValue"><Icon type="md-arrow-round-forward" style="margin-right: 5px"/>生成</Button>
        </div>
        <div :id='schema_id' style="height: 675px; width:49.5%; margin-left: 0.5%"></div>
      </div>
    </div>
  </div>
</template>

<script>
  import JSONEditor from 'jsoneditor'
  import GenerateSchema from 'generate-schema'
  import 'jsoneditor/dist/jsoneditor.min'
  import 'jsoneditor/dist/jsoneditor.min.css'
  import Vue from 'vue'
  import Head from '@/components/Common/Head'

  Vue.component('head-component', Head)
  export default {
    name: 'json-2-json-schema',
    data: function () {
      return {
        json_id: 'json',
        schema_id: 'schema',
        useJsonEditor: true,
        jsonEditor: {},
        schemaEditor: {}
      }
    },
    methods: {
      changeMode () {
        if (this.useJsonEditor) {
          this.schemaEditor.setMode('tree')
          this.jsonEditor.setMode('tree')
        } else {
          this.schemaEditor.setMode('code')
          this.jsonEditor.setMode('code')
        }
        this.useJsonEditor = !this.useJsonEditor
      },
      changeSchemaValue: function () {
        this.schemaEditor.set(GenerateSchema.json(this.jsonEditor.get()))
      }
    },
    mounted: function () {
      let jsonContainer = document.getElementById(this.json_id)
      let options = {
        mode: 'code'
      }
      this.jsonEditor = new JSONEditor(jsonContainer, options)
      let sourceJson = {
        'Array': [1, 2, 3],
        'Boolean': true,
        'Null': null,
        'Number': 123,
        'Object': {'a': 'b', 'c': 'd'},
        'String': 'Hello World'
      }
      this.jsonEditor.set(sourceJson)
      jsonContainer = document.getElementById(this.schema_id)
      this.schemaEditor = new JSONEditor(jsonContainer, options)
      this.schemaEditor.set(GenerateSchema.json(this.jsonEditor.get()))
    }
  }
</script>

<style scoped>
</style>
