<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/cd">线上发布质量</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="tab" style="background-color: #FFFFFF; padding-top: 15px">
      <Alert show-icon type="error" style="margin-left: 1%;margin-right: 1%; font-weight: bolder;">
        迁移周知
        <template slot="desc">持续交付多级流水线大盘，已经统一迁移到 <a href="http://cd.sankuai.com/dashboard" target="_self">http://cd.sankuai.com/dashboard</a></template>
      </Alert>
      <online-deploy-homepage></online-deploy-homepage>
      <!--<Tabs :value="cd_tab" class="tool-tab" @on-click="registerChildrenTab">-->
        <!--<TabPane label="线上发布质量" name="deploy">-->
          <!---->
        <!--</TabPane>-->
      <!--</Tabs>-->
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import OnlineDeployHomepage from '../OnlineDeploy/online-deploy-homepage'
  import { Bus } from '@/global/bus'
  import CommonFooter from '../Common/Footer'
  export default {
    name: 'ContinuousDeliveryHomepage',
    components: {CommonFooter, OnlineDeployHomepage},
    data: function () {
      return {
        tab: 'cd',
        cd_tab: 'deploy',
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      registerChildrenTab: function (value) {
        this.$router.push('/homepage/cd/' + value)
      },
      fixPosition: function () {
        if (document.querySelector('.tool-tab > .ivu-tabs-bar')) {
          this.width = document.querySelector('.tool-tab > .ivu-tabs-bar').style.paddingLeft = (window.innerWidth - 180) + 'px'
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    },
    mounted: function () {
      this.fixPosition()
      window.addEventListener('resize', this.fixPosition)
      if (this.$route.params.tab) {
        let tab = this.$route.params.tab
        // let validTabList = ['tice', 'deploy', 'it']
        let validTabList = ['deploy']
        if (validTabList.indexOf(tab) !== -1) {
          this.cd_tab = tab
        } else {
          if (tab === 'tice') {
            window.location.href = 'http://cd.sankuai.com/dashboard/tp'
            return false
          } else {
            this.$router.push('/content-not-found')
          }
        }
      }
    }
  }
</script>

<style>
  .tab {
    margin-top: 15px;
    margin-left: 1.5%;
    margin-right: 1.5%;
    width: auto
  }

  .tool-tab > .ivu-tabs-bar {
    border-bottom: 0;
    margin-bottom: -10px;
    margin-top: -10px;
    font-weight: bolder;
  }
</style>
