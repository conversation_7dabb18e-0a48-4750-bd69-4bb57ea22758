<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div>
          <Spin v-if="isLoading">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <Row v-if="!isLoading" type="flex">
            <Col span="24">
            <Timeline style="padding-top: 15px;padding-left: 2px">
              <TimelineItem>
                <span>需求运营数据: </span>
                <Row type="flex" style="margin-top: 10px">
                  <Col span="3" style="margin-right: 10px">
                  <!--<Row type="flex">-->
                  <!--<div style="margin-top: 10px">-->
                  <!--<span style="font-weight: bolder;font-size: 20px;color: #2baee9">创建需求：</span>-->
                  <!--</div>-->
                  <!--<div style="margin-left: 5px;margin-top: 20px">-->
                  <!--<span style="font-weight: bolder;font-size: 60px;color: #2baee9">292</span>-->
                  <!--</div>-->
                  <!--<div style="margin-left: 30px;margin-top: 20px">-->
                  <!--<Icon size="80" color="#2baee9" type="md-arrow-forward" />-->
                  <!--</div>-->
                  <!--</Row>-->
                  <Card :style="{backgroundColor:'#2782B6',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        创建
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.createdCnt}}
                          <span style="font-size: 16px;">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <!--<Col span="0.5">-->
                  <!--<div style="margin-left: -8px;margin-top: 40px">-->
                  <!--<Icon size="50" color="#2baee9" type="md-arrow-round-forward" />-->
                  <!--</div>-->
                  <!--</Col>-->
                  <Col span="3">
                  <Card @click.native="cardClickReview" :style="{backgroundColor:'#3576AE',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        评审中
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.inReviewCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="3">
                  <Card @click.native="cardClickDevelop"  :style="{backgroundColor:'#3576AE',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        开发中
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.developingCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="3">
                  <Card @click.native="cardClickTest" :style="{backgroundColor:'#1D68A7',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        测试中
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.testingCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="3">
                  <Card @click.native="cardClickTested" :style="{backgroundColor:'#0165A3',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        测试完成待上线
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.toBeReleasedCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="3">
                  <Card @click.native="cardClickLaunch" :style="{backgroundColor:'#015C9C',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        已上线
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.releasedCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="3">
                  <Card @click.native="cardClickClosed" :style="{backgroundColor:'#01599C',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        效果评估完成
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.closedCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <!--<Col span="8">-->
                  <!--<Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>-->
                  <!--<div :style="{color:'#ffffff',height:'96px'}">-->
                  <!--<div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">-->
                  <!--交付速度(PD)（评审/开发/联调/测试/上线）-->
                  <!--</div>-->
                  <!--<div style="display: flex;padding-top: 15px">-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">-->
                  <!--{{reviewed}}-->
                  <!--<span style="font-size: 16px">PD</span>-->
                  <!--</div>-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">-->
                  <!--{{developed}}-->
                  <!--<span style="font-size: 16px">PD</span>-->
                  <!--</div>-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">-->
                  <!--{{jointed}}-->
                  <!--<span style="font-size: 16px">PD</span>-->
                  <!--</div>-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">-->
                  <!--{{tested}}-->
                  <!--<span style="font-size: 16px">PD</span>-->
                  <!--</div>-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">-->
                  <!--{{launch}}-->
                  <!--<span style="font-size: 16px">PD</span>-->
                  <!--</div>-->
                  <!--</div>-->
                  <!--</div>-->
                  <!--</Card>-->
                  <!--</Col>-->
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>需求交付数据: </span>
                <Row type="flex" style="margin-top: 20px;margin-left: 40%;margin-bottom: 5px">
                  <div style="background-color: #8192D6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px">: 评审时长;</span>
                  <div style="background-color: #20B2AA;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px">: 开发时长;</span>
                  <div style="background-color: #c1405a;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px">: 测试时长;</span>
                  <div style="background-color: #c581d6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px">: 上线前等待时长;</span>
                </Row>
                <Row style="margin-top: 10px" :style="{'height': deliveryheight}">
                  <!--<Col span="21">-->
                  <!--<Row type="flex">-->
                  <!--<div class="reqreviewChart" :style="{width: 10 + '%'}">评审PD:{{10}}%-->
                  <!--</div>-->
                  <!--<div class="techreviewChart" :style="{width: 20 + '%'}">-->
                  <!--<span style="padding-top: 10px">开发PD:{{20}}%</span>-->
                  <!--</div>-->
                  <!--<div class="devChart" :style="{width: 30 + '%'}">-->
                  <!--<span style="margin-top: 20px">测试PD:{{30}}%</span>-->
                  <!--</div>-->
                  <!--<div class="testChart" :style="{width: 20 + '%'}">上线PD:{{20}}%-->
                  <!--</div>-->
                  <!--<div class="otherChart" :style="{width: 15 + '%'}">{{20}}%-->
                  <!--</div>-->
                  <!--</Row>-->
                  <!--</Col>-->
                  <Col span="24">
                  <div id="deliveryspeedchart" style="padding-top:10px; margin-right:5px;max-height: 3000px" :style='deliveryheight'></div>
                  </Col>
                </Row>
                <Row style="margin-top: 10px">
                  <Col span="24">
                  <div id="deliverybubblechart" style="padding-top:10px; margin-right:5px;max-height: 3000px"></div>
                  </Col>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>整体数据: </span>
                <Row type="flex" style="margin-top: 10px">
                  <Col span="8">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        延期数量 （延期提测/不符合预期测试周期/延期上线）
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'34%'}">
                          {{transferDelayCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'34%'}">
                          {{testDelayCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'32%'}">
                          {{launchDelayCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="8">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        测试质量 （有效bug数/有效bug占比/工时平均有效bug）
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'30%'}">
                          {{validBugCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'30%'}">
                          {{validBugRatio}}
                          <span style="font-size: 16px"></span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'40%'}">
                          {{averageBugPerDay}}
                          <span style="font-size: 16px">个/PD</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="8">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        线上质量 （线上bug/线上故障/平均潜藏时长/平均解决时长）
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'15%'}">
                          {{validOnlineBugCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'20%'}">
                          {{validFaultCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'35%'}">
                          {{averageHideDuration}}
                          <span style="font-size: 16px">h</span>
                        </div>
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'30%'}">
                          {{averageDuration}}
                          <span style="font-size: 16px">h</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                <Row type="flex" style="margin-top: 10px">
                  <Col span="4">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        联调占比
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{jointDebugRatio}}
                          <span style="font-size: 16px"></span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        开发测试比
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{devTestRatio}}
                          <span style="font-size: 16px">:1</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        提测
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{reqstatistics.transferedCnt}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <!--<Col span="4">-->
                  <!--<Card :style="{backgroundColor:'#2d8cf0',marginLeft:'0px',marginRight:'10px'}" dis-hover>-->
                  <!--<div :style="{color:'#ffffff',height:'96px'}">-->
                  <!--<div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">-->
                  <!--平均交付速度-->
                  <!--</div>-->
                  <!--<div style="display: flex;padding-top: 15px">-->
                  <!--<div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">-->
                  <!--{{reqstatistics.averageDeliverPD}}-->
                  <!--<span style="font-size: 16px">天/需求</span>-->
                  <!--</div>-->
                  <!--</div>-->
                  <!--</div>-->
                  <!--</Card>-->
                  <!--</Col>-->
                </Row>
              </TimelineItem>
              <TimelineItem>
                <Row type="flex">
                  <span>需求详细: </span>
                  <span style="margin-left: 20px;margin-right: 2px">( 开发时长:</span>
                  <div class="onlyleftChart" :style="{width: 30 + 'px'}">
                  </div>
                  <span style="margin-left: 10px;margin-right: 2px">测试时长:</span>
                  <div class="onlyrightChart" :style="{width: 30 + 'px'}">
                  </div>
                  <span style="margin-left: 10px;margin-right: 2px">产品需求:</span>
                  <div class="reqChart" :style="{width: 15 + 'px'}">
                  </div>
                  <span style="margin-left: 1px;margin-right: 2px">边框</span>
                  <span style="margin-left: 10px;margin-right: 2px">技术需求:</span>
                  <div class="techChart" :style="{width: 15 + 'px'}">
                  </div>
                  <span style="margin-left: 1px;margin-right: 2px">边框</span>
                  <span style="margin-left: 3px">)</span>
                  <Select v-model="filterStatus" multiple clearable style="width:auto;margin-left: 10px;margin-top: -8px" placeholder="请选择需求状态" @on-change="filterReq">
                    <Option v-for="item in filterReqStatusList" :value="item" :key="item">{{ item }}</Option>
                  </Select>
                  <Select v-model="filterType" multiple clearable style="width:auto;margin-left: 3px;margin-top: -8px" placeholder="请选择需求类别" @on-change="filterReq">
                    <Option v-for="item in filterReqTypeList" :value="item" :key="item">{{ item }}</Option>
                  </Select>
                </Row>
                <Row type="flex" style="margin-top: -15px">
                  <Col span="6" v-for="item in jiraIssueDisplayList" :key="item">
                  <Card :bordeerror="true" :style="noticeStyle" v-if="jiraIssueDictOrigin[item].reqType === '需求' || jiraIssueDictOrigin[item].reqType === '产品需求'" style="border-left-color: #19be6b; border-left-width: 4px; min-height: 300px">
                    <Row>
                      <div style="font-weight: bolder; font-size: 18px; ">
                        <i class="fa fa-list-ul" style="padding-right: 5px"></i>
                        <a target="_blank" :href=getIssueUrl(item)>{{item}} {{jiraIssueDictOrigin[item].reqSummary}}</a>
                        <Tag v-if="jiraIssueDictOrigin[item].status === '关闭'" style="margin-top: 2px;margin-left: 8px" color="success">{{jiraIssueDictOrigin[item].status}}</Tag>
                        <Tag v-else style="margin-top: 2px;margin-left: 8px" color="warning">{{jiraIssueDictOrigin[item].status}}</Tag>
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>需求评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].reqReviewCnt}}</span>次<span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].reqReviewHours}}</span>h, 技术评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].techReviewCnt}}</span>次<span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].techReviewHours}}</span>h
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <Row type="flex">
                          <div v-if="jiraIssueDictOrigin[item].planTransferTime !== ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span>
                          </div>
                          <div v-if="jiraIssueDictOrigin[item].planTransferTime === ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planTransferTime, jiraIssueDictOrigin[item].actualTransferTime) === 1">
                            ; 实际提测时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planTransferTime, jiraIssueDictOrigin[item].actualTransferTime) === -1">
                            ; 实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planTransferTime, jiraIssueDictOrigin[item].actualTransferTime) === -2">
                            ; 实际提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planTransferTime, jiraIssueDictOrigin[item].actualTransferTime) === 0">
                            ; 实际提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                        </Row>
                        <!--<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>-->
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <Row type="flex">
                          <div v-if="jiraIssueDictOrigin[item].planReleaseTime !== ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planReleaseTime}}</span>
                          </div>
                          <div v-if="jiraIssueDictOrigin[item].planReleaseTime === ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === 1">
                            ; 实际上线时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === -1">
                            ; 实际上线时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === -2">
                            ; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === 0">
                            ; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                        </Row>
                        <!--<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>-->
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>交付速度(PD):
                      </div>
                    </Row>
                    <Row style="padding-top: 15px; padding-left: 20px">
                      <div v-if="parseFloat(jiraIssueDictOrigin[item].developPD) === 0 && parseFloat(jiraIssueDictOrigin[item].testPD) !== 0" class="onlyrightChart" :style="{width: 100 + '%'}"><div style="font-size: 12px;margin-top: -3px">{{parseFloat(jiraIssueDictOrigin[item].testPD)}}</div>
                      </div>
                      <div v-if="parseFloat(jiraIssueDictOrigin[item].developPD) !== 0 && parseFloat(jiraIssueDictOrigin[item].testPD) === 0" class="onlyleftChart" :style="{width: 100 + '%'}"><div style="font-size: 12px;margin-top: -3px">{{parseFloat(jiraIssueDictOrigin[item].developPD)}}</div>
                      </div>
                      <Row v-if="parseFloat(jiraIssueDictOrigin[item].developPD) !== 0 && parseFloat(jiraIssueDictOrigin[item].testPD) !== 0" type="flex">
                        <div class="leftChart" :style="{width: parseFloat(jiraIssueDictOrigin[item].developPD) / (parseFloat(jiraIssueDictOrigin[item].developPD) + parseFloat(jiraIssueDictOrigin[item].testPD)) * 100 + '%'}"><div style="font-size: 12px;margin-top: -3px">{{parseFloat(jiraIssueDictOrigin[item].developPD)}}</div>
                        </div>
                        <div class="rightChart" :style="{width: parseFloat(jiraIssueDictOrigin[item].testPD) / (parseFloat(jiraIssueDictOrigin[item].developPD) + parseFloat(jiraIssueDictOrigin[item].testPD)) * 100 + '%'}"><div style="font-size: 12px;margin-top: -3px">{{parseFloat(jiraIssueDictOrigin[item].testPD)}}</div>
                        </div>
                      </Row>
                      <div v-if="parseFloat(jiraIssueDictOrigin[item].developPD) === 0 && parseFloat(jiraIssueDictOrigin[item].testPD) === 0" class="noneChart" :style="{width: 100 + '%'}"><div style="font-size: 12px;margin-top: -5px">无</div>
                      </div>
                    </Row>
                  </Card>
                  <Card :bordeerror="true" :style="noticeStyle" v-if="jiraIssueDictOrigin[item].reqType === '技术需求'" style="border-left-color: #ff9900; border-left-width: 4px; min-height: 280px">
                    <Row>
                      <div style="font-weight: bolder; font-size: 18px; ">
                        <i class="fa fa-list-ul" style="padding-right: 5px"></i>
                        <a target="_blank" :href=getIssueUrl(item)>{{item}} {{jiraIssueDictOrigin[item].reqSummary}}</a>
                        <Tag v-if="jiraIssueDictOrigin[item].status === '关闭'" style="margin-top: 2px;margin-left: 8px" color="success">{{jiraIssueDictOrigin[item].status}}</Tag>
                        <Tag v-else style="margin-top: 2px;margin-left: 8px" color="warning">{{jiraIssueDictOrigin[item].status}}</Tag>
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>技术评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].techReviewCnt}}</span> 次<span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].techReviewHours}}</span> h
                      </div>
                    </Row>
                    <!--<Row style="padding-top: 15px">-->
                    <!--<div style="font-weight: bolder; font-size: 14px">-->
                    <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planReleaseTime}}</span> ;实际上线时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>-->
                    <!--</div>-->
                    <!--</Row>-->
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <Row type="flex">
                          <div v-if="jiraIssueDictOrigin[item].planReleaseTime !== ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planReleaseTime}}</span>
                          </div>
                          <div v-if="jiraIssueDictOrigin[item].planReleaseTime === ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === 1">
                            ; 实际上线时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === -1">
                            ; 实际上线时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === -2">
                            ; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div v-if="comparetime(jiraIssueDictOrigin[item].planReleaseTime, jiraIssueDictOrigin[item].actualReleaseTime) === 0">
                            ; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                        </Row>
                        <!--<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>-->
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>交付速度(PD):
                      </div>
                    </Row>
                    <Row style="padding-top: 15px; padding-left: 20px">
                      <!--<div style="font-weight: bolder; font-size: 14px;">-->
                      <!--<div style="background-color: #2d8cf0"></div>开发时长:  <span style="color: #ff0000; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 联调时长:  <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 测试时长:  <span style="color: #2d8cf0; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 上线时长:  <span style="color: #2d8cf0; font-weight: bolder; padding-left: 5px">{{}}</span> PD。-->
                      <!--</div>-->
                      <!--<Tag v-if="getStageStatus(item, unit) === -1" >{{unit}}</Tag>-->
                      <!--<Tag v-if="getStageStatus(item, unit) === 0" color="error">{{unit}}</Tag>-->
                      <Tag v-if="jiraIssueDictOrigin[item].developPD" color="success">开发时长:{{jiraIssueDictOrigin[item].developPD}}</Tag>
                      <Tag v-if="jiraIssueDictOrigin[item].testPD" color="success">测试时长:{{jiraIssueDictOrigin[item].testPD}}</Tag>
                    </Row>
                  </Card>
                  </Col>
                </Row>
                <Row style="text-align: right; padding-top: 15px">
                  <Page :total="jiraIssueOriginList.length" :page-size-opts='[20,60,100]' :page-size='pageSize' @on-page-size-change="changePageSize" @on-change="changePage" :current='currentPage'></Page>
                </Row>
              </TimelineItem>
            </Timeline>
            </Col>
          </Row>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  // import axios from 'axios'
  // import router from '@/router'
  import CommonDirection from './CommonDirection'
  import Highcharts from 'highcharts/highstock'
  import Xrange from 'highcharts/modules/xrange'
  import HighchartsMore from 'highcharts/highcharts-more'
  Xrange(Highcharts)
  HighchartsMore(Highcharts)

  Bus.$on('refreshhomepagedelayinfo', function () {
    // console.log(Bus.processMetricsUnitObject)
    Bus.analyticsHomepageUnitObject.transferDelayCnt = Bus.transferDelayCnt
    Bus.analyticsHomepageUnitObject.testDelayCnt = Bus.testDelayCnt
    Bus.analyticsHomepageUnitObject.launchDelayCnt = Bus.launchDelayCnt
    Bus.analyticsHomepageUnitObject.devTestRatio = Bus.devTestRatio
    Bus.analyticsHomepageUnitObject.jointDebugRatio = Bus.jointDebugRatio
  })
  Bus.$on('refreshhomepagetestinfo', function () {
    // console.log(Bus.processMetricsUnitObject)
    Bus.analyticsHomepageUnitObject.validBugCnt = Bus.validBugCnt
    Bus.analyticsHomepageUnitObject.validBugRatio = Bus.validBugRatio
    Bus.analyticsHomepageUnitObject.averageBugPerDay = Bus.averageBugPerDay
  })
  Bus.$on('refreshhomepageonlineinfo', function () {
    // console.log(Bus.processMetricsUnitObject)
    Bus.analyticsHomepageUnitObject.validFaultCnt = Bus.validFaultCnt
    Bus.analyticsHomepageUnitObject.validOnlineBugCnt = Bus.validOnlineBugCnt
    Bus.analyticsHomepageUnitObject.averageDuration = Bus.averageDuration
    Bus.analyticsHomepageUnitObject.averageHideDuration = Bus.averageHideDuration
  })
  Bus.$on('refreshmarketstatistics', function () {
    Bus.analyticsHomepageUnitObject.reqstatistics = Bus.marketstatistics
  })
  Bus.$on('refreshmarketdetail', function () {
    Bus.analyticsHomepageUnitObject.handlestructuredata(Bus.marketdetail)
    Bus.analyticsHomepageUnitObject.handledeliverydata(Bus.marketdetail)
  })

  let noticeStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '30px'
  }
  export default {
    components: {CommonDirection},
    name: 'analytics-homepage-unit',
    data: function () {
      // let date = new Date()
      // let dateRange = []
      // // 设置初始时间
      // let end = this.getTimeString(date)
      // date.setDate(date.getDate() - 7)s
      // let start = this.getTimeString(date)
      // dateRange.push(start)
      // dateRange.push(end)
      return {
        isLoading: false,
        visiable: true,
        noticeStyle: noticeStyle,
        transferDelayCnt: 0,
        testDelayCnt: 0,
        launchDelayCnt: 0,
        averageBugPerDay: '',
        validBugRatio: '',
        validBugCnt: 0,
        pageSize: 20,
        currentPage: 1,
        deliveryheight: {minHeight: '500px'},
        devTestRatio: '',
        jointDebugRatio: '',
        validFaultCnt: '',
        validOnlineBugCnt: '',
        averageDuration: '',
        averageHideDuration: '',
        deliveryReq: [],
        deliverydata: [],
        deliverybubble: [],
        backupdisplaylist: [],
        reqstatistics: {},
        marketdetail: [],
        filterStatus: [],
        backupOriginList: [],
        filterType: [],
        jiraIssueDisplayList: [],
        jiraIssueOriginList: [],
        filterReqStatusList: [
          '评审中', '开发中', '测试中', '测试完成待上线', '已上线', '效果评估完成'
        ],
        filterReqTypeList: [
          '产品需求', '技术需求'
        ],
        jiraIssueDictOrigin: {}
      }
    },
    methods: {
      getIssueUrl: function (item) {
        return 'https://flow.sankuai.com/browse/' + item
      },
      comparetime: function (plantime, actualtime) {
        let start = []
        let end = []
        let result = -3
        if (plantime !== '' && actualtime !== '') {
          start = plantime.split('-')
          var startdate = new Date(start[0], parseInt(start[1] - 1), start[2])
          end = actualtime.split('-')
          var enddate = new Date(end[0], parseInt(end[1] - 1), end[2])
          if (startdate > enddate) {
            result = 1
          } else if (startdate < enddate) {
            result = -1
          } else {
            result = 0
          }
        }
        if (!actualtime) {
          result = -2
        }
        return result
      },
      setfilterreqtype: function (type) {
        let result = []
        for (let each in this.jiraIssueDictOrigin) {
          if (type === '产品需求') {
            if (this.jiraIssueDictOrigin[each].reqType === '需求' || this.jiraIssueDictOrigin[each].reqType === '产品需求') {
              result.push(each)
            }
          } else {
            if (this.jiraIssueDictOrigin[each].reqType === type) {
              result.push(each)
            }
          }
        }
        return result
      },
      setfilterreqstatus: function (status) {
        let result = []
        for (let each in this.jiraIssueDictOrigin) {
          if (this.jiraIssueDictOrigin[each].status === status) {
            result.push(each)
          }
        }
        return result
      },
      cardClickReview () {
        this.filterStatus = ['评审中']
        this.filterReq()
      },
      cardClickDevelop () {
        this.filterStatus = ['开发中']
        this.filterReq()
      },
      cardClickTest () {
        this.filterStatus = ['测试中']
        this.filterReq()
      },
      cardClickTested () {
        this.filterStatus = ['测试完成待上线']
        this.filterReq()
      },
      cardClickLaunch () {
        this.filterStatus = ['已上线']
        this.filterReq()
      },
      cardClickClosed () {
        this.filterStatus = ['效果评估完成']
        this.filterReq()
      },
      filterReq: function () {
        let originDisplay = []
        let reqTypeSet = []
        let reqStatusSet = []
        if (this.filterStatus.length === 0 && this.filterType.length === 0) {
          originDisplay = this.backupOriginList
        } else {
          if (this.filterType.length === 0) {
            reqTypeSet = this.backupOriginList
          } else {
            for (let type of this.filterType) {
              reqTypeSet = this.getUnion(reqTypeSet, this.setfilterreqtype(type))
            }
          }
          if (this.filterStatus.length === 0) {
            reqStatusSet = this.backupOriginList
          } else {
            for (let status of this.filterStatus) {
              reqStatusSet = this.getUnion(reqStatusSet, this.setfilterreqstatus(status))
            }
          }
          originDisplay = this.getIntersection(reqTypeSet, reqStatusSet)
        }
        this.jiraIssueOriginList = originDisplay
        this.changePage(1)
        // this.changeCurrentPage(this.jiraIssueOriginList)
      },
      handlestructuredata: function (data) {
        this.jiraIssueOriginList = []
        this.jiraIssueDictOrigin = {}
        this.backupOriginList = []
        if (data) {
          for (const each of data) {
            this.jiraIssueOriginList.push(each.reqKey)
            this.jiraIssueDictOrigin[each.reqKey] = each
          }
          this.backupOriginList = this.jiraIssueOriginList
          this.changeCurrentPage(this.jiraIssueOriginList)
        }
      },
      handledeliverydata: function (data) {
        const self = this
        // console.log('data', data)
        this.deliveryReq = []
        this.deliverydata = []
        this.deliverybubble = []
        this.deliveryspeedhighchart()
        this.deliverybubblechart()
        let num = 0
        for (const each in data) {
          if (data[each].status === '已上线' || data[each].status === '效果评估完成') {
            let createTime, startDevTime, startTestTime, endTestTime, actualReleaseTime, deliveryTime
            // if (data[each].createdTime && data[each].actualReleaseTime && this.comparetime(data[each].createdTime, data[each].actualReleaseTime) === -1) {
            //   createTime = this.splittime(data[each].createdTime)
            //   actualReleaseTime = this.splittime(data[each].actualReleaseTime)
            //   this.deliverydata.push({
            //     x: Date.UTC(createTime[0], createTime[1], createTime[2]),
            //     x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1], actualReleaseTime[2]),
            //     y: num,
            //     color: '#8192D6'
            //   })
            //   this.deliveryReq.push(data[each].reqKey)
            //   num += 1
            // }
            if (data[each].createdTime && data[each].actualReleaseTime && this.comparetime(data[each].createdTime, data[each].actualReleaseTime) === -1) {
              createTime = this.splittime(data[each].createdTime)
              actualReleaseTime = this.splittime(data[each].actualReleaseTime)
              if (data[each].startDevTime) {
                startDevTime = this.splittime(data[each].startDevTime)
                this.deliverydata.push({
                  x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2]),
                  x2: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                  y: num,
                  name: data[each].reqSummary,
                  key: data[each].reqKey,
                  url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                  period: '创建到开始开发',
                  z: parseFloat(data[each].deliverPD),
                  starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2])),
                  endtime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                  color: '#8192D6'
                })
                if (data[each].startTestTime) {
                  startTestTime = this.splittime(data[each].startTestTime)
                  this.deliverydata.push({
                    x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                    x2: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                    y: num,
                    name: data[each].reqSummary,
                    key: data[each].reqKey,
                    url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                    period: '开始开发到开始测试',
                    z: parseFloat(data[each].deliverPD),
                    starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                    endtime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                    color: '#20B2AA'
                  })
                  if (data[each].endTestTime) {
                    endTestTime = this.splittime(data[each].endTestTime)
                    this.deliverydata.push({
                      x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                      x2: Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '开始测试到测试完成',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2])),
                      color: '#c1405a'
                    })
                    this.deliverydata.push({
                      x: Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2]),
                      x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '测试完成到上线',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                      color: '#c581d6'
                    })
                  } else {
                    this.deliverydata.push({
                      x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                      x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '开始测试到上线',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                      color: '#c1405a'
                    })
                  }
                } else {
                  this.deliverydata.push({
                    x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                    x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                    y: num,
                    name: data[each].reqSummary,
                    key: data[each].reqKey,
                    url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                    period: '开始开发到上线',
                    z: parseFloat(data[each].deliverPD),
                    starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                    endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                    color: '#20B2AA'
                  })
                }
              } else {
                this.deliverydata.push({
                  x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2]),
                  x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                  y: num,
                  name: data[each].reqSummary,
                  key: data[each].reqKey,
                  url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                  period: '创建到上线',
                  z: parseFloat(data[each].deliverPD),
                  starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2])),
                  endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                  color: '#8192D6'
                })
              }
              this.deliveryReq.push(data[each].reqKey)
              num += 1
            }
            // console.log('pd', parseFloat(data[each].deliverPD))
            if (data[each].actualReleaseTime && parseFloat(data[each].deliverPD) !== 0) {
              deliveryTime = this.splittime(data[each].actualReleaseTime)
              this.deliverybubble.push({
                x: Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2]),
                y: parseFloat(data[each].deliverPD),
                z: parseFloat(data[each].deliverPD),
                name: data[each].reqSummary,
                key: data[each].reqKey,
                url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                time: this.handlexAxis(Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2]))
              })
            }
          }
        }
        // console.log('y', this.deliveryReq)
        // console.log('x', this.deliverydata)
        setTimeout(function () {
          // console.log(self.deliveryReq.length)
          self.refreshHeight(self.deliveryReq.length)
        }, 0)
      },
      splittime: function (data) {
        let result = data.split('-')
        return result
      },
      refreshHeight: function (data) {
        const self = this
        // console.log(data)
        let height = data * 15 + 25 > 500 ? data * 15 + 25 : 500
        this.deliveryheight = {minHeight: height + 'px'}
        // console.log(height)
        setTimeout(function () {
          self.deliveryspeedhighchart()
          self.deliverybubblechart()
        }, 100)
      },
      deliverybubblechart: function () {
        Highcharts.chart('deliverybubblechart', {
          chart: {
            type: 'bubble',
            zoomType: 'xy'
          },
          title: {
            text: '需求创建到上线交付周期'
          },
          subtitle: {
            text: '横坐标代表需求交付时间，纵坐标为需求交付耗费时间，点击可查看具体需求'
          },
          tooltip: {
            enabled: true,
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>交付时间 :&nbsp</th><td>{point.time}</td></tr>' +
            '<tr><th>需求周期 :&nbsp</th><td>{point.y}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open(event.point.url)
                }
              }
            }
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '交付周期（PD）'
            },
            labels: {
              format: '{value} PD'
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: this.reqstatistics.averageDeliverPD,
              width: 2,
              label: {
                text: '平均:' + this.reqstatistics.averageDeliverPD,
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -50
              }
            }],
            min: 0
          },
          // tooltip: {
          //   dateTimeLabelFormats: {
          //     day: '%Y-%m-%d'
          //   }
          // },
          series: [{
            name: '交付分布图',
            data: this.deliverybubble
          }],
          credits: {
            enabled: false
          }
        })
      },
      deliveryspeedhighchart: function () {
        Highcharts.chart('deliveryspeedchart', {
          chart: {
            type: 'xrange'
          },
          title: {
            text: ''
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '需求key'
            },
            categories: this.deliveryReq,
            reversed: true
          },
          legend: {
            enabled: true
          },
          tooltip: {
            dateTimeLabelFormats: {
              day: '%Y-%m-%d'
            },
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>{point.period} :&nbsp</th><td>{point.starttime}~{point.endtime}</td></tr>' +
            '<tr><th>需求周期 :</th><td>{point.z}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open(event.point.url)
                }
              }
            }
          },
          // colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '需求创建到上线交付时间',
            pointWidth: 8,
            data: this.deliverydata,
            dataLabels: {
              enabled: false
            }
          }],
          credits: {
            enabled: false
          }
        })
      },
      handlexAxis (time) {
        var date = new Date(time)
        return date.getFullYear() + '-' + parseInt(date.getMonth() + 1) + '-' + date.getDate()
      },
      getUnion: function (a, b) {
        // 并集
        return Array.from(new Set(a.concat(b)))
      },
      getIntersection: function (a, b) {
        // 交集
        let bSet = new Set(b)
        return Array.from(new Set(a.filter(v => bSet.has(v))))
      },
      getDifference: function (a, b) {
        // 差集
        let aSet = new Set(a)
        Array.from(new Set(a.concat(b).filter(v => !aSet.has(v) || !b.has(v))))
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage(this.jiraIssueOriginList)
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage(this.jiraIssueOriginList)
      },
      changeCurrentPage: function (data) {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.jiraIssueDisplayList = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.jiraIssueDisplayList.push(data[i])
        }
      }
    },
    mounted: function () {
      Bus.analyticsHomepageUnitObject = this
    }
  }
</script>

<style scoped>
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
  a:link {
    text-decoration: none;
  }
  a:active {
    text-decoration: none;
  }
  　a:hover {
    text-decoration: none;
  }
  　a:visited {
    text-decoration: none;
  }
  .reqreviewChart{
    background-color: #83C75D;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .techreviewChart{
    background-color: #5BBD2B;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    vertical-align: middle;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .devChart{
    background-color: #50A625;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .testChart{
    background-color: #489620;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .leftChart{
    background-color: #2f99c1;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .onlyleftChart{
    background-color: #2f99c1;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .onlyrightChart{
    background-color: #e8488b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .rightChart{
    background-color: #e8488b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .reqChart{
    background-color: #19be6b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .techChart{
    background-color: #ff9900;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .otherChart{
    background-color: #f3f3f3;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #000000;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .noneChart{
    background-color: #f3f3f3;
    height: 15px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #000000;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }
</style>
