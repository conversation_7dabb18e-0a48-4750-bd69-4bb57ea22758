<template>
  <div>
    <Row type="flex" style="width: 100%">
      <Col style="text-align: right;width: 100%;display: flex">
      <div>
        <Tooltip placement="bottom" content="点击查看实时统计数据说明">
          <a href="https://123.sankuai.com/km/page/47383369" target="_blank">数据说明</a>
        </Tooltip>
      </div>
      <div>
        <span :style="{fontWeight:'bolder',marginLeft:'10px'}">Analytics</span>
      </div>
      <Cascader v-model="value1" :transfer=true :data="data1" :change-on-select=true @on-change="changeselect" :clearable=false placeholder="请选择要筛选的方向" style="width: 420px;margin-top: -8px;padding-left: 10px"></Cascader>
      <Select :transfer=true
              style="margin-left:10px;margin-top: -8px;width: 100px;text-align: left"
              v-model="value2" :label-in-value="true" @on-change="selectlistchanged">
        <Option v-for="item in selectList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <DatePicker style="margin-top: -8px;width: 200px;margin-left: 10px" v-model="value3" :options="options2" type="daterange" placement="bottom-end" placeholder="请选择时间间隔" @on-change="changedatepicker"></DatePicker>
      <Select v-model="reqtype" multiple clearable style="margin-top: -8px;margin-left: 10px; text-align: left; width:auto" placeholder="请选择需求类型" @on-change="selectreqtype">
        <Option v-for="item in reqtypelist" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <div style="margin-left: 10px;margin-top: -8px">
        <Button @click="postData(value1,value2,value3,reqtype)" type="primary">查看</Button>
      </div>
      <div v-if="display5" style="margin-top: -8px;margin-left: 50px">
        <Button type="primary" @click="modal1 = true">导出统计数据</Button>
        <Modal v-model="modal1" title="提示：" @on-ok="ok" @on-cancel="cancel1">
          <Row>
            <Col order="1" span="1">
            <Icon type="help-circled"
                  style="padding-left:13px;padding-top: 7px;font-size: larger;font-weight: bolder"
                  color="rgb(255, 165, 0)"></Icon>
            </Col>
            <Col order="2" span="20">
            <p style="font-size: 14px;padding-left: 17px;color:rgb(255, 165, 0);padding-top: 4px;">是否导出数据到Wiki？</p>
            </Col>
          </Row>
        </Modal>
      </div>
      <!--<div v-if="display2">-->
      <!--<Modal v-model="modal6" title="异常task提醒" @on-ok="asyncOK('invalidData')" ok-text="导出" @on-cancel="cancel()">-->
      <!--<div style="max-height: 400px;overflow-y: auto">-->
      <!--<Table id="invalidData" stripe border :columns="columns4" :data="invalidData"></Table>-->
      <!--</div>-->
      <!--</Modal>-->
      <!--</div>-->
      <div style="margin-left: 15%;margin-top: -10px">
        <Button class="tool-button" icon="md-arrow-forward" type="dashed" size="small" @click="toOldRouter()">进入新版过程度量</Button>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  import router from '@/router'
  import {Bus} from '@/global/bus'
  import {analyticsbaseAPI, analyticswikiAPI} from '@/global/variable'
  import moment from 'moment'

  let tableData = []
  Bus.$on('refreshRequirmentTaskType', function (data) {
    Bus.commonDirectionObject.setreqType(data)
  })
  Bus.$on('showProcessMetricsTab', function () {
    Bus.commonDirectionObject.display5 = true
  })
  Bus.$on('refreshDistribution', function () {
    Bus.commonDirectionObject.distribution = true
    // Bus.commonDirectionObject.getProcessData(data)
  })
  Bus.$on('refreshgetprocessdata', function (data) {
    Bus.commonDirectionObject.getProcessData(data)
  })

  export default {
    name: 'common-direction',
    data: function () {
      return {
        reqtype: [],
        modal1: false,
        searchapi: true,
        flag: false,
        columns4: [
          {
            title: 'task key',
            key: 'issueKey',
            sortable: true
          },
          {
            title: 'task名称',
            key: 'summary',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: 'https://flow.sankuai.com/browse/' + params.row['issueKey']
                  },
                  on: {
                    click: () => {
                      window.open('https://flow.sankuai.com/browse/' + params.row['issueKey'])
                    }
                  }
                }, params.row['summary'])
              ])
            }
          },
          {
            title: '错误原因',
            key: 'reason',
            sortable: true
          },
          {
            title: '经办人',
            key: 'assignee',
            sortable: true
          }
        ],
        tableData: tableData,
        pattern: '',
        display2: false,
        display3: false,
        display5: false,
        distribution: false,
        modal6: false,
        invalidData: [],
        value2: '',
        value1: [],
        value3: [],
        value4: 0,
        data1: [],
        reqtypelist: [
          {
            value: '产品需求',
            label: '产品需求'
          }, {
            value: '技术需求',
            label: '技术需求'
          }
        ],
        selectList: [
          {
            value: 'CUSTOMED',
            label: '自定义'
          },
          {
            value: 'QUARTER',
            label: '按季度'
          },
          {
            value: 'MONTH',
            label: '按月份'
          }
        ],
        options2: {
          shortcuts: [
            {
              text: 'A week',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                return [start, end]
              }
            },
            {
              text: 'A month',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                return [start, end]
              }
            },
            {
              text: '3 month',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                return [start, end]
              }
            }
          ]
        }
      }
    },
    methods: {
      getreqtype: function () {
        if (parseInt(this.$route.query.reqtype, 0) === 0) {
          this.reqtype.push('技术需求')
          this.reqtype.push('产品需求')
        } else if (parseInt(this.$route.query.reqtype, 0) === 1) {
          this.reqtype.push('产品需求')
        } else if (parseInt(this.$route.query.reqtype, 0) === 2) {
          this.reqtype.push('技术需求')
        } else {
          this.reqtype.push('技术需求')
          this.reqtype.push('产品需求')
        }
      },
      selectreqtype: function (value) {
        const self = this
        self.flag = false
        if (value.length === 1) {
          if (value[0] === '技术需求') {
            Bus.$emit('refreshRequirmentTaskType', '2')
          } else {
            Bus.$emit('refreshRequirmentTaskType', '1')
          }
        } else if (value.length === 2) {
          Bus.$emit('refreshRequirmentTaskType', '0')
        } else {
          self.flag = true
          self.$Message.info('产品需求、技术需求至少选一个！')
        }
        // setTimeout(function () {
        //   if (!self.flag) {
        //     self.postData(self.value1, self.value2, self.value3, self.reqtype)
        //   }
        // }, 100)
      },
      link () {
        window.open('https://wiki.sankuai.com/pages/viewpage.action?pageId=1350559139')
      },
      setOption (value) {
        if (value.value === 'QUARTER') {
          this.secondList = [
            {
              value: 'this-season',
              label: '本季度'
            },
            {
              value: 'last-season',
              label: '上季度'
            }
          ]
        }
        if (value.value === 'MONTH') {
          this.secondList = [
            {
              value: 'this-month',
              label: '本月'
            },
            {
              value: 'last-month',
              label: '上月'
            }
          ]
        }
      },
      changedatepicker (value) {
        // console.log(value, typeof value[0])
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value.length === 2) {
          dict.start = value[0]
          dict.end = value[1]
          router.push({ path: `/homepage/analytics/${this.$route.params.tab}`, query: dict })
        }
      },
      setreqType (value) {
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value) {
          dict.reqtype = value
          router.push({ path: `/homepage/analytics/${this.$route.params.tab}`, query: dict })
        }
      },
      selectlistchanged (value) {
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value.value) {
          dict.type = value.value
          router.push({ path: `/homepage/analytics/${this.$route.params.tab}`, query: dict })
        }
      },
      changeselect (value, selectedData) {
        let dict = {
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          // project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (selectedData.length === 2 && selectedData[1].value === '0') {
          dict = {
            bg: selectedData[0].value,
            bizline: selectedData[1].value,
            type: this.$route.query.type,
            start: this.$route.query.start,
            end: this.$route.query.end,
            reqtype: this.$route.query.reqtype
          }
        } else if (selectedData.length === 3 && selectedData[2].value === '0') {
          dict = {
            bg: selectedData[0].value,
            bizline: selectedData[1].value,
            group: selectedData[2].value,
            type: this.$route.query.type,
            start: this.$route.query.start,
            end: this.$route.query.end,
            reqtype: this.$route.query.reqtype
          }
        } else if (selectedData.length === 4) {
          dict = {
            bg: selectedData[0].value,
            bizline: selectedData[1].value,
            group: selectedData[2].value,
            project: selectedData[3].value,
            type: this.$route.query.type,
            start: this.$route.query.start,
            end: this.$route.query.end,
            reqtype: this.$route.query.reqtype
          }
        }
        router.push({ path: `/homepage/analytics/${this.$route.params.tab}`, query: dict })
      },
      getBizlineData (self) {
        // console.log(analyticsbaseAPI + '/common/bizLine/all')
        axios.get(analyticsbaseAPI + '/common/bizLine/new', {
          timeout: 10000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0) {
            let bizData = []
            for (let bgInfo in message['data']['data']) {
              let bgBizInfo = message['data']['data'][bgInfo]
              let bgTop = {}
              bgTop['value'] = bgBizInfo['id']
              bgTop['label'] = bgBizInfo['name']
              bgTop['children'] = self.getBGData(bgBizInfo['bizInfoList'])
              bizData.push(bgTop)
            }
            self.data1 = bizData
          } else {
            console.log(message['data']['msg'])
          }
          // Bus.$emit('refreshTable', message['data']['data'], self)
          // return message['data']['data']
        }).catch(function (error) {
          console.log(error)
        })
      },
      getBGData (data) {
        let bizData = []
        let tempBiz = {}
        tempBiz['value'] = '0'
        tempBiz['label'] = '全部'
        bizData.push(tempBiz)
        for (let eachBizLine in data) {
          let tempData = {}
          tempData['value'] = data[eachBizLine]['bizLineId']
          tempData['label'] = data[eachBizLine]['bizLineName']
          let tempBizData = []
          let tempGroup = {}
          tempGroup['value'] = '0'
          tempGroup['label'] = '全部'
          tempBizData.push(tempGroup)
          for (let groupList in data[eachBizLine]['groupList']) {
            let groupData = {}
            groupData['value'] = data[eachBizLine]['groupList'][groupList]['groupId']
            groupData['label'] = data[eachBizLine]['groupList'][groupList]['groupName']
            let finalData = data[eachBizLine]['groupList'][groupList]['projectInfoList']
            let projectData = []
            let tempProject = {}
            tempProject['value'] = '0'
            tempProject['label'] = '全部'
            projectData.push(tempProject)
            for (let project in finalData) {
              let childData = {}
              childData['label'] = finalData[project]['displayName']
              childData['value'] = finalData[project]['id']
              projectData.push(childData)
            }
            groupData['children'] = projectData
            tempBizData.push(groupData)
          }
          tempData['children'] = tempBizData
          bizData.push(tempData)
        }
        return bizData
      },
      getProcessData (data) {
        let resourceStats = []
        let timelineStats = []
        let evaluationStats = []
        Bus.transferDelayCnt = 0
        Bus.testDelayCnt = 0
        Bus.launchDelayCnt = 0
        Bus.devTestRatio = ''
        Bus.jointDebugRatio = ''
        if (data) {
          for (let period in data) {
            let eachBizData = []
            eachBizData = data[period]['periodProcessStats']
            for (let each in eachBizData) {
              if (data[period].name === '总计') {
                Bus.transferDelayCnt = eachBizData[each]['timelineStats'].transferDelayCnt
                Bus.testDelayCnt = eachBizData[each]['timelineStats'].testDelayCnt
                Bus.launchDelayCnt = eachBizData[each]['timelineStats'].launchDelayCnt
                Bus.devTestRatio = eachBizData[each]['resourceStats'].devTestRatio
                Bus.jointDebugRatio = eachBizData[each]['resourceStats'].jointDebugRatio
              }
              eachBizData[each]['resourceStats']['direction'] = data[period]['name']
              eachBizData[each]['timelineStats']['direction'] = data[period]['name']
              eachBizData[each]['evaluationStats']['direction'] = data[period]['name']
              eachBizData[each]['resourceStats']['period'] = each
              eachBizData[each]['timelineStats']['period'] = each
              eachBizData[each]['evaluationStats']['period'] = each
              resourceStats.push(eachBizData[each]['resourceStats'])
              timelineStats.push(eachBizData[each]['timelineStats'])
              evaluationStats.push(eachBizData[each]['evaluationStats'])
            }
          }
          // this.$Spin.hide()
        } else {
          // this.$Spin.hide()
        }
        if (this.$route.query.type === 'CUSTOMED') {
          Bus.$emit('refreshhomepagedelayinfo')
        }
        Bus.$emit('refreshResourceStat', resourceStats)
        Bus.$emit('refreshProcessTableLength', resourceStats.length)
        Bus.$emit('refreshTimelineStat', timelineStats)
        Bus.$emit('refreshEvaluationStat', evaluationStats)
        if (Bus.commonDirectionObject.distribution) {
          Bus.$emit('showProcessMetricsTab')
          // Bus.$emit('refreshisLoadingshow')
        }
      },
      getQualityData (data) {
        if (data) {
          let qualityStats = []
          Bus.qualityStats = []
          Bus.validBugCnt = 0
          Bus.validBugRatio = ''
          Bus.averageBugPerDay = ''
          // console.log('data:', data)
          for (let each in data) {
            let eachBizData = []
            eachBizData = data[each]['periodQualityStats']
            for (let period in eachBizData) {
              if (data[each].name === '总计') {
                Bus.validBugCnt = eachBizData[period].validBugCnt
                Bus.validBugRatio = eachBizData[period].validBugRatio
                Bus.averageBugPerDay = eachBizData[period].averageBugPerDay
              }
              eachBizData[period]['direction'] = data[each]['name']
              eachBizData[period]['period'] = period
              qualityStats.push(eachBizData[period])
            }
          }
          if (this.$route.query.type === 'CUSTOMED') {
            Bus.$emit('refreshhomepagetestinfo')
          }
          Bus.$emit('refreshQualityStat', qualityStats)
          Bus.$emit('refreshQualityTableLength', qualityStats.length)
        } else {
          Bus.$emit('refreshQualityStat', [])
        }
      },
      getEfficiencyData (data) {
        let rdEfficiencyStats = []
        let qaEfficiencyStats = []
        // let pmEfficiencyStats = []
        // console.log('人效data', data)
        if (data) {
          for (let period in data) {
            let eachBizData = []
            eachBizData = data[period]['efficiencyStats']
            for (let each in eachBizData) {
              eachBizData[each]['rdEfficiencyStats']['direction'] = data[period]['name']
              eachBizData[each]['qaEfficiencyStats']['direction'] = data[period]['name']
              // eachBizData[each]['evaluationStats']['direction'] = data[period]['name'] // pm
              eachBizData[each]['rdEfficiencyStats']['period'] = each
              eachBizData[each]['qaEfficiencyStats']['period'] = each
              // eachBizData[each]['evaluationStats']['period'] = each
              rdEfficiencyStats.push(eachBizData[each]['rdEfficiencyStats'])
              qaEfficiencyStats.push(eachBizData[each]['qaEfficiencyStats'])
              // evaluationStats.push(eachBizData[each]['evaluationStats'])
            }
          }
        }
        // console.log('人效数据', rdEfficiencyStats, qaEfficiencyStats)
        Bus.$emit('refreshRdEfficiencyStat', rdEfficiencyStats)
        Bus.$emit('refreshEfficiencyTableLength', rdEfficiencyStats.length)
        Bus.$emit('refreshQaEfficiencyStat', qaEfficiencyStats)
        // Bus.$emit('refreshEvaluationStat', qaEfficiencyStats)
        // if (Bus.processMetricsObject.distribution) {
        //   Bus.$emit('showProcessMetricsTab')
        // }
      },
      postData (value1, value2, value3, value4) {
        let self = this
        // console.log('pist value1', value1, value2, value3, value4)
        Bus.ifdisplayincompletedata = false
        let status = 1
        let type = 0
        if (value1.length === 0) {
          self.$Message.info('请选择业务线和方向！')
        } else if (value2 === '') {
          self.$Message.info('请选择时间范围！')
        } else if (value3.length === 0) {
          self.$Message.info('请选择时间！')
        } else if (value4.length === 0) {
          self.$Message.info('产品需求、技术需求至少选一个！')
        } else {
          self.$Spin.show()
          // Bus.$emit('refreshisLoadingfalse')
          status = 0
          if (value4.length === 1) {
            if (value4[0] === '技术需求') {
              type = 2
            } else {
              type = 1
            }
          } else if (value4.length === 2) {
            type = 0
          }
          let start = moment(value3[0]).format('YYYY-MM-DD')
          let end = moment(value3[1]).format('YYYY-MM-DD')
          let params = {
            bgId: value1[0],
            bizLineId: value1[1],
            groupId: value1[2],
            projectId: value1[3],
            periodType: value2,
            beginTime: start,
            endTime: end,
            reqType: type
          }
          if (status === 0) {
            // console.log('start emit ')
            this.getmarketstatistics(params) // 大盘统计数据
            this.getmarketdetail(params) // 大盘详情数据
            this.getinitProcessData(params) // 统计数据
            // this.getinitEfficiencyData(params) // 人效数据
            this.getinitOnlineQualityStats(params) // 线上数据
            if (params.periodType !== 'CUSTOMED') {
              let customparams = {
                bgId: value1[0],
                bizLineId: value1[1],
                groupId: value1[2],
                projectId: value1[3],
                periodType: 'CUSTOMED',
                beginTime: start,
                endTime: end,
                reqType: type
              }
              this.getMarketOtherData(customparams) // 统计数据
              // this.getinitEfficiencyData(params) // 人效数据
              // this.getinitOnlineQualityStats(params) // 线上数据
            }
          } else {
            self.$Spin.hide()
            self.$Message.info('存在未选择的参数！')
          }
        }
      },
      getMarketOtherData (params) {
        axios.get(analyticsbaseAPI + '/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0 || message['data']['status'] === 1) {
            let data = message['data']['data']
            for (let each in data) {
              let eachBizData = []
              let everyBizData = []
              eachBizData = data[each]['periodQualityStats']
              everyBizData = data[each]['periodProcessStats']
              for (let period in eachBizData) {
                if (data[each].name === '总计') {
                  Bus.validBugCnt = eachBizData[period].validBugCnt
                  Bus.validBugRatio = eachBizData[period].validBugRatio
                  Bus.averageBugPerDay = eachBizData[period].averageBugPerDay
                  Bus.$emit('refreshhomepagetestinfo')
                  break
                }
              }
              for (let period in everyBizData) {
                if (data[each].name === '总计') {
                  Bus.transferDelayCnt = everyBizData[period]['timelineStats'].transferDelayCnt
                  Bus.testDelayCnt = everyBizData[period]['timelineStats'].testDelayCnt
                  Bus.launchDelayCnt = everyBizData[period]['timelineStats'].launchDelayCnt
                  Bus.devTestRatio = everyBizData[period]['resourceStats'].devTestRatio
                  Bus.jointDebugRatio = everyBizData[period]['resourceStats'].jointDebugRatio
                  Bus.$emit('refreshhomepagedelayinfo')
                  break
                }
              }
            }
          }
        })
        axios.get(analyticsbaseAPI + '/online/issuestats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.onlineBugStats = []
          Bus.searchpattern = ''
          Bus.validOnlineBugCnt = 0
          Bus.validFaultCnt = 0
          Bus.averageDuration = ''
          Bus.averageHideDuration = ''
          if (message['data']['status'] === 0) {
            // Bus.$emit('refreshDistribution')
            let temp = message['data']['data']['tableData']
            for (const each in temp) {
              if (temp[each].directionName === '总计') {
                Bus.validOnlineBugCnt = temp[each].validBugCnt
                Bus.validFaultCnt = temp[each].validFaultCnt
                Bus.averageDuration = temp[each].averageDuration
                Bus.averageHideDuration = temp[each].averageHideDuration
                Bus.$emit('refreshhomepageonlineinfo')
                break
              }
            }
          } else {
            // self.$Spin.hide()
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getmarketstatistics (params) {
        axios.get(analyticsbaseAPI + '/overview/req/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.marketstatistics = {}
            Bus.marketstatistics = message['data']['data']
            Bus.$emit('refreshmarketstatistics')
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getmarketdetail (params) {
        axios.get(analyticsbaseAPI + '/overview/req/detail', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.marketdetail = []
            Bus.marketdetail = message['data']['data']
            Bus.$emit('refreshmarketdetail')
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitProcessData (params) {
        let self = this
        axios.get(analyticsbaseAPI + '/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.resourceStats = []
          Bus.timelineStats = []
          Bus.evaluationStats = []
          // console.log(message)
          Bus.flag = ''
          Bus.flag = message['data']['data'][0]['pattern']
          if (message['data']['status'] === 0) {
            // console.log('metrics', message['data']['data'])
            Bus.$emit('refreshDistribution')
            Bus.processCreatestats = []
            Bus.processCreatestats = message['data']['data']  // 先保存数据，用于wiki导出
            self.getProcessData(message['data']['data'])
            self.getQualityData(message['data']['data'])
            self.$Spin.hide()
          } else if (message['data']['status'] === 1) {
            self.getQualityData(message['data']['data'])
            let alertdata = JSON.parse(message['data']['msg'])
            Bus.processCreatestats = []
            Bus.processCreatestats = message['data']['data']
            Bus.$emit('refreshDistribution')
            Bus.incompletedata = []
            Bus.incompletedata = message['data']['data']
            let testdata = []
            for (let issuekey in alertdata) {
              let invalidIssue = []
              invalidIssue = alertdata[issuekey]
              testdata.push(invalidIssue)
            }
            // self.display2 = true
            // self.modal6 = true
            // self.invalidData = testdata
            if (self.$route.params.tab === 'process_data' || self.$route.params.tab === 'quality_data') {
              Bus.$emit('refreshshowincompletedata', testdata)
            } else {
              self.getProcessData(Bus.incompletedata)
            }
            self.$Spin.hide()
          } else {
            self.$Spin.hide()
            alert(message['data']['msg'])
            // }
          }
        }).catch(error => {
          self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitEfficiencyData (params) {
        let self = this
        axios.get(analyticsbaseAPI + '/efficiency/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.rdEfficiencyStats = []
          Bus.qaEfficiencyStats = []
          // Bus.pmEfficiencyStats = []
          // console.log(message)
          Bus.searchpattern = ''
          Bus.searchpattern = message['data']['data'][0]['pattern']
          if (message['data']['status'] === 0) {
            // console.log('metrics', message['data']['data'])
            // Bus.$emit('refreshDistribution')
            Bus.efficiencyCreatestats = []
            Bus.efficiencyCreatestats = message['data']['data']  // 先保存数据，用于wiki导出,人效的导出数据，后面可能使用
            self.getEfficiencyData(message['data']['data'])
          } else {
            // self.$Spin.hide()
            alert(message['data']['msg'])
            // }
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitOnlineQualityStats (params) {
        let self = this
        axios.get(analyticsbaseAPI + '/online/issuestats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.onlineBugStats = []
          Bus.searchpattern = ''
          Bus.validOnlineBugCnt = 0
          Bus.validFaultCnt = 0
          Bus.averageDuration = ''
          Bus.averageHideDuration = ''
          Bus.searchpattern = message['data']['data']['pattern']
          if (message['data']['status'] === 0) {
            // Bus.$emit('refreshDistribution')
            Bus.onlineBugStats = message['data']['data']  // 先保存数据，用于wiki导出,人效的导出数据，后面可能使用
            let temp = message['data']['data']['tableData']
            for (const each in temp) {
              if (temp[each].directionName === '总计') {
                Bus.validOnlineBugCnt = temp[each].validBugCnt
                Bus.validFaultCnt = temp[each].validFaultCnt
                Bus.averageDuration = temp[each].averageDuration
                Bus.averageHideDuration = temp[each].averageHideDuration
              }
            }
            if (self.$route.query.type === 'CUSTOMED') {
              Bus.$emit('refreshhomepageonlineinfo')
            }
            Bus.$emit('refreshOnlineQualityStat', Bus.onlineBugStats['tableData'].length)
          } else {
            // self.$Spin.hide()
            alert(message['data']['msg'])
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getrouterparams () {
        const self = this
        self.display3 = false
        self.value1 = []
        const dir = []
        const dict = {
          reqtype: '0',
          req: 0,
          bg: '2',
          bizline: '0'
        }
        if (self.$route.query.bg) {
          dir.push(parseInt(self.$route.query.bg, 0))
          dict.bg = this.$route.query.bg
        }
        if (self.$route.query.bizline) {
          if (self.$route.query.bizline === '0') {
            dir.push(self.$route.query.bizline)
          } else {
            dir.push(parseInt(self.$route.query.bizline, 0))
          }
          dict.bizline = this.$route.query.bizline
        }
        if (self.$route.query.group) {
          if (self.$route.query.group === '0') {
            dir.push(self.$route.query.group)
          } else {
            dir.push(parseInt(self.$route.query.group, 0))
          }
          dict.group = this.$route.query.group
        }
        if (self.$route.query.project) {
          if (self.$route.query.project === '0') {
            dir.push(self.$route.query.project)
          } else {
            dir.push(parseInt(self.$route.query.project, 0))
          }
          dict.project = this.$route.query.project
        }
        if (dir.length === 0) {
          dir.push(2)
          dir.push('0')
          dict.bg = '2'
          dict.bizline = '0'
        }
        self.value1 = dir
        if (self.$route.query.type) {
          self.value2 = self.$route.query.type
          dict.type = this.$route.query.type
        } else {
          self.value2 = 'CUSTOMED'
          dict.type = 'CUSTOMED'
        }
        self.value3 = []
        if (self.$route.query.start) {
          // console.log('start', self.$route.query.start, typeof self.$route.query.start)
          self.value3.push(self.$route.query.start)
          dict.start = self.$route.query.start
        } else {
          const startdate = new Date()
          startdate.setTime(startdate.getTime() - 3600 * 1000 * 24 * 7)
          self.value3.push(startdate.toISOString().substring(0, 10))
          dict.start = startdate.toISOString().substring(0, 10)
        }
        if (self.$route.query.end) {
          self.value3.push(self.$route.query.end)
          dict.end = self.$route.query.end
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.end = new Date().toISOString().substring(0, 10)
        }
        // console.log('value3', self.value3)
        let type = ''
        self.reqtype = []
        if (self.$route.query.reqtype) {
          if (self.$route.query.reqtype === '0') {
            self.reqtype.push('产品需求')
            self.reqtype.push('技术需求')
          } else if (self.$route.query.reqtype === '1') {
            self.reqtype.push('产品需求')
          } else if (self.$route.query.reqtype === '2') {
            self.reqtype.push('技术需求')
          }
          type = self.$route.query.reqtype
        } else {
          self.reqtype.push('产品需求')
          self.reqtype.push('技术需求')
          type = '0'
        }
        // console.log('type', type)
        dict.reqtype = type
        if (self.$route.params.tab !== 'undefined') {
          router.push({ path: `/homepage/analytics/${self.$route.params.tab}`, query: dict })
        } else {
          router.push({ path: '/homepage/analytics', query: dict })
        }
      },
      getinitparams (self) {
        self.display3 = false
        self.value1 = []
        const dir = []
        const dict = {
          reqtype: '0',
          req: 0
        }
        if (self.$route.query.bg) {
          dir.push(self.$route.query.bg)
          dict.bg = this.$route.query.bg
        } else {
          dict.bg = '2'
        }
        if (self.$route.query.bizline) {
          if (self.$route.query.bizline === '0') {
            dir.push(self.$route.query.bizline)
          } else {
            dir.push(parseInt(self.$route.query.bizline, 0))
          }
          dict.bizline = this.$route.query.bizline
        } else {
          dict.bizline = '0'
        }
        if (self.$route.query.group) {
          if (self.$route.query.group === '0') {
            dir.push(self.$route.query.group)
          } else {
            dir.push(parseInt(self.$route.query.group, 0))
          }
          dict.group = this.$route.query.group
        }
        if (self.$route.query.project) {
          if (self.$route.query.project === '0') {
            dir.push(self.$route.query.project)
          } else {
            dir.push(parseInt(self.$route.query.project, 0))
          }
          dict.project = this.$route.query.project
        }
        if (dir.length === 0) {
          dir.push('2')
          dir.push('0')
          dict.bg = '2'
          dict.bizline = '0'
        }
        self.value1 = dir
        if (self.$route.query.type) {
          self.value2 = self.$route.query.type
          dict.type = this.$route.query.type
        } else {
          self.value2 = 'CUSTOMED'
          dict.type = 'CUSTOMED'
        }
        self.value3 = []
        if (self.$route.query.start) {
          // console.log('start', self.$route.query.start, typeof self.$route.query.start)
          self.value3.push(self.$route.query.start)
          dict.start = self.$route.query.start
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.start = new Date().toISOString().substring(0, 10)
        }
        if (self.$route.query.end) {
          self.value3.push(self.$route.query.end)
          dict.end = self.$route.query.end
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.end = new Date().toISOString().substring(0, 10)
        }
        // console.log('value3', self.value3)
        let type = ''
        self.reqtype = []
        if (self.$route.query.reqtype) {
          if (self.$route.query.reqtype === '0') {
            self.reqtype.push('产品需求')
            self.reqtype.push('技术需求')
          } else if (self.$route.query.reqtype === '1') {
            self.reqtype.push('产品需求')
          } else if (self.$route.query.reqtype === '2') {
            self.reqtype.push('技术需求')
          }
          type = self.$route.query.reqtype
        } else {
          self.reqtype.push('产品需求')
          self.reqtype.push('技术需求')
          type = '0'
        }
        // console.log('type', type)
        dict.reqtype = type
        // console.log('重新读取参数之后', self.value1, self.value2, self.value3, self.reqtype)
        // router.push({ path: '/analytics', query: dict })
      },
      setstarttime (monthNum) {
        const date = new Date()
        const month = date.getMonth()
        const year = date.getFullYear()
        date.setYear(year)
        date.setMonth(month - monthNum)
        return date
      },
      toOldRouter () {
        window.open('http://qa.sankuai.com/homepage/analytics/ones/analyticshome')
      },
      asyncOK (data) {
        let self = this
        $('#' + data)[0].__vue__.exportCsv({
          filename: '异常task'
        })
        if (Bus.incompletedata) {
          // self.pattern = Bus.incompletedata[0]['pattern']
          self.getProcessData(Bus.incompletedata)
          Bus.$emit('refreshDistribution')
        }
      },
      cancel () {
        let self = this
        if (Bus.incompletedata) {
          self.pattern = Bus.incompletedata[0]['pattern']
          self.getProcessData(Bus.incompletedata)
          Bus.$emit('refreshDistribution')
        }
      },
      cancel1 () {
      },
      ok () {
        let self = this
        self.$Spin.show()
        let wikidata = {
          processStats: Bus.processCreatestats
        }
        // console.log(wikidata)
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/createstats',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    mounted: function () {
      Bus.commonDirectionObject = this
      let self = this
      self.getBizlineData(self)
      self.getreqtype()
      self.getrouterparams()
      if (self.value1 && self.value2 && self.value3 && self.reqtype) {
        self.postData(self.value1, self.value2, self.value3, self.reqtype)
      }
      // console.log('dict', dict)
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }

  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }

</style>
