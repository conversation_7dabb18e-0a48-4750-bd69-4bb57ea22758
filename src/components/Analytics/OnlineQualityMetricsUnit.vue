<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div>
          <Row>
            <Col span="8">
            <div id="effectiveonlinebugratio" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="averagesolvetime" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="averagehiddentime" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
          </Row>
          <div>
            <Card>
              <div style="margin-bottom: 5px;margin-top: -6px">
                <span style="font-weight: bolder;font-size: larger;">线上问题（线上bug+线上故障）</span>
              </div>
              <Table border :row-class-name="rowClassName" :columns="columns5" :data="tableData"></Table>
            </Card>
          </div>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  //  import router from '@/router'
  import CommonDirection from './CommonDirection'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'

  HighchartsMore(Highcharts)
  // import Layout from 'iview/src/components/layout/layout'
  // let tableData = []
  // Vue.component('head-component', Head)
  // Bus.$on('createTable', function (data, self) {
  //   self.tableData = data['statsData']
  //   self.data6 = self.getProcessData(self.tableData)
  //   self.data_resource = self.data6[0]
  //   self.data_deal = self.data6[1]
  //   self.data_access = self.data6[2]
  // })
  Bus.$on('refreshOnlineQualityStat', function (data) {
    Bus.OnlineQualityObject.tableData = Bus.onlineBugStats['tableData']
    Bus.OnlineQualityObject.setonlinebugchartdata(Bus.OnlineQualityObject.tableData)
    Bus.OnlineQualityObject.chartData = Bus.onlineBugStats['chartData']
    Bus.OnlineQualityObject.queryPattern = Bus.onlineBugStats['pattern']
  })
  Bus.$on('refreshQualityTableLength', function (data) {
  })
  export default {
    // components: {Layout},
    components: {CommonDirection},
    name: 'onlinebug-metrics-unit',
    data: function () {
      return {
        display5: false,
        directories: [],
        validbugratio: [],
        averagebugperday: [],
        periodlist: [],
        columns5: [
          {
            title: '方向',
            key: 'directionName',
            // width: 20,
            sortable: true
          },
          {
            title: '时间',
            key: 'periodTime',
            // width: 20,
            sortable: true
          },
          {
            title: '有效线上问题数',
            // width: 110,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['totalUrl'],
                    target: '_blank'
                  }
                }, params.row['totalCnt'])
              ])
            }
          },
          {
            title: '未解决问题',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['unsolvedUrl'],
                    target: '_blank'
//                    style: {
//                      color: "red"
//                    }
                  }
                }, params.row['unsolvedCnt'])
              ])
            },
            // width: 120,
            sortable: true
          },
          {
            title: '线上bug数',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['validBugUrl'],
                    target: '_blank'
                  }
                }, params.row['validBugCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '线上故障数',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['validFaultUrl'],
                    target: '_blank'
                  }
                }, params.row['validFaultCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '问题平均解决时长(h)',
            key: 'averageDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '问题平均潜藏时长(h)',
            key: 'averageHideDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    target: '_blank',
                    to: this.jump(params.row),
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                }, '查看详情')
              ])
            }
          }
        ],
        tableData: [],
        averagebughiddentime: [],
        averagebugsolvetime: [],
        onlineTotalCnt: []
      }
    },

    methods: {
      getDetailFlag: function (data) {
        if (data['directionName'] === '总计') {
          return true
        } else {
          return false
        }
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0 && raw.direction !== '总计') {
          return 'demo-stripe'
        } else if (raw.direction === '总计') {
          return 'demo-table-info-row'
        } else {
          return ''
        }
      },
      setonlinebugchartdata: function (data) {
        let self = this
        this.directories = []
        this.periodlist = []
        let periodlist = []
        for (const each in data) {
          if (self.directories.indexOf(data[each].directionName) === -1) {
            this.directories.push(data[each].directionName)
          }
          if (periodlist.indexOf(data[each].periodTime) === -1) { // 可以按顺序排列
            periodlist.push(data[each].periodTime)
          }
        }
        self.periodlist = self.sortbyTime(periodlist)
        const averagebugsolvetime = {}
        const averagebughiddentime = {}
        const onlinebugcnt = {}
        for (let i = 0; i < self.periodlist.length; i += 1) {
          const solveLessThanOneHourCnt = []
          const solveLessThanOneDayCnt = []
          const solveLessThanOneMonthCnt = []
          const solveMoreThanOneMonthCnt = []
          const solveErrorCnt = []
          const hiddenMoreThanoneQuarterCnt = []
          const hiddenErrorCnt = []
          const hiddenLessThanOneDayCnt = []
          const hiddenLessThanOneMonthCnt = []
          const hiddenLessThanOneQuarterCnt = []
          const solveresult = []
          const hiddenresult = []
          const validBugCnt = []
          const validFaultCnt = []
          const onlineCnt = []
          for (let k = 0; k < self.directories.length; k += 1) {
            for (const j in data) {
              let solvesum = 0
              let hiddensum = 0
              if (data[j].periodTime === self.periodlist[i] && data[j].directionName === self.directories[k]) {
                solvesum = data[j].solveLessThanOneHourCnt + data[j].solveLessThanOneDayCnt + data[j].solveLessThanOneMonthCnt + data[j].solveMoreThanOneMonthCnt + data[j].solveErrorCnt
                hiddensum = data[j].hiddenLessThanOneDayCnt + data[j].hiddenLessThanOneMonthCnt + data[j].hiddenLessThanOneQuarterCnt + data[j].hiddenMoreThanoneQuarterCnt + data[j].hiddenErrorCnt
                validBugCnt.push(data[j].validBugCnt)
                validFaultCnt.push(data[j].validFaultCnt)
                if (data[j].solveLessThanOneHourCnt && solvesum !== 0) {
                  solveLessThanOneHourCnt.push(Number((data[j].solveLessThanOneHourCnt / solvesum * 100).toFixed(2)))
                } else {
                  solveLessThanOneHourCnt.push(0)
                }
                if (data[j].solveLessThanOneDayCnt && solvesum !== 0) {
                  solveLessThanOneDayCnt.push(Number((data[j].solveLessThanOneDayCnt / solvesum * 100).toFixed(2)))
                } else {
                  solveLessThanOneDayCnt.push(0)
                }
                if (data[j].solveLessThanOneMonthCnt && solvesum !== 0) {
                  solveLessThanOneMonthCnt.push(Number((data[j].solveLessThanOneMonthCnt / solvesum * 100).toFixed(2)))
                } else {
                  solveLessThanOneMonthCnt.push(0)
                }
                if (data[j].solveMoreThanOneMonthCnt && solvesum !== 0) {
                  solveMoreThanOneMonthCnt.push(Number((data[j].solveMoreThanOneMonthCnt / solvesum * 100).toFixed(2)))
                } else {
                  solveMoreThanOneMonthCnt.push(0)
                }
                if (data[j].solveErrorCnt && solvesum !== 0) {
                  solveErrorCnt.push(Number((data[j].solveErrorCnt / solvesum * 100).toFixed(2)))
                } else {
                  solveErrorCnt.push(0)
                }
                if (data[j].hiddenLessThanOneDayCnt && hiddensum !== 0) {
                  hiddenLessThanOneDayCnt.push(Number((data[j].hiddenLessThanOneDayCnt / hiddensum * 100).toFixed(2)))
                } else {
                  hiddenLessThanOneDayCnt.push(0)
                }
                if (data[j].hiddenLessThanOneMonthCnt && hiddensum !== 0) {
                  hiddenLessThanOneMonthCnt.push(Number((data[j].hiddenLessThanOneMonthCnt / hiddensum * 100).toFixed(2)))
                } else {
                  hiddenLessThanOneMonthCnt.push(0)
                }
                if (data[j].hiddenLessThanOneQuarterCnt && hiddensum !== 0) {
                  hiddenLessThanOneQuarterCnt.push(Number((data[j].hiddenLessThanOneQuarterCnt / hiddensum * 100).toFixed(2)))
                } else {
                  hiddenLessThanOneQuarterCnt.push(0)
                }
                if (data[j].hiddenMoreThanoneQuarterCnt && hiddensum !== 0) {
                  hiddenMoreThanoneQuarterCnt.push(Number((data[j].hiddenMoreThanoneQuarterCnt / hiddensum * 100).toFixed(2)))
                } else {
                  hiddenMoreThanoneQuarterCnt.push(0)
                }
                if (data[j].hiddenErrorCnt && hiddensum !== 0) {
                  hiddenErrorCnt.push(Number((data[j].hiddenErrorCnt / hiddensum * 100).toFixed(2)))
                } else {
                  hiddenErrorCnt.push(0)
                }
                break
              }
            }
          }
          if (solveLessThanOneHourCnt.length < self.directories.length) {
            solveLessThanOneHourCnt.push(0)
          }
          if (solveLessThanOneDayCnt.length < self.directories.length) {
            solveLessThanOneDayCnt.push(0)
          }
          if (solveLessThanOneMonthCnt.length < self.directories.length) {
            solveLessThanOneMonthCnt.push(0)
          }
          if (solveMoreThanOneMonthCnt.length < self.directories.length) {
            solveMoreThanOneMonthCnt.push(0)
          }
          if (solveErrorCnt.length < self.directories.length) {
            solveErrorCnt.push(0)
          }
          if (hiddenLessThanOneDayCnt.length < self.directories.length) {
            hiddenLessThanOneDayCnt.push(0)
          }
          if (hiddenLessThanOneMonthCnt.length < self.directories.length) {
            hiddenLessThanOneMonthCnt.push(0)
          }
          if (hiddenLessThanOneQuarterCnt.length < self.directories.length) {
            hiddenLessThanOneQuarterCnt.push(0)
          }
          if (hiddenMoreThanoneQuarterCnt.length < self.directories.length) {
            hiddenMoreThanoneQuarterCnt.push(0)
          }
          if (hiddenErrorCnt.length < self.directories.length) {
            hiddenErrorCnt.push(0)
          }
          onlineCnt.push(
            {
              'name': '线上bug',
              'data': validBugCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          onlineCnt.push(
            {
              'name': '线上故障',
              'data': validFaultCnt,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '1小时以内',
              'data': solveLessThanOneHourCnt,
              'color': '#c1405a',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '大于1小时小于24小时',
              'data': solveLessThanOneDayCnt,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '大于24小时小于1个月',
              'data': solveLessThanOneMonthCnt,
              'color': '#FFA500',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '大于1个月',
              'data': solveMoreThanOneMonthCnt,
              'color': '#20B2AA',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '其他',
              'data': solveErrorCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '24小时以内',
              'data': hiddenLessThanOneDayCnt,
              'color': '#c1405a',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '大于24小时小于1个月',
              'data': hiddenLessThanOneMonthCnt,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '大于1个月小于3个月',
              'data': hiddenLessThanOneQuarterCnt,
              'color': '#FFA500',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '大于3个月',
              'data': hiddenMoreThanoneQuarterCnt,
              'color': '#20B2AA',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '其他',
              'data': hiddenErrorCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          averagebugsolvetime[self.periodlist[i]] = solveresult
          averagebughiddentime[self.periodlist[i]] = hiddenresult
          onlinebugcnt[self.periodlist[i]] = onlineCnt
        }
        self.averagebugsolvetime = []
        self.averagebughiddentime = []
        self.onlineTotalCnt = []
        for (const each in averagebugsolvetime) {
          for (const i in averagebugsolvetime[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第一季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第二季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第三季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第四季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: averagebugsolvetime[each][i].name,
                data: averagebugsolvetime[each][i].data,
                color: averagebugsolvetime[each][i].color,
                stack: averagebugsolvetime[each][i].stack,
                showInLegend: averagebugsolvetime[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: averagebugsolvetime[each][i].name,
                data: averagebugsolvetime[each][i].data,
                color: averagebugsolvetime[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: averagebugsolvetime[each][i].showInLegend
              }
            }
            self.averagebugsolvetime.push(tempdata)
          }
        }
        for (const each in averagebughiddentime) {
          for (const i in averagebughiddentime[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第一季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第二季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第三季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第四季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: averagebughiddentime[each][i].name,
                data: averagebughiddentime[each][i].data,
                color: averagebughiddentime[each][i].color,
                stack: averagebughiddentime[each][i].stack,
                showInLegend: averagebughiddentime[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: averagebughiddentime[each][i].name,
                data: averagebughiddentime[each][i].data,
                color: averagebughiddentime[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: averagebughiddentime[each][i].showInLegend
              }
            }
            self.averagebughiddentime.push(tempdata)
          }
        }
        for (const each in onlinebugcnt) {
          for (const i in onlinebugcnt[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第一季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第二季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第三季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第四季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: onlinebugcnt[each][i].name,
                data: onlinebugcnt[each][i].data,
                color: onlinebugcnt[each][i].color,
                stack: onlinebugcnt[each][i].stack,
                showInLegend: onlinebugcnt[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: onlinebugcnt[each][i].name,
                data: onlinebugcnt[each][i].data,
                color: onlinebugcnt[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: onlinebugcnt[each][i].showInLegend
              }
            }
            self.onlineTotalCnt.push(tempdata)
          }
        }
        setTimeout(function () {
          self.averagebugsolvechart()
          self.averagebughiddenchart()
          self.onlinetotalchart()
        }, 500)
      },
      onlinetotalchart: function () {
        Highcharts.chart('effectiveonlinebugratio', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '有效线上问题'
          },
          subtitle: {
            text: '从左到右按选择时间依次排列'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'normal'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y}',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '个'
          },
          series: this.onlineTotalCnt,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      averagebugsolvechart: function () {
        Highcharts.chart('averagesolvetime', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '线上问题解决时间占比'
          },
          subtitle: {
            text: '从左到右按选择时间依次排列'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'percent'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y} %',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '%'
          },
          series: this.averagebugsolvetime,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      averagebughiddenchart: function () {
        Highcharts.chart('averagehiddentime', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '线上问题隐藏时间占比'
          },
          subtitle: {
            text: '从左到右按选择时间依次排列'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'percent'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y} %',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '%'
          },
          series: this.averagebughiddentime,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      getCountColor: function (cnt) {
        if (cnt > 0) {
          return 'red'
        } else {
          return '#495060'
        }
      },
      getCountFont: function (cnt) {
        if (cnt > 0) {
          return 'bold'
        }
      },
      jump: function (data) {
        const dict = {
          name: data['directionName'],
          period: data['periodTime'],
          queryType: Bus.OnlineQualityObject.queryPattern
        }
        let temp = ''
        temp += '/online/issuedetail?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      }
    },
    mounted: function () {
      Bus.OnlineQualityObject = this
      // console.log('begin', Bus.OnlineQualityObject)
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
