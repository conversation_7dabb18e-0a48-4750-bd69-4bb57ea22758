<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/analytics/analyticshome">过程度量大盘</BreadcrumbItem>
      </Breadcrumb>
      <div class="tab" style="background-color: #FFFFFF; padding-top: 15px; padding-right: 15px">
        <div :style="{borderWidth:0,marginTop:'10px',marginLeft:'15px',marginRight:'10px',marginBottom:'20px'}">
          <common-direction></common-direction>
        </div>
        <Tabs :value="analytics_tab" class="tool-tab" @on-click="registerChildrenTab">
          <TabPane label="首页" name="analyticshome">
            <analytics-homepage-unit></analytics-homepage-unit>
          </TabPane>
          <TabPane label="研发过程" name="process_data">
            <process-metrics-unit></process-metrics-unit>
          </TabPane>
          <TabPane label="提测质量" name="quality_data">
            <quality-metrics-unit></quality-metrics-unit>
          </TabPane>
          <TabPane label="线上质量" name="online_quality_data">
            <onlinebug-metrics-unit></onlinebug-metrics-unit>
          </TabPane>
          <TabPane label="人效数据" name="effect_data">
            <effect-metrics-unit></effect-metrics-unit>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import CommonDirection from './CommonDirection'
  import { Bus } from '@/global/bus'
  import AnalyticsHomepageUnit from './AnalyticsHomepageUnit'
  import ProcessMetricsUnit from './ProcessMetricsUnit'
  import QualityMetricsUnit from './QualityMetricsUnit'
  import OnlinebugMetricsUnit from './OnlineQualityMetricsUnit'
  import EffectMetricsUnit from './EffectMetricsUnit'
  import CommonFooter from '../Common/Footer'

  export default {
    components: {
      CommonFooter,
      EffectMetricsUnit,
      OnlinebugMetricsUnit,
      QualityMetricsUnit,
      ProcessMetricsUnit,
      AnalyticsHomepageUnit,
      CommonDirection
    },
    name: 'analytics-homepage',
    // components: {OnlineDeployHomepage, IntegrationTestHomepage, TicePipelineHomepage, ClientQualityPlatform},
    data: function () {
      return {
        tab: 'analytics',
        analytics_tab: 'analyticshome',
        mis: Bus.userInfo.userLogin,
        invalidData: [],
        modal6: false,
        // display2: false,
        columns4: [
          {
            title: 'task key',
            key: 'issueKey',
            sortable: true
          },
          {
            title: 'task名称',
            key: 'summary',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: 'https://flow.sankuai.com/browse/' + params.row['issueKey']
                  },
                  on: {
                    click: () => {
                      window.open('https://flow.sankuai.com/browse/' + params.row['issueKey'])
                    }
                  }
                }, params.row['summary'])
              ])
            }
          },
          {
            title: '错误原因',
            key: 'reason',
            sortable: true
          },
          {
            title: '经办人',
            key: 'assignee',
            sortable: true
          }
        ]
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      registerChildrenTab: function (value) {
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        this.$router.push({path: `/homepage/analytics/${value}`, query: dict})
      },
      fixPosition: function () {
        if (document.querySelector('.tool-tab > .ivu-tabs-bar')) {
          this.width = document.querySelector('.tool-tab > .ivu-tabs-bar').style.paddingLeft = (window.innerWidth - 570) + 'px'
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      },
      asyncOK (data) {
        $('#' + data)[0].__vue__.exportCsv({
          filename: '异常task'
        })
        if (Bus.incompletedata) {
          // self.pattern = Bus.incompletedata[0]['pattern']

          Bus.$emit('refreshDistribution', Bus.incompletedata)
        }
      },
      cancel () {
        let self = this
        if (Bus.incompletedata) {
          self.pattern = Bus.incompletedata[0]['pattern']
          Bus.$emit('refreshDistribution', Bus.incompletedata)
        }
      },
      cancel1 () {
      }
    },
    mounted: function () {
      Bus.analyticsHomepageObject = this
      this.fixPosition()
      window.addEventListener('resize', this.fixPosition)
      if (this.$route.params.tab === 'undefined' || !this.$route.params.tab) {
        this.$route.params.tab = 'analyticshome'
      }
      if (this.$route.params.tab) {
        let tab = this.$route.params.tab
        let validTabList = ['analyticshome', 'process_data', 'quality_data', 'online_quality_data', 'effect_data']
        if (validTabList.indexOf(tab) !== -1) {
          this.analytics_tab = tab
        } else {
          this.analytics_tab = 'analyticshome'
          // this.$router.push('/homepage/cd/content-not-found')
        }
      } else {
        this.analytics_tab = 'analyticshome'
      }
    },
    activated: function () {
      if (this.$route.meta.isBack) {
        this.resetdata()
      }
      this.$route.meta.isBack = false
    }
  }
</script>

<style>
  .tab {
    margin-top: 15px;
    margin-right: 1.5%;
    width: auto
  }

  .tool-tab > .ivu-tabs-bar {
    border-bottom: 0;
    margin-bottom: -10px;
    margin-top: -10px;
    font-weight: bolder;
  }
</style>
