<template>
  <div>
    <div v-if="display7">
      <Row>
        <Col span="8">
        <div v-if="custom">
          <div id="qaefficiencypie" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </div>
        <div v-else>
          <div id="qaefficiencycolumn" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </div>
        </Col>
      </Row>
      <Card>
        <Table :height="600" border :row-class-name="rowClassName" :columns="qaeffect" :data="effectStats"></Table>
      </Card>
    </div>
    <div v-else>
      <Row>
        <Col span="8">
        <div v-if="custom">
          <div id="qaefficiencypie" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </div>
        <div v-else>
          <div id="qaefficiencycolumn" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
        </div>
        </Col>
      </Row>
      <Card>
        <Table border :columns="qaeffect" :row-class-name="rowClassName" :data="effectStats"></Table>
      </Card>
    </div>
  </div>
</template>

<script>
  // import axios from 'axios'
  // import Vue from 'vue'
  // import Head from '@/components/common/Head'
  import { Bus } from '@/global/bus'
  // import router from '@/router'
  // import { analyticsbaseAPI } from '@/global/variable'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  HighchartsMore(Highcharts)

  Bus.$on('refreshQaEfficiencyStat', function (data) {
    Bus.qaEfficiencyUnitObject.custom = Bus.qaEfficiencyUnitObject.getcharttype()
    Bus.qaEfficiencyUnitObject.effectStats = Bus.qaEfficiencyUnitObject.setqaEfficiencyData(data)
    if (Bus.qaEfficiencyUnitObject.custom) {
      Bus.qaEfficiencyUnitObject.setqaefficiencypie(Bus.qaEfficiencyUnitObject.effectStats)
    } else {
      Bus.qaEfficiencyUnitObject.setqaefficiencycolumn(Bus.qaEfficiencyUnitObject.effectStats)
    }
  })
  Bus.$on('refreshEfficiencyTableLength', function (data) {
    Bus.qaEfficiencyUnitObject.display7 = Bus.qaEfficiencyUnitObject.adjustTableHight(data)
  })

  export default {
    name: 'qa-effect-unit',
    data: function () {
      return {
        effectStats: [],
        display7: false,
        directories: [],
        periodlist: [],
        custom: true,
        efficiency: [],
        qaeffect: [
          {
            title: '方向',
            key: 'direction',
            // width: 20,
            sortable: true
          },
          {
            title: '时间',
            key: 'period',
            // width: 20,
            sortable: true
          },
          {
            title: '需求评审PD',
            key: 'reqReviewPd',
            // width: 20,
            sortable: true
          },
          {
            title: '技术评审PD',
            key: 'techReviewPd',
            // width: 120,
            sortable: true
          },
          {
            title: '测试设计PD',
            key: 'testDesignPd',
            // width: 140,
            sortable: true
          },
          {
            title: '测试PD',
            key: 'testPd',
            // width: 140,
            sortable: true
          },
          {
            title: '总PD',
            key: 'totalPd',
            // width: 140,
            sortable: true
          },
          // {
          //   title: '代码量',
          //   key: 'code',
          //   // width: 140,
          //   sortable: true
          // },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                }, '查看详情')
              ])
            }
          }
        ]
      }
    },
    methods: {
      getDetailFlag: function (data) {
        if (data['direction'] === '总计') {
          return true
        } else {
          return false
        }
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0 && raw.direction !== '总计') {
          return 'demo-stripe'
        } else if (raw.direction === '总计') {
          return 'demo-table-info-row'
        } else {
          return ''
        }
      },
      getcharttype: function () {
        if (this.$route.query.type === 'CUSTOMED') {
          return true
        } else {
          return false
        }
      },
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      setqaEfficiencyData: function (rawdata) {
        // debugger
        // console.log('函数内的质量数据：', rawdata)
        if (rawdata) {
          let metricsStats = []
          for (let period in rawdata) {
            metricsStats.push(rawdata[period])
          }
          return metricsStats
        } else {
          return []
        }
      },
      setqaefficiencycolumn: function (data) { // bug归属
        let self = this
        this.directories = []
        this.periodlist = []
        let periodlist = []
        for (const each in data) {
          if (self.directories.indexOf(data[each].direction) === -1) {
            this.directories.push(data[each].direction)
          }
          if (periodlist.indexOf(data[each].period) === -1) { // 可以按顺序排列
            periodlist.push(data[each].period)
          }
        }
        self.periodlist = self.sortbyTime(periodlist)
        const efficiency = {}
        for (let i = 0; i < self.periodlist.length; i += 1) {
          const result = self.handleEfficiencyseries(data, i)
          const subefficiency = []
          subefficiency.push(
            {
              'name': '需求评审',
              'data': result.reqReviewPd,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          subefficiency.push(
            {
              'name': '技术评审',
              'data': result.techReviewPd,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          subefficiency.push(
            {
              'name': '测试设计',
              'data': result.testDesignPd,
              'color': '#FFA500',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          subefficiency.push(
            {
              'name': '测试',
              'data': result.testPd,
              'color': '#20B2AA',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          efficiency[self.periodlist[i]] = subefficiency
        }
        // const color = ['#00CD66', '#405ac1', '#20B2AA', '#FFA500', '#8192D6', '#c581d6', '#c1405a']
        self.efficiency = []
        for (const each in efficiency) {
          for (const i in efficiency[each]) {
            let tempdata = self.setlegend(efficiency, each, i)
            self.efficiency.push(tempdata)
          }
        }
        // console.log('最后数据情况', self.bugattribute, self.averagebugperday, self.validbugratio)
        setTimeout(function () {
          self.highchartscolumn('qaefficiencycolumn', self.directories, self.efficiency, 'percent', 'QA人效', '从左到右按选择时间依次排列', '%')
        }, 500)
      },
      setlegend (data, each, i) {
        let tempdata = {}
        if (this.$route.query.type === 'QUARTER') {
          if (parseInt(each.split('-')[1]) <= 3) {
            tempdata = {
              name: data[each][i].name,
              data: data[each][i].data,
              color: data[each][i].color,
              stack: '第一季度',
              showInLegend: data[each][i].showInLegend
            }
          } else if (parseInt(each.split('-')[1]) <= 6) {
            tempdata = {
              name: data[each][i].name,
              data: data[each][i].data,
              color: data[each][i].color,
              stack: '第二季度',
              showInLegend: data[each][i].showInLegend
            }
          } else if (parseInt(each.split('-')[1]) <= 9) {
            tempdata = {
              name: data[each][i].name,
              data: data[each][i].data,
              color: data[each][i].color,
              stack: '第三季度',
              showInLegend: data[each][i].showInLegend
            }
          } else {
            tempdata = {
              name: data[each][i].name,
              data: data[each][i].data,
              color: data[each][i].color,
              stack: '第四季度',
              showInLegend: data[each][i].showInLegend
            }
          }
        } else if (this.$route.query.type === 'CUSTOMED') {
          tempdata = {
            name: data[each][i].name,
            data: data[each][i].data,
            color: data[each][i].color,
            stack: data[each][i].stack,
            showInLegend: data[each][i].showInLegend
          }
        } else {
          tempdata = {
            name: data[each][i].name,
            data: data[each][i].data,
            color: data[each][i].color,
            stack: each.split('-')[1] + '月',
            showInLegend: data[each][i].showInLegend
          }
        }
        return tempdata
      },
      setqaefficiencypie: function (data) {
        let self = this
        this.directories = []
        this.periodlist = []
        let periodlist = []
        for (const each in data) {
          if (self.directories.indexOf(data[each].direction) === -1) {
            if (data[each].direction === '总计') {
              this.directories.push(data[each].direction)
            }
          }
          if (periodlist.indexOf(data[each].period) === -1) { // 可以按顺序排列
            periodlist.push(data[each].period)
          }
        }
        self.periodlist = self.sortbyTime(periodlist)
        self.efficiency = []
        for (let i = 0; i < self.periodlist.length; i += 1) {
          const result = self.handleEfficiencyseries(data, i)
          const subefficiency = []
          subefficiency.push(
            {
              'name': '需求评审',
              'y': result.reqReviewPd[0],
              'color': '#8192D6'
            }
          )
          subefficiency.push(
            {
              'name': '技术评审',
              'y': result.techReviewPd[0],
              'color': '#c581d6'
            }
          )
          subefficiency.push(
            {
              'name': '测试设计',
              'y': result.testDesignPd[0],
              'color': '#FFA500'
            }
          )
          subefficiency.push(
            {
              'name': '测试',
              'y': result.testPd[0],
              'color': '#20B2AA'
            }
          )
          self.efficiency = subefficiency
        }
        setTimeout(function () {
          self.highchartspie('qaefficiencypie', self.efficiency)
        }, 500)
      },
      handleEfficiencyseries: function (data, i) {
        const temp = {
          'reqReviewPd': [],
          'techReviewPd': [],
          'testDesignPd': [],
          'testPd': [],
          'sum': 0
        }
        for (let k = 0; k < this.directories.length; k += 1) {
          for (const j in data) {
            if (data[j].period === this.periodlist[i] && data[j].direction === this.directories[k]) {
              temp.sum = data[j].reqReviewPd + data[j].techReviewPd + data[j].testDesignPd + data[j].testPd
              if (data[j].reqReviewPd) {
                temp.reqReviewPd.push(Number((data[j].reqReviewPd / temp.sum * 100).toFixed(2)))
              } else {
                temp.reqReviewPd.push(0)
              }
              if (data[j].techReviewPd) {
                temp.techReviewPd.push(Number((data[j].techReviewPd / temp.sum * 100).toFixed(2)))
              } else {
                temp.techReviewPd.push(0)
              }
              if (data[j].testDesignPd) {
                temp.testDesignPd.push(Number((data[j].testDesignPd / temp.sum * 100).toFixed(2)))
              } else {
                temp.testDesignPd.push(0)
              }
              if (data[j].testPd) {
                temp.testPd.push(Number((data[j].testPd / temp.sum * 100).toFixed(2)))
              } else {
                temp.testPd.push(0)
              }
              break
            }
          }
        }
        if (temp.reqReviewPd.length < this.directories.length) {
          temp.reqReviewPd.push(0)
        }
        if (temp.techReviewPd.length < this.directories.length) {
          temp.techReviewPd.push(0)
        }
        if (temp.testDesignPd.length < this.directories.length) {
          temp.testDesignPd.push(0)
        }
        if (temp.testPd.length < this.directories.length) {
          temp.testPd.push(0)
        }
        return temp
      },
      jump: function (data) {
        // let self = this
        // self.$Spin.show()
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          project: this.$route.query.project,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype,
          name: data['direction'],
          period: data['period']
        }
        let temp = ''
        temp += '/efficiency/detail?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
        //   router.push({path: '/efficiency/detail', query: dict})
        //   let direction, period, searchpattern
        //   direction = data['direction']
        //   period = data['period']
        //   searchpattern = Bus.searchpattern
        //   let params = {
        //     directionName: direction,
        //     period: period,
        //     type: searchpattern
        //   }
        //   // console.log(params)
        //   axios.get(analyticsbaseAPI + '/efficiency/detail', {
        //   // axios.get('http://0.0.0.0:11000/analytics/efficiency/detail', {
        //     params: params,
        //     timeout: 50000000,
        //     dataType: 'json'
        //   }).then(function (message) {
        //     if (message['data']['status'] === 0) {
        //       Bus.$emit('refreshefficiencysearch')
        //       Bus.$emit('refreshefficiencybackallow')
        //       Bus.detailEfficiencyData = []
        //       Bus.detailEfficiencyData = message['data']['data']
        //       Bus.$emit('refreshrdEfficiencyDetailData', Bus.detailEfficiencyData)
        //       Bus.$emit('refreshqaEfficiencyDetailData', Bus.detailEfficiencyData)
        //     } else {
        //       self.$Spin.hide()
        //       alert(message['data']['msg'])
        //     }
        //   }).catch(error => {
        //     self.$Spin.hide()
        //     console.log(error)
        //   })
      }
    },
    mounted: function () {
      Bus.qaEfficiencyUnitObject = this
    }
  }
</script>

<style scoped>
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
