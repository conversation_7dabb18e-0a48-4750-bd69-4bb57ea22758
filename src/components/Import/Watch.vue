<template>
  <div>
    <head-component id="head"></head-component>
    <div class="frame" style="width: 100%">
      <iframe :id="id" :src="src" :style="style" frameborder="0" scrolling="auto"></iframe>
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  let id = 'watch'
  let src = 'http://************:5000'
  export default {
    data: function () {
      return {
        id: id,
        src: src,
        style: {
          'width': '100%',
          'height': (window.innerHeight - 55).toString() + 'px'
        }
      }
    },
    beforeCreate: function () {
      this.$Spin.show()
    },
    mounted: function () {
      let _id = '#' + this.id
      let obj = this
      $(_id).on('load', function () {
        console.log(this)
        obj.$Spin.hide()
      })
    }
  }
  Vue.component('head-component', Head)
</script>

<style scoped>
</style>
