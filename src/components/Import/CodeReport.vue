<template>
  <div>
    <div class="frame" style="width: 100%">
      <iframe :id="id" :src="src" :style="style" frameborder="0" scrolling="auto"></iframe>
    </div>
  </div>
</template>

<script>
  let id = 'codeReport'
  let src = 'http://***********:9500/bugrate/report'
  export default {
    data: function () {
      return {
        id: id,
        src: src,
        style: {
          'width': '100%',
          'height': (window.innerHeight - 33).toString() + 'px'
        }
      }
    },
    beforeCreate: function () {
      this.$Spin.show()
    },
    mounted: function () {
      let _id = '#' + this.id
      let obj = this
      $(_id).on('load', function () {
        console.log(this)
        obj.$Spin.hide()
      })
    }
  }
</script>

<style scoped>
</style>
