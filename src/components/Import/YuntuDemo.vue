<template>
  <div>
    <div class="frame" style="width: 100%">
      <iframe :id="id" :src="src" :name="id" :style="style" frameborder="0" scrolling="auto"></iframe>
    </div>
  </div>
</template>

<script>
  // import Vue from 'vue'
  // import Head from '@/components/common/Head'
  let id = 'yuntuDemo'
  let src = 'https://yuntu.sankuai.com/quote/dashboard/view?thirdParty=&dashboard=dashboard-5a204b8c-b634-4beb-823e-86192ce1419d'
  export default {
    name: 'yuntu-demo',
    data: function () {
      console.log(window.innerHeight)
      return {
        id: id,
        src: src,
        style: {
          width: '100%',
          height: (window.innerHeight - 55).toString() + 'px'
        }
      }
    },
    beforeCreate: function () {
      this.$Spin.show()
    },
    mounted: function () {
      let _id = '#' + this.id
      let obj = this
      $(_id).on('load', function () {
        obj.$Spin.hide()
      })
    }
  }
  // Vue.component('head-component', Head)
</script>

<style scoped>
</style>
