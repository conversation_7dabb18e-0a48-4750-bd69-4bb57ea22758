<template>
  <div>
    <head-component id="head"></head-component>
    <Tabs value="coverRate" style='margin-top: 30px;margin-left: 5%;margin-right: 5%;width: auto;'>
      <TabPane label="覆盖率显示图" name="coverRate">
        <div class="table">
          <coverrate-chart-component></coverrate-chart-component>
        </div>
      </TabPane>
    </Tabs>


  </div>
</template>

<script>
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import coverRatechart from './coverRatechart'
  Vue.component('head-component', Head)
  Vue.component('coverrate-chart-component', coverRatechart)
  export default {
    name: 'coverrate-chart-page'
  }
</script>

<style scoped>

</style>
