<template>
  <div>
    <Steps :current="current" style="margin-bottom: 15px;">
      <Step title="步骤一"></Step>
      <Step title="步骤二"></Step>
      <Step title="步骤三"></Step>
    </Steps>
    <Row>
      <Button type="primary" @click="prev">上一步</Button>
      <Button type="primary" @click="next" style="margin-left: 5px;">下一步</Button>
    </Row>
    <Row style="margin-top: 10px">
      <Card v-if="current === 0">
        <p slot="title">步骤一：请输入plusName </p>
        <Input v-model="value" placeholder="请输入plus name" style="width: 200px"></Input>
        <!--<Button icon="plus-circled" type="primary" shape="circle" style="margin-left: 10px" @click="addPlusname">添加</Button>-->
        <!--<div v-if="plusList.length !== 0" v-for="(item, index) in plusList" style="margin: 5px">-->
          <!--<Tag type="dot" closable @on-close="deleteTag(index, plusList)">{{item}}</Tag>-->
        <!--</div>-->
        <i-select v-model="model1" style="width: 200px" :transfer="true">
          <i-option v-for="item in selectList" :value="item.value" :key="item.value">{{ item.value}}</i-option>
        </i-select>
        <Button type="primary" @click="submit">发布</Button>
      </Card>
    </Row>
    <Row style="margin-top: 10px"  v-if="current === 1">
      <Card style="padding-bottom: 35px;">
        <p slot="title">步骤二：获取plusName列表</p>
        <i-select v-model="model2" style="width: 200px" :transfer="true" @on-change="getPlusList">
          <i-option v-for="item in selectList1" :value="item.value" :key="item.value">{{ item.value}}</i-option>
        </i-select>
        <Table :columns="columns1" :data="data1" style="margin-top: 10px;"></Table>
        <Button type="primary" @click="submit" style="margin-top: 10px;float: right">提交</Button>
      </Card>
    </Row>
    <Row style="margin-top: 10px"  v-if="current === 2">
      <Card>
        <p slot="title">步骤三：创建结果</p>
        <span>创建成功或者失败</span>
      </Card>
    </Row>
  </div>

</template>

<script>
    export default {
      name: 'plusname-table',
      data: function () {
        return {
          value: '',
          plusList: [],
          model1: '',
          model2: '一层plusName',  // 为下拉菜单设置默认值
          current: 0,
          selectList: [
            {
              value: 'm_trace'
            },
            {
              value: 'auto'
            }
          ],
          selectList1: [
            {
              value: '一层plusName'
            },
            {
              value: '二层plusName'
            },
            {
              value: '三层plusName'
            }
          ],
          columns1: [
            {
              title: 'plusName',
              key: 'name'
            },
            {
              title: '是否mock',
              key: 'if_mock',
              render: (h, params) => {
                console.log(h)
                return h('i-switch', {
                  props: {
                    // size: 'large'
                    value: true
                  },
                  on: {
                    'on-change': () => {
                      alert('hello')
                      // 触发事件是on-change,用双引号括起来，
                      // 参数value是回调值，并没有使用到
                      // this.switch(params.index) //params.index是拿到table的行序列，可以取到对应的表格值
                    }
                    // change: () => {
                    //   alert('hello')  // 问题 进入不来为什么
                    //   // this.changePlus_mock(params.index)
                    // }  //change 这个事件不可以 无法触发
                  }
                }, [
                  h('span', {
                    slot: 'open'
                  }, 'ON'),
                  h('span', {
                    slot: 'close'
                  }, 'OFF')
                ])
              }

              // render: (h, params) => {
              //   return h('div', [h('i-switch', {
              //     props: {
              //       value: false
              //     },
              //     on: {
              //       change: () => {
              //         alert(params.index)
              //        // this.change(params.index)
              //       }
              //     }
              //   })
              //   ])
              // }
            }
          ],
          data1: [
            {
              name: 'www.baidu.com'
            },
            {
              name: 'www.baidu.com'
            },
            {
              name: 'www.baidu.com'
            },
            {
              name: 'www.baidu.com'
            }
          ]
        }
      },
      methods: {
        addPlusname: function () {
          if (this.value) {
            this.plusList.push(this.value)
           // alert(this.plusList)
          }
        },
        deleteTag: function (index, list) {
          list.splice(index, 1)
        },
        submit: function () {
          // 在这里进行提交
          alert('提交成功')
        },
        next: function () {
          if (this.current === 2) {
            this.current = 2
          } else {
            this.current += 1
          }
        },
        prev: function () {
          if (this.current === 0) {
            this.current = 0
          } else {
            this.current -= 1
          }
        },
        getPlusList: function () {
          alert('niahao')
        },
        changePlus_mock: function () {
          alert('hello')
        }
      }
    }
</script>

<style scoped>
  #select {
   overflow: visible;
 }
</style>
