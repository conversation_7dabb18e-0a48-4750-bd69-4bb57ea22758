<template>
  <div id="app">
    <Modal v-model="rssCq" title="选择方向" @on-ok="saveRssAdd()" width="666">
      <Cascader :data="directionList" v-model="directionOrigin" placeholder="请选择方向" change-on-select :transfer="true"></Cascader>
    </Modal>
    <Row type="flex" :style="{paddingTop:'0px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10',marginLeft:'10px',marginRight:'10px',marginBottom:'0px'}" dis-hover>
        <Row slot="title" :style="{fontWeight:'bolder'}">
          <Col>
          <span style="padding-bottom: -5px">{{direction}}自动化覆盖率成功率统计结果</span>
          <Button icon="gear-a" type="dashed" size="small" class="tool-button" @click="showRssAddModal" style="margin-left: 10px">切换方向</Button>
          <DatePicker style="width: 300px" :value="value3" type="datetimerange" placeholder="选择日期和时间" class="datepicker"  @on-change="updateChart"></DatePicker>
          </Col>
        </Row>
      </Card>
      </Col>
    </Row>
    <div>
      <Row type="flex">
        <Col span="12" style="padding-left: 20px; padding-right: 20px">
        <div id="container0" class="container1"></div>
        </Col>
        <Col span="12" style="padding-left: 20px; padding-right: 20px">
        <div id="container1" class="container1"></div>
        </Col>
      </Row>
      <Row type="flex" style="margin-top: 40px">
        <Col span="12" style="padding-left: 20px; padding-right: 20px">
        <div id="container2" class="container1"></div>
        </Col>
        <Col span="12" style="padding-left: 20px; padding-right: 20px">
        <div id="container3" class="container1"></div>
        </Col>
      </Row>
    </div>
  </div>
</template>


<script>
  import axios from 'axios'
  import Highcharts from 'highcharts/highstock'  // 必须是highstock 不然nevigator不生效
  import HighchartsComponent from '@/components/ConfigurationCenter/HighchartsComponent.vue'
  export default {
    name: 'app',
    data: function () {
      return {
        value3: [],
        directionList: [],
        startTime: '',
        endTime: '',
        directionOrigin: [],
        rssCq: false,
        chart: null,
        stockChart: null,
        stockChart1: null,
        stockChart2: null,
        stockChart3: null,
        direction: ''
      }
    },
    components: {
      HighchartsComponent
    },
    mounted: function () {
      let endTime = this.formateTime(new Date())
      let startTime = this.formateTime(new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000))
      this.value3 = [startTime, endTime]
      this.direction = '到店事业群'
      if (this.direction && this.value3) {
        this.createChart()
      }
      this.getDirections()
    },
    methods: {
      formateTime: function (time) {
        return time.getFullYear() + '-' + (time.getMonth() >= 9 ? (time.getMonth() + 1) : '0' + (time.getMonth() + 1)) + '-' + (time.getDate() > 9 ? time.getDate() : '0' + time.getDate()) + ' ' + '00:00:00'
      },
      showRssAddModal: function () {
        this.rssCq = true
      },
      getDirections: function () {
        let self = this
        let url = this.getDomain('config') + '/api/direction/get_org_list?display_disable=0'
        axios.get(url).then(function (message) {
          setTimeout(() => {
            self.directionList = []
            self.directionList.push(message.data.info)
          }, 100)
        })
      },
      // 获取方向信息
      saveRssAdd: function () {
        let self = this
        let key = []
        for (let value in self.directionOrigin) {
          if (self.directionOrigin[value] !== '全部') {
            key.push(self.directionOrigin[value])
          }
        }
        if (key.length) {
          self.direction = key[key.length - 1]
        }
        if (self.value3) {
          self.createChart(self)
        }
      },
      updateChart: function (date) {
        let self = this
        // 通过 refs 获取组件信息
        // alert('改变日期')
        //  this.$refs.simpleChart.chart.series[0].update({
        //    color: '#000'
        //  })
        self.value3 = date // 是一个数组,存放着两个日期
        if (self.direction) {
          self.createChart()
        }
      },
      createChart () {
        let self = this
        Highcharts.setOptions({
          // navigator: {
          //   enabled: true
          // },
          colors: ['#058DC7', '#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4'],
          // 禁用UTC时间
          global: {
            useUTC: false
          }
        })
        this.startTime = this.value3[0]
        this.endTime = this.value3[1]
        let url = this.getDomain('envmonitor') + '/cov_rate?direction=' + this.direction + '&startTime=' + this.startTime + '&endTime=' + this.endTime
        $.getJSON(url, (message) => {
          // alert(message['info']['data'])
          if (message['info']['data'].length) {
            this.stockChart = new Highcharts.Chart('container0', {
              chart: {
                zoomType: 'x'
              },
              title: {
                text: this.direction + '自动化覆盖率'
              },
              plotOptions: {
                line: {
                  dataLabels: {
                    // 开启数据标签
                    // enabled: true
                  }
                },
                marker: {
                  lineWidth: 1
                },
                series: {
                  events: {
                    legendItemClick: function (event) {
                      let series = this.chart.series
                      let index = this.index
                      if (series[index].visible) {
                        // 除当前其他反转
                        for (let item in series) {
                          if (!series[item].visible) {
                            series[item].setVisible(true, true)
                          } else {
                            if (item !== index) {
                              series[item].setVisible(false, false)
                            }
                          }
                        }
                      } else {
                        // 当前反转、其他取false
                        series[index].setVisible(true, true)
                        for (let item in series) {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    }
                  }
                }
              },
              xAxis: {
                type: 'datetime', // 设置类型为datetime ,并设置显示格式
                dateTimeLabelFormats: {
                  millisecond: '%H:%M:%S.%L',
                  second: '%H:%M:%S',
                  minute: '%H',
                  hour: '%m-%d-%H',
                  day: '%m-%d',
                  week: '%y-%m-%d',
                  month: '%y-%m',
                  year: '%Y'
                }
              },
              legend: {
                layout: 'vertical',
                align: 'right',
                verticalAlign: 'middle'
              },
              credits: {
                enabled: false
              },
              yAxis: {
                max: 100,
                title: null,
                labels: {
                  formatter: function () {
                    return this.value + '%'
                  }
                },
                lineWidth: 2
              },
              series: [],
              tooltip: {
                shared: true,
                crosshairs: true,
                dateTimeLabelFormats: {
                }
              }
            })
            this.stockChart1 = new Highcharts.Chart('container1', {
              chart: {
                zoomType: 'x'
              },
              title: {
                text: this.direction + '自动化成功率'
              },
              plotOptions: {
                line: {
                  dataLabels: {
                    // 开启数据标签
                    // enabled: true
                  }
                },
                marker: {
                  lineWidth: 1
                },
                series: {
                  events: {
                    legendItemClick: function (event) {
                      let series = this.chart.series
                      let index = this.index
                      if (series[index].visible) {
                        // 除当前其他反转
                        for (let item in series) {
                          if (!series[item].visible) {
                            series[item].setVisible(true, true)
                          } else {
                            if (item !== index) {
                              series[item].setVisible(false, false)
                            }
                          }
                        }
                      } else {
                        // 当前反转、其他取false
                        series[index].setVisible(true, true)
                        for (let item in series) {
                          if (item !== index) {
                            series[item].setVisible(false, false)
                          }
                        }
                      }
                    }
                  }
                }
              },
              xAxis: {
                type: 'datetime', // 设置类型为datetime ,并设置显示格式
                dateTimeLabelFormats: {
                  millisecond: '%H:%M:%S.%L',
                  second: '%H:%M:%S',
                  minute: '%H:%M',
                  hour: '%m-%d-%H',
                  day: '%m-%d',
                  week: '%y-%m-%d',
                  month: '%y-%m',
                  year: '%Y'
                }
              },
              legend: {
                layout: 'vertical',
                align: 'right',
                verticalAlign: 'middle'
              },
              credits: {
                enabled: false
              },
              yAxis: {
                max: 100,
                title: null,
                labels: {
                  formatter: function () {
                    return this.value + '%'
                  }
                },
                lineWidth: 2
              },
              series: [],
              tooltip: {
                shared: true,
                crosshairs: true,
                dateTimeLabelFormats: {
                }
              }
            })
            var chartData = []
            chartData[0] = {}
            chartData[1] = {}
            chartData[0].data = []
            chartData[1].data = []
            chartData[0].title = '自动化覆盖率'
            chartData[1].title = '自动化成功率'
            for (let j = 0; j < message['info']['data'].length; j++) { // 暂时注释掉
              chartData[0].data.push([
                new Date(message['info']['data'][j]['timestamp']).getTime(),
                // Date.parse(message[i]['data'][j]['time']),
                message['info']['data'][j]['line_cov_rate']
              ])
            }
            // console.log(chartData[0].data)
            for (let j = 0; j < message['info']['data'].length; j++) {
              chartData[1].data.push([
                new Date(message['info']['data'][j]['timestamp']).getTime(),
                // Date.parse(message[i]['data'][j]['time']),
                message['info']['data'][j]['case_pass_rate']
              ])
            }
            // 通过此处动态添加series 可以收到的不同数据来进行展示
            // for (let t = 0; t < 2; t++) {
            this.stockChart.addSeries({
              type: 'line',
              name: chartData[0].title,
              color: '#ed3f14',
              data: chartData[0].data.sort()  // 给所有数据添加一个排序
            })
            this.stockChart1.addSeries({
              type: 'line',
              name: chartData[1].title,
              color: '#2b85e4',
              data: chartData[1].data.sort()  // 给所有数据添加一个排序
            })
            // }
            // 判断是否有子方向
            if (message['info']['children'].length) {
              // self.subshow = true
              // alert('有子方向吗')
              this.stockChart2 = new Highcharts.Chart('container2', {
                chart: {
                  zoomType: 'x'
                },
                title: {
                  text: '自动化覆盖率分方向'
                },
                plotOptions: {
                  line: {
                    dataLabels: {
                      // 开启数据标签
                      // enabled: true
                    }
                  },
                  marker: {
                    lineWidth: 1
                  },
                  series: {
                    events: {
                      legendItemClick: function (event) {
                        let series = this.chart.series
                        let index = this.index
                        if (series[index].visible) {
                          // 除当前其他反转
                          for (let item in series) {
                            if (!series[item].visible) {
                              series[item].setVisible(true, true)
                            } else {
                              if (item !== index) {
                                series[item].setVisible(false, false)
                              }
                            }
                          }
                        } else {
                          // 当前反转、其他取false
                          series[index].setVisible(true, true)
                          for (let item in series) {
                            if (item !== index) {
                              series[item].setVisible(false, false)
                            }
                          }
                        }
                      }
                    }
                  }
                },
                xAxis: {
                  type: 'datetime', // 设置类型为datetime ,并设置显示格式
                  dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%m-%d-%H:%M',
                    day: '%m-%d',
                    week: '%y-%m-%d',
                    month: '%y-%m',
                    year: '%Y'
                  }
                },
                legend: {
                  layout: 'vertical',
                  align: 'right',
                  verticalAlign: 'middle'
                },
                credits: {
                  enabled: false
                },
                yAxis: {
                  max: 100,
                  title: null,
                  labels: {
                    formatter: function () {
                      return this.value + '%'
                    }
                  },
                  lineWidth: 2
                },
                series: [],
                tooltip: {
                  shared: true,
                  crosshairs: true
                }
              })
              this.stockChart3 = new Highcharts.Chart('container3', {
                chart: {
                  zoomType: 'x'
                },
                title: {
                  text: '自动化成功率分方向'
                },
                plotOptions: {
                  line: {
                    dataLabels: {
                      // 开启数据标签
                      // enabled: true
                    }
                  },
                  marker: {
                    lineWidth: 1
                  },
                  series: {
                    events: {
                      legendItemClick: function (event) {
                        let series = this.chart.series
                        let index = this.index
                        if (series[index].visible) {
                          // 除当前其他反转
                          for (let item in series) {
                            if (!series[item].visible) {
                              series[item].setVisible(true, true)
                            } else {
                              if (item !== index) {
                                series[item].setVisible(false, false)
                              }
                            }
                          }
                        } else {
                          // 当前反转、其他取false
                          series[index].setVisible(true, true)
                          for (let item in series) {
                            if (item !== index) {
                              series[item].setVisible(false, false)
                            }
                          }
                        }
                      }
                    }
                  }
                },
                xAxis: {
                  type: 'datetime', // 设置类型为datetime ,并设置显示格式
                  dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%m-%d-%H:%M',
                    day: '%m-%d',
                    week: '%y-%m-%d',
                    month: '%y-%m',
                    year: '%Y'
                  }
                },
                legend: {
                  layout: 'vertical',
                  align: 'right',
                  verticalAlign: 'middle'
                },
                credits: {
                  enabled: false
                },
                yAxis: {
                  max: 100,
                  title: null,
                  labels: {
                    formatter: function () {
                      return this.value + '%'
                    }
                  },
                  lineWidth: 2
                },
                series: [],
                tooltip: {
                  shared: true,
                  crosshairs: true
                }
              })
              var chartCoverData = []
              var chartSuccessData = []
              var dataLength = message['info']['children'].length
              // alert(dataLength) // 8
              var i = 0
              for (i; i < dataLength; i += 1) {
                chartCoverData[i] = {}
                chartSuccessData[i] = {}
                chartCoverData[i].direction = message['info']['children'][i]['direction']
                chartSuccessData[i].direction = message['info']['children'][i]['direction']
                // alert(chartCoverData[i].direction)
                chartCoverData[i].data = []
                chartSuccessData[i].data = []
                // alert(message['info']['children'][i]['data'].length)
                for (let j = 0; j < message['info']['children'][i]['data'].length; j++) {
                  chartCoverData[i].data.push([
                    new Date(message['info']['children'][i]['data'][j]['timestamp']).getTime(),
                    message['info']['children'][i]['data'][j]['line_cov_rate']
                  ])
                  chartSuccessData[i].data.push([
                    new Date(message['info']['children'][i]['data'][j]['timestamp']).getTime(),
                    message['info']['children'][i]['data'][j]['case_pass_rate']
                  ])
                }
              }
              for (let t = 0; t < chartCoverData.length; t++) {
                this.stockChart2.addSeries({
                  type: 'line',
                  name: chartCoverData[t].direction,
                  data: chartCoverData[t].data.sort()  // 给所有数据添加一个排序
                })
                this.stockChart3.addSeries({
                  type: 'line',
                  name: chartSuccessData[t].direction,
                  data: chartSuccessData[t].data.sort()  // 给所有数据添加一个排序
                })
              }
            } else {
              //  self.subshow = false
            }
          } else {
            self.$Message.error('该方向没有数据，敬请期待！')
          }
        })
      }
    }
  }
</script>

<style scoped>
  .datepicker {
    width: 400px;
    margin-right: 35px;
    float: right;
  }
  .container1 {
    margin-top: 10px;
    padding-top: 20px;
    /*width: 700px;*/
    height: 450px;
    /*margin-left: 10px;  !*不设置时自动居中*!*/
  }
  .tool-button{
    margin-left: 10px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }

</style>
