<style scoped>
</style>
<template>
  <div style="margin-top: 20px">
    <Card :style="{borderWidth:0,marginTop:'10',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
      <div>
        <Row slot="title" :style="{fontWeight:'bolder'}" type="flex">
          <span style="margin-top: 5px">可用性 | 稳定性统计结果</span>
          <Cascader :data="directionList" v-model="formInline.stack" change-on-select style="width: 250px; margin-left: 5px"></Cascader>
          <Button type="primary" @click="handleSubmit(formInline)" style="margin-left: 5px">
            查询
          </Button>
          <Select v-model="formInline.flag" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option value='0'>稳定性</Option>
            <Option value='1'>可用性</Option>
          </Select>
          <Select v-model="formInline.quarter" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option value='1'>最近1季度</Option>
            <Option value='2'>最近2季度</Option>
            <Option value='3'>最近3季度</Option>
            <Option value='4'>最近4季度</Option>
            <Option value='5'>最近5季度</Option>
          </Select>
          <Select v-model="formInline.month" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in monthList" :value="item.value" :key="item.value">最近{{item.label}}月</Option>
          </Select>
          <Select v-model="formInline.week" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in weekList" :value="item.value" :key="item.value">最近{{item.label}}周</Option>
          </Select>
          <Select v-model="formInline.day" @on-change="handleSubmit(formInline)" style="width: 100px; margin-left: 5px">
            <Option v-for="item in dayList" :value="item.value" :key="item.value">最近{{item.label}}天</Option>
          </Select>
          <Button type="primary" @click="exportData()" style="margin-left: 5px">
            数据导出
          </Button>
        </Row>
      </div>
      <Row type="flex">
        <Content :style="{paddingBottom: '20px', minHeight: '15px', background: '#fff'}">
          <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
            <div slot="title">
              <Row type="flex" style="width: 98.8%">
                <Col style="text-align: right;width: 80%;display: flex">
                  <div>
                    <span :style="{fontWeight:'bolder'}" v-if="parseInt(formInline.flag) === 0">稳定性数据表格</span>
                    <span :style="{fontWeight:'bolder'}" v-else>可用性数据表格</span>
                  </div>
                </Col>
              </Row>
            </div>
            <Row v-if="display">
              <Col style="width: 100%">
                <Table size="small" v-if="tableDisplay" stripe :columns="columns" :data="values" ref="table" :loading="loading" :no-data-text = "loadText" style="overflow-x: scroll; max-width: 100%"></Table>
              </Col>
            </Row>
            <Row v-else>
              <Col style="width: 100%; color: #ff9900">
                <Icon type="md-alert" /><span style="font-weight: bolder; margin-left: 5px">未查询到满足条件的数据</span>
              </Col>
            </Row>
          </Card>
          <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
            <div slot="title">
              <Row type="flex" style="width: 98.8%">
                <Col style="text-align: right;width: 80%;display: flex">
                  <div>
                    <span :style="{fontWeight:'bolder'}" v-if="parseInt(formInline.flag) === 0">稳定性数据图表</span>
                    <span :style="{fontWeight:'bolder'}" v-else>可用性数据图表</span>
                  </div>
                </Col>
              </Row>
            </div>
            <div v-if="display">
              <Row type="flex">
                <Col span="12" style="padding-right: 10px">
                  <div id="chart1" class="container1"></div>
                </Col>
                <Col span="12" style="padding-right: 10px">
                  <div id="chart2" class="container1"></div>
                </Col>
              </Row>
              <Row type="flex">
                <Col span="12" style="padding-right: 10px">
                  <div id="chart3" class="container1"></div>
                </Col>
                <Col span="12" style="padding-right: 10px">
                  <div id="chart4" class="container1"></div>
                </Col>
              </Row>
            </div>
            <Row v-else>
              <Col style="width: 100%; color: #ff9900; min-height: 552px">
                <Icon type="md-alert" /><span style="font-weight: bolder; margin-left: 5px">未查询到满足条件的数据</span>
              </Col>
            </Row>
          </Card>
        </Content>
      </Row>
    </Card>
  </div>
</template>
<script>
  import axios from 'axios'
  import Highcharts from 'highcharts/highstock'  // 必须是highstock 不然nevigator不生效
  // import { Bus } from '@/global/bus'

  export default {
    name: 'dashboard-availability',
    data () {
      return {
        display: false,
        directionList: [],
        tableDisplay: true,
        chart: null,
        stockChart1: null,
        stockChart2: null,  // 以上新增加用来画图
        stockChart3: null,
        stockChart4: null,
        categories: [],
        formInline: {
          flag: '0',
          quarter: '2',
          month: '2',
          week: '2',
          day: '2'
        },
        loading: true,
        loadText: '加载中',
        ruleInline: {
          quarter: [{required: true}],
          month: [{required: true}],
          week: [{required: true}],
          day: [{required: true}]
        },
        columns: [],
        values: [],
        weekList: [{
          value: '1',
          label: '1'
        }, {
          value: '2',
          label: '2'
        }, {
          value: '3',
          label: '3'
        }, {
          value: '4',
          label: '4'
        }],
        monthList: [
          {
            value: '1',
            label: '1'
          }, {
            value: '2',
            label: '2'
          }, {
            value: '3',
            label: '3'
          }
        ],
        dayList: [
          {
            value: '1',
            label: '1'
          }, {
            value: '2',
            label: '2'
          }, {
            value: '3',
            label: '3'
          }, {
            value: '4',
            label: '4'
          }, {
            value: '5',
            label: '5'
          }, {
            value: '6',
            label: '6'
          }, {
            value: '7',
            label: '7'
          }
        ]

      }
    },
    methods: {
      getDirectionList: function () {
        let url = 'http://10.82.128.37:8080/api/cargo/bg'
        let self = this
        axios.get(url).then(function (message) {
          self.directionList = message.data
        })
      },
      handleSubmit (name) {
        let self = this
        self.columns = []
        self.values = []
        let stack = name.stack
        let param = {
          flag: name.flag,
          quarter: name.quarter,
          month: name.month,
          week: name.week,
          day: name.day
        }
        if (typeof (stack) !== 'undefined') {
          this.$Spin.show()
          if (stack.length === 1) {
            // 方向数据
            param.bg = stack[0]
          } else {
            // 编排数据
            param.bg = stack[0]
            param.stack = stack[stack.length - 1]
          }
          axios.get('http://10.82.128.37:8080/api/cargo/bg/metrics', {
            params: param
          }).then(function (response) {
            self.$Spin.hide()
            for (let col of response.data.columns) {
              delete col.width
            }
            self.display = true
            setTimeout(function () {
              self.columns = response.data.columns
              self.values = response.data.values
              self.loading = false
              self.refreshTable()
              self.createChart()
            }, 100)
          }).catch(function (error) {
            self.display = false
            console.log(error)
            self.$Spin.hide()
            setTimeout(() => {
              self.$Message.error('加载数据失败')
            }, 1500)
          })
        } else {
          self.display = false
        }
      },
      exportData: function () {
        this.$refs.table.exportCsv({
          filename: '导出原始数据'
        })
      },
      refreshTable: function () {
        this.tableDisplay = false
        let self = this
        setTimeout(function () {
          self.tableDisplay = true
        }, 200)
      },
      createChart: function () {
        let self = this
        let chartData = []
        let chartMonthData = []
        let chartWeekData = []
        let chartDayData = []
        Highcharts.setOptions({
          // navigator: {
          //   enabled: true
          // },
          colors: ['#ed3f14', '#C1FFC1', '#5cadff', '#19be6b', '#ff9900', '#2b85e4'],
          // 禁用UTC时间
          global: {
            useUTC: false
          }
        })
        this.stockChart1 = new Highcharts.Chart('chart1', {
          // navigator: {
          //   enabled: true
          // },
          // colors: ['#ed3f14', '#C1FFC1', '#5cadff', '#19be6b', '#ff9900', '#2b85e4'],
          // // 禁用UTC时间
          // global: {
          //   useUTC: false
          // },
          chart: {
            zoomType: 'x'
          },
          title: {
            text: '季度数据'
          },
          xAxis: {
            type: 'categories', // 设置类型为datetime ,并设置显示格式
            categories: [],
            formatter: function () {
              return this.value
            }
          },
          legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'top'
          },
          credits: {
            enabled: false
          },
          yAxis: {
            max: 100,
            min: 99,
            title: null,
            labels: {
              formatter: function () {
                return this.value + '%'
              }
            },
            lineWidth: 2
          },
          series: [],
          tooltip: {
            shared: true,
            crosshairs: true
            // dateTimeLabelFormats: {
            //   millisecond: '%H:%M:%S.%L',
            //   second: '%H:%M:%S',
            //   minute: '%y-%m-%d %H:%M',
            //   hour: '%y-%m-%d %H:%M',
            //   day: '%y-%m-%d',
            //   week: '%y-%m-%d',
            //   month: '%y-%m',
            //   year: '%Y'
            // }
          },
          plotOptions: {
            line: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              }
            },
            series: {
              events: {
                legendItemClick: function (event) {
                  let series = this.chart.series
                  let index = this.index
                  if (series[index].visible) {
                    // 除当前其他反转
                    for (let item in series) {
                      if (!series[item].visible) {
                        series[item].setVisible(true, true)
                      } else {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  } else {
                    // 当前反转、其他取false
                    series[index].setVisible(true, true)
                    for (let item in series) {
                      if (item !== index) {
                        series[item].setVisible(false, false)
                      }
                    }
                  }
                }
              }
            }
          }
        })
        this.stockChart2 = new Highcharts.Chart('chart2', {
          chart: {
            zoomType: 'x'
          },
          title: {
            text: '月数据'
          },
          xAxis: {
            type: 'categories', // 设置类型为datetime ,并设置显示格式
            categories: []
            // labels: {
            //   formatter: function () {
            //     return Highcharts.dateFormat('%y-%m', new Date(this.x))
            //   }
            // },
            // tooltip: {
            //   pointFormat: '{point.x:%y-%m}: {point.y} '
            // },
            // dateTimeLabelFormats: {
            //   millisecond: '%H:%M:%S.%L',
            //   second: '%H:%M:%S',
            //   minute: '%H:%M',
            //   hour: '%H:%M',
            //   day: '%m-%d',
            //   week: '%m-%d',
            //   month: '%y-%m',
            //   year: '%Y'
            // }
          },
          legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'top'
          },
          credits: {
            enabled: false
          },
          yAxis: {
            max: 100,
            min: 99,
            title: null,
            labels: {
              formatter: function () {
                return this.value + '%'
              }
            },
            lineWidth: 2
          },
          series: [],
          tooltip: {
            shared: true,
            crosshairs: true
            // dateTimeLabelFormats: {
            //   millisecond: '%H:%M:%S.%L',
            //   second: '%H:%M:%S',
            //   minute: '%y-%m-%d %H:%M',
            //   hour: '%y-%m-%d %H:%M',
            //   day: '%y-%m-%d',
            //   week: '%y-%m-%d',
            //   month: '%y-%m',
            //   year: '%Y'
            // }
          },
          plotOptions: {
            line: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              }
            },
            series: {
              events: {
                legendItemClick: function (event) {
                  let series = this.chart.series
                  let index = this.index
                  if (series[index].visible) {
                    // 除当前其他反转
                    for (let item in series) {
                      if (!series[item].visible) {
                        series[item].setVisible(true, true)
                      } else {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  } else {
                    // 当前反转、其他取false
                    series[index].setVisible(true, true)
                    for (let item in series) {
                      if (item !== index) {
                        series[item].setVisible(false, false)
                      }
                    }
                  }
                }
              }
            }
          }
        })
        this.stockChart3 = new Highcharts.Chart('chart3', {
          chart: {
            zoomType: 'x'
          },
          title: {
            text: '周数据'
          },
          xAxis: {
            type: 'categories', // 设置类型为datetime ,并设置显示格式
            categories: []
          },
          legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'top'
          },
          credits: {
            enabled: false
          },
          yAxis: {
            max: 100,
            min: 99,
            title: null,
            labels: {
              formatter: function () {
                return this.value + '%'
              }
            },
            lineWidth: 2
          },
          series: [],
          tooltip: {
            shared: true,
            crosshairs: true
          },
          plotOptions: {
            line: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              }
            },
            series: {
              events: {
                legendItemClick: function (event) {
                  let series = this.chart.series
                  let index = this.index
                  if (series[index].visible) {
                    // 除当前其他反转
                    for (let item in series) {
                      if (!series[item].visible) {
                        series[item].setVisible(true, true)
                      } else {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  } else {
                    // 当前反转、其他取false
                    series[index].setVisible(true, true)
                    for (let item in series) {
                      if (item !== index) {
                        series[item].setVisible(false, false)
                      }
                    }
                  }
                }
              }
            }
          }
        })
        this.stockChart4 = new Highcharts.Chart('chart4', {
          chart: {
            zoomType: 'x'
          },
          title: {
            text: '天数据'
          },
          xAxis: {
            type: 'categories', // 设置类型为datetime ,并设置显示格式
            categories: []
          },
          legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'top'
          },
          credits: {
            enabled: false
          },
          yAxis: {
            max: 100,
            min: 99,
            title: null,
            labels: {
              formatter: function () {
                return this.value + '%'
              }
            },
            lineWidth: 2
          },
          series: [],
          tooltip: {
            shared: true,
            crosshairs: true
          },
          line: {
            dataLabels: {
              // 开启数据标签
              enabled: true
            }
          },
          plotOptions: {
            line: {
              dataLabels: {
                // 开启数据标签
                enabled: true
              }
            },
            series: {
              events: {
                legendItemClick: function (event) {
                  let series = this.chart.series
                  let index = this.index
                  if (series[index].visible) {
                    // 除当前其他反转
                    for (let item in series) {
                      if (!series[item].visible) {
                        series[item].setVisible(true, true)
                      } else {
                        if (item !== index) {
                          series[item].setVisible(false, false)
                        }
                      }
                    }
                  } else {
                    // 当前反转、其他取false
                    series[index].setVisible(true, true)
                    for (let item in series) {
                      if (item !== index) {
                        series[item].setVisible(false, false)
                      }
                    }
                  }
                }
              }
            }
          }
        })
        let category = []
        let categoryMonth = []
        let categoryWeek = []
        let categoryDay = []
        // 获取所有的category
        for (var m = 0; m < self.values.length; m++) {
          let keys = Object.keys(self.values[m])
          for (let j = 0; j < keys.length; j++) {
            // console.log(keys[j] + '   ' + keys[j].length)
            if (keys[j].indexOf('Q') !== -1) {
              if (category.indexOf(keys[j]) === -1) {
                category.push(keys[j])
              }
            }
            if (keys[j].length === 7 && keys[j].indexOf('W') === -1) {
              if (categoryMonth.indexOf(keys[j]) === -1) {
                categoryMonth.push(keys[j])
              }
            }
            if (keys[j].indexOf('W') !== -1) {
              if (categoryWeek.indexOf(keys[j]) === -1) {
                categoryWeek.push(keys[j])
              }
            }
            // if (keys[j].length === 10 || keys[j].length === 27) {
            if (keys[j].length === 10) {
              if (categoryDay.indexOf(keys[j]) === -1) {
                categoryDay.push(keys[j])
              }
            }
          }
        }
        category.sort()
        categoryMonth.sort()
        categoryWeek.sort()
        categoryDay.sort()
        for (var i = 0; i < self.values.length; i++) {
          chartData[i] = {}
          chartMonthData[i] = {}
          chartWeekData[i] = {}
          chartDayData[i] = {}
          chartData[i].data = []
          chartMonthData[i].data = []
          chartWeekData[i].data = []
          chartDayData[i].data = []
          chartData[i].title = self.values[i].direction
          chartMonthData[i].title = self.values[i].direction
          chartWeekData[i].title = self.values[i].direction
          chartDayData[i].title = self.values[i].direction
          // alert(keys.toString())
          let data = self.values[i]
          for (let j = 0; j < category.length; j++) {
            if (data[category[j]]) {
              // console.log(data[category[j]])
              chartData[i].data.push(
                data[category[j]]
              )
            } else {
              chartData[i].data.push(' ')
            }
          }
          for (let j = 0; j < categoryMonth.length; j++) {
            if (data[categoryMonth[j]]) {
              chartMonthData[i].data.push(
                // keys[j],
                data[categoryMonth[j]]
              )
            } else {
              chartMonthData[i].data.push(' ')
            }
          }
          for (let j = 0; j < categoryWeek.length; j++) {
            if (data[categoryWeek[j]]) {
              chartWeekData[i].data.push(
                // keys[j],
                data[categoryWeek[j]]
              )
            } else {
              chartWeekData[i].data.push(' ')
            }
          }
          for (let j = 0; j < categoryDay.length; j++) {
            if (data[categoryDay[j]]) {
              chartDayData[i].data.push(
                // keys[j],
                data[categoryDay[j]]
              )
            } else {
              chartDayData[i].data.push(' ')
            }
          }
        }
        for (let t = 0; t < self.values.length; t++) {
          // alert(keys.toString())
          this.stockChart1.addSeries({
            type: 'line',
            name: chartData[t].title,
            // color: chartData[t].color,
            data: chartData[t].data // 给所有数据添加一个排序
          })
        }
        this.stockChart1.xAxis[0].setCategories(category)
        for (let t = 0; t < self.values.length; t++) {
          // alert(keys.toString())
          this.stockChart2.addSeries({
            type: 'line',
            name: chartMonthData[t].title,
            // color: chartData[t].color,
            data: chartMonthData[t].data// 给所有数据添加一个排序
          })
        }
        this.stockChart2.xAxis[0].setCategories(categoryMonth)
        for (let t = 0; t < self.values.length; t++) {
          // alert(keys.toString())
          this.stockChart3.addSeries({
            type: 'line',
            name: chartWeekData[t].title,
            data: chartWeekData[t].data// 给所有数据添加一个排序
          })
        }
        this.stockChart3.xAxis[0].setCategories(categoryWeek)
        for (let t = 0; t < self.values.length; t++) {
          // alert(keys.toString())
          this.stockChart4.addSeries({
            type: 'line',
            name: chartDayData[t].title,
            data: chartDayData[t].data// 给所有数据添加一个排序
          })
        }
        this.stockChart4.xAxis[0].setCategories(categoryDay)
      }
    },
    mounted: function () {
      this.getDirectionList()
    }
  }
</script>

<style scoped>
  .container1 {
    margin-top: 10px;
    padding-top: 20px;
    /*width: 700px;*/
    height: 250px;
    /*margin-left: 10px;  !*不设置时自动居中*!*/
  }
</style>
