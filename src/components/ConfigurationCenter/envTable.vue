<template>
  <div>
    <Row>
      <!--<Button type="primary" @click="addEnvlist()">新建</Button>-->
      <Input v-model= "search" placeholder="搜索" icon="md-search" style="width: 200px;padding-bottom: 5px;float: right"></Input>
    </Row>
    <Table stripe border :data="data4" :columns="columns5"></Table>
    <div style="margin-top: 5px;padding-bottom: 120px;overflow: hidden;float: right">
      <Page :total="total" :current="1" :page-size-opts="pagesizeopts" :page-size="40" show-sizer  @on-change="changePage" @on-page-size-change="changePage"></Page>
    </div>
    <Modal v-model="modal1" title="负责人信息" @on-ok="edit()" width="820">
      <div v-if="disabledDirection">
        <Tag checked color="primary"><i class="fa fa-sitemap" aria-hidden="true"></i>{{direction}}</Tag>
        <Tag checked color="primary"><i class="fa fa-sitemap" aria-hidden="true"></i>{{sub_direction}}</Tag>
      </div>
      <div v-if="!disabledDirection">
        <div>
          <Input style="margin-top: 10px;max-height: 300px;width: 500px" v-model="direction" placeholder="请输入方向">
          <span slot="prepend">请输入方向</span>
          </Input>
        </div>
        <div>
          <Input style="margin-top: 10px;max-height: 300px;width: 500px" v-model="sub_direction" placeholder="请输入子方向">
          <span slot="prepend">请输入子方向</span>
        </Input>
        </div>
      </div>
      <div style="margin-top: 10px">
        <span style="display: inline-block;margin-right: 10px;font-weight: bolder">环境经办人:</span>
        <span v-if="pushList1.length !== 0" v-for="(item, index) in pushList1">
             <Tag closable color="primary" @on-close="deleteTag(index, pushList1)">{{item.name}}（{{item.mis}}）</Tag>
        </span>
        <Button icon="md-add" type="dashed" size="small" style="margin-left: 5px" @click="showAddModal(0)">添加环境经办人</Button>
      </div>
      <div style="margin-top: 10px">
        <span style="display: inline-block;margin-right: 10px;font-weight: bolder">环境抄送人:</span>
        <span v-if="ccList.length !== 0" v-for="(item, index) in ccList">
             <Tag closable color="primary" @on-close="deleteTag(index, ccList)">{{item.name}}（{{item.mis}}）</Tag>
        </span>
        <Button icon="md-add" type="dashed" size="small" style="margin-left: 5px" @click="showAddModal(1)">添加环境抄送人</Button>
      </div>
    </Modal>
    <Modal v-model="isAddUser" title="添加负责人信息" @on-ok="addPerson(misList)" :mask-closable="false" z-index="1001">
      <Select
        v-model="misList"
        filterable
        clearable
        remote
        :remote-method="getMisName"
        placeholder="请输入mis账号"
        multiple
        :loading="misLoading">
        <Option v-for="item in userList" :value="item.index" :key="item.index">{{item.label}}</Option>
      </Select>
    </Modal>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'
  let envtableData = []
  Bus.$on('refreshEnvTable', function (data, self) {
    self.envtableData = data
    self.data4 = self.initData(self.envtableData, 40)
    self.total = data.length
    self.search = ''
    self.currentPage = 1
    self.changePage()
    setTimeout(() => {
      self.$Spin.hide()
    }, 1500)
  })
  export default {
    name: 'envTable',
    data: function () {
      return {
        misList: [],
        userList: [],
        misLoading: false,
        search: '',
        columns5: [
          {
            title: '方向',
            key: 'direction',
            sortable: true
          },
          {
            title: '子方向',
            key: 'sub_direction',
            sortable: true
          },
          {
            title: '环境经办人',
            key: 'owner',
            sortable: true,
            render: (h, params) => {
              let item = ''
              let item1, item2
              if (params.row['owner']) {
                item1 = params.row['owner'].split(',')
                item2 = params.row['owner_mis'].split(',')
              } else {
                item1 = []
                item2 = []
              }
              for (let i = 0; i < item1.length; i++) {
                item += item1[i] + '(' + item2[i] + ')' + ','
              }
              return h('span', item.substring(0, item.length - 1))
            }
          },
          {
            title: '环境抄送人',
            key: 'cc',
            sortable: true,
            render: (h, params) => {
              let item = ''
              let item1, item2
              if (params.row['cc']) {
                item1 = params.row['cc'].split(',')
                item2 = params.row['cc_mis'].split(',')
              } else {
                item1 = []
                item2 = []
              }
              for (let i = 0; i < item1.length; i++) {
                item += item1[i] + '(' + item2[i] + ')' + ','
              }
              return h('span', item.substring(0, item.length - 1))
            }
          },
          {
            title: '操作',
            key: 'action',
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      this.show(params.index)
                    }
                  }
                }, '编辑')
              ])
            }
          }
        ],
        pagesizeopts: [10, 20, 30, 40, 50],
        disabledDirection: false,
        pushList1: [],
        ccList: [],
        data4: [],
        direction: '',
        sub_direction: '',
        modal1: false,
        isAddUser: false,
        personMis: '',
        personName: '',
        owner: '',
        owner_mis: '',
        cc: '',
        cc_mis: '',
        envtableData: envtableData,
        data6: this.initData(envtableData, 40),
        total: envtableData.length,
        searchData: [],
        editType: '',
        addUserType: 0
      }
    },
    methods: {
      getData: function (self) {
        // axios获取数据   http://10.20.224.96:9444/owner_info
        axios.get(this.getDomain('envmonitor') + '/api/env_monitor_info', {
          timeout: 10000,
          dataType: 'json'}).then(function (message) {
            Bus.$emit('refreshEnvTable', message['data']['info'], self)
            return message['data']['info']
          }).catch(function (error) {
            console.log(error)
          })
      },
      initData: function (data, pageSize) {
        if (data) {
          let data4 = []
          if (pageSize < data.length) {
            for (let i = 0; i < pageSize; i++) {
              let item = data[i]
              let keys = Object.keys(item)
              if (keys.length) {
                for (let j = 0; j < keys.length; j++) {
                  if (item[keys[j]]) {
                    item[keys[j]].toString()
                  }
                }
                data4.push(item)
              }
            }
          } else {
            for (let i = 0; i < data.length; i++) {
              let item = data[i]
              let keys = Object.keys(item)
              for (let j = 0; j < keys.length; j++) {
                if (item[keys[j]]) {
                  item[keys[j]].toString()
                }
              }
              data4.push(item)
            }
          }
          return data4
        } else {
          return []
        }
      },
      changePage: function () {
        let page = 1
        let pageSize = 40
        for (let i = 0; i < this.$children.length; i++) {
          let child = this.$children[i]
          if (child.currentPage && child.currentPageSize) {
            page = child.currentPage
            pageSize = child.currentPageSize
            this.currentPage = page
          }
        }
        let data = []
        if (!this.search) {
          data = this.envtableData
        } else {
          data = this.searchData
        }

        this.data4 = []
        if (page * pageSize < data.length) {
          for (let i = (page - 1) * pageSize; i < page * pageSize; i++) {
            let item = data[i]
            let keys = Object.keys(item)
            for (let j = 0; j < keys.length; j++) {
              if (item[keys[j]]) {
                item[keys[j]] = item[keys[j]].toString()
              }
            }
            this.data4.push(item)
          }
        } else {
          for (let i = (page - 1) * pageSize; i < data.length; i++) {
            let item = data[i]
            let keys = Object.keys(item)
            for (let j = 0; j < keys.length; j++) {
              if (item[keys[j]]) {
                item[keys[j]] = item[keys[j]].toString()
              }
            }
            this.data4.push(item)
          }
        }
      },
      show: function (index) {
        this.modal1 = true
        this.editType = 'edit'
        this.disabledDirection = true
        this.direction = this.data4[index].direction
        this.sub_direction = this.data4[index].sub_direction
        this.owner = this.data4[index].owner
        this.owner_mis = this.data4[index].owner_mis
        this.pushList1 = []
        if (this.owner) {
          this.owner = this.data4[index]['owner'].split(',')
        } else {
          this.owner = []
        }
        if (this.owner_mis) {
          this.owner_mis = this.data4[index]['owner_mis'].split(',')
        } else {
          this.owner_mis = []
        }
        for (let i = 0; i < this.owner.length; i++) {
          if (this.owner[i] && this.owner_mis[i]) {
            this.pushList1.push({
              mis: this.owner_mis[i],
              name: this.owner[i]
            })
          }
        }

        this.cc = this.data4[index].cc
        this.cc_mis = this.data4[index].cc_mis
        this.ccList = []
        if (this.cc) {
          this.cc = this.data4[index]['cc'].split(',')
        } else {
          this.cc = []
        }
        if (this.cc_mis) {
          this.cc_mis = this.data4[index]['cc_mis'].split(',')
        } else {
          this.cc_mis = []
        }
        for (let i = 0; i < this.cc.length; i++) {
          if (this.cc[i] && this.cc_mis[i]) {
            this.ccList.push({
              mis: this.cc_mis[i],
              name: this.cc[i]
            })
          }
        }
      },
      showAddModal: function (type) {
        this.isAddUser = true
        this.addUserType = type
      },
      deleteTag (index, count) {
        count.splice(index, 1)
      },
      addPerson: function (data) {
        for (let item of data) {
          this.personMis = item.split('=')[0]
          this.personName = item.split('=')[1]
          if (this.addUserType === 0) {
            if (this.personName && this.personMis) {
              let unit = [{
                mis: this.personMis,
                name: this.personName
              }]
              this.pushList1 = this.arrayUniqueConcat(this.pushList1, unit, 'mis')
            }
          } else {
            if (this.personName && this.personMis) {
              let unit = [{
                mis: this.personMis,
                name: this.personName
              }]
              this.ccList = this.arrayUniqueConcat(this.ccList, unit, 'mis')
            }
          }
          this.personName = ''
          this.personMis = ''
        }
        this.userList = []
        this.misList = []
      },
      /*
      addEnvlist: function () {
        this.modal1 = true
        this.editType = 'add'
        this.pushList1 = []
        this.disabledDirection = false
        this.direction = ''
        this.sub_direction = ''
      },
      */
      edit (index) {
        let self = this
        if (self.editType === 'edit') {
          // 修改配置
          self.$Spin.show()
          axios.post(this.getDomain('envmonitor') + '/api/env_monitor_info', {
            'direction': self.direction,
            'sub_direction': self.sub_direction,
            'owner': self.owner,
            'owner_mis': self.owner_mis,
            'cc': self.cc,
            'cc_mis': self.cc_mis
          }).then(function () {
            setTimeout(() => {
              self.$Message.success('修改成功')
              self.$Spin.hide()
              self.getData(self)
            }, 1500)
          }).catch(function (error) {
            console.log(error)
            setTimeout(() => {
              self.$Spin.hide()
              self.$Message.error('修改失败')
            }, 1500)
          })
        }
     /*
        else {
          self.$Spin.show()
          axios.post('http://10.4.243.130:9444/owner_info', {
            'direction': self.direction,
            'sub_direction': self.sub_direction,
            'owner': self.owner,
            'owner_mis': self.owner_mis
          }).then(function () {
            setTimeout(() => {
              self.$Message.success('新建成功')
              self.$Spin.hide()
              self.getData(self)
            }, 1500)
          }).catch(function (error) {
            console.log(error)
            setTimeout(() => {
              self.$Spin.hide()
              self.$Message.error('新建失败')
            }, 1500)
          })
        }
        */
      },
      getMisName (query) {
        this.misLoading = true
        let self = this
        let mislist = []
        if (query) {
          this.userList = []
          axios.post(this.getDomain('account') + '/toolchain/common/userlist?word=' + query).then((message) => {
            self.misLoading = false
            if (message.data.status === 0) {
              if (message.data.data) {
                let data = message.data.data
                for (let config in data) {
                  if (data[config]) {
                    let user = data[config]
                    const temp = {}
                    temp.label = user.name + '(' + user.mis + ')'
                    temp.value = user.mis
                    temp.index = user.mis + '=' + user.name
                    if (temp.label && temp.value) {
                      mislist.push(temp)
                    }
                  }
                }
              }
              let list = mislist.map((item) => {
                return { value: item.value, label: item.label, index: item.index }
              })
              self.userList = list.filter(this.createMisIdFilter(query))
              query = ''
              clearTimeout(this.timeout)
              this.timeout = setTimeout(() => {
              }, 3000 * Math.random())
            }
          })
        } else {
          this.userList = []
          this.misList = []
        }
      },
      createMisIdFilter (queryString) {
        return (item) => {
          return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
        }
      }
    },
    watch: {
      pushList1: function () {
        this.owner = []
        this.owner_mis = []
        for (let item of this.pushList1) {
          this.owner.push(item.name)
          this.owner_mis.push(item.mis)
        }
      },
      ccList: function () {
        this.cc = []
        this.cc_mis = []
        for (let item of this.ccList) {
          this.cc.push(item.name)
          this.cc_mis.push(item.mis)
        }
      },
      search: function () {
        let pageSize = 40
        for (let i = 0; i < this.$children.length; i++) {
          let child = this.$children[i]
          if (child.currentPage && child.currentPageSize) {
            pageSize = child.currentPageSize
          }
        }
        this.data4 = []
        let data = this.envtableData
        if (this.search) {
          for (let i = 0; i < data.length; i++) {
            let item = data[i]
            let keys = Object.keys(item)
            for (let j = 0; j < keys.length; j++) {
              if (item[keys[j]]) {
                if (item[keys[j]].toString().toUpperCase().indexOf(this.search.toUpperCase()) !== -1) {
                  this.data4.push(item)
                  break
                }
              }
            }
          }
          this.searchData = this.data4
          this.total = this.data4.length
          this.changePage()
        } else {
          this.data4 = this.initData(this.envtableData, pageSize)
          this.total = this.envtableData.length
          this.searchData = []
          this.changePage()
        }
      }
    },
    // beforeCreate: function () {
    //   this.$Spin.show()
    // },
    mounted: function () {
      let self = this
      self.getData(self)
    }
  }
</script>
