<template>
  <div>
    <head-component></head-component>
    <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px', bordercolor:'silver'}" dis-hover>


    <div slot="title">
        <h1 style="font-weight: bolder;font-size: 24px;text-align:center">酒店线上数据同步至线下</h1>
     </div>

      <Card :bordered="true" id="notice-card" style="width: 80%;margin-left:10%;margin-top: 10px">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/127242809" target="_blank" style="font-weight: bolder" >当前线下可用数据链接 </a><span>，更新时间：{{DataFileterJobTimer}}</span>
        </span>
        </p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> <span class="ciTip"><a href="https://km.sankuai.com/page/60383478" target="_blank" style="font-weight: bolder" >同步数据应用FAQ </a></span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找 <span class="ciTip">韩莹（hanying05）</span></p>
      </Card>


      <Card id="cardhead_2" style="width: 80%;margin-left:10%;margin-top: 10px">
        <div slot="title">
          <span :style="{fontWeight:'bolder'}">数据库表同步任务</span>
        </div>
        <div>
          <Row type="flex">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="https://dba.sankuai.com/index.php/v2/db_daily_operations/mBitCoin#dpolc" target="_blank">DBA同步任务入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{DBASyncJobTimer}}</span>
            </Col>
          </Row>
          <Row type="flex">
            <Col span="24" style="margin-top: 20px;margin-left: 20px">
            <span>计划同步任务总数：{{DBASyncJobtotalDemand}}个</span>
            </Col>
          </Row>
          <Row type="flex">
            <Col span="24" style="margin-left: 20px;margin-top: 20px">
            <span>执行成功任务数量：{{DBASyncJobsuccessDemand}}个</span>
            </Col>
          </Row>
          <Row type="flex">
            <Col span="24" style="margin-top: 20px;margin-left: 20px">
            <span>执行中任务数量：{{DBASyncJobfailureDemand}}个</span>
            </Col>
          </Row>
          <Row type="flex">
            <Col span="6" style="margin-left: 20px;margin-top: 20px">
            <span>执行失败任务数量ID：</span>
            </Col>
            <Col span="18" style="margin-left: 20px;margin-top: 20px">
            <span v-for="item of DBASyncJobfailureDemandList">
              <tag color="error">{{item}}</tag>
            </span>
            </Col>
          </Row>
        </div>
      </Card>
      <Card id="cardhead_3" style="width: 80%;margin-left:10%;margin-top: 10px">
        <div slot="title">
          <span :style="{fontWeight:'bolder'}">价格表mta_price_day冲突解决任务</span>
        </div>
        <div>
          <Row type="flex">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://ci2.sankuai.com/job/hotel_service/job/Hotel-PriceCleanJob" target="_blank">清除冲突数据任务入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{PriceCleanJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="PriceCleanJobStatus === '执行失败'" color="error">{{PriceCleanJobStatus}}</Tag>
              <Tag v-if="PriceCleanJobStatus === '成功'" color="success">{{PriceCleanJobStatus}}</Tag>
              <Tag v-if="PriceCleanJobStatus === '进行中'">{{PriceCleanJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
          <Row type="flex" style="margin-top: 20px">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://ci2.sankuai.com/job/hotel_service/job/Hotel-PriceSynJob" target="_blank">导入新数据任务入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{PricesyncJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="PricesyncJobStatus === '执行失败'" color="error">{{PricesyncJobStatus}}</Tag>
              <Tag v-if="PricesyncJobStatus === '成功'" color="success">{{PricesyncJobStatus}}</Tag>
              <Tag v-if="PricesyncJobStatus === '进行中'">{{PricesyncJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
        </div>
      </Card>
      <Card id="cardhead_4" style="width: 80%;margin-left:10%;margin-top: 10px">
        <div slot="title">
          <span :style="{fontWeight:'bolder'}">各类缓存同步任务</span>
        </div>
        <div>
          <Row type="flex">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://crane.mws-test.sankuai.com/#/task/com.sankuai.hotel.cbs.daedalustask/history/task_201807101744_0025" target="_blank">国内Poi缓存任务入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{DaedalusJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="DaedalusJobStatus === '执行失败'" color="error">{{DaedalusJobStatus}}</Tag>
              <Tag v-if="DaedalusJobStatus === '成功'" color="success">{{DaedalusJobStatus}}</Tag>
              <Tag v-if="DaedalusJobStatus === '进行中'">{{DaedalusJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
          <Row type="flex" style="margin-top: 20px">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://ci2.sankuai.com/job/hotel_service/job/HotelOH-POI-Sync" target="_blank">海外Poi缓存入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{OhPoiSyncJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="OhPoiSyncJobStatus === '执行失败'" color="error">{{OhPoiSyncJobStatus}}</Tag>
              <Tag v-if="OhPoiSyncJobStatus === '成功'" color="success">{{OhPoiSyncJobStatus}}</Tag>
              <Tag v-if="OhPoiSyncJobStatus === '进行中'">{{OhPoiSyncJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
          <Row type="flex" style="margin-top: 20px">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://crane.mws-test.sankuai.com/#/task/com.sankuai.hotel.goods.goodsoperator/history/task_201807031303_0012" target="_blank">房态产品缓存入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{GoodsOperatorJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="GoodsOperatorJobStatus === '执行失败'" color="error">{{GoodsOperatorJobStatus}}</Tag>
              <Tag v-if="GoodsOperatorJobStatus === '成功'" color="success">{{GoodsOperatorJobStatus}}</Tag>
              <Tag v-if="GoodsOperatorJobStatus === '进行中'">{{GoodsOperatorJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
        </div>
      </Card>
      <Card id="cardhead_5" style="width: 80%;margin-left:10%;margin-top: 10px">
        <div slot="title">
          <span :style="{fontWeight:'bolder'}">数据库表同步任务</span>
        </div>
        <div>
          <Row type="flex">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://dx-ee-plus06.dx.sankuai.com:8415/job/periodic_check/job/hotelsearch_indexmaker/" target="_blank">新建搜索索引入口</a>
            </div>
            </Col>
            <Col span="12">
            <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{SearchIndexJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="SearchIndexJobtatus === '执行失败'" color="error">{{SearchIndexJobtatus}}</Tag>
              <Tag v-if="SearchIndexJobtatus === '成功'" color="success">{{SearchIndexJobtatus}}</Tag>
              <Tag v-if="SearchIndexJobtatus === '进行中'">{{SearchIndexJobtatus}}</Tag>
            </div>
            </Col>
          </Row>
        </div>
      </Card>
      <Card id="cardhead_1" style="width: 80%;margin-left:10%;margin-top: 10px">
        <div slot="title">
          <span :style="{fontWeight:'bolder'}">数据筛选任务及可用数据</span>
        </div>
        <div>
          <Row type="flex">
            <Col span="9">
            <div style="margin-left: 20px;">
              <a href="http://ci2.sankuai.com/job/hotel_service/job/Hotel-dataFileterJob/" target="_blank">数据筛选任务入口</a>
            </div>
            </Col>
            <Col span="12">
              <span style="text-align: center;margin-left: 5px">上一次任务执行时间：{{DataFileterJobTimer}}</span>
            </Col>
            <Col span="3">
            <div style="text-align: center;">
              <Tag v-if="DataFileterJobStatus === '执行失败'" color="error">{{DataFileterJobStatus}}</Tag>
              <Tag v-if="DataFileterJobStatus === '成功'" color="success">{{DataFileterJobStatus}}</Tag>
              <Tag v-if="DataFileterJobStatus === '进行中'">{{DataFileterJobStatus}}</Tag>
            </div>
            </Col>
          </Row>
        </div>
      </Card>
    </Card>
  </div>
</template>

<script>
  import Head from '@/components/Common/Head'
  import axios from 'axios'

  export default {
    components: {Head},
    name: 'data-synchronization',
    data: function () {
      return {
        display4: false,
        OhPoiSyncJobStatus: '',
        OhPoiSyncJobTimer: '',
        SearchIndexJobtatus: '',
        SearchIndexJobTimer: '',
        DBASyncJobStatus: '',
        DBASyncJobTimer: '',
        DBASyncJobProcessDemand: '',
        DBASyncJobtotalDemand: '',
        DBASyncJobsuccessDemand: '',
        DBASyncJobfailureDemand: '',
        DBASyncJobfailureDemandList: '',
        PricesyncJobStatus: '',
        PricesyncJobTimer: '',
        DataFileterJobStatus: '',
        DataFileterJobTimer: '',
        PriceCleanJobStatus: '',
        PriceCleanJobTimer: '',
        GoodsOperatorJobStatus: '',
        GoodsOperatorJobTimer: '',
        DaedalusJobStatus: '',
        DaedalusJobTimer: ''
      }
    },
    methods: {
      getinitialparam () {
        const self = this
        axios.get('http://analytics.hotel.test.sankuai.com/datasync/query', {
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          console.log('message', message['data'])
          if (message['data']['status'] === 0) {
            let syncdata = message['data']['data']
            if (syncdata['OhPoiSyncJob'].status === 0) {
              self.OhPoiSyncJobStatus = syncdata['OhPoiSyncJob']['jobstatus']
              self.OhPoiSyncJobTimer = syncdata['OhPoiSyncJob']['timestr']
            } else {
              self.OhPoiSyncJobStatus = '未知'
              self.OhPoiSyncJobTimer = '0000-00-00'
            }
            if (syncdata['SearchIndexJob'].status === 0) {
              self.SearchIndexJobtatus = syncdata['SearchIndexJob']['jobstatus']
              self.SearchIndexJobTimer = syncdata['SearchIndexJob']['timestr']
            } else {
              self.SearchIndexJobStatus = '未知'
              self.SearchIndexJobTimer = '0000-00-00'
            }
            if (syncdata['DBASyncJob'].status === 0) {
              self.DBASyncJobStatus = syncdata['DBASyncJob']['jobstatus']
              self.DBASyncJobTimer = syncdata['DBASyncJob']['timestr']
              self.DBASyncJobProcessDemand = syncdata['DBASyncJob']['detail'].processDemand
              self.DBASyncJobtotalDemand = syncdata['DBASyncJob']['detail'].totalDemand
              self.DBASyncJobsuccessDemand = syncdata['DBASyncJob']['detail'].successDemand
              self.DBASyncJobfailureDemand = syncdata['DBASyncJob']['detail'].failureDemand
              self.DBASyncJobfailureDemandList = syncdata['DBASyncJob']['detail'].failureDemandList
            } else {
              self.DBASyncJobStatus = '未知'
              self.DBASyncJobTimer = '0000-00-00'
            }
            if (syncdata['PricesyncJob'].status === 0) {
              self.PricesyncJobStatus = syncdata['PricesyncJob']['jobstatus']
              self.PricesyncJobTimer = syncdata['PricesyncJob']['timestr']
            } else {
              self.PricesyncJobStatus = '未知'
              self.PricesyncJobTimer = '0000-00-00'
            }
            if (syncdata['DataFileterJob'].status === 0) {
              self.DataFileterJobStatus = syncdata['DataFileterJob']['jobstatus']
              self.DataFileterJobTimer = syncdata['DataFileterJob']['timestr']
            } else {
              self.DataFileterJobStatus = '未知'
              self.DataFileterJobTimer = '0000-00-00'
            }
            if (syncdata['PriceCleanJob'].status === 0) {
              self.PriceCleanJobStatus = syncdata['PriceCleanJob']['jobstatus']
              self.PriceCleanJobTimer = syncdata['PriceCleanJob']['timestr']
            } else {
              self.PriceCleanJobStatus = '未知'
              self.PriceCleanJobTimer = '0000-00-00'
            }
            if (syncdata['GoodsOperatorJob'].status === 0) {
              self.GoodsOperatorJobStatus = syncdata['GoodsOperatorJob']['jobstatus']
              self.GoodsOperatorJobTimer = syncdata['GoodsOperatorJob']['timestr']
            } else {
              self.GoodsOperatorJobStatus = '未知'
              self.GoodsOperatorJobTimer = '0000-00-00'
            }
            if (syncdata['DaedalusJob'].status === 0) {
              self.DaedalusJobStatus = syncdata['DaedalusJob']['jobstatus']
              self.DaedalusJobTimer = syncdata['DaedalusJob']['timestr']
            } else {
              self.DaedalusJobStatus = '未知'
              self.DaedalusJobTimer = '0000-00-00'
            }
          } else {
            alert('接口请求出错')
          }
        })
      }
    },
    mounted: function () {
      this.getinitialparam()
    }
  }
</script>

<style>
  #cardhead_1 > div.ivu-card-head {
    background-color: #f0f0f0 !important;
  }
  #cardhead_2 > div.ivu-card-head {
    background-color: #f0f0f0 !important;
  }
  #cardhead_3 > div.ivu-card-head {
    background-color: #f0f0f0 !important;
  }
  #cardhead_4 > div.ivu-card-head {
    background-color: #f0f0f0 !important;
  }
  #cardhead_5 > div.ivu-card-head {
    background-color: #f0f0f0 !important;
  }

</style>
