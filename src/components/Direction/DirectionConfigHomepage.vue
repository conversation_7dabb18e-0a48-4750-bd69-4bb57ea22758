<template>
  <div>
    <div v-if="name">
      <head-component></head-component>
      <Breadcrumb style="margin-top: 15px; margin-left: 30px">
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/direction/homepage">方向管理</BreadcrumbItem>
        <BreadcrumbItem v-if="parentDirection.length > 0" v-for="item in parentDirection" :key="item.value" :to="getJumpUrl(item.value)" target="_blank">{{item.label}}</BreadcrumbItem>
        <BreadcrumbItem :to="'/direction/detail/config/' + this.direction">{{name}}</BreadcrumbItem>
      </Breadcrumb>
      <direction-common :mode="mode" :name="name" :enable="enable" :parentKey="parentDirection" :childKey="childDirection"></direction-common>
      <Card :style="cardStyle" dis-hover="">
        <Tabs :value="tabName" @on-click="changeUrl">
          <TabPane :label="AnalyticsLabel" name="basic">
            <direction-analytics-homepage :editable="editable"></direction-analytics-homepage>
          </TabPane>
        </Tabs>
      </Card>
    </div>
    <div v-else>
      <content-not-found :needHeader="true" :needBreadCrumb="true"></content-not-found>
    </div>
  </div>

</template>

<script>
  import { Bus } from '@/global/bus'
  import axios from 'axios'
  import DirectionCommon from '../Direction/DirectionCommon'
  import ContentNotFound from '../Common/NotFoundContent'
  import DirectionAnalyticsHomepage from './DirectionAnalytics/DirectionAnalyticsHomepage'
  let screeHeight = window.screen.height

  let noticeStyle = {
    'marginLeft': '30px',
    'marginRight': '30px',
    'marginTop': '15px'
  }

  let cardStyle = {
    'marginLeft': '30px',
    'marginRight': '30px',
    'marginTop': '15px',
    'minHeight': (screeHeight - 285).toString() + 'px',
    'marginBottom': '20px'
  }

  export default {
    name: 'direction-config-homepage',
    components: {
      DirectionCommon, ContentNotFound, DirectionAnalyticsHomepage},
    data: function () {
      return {
        mode: '配置中心',
        cardStyle: cardStyle,
        tabNameList: ['basic'],
        leafTabNameList: [],
        tabName: 'basic',
        direction: '',
        tab: '',
        tabCI: 'basicCI',
        target: '配置中心',
        paramid: this.$route.params.id,
        noticeStyle: noticeStyle,
        mis: Bus.userInfo.userLogin,
        name: '',
        editable: false,
        parentDirection: [],
        childDirection: [],
        enable: false,
        is_leaf: false,
        typeNameDict: {
          config: '配置中心',
          result: '结果中心'
        },
        AnalyticsLabel: (h) => {
          return h('div', [
            h('Tag', {
              attrs: {
                class: 'ivu-tag-dot-inner'
              },
              style: {
                backgroundColor: '#2b85e4'
              }
            }),
            h('span', '过程度量项目配置')
          ])
        }
      }
    },
    methods: {
      getDetail: function () {
        let self = this
        axios.get(this.getDomain('config') + '/mcd/org/basic_info?direction_id=' + this.direction).then(function (message) {
          if (message.data.result) {
            let data = message.data.info
            if (data) {
              self.name = data.direction_name
              self.enable = data.if_enable
              self.parentDirection = []
              self.childDirection = []
              self.editable = false
              if (self.leafTabNameList.indexOf(self.tab) !== -1) {
                self.name = ''
                Bus.$emit('refreshNotFoundComponents404')
              }
              let parentDirection = message.data.info.parent_list
              for (let each in parentDirection) {
                if (parentDirection[each].direction_id !== 1 && parentDirection[each].direction_id !== parseInt(self.direction)) {
                  let temp = {
                    value: parentDirection[each].direction_id,
                    label: parentDirection[each].direction_name
                  }
                  self.parentDirection.push(temp)
                }
              }
              let childDirection = message.data.info.primary_child
              if (childDirection.length === 0) {
                self.editable = true
              } else {
                for (let each in childDirection) {
                  let temp = {
                    value: childDirection[each].direction_id,
                    label: childDirection[each].direction_name
                  }
                  self.childDirection.push(temp)
                }
              }
            } else {
              self.name = ''
              Bus.$emit('refreshNotFoundComponents404')
              self.enable = ''
              self.parentDirection = []
              self.childDirection = []
            }
          } else {
            self.$Modal.error({
              title: '获取方向信息失败',
              content: '<P>未知错误</P>'
            })
          }
        })
      },
      getJumpUrl: function (value) {
        return '/direction/detail/config/' + value.toString() + '/basic'
      },
      changeUrl: function (value) {
        if (this.tabNameList.indexOf(value) !== -1) {
          this.$router.push({ path: '/direction/detail/config/' + this.direction + '/' + value, query: { direction: this.direction } })
        }
      }
    },
    mounted: function () {
      let param = this.$route.params
      this.direction = param.id
      if (param.hasOwnProperty('tab')) {
        this.tab = param.tab
      } else {
        this.tab = 'basic'
      }
      if (this.tab && this.tab !== 'undefined') {
        // 指定tab
        if (this.tabNameList.indexOf(this.tab) !== -1) {
          // 合法tab
          this.tabName = this.tab
          this.$router.push({ path: '/direction/detail/config/' + this.direction + '/' + this.tab, query: { direction: this.direction } })
          this.getDetail()
        } else {
          this.name = ''
          Bus.$emit('refreshNotFoundComponents404')
        }
      } else {
        // 没有指定tab
        this.getDetail()
      }
    }
  }
</script>

<style scoped>

</style>
