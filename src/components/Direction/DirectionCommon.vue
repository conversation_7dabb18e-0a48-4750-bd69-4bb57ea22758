<template>
  <div>
    <Card :bordered="true" :style="noticeStyle" dis-hover="">
      <Row type="flex">
        <Col span="18">
        <Row style="display: flex">
          <i class="fa fa-list-ul" style="padding-right: 15px;font-size: 24px; margin-top:2px"></i>
          <span style="font-weight: bolder;font-size: 20px">{{name}}方向</span>
          <Tag v-if="enable" color="success" style="margin-left: 5px;margin-top: 4px">启用</Tag>
          <Tag v-if="!enable" color="error" style="margin-left: 5px;margin-top: 4px">停用</Tag>
        </Row>
        <Row v-if="childKey" style="margin-top: 8px">
          <Button size="small" icon="md-return-right" class="button button-blue" style="margin-right: 5px;" v-for="(item, index) in childKey" :key="index" :to="'/direction/detail/' + config[0] + '/' + item.value + '/' + config[1]" target="_blank">{{item.label}}</Button>
        </Row>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
  import axios from 'axios'
  let noticeStyle = {
    'marginLeft': '30px',
    'marginRight': '30px',
    'marginTop': '15px'
  }
  export default {
    name: 'direction-common',
    props: {
      mode: {
        type: String,
        default: '配置中心'
      },
      name: {
        type: String,
        default: ''
      },
      enable: {
        type: Boolean,
        default: false
      },
      parentKey: {
        type: Array,
        default: function () {
          return []
        }
      },
      childKey: {
        type: Array,
        default: function () {
          return []
        }
      }
    },
    data: function () {
      return {
        direction: '',
        target: '配置中心',
        auth: false,
        noticeStyle: noticeStyle,
        config: this.getRouter(),
        typeNameDict: {
          config: '配置中心',
          result: '结果中心'
        }
      }
    },
    methods: {
      getChangeModeAuth: function (openList) {
        let parentList = []
        if (this.parentKey) {
          for (const each of this.parentKey) {
            parentList.push(each.value.toString())
          }
        }
        let auth = false
        if (openList.indexOf(this.$route.params.id) !== -1) {
          auth = true
        } else {
          if (parentList) {
            for (const key of parentList) {
              if (openList.indexOf(key) !== -1) {
                auth = true
              }
            }
          }
        }
        this.auth = auth
      },
      getOpenList: function () {
        let self = this
        let params = {
          type: 'radar_open_list'
        }
        axios.post(this.getDomain('cq') + '/portal/config/query', JSON.stringify(params)).then(function (message) {
          if (message.data.status === 'success') {
            let data = message.data.data[0].config.openList
            if (data) {
              self.getChangeModeAuth(data)
            }
          }
        })
      },
      getRouter: function () {
        let params = []
        if (this.$route.fullPath.indexOf('result') !== -1) {
          params.push('result')
          params.push('ar')
        } else {
          params.push('config')
          params.push('basic')
        }
        return params
      },
      changeMode: function (value) {
        let target = ''
        for (let key of Object.keys(this.typeNameDict)) {
          if (this.typeNameDict[key] === value) {
            target = key
            break
          }
        }
        if (target === '') {
          target = 'config'
        }
        this.$router.push('/direction/detail/' + target + '/' + this.$route.params.id)
      }
    },
    mounted: function () {
      this.target = this.mode
      this.getOpenList()
    }
  }
</script>

<style scoped>
  .tab {
    margin-top: 30px;
    margin-left: 1%;
    margin-right: 1%;
    width: auto
  }

  .tool-tab > .ivu-tabs-bar {
    border-bottom: 0;
    margin-bottom: -10px;
    margin-top: -10px;
    font-weight: bolder;
  }

  .button {
    margin-bottom: 5px;
    margin-right: 3px;
    font-weight: bolder;
    font-size: 8px;
  }

  .button-blue {
    border-color: #1890FF;
    background-color: #E6F7FF;
    color:#1890FF
  }

  .button-blue:hover {
    border-color: #1890FF;
    background-color: #E6F7FF;
    color:#1890FF;
    opacity: .85
  }

</style>
