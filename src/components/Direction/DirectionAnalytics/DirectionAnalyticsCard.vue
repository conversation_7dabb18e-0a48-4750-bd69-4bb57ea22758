<template>
  <Card :bordered="true" class="project-card">
    <Row style="width: 100%">
      <div style="width: -webkit-fill-available; padding-left: 3px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis; display: inline-flex">
        <a :title="project.projectName" style="width: 80%; color: #2d8cf0;font-size: 18px;font-weight: bolder" href="">
          <h4 style="font-weight: bolder; padding-left: 1px">{{project.projectName}} </h4>
        </a>
        <Tooltip placement="top" content="删除项目" style="margin-top: -8px;margin-right: 10px">
          <Button v-if="project.if_owner && haveAuth && deleteAuth" type="text" @click="confirmDelete"><Icon type="ios-trash-outline" size="25"/>删除项目</Button>
        </Tooltip>
      </div>
      <div style="padding-left: 3px; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
        <div style="margin-top: 5px; display: inline-flex; width: -webkit-fill-available">
          <div style="width: 50%">
            key:
            <span>{{project.projectId}}</span>
          </div>
        </div>
      </div>
    </Row>
  </Card>
</template>

<script>
  import { Bus } from '@/global/bus'
  export default {
    props: {
      project: {
        type: Object,
        default: () => {}
      },
      callback_message: {
        type: String,
        default: ''
      },
      deleteAuth: {
        type: Boolean,
        default: true
      },
      need_normal_switch: {
        type: Boolean,
        default: true
      },
      need_option_row: {
        type: Boolean,
        default: true
      }
    },
    name: 'direction-analytics-card',
    data: function () {
      return {
        serverName: {
          ones: 'Ones'
        },
        mis: Bus.userInfo.userLogin,
        haveAuth: this.toolchainAuth(this.mis),
        query: this.$route.query.direction
      }
    },
    methods: {
      confirmAddBlackList: function () {
        let self = this
        this.$Modal.confirm({
          title: '是否确认将该项目加入黑名单？',
          onOk: () => {
            self.addBlackProject()
          },
          onCancel: () => {
          }
        })
      },
      addBlackProject: function () {
        Bus.$emit('refreshAddBlackList', this.project)
      },
      confirmDelete: function () {
        let self = this
        this.$Modal.confirm({
          title: '是否确认解除项目关联？',
          onOk: () => {
            self.deleteProject()
          },
          onCancel: () => {
          }
        })
      },
      deleteProject: function () {
        Bus.$emit(this.callback_message, this.project)
      }
    },
    mounted: function () {
      this.isHaveRootAuth(this, this.mis)
    }
  }
</script>

<style scoped>
  .project-card {
    border-left-color: #19be6b;
    border-left-width: 3px;
    margin-left: 15px;
    margin-right: 0;
    width: auto;
    margin-bottom: 15px;
  }
</style>
