<template>
  <div>
    <Modal v-model="addNormalModal" title="关联标准Ones项目" @on-ok="ok"  @on-cancel="cancel" :mask-closable="false" width="30%" :styles="{top: '150px', height: '90%', overflow: 'auto'}">
      <div style="display: flex;margin-top: 10px">
        <span style="font-weight: bolder;font-size: 12px;width: 70px;margin-left: 20px;margin-top: 8px">关联项目<span style="color: #ff0000;">*</span></span>
        <Input style="width: 80%;margin-left: 0px" v-model="addprojectName" placeholder="请填写Ones项目ID，如7010"/>
      </div>
    </Modal>
    <Row type="flex">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'0px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <Alert show-icon closable banner style="margin-top: -10px">
          <template slot="desc">
            <div><span style="font-weight: bolder;">1. 接入要求：</span></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder"><Tag style="cursor: initial;margin-right: 5px" color="blue">到店平台/住宿度假项目集下项目</Tag> ：直接在相应的<span style="color:red;font-weight: bolder">叶子</span>方向下进行【项目接入】即可</span></li></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder"><Tag style="cursor: initial;margin-right: 5px" color="orange">其他项目接入前提</Tag> ：从到店标准模版中复制子类型且保证复制后的子类型与原类型<span style="font-weight: bolder;color: red">名称相同</span></span></li></div>
            <div><span style="font-weight: bolder;">2. 复制时请选择标准子类型：</span></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder">产品需求类型id为: 2724</span></li></div>
            <div style="margin-left: 20px;"><li><span style="font-size: 10px;font-weight: bolder">技术需求类型id为: 2718</span></li></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder">后端任务类型id为: 2719</span></li></div>
            <div style="margin-left: 20px;"><li><span style="font-size: 10px;font-weight: bolder">前端任务类型id为: 2742</span></li></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder">后端提测类型id为: 2776</span></li></div>
            <div style="margin-left: 20px;"><li><span style="font-size: 10px;font-weight: bolder">前端提测类型id为: 2775</span></li></div>
            <div style="margin-left: 20px;"><li><span style="color: #2D8CF0;font-size: 10px;font-weight: bolder">缺陷类型id为: 2763</span></li></div>
            <div><span style="font-weight: bolder">3. 项目与方向间关联关系：</span></div>
            <div style="margin-left: 20px;"><li><span>1. 父级方向会显示所有子方向关联的项目，但只做展示不支持新增、删除操作</span></li></div>
            <div style="margin-left: 20px;"><li><span>2. 所有增删操作请到最底层叶子节点中完成</span></li></div>
            <div style="margin-left: 20px;"><li><span>3. 当前方向中可查询到的项目即为过程度量查询数据源，请各使用方合理配置</span></li></div>
            <div><span style="font-weight: bolder">4. 后续会针对使用情况排期进行优化，相关使用上的反馈请联系张淼（zhangmiao07）。</span></div>
          </template>
        </Alert>
        <div style="margin-top: 50px">
          <Row type="flex" style="width: 100%;">
            <Col style="width: 85%; display: flex">
            <span style="font-weight: bolder;font-size: 18px">项目列表</span>
            <div style="margin-top: 3px;margin-left: 3px" v-if="edit">
              <Button icon="md-settings" size="small" class="tool-button" @click="addRelatedProject">项目接入</Button>
              <!--<Button icon="md-settings" size="small" class="tool-button-disnormal" @click="adddisNormalRelatedProject">其他模版接入</Button>-->
            </div>
            </Col>
            <Col style="text-align: right; width: 15%">
            <Input v-model="search" icon="md-search" placeholder="搜索" style="margin-top: 0px"/>
            </Col>
          </Row>
          <div v-if="displayList.length === 0">
            <span style="padding-left: 15px; font-size: 12px;font-weight: bolder">没有接入的项目</span>
          </div>
          <div v-else>
            <div v-if="!isLoading">
              <div style="min-height: 620px">
                <Row type="flex" style="margin-top: 15px">
                  <Col span="6" v-for="(item, index) in displayList" :key="index">
                    <direction-analytics-card :project="item" :callback_message="'deleteProjectList'"></direction-analytics-card>
                  </Col>
                </Row>
                <Row style="text-align: right; margin-top: 15px">
                  <Page :total="Total" :page-size="pageSize" @on-change="changePage" :current="currentPage"></Page>
                </Row>
              </div>
            </div>
            <Spin v-else style="margin-top: 5px">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>Loading</div>
            </Spin>
          </div>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'
  import DirectionAnalyticsCard from './DirectionAnalyticsCard'

  Bus.$on('deleteProjectList', function (data) {
    Bus.DirectionAnalyticsHomepageObject.deleteProject(data)
  })
  export default {
    components: {DirectionAnalyticsCard},
    name: 'direction-analytics-homepage',
    props: {
      editable: {
        type: Boolean,
        default: false
      }
    },
    data: function () {
      return {
        addNormalModal: false,
        adddisNormalTicePipelineModal: false,
        currentStep: 0,
        Total: 0,
        pageSize: 28,
        isLoading: false,
        confirmButtonLoading: false,
        currentPage: 1,
        displayList: [],
        search: '',
        edit: this.editable,
        currentissuetype: '',
        currentissuename: '',
        currenttransitionid: '',
        currentselftransitionid: '',
        issueList: [],
        transitionList: [],
        direction: [],
        directionList: [],
        style: {},
        directionValue: '',
        addprojectName: '',
        self_test_end_status: '',
        self_test_start_status: '',
        self_test_transition_id: 0,
        tice_pipeline_end_status: [],
        tice_pipeline_start_status: [],
        tice_pipeline_transition_id: [],
        tag_start_status: [],
        tag_end_status: [],
        tag_transition_id: [],
        ticetransitionList: [],
        selftransitionList: [],
        tagtransitionList: [],
        mis: Bus.userInfo.userLogin,
        currentdirectionId: this.$route.params.id,
        temporaryDataList: [],
        backupOriginData: [],
        keyRelation: {
          'Ones': 'ones'
        }
      }
    },
    methods: {
      getRelatedProjectList: function () {
        let self = this
        let url = this.getDomain('config') + '/mcd/org/analytics/get?direction_id=' + this.currentdirectionId
        axios.get(url).then(function (message) {
          let data = message.data
          if (data.result) {
            self.temporaryDataList = []
            self.backupOriginData = []
            for (let item of data.info) {
              self.temporaryDataList.push(item)
              self.backupOriginData.push(item)
            }
            self.Total = self.backupOriginData.length
            self.setPageDefault()
          }
        })
      },
      selectstatus: function (item) {
        if (item.length > 0 && item.length < 3) {
          this.tice_pipeline_start_status = []
          this.tice_pipeline_end_status = []
          for (const each of item) {
            if (each.value && each.label) {
              this.tice_pipeline_start_status.push(each.label.split('~')[0].split('【')[1].split('】')[0])
              this.tice_pipeline_end_status.push(each.label.split('~')[1].split('【')[1].split('】')[0])
            }
          }
        } else if (item.length >= 3) {
          this.$Modal.error({
            title: '出现错误',
            content: '提测Pipeline触发状态不能超过2个'
          })
        }
      },
      selectselfstatus: function (item) {
        if (item.value && item.label) {
          this.self_test_start_status = ''
          this.self_test_end_status = ''
          this.self_test_start_status = item.label.split('~')[0].split('【')[1].split('】')[0]
          this.self_test_end_status = item.label.split('~')[1].split('【')[1].split('】')[0]
        }
      },
      selecttagstatus: function (item) {
        if (item.length > 0) {
          this.tag_start_status = []
          this.tag_end_status = []
          for (const each of item) {
            if (each.value && each.label) {
              this.tag_start_status.push(each.label.split('~')[0].split('【')[1].split('】')[0])
              this.tag_end_status.push(each.label.split('~')[1].split('【')[1].split('】')[0])
            }
          }
        }
      },
      setPageDefault: function () {
        this.displayList = []
        if (this.temporaryDataList.length > 0) {
          let count = this.Total > this.pageSize ? this.pageSize : this.Total
          for (let i = 0; i < count; i++) {
            this.displayList.push(this.temporaryDataList[i])
          }
        }
      },
      getDirectionValue: function (value, selectedData) {
        this.directionValue = ''
        let currentDirectionItem = selectedData[selectedData.length - 1]
        if (typeof (currentDirectionItem) !== 'undefined') {
          this.directionValue = currentDirectionItem.direction_id
        }
      },
      getDirectionList: function () {
        let self = this
        axios.get(this.getDomain('config') + '/mcd/org/basic?direction_id=1').then(function (message) {
          self.directionList.length = 0
          if (message.data.result) {
            self.directionList.push(message.data.info)
          }
        }).catch(function () {
          self.directionList.length = 0
        })
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage()
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage()
      },
      changeCurrentPage: function () {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.displayList = []
        let count = this.Total > pageSize * currentPage ? pageSize * currentPage : this.Total
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.displayList.push(this.temporaryDataList[i])
        }
      },
      ok: function () {
        let url = this.getDomain('config') + '/mcd/org/analytics/add'
        let item = {
          direction_id: this.currentdirectionId,
          analytics_info: {
            projectId: this.addprojectName,
            projectName: ''
          },
          editor: this.mis
        }
        this.setNormalOnesRelation(url, item)
      },
      setOnesRelation: function (target, item) {
        let self = this
        let url = this.getDomain('pm') + '/ones/getProjectKey?project_id=' + item.common_project_id[0].key
        axios.get(url).then(function (message) {
          let response = message.data
          self.confirmButtonLoading = false
          if (Number(response.status) === 0) {
            if (response.data) {
              item.common_project_id[0].label = response.data.toString().toUpperCase()
              self.getIssuetype(target, item.common_project_id[0].key, item)
            } else {
              self.$Modal.error({
                title: '输入的Ones id不存在'
              })
            }
          } else {
            self.$Modal.error({
              title: '新增失败',
              content: '<P>查询Ones项目名称失败, 失败原因：' + response.msg.toString() + '</P>'
            })
          }
        }).catch(function () {
          self.confirmButtonLoading = false
          self.$Modal.error({
            title: '新增失败',
            content: '<P>请确保Ones项目id为数字</P>'
          })
        })
      },
      setNormalOnesRelation: function (target, item) {
        let self = this
        let url = this.getDomain('pm') + '/ones/getProjectKey?project_id=' + item.analytics_info.projectId
        axios.get(url).then(function (message) {
          let response = message.data
          if (Number(response.status) === 0) {
            if (response.data) {
              item.analytics_info.projectName = response.data.toString().toUpperCase()
              self.setRelation(target, item)
            } else {
              self.$Modal.error({
                title: '输入的Ones id不存在'
              })
            }
          } else {
            self.$Modal.error({
              title: '新增失败',
              content: '<P>查询Ones项目名称失败, 失败原因：' + response.msg.toString() + '</P>'
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '新增失败',
            content: '<P>请确保Ones项目id为数字</P>'
          })
        })
      },
      setRelation: function (target, item) {
        let self = this
        axios.post(target, item).then(function (mes) {
          let data = mes.data
          if (data.result) {
            self.$Modal.success({
              title: '添加成功'
            })
            self.addNormalModal = false
            self.getRelatedProjectList()
          } else {
            self.$Modal.error({
              title: '新增失败',
              content: '<P>失败原因：' + data.info + '</P>'
            })
          }
        })
      },
      selecttransition: function (item) {
        this.currentissuename = item.label
        let self = this
        let url = this.getDomain('jira') + '/ones/search_tice_workflow?subtype_id=' + item.value
        self.ticetransitionList = []
        self.selftransitionList = []
        self.tagtransitionList = []
        axios.get(url).then(function (message) {
          let response = message.data
          if (Number(response.status) === 0) {
            for (const each in response.data) {
              const item = {
                value: parseInt(response.data[each].id),
                label: '【' + response.data[each].currentStateName + '】' + '~' + '【' + response.data[each].nextStateName + '】'
              }
              self.ticetransitionList.push(item)
              self.selftransitionList.push(item)
              self.tagtransitionList.push(item)
            }
          } else {
            self.$Modal.error({
              title: '获取问题类型失败',
              content: '<P>获取问题类型失败, 失败原因：' + response.msg.toString() + '</P>'
            })
          }
        })
      },
      deleteProject: function (key) {
        let self = this
        let url = this.getDomain('config') + '/mcd/org/analytics/delete'
        axios.post(url, {
          direction_id: self.currentdirectionId,
          analytics_info: key,
          editor: self.mis
        }).then(function (message) {
          let data = message.data
          if (data.result) {
            self.getRelatedProjectList()
          } else {
            self.$Modal.error({
              title: '删除失败',
              content: '<P>服务器内部错误</P>'
            })
          }
        })
      },
      confirmBasicInfo: function () {
        this.confirmButtonLoading = true
        let self = this
        if (this.addprojectName) {
          let url = this.getDomain('config') + '/mcd/org/common_project_id?direction_id'
          if (this.backupOriginData.indexOf(this.addprojectName) === -1) {
            this.backupOriginData.push(this.addprojectName)
          }
          let item = {
            direction_id: this.directionValue ? this.directionValue : this.currentdirectionId,
            common_project_id: [{
              type: 'ones',
              key: this.addprojectName.toUpperCase(),
              is_normal: false
            }],
            merge: true
          }
          this.setOnesRelation(url, item)
        } else {
          self.confirmButtonLoading = false
          self.$Message.info('存在必填参数未填写')
        }
      },
      cancel: function () {
        this.addNormalModal = false
        this.addprojectName = ''
      },
      basicInfoCancel: function () {
        this.adddisNormalTicePipelineModal = false
        this.addprojectName = ''
      },
      ticeInfocancel: function () {
        this.adddisNormalTicePipelineModal = false
        this.getRelatedProjectList()
      },
      confirmTiceInfo: function () {
        let self = this
        let params = {
          project_id: this.addprojectName,
          tice_pipeline_start_status: this.tice_pipeline_start_status,
          tice_pipeline_end_status: this.tice_pipeline_end_status,
          self_test_start_status: this.self_test_start_status,
          self_test_end_status: this.self_test_end_status,
          tice_pipeline_transition_id: this.tice_pipeline_transition_id,
          self_test_transition_id: this.self_test_transition_id,
          subtype_id: this.currentissuetype,
          subtype_name: this.currentissuename,
          tag_start_status: this.tag_start_status,
          tag_end_status: this.tag_end_status,
          tag_transition_id: this.tag_transition_id
        }
        let flag = false
        if (params.self_test_transition_id && params.tice_pipeline_transition_id) {
          if (params.tice_pipeline_transition_id.indexOf(params.self_test_transition_id) !== -1) {
            flag = true
          }
        }
        if (params.tice_pipeline_transition_id.length > 2) {
          this.$Modal.error({
            title: '出现错误',
            content: '提测Pipeline触发状态不能超过2个'
          })
        } else if (flag) {
          this.$Modal.error({
            title: '出现错误',
            content: '提测Pipeline不能与自测Pipeline共用一个状态'
          })
        } else {
          if (params.subtype_id && params.tice_pipeline_transition_id) {
            axios.post(this.getDomain('jira') + '/ones/set_tice_hook', params).then(function (message) {
              let data = message.data
              if (data.status === 0) {
                // self.getIssuetype(item.common_project_id[0].key)
                self.$Modal.success({
                  title: '设置成功'
                })
                self.adddisNormalTicePipelineModal = false
                self.getRelatedProjectList()
              } else {
                self.$Modal.error({
                  title: '设置提测信息失败',
                  content: '<P>失败原因：' + data.msg + '</P>'
                })
              }
            })
          } else {
            self.$Modal.error({
              title: '存在未填写参数'
            })
          }
        }
      },
      setOriginData: function () {
        this.temporaryDataList = []
        let originData = this.backupOriginData
        for (let key of originData) {
          this.temporaryDataList.push(key)
        }
        this.Total = this.temporaryDataList.length
      },
      addRelatedProject: function () {
        this.addprojectName = ''
        this.direction = []
        this.addNormalModal = true
      },
      adddisNormalRelatedProject: function () {
        this.currentStep = 0
        this.adddisNormalTicePipelineModal = true
        this.addprojectName = ''
        this.direction = []
        this.currentissuetype = ''
        this.currenttransitionid = ''
        this.currentissuename = ''
        this.self_test_end_status = ''
        this.self_test_start_status = ''
        this.self_test_transition_id = 0
        this.tice_pipeline_end_status = []
        this.tice_pipeline_start_status = []
        this.tice_pipeline_transition_id = []
        this.tag_start_status = []
        this.tag_end_status = []
        this.tag_transition_id = []
        this.ticetransitionList = []
        this.selftransitionList = []
        this.tagtransitionList = []
        this.currentselftransitionid = ''
      }
    },
    mounted: function () {
      Bus.DirectionAnalyticsHomepageObject = this
      this.getRelatedProjectList()
      this.getDirectionList()
    },
    watch: {
      search: function () {
        if (this.search) {
          this.temporaryDataList = []
          let originData = this.backupOriginData
          for (let key of originData) {
            if (key.projectId.indexOf(this.search.toUpperCase()) !== -1 || key.projectName.indexOf(this.search.toUpperCase()) !== -1) {
              this.temporaryDataList.push(key)
            }
          }
          this.Total = this.temporaryDataList.length
          this.setPageDefault()
        } else {
          this.setOriginData()
          this.setPageDefault()
        }
      },
      editable: function () {
        this.edit = this.editable
      }
    }
  }
</script>

<style scoped>
  .toolchain-step-content {
    margin-top: 5px;
  }

  .toolchain-content {
    background-color: #ffffff;
    margin: 10px 15px 20px 15px;
    padding: 15px 10px 20px 10px;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }

  .tool-button {
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }

  .tool-button-disnormal {
    margin-left: 5px;
    color:#ff9900;
    border-color: #ff9900;
    font-weight: bolder;
  }
</style>
