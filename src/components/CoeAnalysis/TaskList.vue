<template>
    <div class="container">
        <Navigator :configMenu="configMenu" :menu="menu" :breadcrumb="breadcrumb"></Navigator>
        <el-form class="row-thin">
            <el-form-item class="col-3-thin">
                <div class="row-thin">
                    <label class="col-3-thin">创建时间</label>
                    <datepicker class="col-9-thin" :disabled="date.disabled" @change="setDate" :selectDate="[date.startDate,date.endDate]">
                    </datepicker>
                </div>
            </el-form-item>
            <el-form-item class="col-3-thin">
                <label class="col-3-thin">任务名</label>
                <el-input class="col-9-thin" v-model="task_name_match"></el-input>
            </el-form-item>
            <el-form-item class="col-3-thin">
                <label class="col-3-thin">任务id</label>
                <el-input class="col-9-thin" v-model="task_id_match"></el-input>
            </el-form-item>
            <el-form-item class="col-3-thin">
                <el-button  type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
            </el-form-item>
        </el-form>
        <div class="row-thin">
        <div class="col-12-thin">
            <el-table :data="coeTask" v-loading="loading" style="width: 100%">
            <el-table-column label="任务ID" width="200px">
                <template slot-scope="scope">
                    <router-link :key="scope.row.id" :to="{path: get_path(scope.row.id)}">{{ scope.row.id }}</router-link>
                </template>
            </el-table-column>
            <el-table-column v-for="header in headers" :key="header.value" :label="header.text" :width="header.width">
                <template slot-scope="scope">
                    {{ formatText(scope.row[header.value]) }}
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" content="任务详情" placement="top">
                        <router-link class="el-button el-button--primary el-button--mini is-circle" 
                        :key="scope.row.id" :to="{path: get_path(scope.row.id)}">
                        <i class="el-icon-view"></i></router-link>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="任务配置" placement="top">
                        <el-button size="mini" icon="el-icon-setting" circle type="primary" @click="onDialog(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="继续触发" placement="top">
                        <el-popconfirm style="margin: 10px;" title="继续触发会重新执行未完成的任务，确认触发么" @confirm="restartTask(scope.row.id)">
                            <el-button size="mini" slot="reference" icon="el-icon-caret-right" circle type="warning"></el-button>
                        </el-popconfirm>
                    </el-tooltip>
                    <!-- <el-tooltip class="item" effect="dark" content="进行对比" placement="top">
                        <el-popover title="请确定对比的基准ID">
                            <el-input title="基准任务ID" v-model="baselineTaskId" placeholder="任务ID"></el-input>
                            <el-button size="mini" slot="reference" icon="el-icon-caret-right" circle type="warning"></el-button>
                        </el-popover>
                    </el-tooltip> -->
                </template>
            </el-table-column>
            </el-table>
        </div>
        </div>
        <el-pagination class="pagination-rounded" v-if="total > pageSize" :current-page="currentPage" :page-size="pageSize"
            :total="total" @current-change="onPageChange" @size-change="onSizeChange"
            layout="total, sizes, prev, pager, next, jumper" />
        <el-dialog :visible.sync="dialog" title="COE任务配置">
          <TaskDescription :task="currentTaskData"></TaskDescription>
        </el-dialog>
        <el-dialog :visible.sync="metrix_dialog" title="计算任务一致性">
            <div class="card-content">
            </div>
        </el-dialog>
    </div>
</template>

<script>
import DatePicker from './datePicker.vue'
import Navigator from './Navigator.vue'
import TaskDescription from './TaskDescription.vue'
import { Select, Option, FormItem, Form, Pagination, Tree } from 'element-ui'
import api from './store/api'
export default {
  components: {
    ElSelect: Select,
    ElOption: Option,
    ElFormItem: FormItem,
    ELForm: Form,
    ELPagination: Pagination,
    ELTree: Tree,
    datepicker: DatePicker,
    Navigator,
    TaskDescription
  },
  data() {
    return {
      task_name_match: '',
      task_id_match: '',
      searchTask: '',
      currentPage: 1,
      total: 0,
      pageSize: 10,
      coeTask: [],
      loading: false,
      baselineTaskId: '',
      breadcrumb: [{'path': '/coe/coestorage', 'title': '首页'}, {'path': '/coe/task', 'title': '任务列表'}],
      date: {
        disabled: false,
        startDate: null,
        endDate: null
      },
      dialog: false,
      metrix_dialog: false,
      currentTaskData: {
        choosed_coe_list: [],
        sub_task_type_list: []
      },
      headers: [
        {text: '任务名称', value: 'name', width: '180px'},
        {text: '提交人', value: 'submitter', width: '120px'},
        {text: '开始时间', value: 'start_date', width: '120px'},
        {text: '截止时间', value: 'end_date', width: '120px'},
        {text: '执行进度', value: 'progress', width: '100px'},
        {text: '执行状态', value: 'state', width: '100px'},
        {text: '来源', value: 'source', width: '120px'}
      ]
    }
  },
  mounted() {
    let end = new Date()
    let start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    if (this.$route.query.startDate) {
      start = this.prase_date(this.$route.query.startDate)
    }
    if (this.$route.query.endDate) {
      end = this.prase_date(this.$route.query.endDate)
    }
    this.date = {
      disabled: false,
      startDate: start,
      endDate: end
    }
    this.syncTaskList()
  },
  computed: {
    configMenu() {
      return this.$store.state.COEStore.configMenu
    },
    menu() {
      return this.$store.state.COEStore.menu
    }
  },
  methods: {
    prase_date(dateString) {
      const [y, m, d] = dateString.split('-')
      return new Date(parseInt(y), parseInt(m) - 1, parseInt(d))
    },
    formatText(text) {
      if (text && text.length > 50) {
        return text.slice(0, 50) + '...'
      }
      return text
    },
    get_path(id) {
      return `/coe/detail/${id}` + window.location.hash
    },
    batchFilter(value, search, item) {
      return item.batch_id.toLowerCase().indexOf(search.toLowerCase()) !== -1
    },
    onDialog(value) {
      console.log('coe=', value)
      this.currentTaskData = value
      this.dialog = true
    },
    syncTaskList() {
      if (this.date.startDate === null || this.date.endDate === null) {
        this.$notify({title: '必须填写时间', message: '', type: 'error'})
        return
      }
      this.loading = true
      const pages = this.currentPage - 1
      const from_ = this.pageSize * pages
      api.getTaskList(this.formatDate(this.date.startDate),
      this.formatDate(this.date.endDate), this.pageSize,
      from_, this.task_id_match, this.task_name_match).then((result) => {
        this.total = result.data['total']
        this.coeTask = result.data['task_list']
        this.loading = false
      }).catch(error => {
        this.$notify({title: '任务列表获取失败', message: error, type: 'error'})
      })
    },
    restartTask(taskId) {
      api.restartTask(taskId).catch(error => {
        this.$notify({title: '重新执行任务失败', message: error, type: 'error'})
      }).then(result => {
        this.$notify({title: '提示', message: '任务已经触发（提示:分析完毕的任务不会重复执行)', type: 'info'})
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    setDate(value) {
      this.date.startDate = value[0]
      this.date.endDate = value[1]
    },
    onPageChange(page) {
      this.currentPage = page
      this.syncTaskList()
    },
    onSizeChange(size) {
      this.pageSize = size
      this.syncTaskList()
    },
    onSearch() {
      this.currentPage = 1
      this.syncTaskList()
    }
  }

}
</script>

<style>
.search {
    padding: 20px;
}
</style>