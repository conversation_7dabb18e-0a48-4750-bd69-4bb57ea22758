<template>
    <div class="container">
        <Navigator :menu="menu" @refresh="sync" :breadcrumb="breadcrumb"></Navigator>
        <el-form class="row-thin">
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">创建时间</label>
                    <datepicker class="col-9-thin" :disabled="date.disabled" @change="setDate"
                        :selectDate="[date.startDate, date.endDate]">
                    </datepicker>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">COE 定级</label>
                    <el-select class="col-6-thin" v-model="coeLevel" multiple placeholder="请选择 COE 定级">
                        <el-option label="S1" value="S1"></el-option>
                        <el-option label="S2" value="S2"></el-option>
                        <el-option label="S3" value="S3"></el-option>
                        <el-option label="S4" value="S4"></el-option>
                        <el-option label="S9" value="S9"></el-option>
                        <el-option label="事件" value="E"></el-option>
                        <el-option label="无需定级" value="N"></el-option>
                        <el-option label="未定级" value="未定级"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">组织架构</label>
                    <el-select class="col-6-thin" placeholder="请选择组织架构" v-model="selectedOrgName">
                        <el-option value="org">
                            <el-tree :data="orgs" show-checkbox node-key="value" @check-change="onOrgsChange"></el-tree>
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
        </el-form>
        <el-form class="row-thin">
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">标题</label>
                    <el-input class="col-6-thin" v-model="brief_match"></el-input>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">原因分类结果</label>
                    <el-select class="col-6-thin" v-model="cause_match" multiple placeholder="请选择原因分类">
                        <el-option v-for="option in cause_options" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">模板选择</label>
                    <el-select class="col-6-thin" v-model="template_ids" multiple placeholder="请选择模板">
                        <el-option v-for="option in template_options" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <!-- <el-form-item class="col-6-thin">
                <div class="row-thin">
                    <label class="col-1-thin">原因召回</label>
                    <el-input class="col-9-thin"  v-model="cause_search"></el-input>
                </div>
            </el-form-item> -->
        </el-form>
        <el-form class="row-thin">
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">六要两不要初筛</label>
                    <el-select class="col-6-thin" v-model="filter6to2not" multiple placeholder="请选择模板">
                        <el-option v-for="option in find_6to2not_options(taskTypes)" :key="option.type" :label="option.title" :value="option.type"></el-option>
                    </el-select>
                    <el-button @click="onSelectAll_6to2not_pred">全选</el-button>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">六要两不要复审</label>
                    <el-select class="col-6-thin" v-model="filter6to2not_reviewed" multiple placeholder="请选择模板">
                        <el-option v-for="option in find_6to2not_options(taskTypes)" :key="option.type" :label="option.title" :value="option.type"></el-option>
                    </el-select>
                    <el-button @click="onSelectAll_6to2not_pred_reviewed">全选</el-button>
                </div>
            </el-form-item>
            <el-form-item class="col-2-thin">
                <div class="row-thin">
                    <label class="col-6-thin">复审与初筛DIFF</label>
                    <el-checkbox class="col-3-thin" v-model="diff6to2not">是</el-checkbox>
                </div>
            </el-form-item>
            <el-form-item class="col-2-thin">
                <div class="row-thin">
                    <label class="col-6-thin">筛查资金风险</label>
                    <el-checkbox class="col-3-thin" v-model="filterfund">是</el-checkbox>
                </div>
            </el-form-item>
        </el-form>
        <el-form class="row-thin">
            <div class="col-10-thin"></div>
            <el-form-item class="col-2-thin">
                <el-tooltip class="item" effect="dark" content="刷新" placement="top">
                    <el-button icon="el-icon-search" circle type="primary" @click="syncCoeListOnFirstPage"></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="说明" placement="top">
                    <el-button icon="el-icon-question" circle type="warning" @click="info_dialog=true"></el-button>
                </el-tooltip>
                <!-- <el-popconfirm class="coe-pop-confirm-item" effect="dark" content="原因分析聚类" @confirm="aggr_task"
                    title="是否进行COE原因下钻分析" placement="top">
                    <el-button icon="el-icon-aim" circle type="primary" slot="reference"></el-button>
                </el-popconfirm> -->
                <el-tooltip class="item" effect="dark" content="判别式搜素" placement="top">
                    <el-button icon="el-icon-aim" circle type="primary" @click="is_discriminative_retrive_visible=true"></el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>
        <div class="row-thin">
            <div class="col-12">
                <el-table :data="coe_store_list" v-loading="loading" style="width: 100%">
                    <el-table-column label="COE" width="250px">
                        <template slot-scope="scope">
                            <a :href="'https://coe.mws.sankuai.com/detail/' + scope.row.coe_id" target="_blank">{{
                                scope.row.brief }}</a>
                            <el-tag type="warning" size="mini" v-if="scope.row.level">{{ scope.row.level }}</el-tag>
                            <el-tag type="info" size="mini" v-if="scope.row.coe_template_name">{{ scope.row.coe_template_name }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" width="200px">
                        <template slot-scope="scope">
                            {{ removeTZ(scope.row.create_at) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="原因分类标签" width="120px">
                        <template slot-scope="scope">
                            <el-tag type="success" size="mini" v-if="filterResultStr(scope.row.cause_analysis.rd_result, 'cause') &&
                                filterResultStr(scope.row.cause_analysis.analysis_result, 'cause') &&
                                formatText(scope.row.cause_analysis.rd_result) === formatText(scope.row.cause_analysis.analysis_result)"
                                @click="on_tag_click(scope.row, 'cause')">
                                {{ formatText(scope.row.cause_analysis.rd_result) }}
                            </el-tag>
                            <span v-else>
                            <el-tag v-if="filterResultStr(scope.row.cause_analysis.rd_result, 'cause')" 
                            @click="on_tag_click(scope.row, 'cause')" type="info" size="mini">
                                {{ formatText(scope.row.cause_analysis.rd_result) }}</el-tag>
                            <el-tag v-if="filterResultStr(scope.row.cause_analysis.analysis_result, 'cause')" 
                            @click="on_tag_click(scope.row, 'cause')" size="mini">
                                {{ formatText(scope.row.cause_analysis.analysis_result) }}</el-tag>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="资金安全标签" width="120px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.fund_safety && scope.row.fund_safety.fund_aggr_classify" size="mini" 
                            @click="on_tag_click(scope.row, 'fund_aggr_classify')">
                                {{ formatText(scope.row.fund_safety.mole_first_level_tag) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="触发条件标签" width="120px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.trigger_method" type="info" 
                            @click="on_tag_click(scope.row, '')" size="mini">
                                {{ formatText(scope.row.trigger_method.rd_result) }}
                            </el-tag>
                            <el-tag v-if="scope.row.trigger_method.analysis_result" type="info"
                            @click="on_tag_click(scope.row, '')" size="mini">
                                {{ formatText(scope.row.trigger_method.analysis_result) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="六要两不要初筛结果" width="250px">
                        <template slot-scope="scope">
                            <span v-for="typename in to_not_to_types" :key="typename">
                                <el-tag type="warning" v-if="filterResultStr(scope.row[typename].rd_result, typename) 
                                && filterResultStr(scope.row[typename].analysis_result_raw, typename)" 
                                @click="on_tag_click(scope.row, typename)"  size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag>
                                <el-tag type="info" v-else-if="filterResultStr(scope.row[typename].rd_result, typename)"
                                @click="on_tag_click(scope.row, typename)" size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag>
                                <el-tag type="primary" v-else-if="filterResultStr(scope.row[typename].analysis_result_raw, typename)"
                                @click="on_tag_click(scope.row, typename)" size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag>
                                <!-- <el-tag type="success" v-else
                                @click="on_tag_click(scope.row, typename)" size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag> -->
                            </span>
                            <span v-if="to_not_to_types.every(typename => 
                                !filterResultStr(scope.row[typename].analysis_result_raw, typename)
                                && !filterResultStr(scope.row[typename].rd_result, typename)
                                )">
                                无违规项
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="六要两不要复审结果" width="250px">
                        <template slot-scope="scope">
                            <span v-for="typename in to_not_to_types" :key="typename">
                                <el-tag type="primary" v-if="filterResultStr(scope.row[typename].analysis_result, typename)"
                                @click="on_tag_click(scope.row, typename)" size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag>
                                <!-- <el-tag type="success" v-else
                                @click="on_tag_click(scope.row, typename)" size="mini">
                                    {{ formatText(find_type_name(typename)) }}
                                </el-tag> -->
                            </span>
                            <span v-if="to_not_to_types.every(typename => !filterResultStr(scope.row[typename].analysis_result, typename))">
                                无
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-tooltip class="item" effect="dark" content="COE详情" placement="top">
                                <el-button size="mini" icon="el-icon-setting" circle type="primary" @click="onDialog(scope.row)"></el-button>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="分析结果更新" placement="top">
                                <el-button size="mini" icon="el-icon-refresh" circle type="primary" @click="onResultUpdate(scope.row.coe_id)"></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-pagination class="pagination-rounded" v-if="total > pageSize" :current-page="currentPage" :page-size="pageSize"
            :total="total" @current-change="onPageChange" @size-change="onSizeChange"
            layout="total, sizes, prev, pager, next, jumper" />
        <el-dialog :visible.sync="detail_dialog" title="COE详情">
          <StorageDescription :storage="storage_data"></StorageDescription>
        </el-dialog>
        <el-dialog :visible.sync="info_dialog" title="说明">
          <h4 class="info-content">操作说明</h4>
          <div class="info-content">· 勾选“筛查六要两不要”即可获取所有违规的COE，只要有自主上报或者LLM分析违规，就会召回</div>
          <div class="info-content">· 勾选“筛查资金风险”即可获取所有可能存在资金安全问题的COE</div>
          <div class="info-content">· 勾选“包含六要两不要分析DIFF”会在搜索结果中找到有修改的标签</div>
          <div class="info-content">· 点击COE链接即可跳转到COE平台相应COE</div>
          <div class="info-content">· 点击配置按钮<i class="el-icon-setting"></i>即可查看COE详情</div>
          <div class="info-content">· 点击刷新按钮<i class="el-icon-refresh"></i>可以同步最新的LLM分析结果</div>
          <br>
          <h4 class="info-content">标签说明</h4>
          <div class="info-content">· <el-tag size="mini">蓝色标签</el-tag>为大模型生成结果</div>
          <div class="info-content">· <el-tag size="mini" type="info">灰色标签</el-tag>为RD填写结果</div>
          <div class="info-content">· 原因分析中的<el-tag size="mini" type="success">绿色标签</el-tag>，代表RD填写的内容与LLM分析结果一致</div>
          <div class="info-content">· 六要两不要中的<el-tag size="mini" type="success">绿色标签</el-tag>，代表没违反</div>
          <div class="info-content">· 六要两不要中的<el-tag size="mini" type="warning">黄色标签</el-tag>，代表LLM召回且RD已经自主上报</div>
          <br>
          <h4 class="info-content">数据说明</h4>
          <div class="info-content">· 每天会同步昨天存在更新的COE</div>
          <div class="info-content">· 模型分析结果优先取review过的，如果COE没有进行过review，那么就取最新一次成功跑出的结果</div>
          <div class="info-content" v-if="RESULT_FILTER_WHITE_LIST.length!==0">
            · 原因分类取值范围是：{{ RESULT_FILTER_WHITE_LIST[0].match }}</div>
          <div class="info-content" v-if="RESULT_FILTER_WHITE_LIST.length!==0">
            · 以下六要两不要标签将视为违规：{{ RESULT_FILTER_WHITE_LIST[1].match }}</div>
        </el-dialog>
        <mtd-modal v-model="is_discriminative_retrive_visible">
            <COEDiscriminativeRetriveModal :searchConfig="searchConfig" :count="total"></COEDiscriminativeRetriveModal>
        </mtd-modal>
    </div>
</template>


<script>
import DatePicker from './datePicker.vue'
import Navigator from './Navigator.vue'
import StorageDescription from './StorageDescription.vue'
import { Select, Option, FormItem, Form, Pagination, Tree } from 'element-ui'
import api from './store/api'
import COEDiscriminativeRetriveModal from './COEDiscriminativeRetriveModal.vue'

export default {
  computed: {
    menu() {
      return this.$store.state.COEStore.menu
    },
    searchConfig() {
      const payload = {
        begin_time: this.formatDate(this.date.startDate),
        end_time: this.formatDate(this.date.endDate),
        levels: this.coeLevel,
        orgs: this.selectedOrg,
        // is_exclude_light_template: this.excludeLightTemplate,
        from: this.pageSize * (this.currentPage - 1),
        size: this.pageSize,
        filter6to2not: this.filter6to2not,
        filterfund: this.filterfund,
        filter6to2not_reviewed: this.filter6to2not_reviewed,
        diff6to2not: this.diff6to2not
      }
      if (this.brief_search && this.brief_search !== '') {
        payload.brief_search = this.brief_search
      } else if (this.cause_search && this.cause_search !== '') {
        payload.cause_search = this.cause_search
      }

      if (this.brief_match && this.brief_match !== '') {
        payload.brief_match = this.brief_match
      }
      if (this.cause_match && this.cause_match.length !== 0) {
        payload.cause_match = this.cause_match
      }
      if (this.template_ids && this.template_ids.length !== 0) {
        payload.template_ids = this.template_ids
      }
      return payload
    }
  },
  watch: {
    selectedOrgName(newVal, oldVal) {
      console.log('selectedOrgName变化:', oldVal, '变为', newVal)
      if (!Array.isArray(newVal)) {
        this.selectedOrgName = []
      }
    }
  },
  data() {
    return {
      breadcrumb: [{'path': '/coe/coestorage', 'title': '首页'}, {'path': '/coe/coestorage', 'title': 'COE列表'}],
      taskTypes: [],
      taskType: '',
      excludeLightTemplate: false,
      date: {
        disabled: false,
        startDate: null,
        endDate: null
      },
      brief_search: '',
      cause_search: '',
      cause_match: [],
      brief_match: '',
      template_ids: [],
      filterfund: false,
      coeLevel: [],
      pageSize: 10,
      currentPage: 1,
      orgs: [],
      selectedOrg: [],
      selectedOrgName: [],
      coe_store_list: [],
      storage_data: null,
      detail_dialog: false,
      loading: false,
      total: 0,
      to_not_to_types: ['to_test', 'to_claim', 'to_check', 'to_inspect', 'to_grey', 'to_rollback', 'not_to_delay', 'not_to_illagle_change_data'],
      RESULT_FILTER_WHITE_LIST: [],
      info_dialog: false,
      cause_options: [],
      template_options: [
        { label: '详细记录模版', value: '16' },
        { label: '精简记录模板', value: '23' },
        { label: '轻量记录', value: '30' },
        { label: '无模板', value: '无模板' }
      ],
      to_not_to_options: [],
      data_path_dict: {
        'fund_safety': {
          text: 'LLM资金风险判断',
          value: 'fund_safety.is_fund_danger.analysis_result',
          lnk_param: { task_id: 'fund_safety.is_fund_danger.analysis_task_id', type: 'fund_judgement' }
        },
        'fund_aggr_classify': {
          text: '资金安全',
          value: 'fund_safety.fund_aggr_classify.analysis_result',
          lnk_param: {task_id: 'fund_safety.fund_aggr_classify.analysis_task_id', type: 'fund_aggr_classify'}
        },
        'cause': {
          text: '原因分类',
          value: 'cause_analysis.analysis_result',
          lnk_param: { task_id: 'cause_analysis.analysis_task_id', type: 'cause' },
          rd_tag: 'cause_analysis.rd_result'
        },
        'to_test': {
          text: '是否违反要测试',
          value: 'to_test.analysis_result',
          lnk_param: { task_id: 'to_test.analysis_task_id', type: 'to_test' },
          rd_tag: 'to_test.rd_result'
        },
        'to_claim': {
          text: '是否违反要周知',
          value: 'to_claim.analysis_result',
          lnk_param: { task_id: 'to_claim.analysis_task_id', type: 'to_claim' },
          rd_tag: 'to_claim.rd_result'
        },
        'to_check': {
          text: '是否违反要审核',
          value: 'to_check.analysis_result',
          lnk_param: { task_id: 'to_check.analysis_task_id', type: 'to_check' },
          rd_tag: 'to_check.rd_result'
        },
        'to_grey': {
          text: '是否违反要灰度',
          value: 'to_grey.analysis_result',
          lnk_param: { task_id: 'to_grey.analysis_task_id', type: 'to_grey' },
          rd_tag: 'to_grey.rd_result'
        },
        'to_inspect': {
          text: '是否违反要观测',
          value: 'to_inspect.analysis_result',
          lnk_param: { task_id: 'to_inspect.analysis_task_id', type: 'to_inspect' },
          rd_tag: 'to_inspect.rd_result'
        },
        'to_rollback': {
          text: '是否违反要可回滚',
          value: 'to_rollback.analysis_result',
          lnk_param: { task_id: 'to_rollback.analysis_task_id', type: 'to_rollback' },
          rd_tag: 'to_rollback.rd_result'
        },
        'not_to_delay': {
          text: '是否延报故障',
          value: 'not_to_delay.analysis_result',
          lnk_param: { task_id: 'not_to_delay.analysis_task_id', type: 'not_to_delay' },
          rd_tag: 'not_to_delay.rd_result'
        },
        'not_to_illagle_change_data': {
          text: '是否违规变更数据',
          value: 'not_to_illagle_change_data.analysis_result',
          lnk_param: { task_id: 'not_to_illagle_change_data.analysis_task_id', type: 'not_to_illagle_change_data' },
          rd_tag: 'not_to_illagle_change_data.rd_result'
        }
      },
      filter6to2not: [],
      diff6to2not: false,
      is_selectAll_6to2not_pred: false,
      is_selectAll_6to2not_pred_reviewed: false,
      filter6to2not_reviewed: [],
      is_discriminative_retrive_visible: false
    }
  },
  mounted() {
    let end = new Date()
    let start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    if (this.$route.query.startDate) {
      start = this.prase_date(this.$route.query.startDate)
    }
    if (this.$route.query.endDate) {
      end = this.prase_date(this.$route.query.endDate)
    }
    this.date = {
      disabled: false,
      startDate: start,
      endDate: end
    }
    console.log(this.date)
    this.syncTypeList()
    this.syncWhiteList()
    api.getOrgs().then(result => {
      this.orgs = result.data.orgs
    }).catch(error => {
      this.$notify({ title: '获取组织结构失败', message: error, type: 'error' })
    })
    this.syncCoeList()
  },
  methods: {
    prase_date(dateString) {
      const [y, m, d] = dateString.split('-')
      return new Date(parseInt(y), parseInt(m) - 1, parseInt(d))
    },
    sync() {
      this.syncTypeList()
      this.syncWhiteList()
      api.getOrgs().then(result => {
        this.orgs = result.data.orgs
      }).catch(error => {
        this.$notify({ title: '获取组织结构失败', message: error, type: 'error' })
      })
      this.syncCoeList()
    },
    onSelectAll_6to2not_pred() {
      if (!this.is_selectAll_6to2not_pred) {
        this.filter6to2not = this.find_6to2not_options(this.taskTypes).map(option => option.type)
      } else {
        this.filter6to2not = []
      }
      this.is_selectAll_6to2not_pred = !this.is_selectAll_6to2not_pred
    },
    onSelectAll_6to2not_pred_reviewed() {
      if (!this.is_selectAll_6to2not_pred_reviewed) {
        this.filter6to2not_reviewed = this.find_6to2not_options(this.taskTypes).map(option => option.type)
      } else {
        this.filter6to2not_reviewed = []
      }
      this.is_selectAll_6to2not_pred_reviewed = !this.is_selectAll_6to2not_pred_reviewed
    },
    onDialog(storagData) {
      this.storage_data = storagData
      this.detail_dialog = true
    },
    get_detailed_data(data, path) {
      const pathList = path.split('.')
      var d = data
      for (var p of pathList) {
        if (p in d) {
          d = d[p]
        } else {
          d = undefined
          break
        }
      }
      const regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (regex.test(d)) {
        d = d.replace('T', ' ').replace('Z', '')
      }
      if (!d) {
        d = '缺失'
      }
      return d
    },
    get_path(taskId, coeId, type) {
      return `/coe/result_tab/task_id/${taskId[0]}` + '?coe_id=' + coeId + '&type=' + type
    },
    on_tag_click(dataSource, typename) {
      let taskIds = this.get_detailed_data(dataSource, this.data_path_dict[typename].lnk_param.task_id)
      let path = this.get_path(taskIds, dataSource['coe_id'], this.data_path_dict[typename].lnk_param.type)
      if (path.includes('缺')) {
        return
      }
      window.open(path + window.location.hash, '_blank')
    },
    onOrgsChange(currentNode, isChecked, isChildrenChecked) {
      console.log('onOrgsChange')
      if (isChecked) {
        this.selectedOrg.push(currentNode.value)
        this.selectedOrgName.push(currentNode.label)
        console.log('isChecked')
      } else {
        console.log('not isChecked')
        const index = this.selectedOrg.indexOf(currentNode.value)
        if (index !== -1 && this.selectedOrg.length !== 0) {
          this.selectedOrg.splice(index, 1)
        }
        const index2 = this.selectedOrgName.indexOf(currentNode.label)
        if (index2 !== -1 && this.selectedOrgName.length !== 0) {
          this.selectedOrgName.splice(index2, 1)
        }
      }
    },
    onResultUpdate(coeId) {
      this.loading = true
      api.storeageResultUpdate(coeId)
        .then(result => {
          this.$notify({ title: coeId + '更新成功', message: '', type: 'success' })
          this.loading = false
          this.syncCoeList()
        })
        .catch(error => {
          this.$notify({ title: coeId + '更新失败', message: error, type: 'error' })
          this.loading = false
        })
    },
    syncWhiteList() {
      api.getResWhiteList().then(result => {
        this.RESULT_FILTER_WHITE_LIST = result.data.white_list
        let causeOptions = []
        for (var item of this.RESULT_FILTER_WHITE_LIST) {
          if (item.types.includes('cause')) {
            for (var matcher of item.match) {
              causeOptions.push({'value': matcher, 'label': matcher})
            }
          }
        }
        this.cause_options = causeOptions
      }).catch(error => {
        this.$notify({ title: '同步结果枚举值失败', message: error, type: 'error' })
      })
    },
    syncTypeList() {
      api.getTypeList().then(result => {
        this.taskTypes = result.data.task_types
      }).catch(error => {
        this.$notify({ title: '同步失败', message: error, type: 'error' })
      })
    },
    filterResultStr(resultStr, typename) {
      if (resultStr === null || resultStr === undefined) {
        return false
      }
      for (var item of this.RESULT_FILTER_WHITE_LIST) {
        if (item.types.includes(typename)) {
          for (var matcher of item.match) {
            if (resultStr === matcher || resultStr === matcher + '（修改）') {
              return true
            }
          }
        }
      }
      return false
    },
    find_type_name(typename) {
      for (let t of this.taskTypes) {
        if (t.type === typename) {
          return t.title
        }
      }
    },
    find_6to2not_options() {
      return this.taskTypes.filter((type) => {
        return this.to_not_to_types.includes(type.type)
      })
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize
      this.syncCoeList()
    },
    onPageChange(page) {
      this.currentPage = page
      this.syncCoeList()
    },
    syncCoeListOnFirstPage() {
      this.currentPage = 1
      this.syncCoeList()
    },
    make_search_payload() {
      const payload = {
        begin_time: this.formatDate(this.date.startDate),
        end_time: this.formatDate(this.date.endDate),
        levels: this.coeLevel,
        orgs: this.selectedOrg,
        // is_exclude_light_template: this.excludeLightTemplate,
        from: this.pageSize * (this.currentPage - 1),
        size: this.pageSize,
        filter6to2not: this.filter6to2not,
        filterfund: this.filterfund,
        filter6to2not_reviewed: this.filter6to2not_reviewed,
        diff6to2not: this.diff6to2not
      }
      if (this.brief_search && this.brief_search !== '') {
        payload.brief_search = this.brief_search
      } else if (this.cause_search && this.cause_search !== '') {
        payload.cause_search = this.cause_search
      }

      if (this.brief_match && this.brief_match !== '') {
        payload.brief_match = this.brief_match
      }
      if (this.cause_match && this.cause_match.length !== 0) {
        payload.cause_match = this.cause_match
      }
      if (this.template_ids && this.template_ids.length !== 0) {
        payload.template_ids = this.template_ids
      }
      return payload
    },
    syncCoeList() {
      let payload = this.make_search_payload()
      console.log('payload', payload)
      this.loading = true
      api.getCoeSyncData(payload).then(res => {
        this.coe_store_list = res.data.coe_list
        this.total = res.data.total
        this.loading = false
      }).catch(error => {
        this.$notify({ title: '获取 COE Storage 失败', message: error, type: 'error' })
        this.loading = false
        this.total = 0
        this.coe_store_list = []
      })
    },
    aggr_task() {
      let payload = this.make_search_payload()
      payload.k = 0
      payload.type = 'aggr_simple'
      payload.name = '原因分析聚合任务'
      payload.source = '手动触发'
      console.log('payload', payload)
      api.createAggrTask(payload).then(res => {
        if (res.data && res.data.id) {
          this.$notify({ title: '创建成功', message: res.data.id, type: 'success' })
        } else {
          this.$notify({ title: '任务创建失败', message: res, type: 'error' })
        }
      }).catch(error => {
        this.$notify({ title: '任务创建失败', message: error, type: 'error' })
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    removeTZ(timeStr) {
      if (timeStr) {
        return timeStr.replace('T', ' ').replace('Z', '')
      } else {
        return ''
      }
    },
    formatText(text) {
      if (text && text.includes('（修改）')) {
        text = text.replace('（修改）', '')
      }
      if (text && text.length > 6) {
        return text.slice(0, 6) + '...'
      }
      return text
    },
    setDate(value) {
      this.date.startDate = value[0]
      this.date.endDate = value[1]
    }
  },
  components: {
    ElSelect: Select,
    ElOption: Option,
    ElFormItem: FormItem,
    ELForm: Form,
    ELPagination: Pagination,
    ELTree: Tree,
    datepicker: DatePicker,
    Navigator,
    StorageDescription,
    COEDiscriminativeRetriveModal
  }
}
</script>

<style scoped>
.primary-span{
    color: #409EFF;
    background-color: rgb(217, 236, 255)
}
.info-span{
    color: #909399;
    background-color: rgb(244, 244, 245);
}
.info-content{
    margin-top: 5px;
}
.el-tag:hover{
    cursor: pointer;
}
.coe-pop-confirm-item{
    margin:10px;
}
</style>

