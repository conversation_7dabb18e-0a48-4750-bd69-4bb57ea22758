<template>
    <div class="el-descriptions">
        <div class="el-descriptions__header">
            <div class="el-descriptions__title">任务配置</div>
            <div class="el-descriptions__extra"></div>
        </div>
        <div class="el-descriptions__body">
            <table class="el-descriptions__table is-bordered">
                <tbody v-for="slicer in spliter">
                    <tr class="el-descriptions-row">
                        <th width="33%" v-for="header in headers.slice(slicer[0],slicer[1])" colspan="1" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label ">
                            {{ header.text }}
                        </th>
                    </tr>
                    <tr class="el-descriptions-row">
                        <th v-for="header in headers.slice(slicer[0],slicer[1])" colspan="1" class="el-descriptions-item__cell el-descriptions-item__content">
                            {{ coe_task[header.value] }}
                        </th>
                    </tr>
                </tbody>
                <tbody>
                    <tr class="el-descriptions-row">
                        <th colspan="3" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label ">
                            任务类型</th>
                    </tr>
                    <tr class="el-descriptions-row">
                        <td colspan="3" class="el-descriptions-item__cell el-descriptions-item__content">
                            <el-tag v-for="title in subTaskTypeTitles" :key="title" size="small">{{ title }}</el-tag>
                        </td>
                    </tr>
                </tbody>
                <tbody>
                    <tr class="el-descriptions-row">
                        <th colspan="3" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label ">
                            选中的COE列表</th>
                    </tr>
                    <tr class="el-descriptions-row">
                        <td colspan="3" class="el-descriptions-item__cell el-descriptions-item__content">
                            <el-table :data="task.choosed_coe_list" style="width: 100%; height: 220px; overflow-y: scroll;">
                                <el-table-column label="COE链接">
                                    <template slot-scope="scope">
                                        <a :href="'https://coe.mws.sankuai.com/detail/' + scope.row.coe_id"
                                            target="_blank">{{
                                                scope.row.brief }}</a>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import api from './store/api'
import { Table, TableColumn, Tag } from 'element-ui'

export default {
  components: {
    ElTable: Table,
    ElTag: Tag,
    ElTableColumn: TableColumn
  },
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      spliter: [[0, 3], [3, 6], [6, 9]],
      headers: [
                { text: '任务ID', value: 'id' },
                { text: 'COE数据', value: 'coe_data_len' },
                { text: '任务名称', value: 'name' },
                { text: '提交人', value: 'submitter' },
                { text: '开始时间', value: 'start_date' },
                { text: '截止时间', value: 'end_date' },
                { text: '执行进度', value: 'progress' },
                { text: '执行状态', value: 'state' },
                { text: '来源', value: 'source' }
      ],
      types: []
    }
  },
  mounted() {
    this.syncTypeList()
  },
  computed: {
    coe_task() {
      const task = this.task
      task.coe_data_len = this.task.choosed_coe_list.length
      return task
    },
    subTaskTypeTitles() {
      const titles = []
      for (const type of this.task.sub_task_type_list) {
        const item = this.types.find(item => item.type === type)
        if (item) {
          titles.push(item.title)
        }
      }
      return titles
    }
  },
  methods: {
    syncTypeList() {
      api.getTypeList().then(result => { this.types = result.data.task_types }).catch(error => {
        this.$notify({ title: '获取TypeList失败', message: error, type: 'error' })
      })
    }
  }
}
</script>

<style>
.el-descriptions-item {
    vertical-align: top
}

.el-descriptions-item__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.el-descriptions-item__container .el-descriptions-item__content,
.el-descriptions-item__container .el-descriptions-item__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

.el-descriptions-item__container .el-descriptions-item__content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.el-descriptions-item__label.has-colon::after {
    content: ':';
    position: relative;
    top: -.5px
}

.el-descriptions-item__label.is-bordered-label {
    font-weight: 700;
    color: #909399;
    background: #fafafa
}

.el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 10px
}

.el-descriptions-item__content {
    word-break: break-word;
    overflow-wrap: break-word
}

.el-descriptions {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 14px;
    color: #303133
}

.el-descriptions__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 20px
}

.el-descriptions__title {
    font-size: 16px;
    font-weight: 700
}

.el-descriptions--mini,
.el-descriptions--small {
    font-size: 12px
}

.el-descriptions__body {
    color: #606266;
    background-color: #FFF
}

.el-descriptions__body .el-descriptions__table {
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    font-weight: 400;
    line-height: 1.5
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-left {
    text-align: left
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-center {
    text-align: center
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-right {
    text-align: right
}

.el-descriptions .is-bordered {
    table-layout: auto
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
    border: 1px solid #EBEEF5;
    padding: 12px 10px
}

.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 12px
}

.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
    padding: 10px
}

.el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 10px
}

.el-descriptions--small.is-bordered .el-descriptions-item__cell {
    padding: 8px 10px
}

.el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 8px
}

.el-descriptions--mini.is-bordered .el-descriptions-item__cell {
    padding: 6px 10px
}

.el-descriptions--mini:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 6px
}

.el-descriptions-item {
    vertical-align: top
}

.el-descriptions-item__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.el-descriptions-item__container .el-descriptions-item__content,
.el-descriptions-item__container .el-descriptions-item__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

.el-descriptions-item__container .el-descriptions-item__content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.el-descriptions-item__label.has-colon::after {
    content: ':';
    position: relative;
    top: -.5px
}

.el-descriptions-item__label.is-bordered-label {
    font-weight: 700;
    color: #909399;
    background: #fafafa
}

.el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 10px
}

.el-descriptions-item__content {
    word-break: break-word;
    overflow-wrap: break-word
}
</style>