<template>
    <el-card v-if="data">
    <div class="chart-title">评估指标</div>
    <div class="chart-container">
        <el-table border :data="[data]" style="width: 100%" header-cell-class-name="custom-table-header">
            <el-table-column v-for="header in headers.slice(0, 3)" :key="header.value" :label="header.text"
                :width="header.width">
                <template slot-scope="scope">
                    {{ scope.row[header.value] }}
                </template>
            </el-table-column>
        </el-table>
        <el-table border :data="[data]" style="width: 100%" header-cell-class-name="custom-table-header">
            <el-table-column v-for="header in headers.slice(3, 6)" :key="header.value" :label="header.text"
                :width="header.width">
                <template slot-scope="scope">
                    {{ scope.row[header.value] }}
                </template>
            </el-table-column>
        </el-table>
        <h5 text-align="right">
            计算规则
            <a href="https://km.sankuai.com/collabpage/1756919370" target="_blank">https://km.sankuai.com/collabpage/1756919370</a>
        </h5>
    </div>
    </el-card>
</template>

<script>
export default {
  props: {
    data: Object
  },
  data() {
    return {
      headers: [
        {value: 'total', text: '有效COE数量', width: ''},
        {value: 'accept_rate', text: '标签接受率', width: ''},
        {value: 'reason_accept_rate', text: '原因采纳率', width: ''},
        {value: 'total_edit_rate', text: '有效修改次数', width: ''},
        {value: 'mean_edit_distance', text: '平均编辑距离', width: ''},
        {value: 'sequence_edit_rate', text: '平均顺序修改量', width: ''}
      ]
    }
  }
}
</script>

<style>
.custom-table-header {
  font-weight: bold;
  background-color: #fafafa !important;
  color: #909399;
}
</style>