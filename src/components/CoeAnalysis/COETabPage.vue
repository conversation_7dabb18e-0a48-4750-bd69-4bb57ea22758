<template>
    <div class="container">
        <Navigator :breadcrumb="breadcrumb"></Navigator>
        <COETab :coe_id="coe_id" :coe_type="type"></COETab>
    </div>
  </template>
  
  
  <script>
  import COETab from './COETab.vue'
  import Navigator from './Navigator.vue'
  
  export default {
    components: {
      COETab,
      Navigator
    },
    computed: {
      breadcrumb() {
        const taskId = this.$router.currentRoute.params.task_id
        return [{'path': '/coe/coestorage', 'title': '首页'},
        {'path': '/coe/task', 'title': '任务列表'},
        {'path': `/coe/detail/${taskId}/coe_result`, 'title': '结果列表'},
        {'path': `/coe/result_tab/task_id/${taskId}?coe_id=${this.coe_id}&type=${this.type}`, 'title': '分析结果'}
        ]
      },
      coe_id() {
        return this.$route.query.coe_id
      },
      type() {
        return this.$route.query.type
      }
    }
  }
  </script>
  
  <style scoped>
  .nav-menu {
    border-bottom: none;
    background-color: #f5f7fa;
  }
  .nav-menu .el-menu-item {
    padding: 0 20px;
  }
  .nav-tabs {
    margin-top: -1px;
  }
  </style>
  