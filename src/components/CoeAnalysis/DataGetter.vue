<template>
    <div class="container">
        <Navigator :configMenu="configMenu" :menu="menu" :breadcrumb="breadcrumb"></Navigator>
        <el-form class="row-thin">
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">创建时间</label>
                    <datepicker class="col-9-thin" :disabled="date.disabled" @change="setDate" :selectDate="[date.startDate,date.endDate]">
                    </datepicker>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">COE 定级</label>
                    <el-select class="col-6-thin" v-model="coeLevel" multiple placeholder="请选择 COE 定级">
                        <el-option label="S1" value="S1"></el-option>
                        <el-option label="S2" value="S2"></el-option>
                        <el-option label="S3" value="S3"></el-option>
                        <el-option label="S4" value="S4"></el-option>
                        <el-option label="S9" value="S9"></el-option>
                        <el-option label="事件" value="事件"></el-option>
                        <el-option label="无需定级" value="无需定级"></el-option>
                        <el-option label="未定级" value="未定级"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item class="col-4-thin">
                <div class="row-thin">
                    <label class="col-3-thin">组织架构</label>
                    <el-select class="col-6-thin" placeholder="请选择组织架构" v-model="selectedOrgName">
                        <el-option value="org">
                            <el-tree :data="orgs" show-checkbox node-key="value" @check-change="onOrgsChange"></el-tree>
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
        </el-form>
        <el-form class="row-thin">
            <el-form-item class="col-3-thin" >
                <div class="row-thin">
                    <label class="col-6-thin">排除轻量级模板</label>
                    <el-checkbox class="col-3-thin" v-model="excludeLightTemplate">是</el-checkbox>
                </div>
            </el-form-item>
            <div class="col-6-thin"></div>
            <el-form-item class="col-3-thin">
                <el-button type="primary" size="small" icon="el-icon-search" @click="clearChoiceAndSyncCoeList">同步COE列表</el-button>
                <el-button type="warning" size="small" icon="el-icon-plus" @click="createTask">创建任务</el-button>
            </el-form-item>
        </el-form>
        <div class="row-thin">
            <div class="col-12">
                <table class="table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
                            </th>
                            <th>COE ID</th>
                            <th v-for="header in headers" :key="header.key">{{ header.text }}</th>
                        </tr>
                    </thead>
                    <tbody v-if="loading">
                        <tr>
                            <td :colspan="headers.length + 2" class="text-center">加载中...</td>
                        </tr>
                    </tbody>
                    <tbody v-else-if="coeList.length === 0">
                        <tr>
                            <td :colspan="headers.length + 2" class="text-center">没有数据...</td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr v-for="item in coeList" :key="item._id">
                            <td>
                                <input type="checkbox" :value="item._id" v-model="checkList">
                            </td>
                            <td><a :href="'https://coe.mws.sankuai.com/detail/' + item._id" target="_blank">{{ item._id
                            }}</a></td>
                            <td v-for="header in headers" :key="header.key">{{ item[header.value] }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <el-pagination class="pagination-rounded" v-if="total > pageSize" :current-page="currentPage" :page-size="pageSize"
            :total="total" @current-change="onPageChange" @size-change="onSizeChange"
            layout="total, sizes, prev, pager, next, jumper" />
        <el-dialog :visible.sync="dialog" title="开始执行COE分析">
          <div class="card">
            <div class="card-content">
                您已选中{{checkList.length}}条COE数据
            </div>
            <div class="card-content">
              <el-input title="任务名" v-model="taskName" placeholder="任务名" type="textarea" :rows="3"></el-input>
              <el-select v-model="taskType" multiple placeholder="请选择任务类型" style="width: 100%;">
                <el-option v-for="item in taskTypes" :key="item.type" :label="item.title" :value="item.type"></el-option>
              </el-select>
            </div>
            <div class="card-content">
                <el-table :data="coeCheckedList" v-loading="loading" style="width: 100%; height: 200px; overflow-y: scroll;">
                    <el-table-column label="选中的COE列表">
                        <template slot-scope="scope">
                            <a :href="'https://coe.mws.sankuai.com/detail/' + scope.row._id" target="_blank">{{ scope.row.brief}}</a>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <footer class="card-footer">
              <el-button type="primary" @click="runCOE">执行COE分析</el-button>
              <el-button @click="dialog = false">关闭</el-button>
            </footer>
          </div>
        </el-dialog>
        
    </div>
</template>


<script>
import DatePicker from './datePicker.vue'
import Navigator from './Navigator.vue'
import { Select, Option, FormItem, Form, Pagination, Tree } from 'element-ui'
import api from './store/api'

export default {
  computed: {
    configMenu() {
      return this.$store.state.COEStore.configMenu
    },
    menu() {
      return this.$store.state.COEStore.menu
    },
    coeList() {
      return this.$store.state.COEStore.coeList
    },
    loading() {
      return this.$store.state.COEStore.isCoeListLoading
    },
    total() {
      return this.$store.state.COEStore.totalCoeListCount
    },
    coeCheckedList() {
      return this.$store.state.COEStore.coeCheckedList
    }
  },
  data() {
    return {
      breadcrumb: [{'path': '/coe/coestorage', 'title': '首页'}, {'path': '/coe/coegetter', 'title': '任务创建'}],
      headers: [
                { text: 'COE标题', value: 'brief' },
                { text: '故障类型', value: 'category' },
                { text: '定级', value: 'level' },
                { text: '创建时间', value: 'create_at' },
                { text: '负责人', value: 'owner' }
      ],
      taskTypes: [],
      taskType: '',
      dialog: false,
      checkList: [],
      excludeLightTemplate: false,
      date: {
        disabled: false,
        startDate: null,
        endDate: null
      },
      coeLevel: [],
      selectAll: false,
      pageSize: 10,
      currentPage: 1,
      orgs: [],
      selectedOrg: [],
      selectedOrgName: [],
      taskName: ''

    }
  },
  mounted() {
    const payload = this.$store.state.COEStore.coeDataGetterPayload
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    this.date = {
      disabled: false,
      startDate: start,
      endDate: end
    }
    this.coeLevel = payload.level
    this.pageSize = payload.page_size
    this.currentPage = payload.page
    this.checkList = this.$store.state.COEStore.coeCheckedList.map(({_id}) => _id)
    this.syncTypeList()
    api.getOrgs().then(result => {
      this.orgs = result.data.orgs
    }).catch(error => {
      this.$notify({title: '获取组织结构失败', message: error, type: 'error'})
    })
    this.syncCoeList()
  },
  watch: {
    checkList: {
      handler(val) {
        const newList = []
        const currentCheckedIds = new Set(val) // 创建val的副本
        console.log('currentCheckedIds:', currentCheckedIds)
        this.$store.state.COEStore.coeCheckedList.forEach(value => {
          if (currentCheckedIds.has(value._id)) {
            newList.push(value)
            currentCheckedIds.delete(value._id)
          }
        })
        this.$store.state.COEStore.coeList.forEach(value => {
          if (currentCheckedIds.has(value._id)) {
            newList.push(value)
            currentCheckedIds.delete(value._id)
          }
        })
        console.log('checkedList变化:', newList)
        this.$store.dispatch('updateCoeCheckedList', newList)
      },
      deep: true
    },
    loading(newVal, oldVal) {
      console.log('loading变化:', newVal, oldVal)
      if (this.coeList.every(item => this.checkList.includes(item._id)) && !newVal) {
        this.selectAll = true
      } else {
        this.selectAll = false
      }
    },
    selectedOrgName(newVal, oldVal) {
      console.log('selectedOrgName变化:', oldVal, '变为', newVal)
      if (!Array.isArray(newVal)) {
        this.selectedOrgName = []
      }
    }
  },
  methods: {
    onOrgsChange(currentNode, isChecked, isChildrenChecked) {
      if (isChecked) {
        this.selectedOrg.push(currentNode.value)
        this.selectedOrgName.push(currentNode.label)
      } else {
        const index = this.selectedOrg.indexOf(currentNode.value)
        if (index !== -1 && this.selectedOrg.length !== 0) {
          this.selectedOrg.splice(index, 1)
        }
        const index2 = this.selectedOrgName.indexOf(currentNode.label)
        if (index2 !== -1 && this.selectedOrgName.length !== 0) {
          this.selectedOrgName.splice(index2, 1)
        }
      }
    },
    syncTypeList() {
      api.getTypeList().then(result => { this.taskTypes = result.data.task_types }).catch(error => {
        this.$notify({title: '同步失败', message: error, type: 'error'})
      })
    },
    toggleSelectAll() {
      if (this.selectAll) {
        this.coeList.forEach(item => {
          if (!this.checkList.includes(item._id)) {
            this.checkList.push(item._id)
          }
        })
      } else {
        this.checkList = this.checkList.filter(id => !this.coeList.some(item => item._id === id))
      }
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize
      this.syncCoeList()
    },
    onPageChange(page) {
      this.currentPage = page
      this.syncCoeList()
    },
    clearChoiceAndSyncCoeList() {
      this.checkList = []
      this.currentPage = 1
      this.syncCoeList()
      this.$notify({title: '提示', message: '同步COE列表会清空勾选框', type: 'success'})
    },
    syncCoeList() {
      console.log(this.checkList)
      const payload = this.$store.state.COEStore.coeDataGetterPayload
      payload.page_size = this.pageSize
      payload.page = this.currentPage
      payload.create_start = this.formatDate(this.date.startDate)
      payload.create_end = this.formatDate(this.date.endDate)
      payload.level = this.coeLevel
      payload.orgs = this.selectedOrg
      payload.excludeLightTemplate = this.excludeLightTemplate
      this.$store.dispatch('updateCoeList', payload)
    },
    runCOE() {
      const payload = {
        coe_list: this.$store.state.COEStore.coeCheckedList,
        name: this.taskName,
        source: '手动触发',
        type_list: this.taskType
      }
      console.log(`执行COE分析:${payload}`)
      api.startTask(payload).catch(error => {
        this.$notify({title: '网络问题', message: error.message, type: 'error'})
      })
      this.dialog = false
    },
    createTask() {
      if (this.checkList && this.checkList.length !== 0) {
        this.dialog = true
      } else {
        this.$notify({title: '警告', message: '勾选框为空不能创建任务', type: 'warning'})
      }
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    setDate(value) {
      this.date.startDate = value[0]
      this.date.endDate = value[1]
    },
    showDocument(item) {
      this.selectedDocument = item
      this.dialog = true
    }
  },
  components: {
    ElSelect: Select,
    ElOption: Option,
    ElFormItem: FormItem,
    ELForm: Form,
    ELPagination: Pagination,
    ELTree: Tree,
    datepicker: DatePicker,
    Navigator
  }
}
</script>

