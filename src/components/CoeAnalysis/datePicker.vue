<template>
    <div>
        <el-date-picker :disabled="disabled" v-model="selectDateThis" type="daterange" :placeholder="placeholder"
            @change="updateDate" :picker-options="pickOptionDate" class="date-picker">
        </el-date-picker>
    </div>
</template>

<script>
import { DatePicker } from 'element-ui'

/** 日期组件
 */
export default {
  props: {
    selectDate: Array,
    disabled: {
      type: Boolean
    }
  },
  mounted() {
    this.selectDateThis = this.selectDate
  },
  data() {
    return {
      selectDateThis: []
    }
  },
  watch: {
    selectDate(newVal) {
      this.selectDateThis = newVal
    }
  },
  computed: {
    placeholder() {
      return '请输入开始时间'
    },
    pickOptionDate() {
      console.log('pickOptionDate')
      return {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: this.shortcuts
      }
    },
    shortcuts() {
      const currentDate = new Date()

      const shortcuts = [{
        text: '最近一周',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '最近一个月',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '最近三个月',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          picker.$emit('pick', [start, end])
        }
      }]

      for (let i = 0; i < 8; i++) {
        const start = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)
        const end = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, this.getMonthLastDay(start))
        let phase = '今'
        if (start.getFullYear() !== currentDate.getFullYear()) {
          phase = '去'
        }
        shortcuts.push({
          text: `${phase}年${start.getMonth() + 1}月`,
          onClick(picker) {
            picker.$emit('pick', [start, end])
          }
        })
      }

      return shortcuts
    }
  },
  methods: {
    getMonthLastDay(currentDate) {
      const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
      const lastDay = new Date(nextMonth.getTime() - 24 * 60 * 60 * 1000).getDate()
      return lastDay
    },
    updateDate() {
      this.$emit('input', this.selectDateThis)
      this.$emit('change', this.selectDateThis)
    },
    disabledDate(time) {
      let result = false
      if (this.startDate && this.endDate) {
        result = !(time >= this.startDate && time <= this.endDate)
      } else if (this.startDate) {
        result = time < this.startDate
      } else if (this.endDate) {
        result = time > this.endDate
      }
      return result
    }
  },
  components: {
    ElDatePicker: DatePicker
  }
}
</script>
  
<style scoped>
.date-picker {
    width: 80%;
    overflow-y: scroll;
}
</style>
  