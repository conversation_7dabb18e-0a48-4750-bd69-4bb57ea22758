<template>
    <div class="el-descriptions">
        <div class="el-descriptions__header">
            <div class="el-descriptions__title">{{ storage_data.brief }}</div>
        </div>
        <div>如果数据缺失，请重新执行相应任务</div>
        <div>点击单项的文本链接可以进入具体任务</div>
        <div>单项右侧的蓝色 Tag 为 RD 在 COE 平台上填写的内容</div>
        <br>
        <div class="el-descriptions__body">
            <table class="el-descriptions__table is-bordered">
                <tbody v-for="slicer in spliter">
                    <tr class="el-descriptions-row">
                        <th width="33%" v-for="header in headers.slice(slicer[0], slicer[1])" colspan="1"
                            class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label ">
                            {{ header.text }}
                        </th>
                    </tr>
                    <tr class="el-descriptions-row">
                        <th v-for="header in headers.slice(slicer[0], slicer[1])" colspan="1"
                            class="el-descriptions-item__cell el-descriptions-item__content">
                            <span v-if="header.value==='None'"></span>
                            <router-link target="_blank" v-else-if="header.lnk_param && has_detailed_data(storage_data, header.lnk_param.task_id)" 
                                :key="storage_data.coe_id" 
                                :to="{path: get_path(
                                    get_detailed_data(storage_data, header.lnk_param.task_id), 
                                    storage_data.coe_id,
                                    header.lnk_param.type)}">
                                {{ get_detailed_data(storage_data, header.value) }}
                            </router-link>
                            <span v-else>
                                {{ get_detailed_data(storage_data, header.value) }}
                            </span>
                            <el-tag size="mini" v-if="header.rd_tag">RD填写：{{ get_detailed_data(storage_data, header.rd_tag) }}</el-tag>
                        </th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import { Table, TableColumn, Tag } from 'element-ui'

export default {
  components: {
    ElTable: Table,
    ElTag: Tag,
    ElTableColumn: TableColumn
  },
  props: {
    storage: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      headers: [
        { text: '创建人', value: 'create_by' },
        { text: '创建时间', value: 'create_at' },
        { text: '更新时间', value: 'update_at' },
        { text: '组织架构', value: 'org_path' },
        { text: '定级', value: 'level' },
        { text: '模板名称', value: 'coe_template_name' },
        { text: '订单损失量', value: 'fund_safety.deal_loss_amount' },
        { text: '涉及金额（元）', value: 'fund_safety.involved_amount' },
        { text: '实际资损金额（元）', value: 'fund_safety.actual_loss_amount' },
        { text: '财务差异金额（元）', value: 'fund_safety.financial_diff_amount' },
        // {
        //   text: 'LLM资金风险判断',
        //   value: 'fund_safety.is_fund_danger.analysis_result',
        //   lnk_param: { task_id: 'fund_safety.is_fund_danger.analysis_task_id', type: 'fund_judgement' }
        // },
        {
          text: '资金安全',
          value: 'fund_safety.mole_first_level_tag',
          lnk_param: {task_id: 'fund_safety.fund_aggr_classify.analysis_task_id', type: 'fund_aggr_classify'}
        },
        {
          text: '原因分类',
          value: 'cause_analysis.analysis_result',
          lnk_param: { task_id: 'cause_analysis.analysis_task_id', type: 'cause' },
          rd_tag: 'cause_analysis.rd_result'
        },
        {
          text: '是否违反要测试',
          value: 'to_test.analysis_result',
          lnk_param: { task_id: 'to_test.analysis_task_id', type: 'to_test' },
          rd_tag: 'to_test.rd_result'
        },
        {
          text: '是否违反要周知',
          value: 'to_claim.analysis_result',
          lnk_param: { task_id: 'to_claim.analysis_task_id', type: 'to_claim' },
          rd_tag: 'to_claim.rd_result'
        },
        {
          text: '是否违反要审核',
          value: 'to_check.analysis_result',
          lnk_param: { task_id: 'to_check.analysis_task_id', type: 'to_check' },
          rd_tag: 'to_check.rd_result'
        },
        {
          text: '是否违反要灰度',
          value: 'to_grey.analysis_result',
          lnk_param: { task_id: 'to_grey.analysis_task_id', type: 'to_grey' },
          rd_tag: 'to_grey.rd_result'
        },
        {
          text: '是否违反要观测',
          value: 'to_inspect.analysis_result',
          lnk_param: { task_id: 'to_inspect.analysis_task_id', type: 'to_inspect' },
          rd_tag: 'to_inspect.rd_result'
        },
        {
          text: '是否违反要可回滚',
          value: 'to_rollback.analysis_result',
          lnk_param: { task_id: 'to_rollback.analysis_task_id', type: 'to_rollback' },
          rd_tag: 'to_rollback.rd_result'
        },
        {
          text: '是否延报故障',
          value: 'not_to_delay.analysis_result',
          lnk_param: { task_id: 'not_to_delay.analysis_task_id', type: 'not_to_delay' },
          rd_tag: 'not_to_delay.rd_result'
        },
        {
          text: '是否违规变更数据',
          value: 'not_to_illagle_change_data.analysis_result',
          lnk_param: { task_id: 'not_to_illagle_change_data.analysis_task_id', type: 'not_to_illagle_change_data' },
          rd_tag: 'not_to_illagle_change_data.rd_result'
        }
      ]
    }
  },
  computed: {
    storage_data() {
      const storage = this.storage
    //   storage['fund_safety.deal_loss_amount'] = storage.fund_safety.deal_loss_amount
    //   storage['fund_safety.involved_amount'] = storage.fund_safety.involved_amount
    //   storage['fund_safety.actual_loss_amount'] = storage.fund_safety.actual_loss_amount
    //   storage['fund_safety.financial_diff_amount'] = storage.fund_safety.financial_diff_amount
    //   storage['fund_safety.is_fund_danger.analysis_result'] = storage.fund_safety.is_fund_danger.analysis_result
    //   storage['fund_safety.is_fund_danger.analysis_task_id'] = storage.fund_safety.is_fund_danger.analysis_task_id
      return storage
    },
    spliter() {
      var length = this.headers.length
      var sp = []
      for (var i = 0; i <= length; i += 3) {
        sp.push([i, i + 3])
      }
      return sp
    }
  },
  methods: {
    has_detailed_data(data, path) {
      const pathList = path.split('.')
      var d = data
      for (var p of pathList) {
        if (p in d) {
          d = d[p]
        } else {
          return false
        }
      }
      return true
    },
    get_detailed_data(data, path) {
      const pathList = path.split('.')
      var d = data
      for (var p of pathList) {
        if (p in d) {
          d = d[p]
        } else {
          d = undefined
          break
        }
      }
      const regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/
      if (regex.test(d)) {
        d = d.replace('T', ' ').replace('Z', '')
      }
      if (!d) {
        d = '缺失'
      }
      return d
    },
    get_path(taskId, coeId, type) {
      if (taskId) {
        return `/coe/result_tab/task_id/${taskId[0]}` + '?coe_id=' + coeId + '&type=' + type + window.location.hash
      }
      return null
    }
  }
}
</script>

<style>
.el-descriptions-item {
    vertical-align: top
}

.el-descriptions-item__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.el-descriptions-item__container .el-descriptions-item__content,
.el-descriptions-item__container .el-descriptions-item__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

.el-descriptions-item__container .el-descriptions-item__content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.el-descriptions-item__label.has-colon::after {
    content: ':';
    position: relative;
    top: -.5px
}

.el-descriptions-item__label.is-bordered-label {
    font-weight: 700;
    color: #909399;
    background: #fafafa
}

.el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 10px
}

.el-descriptions-item__content {
    word-break: break-word;
    overflow-wrap: break-word
}

.el-descriptions {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 14px;
    color: #303133
}

.el-descriptions__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 20px
}

.el-descriptions__title {
    font-size: 16px;
    font-weight: 700
}

.el-descriptions--mini,
.el-descriptions--small {
    font-size: 12px
}

.el-descriptions__body {
    color: #606266;
    background-color: #FFF
}

.el-descriptions__body .el-descriptions__table {
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    font-weight: 400;
    line-height: 1.5
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-left {
    text-align: left
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-center {
    text-align: center
}

.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell.is-right {
    text-align: right
}

.el-descriptions .is-bordered {
    table-layout: auto
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
    border: 1px solid #EBEEF5;
    padding: 12px 10px
}

.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 12px
}

.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
    padding: 10px
}

.el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 10px
}

.el-descriptions--small.is-bordered .el-descriptions-item__cell {
    padding: 8px 10px
}

.el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 8px
}

.el-descriptions--mini.is-bordered .el-descriptions-item__cell {
    padding: 6px 10px
}

.el-descriptions--mini:not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 6px
}

.el-descriptions-item {
    vertical-align: top
}

.el-descriptions-item__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.el-descriptions-item__container .el-descriptions-item__content,
.el-descriptions-item__container .el-descriptions-item__label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline
}

.el-descriptions-item__container .el-descriptions-item__content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.el-descriptions-item__label.has-colon::after {
    content: ':';
    position: relative;
    top: -.5px
}

.el-descriptions-item__label.is-bordered-label {
    font-weight: 700;
    color: #909399;
    background: #fafafa
}

.el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 10px
}

.el-descriptions-item__content {
    word-break: break-word;
    overflow-wrap: break-word
}
</style>