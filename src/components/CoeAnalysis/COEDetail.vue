<template>
  <div class="container">
    <Navigator @refresh="sync" :menu="menu" :breadcrumb="breadcrumb"></Navigator>
    <div v-if="taskLoaded">
        <TaskDescription :task="coeTaskData"></TaskDescription>
    </div>
  </div>
</template>


<script>
import Navigator from './Navigator.vue'
import COEOverview from './COEOverview.vue'
import COETab from './COETab.vue'
import COEResult from './COEResult.vue'
import TaskDescription from './TaskDescription.vue'
import api from './store/api'

export default {
  components: {
    Navigator,
    COEOverview,
    COETab,
    COEResult,
    TaskDescription
  },
  computed: {
    breadcrumb() {
      return [{'path': '/coe/coestorage', 'title': '首页'},
        {'path': '/coe/task', 'title': '任务列表'},
        {'path': `coe/detail/${this.task_id}/task_description`, 'title': '任务配置'}]
    },
    tab() {
      const tab = this.$route.params.tab
      return tab || 'task_description'
    },
    task_id() {
      return this.$router.currentRoute.params.task_id
    },
    menu() {
      const menu = [
        {
          title: '任务配置',
          path: `/coe/detail/${this.task_id}/task_description`
        },
        {
          title: '结果概览',
          path: `/coe/detail/${this.task_id}/coe_overview`
        },
        {
          title: '结果列表',
          path: `/coe/detail/${this.task_id}/coe_result`
        }
      ]
      return menu
    }
  },
  data() {
    return {
      activeIndex: '0',
      coe_id: null,
      type: null,
      tabs: [],
      coeTaskData: {},
      taskLoaded: false
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    sync() {
    //   this.$store.dispatch('updateCoeResultList', this.$router.currentRoute.params.task_id)
      this.findTask()
    },
    findTask() {
      const taskId = this.$router.currentRoute.params.task_id
      api.findTask(taskId).then(result => {
        this.coeTaskData = result.data.task
        this.taskLoaded = true
      }).catch(error => {
        this.$notify({title: '获取Task失败', message: error, type: 'error'})
      })
    },
    parseQueryString(str) {
      const result = {}
      const pairs = str.replace(/^#/, '').split('&')
      for (let i = 0; i < pairs.length; i++) {
        const pair = pairs[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1] || '')
        if (key) {
          result[key] = value
        }
      }
      return result
    },
    removeTab(index) {
      console.log(index)
      this.tabs.splice(index, 1)
      if (this.tabs.length !== 0 && index !== 0) {
        this.activeIndex = this.tabs[index - 1].name
      } else {
        this.activeIndex = '2'
      }
      console.log(this.activeIndex)
    }
  }
}
</script>

<style scoped>
.nav-menu {
  border-bottom: none;
  background-color: #f5f7fa;
}
.nav-menu .el-menu-item {
  padding: 0 20px;
}
.nav-tabs {
  margin-top: -1px;
}
</style>
