import urlConf from '../../../assets/user_env'
import axios from 'axios'

import { Bus } from '@/global/bus'

let coeLocalHeaders = {
  userInfo: JSON.stringify({
    userId: '',
    userName: 'Dev',
    userLogin: 'Dev',
    userUrl: ''
  })
}

Bus.$on('refreshUserInfo', (userInfo) => {
  coeLocalHeaders.userInfo = encodeURI(JSON.stringify(userInfo))
})

const switchBaseUrl = () => {
  const currentHash = window.location.hash
  if (currentHash === '#test') {
    return urlConf.coe_base_url
  } else {
    return urlConf.coe_prod_url
  }
}

const getCoeList = (payload) => {
  return axios({
    url: switchBaseUrl() + '/query/incidents',
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const getTypeList = () => {
  return axios({
    url: switchBaseUrl() + '/task/type/list',
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const getTaskList = (beginTime, endTime, size, from_, taskIdMatch, taskNameMatch) => {
  return axios({
    url: `${switchBaseUrl()}/task/show?begin_time=${beginTime}&end_time=${endTime}&size=${size}&from_=${from_}&task_id_match=${taskIdMatch}&task_name_match=${taskNameMatch}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const getOrgs = (params) => {
  return axios({
    url: switchBaseUrl() + '/orgs',
    method: 'GET',
    data: JSON.stringify(params),
    headers: coeLocalHeaders
  })
}

const startTask = (params) => {
  return axios({
    url: switchBaseUrl() + '/task/start',
    method: 'POST',
    data: params,
    headers: coeLocalHeaders
  })
}

const findTask = (taskId) => {
  return axios({
    url: switchBaseUrl() + `/task/find?task_id=${taskId}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const getResultList = (taskId, size, from_) => {
  return axios({
    url: `${switchBaseUrl()}/result/list?task_id=${taskId}&size=${size}&from_=${from_}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const getResultDetail = (taskId, coeId, type) => {
  return axios({
    url: `${switchBaseUrl()}/result/detail?task_id=${taskId}&coe_id=${coeId}&type=${type}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const restartTask = (taskId) => {
  return axios({
    url: `${switchBaseUrl()}/task/restart?task_id=${taskId}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const topicAnalysis = (payload) => {
  const taskId = payload.taskId
  const topic = payload.topic
  return axios({
    url: `${switchBaseUrl()}/topic/analysis?task_id=${taskId}`,
    method: 'POST',
    data: {
      task_id: taskId,
      topic: topic
    },
    headers: coeLocalHeaders
  })
}

const saveCoeResult = (payload) => {
  return axios({
    url: `${switchBaseUrl()}/result/save`,
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const markExperience = (payload) => {
  return axios({
    url: `${switchBaseUrl()}/experience/mark`,
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const saveExperience = (payload) => {
  return axios({
    url: `${switchBaseUrl()}/experience/save`,
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const getExperience = (type, size, from_) => {
  return axios({
    url: `${switchBaseUrl()}/experience/list?type=${type}&size=${size}&from_=${from_}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const deleteExperience = (id) => {
  return axios({
    url: `${switchBaseUrl()}/experience/delete?id=${id}`,
    method: 'POST',
    headers: coeLocalHeaders
  })
}

const getCoeSyncData = (payload) => {
  return axios({
    url: `${switchBaseUrl()}/coestore/show`,
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const getResWhiteList = () => {
  return axios({
    url: switchBaseUrl() + '/result/filter/white_list',
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const storeageResultUpdate = (coeId) => {
  return axios({
    url: switchBaseUrl() + `/coestore/result_update?coe_id=${coeId}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

const createAggrTask = (payload) => {
  return axios({
    url: switchBaseUrl() + '/task/aggr/start',
    method: 'POST',
    data: payload,
    headers: coeLocalHeaders
  })
}

const taskAggrRecall = (taskId, query, threshold) => {
  return axios({
    url: switchBaseUrl() + `/task/aggr/recall?query=${query}&task_id=${taskId}&threshold=${threshold}`,
    method: 'GET',
    headers: coeLocalHeaders
  })
}

export default {
  restartTask,
  getCoeList,
  getOrgs,
  getTaskList,
  startTask,
  findTask,
  getResultList,
  getResultDetail,
  topicAnalysis,
  saveCoeResult,
  markExperience,
  saveExperience,
  getExperience,
  deleteExperience,
  getTypeList,
  getCoeSyncData,
  getResWhiteList,
  storeageResultUpdate,
  switchBaseUrl,
  coeLocalHeaders,
  createAggrTask,
  taskAggrRecall
}
