import API from './api'
import portal from '@/main'
import { Bus } from '@/global/bus'

Bus.$on('refreshUserInfo', (userInfo) => {
  portal.$store.dispatch('updateCoeUserInfo', userInfo)
})

export default {
  state: {
    menu: [
      {
        'title': 'COE维度',
        'path': '/coe/coestorage'
      },
      {
        'title': '任务维度',
        'path': '/coe',
        'subMenu': [
          {'title': '任务列表', 'path': '/coe/task'},
          {'title': '任务创建', 'path': '/coe/coegetter'}
        ]
      },
      {
        'title': '知识维度',
        'path': '/coe/experience'
      }
    ],
    configMenu: [
      {
        'title': 'COE经验知识',
        'path': '/coe/experience'
      }
    ],
    userInfo: {
      userId: '',
      userName: 'Dev',
      userLogin: 'Dev',
      userUrl: ''
    },
    activeMenuItemIndex: null,
    coe_result: {
      history: [
        {
          id: '1',
          thought: 'thought1',
          result: 'result1'
        },
        {
          id: '2',
          thought: 'thought2',
          result: 'result2'
        }
      ]
    },
    coeList: [],
    totalCoeListCount: 0,
    isCoeListLoading: false,
    coeCheckedList: [],
    coeDataGetterPayload: {
      'key': '',
      'category': [],
      'categoryDate': [],
      'orgs': [],
      'responsible_org_ids': [],
      'level': [],
      'effectLevelDate': [],
      'level_standard_category': [],
      'level_standard_id': [],
      'level_standard': [],
      'sort_by': 'occur_time',
      'sort': 'desc',
      'list_type': 'all',
      'page': 1,
      'page_size': 20,
      'appkey': '',
      'locators': '',
      'finders': '',
      'reason': [],
      'tags': [],
      'cause_todo_tags': [],
      'coe_template_id': null,
      'occur_start': '',
      'occur_end': '',
      'create_start': '',
      'create_end': '',
      'level_start': '',
      'level_end': ''
    },
    coeResultList: [],
    coeResultAggrs: {},
    coeWordCloudData: {},
    topicResult: {},
    experienceData: {
      total: 0,
      experience_list: []
    },
    resultListPackage: {}
  },
  mutations: {
    setMenu(state, menu) {
      state.menu = menu
    },
    setActiveMenuItemIndex(state, activeMenuItemIndex) {
      state.activeMenuItemIndex = activeMenuItemIndex
    },
    setCoeList(state, coeList) {
      state.coeList = coeList
    },
    setIsCoeListLoading(state, loading) {
      state.isCoeListLoading = loading
    },
    setCoeDataGetterPayload(state, payload) {
      state.coeDataGetterPayload = payload
    },
    setTotalCoeListCount(state, total) {
      state.totalCoeListCount = total
    },
    setCoeCheckedList(state, checkList) {
      state.coeCheckedList = checkList
    },
    setCoeResultList(state, coeResultList) {
      state.coeResultList = coeResultList
    },
    setCoeResultAggrs(state, coeResultAggrs) {
      state.coeResultAggrs = coeResultAggrs
    },
    setCoeWordCloudData(state, coeWordCloudData) {
      state.coeWordCloudData = coeWordCloudData
    },
    setTopicResult(state, topicResult) {
      state.topicResult = topicResult
    },
    setExpceienceData(state, experienceData) {
      state.experienceData = experienceData
    },
    setCoeUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setResultListPackage(state, resultListPackage) {
      state.resultListPackage = resultListPackage
    }
  },
  actions: {
    updateBatchId({ commit }, batchId) {
      commit('setBatchId', batchId)
    },
    updateCoeUserInfo({ commit }, userInfo) {
      commit('setCoeUserInfo', userInfo)
    },
    updateType({ commit }, type) {
      commit('setType', type)
    },
    updateCoeList({commit}, payload) {
      commit('setCoeDataGetterPayload', payload)
      commit('setIsCoeListLoading', true)
      API.getCoeList(payload).then(result => {
        commit('setIsCoeListLoading', false)
        commit('setCoeList', result.data['incidents'])
        commit('setTotalCoeListCount', result.data['total_count'])
      }).catch(reason => {
        window.alert(reason)
      })
    },
    updateCoeCheckedList({commit}, checkList) {
      commit('setCoeCheckedList', checkList)
    },
    updateCoeResultList({commit}, payload) {
      const taskId = payload.taskId
      const callback = payload.callback
      API.getResultList(taskId, 10000, 0).then(result => {
        const resultList = result.data.result_list
        const aggrs = result.data.aggrs
        const coeWordCloudData = result.data.cloud
        const resultListPackage = result.data
        commit('setCoeResultList', resultList)
        commit('setCoeResultAggrs', aggrs)
        commit('setCoeWordCloudData', coeWordCloudData)
        commit('setResultListPackage', resultListPackage)
        if (callback instanceof Function) {
          callback()
        }
      })
    },
    updateTopic({commit}, taskId, topic) {
      commit('setIsCoeListLoading', true)
      API.topicAnalysis(taskId, topic).then(result => {
        const topicResult = result.data
        commit('setTopicResult', topicResult)
        commit('setIsCoeListLoading', false)
      })
    },
    updateExperience({commit}, experienceData) {
      commit('setExpceienceData', experienceData)
    }
  }
}
