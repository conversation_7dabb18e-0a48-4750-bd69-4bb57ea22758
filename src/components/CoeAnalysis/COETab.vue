<template>
  <el-container v-loading="saving>0" >
    <div class="row-thin">
      <!-- <div class="col-3">
        <div class="card result-card">
          <div class="card-header">
            标准参照模版
            <button class="btn btn-outline-info" :disabled="currentIndex === 0" @click="currentIndex--">&lt;</button>
            <button class="btn btn-outline-info" :disabled="currentIndex === history.length - 1" @click="currentIndex++">&gt;</button>
          </div>
          <div class="list-group">
            <div v-if="history.length <= currentIndex" class="list-group-item">
                <div class="classify-result-content">暂无数据</div>
            </div>
            <div v-else-if="history[currentIndex].data">
                <div class="classify-result-title"><a :href="`https://coe.mws.sankuai.com/detail/${history[currentIndex].coe_id}`">{{ history[currentIndex].brief }}</a></div>
                <div class="user-msg">总结: <br>{{ history[currentIndex].data[0].content }}</div>
                <hr>
                <div class="bot-msg">回答: <br>{{ history[currentIndex].data[history[currentIndex].data.length-1].content }}</div>
                <hr>
            </div>
          </div>
        </div>
      </div> -->
      <div class="col-3">
        <div class="card result-card">
          <div class="card-header">
            修订记录
          </div>
          <div class="list-group">
            <div v-if="changeLog.length === 0" class="list-group-item">
                <div class="classify-result-content">暂无数据</div>
            </div>
            <div v-else >
                <div v-for="log in changeLog.slice().reverse()" class="list-group-item">
                    <div v-if="log.action==='changeIndex'">
                        <div class="classify-result-title">修改顺序</div>
                        <div >修改者: {{ log.submitter }}</div>
                        <hr>
                        <div >{{ diffList(log.new_index_list) }}</div>
                    </div>
                    <div v-else-if="log.action==='contentChange'">
                        <div class="classify-result-title">修改内容</div>
                        <div >修改者: {{ log.submitter }}</div>
                        <hr>
                        <div >序号: {{ log.exp_index }}</div>
                        <hr>
                        <el-tooltip v-if="log.new_message && log.old_message" :content="log.new_message.content" placement="top">
                            <div v-html="highlightChangedText(log.old_message.content, log.new_message.content, false)" class="ellipsis"></div>
                        </el-tooltip>                    
                    </div>
                    <div v-if="log.action==='reviewedTagChange'">
                        <div class="classify-result-title">标记reviewed</div>
                        <div >修改者: {{ log.submitter }}</div>
                        <hr>
                        <div >改为：{{ log.new_tag }}</div>
                    </div>
                    <div v-if="log.action==='reasonableTagChange'">
                        <div class="classify-result-title">推理过程合理标记</div>
                        <div >修改者: {{ log.submitter }}</div>
                        <hr>
                        <div >改为：{{ log.new_tag }}</div>
                    </div>
                </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-6">
        <div class="card result-card">
          <div class="card-header">
            <div style="display: flex; justify-content: flex-start; align-items: center;">
                <div>COE分析结果</div>
                <!-- <div style="margin-left: auto;">
                    <el-button size="mini" @click="on_reasonable" v-if="coeResult.is_reasonable">原因合理</el-button>
                    <el-button type="warning" size="mini" v-else @click="on_reasonable">原因不合理</el-button>
                </div> -->
                <div style="margin-left: auto;">
                    <el-button circle type="success" size="mini" @click="on_reviewed" v-if="coeResult && coeResult.is_reviewed" icon="el-icon-check"></el-button>
                    <el-button circle size="mini" v-else icon="el-icon-minus" @click="on_reviewed"></el-button>
                </div>
            </div>
          </div>
          <div class="classify-result-title">COE链接</div>
          <div class="card-body">
            <a :href="coe_link" target="_blank">{{ title }}</a>
          </div>
          <div class="classify-result-title">
            总结结果
            <el-switch class="card-body" v-model="is_reasonable" active-text="推理合理" inactive-text="推理乱套" @change="on_reasonable">
            </el-switch>
          </div>
          <div class="card-body">
            <div class="classify-result-content" v-html="summary"></div>
            <hr>
          </div>
          
          <div class="classify-result-title">回答结果</div>
          <div class="list-group">
            <draggable v-model="answer_list" :options="{group:'items', handle:'.drag-handle'}" animation="300" chosenClass="drag-chosen">
                <div v-for="(item, index) in answer_list" :key="index" class="list-group-item">
                    <h5>
                        <span class="drag-handle">
                            <el-icon class="el-icon-rank" size="mini"></el-icon>
                        </span>
                        <span v-if="index===0" class="answer-title-primary">
                            首要结果
                        </span>
                        <span v-else class="answer-title-secondary">
                            次要结果{{index}}
                        </span>
                    </h5>
                    <div class="classify-result-content">{{ item.content }}</div>
                    <hr>
                    <div class="action">
                        <button class="btn btn-primary" @click="showEditModal(index)">修改结果</button>
                    </div>
                    
                </div>
            </draggable>
          </div>
        </div>
      </div>
      <div class="col-3">
        <div class="card result-card">
          <div class="card-header">COE分析过程</div>
          <div class="chat-box">
            <div v-for="(msg, index) in detail" :key="index"
              :class="getMessageClass(msg.role)" v-html="msg.content"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal" :class="{ 'is-active': dialog }">
        <div class="modal-background" @click="dialog = false"></div>
        <div class="modal-content modal-content-long">
          <div class="modal-header">
            <button type="button" class="close" @click="dialog = false" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
            <h3 class="modal-title" id="editModalLabel">编辑回答结果</h3>
          </div>
          <div class="modal-body" v-if="is_6to2not()">
            <div class="form-group">
              <label for="summaryTextarea">COE智能总结</label>
              <textarea class="form-control" id="summaryTextarea" rows="7" v-model="editSummary"></textarea>
            </div>
            <!-- <div v-for="(item, index) in editStructedFieldsSummary" :key="index" class="form-group">
                <label>{{item.key}}</label>
                <textarea class="form-control" :id="index" 
                    v-if="item.type==='text'"
                    :readonly="!item.editable"
                    rows="3" v-model="editStructedPlaceholderSummary[index]"></textarea>
                <select class="form-control" :id="index" 
                    v-if="item.type==='selector'"
                    :readonly="!item.editable"
                    v-model="editStructedPlaceholderSummary[index]">
                    <option v-for="option in item.choice" :value="option">{{ option }}</option>
                </select>
            </div> -->
            <div v-for="(item, index) in editStructedFields" :key="index" class="form-group">
                <label>{{item.key}}</label>
                <textarea class="form-control" :id="index" 
                    v-if="item.type==='text'"
                    :readonly="!item.editable"
                    rows="3" v-model="editStructedPlaceholder[index]"></textarea>
                <select class="form-control" :id="index" 
                    v-if="item.type==='selector'"
                    :readonly="!item.editable"
                    v-model="editStructedPlaceholder[index]">
                    <option v-for="option in item.choice" :value="option">{{ option }}</option>
                </select>
            </div>
          </div>
          <div class="modal-body" v-else>
            <div class="form-group">
              <label for="summaryTextarea">COE智能总结</label>
              <textarea class="form-control" id="summaryTextarea" rows="7" v-model="editSummary"></textarea>
            </div>
            <div class="form-group">
              <label for="answerTextarea">回答结果</label>
              <textarea class="form-control" id="answerTextarea" rows="3" v-model="editAnswer"></textarea>
            </div>
          </div>
          <div class="modal-footer">

            <el-button type="warning" v-if="editIndex !== null && editIndex !== undefined &&  answer_message[editIndex] && answer_message[editIndex].is_marked" icon="el-icon-star-on" @click="unmarkExp">
                已收藏
            </el-button>
            <el-button type="warning" v-else icon="el-icon-star-off" @click="markExp">
                未收藏
            </el-button>
            <!-- <el-button type="primary" @click="dialog = false">取消</el-button> -->
            <el-button type="primary" @click="updateResult">保存</el-button>
          </div>
        </div>
      </div>
    </el-container>
</template>

<script>
import api from './store/api'
import draggable from 'vuedraggable'
import {Tooltip, Container, Icon, Button} from 'element-ui'

export default {
  components: {
    draggable,
    ElTooltip: Tooltip,
    ElContainer: Container,
    ElIcon: Icon,
    ElButton: Button
  },
  props: {
    coe_type: String,
    coe_id: String
  },
  data() {
    return {
      editIndex: 0,
      editSummary: '',
      editAnswer: '',
      editStructedFieldsSummary: [],
      editStructedPlaceholderSummary: [],
      editStructedFields: [],
      editStructedPlaceholder: [],
      dialog: false,
      coeResult: null,
      currentIndex: 0,
      answer_message: [],
      history: [],
      changeLog: [],
      saving: 0,
      is_reasonable: true
    }
  },
  mounted() {
    this.find_result()
  },
  methods: {
    is_6to2not() {
      const validTypes = ['to_test', 'to_claim', 'to_check', 'to_grey', 'to_inspect', 'to_rollback', 'not_to_delay', 'not_to_illagle_change_data']
      return validTypes.includes(this.coe_type)
    },
    get_edit_struct(fields, placeholder, marker = '\n') {
      let text = []
      for (let i = 0; i < fields.length; i++) {
        const key = fields[i].key
        const value = placeholder[i]
        text.push(`${key}: ${value}`)
      }
      return text.join(marker)
    },
    getMessageClass(role) {
      if (role === 'user' || role === 'Human') {
        return 'user-msg'
      } else if (role === 'assistant' || role === 'AI') {
        return 'bot-msg'
      } else {
        return 'sys-msg'
      }
    },
    merge_lines(text) {
      if (!text || text === '') {
        return []
      }
      const lines = text.split('\n').filter(line => line.trim() !== '')
      let previousLine = ''
      const result = []
      const regex = /([^0-9:：]+)[:：](.*)/

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        const match = regex.exec(line)

        if (match) {
          const key = match[1].trim()
          const value = match[2].trim()
          result.push({ key, value })
          previousLine = line
        } else {
          if (previousLine !== '') {
            const previousMatch = regex.exec(previousLine)
            if (previousMatch) {
              const value = previousMatch[2].trim() + line
              result[result.length - 1].value = value
            }
          }
        }
      }
      return result
    },
    make_edit_struct(text) {
      // 构建列表
      const textLines = this.merge_lines(text)
      const list = textLines.map(item => {
        const key = item.key
        const value = item.value
        let payload = { 'key': key, 'value': value, 'editable': true, 'type': 'text' }
        if (key.includes('判断结果')) {
          payload['type'] = 'selector'
          payload['choice'] = ['违反', '没违反', '无法确定']
          if (value.includes('不违反') || value.includes('没违反') || value.includes('未违反')) {
            payload['value'] = '没违反'
          } else if (value.includes('无法确定') || value.includes('不涉及') || value.includes('无法判断') || value.includes('不能确定')) {
            payload['value'] = '无法确定'
          } else if (value.includes('违反')) {
            payload['value'] = '违反'
          } else {
            const match2 = /^([\s\S]*?)(?:[,:;，；。！？\n])(.+)/.exec(value)
            if (match2) {
              payload['value'] = match2[1]
            } else {
              payload['value'] = value
            }
          }
        }
        if (key.includes('关键词')) {
          payload['editable'] = false
        }
        return payload
      })
      // 把判断结果提前
      list.sort((a, b) => {
        let aScore = 0
        let bScore = 0
        if (a.key.includes('判断结果')) {
          aScore = 2
        } else if (a.key.includes('判断依据')) {
          aScore = 1
        }
        if (b.key.includes('判断结果')) {
          bScore = 2
        } else if (b.key.includes('判断依据')) {
          bScore = 1
        }
        return bScore - aScore
      })
      // 返回
      let editStructedPlaceholder = [] // 清空this.editStructedFields
      list.forEach(item => {
        editStructedPlaceholder.push(item.value) // 将每个元素添加到this.editStructedFields中
      })
      let editStructedFields = list
      return {
        fields: editStructedFields,
        placeholder: editStructedPlaceholder
      }
    },
    enter_saving_with_lock(callback) {
      // 前端对saving对象加同步锁
      let count = 0
      const interval = setInterval(() => {
        if (count >= 100) {
          this.$notify({title: '保存失败', message: '上一个请求超时', type: 'error'})
          clearInterval(interval) // 跳出循环
        } else if (this.saving > 0) {
          count++
          console.log('等待上一个更改完成...')
        } else {
          count += 10000
          callback()
          clearInterval(interval) // 跳出循环
        }
      }, 300) // 0.3秒一次，100次最多30秒，前一个请求30秒就算超时了
    },
    find_result() {
      const taskId = this.$router.currentRoute.params.task_id
      api.getResultDetail(taskId, this.coe_id, this.coe_type).then(result => {
        const task = result.data.result
        this.coeResult = task
        this.history = result.data.similiar_case_list
        this.answer_message = result.data.answer_message
        this.changeLog = task.change_log
        this.is_reasonable = task.is_reasonable
      }).catch(error => {
        this.$notify({title: '获取失败', message: error, type: 'error'})
      })
    },
    getEditDistance(str1, str2) {
      const len1 = str1.length
      const len2 = str2.length
      const dp = new Array(len1 + 1).fill(0).map(() => new Array(len2 + 1).fill(0))
      for (let i = 0; i <= len1; i++) {
        dp[i][0] = i
      }
      for (let j = 0; j <= len2; j++) {
        dp[0][j] = j
      }
      for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
          if (str1[i - 1] === str2[j - 1]) {
            dp[i][j] = dp[i - 1][j - 1]
          } else {
            dp[i][j] = Math.min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]) + 1
          }
        }
      }
      return dp[len1][len2]
    },
    highlightChangedText(oldText, newText, retain = true) {
      const editDistance = this.getEditDistance(oldText, newText)
      const len = newText.length
      let i = 0
      let j = 0
      let result = ''
      while (i < len && j < len) {
        if (oldText[i] === newText[j]) {
          if (retain) { result += oldText[i] }
          i++
          j++
        } else {
          if (editDistance === 1) {
            result += `<span style="color:red">${newText[j]}</span>`
            i++
            j++
          } else {
            result += `<span style="color:red">${newText.slice(j, j + editDistance)}</span>`
            j += editDistance
          }
        }
      }
      return result
    },
    showEditModal(index) {
      this.editIndex = index
      this.editSummary = this.answer_message[index].data[0].content
      this.editAnswer = this.answer_list[index].content
      this.dialog = true
      let { fields, placeholder } = this.make_edit_struct(this.editAnswer)
      this.editStructedFields = fields
      this.editStructedPlaceholder = placeholder
      let { fields: f, placeholder: p } = this.make_edit_struct(this.editSummary)
      f = f.filter(item => !item.key.includes('推测'))
      const res = this.get_edit_struct(f, p, '\n\n')
      if (res && res.trim() !== '') {
        this.editSummary = res
      }
    },
    updateCoeResultList() {
        // 此方法本来是每次修改以后都会更新结果大表，但是变为新页面的模式以后，就手动刷新了，此处不执行任何动作
        // this.$store.dispatch('updateCoeResultList', this.$router.currentRoute.params.task_id)
    },
    markExp() {
      const payload = {
        id: this.answer_message[this.editIndex].id
      }
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.markExperience(payload).then(result => {
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '标记失败', message: error, type: 'error'})
        })
        this.dialog = false
      })
    },
    unmarkExp() {
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.deleteExperience(this.answer_message[this.editIndex].id).then(result => {
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '标记失败', message: error, type: 'error'})
        })
        this.dialog = false
      })
    },
    on_reviewed(item, type) {
      const payload = {
        coe_result: this.coeResult,
        change_log: {
          'action': 'reviewedTagChange',
          'new_tag': !this.coeResult.is_reviewed,
          'old_tag': this.coeResult.is_reviewed
        }
      }
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.saveCoeResult(payload).then(result => {
          this.updateCoeResultList()
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '标记失败', message: error, type: 'error'})
          this.saving -= 1
        })
      })
    },
    on_reasonable() {
      const payload = {
        coe_result: this.coeResult,
        change_log: {
          'action': 'reasonableTagChange',
          'new_tag': !this.coeResult.is_reasonable,
          'old_tag': this.coeResult.is_reasonable
        }
      }
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.saveCoeResult(payload).then(result => {
          this.updateCoeResultList()
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '标记失败', message: error, type: 'error'})
          this.saving -= 1
        })
      })
    },
    resultIndexChange(newIndexList) {
      const payload = {
        coe_result: this.coeResult,
        change_log: {
          'action': 'changeIndex',
          'new_index_list': newIndexList
        }
      }
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.saveCoeResult(payload).then(result => {
          this.updateCoeResultList()
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '保存失败', message: error, type: 'error'})
          this.saving -= 1
        })
      })
    },
    diffList(newIndexList) {
      const len = newIndexList.length
      let i = 0
      let pos = 0
      while (i < len - 1) {
        if (newIndexList[i + 1] - 1 !== newIndexList[i]) {
          if (newIndexList[i + 1] < newIndexList[i]) {
            pos = i + 1
          } else {
            pos = i
          }
        }
        i++
      }
      return `将 ${newIndexList[pos]} 位置替换到 ${pos} 位置`
    },
    resultContentChange(expIndex, refIndex, oldContent, newContent) {
      const payload = {
        coe_result: this.coeResult,
        change_log: {
          'action': 'contentChange',
          'exp_index': expIndex,
          'msg_index': refIndex,
          'new_message': newContent,
          'old_message': oldContent
        }
      }
      this.enter_saving_with_lock(() => {
        this.saving += 1
        api.saveCoeResult(payload).then(result => {
          this.updateCoeResultList()
          this.find_result()
          this.saving -= 1
        }).catch(error => {
          this.$notify({title: '保存失败', message: error, type: 'error'})
          this.saving -= 1
        })
      })
    },
    updateResult() {
      const answerMessage = this.answer_message
      if (this.is_6to2not()) {
        this.editAnswer = this.get_edit_struct(this.editStructedFields, this.editStructedPlaceholder)
      }
      if (answerMessage) {
        const user = answerMessage[this.editIndex].data[0]
        const assistant = answerMessage[this.editIndex].data[1]
        if (user.content !== this.editSummary) {
          const newContent = {
            'role': user.role, 'content': this.editSummary
          }
          this.resultContentChange(this.editIndex, 0, user, newContent)
        }
        if (assistant.content !== this.editAnswer) {
          const newContent = {
            'role': assistant.role, 'content': this.editAnswer
          }
          this.resultContentChange(this.editIndex, 1, assistant, newContent)
        }
      }
      this.dialog = false
    }
  },
  computed: {
    coe_link() {
      return 'https://coe.mws.sankuai.com/detail/' + this.coe_id + window.location.hash
    },
    summary() {
      const task = this.coeResult
      if (task) {
        let reason = task.reason
        let { fields: f, placeholder: p } = this.make_edit_struct(reason)
        f = f.filter(item => !item.key.includes('推测'))
        reason = this.get_edit_struct(f, p, '\n\n')
        return reason || ''
      }
      return ''
    },
    detail() {
      const task = this.coeResult
      if (task) {
        return task.message || []
      }
      return []
    },
    title() {
      const task = this.coeResult
      if (task) {
        return task.brief || '标题缺失'
      }
      return ''
    },
    answer_list: {
      get() {
        const answerList = []
        const task = this.coeResult
        if (task) {
          const answerMessage = this.answer_message
          for (let i = 0; i < answerMessage.length; i++) {
            const data = answerMessage[i].data
            const isMarked = answerMessage[i].is_marked
            var item = {
              isMarked: isMarked,
              content: '',
              index: i
            }
            if (data && data.length > 0) {
              item.content = data[data.length - 1].content
            }
            answerList.push(item)
          }
        }
        return answerList
      },
      set(newAnswerList) {
        const newIndex = newAnswerList.map(item => item.index)
        const hasIndexNotEqualIndexList = newIndex.some((item, index) => item !== index)
        if (hasIndexNotEqualIndexList) {
          this.resultIndexChange(newIndex)
        }
      }
    }
  }
}
</script>

<style>

/*选中样式*/
.drag-chosen {
    border-bottom: solid 2px #3089dc !important;
    border-top: solid 2px #3089dc !important;
}
.classify-result-title {
  font-size: 18px;
  margin-top: 15px;
  line-height: 1.5;
  font-weight: bolder;
  color: #333;
  padding-left: 10px;

}

.list-flex-grow {
  flex-grow: 1;
  margin: 10px;
}

.classify-result-content {
  font-size: 14px;
  line-height: 1.5;
  font-weight: bolder;
  color: #666;
  white-space: pre-wrap;
  overflow-x: auto;
}

.card {
  overflow-y: scroll;
  height: 85vh;
  overflow-x: hidden;
}

.card-header{
  font-size: 18px;
  font-weight: bold;
}

.card-body{
    padding-left: 10px;
}

.chat-box {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.user-msg {
  align-self: flex-end;
  background-color: #007aff;
  color: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  margin: 8px;
  width: 80%;
  white-space: pre-wrap;
}

.sys-msg {
  align-self: flex-end;
  background-color: #FFD9BF; /* 金色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  padding: 8px 12px;
  border-radius: 8px;
  margin: 8px;
  width: 80%;
  white-space: pre-wrap;
}

.bot-msg {
  align-self: flex-start;
  background-color: #f2f2f2;
  color: #333;
  padding: 8px 12px;
  border-radius: 8px;
  margin: 8px;
  width: 80%;
  white-space: pre-wrap;
}

.chat-box img{
    max-width: 100%;
    height: auto;
}

.drag-handle {
  margin-right: 10px;
  border-radius: 4px;
  border: solid 1px;
}

.answer-title-primary{
    font-size: 16px;
    font-weight: bold;
    color: red;
    margin-bottom: 10px;
}

.answer-title-secondary{
    font-size: 16px;
    font-weight: bold;
    color: #00bcd4;
    margin-bottom: 10px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.modal-content-long {
  height: 90vh;
}
</style>