<template>
    <el-card>
        <div class="chart-title">{{ title }}</div>
        <div class="chart-container">
            <div ref="chart" class="chart"></div>
        </div>
    </el-card>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    title: String,
    secondData: {
      type: Array,
      default: () => []
    },
    chartData: Array
  },
  data() {
    return {
      chart: null,
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      baseSeries: {
        type: 'pie',
        radius: ['0', '40%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold'
          }
        },
        itemStyle: {
          borderWidth: 1,
          borderColor: '#fff'
        },
        labelLine: {
          show: false
        }
      },
      secondSeries: {
        type: 'pie',
        radius: ['40%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: '#fff'
        },
        labelLine: {
          show: false
        }
      }
    }
  },
  mounted() {
    this.render()
  },
  activated() {
    this.render()
  },
  deactivated() {
    this.dispose()
  },
  watch: {
    chartData: {
      handler() {
        this.dispose()
        this.render()
      },
      deep: true
    }
  },
  methods: {
    dispose() {
      if (this.char) {
        this.chart.dispose()
      }
    },
    render() {
      const chart = echarts.init(this.$refs.chart)
      this.chart = chart
      let listener = () => {
        chart.resize()
      }
      window.addEventListener('resize', listener)
      let series = []
      if (this.secondData && this.secondData.length !== 0) {
        console.log(this.secondData)
        series.push({
          ...this.secondSeries,
          data: this.secondData
        })
        series.push({
          ...this.baseSeries,
          data: this.chartData
        })
      } else {
        series.push({
          ...this.secondSeries,
          data: this.chartData
        })
      }
      chart.setOption({
        title: {
          text: this.title,
          left: 'center'
        },
        tooltip: this.tooltip,
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20
        },
        series: series
      })
    }
  }
}
</script>