<template>
  <div class='row'>
    <el-page-header class="col-3" @back="goBack">
        <h1 slot="content"><a :href="base_url">COE智能分析</a></h1>
    </el-page-header>
    <div class="col-3">
        <el-breadcrumb separator="/" v-if="breadcrumb.length!==0">
            <el-breadcrumb-item v-for="item in breadcrumb" :key="item.title" :to="get_item_path(item)">{{ item.title }}</el-breadcrumb-item>
        </el-breadcrumb>
    </div>
    <div class='col-4'>
        <!-- <div class="row-thin">
            <div v-for='(menuItem, index) in menu' :key='index' @click='menuItemOnClick(menuItem, index)'
                :style="{ width: itemWidth + '%' }" :class="isActiveMenuItem(menuItem)">
                {{ menuItem.title }}
            </div>
        </div> -->

        <el-menu v-if="menu" background-color="#f7f7f9" :default-active="activeIndex" mode="horizontal" @select="handleSelect">
            <div v-for='(menuItem, index) in menu' :key='index' style="display: inline-block;"> 
                <el-submenu :key='index' v-if="menuItem.subMenu" :index="menuItem.path">
                    <template slot="title">{{ menuItem.title }}</template>
                    <el-menu-item v-for="(menuItem2, index2) in menuItem.subMenu" 
                        :key="index2" :index="menuItem2.path">
                        {{ menuItem2.title }}
                    </el-menu-item>
                </el-submenu>
                <el-menu-item :key='index' v-if="!menuItem.subMenu" :index="menuItem.path">
                    {{ menuItem.title }} 
                </el-menu-item>
            </div>
        </el-menu>
    </div>

    <div class="col-1">
        <el-button size="small" icon="el-icon-refresh" circle @click="refreshPage"></el-button>
    </div>
    <div id="userInfo col-2" >
        <Dropdown @on-click="exitSystem">
        <div style="margin-top: 2px">
            {{ userInfo.userName }}<Icon type="ios-arrow-down" style="padding-left: 5px"></Icon>
        </div>
        <DropdownMenu slot="list">
            <DropdownItem style="text-align: center" name="quit">
                <Icon type="md-exit" style="padding-right: 5px" />退出
            </DropdownItem>
        </DropdownMenu>
        </Dropdown>

    </div>
  </div>
</template>
  
<script>
import { Bus } from '@/global/bus'
export default {
  props: {
    menu: Array,
    configMenu: Array,
    breadcrumb: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    title() {
      return 'COE智能分析'
    },
    userInfo() {
      return this.$store.state.COEStore.userInfo
    },
    itemWidth() {
      const len = this.menu.length
      return 100 / len
    },
    base_url() {
      return '/coe/coestorage' + window.location.hash
    },
    activeIndex: {
      get() {
        const items = this.flatItems(this.menu)
        console.log(items)
        for (let i = 0; i < items.length; i++) {
          if (this.isActiveMenuItem(items[i]) === 'active') {
            return items[i].path
          }
        }
      },
      set(value) {
        // 不执行任何操作
      }
    }
  },
  methods: {
    refreshPage(menuItemIndex) {
      console.log('refresh')
      this.$emit('refresh')
    },
    get_item_path(item) {
      return {
        path: item.path + window.location.hash
      }
    },
    exitSystem(value) {
      if (value === 'quit') {
        // 退出
        Bus.ssoWeb.logout().then(result => {
          window.location.href = window.location.href
        })
      }
    },
    isActiveMenuItem(menuItem) {
      console.log('isActiveMenuItem')
      const currentUrl = window.location.href
      if (currentUrl.includes(menuItem.path)) {
        return 'active'
      }
      return 'deactive'
    },
    flatItems(menu) {
      let items = []
      menu.forEach(item => {
        if (item.subMenu) {
          items = items.concat(this.flatItems(item.subMenu))
        } else {
          items.push(item)
        }
      })
      return items
    },
    handleSelect(key, keyPath) {
      console.log(this.$router.history)
      console.log(key, keyPath)
      this.$router.replace({path: key + window.location.hash})
    },
    getBreadcrumb() {
      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)
      console.log('match')
      console.log(matched)
      const first = matched[0]
      if (first && first.name.trim().toLocaleLowerCase() !== 'Dashboard'.toLocaleLowerCase()) {
        matched = [{path: '/dashboard', meta: { title: '首页' }}].concat(matched)
      }
      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)
    },
    goBack() {
      if (this.$router.history.length === 1) {
        this.$notify({title: '已经到达第一页', type: 'warn'})
        return
      }
      this.$router.go(-1)
    }
  }
}
</script>

<style>
.active {
  border-bottom: 2px solid rgb(0, 149, 255);
  text-align: center;
}

.deactive {
  text-align: center;
}

.active :hover {
  color: rgb(0, 149, 255);
}

.deactive :hover {
  color: rgb(0, 149, 255);
}
.container {
  padding: 0;
  width: 95%;
}

.row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 15px;
  width: 100%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 15px;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
  padding: 15px;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 15px;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 15px;
}

.col-1 {
  flex: 0 0 8.33%;
  max-width: 8.33%;
  padding: 15px;
}

.col-2 {
  flex: 0 0 16.67%;
  max-width: 16.67%;
  padding: 15px;
}

.col-4 {
  flex: 0 0 33.33%;
  max-width: 33.33%;
  padding: 15px;
}

.col-10 {
  flex: 0 0 83%;
  max-width: 83%;
  padding: 15px;
}


.scrollable {
    overflow-x: auto;
    overflow-y: auto;
}

.row-thin {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

.col-12-thin {
  flex: 0 0 100%;
  max-width: 100%;
}

.col-9-thin {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10-thin {
  flex: 0 0 83%;
  max-width: 83%;
}

.col-6-thin {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-3-thin {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-1-thin {
  flex: 0 0 8.33%;
  max-width: 8.33%;
}

.col-2-thin {
  flex: 0 0 16.67%;
  max-width: 16.67%;
}

.col-4-thin {
  flex: 0 0 33.33%;
  max-width: 33.33%;
}


.btn {
  display: inline-block;
  font-weight: 400;
  color: #fff;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #007bff;
  border: 1px solid #007bff;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}

.table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.modal {
  display: none;
  position: fixed;
  z-index: 20;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.is-active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-background {
  position: absolute;
  z-index: -1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  z-index: 1;
  background-color: #fff;
  border-radius: 0.5rem;
  width: 60%;
  height: 60%;
  overflow: auto;
}

.card {
  margin-bottom: 1.5rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: #f7f7f9;
  border-bottom: 1px solid #dee2e6;
}

.card-header-title {
  margin-bottom: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.card-content {
  padding: 1.25rem;
  border-top: 1px solid #dee2e6;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: #f7f7f9;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.button {
  display: inline-block;
  font-weight: 400;
  color: #fff;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #007bff;
  border: 1px solid #007bff;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.button.is-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.button.is-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.sift_btn {
    margin: 10px;
}

.pagination-rounded .el-pagination__sizes,
.pagination-rounded .el-pagination button {
    border-radius: 20px;
}

.pagination-rounded {
    text-align: right;
}

.el-input__inner{
    width: 100%;
}
.el-select-dropdown__item{
  height: 100%;
  background-color: white;
}
.el-page-header__title{
    display: flex;
    align-items: center;
    justify-content: center;
}
.el-submenu__icon-arrow{
    right: 0 !important;
}
</style>