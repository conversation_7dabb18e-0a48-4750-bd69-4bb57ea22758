<template>
    <div class="container">
        <Navigator @refresh="sync" :menu="menu" :breadcrumb="breadcrumb"></Navigator>
        <div class="el-descriptions__header">
            <div class="el-descriptions__title">结果概览</div>
            <div class="el-descriptions__extra"></div>
        </div>
        <el-collapse v-model="activeName" accordion v-loading="taskLoading">
            <el-collapse-item v-for="item in typeList" :title="item.title" :name="item.type" :key="item.type">
                <div v-if="item.type === 'cause'">
                    <div class="row">
                        <div class="col-6">
                            <PieChart :title="item.title" :chart-data="arrgsData['cause']" v-if="activeName===item.type"></PieChart>
                        </div>
                        <!-- <div class="col-6">
                            <el-card>
                                <div class="wordcloud-title">COE原因分析词云</div>
                                <div class="wordcloud-container">
                                    <VueWordcloud :words="coeWordCloudData" v-if="activeName===item.type"
                                        :color="([, weight]) => weight > 10 ? 'DeepPink' : weight > 5 ? 'RoyalBlue' : 'Indigo'"
                                        font-family="Roboto">
                                        <template slot-scope="{text, weight, word}">
                                            <div :title="weight" style="cursor: pointer;" @click="onWordClick(word)">
                                                {{ text }}
                                            </div>
                                        </template>
                                    </VueWordcloud>
                                </div>
                            </el-card>
                        </div> -->
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <el-card>
                                <div class="chart-title">COE主题分析</div>
                                <div class="chart-container">
                                    <div class="row">
                                        <el-input class="col-9" placeholder="请输入主题" v-model="topic"></el-input>
                                        <el-button class="col-3" type="primary" @click="analyze">分析</el-button>
                                    </div>
                                    <div class="result" v-if="loading">模型思考中...</div>
                                    <div class="result" v-else>{{ result }}</div>
                                </div>
                            </el-card>
                        </div>
                        <div class="col-6">
                            <el-card>
                                <div class="chart-title">按主题聚类的COE列表(TOP5)</div>
                                <div class="chart-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>定级</th>
                                                <th>标题</th>
                                                <th>发生时间</th>
                                            </tr>
                                        </thead>
                                        <tbody v-if="loading">
                                            <tr>
                                                <td :colspan="3" class="text-center">模型思考中...</td>
                                            </tr>
                                        </tbody>
                                        <tbody v-else-if="coeList.length === 0">
                                            <tr>
                                                <td :colspan="3" class="text-center">没有数据...</td>
                                            </tr>
                                        </tbody>
                                        <tbody v-else>
                                            <tr v-for="item in coeList" :key="item.id">
                                                <td>{{ item.level }}</td>
                                                <td><a :href="`https://coe.mws.sankuai.com/detail/${item.coe_id}`"
                                                        target="_blank">{{ item.brief }}</a></td>
                                                <td>{{ item.occur_time.split('T')[0] }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </el-card>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <Metrics :data="metrics[item.type]"></Metrics>
                        </div>
                    </div>
                </div>
                <div v-if="item.type === 'aggr_simple'">
                    <div class="row-no-center">
                        <div style="width: 50%; overflow-x: auto;">
                            <el-tree :data="treeData" :props="defaultProps" :expand-on-click-node="false" class="coe-aggr-tree-warper">
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span width="100px">{{ node.label }}</span>
                                    <span v-if="node.label">
                                        <el-button type="text" size="mini" @click="onEdit(node, data)">修改</el-button>
                                        <el-button type="text" v-if="node.level<=2" size="mini" @click="onAppend(node, data)">下方添加</el-button>
                                        <!-- <el-button type="text" v-if="node.level" size="mini" @click="onDelete(node, data)">删除</el-button> -->
                                        <!-- <el-button type="text" v-if="node.level<=2" size="mini" @click="onRecall(node, data)">召回</el-button> -->
                                    </span>
                                </span>
                            </el-tree>
                        </div>
                        <div style="width: 50%;">
                            <!-- <el-input v-model="threshold" >
                                <template slot="prepend">召回阈值</template>
                                <el-button slot="append" icon="el-icon-search" @click="onRecallQuery(current_query)"></el-button>
                            </el-input> -->
                            <el-input v-model="current_query" disabled >
                                <template slot="prepend">当前查询</template>
                                <!-- <el-button slot="append" icon="el-icon-search" @click="onRecallQuery(current_query)"></el-button> -->
                            </el-input>
                            <el-button slot="append" @click="onRecallQuery(current_query)">重新计算召回</el-button>
                            <SunburstChart  v-if="recall_data" title="" :chart-data="recall_data.rootList"></SunburstChart>
                            <el-table  v-if="recall_data" :data="recall_data.choosed_coes" style="width: 100%; height: 400px; overflow-y: scroll;">
                                <el-table-column
                                    type="index"
                                    :index="indexMethod"/>
                                <el-table-column label="COE链接">
                                    <template slot-scope="scope">
                                        <a :href="'https://coe.mws.sankuai.com/detail/' + scope.row.coe.coe_id"
                                            target="_blank">{{scope.row.coe.brief }}</a>
                                    </template>
                                </el-table-column>
                                <el-table-column label="相似描述">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.text }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="二级分类">
                                    <template slot-scope="scope">
                                        <div>{{ scope.row.query }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="LLM分析链接">
                                    <template slot-scope="scope">
                                        <a :href="get_detail_href(scope.row.coe)" v-if="get_detail_href(scope.row.coe)!==''"
                                            target="_blank">{{ scope.row.coe.cause_analysis.analysis_result }}</a>
                                        <div v-else>{{ scope.row.coe.cause_analysis.analysis_result }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="原因相似度">
                                    <template slot-scope="scope">
                                        {{ scope.row.score }}
                                    </template>
                                </el-table-column>
                                <!-- <el-table-column label="人工反馈相关与否">
                                    <template slot-scope="scope">
                                        <el-switch v-model="scope.row.is_related" @change="onRelatedChange(scope, $event)" active-text="是" inactive-text="否"></el-switch>
                                    </template>
                                </el-table-column> -->
                            </el-table>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="row">
                        <div class="col-6">
                            <PieChart :title="item.title" :chart-data="arrgsData[item.type]" v-if="activeName===item.type"></PieChart>
                        </div>
                        <div class="col-6">
                            <Metrics :data="metrics[item.type]"></Metrics>
                        </div>
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>
        <el-dialog :visible.sync="edit_dialog" title="修改描述">
          <el-input v-model="edit_input"></el-input>
          <el-button @click="onEditCommit">提交</el-button>
          <el-button @click="onDelete">删除</el-button>
          <!-- <el-button @click="">清除历史召回</el-button> -->
        </el-dialog>
    </div>
</template>

<script>
import VueWordcloud from 'vuewordcloud'
import api from './store/api'
import PieChart from './PieChart.vue'
import Metrics from './Metrics.vue'
import Navigator from './Navigator.vue'
import SunburstChart from './SunburstChart.vue'
let id = 1000

export default {
  components: {
    VueWordcloud,
    PieChart,
    SunburstChart,
    Metrics,
    Navigator
  },
  data() {
    return {
      edit_dialog: false,
      threshold: 0.85,
      edit_input: '',
      edit_node: {},
      current_query: '',
      text: '',
      topic: '',
      activeName: null,
      types: [],
      recall_data: null,
      task: {},
      taskLoading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  computed: {
    breadcrumb() {
      return [{'path': '/coe/coestorage', 'title': '首页'},
        {'path': '/coe/task', 'title': '任务列表'},
        {'path': `coe/detail/${this.task_id}/coe_overview`, 'title': '结果概览'}]
    },
    loading() {
      return this.$store.state.COEStore.isCoeListLoading || this.taskLoading
    },
    arrgsData() {
      return this.$store.state.COEStore.coeResultAggrs
    },
    coeData() {
      return this.$store.state.COEStore.coeResultList
    },
    aggrResult() {
      const aggrItem = this.coeData.find(item => item.hasOwnProperty('aggr_simple') && item.aggr_simple !== null)
      if (aggrItem === null || aggrItem === undefined) {
        return null
      }
      return aggrItem.aggr_simple.content
    },
    aggr_simple_chain() {
      const aggrItem = this.coeData.find(item => item.hasOwnProperty('aggr_simple') && item.aggr_simple !== null)
      if (aggrItem === null || aggrItem === undefined) {
        return null
      }
      return aggrItem.aggr_simple
    },
    metrics() {
      console.log(this.$store.state.COEStore.resultListPackage.metrics)
      return this.$store.state.COEStore.resultListPackage.metrics
    },
    coeWordCloudData() {
      return this.$store.state.COEStore.coeWordCloudData
    },
    result() {
      const topicResult = this.$store.state.COEStore.topicResult
      if (topicResult && topicResult.answer) {
        return topicResult.answer
      } else {
        return ''
      }
    },
    coeList() {
      const topicResult = this.$store.state.COEStore.topicResult
      if (topicResult && topicResult.answer) {
        return topicResult.result_list
      } else {
        return []
      }
    },
    typeList() {
      const typeList = []
      if (this.task.sub_task_type_list === null || this.task.sub_task_type_list === undefined) {
        return typeList
      }
      for (const type of this.task.sub_task_type_list) {
        const item = this.types.find(item => item.type === type)
        if (item) {
          typeList.push(item)
        }
      }
      return typeList
    },
    task_id() {
      return this.$router.currentRoute.params.task_id
    },
    menu() {
      const menu = [
        {
          title: '任务配置',
          path: `/coe/detail/${this.task_id}/task_description`
        },
        {
          title: '结果概览',
          path: `/coe/detail/${this.task_id}/coe_overview`
        },
        {
          title: '结果列表',
          path: `/coe/detail/${this.task_id}/coe_result`
        }
      ]
      return menu
    }
  },
  mounted() {
    this.syncTypeList()
    if (this.arrgsData === null || this.arrgsData === undefined ||
        Object.keys(this.arrgsData).length === 0) {
      this.taskLoading = true
      this.findTask()
    } else {
      this.taskLoading = true
      this.searchTask()
    }
  },
  methods: {
    indexMethod(index) {
      return index + 1
    },
    get_detail_href(coe) {
      try {
        const taskId = coe.cause_analysis.analysis_task_id[0]
        const coeId = coe.coe_id
        const type = 'cause'
        return `/coe/result_tab/task_id/${taskId}?coe_id=${coeId}&type=${type}`
      } catch (error) {
        console.error(error)
        return ''
      }
    },
    update_simple_aggr() {
      const chain = this.aggr_simple_chain
      let content = this.encode_aggr_from_tree()
      const changelog = {
        action: 'contentChange',
        exp_index: 0,
        msg_index: 1,
        new_message: {role: 'assistant', content: content}
      }
      this.update_chain('aggr_simple', chain.coe_id, changelog)
    },
    update_chain(type, coeId, changelog) {
      const taskId = this.$router.currentRoute.params.task_id
      const payload = {
        to_sync_coe_result: false,
        coe_result: {
          task_id: [taskId],
          type: type,
          coe_id: coeId
        },
        change_log: changelog
      }
      this.taskLoading = true
      api.saveCoeResult(payload).then(result => {
        this.taskLoading = false
        this.$notify({title: '修改成功', message: '', type: 'success'})
      }).catch(error => {
        this.$notify({title: '修改失败', message: error, type: 'error'})
        this.taskLoading = false
      })
    },
    onRelatedChange(scope, newFlg) {
      console.log(newFlg)
      console.log(scope)
      let index = scope.row.index || scope.$index
      let chain = this.recall_data.chain
      let content = {score: scope.row.score, is_related: newFlg}
      let oldContent = {score: scope.row.score, is_related: !newFlg}

      const changelog = {
        action: 'contentChange',
        exp_index: index,
        msg_index: 1,
        new_message: {role: 'assistant', content: JSON.stringify(content)},
        old_message: {role: 'assistant', content: JSON.stringify(oldContent)}
      }
      this.update_chain(chain.type, chain.coe_id, changelog)
    },
    onAppend(node, data) {
      const newChild = { id: id++, label: '<请输入内容>', children: [] }
      if (!data.children) {
        this.$set(data, 'children', [])
      }
      data.children.push(newChild)
      this.update_simple_aggr()
    },
    find_aggr_data(query) {
      let data = this.recall_data.result
      let frequency = {}
      let queryChilds = {}
      for (var index of [2, 1]) {
        frequency[index] = {}
        data.forEach(item => {
          const currentQuery = item.parent_list[index]
          const subQuery = item.parent_list[index - 1]
          if (frequency[index].hasOwnProperty(subQuery)) {
            frequency[index][subQuery]++
          } else {
            frequency[index][subQuery] = 1
          }
          if (!queryChilds.hasOwnProperty(currentQuery)) {
            queryChilds[currentQuery] = new Set()
          }
          queryChilds[currentQuery].add(subQuery)
        })
      }
      console.log(queryChilds)
      this.recall_data.choosed_coes = data
      console.log(frequency)
      this.recall_data.aggr_data = Object.entries(frequency[2]).map(([name, value]) => ({ name, value }))
      var names = this.recall_data.aggr_data.map(item => item.name)
      var chNames = []
      console.log(names)
      for (var name of names) {
        var chs = queryChilds[name]
        chNames.push(...chs)
      }
      console.log(chNames)
      this.recall_data.aggr_data_second = []
      for (var ch of chNames) {
        this.recall_data.aggr_data_second.push({name: ch, value: frequency[1][ch]})
      }
      console.log(this.recall_data.aggr_data_second)
    },
    onRecallQuery(query) {
      const taskId = this.task_id
      this.taskLoading = true
      api.taskAggrRecall(taskId, query, this.threshold).then(result => {
        console.log(result.data)
        this.recall_data = result.data
        this.recall_data.query = query
        this.find_aggr_data(this.current_query)
        this.taskLoading = false
      }).catch(error => {
        this.$notify({title: '召回失败', message: error, type: 'error'})
        this.taskLoading = false
      })
    },
    onRecall(node, data) {
      const query = node.label
      this.current_query = query
      if (this.recall_data === null) {
        this.onRecallQuery(query)
      }
      this.find_aggr_data(this.current_query)
    },
    onEdit(node, data) {
      this.edit_dialog = true
      this.edit_input = node.label
      this.edit_node = node
      console.log(data)
      this.edit_data = data
    },
    onEditCommit() {
      this.edit_data.label = this.edit_input
      this.edit_dialog = false
      this.update_simple_aggr()
    },
    onDelete() {
      const node = this.edit_node
      const data = this.edit_data
      const parent = node.parent
      const children = parent.data.children || parent.data
      const index = children.findIndex(d => d.label === data.label)
      children.splice(index, 1)
      this.edit_dialog = false
      this.update_simple_aggr()
    },
    decode_aggr_to_tree(text) {
      if (text === undefined || text === null) {
        return
      }
      let lines = text.split(/\n/)
      let rootList = []
      let currentRoot = null
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        if (/^[a-z]\./i.test(line)) {
          let desc = line.split('.')[1].trim()
          desc = /:|：/.test(desc) ? desc.split(/：|:/) : [desc, '']
          let title = desc[0].trim()
          let other = desc[1].trim()
          other = other.replace('包括', '')
          other = other.replace('等。', '')
          other = other.split(/、|，|,/)
          let children = other.map(item => { return {label: item} })
          currentRoot.children.push({label: title, children: children})
        } else if (/^\d+\./i.test(line)) {
          let desc = line.split('.')[1].trim()
          desc = /:|：/.test(desc) ? desc.split(/：|:/) : [desc, '']
          let title = desc[0].trim()
          if (currentRoot) {
            rootList.push(currentRoot)
          }
          currentRoot = {label: title, children: []}
        }
      }
      if (currentRoot) {
        rootList.push(currentRoot)
      }
      return [{label: 'root', children: rootList}]
    },
    encode_aggr_from_tree() {
      let rootList = this.treeData[0].children
      let text = ''
      for (let i = 0; i < rootList.length; i++) {
        text = text + `${i + 1}. ${rootList[i].label}:\n`
        for (let j = 0; j < rootList[i].children.length; j++) {
          let node = rootList[i].children[j]
          text = text + `  ${String.fromCharCode(97 + j)}. ${node.label}:`
          let items = node.children.map(i => i.label)
          text = text + items.join(', ') + '\n'
        }
      }
      return text
    },
    sync() {
      this.syncTypeList()
      this.taskLoading = true
      this.findTask()
    },
    syncTypeList() {
      api.getTypeList().then(result => { this.types = result.data.task_types }).catch(error => {
        this.$notify({title: '获取TypeList失败', message: error, type: 'error'})
      })
    },
    analyze() {
      const taskId = this.$router.currentRoute.params.task_id
      this.$store.dispatch('updateTopic', { taskId, topic: this.topic })
    },
    findTask() {
      const taskId = this.$router.currentRoute.params.task_id
      this.taskLoading = true
      api.findTask(taskId).then(result => {
        this.task = result.data.task
        this.$store.dispatch('updateCoeResultList', {
          taskId: this.$router.currentRoute.params.task_id,
          callback: () => {
            console.log('taskLoaded')
            this.taskLoading = false
            this.treeData = this.decode_aggr_to_tree(this.aggrResult)
          }})
      }).catch(error => {
        this.$notify({title: '获取Task失败', message: error, type: 'error'})
        this.taskLoading = false
      })
    },
    searchTask() {
      const taskId = this.$router.currentRoute.params.task_id
      this.taskLoading = true
      api.findTask(taskId).then(result => {
        this.task = result.data.task
        this.treeData = this.decode_aggr_to_tree(this.aggrResult)
        console.log('taskLoading')
        this.taskLoading = false
      }).catch(error => {
        this.$notify({title: '获取Task失败', message: error, type: 'error'})
        this.taskLoading = false
      })
    }
  }

}
</script>

<style>
.coe-aggr-tree-warper {
    display: inline-block; 
    min-width: 50px; 
    min-height: 100px;
    background-color: #f5f7fa;
}

.coe-analysis {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.coe-summary {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.coe-summary-chart,
.coe-summary-wordcloud {
    width: 50%;
    margin-right: 20px;
}

.chart-title,
.wordcloud-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.chart-container,
.wordcloud-container {
    height: 300px;
    overflow-y: auto;
}

.chart {
    height: 100%;
}

.result {
    white-space: pre-wrap;
}

.coe-analysis-detail {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.coe-analysis-chart {
    width: 25%;
    margin-right: 20px;
}
.el-collapse-item__wrap{
    background-color: transparent;
}
.el-collapse-item__header{
    background-color: transparent;
}
.row-no-center {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
</style>
