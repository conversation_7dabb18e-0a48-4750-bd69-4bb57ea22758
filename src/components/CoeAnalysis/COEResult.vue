<template>
    <div class="container">
        <Navigator @refresh="sync" :menu="menu" :breadcrumb="breadcrumb"></Navigator>
        <div class="el-descriptions__header">
            <div class="el-descriptions__title">结果列表</div>
            <div class="el-descriptions__extra"></div>
        </div>
<!-- <div class="search">
        <input type="text" v-model="searchContent" placeholder="主题搜索">
        </div> -->
        <div>
            <el-table ref="simpleTable" v-loading="taskLoading" :data="coeData" border stripe height="80vh" :fixed="true">
                <el-table-column type="index" width="50" />
                <el-table-column label="COE" width="300">
                    <template slot-scope="item">
                        <a :href="`https://coe.mws.sankuai.com/detail/${item.row.coe_id}`" target="_blank">{{ item.row.brief
                        }}</a>
                        <el-tag v-if="item.row.coe_store.level" type="warning">{{ item.row.coe_store.level }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column v-for="header in headers" :key="header.value" :prop="header.value" :label="header.text"
                    :filters="getFilters(header.value)" :filter-method="filterHandler" sortable>
                    <template slot-scope="item">
                        <el-tooltip class="item-tooltip" effect="dark" placement="top">
                            <div slot="content" style="white-space: pre-wrap; max-width: 300px;">{{ (item.row[header.value]
                                || {}).content }}</div>
                            <div v-if="item.row[header.value]">
                                <el-button circle type="success" class="very-small" @click="on_reviewed(item.row, header.value)"
                                    v-if="(item.row[header.value] || {}).is_reviewed" icon="el-icon-check"></el-button>
                                <el-button circle size="mini" class="very-small" v-else icon="el-icon-minus"
                                    @click="on_reviewed(item.row, header.value)"></el-button>
                                <a :href="tag_lnk(item.row, header.value)" target="_blank"
                                    v-if="isRed((item.row[header.value] || {}).content)" class="item-table-warn">{{
                                        displayText((item.row[header.value] || {}).content) }}</a>
                                <a v-else :href="tag_lnk(item.row, header.value)" target="_blank">{{ displayText((item.row[header.value] ||
                                    {}).content) }}</a>
                                <el-tag v-if="!item.row[header.value].is_reasonable" type="warning" size="mini">推理乱套</el-tag>
                                <el-tag v-if="header.value === 'cause' && item.row.coe_store.cause_analysis" size="mini" type="info">{{
                                    item.row.coe_store.cause_analysis.rd_result
                                }}</el-tag>
                            </div>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { Table, TableColumn } from 'element-ui'
import Navigator from './Navigator.vue'
import api from './store/api'

export default {
  components: {
    Navigator,
    ElTable: Table,
    ElTableColumn: TableColumn
  },
  data() {
    return {
      searchContent: '',
      headerDict: {},
      filterHeaders: ['coe_id', 'brief'],
      saving: 0,
      task: {},
      taskLoading: false,
      headers: []
    }
  },
  mounted() {
    this.syncTypeList()
    this.taskLoading = true
    this.findTask()
    // if (this.coeData === null || this.coeData === undefined || this.coeData.length === 0) {
    //   this.taskLoading = true
    //   this.findTask()
    // } else {
    //   this.taskLoading = true
    //   this.searchTask()
    // }
  },
  computed: {
    breadcrumb() {
      return [{'path': '/coe/coestorage', 'title': '首页'},
        {'path': '/coe/task', 'title': '任务列表'},
        {'path': `coe/detail/${this.task_id}/coe_result`, 'title': '结果列表'}]
    },
    coeData() {
      return this.$store.state.COEStore.coeResultList
    },
    total() {
      return this.$store.state.COEStore.coeResultList.length
    },
    task_id() {
      return this.$router.currentRoute.params.task_id
    },
    menu() {
      const menu = [
        {
          title: '任务配置',
          path: `/coe/detail/${this.task_id}/task_description`
        },
        {
          title: '结果概览',
          path: `/coe/detail/${this.task_id}/coe_overview`
        },
        {
          title: '结果列表',
          path: `/coe/detail/${this.task_id}/coe_result`
        }
      ]
      return menu
    }
  },
  methods: {
    sync() {
      this.syncTypeList()
      this.taskLoading = true
      this.findTask()
    },
    write_headers() {
      const headers = []
      console.log(this.task.sub_task_type_list)
      this.task.sub_task_type_list.forEach((key) => {
        headers.push({
          text: this.headerDict[key] || key,
          value: key
        })
      })
      this.headers = headers
    },
    findTask() {
      const taskId = this.$router.currentRoute.params.task_id
      this.taskLoading = true
      api.findTask(taskId).then(result => {
        this.task = result.data.task
        this.write_headers()
        console.log('taskLoading')
        this.$store.dispatch('updateCoeResultList', {
          taskId: this.$router.currentRoute.params.task_id,
          callback: () => {
            console.log('taskLoaded')
            this.taskLoading = false
            this.$refs.simpleTable.doLayout()
          }})
      }).catch(error => {
        this.$notify({title: '获取Task失败', message: error, type: 'error'})
      })
    },
    searchTask() {
      const taskId = this.$router.currentRoute.params.task_id
      this.taskLoading = true
      api.findTask(taskId).then(result => {
        this.task = result.data.task
        this.write_headers()
        console.log('taskLoading')
        this.taskLoading = false
        this.$refs.simpleTable.doLayout()
      }).catch(error => {
        this.$notify({title: '获取Task失败', message: error, type: 'error'})
      })
    },
    getFilters(type) {
      const resList = this.$store.state.COEStore.coeResultList
      const fliterSet = new Set()
      if (resList === null || resList === undefined || type === undefined || type === null) {
        return []
      }
      resList.forEach((item) => {
        if (item[type]) {
          fliterSet.add(item[type].content)
        }
      })
      const filterList = []
      fliterSet.forEach(item => {
        if (item) {
          const text = item
          filterList.push({
            text: text, value: text
          })
        }
      })
      return filterList
    },
    filterHandler(value, row, column) {
      const property = column['property']
      return row[property].content === value
    },
    displayText(text) {
      return text && text.length > 100
                ? text.substring(0, 100) + '...'
                : text
    },
    isRed(text) {
      return text.startsWith('违规') || text.startsWith('违反') || text.startsWith('无法确定') || text.startsWith('相关')
    },
    syncTypeList() {
      api.getTypeList().then(result => {
        const taskTypes = result.data.task_types
        this.headerDict = {}
        taskTypes.forEach((item) => {
          this.headerDict[item.type] = item.title
        })
      }).catch(error => {
        this.$notify({ title: '同步失败', message: error, type: 'error' })
      })
    },
    on_reviewed(item, type) {
      const taskId = this.$router.currentRoute.params.task_id
      const payload = {
        coe_result: {
          task_id: [taskId],
          type: type,
          coe_id: item.coe_id
        },
        change_log: {
          'action': 'reviewedTagChange',
          'new_tag': !item[type].is_reviewed,
          'old_tag': item[type].is_reviewed
        }
      }
      this.taskLoading = true
      this.saving += 1
      api.saveCoeResult(payload).then(result => {
        this.$store.dispatch('updateCoeResultList', {
          taskId: this.$router.currentRoute.params.task_id,
          callback: () => {
            console.log('taskLoaded')
            this.taskLoading = false
            this.$refs.simpleTable.doLayout()
          }}
        )
        this.saving -= 1
      }).catch(error => {
        this.$notify({ title: '标记失败', message: error, type: 'error' })
        this.saving -= 1
      })
    },
    tag_lnk(item, type) {
      const taskId = this.$router.currentRoute.params.task_id
      return '/coe/result_tab/task_id/' + taskId + '?coe_id=' + item.coe_id + '&type=' + type + window.location.hash
    },
    choose(item, type) {
      const taskId = this.$router.currentRoute.params.task_id
    //   this.$router.push({name: 'COETab',
    //     path: '/coe/result_tab/task_id/' + taskId,
    //     hash: window.location.hash,
    //     query: {coe_id: item.coe_id, type: type}})
      let routerUrl = this.$router.resolve({name: 'COETab',
        path: '/coe/result_tab/task_id/' + taskId,
        hash: window.location.hash,
        query: {coe_id: item.coe_id, type: type}})
      window.open(routerUrl.href)
    }
  }
}
</script>

<style scoped>
.search {
    padding: 20px;
}

.item-tooltip {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-table-warn {
    color: red;
}
.very-small{
    padding: 2px;
}
</style>