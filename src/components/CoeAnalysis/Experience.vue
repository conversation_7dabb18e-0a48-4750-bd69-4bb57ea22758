<template>
  <div class="container">
    <Navigator :configMenu="configMenu" :menu="menu"></Navigator>
    <el-form class="row">
            <el-form-item class="col-4-thin" label="任务类型">
                <el-select v-model="type" placeholder="请选择 COE 定级">
                    <el-option v-for="item in headers" :key="item.type" :label="item.title" :value="item.type"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item class="col-3-thin">
                <el-button type="primary" icon="el-icon-search" @click="listExp">查询</el-button>
            </el-form-item>
        </el-form>
    <div class="row">
      <div v-for="(item, index) in cardList" :key="index" class="col-3">
        <el-card :body-style="{ padding: '20px' }">
          <h3><a :href="`https://coe.mws.sankuai.com/detail/${item.coe_id}`">{{ item.brief }}</a></h3>
          <!-- <div>
            ID:{{item.id}}
          </div> -->
          <div>
            逻辑思考:
            <el-input type="textarea" :rows="6" :value="item.data[0].content" readonly></el-input>
          </div>
          <div>
            回答:
            <el-input type="textarea" :rows="6" :value="item.data[item.data.length-1].content" readonly></el-input>
          </div>
          <div>
            <el-button type="primary" @click="showDialog(index)">
              修改知识
            </el-button>
          </div>
        </el-card>
      </div>
      
      <el-dialog :visible.sync="dialogVisible" :before-close="handleClose" width="800px" center>
        <div v-if="cardList[dialogIndex]">
          <h2>知识-{{ cardList[dialogIndex].id }}</h2>
          <div>
            逻辑思考:
            <el-input type="textarea" :rows="6" v-model="thought" placeholder="请输入内容"></el-input>
          </div>
          <div>
            回答:
            <el-input type="textarea" :rows="6" v-model="result" placeholder="请输入内容"></el-input>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="saveDialog" :loading="loading">保存</el-button>
            <el-button type="danger" @click="deleteExperience" :loading="loading">删除知识</el-button>
            <el-button @click="dialogVisible = false" :loading="loading">关闭</el-button>
          </span>
        </div>
      </el-dialog>
    </div>
    <el-pagination class="pagination-rounded" v-if="total > pageSize" :current-page="currentPage" :page-size="pageSize"
            :total="total" :page-sizes="[2,4,8]" @current-change="onPageChange" @size-change="onSizeChange" 
            layout="total, sizes, prev, pager, next, jumper" />
  </div>
</template>

<script>
import Navigator from './Navigator.vue'
import api from './store/api'
import { Select, Option, FormItem, Form, Pagination, Tree } from 'element-ui'
export default {
  components: {
    ElSelect: Select,
    ElOption: Option,
    ElFormItem: FormItem,
    ELForm: Form,
    ELPagination: Pagination,
    ELTree: Tree,
    Navigator
  },
  computed: {
    cardList() {
      var cardList = this.$store.state.COEStore.experienceData.experience_list
      return cardList
    },
    total() {
      return this.$store.state.COEStore.experienceData.total
    },
    configMenu() {
      return this.$store.state.COEStore.configMenu
    },
    menu() {
      return this.$store.state.COEStore.menu
    }
  },
  data() {
    return {
      loading: false,
      headers: [],
      thought: null,
      result: null,
      dialogVisible: false,
      dialogIndex: null,
      newVariable: null,
      pageSize: 4,
      currentPage: 1,
      type: 'cause'
    }
  },
  mounted() {
    this.syncTypeList()
    this.listExp()
  },
  methods: {
    listExp() {
      api.getExperience(this.type, this.pageSize, (this.currentPage - 1) * this.pageSize).then(result => {
        this.$store.dispatch('updateExperience', result.data)
      }).catch(error => {
        this.$notify({title: '保存失败', message: error, type: 'error'})
      })
    },
    showDialog(index) {
      this.dialogIndex = index
      this.dialogVisible = true
      const item = this.cardList[index]
      this.thought = item.data[0].content
      this.result = item.data[item.data.length - 1].content
    },
    syncTypeList() {
      api.getTypeList().then(result => { this.headers = result.data.task_types }).catch(error => {
        this.$notify({title: '获取TypeList失败', message: error, type: 'error'})
      })
    },
    saveDialog() {
      const payload = this.cardList[this.dialogIndex]
      payload.data = [
            {'role': 'user', 'content': this.thought},
            {'role': 'assistant', 'content': this.result}
      ]
      this.loading = true
      api.saveExperience(payload).then(result => {
        this.listExp()
        this.dialogVisible = false
        this.loading = false
      }).catch(error => {
        this.$notify({title: '保存失败', message: error, type: 'error'})
      })
    },
    deleteExperience() {
      const item = this.cardList[this.dialogIndex]
      this.loading = true
      api.deleteExperience(item.id).then(result => {
        this.listExp()
        this.dialogVisible = false
        this.loading = false
      }).catch(error => {
        this.$notify({title: '删除失败', message: error, type: 'error'})
      })
    },
    handleClose(done) {
      done()
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize
      this.listExp()
    },
    onPageChange(page) {
      this.currentPage = page
      this.listExp()
    }
  }
}
</script>
