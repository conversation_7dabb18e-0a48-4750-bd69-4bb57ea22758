<template>
    <el-card>
        <div class="chart-container">
            <div ref="chart" class="chart"></div>
        </div>
    </el-card>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    title: String,
    chartData: Array
  },
  data() {
    return {
      chart: null,
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return params.name + ': ' + params.value + ' (' + params.data.rate.toFixed(2) + '%)'
        }
      },
      baseSeries: {
        type: 'sunburst',
        radius: ['0', '95%'],
        sort: undefined,
        label: {
          show: false,
          position: 'center'
        },
        levels: [
          {},
          {
            r0: '15%',
            r: '35%',
            itemStyle: {
              borderWidth: 2
            },
            label: {
              rotate: 'tangential'
            }
          },
          {
            r0: '35%',
            r: '70%',
            label: {
              align: 'right'
            }
          },
          {
            r0: '70%',
            r: '72%',
            label: {
              position: 'outside',
              padding: 3,
              silent: false
            },
            itemStyle: {
              borderWidth: 3
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.render()
  },
  activated() {
    this.render()
  },
  deactivated() {
    this.dispose()
  },
  watch: {
    chartData: {
      handler() {
        this.dispose()
        this.render()
      },
      deep: true
    }
  },
  methods: {
    dispose() {
      if (this.char) {
        this.chart.dispose()
      }
    },
    render() {
      const chart = echarts.init(this.$refs.chart)
      this.chart = chart
      let listener = () => {
        chart.resize()
      }
      window.addEventListener('resize', listener)
      let series = [
        {
          ...this.baseSeries,
          data: this.chartData
        }
      ]
      chart.setOption({
        title: {
          text: this.title,
          left: 'center'
        },
        visualMap: {
          type: 'continuous',
          min: 0,
          max: 10,
          inRange: {
            color: ['#2F93C8', '#AEC48F', '#FFDB5C', '#F98862']
          }
        },
        tooltip: this.tooltip,
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20
        },
        series: series
      })
    }
  }
}
</script>