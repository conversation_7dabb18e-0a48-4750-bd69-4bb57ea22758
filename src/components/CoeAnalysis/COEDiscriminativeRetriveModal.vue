<template>
    <div>
        <mtd-announcement type="info" show-icon>
            <template #default>
                尝试在 {{ count }}中，逐个 COE 按照如下标准进行筛选
            </template>
        </mtd-announcement>
        <Divider></Divider>
        <mtd-form>
            <mtd-form-item label="背景知识">
                <mtd-select class="text-area-input" v-model="system" autosize filterable allow-create placeholder="背景知识">
                    <mtd-option v-for="item in systemOptions" :key="item" :label="item" :value="item"/>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="筛选条件如下">
                <mtd-select class="text-area-input" v-model="cretia" autosize filterable allow-create placeholder="请输入筛选条件，您可以手动录入，然后新建选项">
                    <mtd-option v-for="item in cretiaOptions" :key="item" :label="item" :value="item"/>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="一些示例如下">
                <mtd-textarea class="text-area-input" v-model="fewShot" autosize placeholder="给大模型一点示例吧?（选填）" />
            </mtd-form-item>
            <mtd-form-item label="COE内容如下">
                <mtd-select class="text-area-input" v-model="content_fields" multiple clearable allow-create placeholder="请选择内容">
                    <mtd-option v-for="item in content_fields_options" :key="item" :label="item" :value="item"/>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="输出格式">
                <mtd-select class="text-area-input" v-model="output_format" filterable autosize allow-create placeholder="输出格式">
                    <mtd-option v-for="item in formatOptions" :key="item" :label="item" :value="item"/>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="模型选择">
                <mtd-select class="text-area-input" v-model="model" placeholder="请选择模型">
                    <mtd-option v-for="item in model_options" :key="item" :label="item" :value="item"/>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="关键词">
                <mtd-textarea class="text-area-input" v-model="keywords" autosize placeholder="请输入关键词，以逗号分隔（选填）" />
            </mtd-form-item>
            <mtd-form-item label="执行">
                <mtd-button icon="mtdicon-search" @click="on_search"></mtd-button>
            </mtd-form-item>
        </mtd-form>
    </div>
</template>

<script>
import api from './store/api'

export default {
  props: {
    searchConfig: Object,
    count: Number
  },
  data() {
    return {
      system: '你是一个线上问题的复盘专员，你需要根据我给的判断条件，逐条判断COE是否相关。',
      fewShot: '',
      cretia: '',
      output_format: '结果: 相关 或者 不相关 \n原因: ',
      keywords: '',
      content_fields: ['智能总结', '[标题]', '[现象]'],
    //   content_fields_options: ['智能总结', '时间线', '[发生时间]', '[标题]', '[现象]', '[客户影响]', '[故障类型]', '[经验教训]',
    //     '[正确做法]', '[开始处理时间]', '分析测试流程', '分析规避方案', '分析故障发现', '分析Code Review流程', '分析故障根因', '分析故障影响',
    //     '分析应急预案', '分析测试流程', '分析变更流程', '分析故障响应', '分析故障定位', '分析处理流程', '其他' ],
      content_fields_options: ['智能总结', '[标题]', '[现象]', '[客户影响]', '[故障类型]', '[经验教训]', '[正确做法]'],
      model_options: ['deepseek-chat', 'gpt-4o-mini'],
      model: 'deepseek-chat',
      systemOptions: [
        '你是一个线上问题的复盘专员，你需要根据我给的判断条件，判断COE是否相关。',
        '你是一个线上问题的复盘专员，你需要判断COE是否符合我给出的筛选条件。'
      ],
      cretiaOptions: [
        '请问线上问题是否与 AB 实验相关？',
        '请问线上问题是否影响优惠券的使用？'
      ],
      formatOptions: [
        '结果: 相关 或者 不相关 \n原因: ',
        '结果: 是 或者 否 \n原因: '
      ]
    }
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    on_search() {
      let searchConfig = this.searchConfig
      searchConfig.size = 5000
      searchConfig.from = 0
      const payload = {
        storage_config: this.searchConfig,
        extral_args: [
            {key: 'system', value: this.system},
            {key: 'output_format', value: this.output_format},
            {key: 'few_shot', value: this.fewShot},
            {key: 'cretia', value: this.cretia},
            {key: 'keywords', value: this.keywords},
            {key: 'content_fields', value: this.content_fields.join(',')},
            {key: 'model', value: this.model}
        ],
        type_list: ['determinate_search'],
        name: 'COE 判别式筛查',
        source: '手动触发'
      }
      console.log(payload)
      api.startTask(payload).then(resp => {
        const taskId = resp.data.id
        const url = `https://qa.sankuai.com/coe/detail/${taskId}/coe_result` + window.location.hash
        this.$notify({title: '触发成功', message: `<a href="${url}" target="_blank">任务创建完成</a>，请耐心等待分析结果`, dangerouslyUseHTMLString: true, type: 'success'})
      }).catch(error => {
        this.$notify({title: '网络问题', message: error.message, type: 'error'})
      })
    }
  }
}
</script>

<style>
.text-area-input{
    width: 700px;
}
</style>