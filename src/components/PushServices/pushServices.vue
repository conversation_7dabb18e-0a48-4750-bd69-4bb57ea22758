<template xmlns="http://www.w3.org/1999/html">
  <div>
    <Modal v-model="pushModal" @on-ok="pushService" width=800 okText="推送">
      <p slot="header"><span class="ciTip">{{this.getProjectId(this.pushIndex)}}</span>项目即时推送</p>
      <Card :bordered="true">
        <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 要推送的项目必须<span class="ciTip">已经接入</span>Bug推送服务！</p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">刘振舟（liuzhenzhou）</span></p>
        <p><i class="fa fa-circle-o" aria-hidden="true"></i> 当<span class="ciTip">关掉</span>推送报告人、推送经办人、推送抄送人三个按钮时，只会<span class="ciTip">推送整体情况！</span></p>
      </Card>
      <Timeline style="margin-top: 15px">
        <TimelineItem>推送报告人
          <i-switch size="large" style="margin-left: 5px" v-model="pushModalCreator">
            <span slot="open">推送</span>
            <span slot="close">关闭</span>
          </i-switch>
        </TimelineItem>
        <TimelineItem>推送经办人
          <i-switch size="large" style="margin-left: 5px" v-model="pushModalProcessor">
            <span slot="open">推送</span>
            <span slot="close">关闭</span>
          </i-switch>
        </TimelineItem>
        <TimelineItem>推送抄送人
          <i-switch size="large" style="margin-left: 5px" v-model="pushModalCc">
            <span slot="open">推送</span>
            <span slot="close">关闭</span>
          </i-switch>
        </TimelineItem>
        <TimelineItem>推送<span class="ciTip">{{this.getProjectId(this.pushIndex)}}</span>项目的全部推送
          <i-switch size="large" style="margin-left: 5px" v-model="pushModalAll">
            <span slot="open">推送</span>
            <span slot="close">关闭</span>
          </i-switch>
        </TimelineItem>
      </Timeline>
    </Modal>
    <Modal v-model="basicInfoModal" @on-ok="saveService" width=850>
      <p v-if="isEditMode" slot="header"><span class="ciTip">{{this.getProjectId(this.editIndex)}}</span>项目Bug推送服务配置修改</p>
      <p v-else slot="header">新增Bug推送服务配置</p>
      <div style="max-height: 500px;overflow-y: auto">
        <Card :bordered="true">
          <h4 slot="title"><i class="fa fa-exclamation" aria-hidden="true"></i> 注意事项</h4>
          <p><i class="fa fa-circle-o" aria-hidden="true"></i> 将<span class="ciTip">HBFRJira</span>加为<span class="ciTip">jira里对应项目（如HI）</span>的项目用户权限,没有权限获取不到数据</p>
          <p><i class="fa fa-circle-o" aria-hidden="true"></i> 使用中遇到问题请找<span class="ciTip">刘振舟（liuzhenzhou）</span></p>
          <p><i class="fa fa-circle-o" aria-hidden="true"></i> 点击前往<span class="ciTip"><a href="https://wiki.sankuai.com/pages/viewpage.action?pageId=1000954388" target="_blank">使用说明</a></span>。</p>
          <p><i class="fa fa-circle-o" aria-hidden="true"></i> 提示群组没有推送权限时，请按照<a href="https://wiki.sankuai.com/pages/viewpage.action?pageId=1115495035" target="_blank"><span class="ciTip">https://wiki.sankuai.com/pages/viewpage.action?pageId=1115495035</span></a>申请权限。</p>
          <p><i class="fa fa-circle-o" aria-hidden="true"></i> 酒旅QA助手 公众号ID <span class="ciTip">513141。</span></p>
        </Card>
        <Timeline style="margin-top: 15px">
          <TimelineItem v-if="!isEditMode">项目组ID<span style="color: #ff0000;">*</span>
            <Input v-model="basicProjectId" style="margin-top: 15px" placeholder="e.g:HI">
              <span slot="prepend">项目组ID</span>
            </Input>
          </TimelineItem>
          <TimelineItem>待解决JQL<span style="color: #ff0000;">*</span>
            <Input v-model="basicSolveJql" style="margin-top: 15px" placeholder='e.g:project = HI AND issuetype in (产品Bug, 其它, "Online Bug", Bug, 自测Bug, 子Bug, Sub-Bug) AND status in (Open, 待需求评审, Reopened, 客户端开发中, 开发完成) ORDER BY status ASC, summary ASC, created DESC'>
              <span slot="prepend">待解决JQL</span>
            </Input>
          </TimelineItem>
          <TimelineItem>待验收JQL
            <Input v-model="basicCheckJql" style="margin-top: 15px" placeholder='e.g:project = HI AND issuetype in (产品Bug, 其它, "Online Bug", Bug, 自测Bug, 子Bug, Sub-Bug) AND status in (已打包待测试) ORDER BY status ASC, summary ASC, created DESC'>
              <span slot="prepend">待验收JQL</span>
            </Input>
          </TimelineItem>
          <TimelineItem>要推送的大象群组ID<span style="color: #ff0000;">*</span>
            <Input v-model="basicDxId" style="margin-top: 15px" placeholder='e.g:200785'>
              <span slot="prepend">大象群组ID</span>
            </Input>
          </TimelineItem>
          <TimelineItem>项目名称后缀<span style="color: #ff0000;">*</span>
            <Input v-model="basicSuffix" style="margin-top: 15px" placeholder='e.g:自测bug'>
              <span slot="prepend">项目名称后缀</span>
            </Input>
          </TimelineItem>
          <TimelineItem>推送报告人
            <i-switch size="large" style="margin-left: 5px" v-model="basicModalCreator">
              <span slot="open">推送</span>
              <span slot="close">关闭</span>
            </i-switch>
          </TimelineItem>
          <TimelineItem>推送经办人
            <i-switch size="large" style="margin-left: 5px" v-model="basicModalProcessor">
              <span slot="open">推送</span>
              <span slot="close">关闭</span>
            </i-switch>
          </TimelineItem>
          <TimelineItem>推送抄送人
            <i-switch size="large" style="margin-left: 5px" v-model="basicModalCc">
              <span slot="open">推送</span>
              <span slot="close">关闭</span>
            </i-switch>
          </TimelineItem>
          <TimelineItem>超期阈值
            <span style="padding-left: 5px"><InputNumber :max="5" :min="1" :step="1" v-model="basicDateGate" :precision=0 :formatter="value => `${value}天`" :parser="value => value.replace('天', '')"></InputNumber></span> <span style="color: #ffa500;font-weight: bolder;padding-left: 5px"># 当前时间 - issue创建时间 >= 超期阈值 时才进行推送</span>
          </TimelineItem>
          <TimelineItem>推送阈值
            <span style="padding-left: 5px"><InputNumber :max="5" :min="0" :step="1" v-model="basicCountGate" :precision=0></InputNumber></span> <span style="color: #ffa500;font-weight: bolder;padding-left: 5px"># 解决bug数 + 待验收bug数 >= 推送阈值 时才进行推送</span>
          </TimelineItem>
        </Timeline>
      </div>
    </Modal>
    <head-component></head-component>
    <Tabs value="bug" style="margin-left: 20px; margin-right: 20px; width: auto;margin-top: 30px">
      <TabPane label="Bug推送" name="bug">
        <Row type="flex">
          <Col style="text-align: left; width: 85%; padding-top: 25px">
            <Button type="primary" @click="basicInfoAdd">新增</Button>
          </Col>
          <Col style="text-align: right; width: 15%; padding-top: 25px">
            <Input v-model= "search" placeholder="搜索" icon="md-search" style="width: 200px;padding-bottom: 10px;"/>
          </Col>
        </Row>
        <Table :columns="bugColumns" :data="displayData" border stripe id="bug_info_table" class="bug-info-table"></Table>
        <div style='margin-top: 10px;padding-bottom: 120px;overflow: hidden;float: right'>
          <Page :total='total' :page-size='pageSize'  @on-change='changePage' :current='currentPage'></Page>
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'
  export default {
    name: 'pushServices',
    data: function () {
      return {
        basicProjectId: '',
        basicSolveJql: '',
        basicCheckJql: '',
        basicDxId: '',
        basicSuffix: '',
        basicModalCreator: true,
        basicModalProcessor: true,
        basicModalCc: true,
        basicDateGate: 1,
        basicCountGate: 0,
        editIndex: 0,
        basicInfoModal: false,
        isEditMode: false,
        pushModalCreator: true,
        pushModalProcessor: true,
        pushModalCc: true,
        pushModalAll: false,
        pushModal: false,
        pushIndex: 0,
        search: '',
        total: 0,
        currentPage: 1,
        bugColumns: [
          {
            title: '序号',
            key: 'no',
            width: 60,
            align: 'center'
          },
          {
            title: '项目组ID',
            key: 'projectId',
            width: 120,
            align: 'left',
            render: (h, params) => {
              if (params.row['projectId']) {
                return h('Tag', {
                  attrs: {
                    color: 'primary'
                  }
                }, params.row['projectId'])
              }
            }
          },
          {
            title: '待解决JQL',
            key: 'jqlSolved',
            align: 'left'
          },
          {
            title: '待验收JQL',
            key: 'jqlChecked',
            align: 'left'
          },
          {
            title: '大象群组ID',
            key: 'gid',
            width: 120,
            align: 'left',
            render: (h, params) => {
              return h('Tag', {
                attrs: {
                  color: 'primary'
                }
              }, params.row.gid)
            }
          },
          {
            title: '推送报告人',
            key: 'pushCreator',
            width: 100,
            align: 'center',
            render: (h, params) => {
              if (Number(params.row.pushCreator) === 1) {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-checkmark-circle',
                      size: 18,
                      color: '#19be6b'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              } else {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-close-circle',
                      size: 18,
                      color: '#ed3f14'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              }
            }
          },
          {
            title: '推送处理人',
            key: 'pushProcess',
            width: 100,
            align: 'center',
            render: (h, params) => {
              if (Number(params.row.pushProcess) === 1) {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-checkmark-circle',
                      size: 18,
                      color: '#19be6b'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              } else {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-close-circle',
                      size: 18,
                      color: '#ed3f14'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              }
            }
          },
          {
            title: '推送抄送人',
            key: 'pushCc',
            width: 100,
            align: 'center',
            render: (h, params) => {
              if (Number(params.row.pushCc) === 1) {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-checkmark-circle',
                      size: 18,
                      color: '#19be6b'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              } else {
                return h('div', [
                  h('Icon', {
                    props: {
                      type: 'md-close-circle',
                      size: 18,
                      color: '#ed3f14'
                    },
                    style: {
                      fontsize: '30px'
                    }
                  })
                ])
              }
            }
          },
          {
            title: '超期阈值',
            key: 'warningDay',
            width: 85,
            align: 'center'
          },
          {
            title: '推送阈值',
            key: 'pushGating',
            width: 85,
            align: 'center'
          },
          {
            title: '后缀',
            key: 'suffix',
            width: 200,
            align: 'left'
          },
          {
            title: '操作',
            key: 'option',
            align: 'center',
            width: 180,
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      // alert(params.index)
                      this.basicInfoEdit(params.index)
                    }
                  }
                }, '编辑'),
                h('Button', {
                  props: {
                    type: 'success',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      this.pushServiceModal(params.index)
                    }
                  }
                }, '推送'),
                h('Button', {
                  props: {
                    type: 'error',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      // alert(params.index)
                      this.stopService(params.index)
                    }
                  }
                }, '停用')
              ])
            }
          }
        ],
        displayData: [],
        bugData: [],
        bugDataBackUp: [],
        pageSize: 20
      }
    },
    methods: {
      getBasicData: function () {
        let self = this
        axios.get('http://10.25.73.168:10002/getBasicData', {
          timeout: 10000, dataType: 'json'
        }).then(function (message) {
          if (message.data) {
            self.bugData = []
            self.bugDataBackUp = []
            self.bugData = message.data
            self.bugDataBackUp = message.data
            self.changeCurrentPage(self.bugData)
          }
        }).catch(function (error) {
          self.bugData = []
          self.bugDataBackUp = []
          console.log(error)
          setTimeout(() => {
            self.$Message.error('加载数据失败')
          }, 1500)
        })
      },
      changeCurrentPage (data) {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.displayData = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.displayData.push(data[i])
        }
        this.total = data.length
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage(this.bugData)
      },
      stopService: function (index) {
        let self = this
        let data = this.displayData[index]
        axios.post('http://10.25.73.168:10002/stopPushService', JSON.stringify({
          'project_id': data.projectId,
          'gid': data.gid,
          'context': Bus.userInfo.userLogin,
          'suffix': data.suffix
        })).then(function (message) {
          if (message.data.status === 'success') {
            self.$Message.success('停用成功！')
            self.getBasicData()
          } else {
            self.$Message.error(message.data.reason)
          }
        }).catch(function (error) {
          console.log(error)
          self.$Message.error('内部服务错误！')
        })
      },
      pushServiceModal: function (index) {
        this.pushIndex = index
        this.pushModal = true
      },
      pushService: function () {
        // let self = this
        let data = this.displayData[this.pushIndex]
        axios.post('http://10.25.73.168:10002/push', JSON.stringify({
          'project_id': data.projectId,
          'push_creator': this.pushModalCreator,
          'push_process': this.pushModalProcessor,
          'push_cc': this.pushModalCc,
          'push_all': this.pushModalAll,
          'context': Bus.userInfo.userLogin,
          'suffix': data.suffix
        })).then(function (message) {
          if (message.data.status === 'success') {
            self.$Message.success('推送成功！')
            self.pushModalCreator = true
            self.pushModalProcessor = true
            self.pushModalCc = true
          } else {
            self.$Message.error(message.data.reason)
          }
        }).catch(function (error) {
          console.log(error)
          self.$Message.error('内部服务错误！')
        })
      },
      getProjectId: function (index) {
        if (this.displayData && this.displayData.length > 0) {
          if ('projectId' in this.displayData[index]) {
            return this.displayData[index]['projectId']
          } else {
            return ''
          }
        }
      },
      basicInfoEdit (index) {
        this.editIndex = index
        this.isEditMode = true
        this.basicInfoModal = true
        let data = this.displayData[index]
        this.basicProjectId = data.projectId
        this.basicSolveJql = data.jqlSolved
        this.basicCheckJql = data.jqlChecked
        this.basicDxId = data.gid
        this.basicSuffix = data.suffix
        this.basicModalCreator = this.parseIntToBool(data.pushCreator)
        this.basicModalProcessor = this.parseIntToBool(data.pushProcess)
        this.basicModalCc = this.parseIntToBool(data.pushCc)
        this.basicDateGate = data.warningDay
        this.basicCountGate = data.pushGating
      },
      basicInfoAdd () {
        this.isEditMode = false
        this.basicInfoModal = true
        this.basicProjectId = ''
        this.basicSolveJql = ''
        this.basicCheckJql = ''
        this.basicDxId = ''
        this.basicSuffix = ''
        this.basicModalCreator = true
        this.basicModalProcessor = true
        this.basicModalCc = true
        this.basicDateGate = 1
        this.basicCountGate = 0
      },
      saveService () {
        let self = this
        this.$Message.info('开始处理！')
        if (this.isEditMode) {
          // 编辑模式
          let data = this.displayData[this.editIndex]
          axios.post('http://10.25.73.168:10002/update', JSON.stringify({
            'project_id': this.basicProjectId,
            'solved_jql': this.basicSolveJql,
            'checked_jql': this.basicCheckJql,
            'dx_id': this.basicDxId,
            'push_creator': this.parseBoolToInt(this.basicModalCreator),
            'push_process': this.parseBoolToInt(this.basicModalProcessor),
            'push_cc': this.parseBoolToInt(this.basicModalCc),
            'warning_day': this.basicDateGate,
            'context': Bus.userInfo.userLogin,
            'push_gating': this.basicCountGate,
            'suffix': this.basicSuffix,
            'old_dx_id': data.gid,
            'old_suffix': data.suffix
          })).then(function (message) {
            if (message.data.status === 'success') {
              self.$Message.success('更新成功！')
              self.getBasicData()
            } else {
              self.$Message.error(message.data.reason)
              self.basicInfoModal = true
            }
          }).catch(function (error) {
            console.log(error)
            self.$Message.error('内部服务错误！')
            self.basicInfoModal = true
          })
        } else {
          // 新增模式
          axios.post('http://10.25.73.168:10002/db', JSON.stringify({
            'project_id': this.basicProjectId,
            'solved_jql': this.basicSolveJql,
            'checked_jql': this.basicCheckJql,
            'dx_id': this.basicDxId,
            'push_creator': this.parseBoolToInt(this.basicModalCreator),
            'push_process': this.parseBoolToInt(this.basicModalProcessor),
            'push_cc': this.parseBoolToInt(this.basicModalCc),
            'warning_day': this.basicDateGate,
            'context': Bus.userInfo.userLogin,
            'push_gating': this.basicCountGate,
            'suffix': this.basicSuffix
          })).then(function (message) {
            if (message.data.status === 'success') {
              self.$Message.success('新增成功！')
              self.getBasicData()
            } else {
              self.$Message.error(message.data.reason)
              self.basicInfoModal = true
            }
          }).catch(function (error) {
            console.log(error)
            self.$Message.error('内部服务错误！')
            self.basicInfoModal = true
          })
        }
      },
      parseBoolToInt: function (bool) {
        if (bool) {
          return 1
        } else {
          return 0
        }
      },
      parseIntToBool: function (int) {
        if (int === 1) {
          return true
        } else {
          return false
        }
      }
    },
    mounted: function () {
      this.getBasicData()
    },
    watch: {
      search: function () {
        this.bugData = []
        if (this.search) {
          for (let item of this.bugDataBackUp) {
            let keys = Object.keys(item)
            for (let unit of keys) {
              if (item[unit].toString().toLowerCase().indexOf(this.search.toLowerCase()) !== -1) {
                this.bugData.push(item)
                break
              }
            }
          }
        } else {
          this.bugData = this.bugDataBackUp
        }
        this.total = this.bugData.length
        this.changeCurrentPage(this.bugData)
      }
    }
  }
</script>

<style>
  .bug-info-table th:nth-child(2) {
    text-align: center;
  }
  .bug-info-table th:nth-child(3) {
    text-align: center;
  }
  .bug-info-table th:nth-child(4) {
    text-align: center;
  }
  .bug-info-table th:nth-child(5) {
    text-align: center;
  }
  .bug-info-table th:nth-child(11) {
    text-align: center;
  }
  .ciTip{
    color: #337ab7;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: bold;
  }
</style>
