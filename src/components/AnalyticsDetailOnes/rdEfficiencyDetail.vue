<template>
  <div>
    <Table stripe border :columns="rdcolumns" :data="rdeffectDetail"></Table>
    <div style="margin-top: 10px;padding-bottom: 120px;overflow: hidden;float: right">
      <Page :total="count" :page-size="pagesize" :current="currentPage" show-total @on-change="changePage"></Page>
    </div>
  </div>
</template>

<script>
  // import axios from 'axios'
  // import Vue from 'vue'
  // import Head from '@/components/common/Head'
  import { Bus } from '@/global/bus'
  // import router from '@/router'
  // import { analyticsbaseAPI } from '@/global/variable'

  Bus.$on('refreshrdEfficiencyDetailData', function (data) {
    Bus.rdEfficiencyDetailOnesObject.backupdata = Bus.rdEfficiencyDetailOnesObject.processData(data) // 所有数据
    Bus.rdEfficiencyDetailOnesObject.count = Bus.rdEfficiencyDetailOnesObject.backupdata.length
    Bus.rdEfficiencyDetailOnesObject.rdeffectDetail = Bus.rdEfficiencyDetailOnesObject.getinitdata(Bus.rdEfficiencyDetailOnesObject.backupdata, Bus.rdEfficiencyDetailOnesObject.pagesize)
    console.log(Bus.rdEfficiencyDetailOnesObject.rdeffectDetail)
    // console.log('start', data)
  })
  export default {
    name: 'rd-efficiency-detail',
    data: function () {
      return {
        rdeffectDetail: [],
        backupdata: [],
        count: 0,
        pagesize: 10,
        currentPage: 1,
        rdcolumns: [
          {
            title: 'RD',
            key: 'person',
            // width: 20,
            sortable: true
          },
          {
            title: '需求评审PD',
            key: 'reqReviewPd',
            // width: 20,
            sortable: true
          },
          {
            title: '技术评审PD',
            key: 'tecReviewPd',
            // width: 120,
            sortable: true
          },
          {
            title: '技术需求PD',
            key: 'tecDevPd',
            // width: 140,
            sortable: true
          },
          {
            title: '产品需求PD',
            key: 'reqDevPd',
            // width: 140,
            sortable: true
          },
          {
            title: 'bug修复PD',
            key: 'debugPd',
            // width: 140,
            sortable: true
          },
          {
            title: '总PD',
            key: 'totalPd',
            // width: 140,
            sortable: true
          }
        ]
      }
    },
    methods: {
      processData (data) {
        if (data) {
          let metricsStats = []
          let rawData = data['rdEfficiency']
          for (let groupRaw in rawData) {
            metricsStats.push(rawData[groupRaw])
          }
          this.$Spin.hide()
          // Bus.$emit('refreshExportWiki')
          Bus.efficiencyWiki = []
          Bus.efficiencyWiki = rawData
          console.log('导出的详细数据', Bus.efficiencyWiki)
          return metricsStats
        } else {
          return []
        }
      },
      getinitdata (data, page) {
        if (data) {
          let displaydata = []
          if (page < data.length) {
            for (let i = 0; i < page; i += 1) {
              displaydata.push(data[i])
            }
          } else {
            for (let i = 0; i < data.length; i += 1) {
              displaydata.push(data[i])
            }
          }
          return displaydata
        }
      },
      changePage (page) {
        const self = this
        if (page !== this.currentPage) {
          self.currentPage = page
          self.changeCurrentPage(self.backupdata)
        }
      },
      changeCurrentPage (data) {
        const currentPage = this.currentPage
        const pageSize = this.pagesize
        this.rdeffectDetail = []
        const count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i += 1) {
          this.rdeffectDetail.push(data[i])
        }
      }
    },
    mounted: function () {
      Bus.rdEfficiencyDetailOnesObject = this
      // console.log('begin', Bus.qualityMetricsUnitObject)
    }
  }
</script>

<style scoped>
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
