<template>
  <div>
    <head-component></head-component>
    <div>
      <Card :bordered="true" :style="noticeStyle" >
        <Row type="flex">
          <Col span="24">
          <Row style="display: flex">
            <div style="margin-top: 6px">
              <Icon type="ios-list-box-outline" size="20"/>
            </div>
            <span :style="{'fontWeight':'bolder'}"><a :href="reqDetailData.basicInfo.reqUrl" target="_blank"><span
              style="margin-left: 5px;font-size: 24px">{{req + '-' + reqDetailData.basicInfo.reqName}}</span></a></span>
            <div style="margin-left: 10px;margin-top: 5px;">
              <tag color="default">{{reqDetailData.basicInfo.status}}</tag>
            </div>
          </Row>
          <Row style="display: flex;margin-top: 2px;">
            <span style="font-size: 10px;font-weight: bolder;margin-top: 5px;margin-right: 3px">PM:</span>
            <div v-if="reqDetailData.basicInfo.pm">
              <Tag  color="primary">{{reqDetailData.basicInfo.pm}}</Tag>
            </div>
            <div v-if="!reqDetailData.basicInfo.pm"><Tag color="error">无</Tag></div>
            <span style="margin-left: 8px;font-size: 10px;font-weight: bolder;margin-top: 5px;margin-right: 3px">QA:</span>
            <div v-if="reqDetailData.basicInfo.qa.length !== 0" v-for="item in reqDetailData.basicInfo.qa" style="display: flex">
              <Tag color="warning">{{item}}</Tag>
            </div>
            <div v-if="reqDetailData.basicInfo.qa.length === 0"><Tag color="error">无</Tag></div>
            <span style="margin-left: 8px;font-size: 10px;font-weight: bolder;margin-top: 5px;margin-right: 3px" >RD:</span>
            <div v-if="reqDetailData.basicInfo.rd.length > 0 && reqDetailData.basicInfo.rd.length < 12" v-for="item in reqDetailData.basicInfo.rd" style="display: flex;">
              <Tag color="success">{{item}}</Tag>
            </div>
            <div v-if="reqDetailData.basicInfo.rd.length >= 12" v-for="(item, index) in reqDetailData.basicInfo.rd" style="display: flex">
              <Tag color="success" v-if="index < 12">{{item}}</Tag>
            </div>
            <div v-if="reqDetailData.basicInfo.rd.length === 0"><Tag color="error">无</Tag></div>
          </Row>
          <Row style="display: flex;margin-left: 25px">
            <div v-if="reqDetailData.basicInfo.rd.length >= 12" v-for="(item, index) in reqDetailData.basicInfo.rd" style="display: flex;">
              <Tag color="success" v-if="index >= 12">{{item}}</Tag>
            </div>
          </Row>
          </Col>
        </Row>
      </Card>
      <Card :bordered="true" :style="cardStyle">
        <Timeline style="margin-top: 15px; margin-left: 15px; margin-right: 15px">
          <TimelineItem id="schedule">
            <h4 style="margin-top: -2px"> 一、 排期情况</h4>
            <Row>
              <Col span="16">
              <div id="schedulexraychart" style="padding-top:10px; margin-right:5px;max-height: 500px;"></div>
              </Col>
            </Row>
          </TimelineItem>
          <TimelineItem id="source">
            <div class="time">
              <Divider style="margin-top: -25px; background-color: #dcdee2; box-shadow:0 1px 2px #dcdee2;"/>
              <Row>
                <Col span="24">
                <h4 style="margin-top: -2px"> 二、 过程度量</h4>
                <Row type="flex" class="bottomBorder">
                  <Col span="20">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px'}" dis-hover>
                    <div :style="{minHeight:'100px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        1.开发情况
                      </div>
                      <div style="font-size: 10px">
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:22%; font-weight: bolder;padding-top: 3px;">
                            需求涉及的开发task（已完成占比）
                          </div>
                          <div v-if="parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) > 0 && parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) < 1" class="leftChart" :style="{width: reqDetailData.reqProcessInfo.devTaskCompleteRate * 100 * 0.74 + '%'}">{{(parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) > 0 && parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) < 1" class="rightChart" :style="{width: (100 - reqDetailData.reqProcessInfo.devTaskCompleteRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{reqDetailData.reqProcessInfo.completeDevTaskCount}}</div>
                          </div>
                          <div v-if="parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(reqDetailData.reqProcessInfo.devTaskCompleteRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{reqDetailData.reqProcessInfo.devTaskCompleteRate * 100}}%
                          </div>
                          <div style="width: 8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{reqDetailData.reqProcessInfo.devTaskSize}}</div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:20%;font-weight: bolder;padding-top: 3px;">
                            task列表
                          </div>
                          <div style="width:80%;margin-top: 2px;margin-left: 0px;font-weight: bolder;font-size: 10px;">
                            <div v-if="reqDetailData.reqProcessInfo.relateTaskInfo.length !== 0">
                              <div v-for="item in reqDetailData.reqProcessInfo.relateTaskInfo">
                                <Col span="8">
                                  <a :href="item.url" target="_blank" style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;display:-webkit-box; -webkit-box-orient:vertical;padding-left: 10px;padding-right: 10px;padding-bottom: 8px">{{item.name}}</a>
                                </Col>
                              </div>
                            </div>
                            <div v-else>
                              无关联任务
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Row type="flex">
                    <Col span="24">
                    <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px'}" dis-hover>
                      <div :style="{minHeight:'100px'}">
                        <div
                          :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        </div>
                        <div style="font-size: 10px">
                          <div style="display: flex;margin-top: 15px">
                            <div style="width:55%; font-weight: bolder;padding-top: 3px;">
                              覆盖代码仓库数
                            </div>
                            <div>
                              <Tag color="success">{{reqDeliveryData.branchCount}}个</Tag>
                            </div>
                          </div>
                          <div style="display: flex;margin-top: 15px">
                            <div style="width:55%;font-weight: bolder;padding-top: 3px;">
                              涉及仓库分支
                            </div>
                            <div>
                              <Tag color="success">{{reqDeliveryData.repoCount}}个</Tag>
                            </div>
                          </div>
                          <div style="font-size: 10px">
                            <div style="display: flex;margin-top: 15px">
                              <div style="width:55%; font-weight: bolder;padding-top: 3px;">
                                变更代码行数
                              </div>
                              <div>
                                <Tag color="success">{{reqDeliveryData.codeLines}}</Tag>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                    </Col>
                  </Row>
                  </Col>
                </Row>
                <Row type="flex">
                  <Col span="24">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',}" dis-hover>
                    <div
                      :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                      2.提测情况
                    </div>
                    <div style="font-size: 10px;">
                      <Card style="border-width:0;margin-left:20px;margin-right:20px" dis-hover>
                        <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                          2.1 服务端提测
                        </div>
                        <div v-if="reqDetailData.reqProcessInfo.ticeInfo.beTiceList.length !== 0" style="min-height: 80px">
                          <div v-for="item in reqDetailData.reqProcessInfo.ticeInfo.beTiceList">
                            <Col span="6">
                              <Card :bordered="false" dis-hover>
                                <div :style="{minHeight:'80px',maxHeight:'80px'}">
                                  <div
                                    :style="{fontWeight: 'bolder',fontSize: 15,borderBottom:'1px solid #e9eaec',marginTop:'-12px'}">
                                    <a :href="item.url" target="_blank">{{item.name}}</a>
                                    <Tag style="margin-top: 2px;margin-left: 8px" color="success">{{item.status}}</Tag>
                                  </div>
                                  <Row type="flex" style="margin-top: 10px">
                                    <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                                    <span style="font-weight: bolder;font-size: 10px">QA:</span><span
                                    style="font-weight: bolder;font-size: 10px;margin-left: 5px"> {{item.qa}}</span>
                                    <!--<div style="margin-left: 10px;margin-top: -5px">-->
                                      <!--<Tag color="error">提测delay</Tag>-->
                                    <!--</div>-->
                                  </Row>
                                  <!--<Row type="flex" style="margin-top: 8px">-->
                                    <!--<div style="word-break: break-all">-->
                                      <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
                                      <!--<span style="font-weight: bolder;font-size: 10px">提测delay原因:</span><span style="font-weight: bolder;font-size: 10px;margin-left: 5px">暂无1231432432432148dnwiafknewjdwojdiwojdojiowjdioqlj</span>-->
                                    <!--</div>-->
                                  <!--</Row>-->
                                  <Row type="flex" style="margin-top: 8px">
                                    <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                                    <span style="font-weight: bolder;font-size: 10px;">提测次数:</span><span
                                    style="font-weight: bolder;font-size: 10px;margin-left: 5px">{{item.sum}}</span>
                                    <span style="font-weight: bolder;font-size: 10px;margin-left: 10px">有效打回次数:</span><span
                                    style="font-weight: bolder;font-size: 10px;margin-left: 5px">{{item.validError}}</span>
                                  </Row>
                                </div>
                              </Card>
                            </Col>
                          </div>
                        </div>
                        <div v-else style="height: 80px">
                          暂无提测
                        </div>
                      </Card>
                      <Card :style="{borderWidth:0,marginLeft:'20px',marginRight:'20px',marginTop: '10px'}" dis-hover>
                        <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                          2.2 前端提测
                        </div>
                        <div v-if="reqDetailData.reqProcessInfo.ticeInfo.feTiceList.length !== 0" style="min-height: 60px">
                          <div v-for="item in reqDetailData.reqProcessInfo.ticeInfo.feTiceList">
                            <Col span="6">
                              <Card :bordered="false" dis-hover>
                                <div :style="{minHeight:'60px',maxHeight:'60px'}">
                                  <div
                                    :style="{fontWeight: 'bolder',fontSize: 15,borderBottom:'1px solid #e9eaec',marginTop:'-12px'}">
                                    <a :href="item.url" target="_blank">{{item.name}}</a>
                                    <Tag style="margin-top: 2px;margin-left: 8px" color="success">{{item.status}}</Tag>
                                  </div>
                                  <Row type="flex" style="margin-top: 10px">
                                    <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                                    <span style="font-weight: bolder;font-size: 10px">QA:</span><span
                                    style="font-weight: bolder;font-size: 10px;margin-left: 5px"> {{item.qa}}</span>
                                    <!--<div style="margin-left: 10px;margin-top: -5px">-->
                                    <!--<Tag color="error">提测delay</Tag>-->
                                    <!--</div>-->
                                  </Row>
                                  <!--<Row type="flex" style="margin-top: 8px">-->
                                  <!--<div style="word-break: break-all">-->
                                  <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
                                  <!--<span style="font-weight: bolder;font-size: 10px">提测delay原因:</span><span style="font-weight: bolder;font-size: 10px;margin-left: 5px">暂无1231432432432148dnwiafknewjdwojdiwojdojiowjdioqlj</span>-->
                                  <!--</div>-->
                                  <!--</Row>-->
                                </div>
                              </Card>
                            </Col>
                          </div>
                        </div>
                        <div v-else style="min-height: 60px">
                          暂无提测
                        </div>
                      </Card>
                      <Card :style="{borderWidth:0,marginLeft:'20px',marginRight:'20px',}" dis-hover>
                        <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                          2.3 客户端提测
                        </div>
                        <div v-if="reqDetailData.reqProcessInfo.ticeInfo.clientTiceList.length !== 0" style="min-height: 60px">
                          <div v-for="item in reqDetailData.reqProcessInfo.ticeInfo.clientTiceList">
                            <Col span="6">
                              <Card :bordered="false" dis-hover>
                                <div :style="{minHeight:'60px',maxHeight:'60px'}">
                                  <div
                                    :style="{fontWeight: 'bolder',fontSize: 15,borderBottom:'1px solid #e9eaec',marginTop:'-12px'}">
                                    <a :href="item.url" target="_blank">{{item.name}}</a>
                                    <Tag style="margin-top: 2px;margin-left: 8px" color="success">{{item.status}}</Tag>
                                  </div>
                                  <Row type="flex" style="margin-top: 10px">
                                    <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                                    <span style="font-weight: bolder;font-size: 10px">QA:</span><span
                                    style="font-weight: bolder;font-size: 10px;margin-left: 5px"> {{item.qa}}</span>
                                    <!--<div style="margin-left: 10px;margin-top: -5px">-->
                                      <!--<Tag color="error">提测delay</Tag>-->
                                    <!--</div>-->
                                  </Row>
                                  <!--<Row type="flex" style="margin-top: 8px">-->
                                    <!--<div style="word-break: break-all">-->
                                      <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
                                      <!--<span style="font-weight: bolder;font-size: 10px">提测delay原因:</span><span style="font-weight: bolder;font-size: 10px;margin-left: 5px">暂无1231432432432148dnwiafknewjdwojdiwojdojiowjdioqlj</span>-->
                                    <!--</div>-->
                                  <!--</Row>-->
                                </div>
                              </Card>
                            </Col>
                          </div>
                        </div>
                        <div v-else style="min-height: 60px">
                          暂无提测
                        </div>
                      </Card>
                    </div>
                  </Card>
                  </Col>
                </Row>
                <Row type="flex">
                  <Col span="24">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',minHeight:'200px'}" dis-hover>
                    <div>
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        3.资源消耗
                      </div>
                      <div style="font-size: 10px">
                        <!--<Row type="flex">-->
                          <!--<Col span="8">-->
                          <!--<Row type="flex" style="margin-left: 40%;margin-top:5px;margin-bottom: -5px">-->
                            <!--<span style="margin-top:-3px;margin-right: 8px">开发时长;</span>-->
                            <!--<div style="background-color: #8192D6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                            <!--</div>-->
                            <!--<span style="margin-top:-3px;margin-right: 8px">: 测试时长;</span>-->
                            <!--<div style="background-color: #20B2AA;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                            <!--</div>-->
                          <!--</Row>-->
                          <!--<Row>-->
                            <!--<div id="processxrangechart" style="padding-top:10px; margin-right:5px;max-height: 400px;"></div>-->
                          <!--</Row>-->
                          <!--</Col>-->
                          <!--<Col span="8">-->
                          <!--<Card :bordered="false" dis-hover>-->
                            <!--<div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">-->
                              <!--<div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >实际工时统计(PD)-->
                              <!--</div>-->
                              <!--<Row type="flex">-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:10%">开发工时:</span>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-01-20</span>-->
                                <!--<div class="scheduleactualdevChart" style="padding-left: 5px;padding-right: 5px;margin-top: 4px;width: 50%"><div style="margin-top:-6px;"><span style="font-size: 3px;font-weight: bold;color:white">20</span></div></div>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-02-20</span>-->
                              <!--</Row>-->
                              <!--<Row type="flex" style="margin-top: 20px">-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:10%">联调工时:</span>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-01-10</span>-->
                                <!--<div class="scheduleactualjointChart" style="padding-left: 5px;padding-right: 5px;margin-top: 4px;width: 50%"><div style="margin-top:-6px;"><span style="font-size: 3px;font-weight: bold;color:white">10.5</span></div></div>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-02-20</span>-->
                              <!--</Row>-->
                              <!--<Row type="flex" style="margin-top: 20px">-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:10%">测试工时:</span>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-01-10</span>-->
                                <!--<div class="scheduleactualtestChart" style="padding-left: 5px;padding-right: 5px;margin-top: 4px;width: 50%"><div style="margin-top:-6px;"><span style="font-size: 3px;font-weight:bold;color:white">5</span></div></div>-->
                                <!--<span style="font-weight: bolder;font-size: 10px;width:20%">2019-02-20</span>-->
                              <!--</Row>-->
                            <!--</div>-->
                          <!--</Card>-->
                          <!--</Col>-->
                        <Row type="flex" style="margin-top: 0px">
                          <Col span="5">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >计划上线时间
                                </div>
                                <div v-if="reqDetailData.reqProcessInfo.planReleaseTime" :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.planReleaseTime}}</div>
                                <div v-else><div style="margin-top: 20px;font-size: 10px;font-weight: bolder">暂无</div></div>
                              </div>
                            </Card>
                          </Col>
                          <Col span="5">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'75px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >实际上线时间
                                </div>
                                <div v-if="reqDetailData.reqProcessInfo.actualReleaseTime" :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.actualReleaseTime}}</div>
                                <div v-else><div style="margin-top: 20px;font-size: 10px;font-weight: bolder">暂无</div></div>
                                <Tag v-if="reqDetailData.reqProcessInfo.actualReleaseTime && reqDetailData.reqProcessInfo.planReleaseTime && comparetime(reqDetailData.reqProcessInfo.actualReleaseTime, reqDetailData.reqProcessInfo.planReleaseTime) === 1" color="error">延期上线</Tag>
                              </div>
                            </Card>
                          </Col>
                          <Col span="5">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >开发总工时
                                </div>
                                <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.devInfo.totalDevPd}}</div>
                              </div>
                            </Card>
                          </Col>
                          <Col span="5">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >联调总工时
                                </div>
                                <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.devInfo.totalDebugPd}}</div>
                              </div>
                            </Card>
                          </Col>
                          <Col span="4">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'75px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >测试总工时
                                </div>
                                <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.devInfo.totalTestPd}}</div>
                                <!--<Tag color="error">不符合预期测试周期</Tag>-->
                              </div>
                            </Card>
                          </Col>
                        </Row>
                          <!--</Col>-->
                        <!--</Row>-->
                        <Row type="flex">
                          <Col span="8">
                          <div id="devandtestperreqcolumn" style="padding-top:10px; margin-right:5px; max-height: 200px;min-height: 200px;"></div>
                          </Col>
                          <Col span="8">
                          <div id="devandjointdebugperreqpie" style="padding-top:10px; margin-right:5px; max-height: 200px;min-height: 200px;"></div>
                          </Col>
                          <Col span="8">
                          <Row type="flex" style="margin-top: 20px">
                            <Col span="12">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >开发联调比
                                </div>
                                <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.ticeStats.devDebugRate}}:1</div>
                              </div>
                            </Card>
                            </Col>
                            <Col span="12">
                            <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                              <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                                <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}" >测试人效
                                </div>
                                <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{reqDetailData.reqProcessInfo.ticeStats.testEfficiency}}:1</div>
                              </div>
                            </Card>
                            </Col>
                          </Row>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                </Col>
              </Row>
            </div>
          </TimelineItem>
          <TimelineItem id="quality">
            <div>
              <Divider style="margin-top: -20px; background-color: #dcdee2; box-shadow:0 1px 2px #dcdee2;"/>
              <Row>
                <Col span="24">
                <h4 style="margin-top: -2px"> 三、 交付质量</h4>
                <Row type="flex" style="margin-top: 10px;min-height: 480px">
                  <Col span="12">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover >
                    <div :style="{height: '96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        QA测试问题
                      </div>
                      <div>
                        <Row type="flex">
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                提测次数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{reqDeliveryData.tice_all_count}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                有效打回次数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{reqDeliveryData.tice_valid_back}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                有效bug数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{reqDetailData.reqDeliverInfo.validBugCount}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                有效bug占比
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{reqDetailData.reqDeliverInfo.totalValidBugRate}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                        </Row>
                        <Row type="flex" style="margin-top: 5px">
                          <Col span="12">
                          <div id="bugattributeperreqpie" style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                          <Col span="12">
                          <div id="effectivebugperdayperreqcolumn" style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="12">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover >
                    <div :style="{height: '96px'}">
                      <div :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        线上问题
                      </div>
                      <div>
                        <Row type="flex">
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                有效线上bug
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                线上故障数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                平均潜藏时长
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  <span style="color: #808695; font-size: 16px">h</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                平均解决时长
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  <span style="color: #808695; font-size: 16px">h</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                        </Row>
                        <Row type="flex" style="margin-top: 5px">
                          <Col span="12">
                          <div id="solvetimeperreqratiopie" style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                          <Col span="12">
                          <div id="hiddentimeperreqratiopie" style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                <Row type="flex" class="bottomBorder">
                  <Col span="4">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        发布次数
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{reqDeliveryData.deploy}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        回滚次数
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{reqDeliveryData.rollback}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        线上发布回滚率
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{reqDeliveryData.deploy !== 0 ? (reqDeliveryData.rollback / reqDeliveryData.deploy * 100).toFixed(2) : 0}}
                          <span style="color: #808695; font-size: 16px">%</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        hotfix合入master数量
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{reqDeliveryData.hotfix_merge}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        hotfix跳过数量
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{reqDeliveryData.hotfix_deploy}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                </Col>
              </Row>
            </div>
          </TimelineItem>
        </Timeline>
      </Card>
    </div>
  </div>
</template>
<script>
  import Vue from 'vue'
  import axios from 'axios'
  import Head from '@/components/Common/Head'
  import {Bus} from '@/global/bus'
  import Highcharts from 'highcharts/highstock'
  import Xrange from 'highcharts/modules/xrange'
  import HighchartsMore from 'highcharts/highcharts-more'
  import {analyticsbaseAPI} from '@/global/variable'

  Xrange(Highcharts)
  HighchartsMore(Highcharts)

  let screeHeight = window.screen.height

  let noticeStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '30px'
  }

  let cardStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '15px',
    'minHeight': (screeHeight - 325).toString() + 'px',
    'marginBottom': '20px'
  }

  // let cardTitleStyle = {
  //   'minHeight': (screeHeight - 415).toString() + 'px',
  //   'overflowY': 'auto'
  // }

  Vue.component('head-component', Head)

  export default {
    name: 'requirement-detail',
    data: function () {
      return {
        req: this.$route.params.reqid,
        noticeStyle: noticeStyle,
        cardStyle: cardStyle,
        scheduledirectionlist: ['前端', '客户端', '后端'],
        scheduletime: [],
        processmetricslist: ['计划时间', '实际时间'],
        processmetricsdata: [],
        reqDetailData: this.getReqData(),
        reqDeliveryData: this.getDeliveryData(),
        devjointratioseries: [],
        sourceconsumeseries: [],
        validbugperdayseries: [],
        validbugattributeseries: [],
        scheduleseries: []
      }
    },
    methods: {
      getReqData: function () {
        let data = {
          basicInfo: {
            reqName: '',
            reqUrl: '',
            status: '',
            qa: [],
            rd: [],
            pm: ''
          },
          reqDeliverInfo: {
            beAverageBugRate: '0.00',
            beBugCount: 0,
            beBugRate: '0.00',
            beValidBugRate: '0.00',
            clientAverageBugRate: '0.00',
            clientBugCount: 0,
            clientBugRate: '0.00',
            clientValidBugRate: '0.00',
            feAverageBugRate: '0.00',
            feBugCount: 0,
            feBugRate: '0.00',
            feValidBugRate: '0.00',
            otherBugCount: 0,
            otherBugRate: '0.00',
            productBugCount: 0,
            productBugRate: '0.00',
            totalAverageBugRate: '0.00',
            totalBugCount: 0,
            totalValidBugRate: '0.00',
            validBugCount: 0
          },
          reqProcessInfo: {
            actualReleaseTime: '',
            completeDevTaskCount: 0,
            devInfo: {
              totalDebugPd: 0,
              totalDevPd: 0,
              totalTestPd: 0
            },
            devStats: {
              androidDebugPD: 0,
              androidDevPD: 0,
              beDebugPD: 0,
              beDebugRate: '0.000',
              beDevPD: 0,
              beDevRate: '0.000',
              clientDebugPD: 0,
              clientDebugRate: '0.000',
              clientDevPD: 0,
              clientDevRate: '0.000',
              dataDebugPD: 0,
              dataDevPD: 0,
              feDebugPD: 0,
              feDebugRate: '0.000',
              feDevPD: 0,
              feDevRate: '0.000',
              hardwareDebugPD: 0,
              hardwareDevPD: 0,
              iOSDebugPD: 0,
              iOSDevPD: 0,
              totalDebugPD: 0,
              totalDevPD: 0
            },
            devTaskCompleteRate: '0.00',
            devTaskSize: 0,
            planReleaseTime: '',
            relateTaskInfo: [],
            ticeInfo: {
              beTiceList: [],
              clientTiceList: [],
              feTiceList: [],
              testPD: 0
            },
            ticeStats: {
              averageDevPd: '0.00',
              averageTestPd: '0.00',
              devDebugRate: '0.0',
              testEfficiency: '0.00'
            }
          },
          reqScheduleInfo: {
            beDevSchedule: [],
            clientDevSchedule: [],
            feDevSchedule: []
          }
        }
        return data
      },
      getDeliveryData: function () {
        let data = {
          repoCount: 0,
          branchCount: 0,
          codeLines: 0,
          tice_all_count: 0,
          tice_valid_back: 0,
          deploy: 0,
          rollback: 0,
          hotfix_deploy: 0,
          hotfix_merge: 0
        }
        return data
      },
      getonesinitdata: function () {
        let self = this
        axios.get(analyticsbaseAPI + '/detail/req/' + self.$route.params.reqid, {
          dataType: 'json'
        }).then(function (message) {
          if (message.data.status === 0) {
            let data = message.data.data
            console.log(data)
            self.reqDetailData = data
            self.handlechartdata(data)
          }
        }).catch(function (err) {
          console.log(err)
        })
      },
      getonesDeliveryData: function () {
        let self = this
        let params = {
          beginTime: this.$route.query.start,
          endTime: this.$route.query.end
        }
        axios.get(analyticsbaseAPI + '/detail/delivery/' + self.$route.params.reqid, {
          params: params,
          dataType: 'json'
        }).then(function (message) {
          if (message.data.status === 0) {
            let data = message.data.data
            self.reqDeliveryData = data
          }
        }).catch(function (err) {
          console.log(err)
        })
      },
      handlechartdata: function (data) {
        let self = this
        self.sourceconsumeseries = []
        self.sourceconsumeseries = [
          {
            name: '开发',
            data: [data.reqProcessInfo.devStats.feDevPD, data.reqProcessInfo.devStats.beDevPD, data.reqProcessInfo.devStats.clientDevPD, data.reqProcessInfo.devStats.totalDevPD]
          },
          {
            name: '联调',
            data: [data.reqProcessInfo.devStats.feDebugPD, data.reqProcessInfo.devStats.beDebugPD, data.reqProcessInfo.devStats.clientDebugPD, data.reqProcessInfo.devStats.totalDebugPD]
          },
          {
            name: '测试',
            data: [data.reqProcessInfo.devStats.feTestPD, data.reqProcessInfo.devStats.beTestPD, data.reqProcessInfo.devStats.clientTestPD, data.reqProcessInfo.devStats.totalTestPD]
          }
        ]
        self.devjointratioseries = []
        self.devjointratioseries = [
          {
            name: '后端开发时长',
            y: data.reqProcessInfo.devStats.beDevRate * 100
          },
          {
            name: '前端开发时长',
            y: data.reqProcessInfo.devStats.feDevRate * 100
          },
          {
            name: '客户端开发时长',
            y: data.reqProcessInfo.devStats.clientDevRate * 100
          },
          {
            name: '后端联调时长',
            y: data.reqProcessInfo.devStats.beDebugRate * 100
          },
          {
            name: '前端联调时长',
            y: data.reqProcessInfo.devStats.feDebugRate * 100
          },
          {
            name: '客户联调时长',
            y: data.reqProcessInfo.devStats.clientDebugRate * 100
          }
        ]
        self.validbugattributeseries = []
        self.validbugattributeseries = [
          {
            name: '后端',
            y: data.reqDeliverInfo.beBugRate * 100
          },
          {
            name: '前端',
            y: data.reqDeliverInfo.feBugRate * 100
          },
          {
            name: '客户端',
            y: data.reqDeliverInfo.clientBugRate * 100
          },
          {
            name: '产品',
            y: data.reqDeliverInfo.productBugRate * 100
          },
          {
            name: '其他',
            y: data.reqDeliverInfo.otherBugRate * 100
          }
        ]
        self.validbugperdayseries = []
        self.validbugperdayseries = [
          {
            name: '工时平均有效bug',
            data: [parseFloat(data.reqDeliverInfo.feAverageBugRate), parseFloat(data.reqDeliverInfo.beAverageBugRate), parseFloat(data.reqDeliverInfo.clientAverageBugRate), parseFloat(data.reqDeliverInfo.totalAverageBugRate)]
          },
          {
            name: '有效bug占比',
            data: [parseFloat(data.reqDeliverInfo.feValidBugRate), parseFloat(data.reqDeliverInfo.beValidBugRate), parseFloat(data.reqDeliverInfo.clientValidBugRate), parseFloat(data.reqDeliverInfo.totalValidBugRate)]
          }
        ]
        self.scheduleseries = []
        if (data.reqScheduleInfo.beDevSchedule.length !== 0) {
          for (let eachbe in data.reqScheduleInfo.beDevSchedule) {
            if (data.reqScheduleInfo.beDevSchedule[eachbe].expectStartTime && data.reqScheduleInfo.beDevSchedule[eachbe].expectEndTime) {
              let start = this.splittime(data.reqScheduleInfo.beDevSchedule[eachbe].expectStartTime)
              let end = this.splittime(data.reqScheduleInfo.beDevSchedule[eachbe].expectEndTime)
              let tempseries = {
                x: Date.UTC(start[0], start[1] - 1, start[2]),
                x2: Date.UTC(end[0], end[1] - 1, end[2]),
                color: '#8192D6',
                name: data.reqScheduleInfo.beDevSchedule[eachbe].devTaskName,
                starttime: this.handlexAxis(Date.UTC(start[0], start[1] - 1, start[2])),
                endtime: this.handlexAxis(Date.UTC(end[0], end[1] - 1, end[2])),
                y: 0
              }
              self.scheduleseries.push(tempseries)
            }
          }
        }
        if (data.reqScheduleInfo.clientDevSchedule.length !== 0) {
          for (let eachclient in data.reqScheduleInfo.clientDevSchedule) {
            if (data.reqScheduleInfo.clientDevSchedule[eachclient].expectStartTime && data.reqScheduleInfo.clientDevSchedule[eachclient].expectEndTime) {
              let start = this.splittime(data.reqScheduleInfo.clientDevSchedule[eachclient].expectStartTime)
              let end = this.splittime(data.reqScheduleInfo.clientDevSchedule[eachclient].expectEndTime)
              let tempseries = {
                x: Date.UTC(start[0], start[1] - 1, start[2]),
                x2: Date.UTC(end[0], end[1] - 1, end[2]),
                name: data.reqScheduleInfo.clientDevSchedule[eachclient].devTaskName,
                color: '#20B2AA',
                starttime: this.handlexAxis(Date.UTC(start[0], start[1] - 1, start[2])),
                endtime: this.handlexAxis(Date.UTC(end[0], end[1] - 1, end[2])),
                y: 1
              }
              self.scheduleseries.push(tempseries)
            }
          }
        }
        if (data.reqScheduleInfo.feDevSchedule.length !== 0) {
          for (let eachfe in data.reqScheduleInfo.feDevSchedule) {
            if (data.reqScheduleInfo.feDevSchedule[eachfe].expectStartTime && data.reqScheduleInfo.feDevSchedule[eachfe].expectEndTime) {
              let start = this.splittime(data.reqScheduleInfo.feDevSchedule[eachfe].expectStartTime)
              let end = this.splittime(data.reqScheduleInfo.feDevSchedule[eachfe].expectEndTime)
              let tempseries = {
                x: Date.UTC(start[0], start[1] - 1, start[2]),
                x2: Date.UTC(end[0], end[1] - 1, end[2]),
                color: '#FFA500',
                name: data.reqScheduleInfo.feDevSchedule[eachfe].devTaskName,
                starttime: this.handlexAxis(Date.UTC(start[0], start[1] - 1, start[2])),
                endtime: this.handlexAxis(Date.UTC(end[0], end[1] - 1, end[2])),
                y: 2
              }
              self.scheduleseries.push(tempseries)
            }
          }
        }
        setTimeout(function () {
          self.devtestratiochart()
          self.devjointdebugchart()
          self.bugattributchart()
          self.effectivebugperdaychart()
          self.schedulechart()
        }, 0)
      },
      handlexAxis (time) {
        var date = new Date(time)
        return date.getFullYear() + '-' + parseInt(date.getMonth() + 1) + '-' + date.getDate()
      },
      splittime: function (data) {
        let result = data.split('-')
        return result
      },
      comparetime: function (plantime, actualtime) {
        let start = []
        let end = []
        let result = -3
        if (plantime !== '' && actualtime !== '') {
          start = plantime.split('-')
          var startdate = new Date(start[0], parseInt(start[1] - 1), start[2])
          end = actualtime.split('-')
          var enddate = new Date(end[0], parseInt(end[1] - 1), end[2])
          if (startdate > enddate) {
            result = 1
          } else if (startdate < enddate) {
            result = -1
          } else {
            result = 0
          }
        }
        if (!actualtime) {
          result = -2
        }
        return result
      },
      processmetricchart: function () {
        Highcharts.chart('processxrangechart', {
          chart: {
            type: 'xrange',
            height: 200
          },
          title: {
            text: ''
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '计划工时vs实际工时'
            },
            tickPixelInterval: '10000px',
            categories: this.processmetricslist,
            reversed: true
          },
          legend: {
            enabled: true
          },
          tooltip: {
            dateTimeLabelFormats: {
              day: '%Y-%m-%d'
            },
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th>{point.name}开始 :&nbsp</th><td>{point.x}</td></tr>' +
            '<tr><th>{point.name}结束 :&nbsp</th><td>{point.x2}</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            xrange: {
              pointWidth: 1,
              groupPadding: 0
              // dataLabels: {
              //   enabled: true,
              //   format: '{point.key}'
              // },
              // events: {
              //   click: function (event) {
              //     window.open(event.point.url)
              //   }
              // }
            }
          },
          // colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '开发、测试开始、结束时间',
            pointWidth: 8,
            data: [{
              x: Date.UTC(2019, 10, 21),
              x2: Date.UTC(2019, 11, 2),
              color: '#8192D6',
              name: '开发',
              y: 0
            }, {
              x: Date.UTC(2019, 11, 21),
              x2: Date.UTC(2019, 12, 2),
              color: '#20B2AA',
              name: '测试',
              y: 0
            }, {
              x: Date.UTC(2019, 9, 21),
              x2: Date.UTC(2019, 10, 2),
              color: '#8192D6',
              name: '开发',
              y: 1
            }, {
              x: Date.UTC(2019, 10, 21),
              x2: Date.UTC(2019, 12, 25),
              color: '#20B2AA',
              name: '测试',
              y: 1
            }],
            dataLabels: {
              enabled: false
            }
          }],
          credits: {
            enabled: false
          }
        })
      },
      bugattributchart: function () {
        Highcharts.chart('bugattributeperreqpie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '有效bug归属方向占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: this.validbugattributeseries
          }],
          credits: {
            enabled: false
          }
        })
      },
      effectivebugperdaychart: function () {
        Highcharts.chart('effectivebugperdayperreqcolumn', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '各端工时平均有效bug'
          },
          xAxis: {
            categories: ['前端', '后端', '客户端', '整体']
          },
          yAxis: {
            min: 0,
            max: 1,
            title: {
              text: null
            },
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.validbugperdayseries,
          credits: {
            enabled: false
          }
        })
      },
      evesolvetimechart: function () {
        Highcharts.chart('solvetimeperreqratiopie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '线上问题解决时间占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: [{
              name: '1小时以内',
              y: 45
            }, {
              name: '大于1小时小于24小时',
              y: 15
            }, {
              name: '大于24小时小于1个月',
              y: 10
            }, {
              name: '大于1个月',
              y: 20
            }, {
              name: '其他',
              y: 10
            }]
          }],
          credits: {
            enabled: false
          }
        })
      },
      evehiddentimechart: function () {
        Highcharts.chart('hiddentimeperreqratiopie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '线上问题隐藏时间占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: [{
              name: '24小时以内',
              y: 45
            }, {
              name: '大于24小时小于1个月',
              y: 15
            }, {
              name: '大于1个月小于3个月',
              y: 10
            }, {
              name: '大于3个月',
              y: 20
            }, {
              name: '其他',
              y: 10
            }]
          }],
          credits: {
            enabled: false
          }
        })
      },
      devtestratiochart: function () {
        Highcharts.chart('devandtestperreqcolumn', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '各端开发测试资源消耗'
          },
          xAxis: {
            categories: ['前端', '后端', '客户端', '整体']
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.sourceconsumeseries,
          credits: {
            enabled: false
          }
        })
      },
      devjointdebugchart: function () {
        Highcharts.chart('devandjointdebugperreqpie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '各端开发联调占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: this.devjointratioseries
          }],
          credits: {
            enabled: false
          }
        })
      },
      schedulechart: function () {
        Highcharts.chart('schedulexraychart', {
          chart: {
            type: 'xrange',
            height: 200
          },
          title: {
            text: ''
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '各端排期情况'
            },
            categories: this.scheduledirectionlist,
            reversed: true
          },
          legend: {
            enabled: true
          },
          tooltip: {
            dateTimeLabelFormats: {
              day: '%Y-%m-%d'
            },
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{}</th></tr>' +
            '<tr><th>{point.name} :&nbsp</th><td>{point.starttime}~{point.endtime}</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          series: [{
            name: '各端开始开发排期',
            pointWidth: 8,
            data: this.scheduleseries,
            dataLabels: {
              enabled: false
            }
          }],
          credits: {
            enabled: false
          }
        })
      }
    },
    mounted: function () {
      Bus.requirementDetailUnitOnesObject = this
      Bus.requirementDetailUnitOnesObject.getonesDeliveryData()
      Bus.requirementDetailUnitOnesObject.getonesinitdata()
      // Bus.requirementDetailUnitOnesObject.schedulechart()
      // Bus.requirementDetailUnitOnesObject.devtestratiochart()
      // Bus.requirementDetailUnitOnesObject.devjointdebugchart()
      // Bus.requirementDetailUnitOnesObject.processmetricchart()
      // Bus.requirementDetailUnitOnesObject.bugattributchart()
      // Bus.requirementDetailUnitOnesObject.effectivebugperdaychart()
      // Bus.requirementDetailUnitOnesObject.evesolvetimechart()
      // Bus.requirementDetailUnitOnesObject.evehiddentimechart()
    }
  }
</script>

<style scoped>
  .leftChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .scheduleactualdevChart {
    background-color: rgb(129, 146, 214);
    height: 10px;
    border-top-left-radius: 3.5px;
    border-top-right-radius: 3.5px;
    border-bottom-left-radius: 3.5px;
    border-bottom-right-radius: 3.5px;
  }

  .scheduleactualjointChart {
    background-color: rgb(32, 178, 170);
    height: 10px;
    border-top-left-radius: 3.5px;
    border-top-right-radius: 3.5px;
    border-bottom-left-radius: 3.5px;
    border-bottom-right-radius: 3.5px;
  }

  .scheduleactualtestChart {
    background-color: #c581d6;
    height: 10px;
    border-top-left-radius: 3.5px;
    border-top-right-radius: 3.5px;
    border-bottom-left-radius: 3.5px;
    border-bottom-right-radius: 3.5px;
  }

  .allChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .rightChart {
    background-color: #f3f3f3;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .bottomBorder {
    border-bottom-style: dotted;
    border-bottom-color: #dcdee2;
    border-bottom-width: 1px;
  }
</style>
