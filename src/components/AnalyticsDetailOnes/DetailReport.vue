<template>
  <div>
    <head-component id="head"></head-component>
    <div>
      <Row span="24">
        <Col order="1" span="23">
          <Tabs :value="detail" style='margin-top: 30px;margin-left:50px;margin-right:20px;width: auto;'>
            <TabPane label="过程度量" name="process_metrics">
              <detail-process-report-ones></detail-process-report-ones>
            </TabPane>
            <!--<TabPane label="质量数据" name="quality_metrics">-->
              <!--<detailQualityReport></detailQualityReport>-->
            <!--</TabPane>-->
          </Tabs>
        </Col>
        <!--<Col v-if="displayexport" order="2" span="1" style="margin-top: 10px;">-->
          <!--<div>-->
            <!--<Button type="primary" @click="modal2 = true">导出质<br>量报告</Button>-->
            <!--<Modal-->
              <!--v-model="modal2" title="提示：" @on-ok="ok" @on-cancel="cancel1">-->
              <!--<p>是否导出数据到wiki？</p>-->
            <!--</Modal>-->
          <!--</div>-->
        <!--</Col>-->
      </Row>
    </div>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import { analyticswikiAPI, analyticsbaseonesAPI } from '@/global/variable'
  import Vue from 'vue'
  import Head from '@/components/Common/Head'
  import axios from 'axios'
  import DetailProcessReportOnes from './DetailProcessReport'
  Vue.component('head-component', Head)
  Bus.$on('refreshDefaultTabOnes', function () {
    Bus.DetailOnesObject.detail = 'quality_metrics'
    // console.log(Bus.DetailOnesObject)
  })
  Bus.$on('refreshsearchOnes', function () {
    Bus.DetailOnesObject.search = false
    // console.log('refresh search arrive', Bus.DetailOnesObject.search)
  })
  Bus.$on('refreshExportWikiOnes', function () {
    Bus.DetailOnesObject.displayexport = true
  })
  Bus.$on('refreshbackallowOnes', function () {
    Bus.DetailOnesObject.backallow = false
  })
  export default {
    components: {DetailProcessReportOnes},
    name: 'detailReport',
    data: function () {
      return {
        displayexport: false,
        modal2: false,
        display7: true,
        search: true,
        backallow: true,
        detail: 'process_metrics'
      }
    },
    methods: {
      changeExpectEarn (data) {
        if (!data) {
          return '无'
        }
        if (data === 'None') {
          return ''
        }
      },
      processData (rawdata) {
        // console.log(rawdata)
        if (rawdata) {
          let metricsStats = []
          for (let period in rawdata) {
            for (let task in rawdata[period]) {
              for (let eachrow in rawdata[period][task]) {
                rawdata[period][task][eachrow]['direction'] = task
                metricsStats.push(rawdata[period][task][eachrow])
              }
            }
          }
          this.$Spin.hide()
          return metricsStats
        } else {
          return []
        }
      },
      cancel1 () {
      },
      ok () {
        let self = this
        self.$Spin.show()
        let wikidata = {
          processRaw: Bus.processWiki,
          qualityView: Bus.qualityWiki,
          period: Bus.period,
          direction: Bus.direction
        }

        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/exportreport',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    created: function () {
      // console.log('create', this.search)
    },
    mounted: function () {
      Bus.DetailOnesObject = this
      const self = this
      Bus.DetailOnesObject.$Spin.show()
      let params = {
        name: this.$route.query.name,
        period: this.$route.query.period,
        searchtype: this.$route.query.searchtype,
        app: this.$route.query.app,
        version: this.$route.query.version,
        platform: this.$route.query.platform
      }
      // axios.get(analyticsbaseAPI + '/overview/tice/ones/detail', {
      axios.get(analyticsbaseonesAPI + '/analytics/overview/tice/ones/detail', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          // console.log(message)
          // console.log('保存的查询条件', message)
          // router.push({ path: '/detail' })
          // Bus.$emit('refreshDefaultTab')
          Bus.detailData = []
          Bus.detailData = message['data']['data']
          // console.log(Bus.detailData)
          Bus.$emit('refreshprocessDetailDataOnes', Bus.detailData)
          Bus.$emit('refreshqualityDetailDataOnes', Bus.detailData)
          // console.log('Bus-detailData', Bus.detailData)
        } else {
          self.$Spin.hide()
          alert(message['data']['msg'])
        }
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
      // }
    }
  }
</script>

<style scoped>

</style>
