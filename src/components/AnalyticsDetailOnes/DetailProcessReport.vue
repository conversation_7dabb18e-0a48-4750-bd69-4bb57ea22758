<template>
  <div>
    <h4 style="text-align:center">过程度量详细数据信息({{message}})</h4>
    <Checkbox-group v-model="tableColumnsChecked" @on-change="changeTableColumns">
      <Checkbox label="beDevPd">后端开发PD</Checkbox>
      <Checkbox label="feDevPd">前端开发PD</Checkbox>
      <Checkbox label="clientDevPd">客户端开发PD</Checkbox>
      <Checkbox label="beJointDebugPd">联调PD</Checkbox>
      <Checkbox label="totalDevPd">总开发PD</Checkbox>
      <Checkbox label="devTestRate">开发测试比</Checkbox>
      <Checkbox label="actualTestPd">实际测试PD</Checkbox>
      <Checkbox label="actualTransferTime">实际提测时间</Checkbox>
      <Checkbox label="actualTestFinishTime">实际测完时间</Checkbox>
      <Checkbox label="beValidBugCnt">后端有效Bug</Checkbox>
      <Checkbox label="feValidBugCnt">前端有效Bug</Checkbox>
      <Checkbox label="clientValidBugCnt">客户端有效Bug</Checkbox>
      <Checkbox label="pmValidBugCnt">产品有效Bug</Checkbox>
      <Checkbox label="totalCnt">问题总数</Checkbox>
      <Checkbox label="totalBugCnt">有效问题数</Checkbox>
      <Checkbox label="validBugCnt">有效Bug总数</Checkbox>
      <Checkbox label="averageBugPerDay">总Bug率</Checkbox>
      <Checkbox label="averageBeBugPerDay">后端Bug率</Checkbox>
      <Checkbox label="averageFeBugPerDay">前端Bug率</Checkbox>
      <Checkbox label="averageClientBugPerDay">客户端Bug率</Checkbox>
      <Checkbox label="changeBugRate">千行代码Bug率</Checkbox>
      <Checkbox label="autoTestBugRate">自动化发现问题占比</Checkbox>
      <Checkbox label="preRiskRate">风险前置率</Checkbox>
      <Checkbox label="transferDelayReason">提测delay原因</Checkbox>
      <Checkbox label="validBugRate">有效Bug率</Checkbox>
      <Checkbox label="beBugRate">后端Bug占比</Checkbox>
      <Checkbox label="feBugRate">前端Bug占比</Checkbox>
      <Checkbox label="clientBugRate">客户端Bug占比</Checkbox>
      <Checkbox label="pmBugRate">产品Bug占比</Checkbox>
      <Checkbox label="otherBugRate">其他Bug占比</Checkbox>
    </Checkbox-group>

    <Col style="text-align: right; margin-bottom: 1%; margin-right: 1%">
      <!--<Button type="primary" @click="createWiki()">导出wiki</Button>-->
    </Col>
    <Table stripe border :columns="columns4" :data="processRawData"></Table>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import axios from 'axios'
  import { analyticswikiAPI } from '@/global/variable'
  Bus.$on('refreshprocessDetailDataOnes', function (data) {
    // console.log('start', data)
    Bus.processDetailOnesObject.processRawData = Bus.processDetailOnesObject.processData(data)
  })
  export default {
    name: 'detailProcessReportOnes',
    data: function () {
      return {
        message: '全部',
        taskurl: [],
        columns4: [],
        processRawData: [],
        tableColumnsChecked: ['direction', 'taskSummary', 'business', 'beJointDebugPd', 'totalDevPd', 'actualTestPd', 'actualTransferTime', 'actualTestFinishTime', 'validBugCnt', 'devTestRate', 'averageBugPerDay', 'preRiskRate', 'changeBugRate', 'autoTestBugRate']
      }
    },
    methods: {
      changeExpectEarn (data) {
        if (!data) {
          return '无'
        } else {
          return '有'
        }
      },
      getmessage () {
        if (this.$route.query.reqtype === '1') {
          this.message = '产品'
        }
        if (this.$route.query.reqtype === '2') {
          this.message = '技术'
        }
      },
      processData (data) {
        if (data) {
          let metricsStats = []
          let rawData = data['processRaw']

          for (let groupRaw in rawData) {
            for (let eachTask in rawData[groupRaw]) {
              rawData[groupRaw][eachTask]['direction'] = groupRaw
              metricsStats.push(rawData[groupRaw][eachTask])
              // console.log('metricsStats', metricsStats)
            }
          }
          this.$Spin.hide()
          Bus.$emit('refreshExportWikiOnes')
          Bus.processWiki = []
          Bus.processWiki = rawData
          // console.log('导出的详细数据', Bus.processWiki)
          return metricsStats
        } else {
          return []
        }
      },
      createWiki () {
        let self = this
        self.$Spin.show()
        let wikidata = Bus.processdetaildata
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/createprocess',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      },
      changeTableColumns () {
        this.columns4 = this.getTable2Columns()
      },
      getTable2Columns () {
        const table2ColumnList = {
          direction: {
            title: '方向',
            key: 'direction',
            sortable: true
          },
          taskSummary: {
            title: '提测名称',
            key: 'taskSummary',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['taskUrl']
                  },
                  on: {
                    click: () => {
                      window.open(params.row['taskUrl'])
                    }
                  }
                }, params.row['taskSummary'])
              ])
            }
          },
          business: {
            title: '业务方向',
            key: 'business',
            sortable: true
          },
          beDevPd: {
            title: '后端开发PD',
            key: 'beDevPd',
            sortable: true
          },
          feDevPd: {
            title: '前端开发PD',
            key: 'feDevPd',
            sortable: true
          },
          clientDevPd: {
            title: '客户端开发PD',
            key: 'clientDevPd'
          },
          beJointDebugPd: {
            title: '联调PD',
            key: 'beJointDebugPd',
            sortable: true
          },
          totalDevPd: {
            title: '总开发PD',
            key: 'totalDevPd',
            sortable: true
          },
          actualTestPd: {
            title: '实际测试PD',
            key: 'actualTestPd',
            sortable: true
          },
          devTestRate: {
            title: '开发测试比',
            key: 'devTestRate',
            sortable: true
          },
          actualTransferTime: {
            title: '实际提测时间',
            key: 'actualTransferTime',
            sortable: true
          },
          actualTestFinishTime: {
            title: '实际测完时间',
            key: 'actualTestFinishTime',
            sortable: true
          },
          beValidBugCnt: {
            title: '后端有效Bug',
            key: 'beValidBugCnt',
            sortable: true
          },
          feValidBugCnt: {
            title: '前端有效Bug',
            key: 'feValidBugCnt',
            sortable: true
          },
          clientValidBugCnt: {
            title: '客户端有效Bug',
            key: 'clientValidBugCnt',
            sortable: true
          },
          pmValidBugCnt: {
            title: '产品有效Bug',
            key: 'pmValidBugCnt',
            sortable: true
          },
          totalCnt: {
            title: '问题总数',
            key: 'totalCnt',
            sortable: true
          },
          totalBugCnt: {
            title: '有效问题数',
            key: 'totalBugCnt',
            sortable: true
          },
          validBugCnt: {
            title: '有效Bug总数',
            key: 'validBugCnt',
            sortable: true
          },
          averageBugPerDay: {
            title: '总Bug率',
            key: 'averageBugPerDay',
            sortable: true
          },
          averageBeBugPerDay: {
            title: '后端Bug率',
            key: 'averageBeBugPerDay',
            sortable: true
          },
          averageFeBugPerDay: {
            title: '前端Bug率',
            key: 'averageFeBugPerDay',
            sortable: true
          },
          averageClientBugPerDay: {
            title: '客户端Bug率',
            key: 'averageClientBugPerDay',
            sortable: true
          },
          preRiskRate: {
            title: '风险前置率',
            key: 'preRiskRate',
            sortable: true
          },
          changeBugRate: {
            title: '千行代码Bug率',
            key: 'changeBugRate',
            sortable: true
          },
          autoTestBugRate: {
            title: '自动化发现问题占比',
            key: 'autoTestBugRate',
            sortable: true
          },
          transferDelayReason: {
            title: '提测delay原因',
            key: 'transferDelayReason',
            sortable: true
          },
          validBugRate: {
            title: '有效Bug率',
            key: 'validBugRate',
            sortable: true
          },
          beBugRate: {
            title: '后端Bug占比',
            key: 'beBugRate',
            sortable: true
          },
          feBugRate: {
            title: '前端Bug占比',
            key: 'feBugRate',
            sortable: true
          },
          clientBugRate: {
            title: '客户端Bug占比',
            key: 'clientBugRate',
            sortable: true
          },
          pmBugRate: {
            title: '产品Bug占比',
            key: 'pmBugRate',
            sortable: true
          },
          otherBugRate: {
            title: '产品Bug占比',
            key: 'otherBugRate',
            sortable: true
          }
        }
        let data = []
        this.tableColumnsChecked.forEach(col => data.push(table2ColumnList[col]))
        // console.log('tableColumnsDisplayed', data)
        return data
      }
    },
    mounted: function () {
      Bus.processDetailOnesObject = this
      this.changeTableColumns()
      this.getmessage()
    }
  }
</script>

<style scoped>

</style>
