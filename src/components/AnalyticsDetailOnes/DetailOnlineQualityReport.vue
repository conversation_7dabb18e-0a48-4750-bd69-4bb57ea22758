<template>
  <div>
    <h4 style="text-align:center;margin-top: 20px">线上Bug</h4>
    <div style="margin-left: 30px;margin-right: 30px;margin-top: 20px">
      <Table stripe border :columns="onlineQualityDetailColumns" :data="tableData"></Table>
    </div>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
  import { analyticsbaseonesAPI } from '@/global/variable'
  import axios from 'axios'

  Bus.$on('refreshOnlineQualityOnesDetail', function (data) {
    Bus.onlineQualityDetailOnesObject.tableData = Bus.onlineQualityDetailOnesObject.processOnesData(data)
    // console.log('线上质量详细数据', Bus.onlineQualityDetailOnesObject.tableData)
  })

  export default {
    name: 'detailOnlineQualityReport',
    data: function () {
      return {
        tableData: [],
        onlineQualityDetailColumns: [
          {
            title: '方向',
            key: 'directionName',
            // width: 20,
            sortable: true
          },
          {
            title: '问题名称',
            key: 'issueSummary',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: params.row['taskUrl']
                  },
                  on: {
                    click: () => {
                      window.open(params.row['taskUrl'])
                    }
                  }
                }, params.row['onesBugsModel'].name)
              ])
            }
          },
          {
            title: '状态',
            key: 'state',
            // width: 20,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].stateValue)
              ])
            }
          },
          {
            title: '总持续时间',
            key: 'totalDuration',
            // width: 20,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row.totalDuration)
              ])
            }
          },
          {
            title: '总隐藏时间',
            key: 'totalHideDuration',
            // width: 20,
            sortable: true
          },
          {
            title: '原因分类',
            key: 'bugReason',
            // width: 20,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].bugReason)
              ])
            },
            sortable: true
          },
          {
            title: '归属方向',
            key: 'bugBelong',
            // width: 20,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].bugBelong)
              ])
            },
            sortable: true
          },
          {
            title: '优先级',
            key: 'priority',
            // width: 20,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].bugPriority)
              ])
            },
            sortable: true
          },
          {
            title: '是否通过自动化发现',
            key: 'couldFindByAuto',
            // width: 20,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].couldFindByAutoTest)
              ])
            },
            sortable: true
          },
          {
            title: '发生时间',
            key: 'detectionTime',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].detectionTime)
              ])
            },
            // width: 20,
            sortable: true
          },
          {
            title: '发现时间',
            key: 'releasedTime',
            // width: 20,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].releaseTime)
              ])
            },
            sortable: true
          },
          {
            title: '解决时间',
            key: 'solvedTime',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].solveTime)
              ])
            },
            // width: 20,
            sortable: true
          },
          {
            title: 'rd负责人',
            key: 'rdInCharge',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].createdBy)
              ])
            },
            // width: 20,
            sortable: true
          },
          {
            title: 'qa负责人',
            key: 'qaInCharge',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['onesBugsModel'].assigned)
              ])
            },
            // width: 20,
            sortable: true
          }
        ]
      }
    },
    methods: {
      getStatusColor: function (status) {
        if (status === '未开始') {
          return 'red'
        }
      },
      processOnesData (data) {
        console.log(data)
        if (data) {
          let onlineQualityDetail = []
          for (let groupRaw in data) {
            onlineQualityDetail.push(data[groupRaw])
          }
          this.$Spin.hide()
          return onlineQualityDetail
        } else {
          return []
        }
      }
    },
    mounted: function () {
      Bus.onlineQualityDetailOnesObject = this
      Bus.onlineQualityDetailOnesObject.$Spin.show()
      const self = this

      let params = {
        name: this.$route.query.name,
        period: this.$route.query.period,
        searchtype: this.$route.query.searchtype,
        index: this.$route.query.index
      }
      // axios.get(analyticsbaseAPI + '/online/issuedetail/ones', {
      axios.get(analyticsbaseonesAPI + '/analytics/online/issuedetail/ones', {
        params: params,
        timeout: 50000000,
        dataType: 'json'
      }).then(function (message) {
        if (message['data']['status'] === 0) {
          Bus.detailData = []
          Bus.detailData = message['data']['data']
          Bus.$emit('refreshOnlineQualityOnesDetail', Bus.detailData)
        } else {
          self.$Spin.hide()
          alert(message['data']['msg'])
        }
      }).catch(error => {
        self.$Spin.hide()
        console.log(error)
      })
    }
  }
</script>

<style>
  /*.layout{*/
  /*!*border: 1px solid #d7dde4;*!*/
  /*position: relative;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
  /*border: 1px solid #d7dde4;*/
  /*margin-top: 100px;*/
  /*background: #f5f7f9;*/
  /*position: fixed;*/
  /*width:10px;*/
  /*border-radius: 4px;*/
  /*overflow: hidden;*/
  /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
  /*!*position: relative;*!*/
  /*margin-right: 0;*/
  /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
