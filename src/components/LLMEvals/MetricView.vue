<template>
    <div>
        <Layout>
            <Header style="background: #fff; border-bottom: 1px solid lightgray;">
                <Navigator @refresh="refresh">
                    <template v-slot:inline-section-place>
                        <el-date-picker class="date-picker" v-model="dateRange"
                            type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </template>
                </Navigator>
            </Header>
            <Layout>
            <Sider width="200px" :style="{background: '#fff'}">
                <Menu theme="light" v-for="task in taskConfigs" :active-name="activateName"
                    width="auto" @on-select="handleSelect" :key="task.title">
                    <MenuItem :name="task.title">
                        {{task.title}}
                    </MenuItem>
                </Menu>
            </Sider>
            <Content>
                <mtd-card v-loading="loading">
                    <template v-if="item !== null" #title >
                        {{ item.title }} &emsp;
                        <Button type="primary" icon="ios-contract" shape="circle" @click="goToFollowUp">去跟进</Button>
                    </template>
                    <el-row>
                        <el-col class="statics-title" :span=3>Task总量</el-col>
                        <el-col class="statics-title" :span=3>成功Task数</el-col>
                        <el-col class="statics-title" :span=4>Task成功率</el-col>
                        <el-col class="statics-title" :span=4>
                            Task-API耗时
                            <sub class="up-sub">
                            <el-tooltip content="每次 Task 中业务 API 的耗时秒数" placement="top">
                                <i class="el-icon-question"></i>
                            </el-tooltip>
                            </sub>
                        </el-col>
                        <el-col class="statics-title" :span=4>
                            Task-评测耗时
                            <sub class="up-sub">
                            <el-tooltip content="每次 Task 中 QA评测 的耗时秒数" placement="top">
                                <i class="el-icon-question"></i>
                            </el-tooltip>
                            </sub>
                        </el-col>
                        <el-col class="statics-title" :span="4">
                            Task总耗时
                            <sub class="up-sub">
                            <el-tooltip content="总耗时 = API+评测+(WIKI耗时+数据上报耗时+统计耗时+读取文件耗时)" placement="top">
                                <i class="el-icon-question"></i>
                            </el-tooltip>
                            </sub>
                        </el-col>
                        <!-- <el-col>跟进率</el-col> -->
                    </el-row>
                    <el-row v-if="item !== null">
                        <el-col :span="3" class="statics-info">{{ item.sumup['total'] }}<sub>次</sub></el-col>
                        <el-col :span="3" class="statics-success">{{ item.sumup['passed_num'] }}<sub>次</sub></el-col>
                        <el-col :span="4" class="statics-normal">{{ (item.sumup['passed_rate']*100).toFixed(2) }}<sub>%</sub></el-col>
                        <el-col :span="4" class="statics-warn">{{ (item.sumup['api_seconds_per_task']/60).toFixed(2) }}<sub>分钟/次</sub></el-col>
                        <el-col :span="4" class="statics-info">{{ (item.sumup['scorer_seconds_per_task']/60).toFixed(2) }}<sub>分钟/次</sub></el-col>
                        <el-col :span="4" class="statics-normal">{{ (item.sumup['during_seconds_per_task']/60).toFixed(2) }}<sub>分钟/次</sub></el-col>
                    </el-row>
                    <Divider/>
                    <el-row>
                        <el-col class="statics-title" :span=3>平均Case量</el-col>
                        <el-col class="statics-title" :span=3>评测计费</el-col>
                        <el-col class="statics-title" :span=4>平均计费</el-col>
                        <el-col class="statics-title" :span=4>API平均耗时</el-col>
                        <el-col class="statics-title" :span=4>评测平均耗时</el-col>
                        <el-col class="statics-title" :span="4">
                            用例平均耗时
                            <sub class="up-sub">
                            <el-tooltip content="用例耗时是 Task 总耗时除以 API 请求量" placement="top">
                                <i class="el-icon-question"></i>
                            </el-tooltip>
                            </sub>
                        </el-col>
                    </el-row>
                    <el-row v-if="item !== null">
                        <el-col :span="3" class="statics-info">{{ divide(item.sumup['api_count'],item.sumup['total']).toFixed(1) }}<sub>条/次</sub></el-col>
                        <el-col :span="3" class="statics-error">{{ item.sumup['total_cost'].toFixed(2) }}<sub>元</sub></el-col>
                        <el-col :span="4" class="statics-normal">{{ (item.sumup['mean_cost_per_api']*100).toFixed(2) }}<sub>元/百条</sub></el-col>
                        <el-col :span="4" class="statics-warn">{{ item.sumup['api_seconds_per_api'].toFixed(2) }}<sub>秒/条</sub></el-col>
                        <el-col :span="4" class="statics-info">{{ item.sumup['scorer_seconds_per_api'].toFixed(2) }}<sub>秒/条</sub></el-col>
                        <el-col :span="4" class="statics-normal">{{ item.sumup['during_seconds_per_api'].toFixed(2) }}<sub>秒/条</sub></el-col>
                    </el-row>
                    <Divider/>
                    <el-row>
                        <el-col :span="12">
                            <div ref="lineChart-task-count" style="width: 100%;height:400px;"></div>
                        </el-col>
                        <el-col :span="12">
                            <div ref="lineChart-task-time" style="width: 100%;height:400px;"></div>
                        </el-col>
                    </el-row>
                </mtd-card>
            </Content>
        </Layout>
        </Layout>

    </div>
</template>


<script>
import api from '../LLMEvals/api'
import Navigator from '../LLMEvals/Navigator.vue'
import * as echarts from 'echarts'

export default {
  components: {
    Navigator
  },
  data() {
    return {
      item: null,
      dateRange: [],
      activateName: '全量',
      taskConfigs: [{'title': '全量'}],
      loading: false,
      charts: []
    }
  },
  mounted() {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - 7)
    endDate.setDate(endDate.getDate() + 1)
    this.dateRange = [startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]
    window.addEventListener('resize', this.handleResize)
    this.refresh()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.destroyEcharts()
  },
  methods: {
    divide(a, b) {
      if (b === 0) {
        return 0
      }
      return a / b
    },
    refresh() {
      this.data = []
      this.fetchData()
    },
    goToFollowUp() {
      // 跳转逻辑
      let params = new URLSearchParams()
      params.append('startDate', this.dateRange[0])
      params.append('endDate', this.dateRange[1])
      for (const task of this.taskConfigs) {
        if (task.title !== this.activateName) {
          continue
        }
        if (task.workflow) { params.append('workflow', task.workflow) }
        if (task.taskNameSub) { params.append('taskName', task.taskNameSub) }
      }
      window.open('/evals/remote/tasklist?' + params.toString())
    },
    async fetchData() {
      this.loading = true
      let resp = await api.taskMetricConfig()
      this.taskConfigs = resp.data
      for (const task of this.taskConfigs) {
        if (task.title !== this.activateName) {
          continue
        }
        let item = await this.getData(task.title, task.workflow, task.taskNameSub)
        this.item = item
        this.loading = false
        this.destroyEcharts()
        this.charts.push(this.initLineChart(item.aggr, 'lineChart-task-count', ['total', 'passed_num'], 'Task数量曲线', '个'))
        this.charts.push(this.initLineChart(item.aggr, 'lineChart-task-time', ['during_seconds', 'scorer_seconds', 'api_seconds'], '耗时曲线', '秒/条'))
      }
    },
    async getData(title, workflow, taskNameSub) {
      try {
        const resp = await api.taskMetric(this.dateRange[0], this.dateRange[1], 'day', workflow, taskNameSub)
        let item = resp.data
        item['title'] = title
        this.data.push(resp.data)
        return item
      } catch (error) {
        this.exporting = false
        api.notifyError(this.$notify, '获取数据失败', error)
      }
    },
    handleSelect(value) {
      this.activateName = value
      this.fetchData()
    },
    destroyEcharts() {
      for (const chart of this.charts) {
        chart.dispose()
      }
      this.charts = []
    },
    initLineChart(aggr, refName, fields, title, sub) {
      const nameDict = {
        'total': 'task总数',
        'passed_num': '成功task数',
        'during_seconds': '用例耗时',
        'scorer_seconds': '评测耗时',
        'api_seconds': 'API耗时'
      }
      const getName = (name) => {
        if (nameDict[name]) {
          return nameDict[name]
        }
        return name
      }
      const chartDom = this.$refs[refName]
      let myChart
      if (Array.isArray(chartDom) && chartDom.length > 0) {
        myChart = echarts.init(chartDom[0])
      } else {
        myChart = echarts.init(chartDom)
      }

      let series = []
      let xAxis = []
      for (let field of fields) {
        series.push({
          name: getName(field),
          data: aggr[field].map(item => item['value']),
          type: 'line'
        })
      }
      for (let field of fields) {
        xAxis = aggr[field].map(item => item['key'])
        break
      }

      const option = {
        title: {
          text: title, // 图表标题
          left: 'center' // 标题位置
        },
        tooltip: {
          trigger: 'axis', // 触发类型：坐标轴触发
          formatter: function (params) {
            let tooltip = params[0].name + '<br/>'
            params.forEach(param => {
              tooltip += `${getName(param.seriesName)}: ${param.value.toFixed(3)} ${sub}<br/>`
            })
            return tooltip
          }
        },
        legend: {
          data: series.map(item => getName(item.name)), // 图例数据，从series中获取每个系列的name作为图例
          top: 'bottom' // 图例位置
        },
        xAxis: {
          type: 'category',
          data: xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: series
      }
      myChart.setOption(option)
      myChart.resize()
      return myChart
    },
    handleResize() {
      for (const chart of this.charts) {
        chart.resize()
      }
    }
  }
}
</script>

<style scoped>
.up-sub{
    vertical-align: super;
    font-size: smaller;
}
.date-picker{
    width: 100%;
}

.statics-title {
    font-size: 16px;
    color: #303133;
    font-weight: 700;
    text-align: left;
}

.statics-success, .statics-error, .statics-info, .statics-normal, .statics-warn {
    font-size: 30px;
    margin-top: 5px;
    font-weight: 700;
    text-align: left;
}

.statics-success {
    color: #67c23a;
}

.statics-error {
    color: #f56c6c;
}

.statics-info {
    color: #409eff;
}

.statics-normal {
    color: #303133;
}

.statics-warn {
    color: #e6a23c;
}

.statics-info sub, .statics-success sub, .statics-normal sub, .statics-warn sub, .statics-error sub {
    font-size: 0.5em; /* 调整下标字体大小 */
    vertical-align: baseline; /* 调整下标的垂直对齐方式 */
    position: relative;
    bottom: -0.25em; /* 调整下标的位置 */
}
</style>
