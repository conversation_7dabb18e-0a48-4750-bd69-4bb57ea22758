<template>
    <div>
      <Layout>
        <Header style="background: #fff; border-bottom: 1px solid lightgray;">
           <Navigator @refresh="refresh"></Navigator>
        </Header>
        <Content>
           <div class="container">
        <el-row class="task-list-ctrl">
            <el-col :span="6" class="task-ctrl">
                <el-date-picker class="date-picker" v-model="dateRange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"></el-date-picker>
            </el-col>
            <el-col :span="6" class="task-ctrl">
                <div class="el-input-group__prepend">
                    <span class="option-title">评测仓库</span>
                </div>
                <el-select class="table-cell-section" v-model="repoFilter" filterable allow-create clearable placeholder="请选评测仓库">
                    <el-option v-for="spec in repoList" :key="spec" :label="spec" :value="spec"></el-option>
                </el-select>
            </el-col>
            <el-col :span="6" class="task-ctrl">
                <div class="el-input-group__prepend">
                    <span class="option-title">仓库分支</span>
                </div>
                <el-select class="table-cell-section" v-model="branchFilter" filterable allow-create clearable placeholder="请选评测仓库分支">
                    <el-option v-for="spec in branchList" :key="spec" :label="spec" :value="spec"></el-option>
                </el-select>
            </el-col>
            <el-col :span="6" class="task-ctrl">
                <el-input v-model="WorkflowSearch" filterable allow-create clearable placeholder="评测workflow">
                    <template slot="prepend">
                        <span class="option-title">workflow</span>
                    </template>
                </el-input>
            </el-col>
        </el-row>
        <el-row class="task-list-ctrl">
            <el-col :span="6" class="task-ctrl">
                <el-input v-model="taskNameSearch" filterable allow-create clearable placeholder="任务名称">
                    <template slot="prepend">
                        <span class="option-title">任务名称</span>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="6" class="task-ctrl">
                <el-input v-model="personSearch" filterable allow-create clearable placeholder="人员">
                    <template slot="prepend">
                        <span class="option-title">评测人员</span>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="12" class="text-right">
                <el-button type="primary" @click="search_tasks" size="mini" class="mt-button"><i class="el-icon-search"></i> 搜索</el-button>
                <el-button type="primary" @click="showDialog" size="mini" class="mt-button"><i class="el-icon-setting"></i>表头</el-button>
                <el-button type="primary" @click="compareTasks" size="mini" class="mt-button"><i class='el-icon-sort'></i>对比</el-button>
            </el-col>
        </el-row>

        <el-table class="task-list-table" :data="taskList" border ref="taskTable" row-key="spec_id">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="spec_id" :label="headerMap['spec_id']" sortable>
                <template slot-scope="scope">
                    <a :href="`/evals/remote/task?task_id=${scope.row.spec_id}`" target="_blank">{{ scope.row.spec_id }}</a>
                </template>
            </el-table-column>

            <el-table-column v-for="header in displayedHeaders" :key="header" :prop="header"
                :width="getWidth(header)"
                :label="headerMap[header]" sortable>
                <template slot-scope="scope">
                    <a v-if="header=='mcd_task_id'" target="_blank" :href="`https://cd.sankuai.com/pipeline/result/detail?type=deploy&id=${scope.row[header]}&engineType=devtools`">{{ scope.row[header] }}</a>
                    <div v-else-if="header=='person'">
                        <el-tag v-for="p in scope.row['person']" size="mini" :key="p">{{ p }}</el-tag>
                    </div>
                    <div v-else>{{ scope.row[header] }}</div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            @current-change="handleCurrentChange">
        </el-pagination>
        <el-dialog title="配置表头" :visible.sync="dialogVisible" width="30%" :modal-append-to-body="false">
            <el-checkbox-group v-model="selectedHeaders">
                <el-checkbox v-for="header in headers" :key="header" :label="header">
                {{ headerMap[header] }}</el-checkbox>
            </el-checkbox-group>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">返回</el-button>
            </div>
        </el-dialog>
        </div>
        </Content>
      </Layout>


    </div>
</template>


<script>
import api from '../LLMEvals/api'
import Navigator from '../LLMEvals/Navigator.vue'

export default {
  components: {
    Navigator
  },
  data() {
    return {
      headerMap: {
        api_thread_num: 'API线程数',
        repo: '仓库',
        branch: '分支',
        data_limit: '数据限制',
        person: '人员',
        progress: '进度',
        running_mode: '运行模式',
        spec_id: 'ID',
        status: '状态',
        submit_time: '提交时间',
        during: '任务耗时(秒)',
        swimlane: 'swimlane',
        task_name: '任务名称',
        update_time: '更新时间',
        end_time: '结束时间',
        workflow: 'workflow 配置',
        mcd_task_id: 'mcd任务号'
      },
      taskList: [],
      dialogVisible: false,
      selectedHeaders: [],
      dateRange: [],
      branchList: [],
      repoList: [],
      repoFilter: null,
      branchFilter: null,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      WorkflowSearch: null,
      taskNameSearch: null,
      personSearch: null
    }
  },
  computed: {
    displayedHeaders() {
    //   return ['submit_time', 'spec_id', 'task_name', ...this.selectedHeaders]
      const baseHeaders = ['spec_id', 'task_name', 'workflow', 'during', 'mcd_task_id']
      const filteredSelectedHeaders = this.selectedHeaders.filter(header => !baseHeaders.includes(header))
      return [...['task_name', 'workflow', 'during', 'mcd_task_id'], ...filteredSelectedHeaders]
    },
    headers() {
      const baseHeaders = ['spec_id', 'task_name', 'workflow', 'during', 'mcd_task_id']
      return Object.keys(this.headerMap).filter(header => !baseHeaders.includes(header))
    }
  },
  watch: {
    repoFilter() {
      this.get_branch_list()
      this.search_tasks()
    },
    branchFilter() {
      this.search_tasks()
    }
  },
  mounted() {
  // 设置默认的日期范围为最近一周
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - 7)
    endDate.setDate(endDate.getDate() + 1)
    this.dateRange = [startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]
    if (this.$route.query.startDate) {
      this.dateRange[0] = this.$route.query.startDate
    }
    if (this.$route.query.endDate) {
      this.dateRange[1] = this.$route.query.endDate
    }
    if (this.$route.query.workflow) {
      this.WorkflowSearch = this.$route.query.workflow
    }
    if (this.$route.query.taskName) {
      this.taskNameSearch = this.$route.query.taskName
    }
    this.selectedHeaders = ['spec_id', 'task_name', 'workflow', 'person', 'status']
    this.refresh()
    this.get_repo_config()
  },
  methods: {
    getWidth(header) {
      if (header === 'person') {
        return 250
      } else if (header === 'during' || header === 'status') {
        return 90
      } else if (header === 'mcd_task_id') {
        return 120
      }
      return null
    },
    refresh() {
      this.search_tasks()
    },
    compareTasks() {
      const selectedRows = this.$refs.taskTable.selection
      if (selectedRows.length !== 2) {
        this.$message.error('请选择两个任务进行对比')
        return
      }
      window.open(`/evals/remote/task?task_id=${selectedRows[0].spec_id}&diff_task_id=${selectedRows[1].spec_id}`)
    },
    get_repo_config() {
      api.getWorkflowRepo().then((result) => {
        this.repoList = result.data
      })
    },
    get_branch_list() {
      api.getWorkflowBranch(this.repoFilter).then(result => {
        this.branchList = result.data
      })
    },
    search_tasks() {
      const _from = (this.currentPage - 1) * this.pageSize
      const _size = this.pageSize
      api.getRemoteTaskList(this.dateRange[0], this.dateRange[1], _from, _size, this.repoFilter, this.branchFilter,
      this.WorkflowSearch, this.taskNameSearch, this.personSearch).then(r => {
        this.taskList = r.data.hits.map(i => i._source)
        this.total = r.data.total
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 列表失败', error)
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.search_tasks()
    },
    showDialog() {
      this.dialogVisible = true
    }
  }
}
</script>


<style scoped>
.container {
    margin: 0 auto;
    padding-top: 4px;
    padding-left: 16px;
    padding-right: 16px
}

.table-cell-section {
    width: 100%;
    display: table-cell;
}
.task-list-ctrl{
    margin-bottom: 8px;
}
.task-list-table{
    padding: 0;
    overflow-y: scroll;
    height: calc(100vh - 200px);
}
.date-picker{
    width: 100%;
}
.task-ctrl{
    padding-right:8px;
    padding-left:8px;
}
</style>

<style>
.el-input__inner{
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
</style>
