<template>
    <div v-loading="aggrLoading">
        <div class="aggr-content">
            <el-button size="mini" circle icon="el-icon-refresh" @click="refresh"></el-button>
            通过率/平均分(修订) : {{ shownValue(aggrResult['通过率(score平均分)'], true) }}
        </div>
    </div>
</template>

<script>
import api from '../LLMEvals/api'

export default {
  props: {
    subTaskId: String,
    taskId: String
  },
  data() {
    return {
      aggrLoading: false,
      aggrResult: {}
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    shownValue(item, percentage = false) {
      if (!item) {
        return null
      }
      const value = item.value
      if (typeof value === 'number') {
        if (percentage) {
          const p = value * 100
          return p.toFixed(2) + '%'
        } else if (Number.isInteger(value)) {
          return value
        } else {
          return value.toFixed(2)
        }
      } else {
        return value
      }
    },
    refresh() {
      this.getAggrData()
    },
    getAggrData() {
      this.aggrLoading = true
      api.aggrData(this.taskId, this.subTaskId).then(r => {
        this.aggrResult = r.data
        this.aggrLoading = false
      }).catch((error) => {
        // api.notifyError(this.$notify, '统计失败', error)
        console.error(error)
        this.aggrLoading = false
      })
    }
  }
}
</script>

<style scoped>
.aggr-title {
    font-size: 14px; /* 字体大小 */
    color:  #666;  /* 字体颜色 */
    margin-bottom: 5px; /* 下边距 */
    font-weight: bold; /* 字体加粗 */
    text-align: center;
}

.aggr-content {
    font-size: 16px; /* 字体大小 */
    color: #333; /* 字体颜色 */
    line-height: 1.5; /* 行高 */
    padding: 5px 0; /* 内边距 */
    text-align: center;
}
</style>
