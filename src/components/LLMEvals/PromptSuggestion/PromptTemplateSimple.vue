<template>
    <div>
        <mtd-card>
            <template #title >
                简单评测
            </template>
            <flow-chart-row>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'data-design')" :focus="activateName==='data-design'"
                    laneType="" right label="1">确定数据</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'prompt-design')"  :focus="activateName==='prompt-design'"
                    laneType="" left right label="2">设计 prompt</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col end>
                    <flow-chart-lane :check="handleSelect.bind(this, 'run')" :focus="activateName==='run'"
                    laneType="" left label="3">执行评测</flow-chart-lane>
                </flow-chart-col>
            </flow-chart-row>
            <Divider/>
            <prompt-suggest-card  v-model="currentPrompt" 
                :defaultConfigs="designPromptConfig"
                v-if="activateName==='prompt-design'" 
                :repo="repo"></prompt-suggest-card>
            <data-design-card v-else-if="activateName==='data-design'" :data="inputData" :tool="apiTool" :toolName="toolName" @change="handleChange"></data-design-card>
            <div v-else>
                <mtd-form inline>
                    <mtd-form-item label="切换展示">
                        <mtd-button @click="handleSwitch" circle icon="mtdicon-share-arrow-fill" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="开始执行">
                        <mtd-button @click="handleRun" circle icon="mtdicon-paperplane" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="进度">
                        <mtd-progress style="width: 260px;" type="steps" size="small" 
                        :steps="inputData.length" 
                        :percentage="runningStep/inputData.length* 100"
                        :show-info="false"
                        class="progress-base">
                        </mtd-progress>
                        <span>{{ runningStep }} / {{ inputData.length }}</span>
                    </mtd-form-item>
                </mtd-form>
                <div v-if="!showCoding">
                    <Divider>评测其他配置</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            1. 如果大模型的输出结果是 json 格式的，那么评测会主动找到 json 字符串，并通过 jpath 聚焦回答结果<br>
                            2. 如果大模型输出的是文本格式的，那么评测会根据行号聚焦到某一行，根据 re.pattern 聚焦回答结果
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <mtd-form inline>
                        <!-- <mtd-form-item label="Scorer">
                            <mtd-select disabled v-model="scorerChoice" placeholder="请选择Scorer" filterable :options="scorerOptions"/>
                        </mtd-form-item> -->
                        <mtd-form-item label="模型">
                            <mtd-select v-model="model" placeholder="请选择模型" filterable :options="modelOptions"/>
                        </mtd-form-item>
                        <mtd-form-item label="识别模式" required>
                            <mtd-select v-model="score_result_recognization" :options="recognizeOptions" style="width: 200px;">
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="jpath聚焦" helper="关注输出 json 的内容" v-if="score_result_recognization==='json'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                            v-model="score_jpath" :options="jpathPatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="行号聚焦" helper="关注多行输出的第几行" v-if="score_result_recognization==='mutiline'">
                            <mtd-input-number v-model="score_line_num" :min="0"/>
                        </mtd-form-item>
                        <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式" v-if="score_result_recognization==='mutiline'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create
                            v-model="score_re_pattern" :options="rePatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName2" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                    </mtd-form>
                    <Divider>得分映射关系</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            得分映射关系是一个字典，如果聚焦的内容命中了字典的某个 key，那么得分就是相应的 value
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <string-dict-editor v-model="score_mapping"/>
                    <Divider>执行结果</Divider>
                    <mtd-loading :loading="loading">
                        <el-empty v-if="resultItem.length===0" description="缺少数据"></el-empty>
                        <mtd-card v-else v-for="(item,ind) in resultItem" :key="ind" shadow="hover" :title="`编号${ind}`">
                            <message-box :item="item" :frontEndConfig="frontEndConfig"></message-box>
                        </mtd-card>
                    </mtd-loading>
                </div>
                <div v-else>
                    <div v-if="!coding">
                        <el-empty v-if="resultItem.length===0" description="缺少 prompt"></el-empty>
                    </div>
                    <pre v-else>{{ coding }}</pre>
                </div>
            </div>
        </mtd-card>
    </div>
</template>


<script>
import api from '../api'
import FlowChartCol from '../../FlowChart/FlowChartCol.vue'
import FlowChartRow from '../../FlowChart/FlowChartRow.vue'
import FlowChartLane from '../../FlowChart/FlowChartLane.vue'
import PromptSuggestCard from '../PromptSuggestion/PromptSuggestCard.vue'
import DataDesignCard from '../PromptSuggestion/DataDesignCard.vue'
import MessageBox from '../Tools/MessageBox.vue'
import JsonInputer from '../Tools/JsonInputer.vue'
import StringDictEditor from '../Tools/StringDictEditor.vue'

export default {
  components: {
    FlowChartCol,
    FlowChartRow,
    FlowChartLane,
    PromptSuggestCard,
    DataDesignCard,
    MessageBox,
    JsonInputer,
    StringDictEditor
  },
  props: {
    repo: String
  },
  watch: {
    currentPrompt(value) {
      console.log('currentPrompt', value)
      this.model = value.recommand_model
      if (value.result_recognization.includes('json')) {
        this.score_result_recognization = 'json'
        this.score_jpath = value.jpath
      } else {
        this.score_result_recognization = 'mutiline'
        this.score_re_pattern = value.repattern
      }
    }
  },
  data() {
    return {
      recognizeOptions: [{'value': 'json', 'label': 'json'}, {'value': 'mutiline', 'label': '多行'}, {'value': 'none', 'label': '无'}],
      activateName: 'data-design',
      currentPrompt: null,
      designPromptConfig: {
        scene: ['Experience', 'Sentivity'],
        recommand_model: ['gpt-3.5-turbo-1106', 'deepseek-chat']
      },
      rePatterns: api.rePatterns,
      currentData: null,
      inputData: [{
        'poiId': '1457375',
        'poiName': '北京昆泰酒店',
        'userInput': '怎么联系',
        'userId': '1',
        'token': '1',
        '类型': '联系',
        '备注': '010-86301234'
      }],
      score_jpath: '.',
      score_result_recognization: 'json',
      score_line_num: 0,
      model: 'deepseek-chat',
      modelOptions: [],
      score_mapping: {'是': 1, '否': 0},
      score_re_pattern: '回答[：:]\\s*([\\u4E00-\\u9Fa5]+)',
      scorerChoice: 'toolchain_llmeval.workflow.workflow_tools.scorer.online:DefaultSimpleChatScorer',
      scorerOptions: [{label: 'DefaultSimpleChatScorer', value: 'toolchain_llmeval.workflow.workflow_tools.scorer.online:DefaultSimpleChatScorer'}],
      frontEndConfig: {},
      resultItem: [],
      loading: false,
      showCoding: false,
      toolName: 'HTTP-API',
      apiTool: {
        cls: 'toolchain_llmeval.workflow.workflow_tools.api_completion_fns.default:HTTPCompletionFn',
        args: {
          host: 'https://fuxi.sankuai.com',
          uri: '/api/flow/run',
          method: 'POST',
          completion_jpath: '.',
          param_template: null,
          env: 'prod',
          body_template: '{\n  "flowId": 263, \n  "inputParams": {\n    "poiName": "{poiName}",  \n    "historyMessage":[],\n    "poiId": "{poiId}", \n    "userInput": "{userInput}", \n    "userId": "{userId}", \n    "token":"{token}"\n  }\n}',
          auth_method: 'shepherd',
          extension_output_field_jpath: {},
          lion_token_name: null,
          lion_mws_secretary_name: null,
          lion_oceanus_token_name: null,
          sso_field_name: null,
          server_appkey: null,
          timeout: 1200,
          headers: {},
          cookie: {}
        }
      },
      runningStep: 0,
      jpathPatterns: api.jpathPatterns,
      createName: '',
      createName2: ''
    }
  },
  computed: {
    coding() {
      if (!this.currentPrompt) {
        return ''
      }
      const name = this.currentPrompt.recommand_name
      return {
        'Scorers': {[name]: {
          'cls': this.scorerChoice,
          'args': {
            'prompt': {
              'messages': this.currentPrompt.messages,
              'search_text': this.currentPrompt.search_text,
              'result_recognization': this.currentPrompt.result_recognization
            },
            model: this.model,
            score_jpath: this.score_jpath,
            score_line_num: this.score_line_num,
            score_mapping: this.score_mapping,
            score_re_pattern: this.score_re_pattern,
            score_result_recognization: this.score_result_recognization
          }
        }}
      }
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    sync() {
      api.enumModels().then(resp => {
        this.modelOptions = resp.data.models.map(v => {
          return {'label': v, 'value': v}
        })
      })
    },
    validateJSON(input) {
      try {
        JSON.parse(input)
        return true
      } catch (error) {
        return false
      }
    },
    handleSwitch() {
      this.showCoding = !this.showCoding
    },
    handleSelect(name) {
      this.activateName = name
    },
    handleChange(value) {
      this.inputData = value
    },
    step() {
      this.$emit('step')
      this.runningStep = this.runningStep + 1
    },
    done(data) {
      this.$emit('done', data)
    },
    begin() {
      this.$emit('begin')
      this.runningStep = 0
    },
    async handleRun() {
      this.begin()
      if (!this.inputData || this.inputData.length === 0) {
        api.notifyError(this.$notify, '当前数据条目为 0 条', Error('缺少数据'))
        this.done([])
        return
      }
      this.resultItem = []
      for (const item of this.inputData) {
        try {
          const result = await api.onlineScorerRun({
            'tool': {
              'tool_name': 'online-scorer',
              'cls': this.scorerChoice,
              'args': {
                'score_jpath': this.score_jpath,
                'score_line_num': this.score_line_num,
                'score_mapping': this.score_mapping,
                'score_re_pattern': this.score_re_pattern,
                'prompt': this.currentPrompt,
                'model': this.model,
                'score_result_recognization': this.score_result_recognization
              }
            },
            'data_list': [item]
          })
          this.resultItem.push(...result.data.data_items)
        //   this.frontEndConfig = result.data.data_list.front_end_config
          Object.keys(result.data.data_items[0]).forEach(key => {
            if (['__change_log__', '__es_id__', '__id__', 'completion', 'eval_messages', 'messages', 'model_graded', 'score'].includes(key)) {
              this.frontEndConfig[key] = {'show': false}
            } else {
              this.frontEndConfig[key] = {'show': true}
            }
          })
          this.step()
        } catch (error) {
          api.notifyError(this.$notify, '执行失败', error)
          this.step()
        }
      }
      this.done(this.resultItem)
    }
  }
}
</script>