<template>
    <mtd-modal v-model="selfVisible" :title="title" width="1000px" placement="top" @close="onClose">
        <mtd-form label-position="right" inline>
            <mtd-form-item label="场景">
                <mtd-select v-model="scene" class="select-width" filterable :options="sceneOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="来源">
                <mtd-select v-model="source" class="select-width" filterable :options="sourceOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="是否有参考">
                <mtd-select v-model="has_reference" class="select-width" filterable :options="referenceOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="有无示例">
                <mtd-select v-model="has_few_shot" class="select-width" filterable :options="fewShotsOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="有无原因分析">
                <mtd-select v-model="to_give_reason" class="select-width" filterable :options="giveReasonOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="模型建议">
                <mtd-select v-model="recommand_model" class="select-width" filterable :options="modelOptions"
                auto-clear-query clearable allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="预设名称">
                <mtd-input v-model="recommand_name"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="识别方式">
                <mtd-select v-model="result_recognization" class="select-width" filterable :options="resultRecogOptions"
                auto-clear-query clearable :loading="suggestLoading" remote 
                :remote-method="suggestPrompt.bind(this, 'result_recognization')"
                :debounce="300" allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="识别方式">
                <mtd-select v-model="result_recognization" class="select-width" filterable :options="resultRecogOptions"
                auto-clear-query clearable :loading="suggestLoading" remote 
                :remote-method="suggestPrompt.bind(this, 'result_recognization')"
                :debounce="300" allow-create
                ></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="jpath聚焦" helper="关注输出 json 的内容">
                <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                v-model="jpath" :options="jpathPatterns">
                    <template #footer>
                        <div class="create-option">
                            <mtd-input v-model="createName" style="margin-right:8px" />
                            <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                        </div>
                    </template>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式">
                <mtd-select style="width: 300px;" filterable clearable allow-create
                v-model="repattern" :options="rePatterns">
                    <template #footer>
                        <div class="create-option">
                            <mtd-input v-model="createName2" style="margin-right:8px" />
                            <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                        </div>
                    </template>
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="关键词">
                <mtd-tag v-for="(keyword, ind) in keywords" :key="ind" closable @close="removeKeyword(ind)">{{ keyword }}</mtd-tag>
                <mtd-input v-model="newKeyword"></mtd-input>
                <mtd-button type="primary" @click="addKeyword" icon="add" circle></mtd-button>
            </mtd-form-item>
        </mtd-form>
        <Divider></Divider>
        <div>
            <mtd-button type="primary" @click="addItem">
                <Icon type="md-add" />添加项</mtd-button>
            <mtd-card class="card-box" shadow="hover" v-for="(item, index) in messages" :key="index">
                <template #title>
                    <mtd-select v-model="item.role" placeholder="角色" :options="roleOptions">
                    </mtd-select>
                </template>
                <Input title="文本内容" type="textarea" autosize multi-line clearable v-model="item.content" placeholder="内容"></Input>
                <template #extra>
                    <mtd-button @click="removeItem(index)" type="danger"><Icon type="md-trash" />删除</mtd-button>
                </template>
            </mtd-card>
        </div>
        <template v-slot:footer>
            <div>
                <mtd-button @click="onSave" type="primary">保存</mtd-button>
                <mtd-button @click="onClose">取消</mtd-button>
            </div>
        </template>
    </mtd-modal>
</template>

<script>
import api from '../api'

export default{
  props: {
    visible: Boolean,
    title: String,
    prompt: Object,
    promptId: String
  },
  data() {
    return {
      roleOptions: [{'value': 'user', 'label': '提问者(user)'}, {'value': 'assistant', 'label': 'AI(assistant)'}, {'value': 'system', 'label': '背景指令(system)'}],
      scene: null,
      source: null,
      has_reference: null,
      to_give_reason: null,
      has_few_shot: null,
      keywords: [],
      recommand_model: null,
      submittor: null,
      recommand_name: null,
      search_text: '',
      result_recognization: null,
      suggestLoading: false,
      sceneOptions: [],
      fewShotsOptions: [],
      sourceOptions: [],
      referenceOptions: [],
      giveReasonOptions: [],
      keywordsOptions: [],
      modelOptions: [],
      submittorOptions: [],
      nameOptions: [],
      resultRecogOptions: [],
      messages: [],
      selfVisible: false,
      newKeyword: '',
      rePatterns: api.rePatterns,
      jpathPatterns: api.jpathPatterns,
      jpath: '.',
      repattern: '(.*)',
      createName: '',
      createName2: ''
    }
  },
  mounted() {
    this.sync()
    this.selfVisible = this.visible
  },
  watch: {
    prompt() {
      this.sync()
    },
    visible(value) {
      this.selfVisible = value
    }
  },
  methods: {
    sync() {
      if (this.prompt) {
        this.suggestPrompt('scene', '')
        this.scene = this.prompt.scene
        this.source = this.prompt.source
        this.has_few_shot = this.prompt.has_few_shot
        this.has_reference = this.prompt.has_reference
        this.to_give_reason = this.prompt.to_give_reason
        this.recommand_model = this.prompt.recommand_model
        this.recommand_name = this.prompt.recommand_name
        this.keywords = this.prompt.keywords
        this.result_recognization = this.prompt.result_recognization
        this.messages = this.prompt.messages
        this.jpath = this.prompt.jpath
        this.repattern = this.prompt.repattern
      }
      api.enumModels().then(resp => {
        this.modelOptions = resp.data.models.map(v => {
          return {'label': v, 'value': v}
        })
      })
    },
    getBucketKeys(aggr) {
      if (!aggr) {
        return []
      }
      const buckets = aggr.buckets
      return buckets.map(bucket => { return {value: bucket.key, label: bucket.key} })
    },
    suggestPrompt(field, query) {
      this.suggestLoading = true
      let payload = {
      }
      payload[field] = query
      api.suggestPrompt(payload).then(resp => {
        const data = resp.data
        this.sceneOptions = this.getBucketKeys(data.aggregations.scene)
        this.sourceOptions = this.getBucketKeys(data.aggregations.source)
        this.referenceOptions = this.getBucketKeys(data.aggregations.has_reference)
        this.fewShotsOptions = this.getBucketKeys(data.aggregations.has_few_shot)
        this.giveReasonOptions = this.getBucketKeys(data.aggregations.to_give_reason)
        // this.modelOptions = this.getBucketKeys(data.aggregations.recommand_model)
        this.nameOptions = this.getBucketKeys(data.aggregations.recommand_name)
        this.keywordsOptions = this.getBucketKeys(data.aggregations.keywords)
        this.submittorOptions = this.getBucketKeys(data.aggregations.submittor)
        this.resultRecogOptions = this.getBucketKeys(data.aggregations.result_recognization)
        this.suggestLoading = false
      }).catch((error) => {
        this.suggestLoading = false
        api.notifyError(this.$notify, 'prompt 推荐失败', error)
      })
    },
    addItem() {
      this.messages.push({ role: '', content: '' })
    },
    addKeyword() {
      this.keywords.push(this.newKeyword)
      this.newKeyword = ''
    },
    removeKeyword(ind) {
      this.keywords.splice(ind, 1)
    },
    removeItem(index) {
      this.messages.splice(index, 1)
    },
    onSave() {
      const promptData = {
        scene: this.scene,
        source: this.source,
        has_few_shot: this.has_few_shot,
        has_reference: this.has_reference,
        to_give_reason: this.to_give_reason,
        recommand_model: this.recommand_model,
        recommand_name: this.recommand_name,
        keywords: this.keywords,
        result_recognization: this.result_recognization,
        messages: this.messages,
        jpath: this.jpath,
        repattern: this.repattern
      }
      api.editPrompt({
        '_source': promptData,
        '_id': this.promptId
      }).then(resp => {
        this.$emit('save', resp.data)
      }).catch((error) => {
        api.notifyError(this.$notify, '保存失败', error)
      })
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>

</style>