<template>
    <div>
        <mtd-card>
            <template #title >
                简单评测
            </template>
            <flow-chart-row>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'data-design')" :focus="activateName==='data-design'"
                    laneType="" right label="1">确定数据</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'tool-choice')"  :focus="activateName==='tool-choice'"
                    laneType="" left right label="2">选择工具</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col end>
                    <flow-chart-lane :check="handleSelect.bind(this, 'run')" :focus="activateName==='run'"
                    laneType="" left label="3">执行评测</flow-chart-lane>
                </flow-chart-col>
            </flow-chart-row>
            <Divider/>
            <!-- <prompt-suggest-card  v-model="currentPrompt" 
                :defaultConfigs="designPromptConfig"
                v-if="activateName==='tool-choice'" 
                :repo="repo"></prompt-suggest-card> -->
            <ToolListCard @select="handleToolSelect" :repo="repo" v-if="activateName==='tool-choice'" ></ToolListCard>
            <data-design-card v-else-if="activateName==='data-design'" :data="inputData" :tool="apiTool" :toolName="toolName" @change="handleChange"></data-design-card>
            <div v-else>
                <mtd-form inline>
                    <mtd-form-item label="切换展示">
                        <mtd-button @click="handleSwitch" circle icon="mtdicon-share-arrow-fill" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="开始执行">
                        <mtd-button @click="handleRun" circle icon="mtdicon-paperplane" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="进度">
                        <mtd-progress style="width: 260px;" type="steps" size="small" 
                        :steps="inputData.length" 
                        :percentage="runningStep/inputData.length* 100"
                        :show-info="false"
                        class="progress-base">
                        </mtd-progress>
                        <span>{{ runningStep }} / {{ inputData.length }}</span>
                    </mtd-form-item>
                </mtd-form>
                <div v-if="!showCoding">
                    <Divider>工具参数制定</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            请按照json格式，填写一下工具的参数，必须json校验成功
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <vue-json-editor v-model="toolArgs" :options="options"></vue-json-editor>
                    <Divider>执行结果</Divider>
                    <mtd-loading :loading="loading">
                        <el-empty v-if="resultItem.length===0" description="缺少数据"></el-empty>
                        <mtd-card v-else v-for="(item,ind) in resultItem" :key="ind" shadow="hover" :title="`编号${ind}`">
                            <message-box :item="item" :frontEndConfig="frontEndConfig"></message-box>
                        </mtd-card>
                    </mtd-loading>
                </div>
                <div v-else>
                    <div v-if="!coding">
                        <el-empty v-if="resultItem.length===0" description="缺少 prompt"></el-empty>
                    </div>
                    <pre v-else>{{ coding }}</pre>
                </div>
            </div>
        </mtd-card>
    </div>
</template>


<script>
import api from '../api'
import FlowChartCol from '../../FlowChart/FlowChartCol.vue'
import FlowChartRow from '../../FlowChart/FlowChartRow.vue'
import FlowChartLane from '../../FlowChart/FlowChartLane.vue'
import PromptSuggestCard from '../PromptSuggestion/PromptSuggestCard.vue'
import DataDesignCard from '../PromptSuggestion/DataDesignCard.vue'
import MessageBox from '../Tools/MessageBox.vue'
import JsonInputer from '../Tools/JsonInputer.vue'
import StringDictEditor from '../Tools/StringDictEditor.vue'
import ToolListCard from './ToolListCard.vue'
import VueJsonEditor from 'vue-json-editor'

export default {
  components: {
    VueJsonEditor,
    FlowChartCol,
    FlowChartRow,
    FlowChartLane,
    PromptSuggestCard,
    DataDesignCard,
    MessageBox,
    JsonInputer,
    StringDictEditor,
    ToolListCard
  },
  props: {
    repo: String
  },
  watch: {
  },
  data() {
    return {
      inputData: [{
        'poiId': '1457375',
        'poiName': '北京昆泰酒店',
        'userInput': '怎么联系',
        'userId': '1',
        'token': '1',
        '类型': '联系',
        '备注': '010-86301234'
      }],
      activateName: 'data-design',
      rePatterns: api.rePatterns,
      apiTool: {},
      resultItem: [],
      loading: false,
      showCoding: false,
      runningStep: 0,
      frontEndConfig: {},
      toolArgs: {}, // 初始化为空对象或者你的初始 JSON 数据
      options: {
        mode: 'code', // 可选 'tree', 'view', 'form', 'code' 或 'text'
        modes: ['tree', 'view', 'form', 'code', 'text'], // 允许用户切换的模式
        onError: (err) => {
          api.notifyError(this.$notify, 'JSON 编辑器错误', err)
        }
      }
    }
  },
  computed: {
    coding() {
      if (!this.apiTool) {
        return ''
      }
      return this.apiTool
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    sync() {
      api.enumModels().then(resp => {
        this.modelOptions = resp.data.models.map(v => {
          return {'label': v, 'value': v}
        })
      })
    },
    validateJSON(input) {
      try {
        JSON.parse(input)
        return true
      } catch (error) {
        return false
      }
    },
    handleSwitch() {
      this.showCoding = !this.showCoding
    },
    handleSelect(name) {
      this.activateName = name
    },
    handleChange(value) {
      this.inputData = value
    },
    handleToolSelect(value) {
      this.apiTool = {...value}
      this.activateName = 'run'
      if (this.apiTool.args) {
        this.toolArgs = this.apiTool.args
      }
    },
    step() {
      this.$emit('step')
      this.runningStep = this.runningStep + 1
    },
    done(data) {
      this.$emit('done', data)
    },
    begin() {
      this.$emit('begin')
      this.runningStep = 0
    },
    async handleRun() {
      this.begin()
      if (!this.inputData || this.inputData.length === 0) {
        api.notifyError(this.$notify, '当前数据条目为 0 条', Error('缺少数据'))
        this.done([])
        return
      }
      this.resultItem = []
      for (const item of this.inputData) {
        try {
          this.apiTool.args = this.toolArgs
          const result = await api.runToolsOnline(
            this.repo,
            this.apiTool,
            {item: item},
            {}
          )
        //   const itemAndExtra = {...result.data.item, ...result.data.extra || {}}
          this.resultItem.push(result.data)
        //   this.frontEndConfig = result.data.data_list.front_end_config
          Object.keys(result.data.item).forEach(key => {
            if (['__change_log__', '__es_id__', '__id__', 'completion', 'eval_messages', 'messages', 'model_graded', 'score'].includes(key)) {
              this.frontEndConfig[key] = {'show': false}
            } else {
              this.frontEndConfig[key] = {'show': true}
            }
          })
          if (result.data.extra) {
            Object.keys(result.data.extra).forEach(key => {
              if (['__change_log__', '__es_id__', '__id__', 'completion', 'eval_messages', 'messages', 'model_graded', 'score'].includes(key)) {
                this.frontEndConfig[key] = {'show': false}
              } else {
                this.frontEndConfig[key] = {'show': true}
              }
            })
          }
          this.step()
        } catch (error) {
          api.notifyError(this.$notify, '执行失败', error)
          this.step()
        }
      }
      this.done(this.resultItem)
    }
  }
}
</script>