<template>
    <div>
        <div>
            <mtd-announcement title="提示" type="info" show-icon>
                <template #description>
                    1. prompt 设计功能，能够帮你快速找到一些已有的可供参考的 prompt，通过复制，你可以把 prompt 放到自己的名下备用<br>
                    2. 先“复制”，再“编辑”，点击“当前”按钮即可编辑自己的 prompt<br>
                    3. Experience 类、Sentivity 类的评测，可以根据自己需要，把两种校验规则合起来
                </template>
            </mtd-announcement>
            <div style="height: 10px;"></div>
            <mtd-form label-position="right" inline>
                <mtd-form-item label="场景" helper="建议选 Experience 体验类评测">
                    <mtd-select v-model="scene" class="select-width" filterable multiple :options="sceneOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="来源" helper="langchain 的 prompt 比较简单">
                    <mtd-select v-model="source" class="select-width" filterable multiple :options="sourceOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="是否有参考" helper="体验类评测不需要，相关性需要">
                    <mtd-select v-model="has_reference" class="select-width" filterable :options="referenceOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="有无示例" helper="有示例的 prompt 在完善中">
                    <mtd-select v-model="has_few_shot" class="select-width" filterable :options="fewShotsOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="有无原因分析" helper="建议选择'是'，增加可解释性">
                    <mtd-select v-model="to_give_reason" class="select-width" filterable :options="giveReasonOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="模型建议" helper="建议 deepseek-chat">
                    <mtd-select v-model="recommand_model" class="select-width" multiple filterable :options="modelOptions"
                    auto-clear-query clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="预设名称" helper="如果场景不够细致，可以搜名称">
                    <mtd-select v-model="recommand_name" class="select-width" filterable :options="nameOptions"
                    auto-clear-query clearable :loading="suggestLoading" remote 
                    :remote-method="suggestPrompt.bind(this, 'recommand_name')"
                    :debounce="300" allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="关键词" helper="支持多选搜索，是场景的补充">
                    <mtd-select v-model="keywords" class="select-width" filterable multiple :options="keywordsOptions"
                    auto-clear-query clearable :loading="suggestLoading" remote 
                    :remote-method="suggestPrompt.bind(this, 'keywords')"
                    :debounce="300" allow-create
                    ></mtd-select>
                </mtd-form-item>
                <!-- <mtd-form-item label="提交者" helper="可以填 liyilun02">
                    <mtd-select v-model="submittor" class="select-width" filterable :options="submittorOptions"
                    auto-clear-query clearable :loading="suggestLoading" remote 
                    :remote-method="suggestPrompt.bind(this, 'submittor')"
                    :debounce="300" allow-create
                    ></mtd-select>
                </mtd-form-item> -->
                <mtd-form-item label="识别方式" helper="json 或者多行">
                    <mtd-select v-model="result_recognization" class="select-width" filterable :options="resultRecogOptions"
                    auto-clear-query clearable :loading="suggestLoading" remote 
                    :remote-method="suggestPrompt.bind(this, 'result_recognization')"
                    :debounce="300" allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="打分类别" helper="二元或者5级打分">
                    <mtd-select v-model="score_type" class="select-width" filterable :options="scoreTypeOptions" clearable allow-create
                    ></mtd-select>
                </mtd-form-item>
                <mtd-form-item label="搜索文本" helper="可以尝试全文本搜索">
                    <mtd-input v-model="search_text"></mtd-input>
                </mtd-form-item>
                <mtd-form-item label="操作">
                    <mtd-button type="primary" style="margin-right: 12px;" @click="onSearch">
                        <Icon type="md-search" />搜索
                    </mtd-button>
                    <mtd-button type="success" style="margin-right: 12px;" v-if="this.currentPromptId" @click="onEditVisible(true)">
                        <Icon type="ios-settings" />当前
                    </mtd-button>
                    <mtd-button type="primary" style="margin-right: 12px;" v-if="this.private" @click="onPrivateSwitch">
                        <Icon type="ios-share-alt" />公共
                    </mtd-button>
                    <mtd-button type="primary" style="margin-right: 12px;" v-else @click="onPrivateSwitch">
                        <Icon type="ios-share-alt" />我的
                    </mtd-button>
                </mtd-form-item>
            </mtd-form>
            <Divider></Divider>
            <mtd-card class="card-box" shadow="hover" v-for="prompt in shownPromptData" :key="prompt._id">
                <template #title>
                    <Icon type="md-checkbox" color="green" v-if="prompt._id===currentPromptId"/>
                    {{ prompt._source.recommand_name || "未命名" }}
                    <mtd-tag size="small" theme="green">{{ prompt._source.scene }}</mtd-tag>
                    <mtd-tag size="small" theme="brown">{{ prompt._source.recommand_model }}</mtd-tag>
                    <mtd-tag size="small" theme="blue" v-for="k in splitKeywords(prompt._source.keywords)" :key="k">{{ k }}</mtd-tag>
                    <mtd-tag size="small" theme="gray" v-if="!prompt._source.private">公共</mtd-tag>
                </template>
                <div style="white-space: pre-wrap;">{{ prompt._source.search_text }}</div>
                <template #extra>
                    <mtd-button type="text-primary" v-if="!prompt._source.private && isAdm && prompt._id!==currentPromptId" @click="onChoose(prompt)">管理</mtd-button>
                    <mtd-button type="text-primary" v-if="!prompt._source.private" @click="onCopy(prompt)">复制</mtd-button>
                    <mtd-button type="text-primary" v-else-if="prompt._id!==currentPromptId" @click="onChoose(prompt)">选用</mtd-button>
                </template>
            </mtd-card>
            <mtd-pagination show-total show-quick-jumper show-size-changer :total="total"
            :current-page.sync="currentPage" v-model:current-page="currentPage" v-model:page-size="pageSize" />
        </div>
        <prompt-edit-modal 
        :visible="editVisible" 
        @close="onEditVisible(false)"
        @save="onEditSave"
        title="编辑当前选中的 prompt"
        :prompt="currentPrompt"
        :promptId="currentPromptId"
        ></prompt-edit-modal>
    </div>
</template>

<script>
import api from '../api'
import PromptEditModal from '../PromptSuggestion/PromptEditModal'

export default{
  components: {
    PromptEditModal
  },
  props: {
    repo: String,
    value: {
      type: Object,
      default: null
    },
    defaultConfigs: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      scene: null,
      source: null,
      has_reference: null,
      to_give_reason: null,
      has_few_shot: null,
      keywords: null,
      recommand_model: null,
      submittor: null,
      recommand_name: null,
      search_text: '',
      result_recognization: null,
      score_type: null,
      suggestLoading: false,
      private: false,
      sceneOptions: [],
      fewShotsOptions: [],
      sourceOptions: [],
      referenceOptions: [],
      giveReasonOptions: [],
      keywordsOptions: [],
      modelOptions: [],
      submittorOptions: [],
      nameOptions: [],
      resultRecogOptions: [],
      scoreTypeOptions: [],
      promptData: [],
      currentPrompt: null,
      currentPromptId: null,
      editVisible: false,
      isAdm: false,
      total: 100,
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    shownPromptData() {
      // 首先，复制原始数组以避免直接修改
      const promptDataCopy = [...this.promptData]
      // 置顶
      const index = promptDataCopy.findIndex(p => p._id === this.currentPromptId)
      if (index > -1) {
        const [selectedPrompt] = promptDataCopy.splice(index, 1)
        promptDataCopy.unshift(selectedPrompt)
      }
      // 返回新的数组，而不是修改原始数组
      return promptDataCopy
    }
  },
  created() {
    if (this.value) {
      this.currentPrompt = this.value
      this.currentPromptId = this.value.id
    }
  },
  mounted() {
    if (this.defaultConfigs) {
      this.source = this.defaultConfigs.source || null
      this.scene = this.defaultConfigs.scene || null
      this.has_reference = this.defaultConfigs.has_reference || null
      this.to_give_reason = this.defaultConfigs.to_give_reason || null
      this.has_few_shot = this.defaultConfigs.has_few_shot || null
      this.recommand_model = this.defaultConfigs.recommand_model || null
      this.score_type = this.defaultConfigs.score_type || null
    }
    if (this.value) {
      this.currentPrompt = this.value
      this.currentPromptId = this.value.id
      this.private = true
    }
    this.sync()
    this.cardFocus()
  },
  watch: {
    private() {
      this.sync()
    },
    repo() {
      this.onSearch()
    },
    currentPrompt(value) {
      this.$emit('input', value)
      this.$emit('promptChange', value)
    },
    currentPage() {
      this.onSearch()
      this.cardFocus()
    },
    pageSize() {
      this.onSearch()
      this.cardFocus()
    }
  },
  methods: {
    cardFocus() {
      window.scrollTo(0, 500)
    },
    sync() {
      this.suggestPrompt('scene', '')
      this.onSearch()
      api.isAdmin().then(resp => {
        this.isAdm = resp.data.check
      })
    },
    splitKeywords(text) {
      if (!text) {
        return []
      }
      return text
    },
    getBucketKeys(aggr) {
      if (!aggr) {
        return []
      }
      const buckets = aggr.buckets
      return buckets.map(bucket => { return {value: bucket.key, label: bucket.key} })
    },
    onPrivateSwitch() {
      this.private = !this.private
    },
    onEditVisible(value) {
      this.editVisible = value
    },
    onEditSave(data) {
      this.currentPrompt = data._source
      this.currentPromptId = data._id
      this.editVisible = false
      setTimeout(() => {
        this.sync()
      }, 1000)
    },
    onChoose(prompt) {
      this.currentPrompt = prompt._source
      this.currentPromptId = prompt._id
      this.editVisible = true
      this.cardFocus()
    },
    onCopy(prompt) {
      const source = prompt._source
      api.addPrompt({
        '_source': source,
        'repo': this.repo
      }).then(resp => {
        const data = resp.data
        this.currentPrompt = data['_source']
        this.currentPromptId = data['_id']
        this.private = true
        this.editVisible = true
        setTimeout(() => {
          this.sync()
        }, 1000)
      }).catch((error) => {
        api.notifyError(this.$notify, '复制 prompt 失败', error)
      })
    },
    onSearch() {
      const payload = {
        from: (this.currentPage - 1) * this.pageSize,
        size: this.pageSize,
        scene: this.scene,
        source: this.source,
        has_reference: this.has_reference,
        to_give_reason: this.to_give_reason,
        has_few_shot: this.has_few_shot,
        keywords: this.keywords,
        recommand_model: this.recommand_model,
        submittor: this.submittor,
        recommand_name: this.recommand_name,
        search_text: this.search_text,
        result_recognization: this.result_recognization,
        private: this.private,
        repo: this.repo,
        score_type: this.score_type
      }
      api.listPrompt(payload).then(resp => {
        const data = resp.data
        this.promptData = data.hits
        this.total = data.total
        for (const prompt of this.promptData) {
          if (prompt._id === this.currentPromptId) {
            this.currentPrompt = prompt._source
          }
        }
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 prompt 列表失败', error)
      })
    },
    suggestPrompt(field, query) {
      this.suggestLoading = true
      let payload = {
        private: this.private
      }
      payload[field] = query
      api.suggestPrompt(payload).then(resp => {
        const data = resp.data
        this.sceneOptions = this.getBucketKeys(data.aggregations.scene)
        this.sourceOptions = this.getBucketKeys(data.aggregations.source)
        this.referenceOptions = this.getBucketKeys(data.aggregations.has_reference)
        this.fewShotsOptions = this.getBucketKeys(data.aggregations.has_few_shot)
        this.giveReasonOptions = this.getBucketKeys(data.aggregations.to_give_reason)
        this.modelOptions = this.getBucketKeys(data.aggregations.recommand_model)
        this.nameOptions = this.getBucketKeys(data.aggregations.recommand_name)
        this.keywordsOptions = this.getBucketKeys(data.aggregations.keywords)
        this.submittorOptions = this.getBucketKeys(data.aggregations.submittor)
        this.resultRecogOptions = this.getBucketKeys(data.aggregations.result_recognization)
        this.scoreTypeOptions = this.getBucketKeys(data.aggregations.score_type)
        this.suggestLoading = false
      }).catch((error) => {
        this.suggestLoading = false
        api.notifyError(this.$notify, 'prompt 推荐失败', error)
      })
    }
  }
}
</script>

<style scoped>

</style>