<template>
    <div>
        <Layout>
            <Header style="background: #fff; border-bottom: 1px solid lightgray;">
                <Navigator @refresh="sync">
                    <template v-slot:inline-section-title>评测仓库</template>
                    <template v-slot:inline-section-place>
                        <el-select class="table-cell-section" v-model="repo" filterable placeholder="请选评测仓库">
                            <el-option v-for="spec in repoList" :key="spec" :label="spec" :value="spec"></el-option>
                        </el-select>
                    </template>
                </Navigator>
            </Header>
            <Layout>
            <Sider :style="{background: '#fff', width: '200px'}">
                <Menu theme="light" v-for="menu in menuChoice" :active-name="activateName"
                    width="auto" @on-select="handleSelect" :key="menu">
                    <MenuItem :name="menu">
                        {{menuChinese[menu]}}
                    </MenuItem>
                </Menu>
            </Sider>
            <Content>
                <prompt-template-simple v-if="activateName==='simple'" :repo="repo"></prompt-template-simple>
                <prompt-template-list v-if="activateName==='list'" :repo="repo"></prompt-template-list>
                <tool-template-simple v-if="activateName==='packed_tools'" :repo="repo"></tool-template-simple>
            </Content>
        </Layout>
        </Layout>
    </div>
</template>


<script>
import api from '../api'
import Navigator from '../Navigator.vue'
import PromptTemplateSimple from '../PromptSuggestion/PromptTemplateSimple.vue'
import PromptTemplateList from '../PromptSuggestion/PromptTemplateList.vue'
import ToolTemplateSimple from '../PromptSuggestion/ToolTemplateSimple.vue'

export default {
  components: {
    Navigator,
    PromptTemplateSimple,
    PromptTemplateList,
    ToolTemplateSimple
  },
  data() {
    return {
      repo: 'ssh://*******************/hbqa/llmeval-example.git',
      repoList: [],
      activateName: 'simple',
      loading: false,
      menuChoice: ['simple', 'list', 'packed_tools'],
      menuChinese: {
        'simple': '简单评测',
        'list': '先分解后评测',
        'packed_tools': '已有能力市场'
        // 'compress_first': '先压缩再评测',
        // 'split_first': '先拆分后遍历评测'
      }
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    sync() {
      api.getWorkflowRepo().then((result) => {
        this.repoList = result.data
        if (this.repoList.length !== 0) {
          this.repo = this.repoList[0]
        }
      })
    },
    handleSelect(name) {
      this.activateName = name
    }
  }
}
</script>