<template>
    <div>
        <div class="tool-filters">
            <mtd-select v-model="selectedToolType" style="width: 200px; margin-right: 20px;">
                <mtd-option value="registred_tools" label="完整工具" />
                <mtd-option value="half_registred_tools" label="待完善工具" />
                <mtd-option value="not_registred_tools" label="未追踪工具" />
            </mtd-select>
            <mtd-select v-model="selectedToolCategory" multiple show-select-all clearable style="width: 400px;">
                <mtd-option value="Scorers" label="评测方法" />
                <mtd-option value="APICompletionFns" label="Bot接口方法" />
                <mtd-option value="FileAdaptors" label="数据构建方法" />
            </mtd-select>
        </div>
        <mtd-loading v-if="loading" />
        <div class="tool-cards-container">
            <mtd-card v-for="(value, name) in filteredTools" :key="name" class="tool-card">
                <template #title>
                    <div class="tool-card-header">
                        <h5>工具：{{ truncateText(value.desc|| name || '暂无描述', 7) }}</h5>
                    </div>
                </template>
                <template #extra>
                    <mtd-button size="small" type="primary" @click="selectTool(value)">选择</mtd-button>
                    <mtd-button size="small" @click="showArgs(value)">参数</mtd-button>
                </template>
                <template #default>
                    <p class="mutiline-paragraph"><strong>类型：</strong>
                        <mtd-tag theme="blue" class="mutiline-paragraph">{{ getToolName(value.tool_type) }}</mtd-tag>
                    </p>
                    <p class="mutiline-paragraph"><strong>名称：</strong>{{ name }}</p>
                    <p class="mutiline-paragraph"><strong>工具用途：</strong>{{ value.desc }}</p>
                    <p class="mutiline-paragraph"><strong>类路径：</strong>{{ value.cls }}</p>
                    
                </template>
            </mtd-card>
        </div>
        <mtd-modal v-model="showArgsModal" title="参数详情">
            <mtd-list v-if="shownValue.args">
                <mtd-list-item v-for="(argValue, argName) in shownValue.args" :key="argName">
                <pre>{{ argName }}: {{ argValue }}</pre>
                </mtd-list-item>
            </mtd-list>
            <el-empty v-else description="尚未配置参数"/>
        </mtd-modal>
    </div>
</template>

<script>
import api from '../api'
export default {
  props: {
    repo: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      allTools: {},
      selectedToolType: 'half_registred_tools',
      selectedToolCategory: ['Scorers', 'APICompletionFns'],
      filteredTools: {},
      shownValue: {},
      showArgsModal: false,
      loading: false // 加载状态
    }
  },
  watch: {
    // 监听repo变化
    repo(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.allTools = {}
        this.fetchTools()
      }
    },
    selectedToolType() {
      this.fetchTools()
    },
    selectedToolCategory() {
      this.fetchTools()
    }
  },
  computed: {

  },
  mounted() {
    this.allTools = {}
    this.fetchTools()
  },
  methods: {
    showArgs(value) {
      this.shownValue = value
      this.showArgsModal = true
    },
    getToolName(name) {
      switch (name) {
        case 'Scorers':
          return '评测方法'
        case 'APICompletionFns':
          return 'Bot接口方法'
        case 'FileAdaptors':
          return '数据构建方法'
        default:
          return name
      }
    },
    truncateText(text, maxLength) {
      if (text.length > maxLength) {
        return text.substring(0, maxLength) + '...'
      }
      return text
    },
    getFilteredTools() {
      const tools = this.allTools[this.selectedToolType] || {}
      return Object.fromEntries(
        Object.entries(tools).filter(([_, value]) =>
          this.selectedToolCategory.includes(value.tool_type)
        )
      )
    },
    selectTool(value) {
      console.log('ToolListCard: ', value)
      this.$emit('select', value)
    },
    async fetchTools() {
      if (!this.allTools.hasOwnProperty(this.selectedToolType)) {
        this.loading = true // 开始加载
        this.filteredTools = {}
        try {
          const response = await api.getToolsOnline(this.repo, this.selectedToolType)
          this.allTools[this.selectedToolType] = response.data[this.selectedToolType]
        } catch (error) {
          api.notifyError(this.$notify, '仓库未注册', Error('没有对应的数据'))
          this.allTools = {}
        } finally {
          this.loading = false // 加载结束
        }
      }
      this.filteredTools = this.getFilteredTools()
    }
  }
}
</script>

<style scoped>
.tool-filters {
  display: flex;
  margin-bottom: 20px;
}
.tool-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.tool-card {
    width: 300px;
}

.tool-card-header {
    display: flex;
    align-items: center;
}
.mutiline-paragraph {
    word-break: break-all; /* 允许在单词内换行 */
    white-space: normal; /* 默认的空白处理方式，允许空白处换行 */
}
</style>