<template>
    <div>
        <mtd-tabs v-model="activeName">

            <mtd-tab-pane label="上传数据" value="upload">
                <mtd-announcement title="说明" type="info" show-icon>
                    <template #description>
                        1. 上传数据主要用于把数据内容告诉前端，上传的文件并不会落库，所以可以放心多次上传<br>
                        2. 数据量的限制最多为 10 条，如果希望跑更多的评测，请参考<a target="_blank" href="https://km.sankuai.com/collabpage/2184039381"><span class="text-head" style="color: #E65100;">快速开始</span></a><br>
                        3. json 格式的数据不适合在表格中展示，请到“手工数据”中查看
                    </template>
                </mtd-announcement>
                <div style="height: 10px;"></div>
                <mtd-form inline>
                    <mtd-form-item label="上传文件"><mtd-upload accept=".xlsx, .csv, .json, .jsonl" action="" :before-upload="handleBeforeUpload"/></mtd-form-item>
                    <mtd-form-item label="数量限制"><mtd-input-number v-model="limit" :min="1" :max="10"></mtd-input-number></mtd-form-item>
                </mtd-form>
                <mtd-table :data="data" show-overflow-tooltip bordered>
                    <mtd-table-column :prop="header" :label="header" v-for="header in headers" :key="header"/>
                </mtd-table>
            </mtd-tab-pane>
            <mtd-tab-pane label="HTTP接口" value="http-completion-fn">
                <http-completion-fn-tool :tool="tool" :toolName="toolName" :data="data"
                @done="emitHttp"></http-completion-fn-tool>
            </mtd-tab-pane>
            <mtd-tab-pane label="字段解析" value="field-phrase-tool">
                <field-phrase-trans-tool :data="data"
                @done="emitHttp"></field-phrase-trans-tool>
            </mtd-tab-pane>
            <mtd-tab-pane label="手工数据" value="random">
                <mtd-announcement title="提示" type="info" show-icon>
                    <template #description>
                        1. 手工数据能够帮助你快速设计数据入参，通过随机拉取已有评测的数据信息，可以获得一些 mock 数据<br>
                        2. 新增数据按钮，会增加数据条目，尽量不要新增太多数据
                    </template>
                </mtd-announcement>
                <div style="height: 10px;"></div>
                <mtd-form inline>
                    <mtd-form-item label="新增字段">
                        <mtd-button type="primary" icon="add" shape="circle" @click="addItem"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="数据条目">
                        <mtd-input-number v-model="currentStep" :min="1" :max="dataItem.length"></mtd-input-number>
                    </mtd-form-item>
                    <mtd-form-item label="新增数据">
                        <mtd-button type="secondary" icon="add" shape="circle" @click="addData"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="删除数据">
                        <mtd-button type="warning" icon="mtdicon-delete" shape="circle" @click="deleteData(currentStep-1)"></mtd-button>
                    </mtd-form-item>
                </mtd-form>
                <mtd-card class="card-box" allow-create shadow="hover" v-for="(item, index) in dataItem[currentStep-1]" :key="index">
                    <template #title>
                        <mtd-select v-model="item.field"  placeholder="字段名"
                        allow-create filterable @create="handleCreate" @change="getRandom(index, currentStep-1, item.field)">
                            <mtd-option v-for="item in fieldOptions" :key="item.value" :label="item.label" 
                            :value="item.value" :disabled="item.disabled">
                                <div class="option-item">
                                    {{ item.label }}
                                    <mtd-icon-button v-if="item.customCreate" @click="(e) => removeOption(e, item.value)" class="remove-btn"
                                    type="secondary" icon="delete-o" />
                                </div>
                            </mtd-option>
                            <template #footer>
                                <div class="create-option">
                                    <mtd-input v-model="createName" style="width: 110px; margin-right:8px" />
                                    <mtd-button type="primary" @click="handleCreate(createName)" :disabled="!createName">新建</mtd-button>
                                </div>
                            </template>
                        </mtd-select>
                    </template>
                    <json-inputer v-if="typeof item.content === 'object' || Array.isArray(item.content)" 
                        v-model="item.content" valid style="height: 300px;"></json-inputer>
                    <Input v-else title="文本内容" type="textarea" autosize multi-line clearable v-model="item.content" placeholder="内容"></Input>
                    <template #extra>
                        <mtd-button @click="getRandom(index, currentStep-1, item.field)" type="primary"><Icon type="md-infinite" />随机</mtd-button>
                        <mtd-button @click="removeItem(index)" type="danger"><Icon type="md-trash" />删除</mtd-button>
                    </template>
                </mtd-card>
            </mtd-tab-pane>
        </mtd-tabs>
    </div>
</template>

<script>
import api from '../api'
import Papa from 'papaparse'
import * as XLSX from 'xlsx'
import HttpCompletionFnTool from '../Tools/HttpCompletionFnTool.vue'
import FieldPhraseTransTool from '../Tools/FieldPhraseTransTool.vue'
import JsonInputer from '../Tools/JsonInputer.vue'

export default{
  props: {
    data: Array,
    tool: Object,
    toolName: {
      type: String,
      default() {
        return 'HTTP-API'
      }
    }
  },
  data() {
    return {
      fieldOptions: [],
      dataItem: [[
        {'field': 'poiId', 'content': '1457375'},
        {'field': 'poiName', 'content': '北京昆泰酒店'},
        {'field': 'userInput', 'content': '怎么联系'},
        {'field': 'userId', 'content': '1'},
        {'field': 'token', 'content': '1'},
        {'field': '类型', 'content': '联系'},
        {'field': '备注', 'content': '010-86301234'}
      ]],
      createName: '',
      activeName: 'upload',
      uploadDataList: [],
      limit: 1,
      currentStep: 1
    }
  },
  components: {
    HttpCompletionFnTool,
    JsonInputer,
    FieldPhraseTransTool
  },
  computed: {
    headers() {
      const keysSet = new Set() // 使用Set来自动去重
      this.data.forEach(item => {
        Object.keys(item).forEach(key => {
          keysSet.add(key)
        })
      })
      return Array.from(keysSet) // 将Set转换为Array返回
    }
  },
  mounted() {
    this.syncRandomData(this.data)
    this.uploadDataList = this.data
    this.sync()
  },
  watch: {
  },
  methods: {
    sync() {
      this.suggestFields()
    },
    syncRandomData(data) {
      this.dataItem = data.map(d => Object.entries(d).map((value) => {
        return {
          'field': value[0],
          'content': value[1]
        }
      }))
    },
    emitByRandom() {
      const dataItem = this.dataItem.map(value => value.reduce((acc, item) => {
        acc[item.field] = item.content
        return acc
      }, {}))
      this.emitChange(dataItem)
    },
    emitByUpload() {
      const data = this.uploadDataList.slice(0, this.limit)
      this.emitChange(data)
      this.syncRandomData(data)
    },
    emitHttp(data) {
      this.emitChange(data)
      this.syncRandomData(data)
      this.activeName = 'random'
    },
    emitChange(dataList) {
      this.$emit('change', dataList)
    },
    getRandom(index, step, field) {
      const fieldLower = field.toLowerCase()
      const relatedFields = this.fieldOptions.filter(option => option.value.toLowerCase().includes(fieldLower)).map(option => option.value)
      relatedFields.push(field)
      api.randomDataField(relatedFields, 10).then(resp => {
        this.dataItem[step][index].content = resp.data.phases[0]
        this.emitByRandom()
      }).catch((error) => {
        this.suggestLoading = false
        api.notifyError(this.$notify, '内容生成失败', error)
      })
    },
    removeOption(e, val) {
      e.stopPropagation()
      this.fieldOptions.splice(this.fieldOptions.findIndex(item => item.value === val), 1)
    },
    handleCreate(val) {
      this.fieldOptions.push({
        label: val,
        value: val,
        customCreate: true // 用户自定义的属性
      })
    },
    getBucketKeys(aggr) {
      if (!aggr) {
        return []
      }
      const buckets = aggr.buckets
      return buckets.map(bucket => { return {value: bucket.key, label: bucket.key} })
    },
    suggestFields() {
      api.suggestDataFields().then(resp => {
        const data = resp.data
        this.fieldOptions = this.getBucketKeys(data.aggregations.fields)
      }).catch((error) => {
        this.suggestLoading = false
        api.notifyError(this.$notify, 'fields 推荐失败', error)
      })
    },
    addData() {
      const newItem = this.headers.map((header, ind) => {
        return {field: header, content: ''}
      })
      this.dataItem.push(newItem)
      this.currentStep = this.dataItem.length
      this.emitByRandom()
    },
    deleteData(ind) {
      this.dataItem.splice(ind, 1)
      this.currentStep = this.currentStep - 1
      this.emitByRandom()
    },
    addItem() {
      this.dataItem.forEach(subArray => { subArray.unshift({ role: '', content: '' }) })
      this.emitByRandom()
    },
    removeItem(index) {
      this.dataItem.forEach(subArray => { subArray.splice(index, 1) })
      this.emitByRandom()
    },
    handleBeforeUpload(file) {
      this.handleUpload(file)
      // 返回false以阻止文件自动上传
      return false
    },
    handleUpload(file) {
      const fileType = file.name.split('.').pop().toLowerCase()
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = e.target.result
        let upData = []
        switch (fileType) {
          case 'xlsx':
            upData = this.parseExcel(data)
            break
          case 'csv':
            upData = this.parseCSV(data)
            break
          case 'json':
            upData = this.parseJSON(data)
            break
          case 'jsonl':
            upData = this.parseJSONL(data)
            break
          default:
            console.error('不支持的文件类型')
        }
        this.uploadDataList = upData
        this.emitByUpload()
      }

      if (fileType === 'xlsx') {
        reader.readAsBinaryString(file)
      } else {
        reader.readAsText(file)
      }
    },
    parseExcel(data) {
      const workbook = XLSX.read(data, {type: 'binary'})
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet)
      return jsonData
    },
    parseCSV(data) {
    // 直接使用CSV解析库或自定义解析逻辑
      return Papa.parse(data).data
    },
    parseJSON(data) {
      const jsonData = JSON.parse(data)
      return jsonData
    },
    parseJSONL(data) {
    // JSONL格式是每行一个JSON对象
      const lines = data.split('\n').filter(line => line.trim())
      const jsonData = lines.map(line => JSON.parse(line))
      return jsonData
    }
  }
}
</script>

<style scoped>

</style>