<template>
    <div>
        <mtd-card>
            <template #title >
                先分解后评测
            </template>
            <flow-chart-row>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'data-design')":focus="activateName==='data-design'"
                    laneType="" right label="1">确定数据</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'sp-prompt-design')" :focus="activateName==='sp-prompt-design'"
                    laneType="" left right label="2">句子拆分</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col>
                    <flow-chart-lane :check="handleSelect.bind(this, 'check-prompt-design')" :focus="activateName==='check-prompt-design'"
                    laneType="" left right label="3">单项校验</flow-chart-lane>
                </flow-chart-col>
                <flow-chart-col end>
                    <flow-chart-lane :check="handleSelect.bind(this, 'run')":focus="activateName==='run'"
                    laneType="" left label="4">执行评测</flow-chart-lane>
                </flow-chart-col>
            </flow-chart-row>
            <Divider/>
            <prompt-suggest-card  v-model="currentPrompt" key="check-prompt-design" :defaultConfigs="designPromptConfig"
                v-if="activateName==='check-prompt-design'" :repo="repo"></prompt-suggest-card>
            <prompt-suggest-card  v-model="currentPromptSP" key="sp-prompt-design" :defaultConfigs="designPromptConfigSP"
                v-else-if="activateName==='sp-prompt-design'" :repo="repo"></prompt-suggest-card>
            <data-design-card v-else-if="activateName==='data-design'" :data="inputData" :tool="apiTool" :toolName="toolName" @change="handleChange"></data-design-card>
            <div v-else>
                <mtd-form inline>
                    <mtd-form-item label="切换展示">
                        <mtd-button @click="handleSwitch" circle icon="mtdicon-share-arrow-fill" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="开始执行">
                        <mtd-button @click="handleRun" circle icon="mtdicon-paperplane" type="primary"></mtd-button>
                    </mtd-form-item>
                    <mtd-form-item label="进度">
                        <mtd-progress style="width: 260px;" type="steps" size="small" 
                        :steps="inputData.length" 
                        :percentage="runningStep/inputData.length* 100"
                        :show-info="false"
                        class="progress-base">
                        </mtd-progress>
                        <span>{{ runningStep }} / {{ inputData.length }}</span>
                    </mtd-form-item>
                </mtd-form>
                <div v-if="!showCoding">
                    <Divider>预期字段配置</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            一预期的字段可能会包装在一个 json 中，可以用这个配置找到对应位置数据。请注意，预期字段必须能够解析为字符串或者列表，如果列表里面的是json，那么会转换为字符串直接输入
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <mtd-form inline>
                        <mtd-form-item label="预期字段" required helper="请填写一个待解析的字符串或者 json 对象的字段名称">
                            <mtd-input v-model="fieldPrepareIdeal.field_name" style="width: 500px;"
                            placeholder="field_name"></mtd-input>
                        </mtd-form-item>
                        <mtd-form-item label="回填字段" required helper="请填写一个空的字段名称，会把对预期字段的识别内容填入此处，预计是一个 list">
                            <mtd-input v-model="fieldPrepareIdeal.output_field_name" style="width: 500px;"
                            placeholder="output_field_name"></mtd-input>
                        </mtd-form-item>
                        <mtd-form-item label="识别模式" required>
                            <mtd-select v-model="fieldPrepareIdeal.recognize" :options="recognizeOptions" style="width: 200px;">
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="jpath" v-if="fieldPrepareIdeal.recognize=='json'">
                            <template #helper>
                                用于识别这个字段的内容，如果写 . 那么就是返回值的 json 整体，<br>
                                如果填 $.data 就是 json 对象的 data 字段。建议先填 . 看下效果。
                            </template>
                            <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                            v-model="fieldPrepareIdeal.jpath" :options="jpathPatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式" v-if="fieldPrepareIdeal.recognize=='mutiline'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create
                            v-model="fieldPrepareIdeal.pattern" :options="rePatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName2" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                    </mtd-form>
                    <Divider>句子拆分配置</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            大模型对句子拆分的结果，依旧是一个字符串，一般会用过解析 json 的方式获取拆分结果列表，请注意句子拆分的结果，必须识别为一个列表
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <mtd-form inline>
                        <mtd-form-item label="模型">
                            <mtd-select v-model="sp_model" placeholder="请选择模型" filterable :options="modelOptions"/>
                        </mtd-form-item>
                        <mtd-form-item label="识别模式" required>
                            <mtd-select v-model="fieldPrepareSP.recognize" :options="recognizeOptions" style="width: 200px;">
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="jpath" v-if="fieldPrepareSP.recognize=='json'">
                            <template #helper>
                                用于识别这个字段的内容，如果写 . 那么就是返回值的 json 整体，<br>
                                如果填 $.data 就是 json 对象的 data 字段。建议先填 . 看下效果。
                            </template>
                            <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                            v-model="fieldPrepareSP.jpath" :options="jpathPatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式" v-if="fieldPrepareSP.recognize=='mutiline'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create
                            v-model="fieldPrepareSP.pattern" :options="rePatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName2" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="回填字段" required helper="请填写一个空的字段名称，会把拆分结果填入此处，也与单项校验的入参一致">
                            <mtd-input v-model="fieldPrepareSP.output_field_name" style="width: 500px;"
                            placeholder="output_field_name"></mtd-input>
                        </mtd-form-item>
                    </mtd-form>
                    <Divider>单项校验配置</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            大模型会遍历每一个 (预期/拆分) 对，使用单项校验的 prompt 进行分析，并且通过结果遍历的规则，获得最终结果
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <mtd-form inline>
                        <!-- <mtd-form-item label="Scorer">
                            <mtd-select disabled v-model="scorerChoice" placeholder="请选择Scorer" filterable :options="scorerOptions"/>
                        </mtd-form-item> -->
                        <mtd-form-item label="模型">
                            <mtd-select v-model="model" placeholder="请选择模型" filterable :options="modelOptions"/>
                        </mtd-form-item>
                        <mtd-form-item label="识别模式" required>
                            <mtd-select v-model="score_result_recognization" :options="recognizeOptions" style="width: 200px;">
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="jpath聚焦" helper="关注输出 json 的内容" v-if="score_result_recognization==='json'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                            v-model="score_jpath" :options="jpathPatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="行号聚焦" helper="关注多行输出的第几行" v-if="score_result_recognization==='mutiline'">
                            <mtd-input-number v-model="score_line_num" :min="0"/>
                        </mtd-form-item>
                        <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式" v-if="score_result_recognization==='mutiline'">
                            <mtd-select style="width: 300px;" filterable clearable allow-create
                            v-model="score_re_pattern" :options="rePatterns">
                                <template #footer>
                                    <div class="create-option">
                                        <mtd-input v-model="createName2" style="margin-right:8px" />
                                        <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                                    </div>
                                </template>
                            </mtd-select>
                        </mtd-form-item>
                        <mtd-form-item label="结果遍历" helper="请选择一种遍历结果的方式">
                            <mtd-select v-model="aggr_method" style="width: 300px;"
                            placeholder="结果遍历" :options="aggrMethodOptions"></mtd-select>
                        </mtd-form-item>
                    </mtd-form>
                    <Divider>得分映射关系</Divider>
                    <mtd-announcement title="说明" type="info" show-icon>
                        <template #description>
                            得分映射关系是一个字典，如果聚焦的内容命中了字典的某个 key，那么得分就是相应的 value
                        </template>
                    </mtd-announcement>
                    <div style="height: 10px;"></div>
                    <string-dict-editor v-model="score_mapping"/>
                    <Divider>执行结果</Divider>
                    <mtd-loading :loading="loading">
                        <el-empty v-if="resultItem.length===0" description="缺少数据"></el-empty>
                        <mtd-card v-else v-for="(item,ind) in resultItem" :key="ind" shadow="hover" :title="`编号${ind}`">
                            <message-box :item="item" :frontEndConfig="frontEndConfig"></message-box>
                        </mtd-card>
                    </mtd-loading>
                </div>
                <div v-else>
                    <div v-if="!coding">
                        <el-empty v-if="resultItem.length===0" description="缺少 prompt"></el-empty>
                    </div>
                    <pre v-else>{{ coding }}</pre>
                </div>
            </div>
        </mtd-card>
    </div>
</template>


<script>
import api from '../api'
import FlowChartCol from '../../FlowChart/FlowChartCol.vue'
import FlowChartRow from '../../FlowChart/FlowChartRow.vue'
import FlowChartLane from '../../FlowChart/FlowChartLane.vue'
import PromptSuggestCard from '../PromptSuggestion/PromptSuggestCard.vue'
import DataDesignCard from '../PromptSuggestion/DataDesignCard.vue'
import MessageBox from '../Tools/MessageBox.vue'
import JsonInputer from '../Tools/JsonInputer.vue'
import StringDictEditor from '../Tools/StringDictEditor.vue'

export default {
  components: {
    FlowChartCol,
    FlowChartRow,
    FlowChartLane,
    PromptSuggestCard,
    DataDesignCard,
    MessageBox,
    JsonInputer,
    StringDictEditor
  },
  props: {
    repo: String
  },
  watch: {
    currentPromptSP(value) {
      console.log('currentPromptSP', value)
      this.sp_model = value.recommand_model
      if (value.result_recognization.includes('json')) {
        this.fieldPrepareSP.recognize = 'json'
        this.fieldPrepareSP.jpath = value.jpath
      } else {
        this.fieldPrepareSP.recognize = 'mutiline'
        this.fieldPrepareSP.pattern = value.repattern
      }
    },
    currentPrompt(value) {
      console.log('currentPrompt', value)
      this.model = value.recommand_model
      if (value.result_recognization.includes('json')) {
        this.score_result_recognization = 'json'
        this.score_jpath = value.jpath
      } else {
        this.score_result_recognization = 'mutiline'
        this.score_re_pattern = value.repattern
      }
    }
  },
  data() {
    return {
      recognizeOptions: [{'value': 'json', 'label': 'json'}, {'value': 'mutiline', 'label': '多行'}, {'value': 'none', 'label': '无'}],
      activateName: 'data-design',
      score_result_recognization: 'json',
      currentPrompt: null,
      currentPromptSP: null,
      fieldPrepareSP: {
        'output_field_name': '_splited',
        'recognize': 'json'
      },
      fieldPrepareIdeal: {
        'field_name': 'ideal',
        'output_field_name': 'ideal_phrased',
        'recognize': 'none'
      },
      designPromptConfigSP: {
        scene: ['拆分', '问题生成', '生成问题', '命名实体识别'],
        recommand_model: ['deepseek-chat']
      },
      designPromptConfig: {
        scene: ['Relevance', 'Truth'],
        recommand_model: ['deepseek-chat']
      },
      aggr_method: 'minmax',
      currentData: null,
      inputData: [{
        'poiId': '1457375',
        'poiName': '北京昆泰酒店',
        'userInput': '怎么联系',
        'userId': '1',
        'token': '1',
        '类型': '联系',
        '备注': '010-86301234'
      }],
      score_jpath: '.',
      score_line_num: 0,
      model: 'deepseek-chat',
      modelOptions: [],
      score_mapping: {'是': 1, '否': 0},
      score_re_pattern: '回答[：:]\\s*([\\u4E00-\\u9Fa5]+)',
      scorerChoice: 'toolchain_llmeval.workflow.workflow_tools.scorer.online:DefaultSplitChatScorer',
      scorerOptions: [{label: 'DefaultSplitChatScorer', value: 'toolchain_llmeval.workflow.workflow_tools.scorer.online:DefaultSplitChatScorer'}],
      aggrMethodOptions: [
        {label: '任何 pair 都相关', value: 'min'},
        {label: '有一个 pair 相关', value: 'max'},
        {label: '有一个回答在预期中就算通过', value: 'minmax'},
        {label: '有一个回答不在预期中就算不通过', value: 'maxmin'}
      ],
      frontEndConfig: {},
      resultItem: [],
      loading: false,
      showCoding: false,
      toolName: 'HTTP-API',
      createName: '',
      createName2: '',
      apiTool: {
        cls: 'toolchain_llmeval.workflow.workflow_tools.api_completion_fns.default:HTTPCompletionFn',
        args: {
          host: 'https://fuxi.sankuai.com',
          uri: '/api/flow/run',
          method: 'POST',
          completion_jpath: '.',
          param_template: null,
          env: 'prod',
          body_template: '{\n  "flowId": 263, \n  "inputParams": {\n    "poiName": "{poiName}",  \n    "historyMessage":[],\n    "poiId": "{poiId}", \n    "userInput": "{userInput}", \n    "userId": "{userId}", \n    "token":"{token}"\n  }\n}',
          auth_method: 'shepherd',
          extension_output_field_jpath: {},
          lion_token_name: null,
          lion_mws_secretary_name: null,
          lion_oceanus_token_name: null,
          sso_field_name: null,
          server_appkey: null,
          timeout: 1200,
          headers: {},
          cookie: {}
        }
      },
      runningStep: 0,
      rePatterns: api.rePatterns,
      jpathPatterns: api.jpathPatterns,
      sp_model: 'deepseek-chat'
    }
  },
  computed: {
    codingArgs() {
      return {
        'prompt': {
          'messages': this.currentPrompt.messages,
          'search_text': this.currentPrompt.search_text,
          'result_recognization': this.currentPrompt.result_recognization
        },
        model: this.model,
        sp_model: this.sp_model,
        score_jpath: this.score_jpath,
        score_line_num: this.score_line_num,
        score_mapping: this.score_mapping,
        score_re_pattern: this.score_re_pattern,
        sp_prompt: {
          'messages': this.currentPromptSP.messages,
          'search_text': this.currentPromptSP.search_text,
          'result_recognization': this.currentPromptSP.result_recognization
        },
        score_result_recognization: this.score_result_recognization,
        sp_phrase_config: this.fieldPrepareSP,
        aggr_method: this.aggr_method,
        ideal_phrase_config: this.fieldPrepareIdeal
      }
    },
    coding() {
      if (!this.currentPrompt || !this.currentPromptSP) {
        return ''
      }
      const name = this.currentPrompt.recommand_name
      return {
        'Scorers': {[name]: {
          'cls': this.scorerChoice,
          'args': this.codingArgs
        }}
      }
    }
  },
  mounted() {
    this.sync()
  },
  methods: {
    sync() {
      api.enumModels().then(resp => {
        this.modelOptions = resp.data.models.map(v => {
          return {'label': v, 'value': v}
        })
      })
    },
    validateJSON(input) {
      try {
        JSON.parse(input)
        return true
      } catch (error) {
        return false
      }
    },
    handleSwitch() {
      this.showCoding = !this.showCoding
    },
    handleSelect(name) {
      this.activateName = name
    },
    handleChange(value) {
      this.inputData = value
    },
    step() {
      this.$emit('step')
      this.runningStep = this.runningStep + 1
    },
    done(data) {
      this.$emit('done', data)
    },
    begin() {
      this.$emit('begin')
      this.runningStep = 0
    },
    async handleRun() {
      this.begin()
      if (!this.inputData || this.inputData.length === 0) {
        api.notifyError(this.$notify, '当前数据条目为 0 条', Error('缺少数据'))
        this.done([])
        return
      }
      this.resultItem = []
      for (const item of this.inputData) {
        try {
          const result = await api.onlineScorerRun({
            'tool': {
              'tool_name': 'online-scorer',
              'cls': this.scorerChoice,
              'args': this.codingArgs

            },
            'data_list': [item]
          })
          this.resultItem.push(...result.data.data_items)
        //   this.frontEndConfig = result.data.data_list.front_end_config
          Object.keys(result.data.data_items[0]).forEach(key => {
            if (['__change_log__', '__es_id__', '__id__', 'completion', 'eval_messages', 'messages', 'model_graded', 'score'].includes(key)) {
              this.frontEndConfig[key] = {'show': false}
            } else {
              this.frontEndConfig[key] = {'show': true}
            }
          })
          this.step()
        } catch (error) {
          api.notifyError(this.$notify, '执行失败', error)
          this.step()
        }
      }
      this.done(this.resultItem)
    }
  }
}
</script>