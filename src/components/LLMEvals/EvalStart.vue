<template>
    <div>
        <Navigator @refresh="refresh">
            <template v-slot:inline-section-title>评测仓库</template>
            <template v-slot:inline-section-place>
                <el-select class="table-cell-section" v-model="repo" filterable placeholder="请选评测仓库">
                    <el-option v-for="spec in repoList" :key="spec" :label="spec" :value="spec"></el-option>
                </el-select>
            </template>
        </Navigator>
        <div class="container">
            <h1 class="title">LLMEvals开始测评</h1>
            <div class="upload-section">
                <el-divider>报告配置</el-divider>
                <el-input class="select-section" v-model="wikiTitle" placeholder="请输入wiki报告名称">
                    <template slot="prepend">报告名称</template>
                </el-input>
                <el-input class="select-section" v-model="wikiDesc" placeholder="请输入简要描述">
                    <template slot="prepend">简要描述</template>
                </el-input>
                <el-input class="select-section" label="学城父文档ID" v-model="parent_wiki" placeholder="请输入学城父文档id">
                    <template slot="prepend">学城父文档ID</template>
                </el-input>
                <div class="el-input-group__prepend">API并发数</div>
                <el-input-number class="table-cell-section" controls-position="right" v-model="completion_threads" placeholder="">
                </el-input-number>
                <el-divider>评测配置</el-divider>
                <el-card v-for="(item, ind) in evaluations" :key="item.key" class="card-box">
                    <div slot="header" class="clearfix">
                        <span class="card-title">评测-{{ ind + 1 }}</span>
                        <el-button class="right-button" type="text" icon="el-icon-minus"
                            @click="removeReplacer(ind)"></el-button>
                        <el-button class="right-button" type="text" icon="el-icon-plus" @click="addReplacer"></el-button>
                    </div>
                    <div class="inline-section">
                        <div class="el-input-group__prepend">评测名称</div>
                        <el-select class="table-cell-section" v-model="evaluations[ind].key" filterable
                            placeholder="请选择评测名称">
                            <el-option v-for="spec in evalSpecList" :key="spec.key"
                                :label="get_eval_name_display(spec)" :value="spec.key"></el-option>
                        </el-select>
                    </div>
                    <el-upload class="upload-demo" :action="get_upload_base(item)" ref="rebateUpload" :headers="upload_header"
                        :on-remove="handleRemove.bind(this, item)" :on-success="handleSuccess.bind(this, item)"
                        :before-remove="beforeRemove.bind(this, item)" :on-exceed="handleExceed.bind(this, item)"
                        :on-error="handleError.bind(this, item)" :on-change="handleChange.bind(this, item)"
                        :on-preview="handlePreview.bind(this, item)">
                        <el-button size="small" type="primary">点击上传文件</el-button>
                    </el-upload>

                </el-card>
            </div>
            <el-divider>操作</el-divider>
            <div class="submit-section">
                <el-button type="primary" @click="submitTask">提交任务</el-button>
            </div>
        </div>
    </div>
</template>


<script>
import api from '../LLMEvals/api'
import Navigator from '../LLMEvals/Navigator.vue'
export default {
  components: {
    Navigator
  },
  data() {
    return {
      repoList: [],
      repo: '',
      selectedEvaluation: '',
      evalSpecList: [],
      parent_wiki: '1893747845',
      completion_threads: 1,
      evaluations: [
                { key: '', files: [] }
      ],
      wikiTitle: '',
      wikiDesc: '',
      upload_header: api.localHeaders
    }
  },
  mounted() {
    this.refresh()
  },
  watch: {
    repo(newRepo) {
      api.getEvalSpec(newRepo).then(result => {
        this.evalSpecList = result.data.data
        console.log(this.evalSpecList)
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 evals 评测配置集失败', error)
      })
    }
  },
  methods: {
    get_eval_name_display(spec) {
      if (spec.metric_chinese_name) {
        return `${spec.metric_chinese_name}(${spec.key})`
      } else {
        return spec.key
      }
    },
    refresh() {
      this.sync()
      const currentDate = new Date()
      const year = currentDate.getFullYear() // 获取当前年份
      const month = currentDate.getMonth() + 1 // 获取当前月份（注意月份是从0开始计数的，所以要加1）
      const day = currentDate.getDate()
      const hour = currentDate.getHours() // 获取当前小时
      const minute = currentDate.getMinutes() // 获取当前分钟
      const second = currentDate.getSeconds() // 获取当前秒数
      this.wikiTitle = `LLM 评测 - ${year}/${month}/${day} - ${hour}:${minute}:${second}`
      this.wikiDesc = this.wikiTitle
      this.evaluations = [{ key: '', files: [] }]
    },
    get_upload_base(item) {
      return api.LLM_EVAL_URL() + '/registry/data/upload' + '?eval_name=' + item.key
    },
    sync() {
      api.getRepo().then((result) => {
        this.repoList = result.data
        this.repo = this.current_repo
      })
      api.getEvalSpec(this.repo).then(result => {
        this.evalSpecList = result.data.data
        console.log(this.evalSpecList)
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 evals 评测配置集失败', error)
      })
    },
    addReplacer() {
      this.evaluations.push({ key: null, files: [] })
    },
    removeReplacer(ind) {
      this.evaluations.splice(ind, 1)
    },
    submitTask() { // 处理提交任务的逻辑
      console.log(this.evaluations)
      const metrics = this.evaluations.map(item => item.key)
      const replacerDictSamplesJsonl = this.evaluations.reduce((accumulator, item) => {
        if (item.files.length > 0) {
          accumulator[item.key] = item.files[0].name
        }
        return accumulator
      }, {})
      let payload = {
        'metrics': metrics,
        'parent_wiki': this.parent_wiki,
        'force_complete': true,
        'data_sync': 'load',
        'completion_threads': this.completion_threads,
        'report_title': this.wikiTitle,
        'report_description': this.wikiDesc,
        'replacer_dict_samples_jsonl': replacerDictSamplesJsonl
      }
      api.taskSubmit(payload, this.repo).then(res => {
        this.$notify({ title: '任务提交成功', message: res.statusText, type: 'success' })
      }).catch(error => {
        api.notifyError(this.$notify, '任务提交失败', error)
      })
    },
    handleRemove(item, file, fileList) {
      console.log('handleRemove')
      console.log(file, fileList)
      item.files = fileList
    },
    handlePreview(item, file) {
      window.open(file.url)
    },
    handleChange(item, file, fileList) {
      console.log('handleChange')
      if (fileList.length > 1) {
        fileList.splice(0, 1)  // 删除之前那个文件
      }
      item.files = fileList
    },
    handleError(item, response, files, fileList) {
      console.log('handleError')
      this.$notify({ title: '上传失败', message: response, type: 'error' })
    },
    handleExceed(item, files, fileList) {
      console.log('handleExceed')
      this.$notify({ title: '文件太多', message: '当前限制选择 1 个文件，请删除后上传', type: 'warning' })
      item.files = files
    },
    beforeRemove(item, file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleSuccess(item, response, file, fileList) {
      console.log('handleSuccess')
      console.log(response)
      file.name = response.data.file_name
      file.url = response.data.resource_url
      console.log(fileList)
      item.files = fileList
    }
  }
}
</script>


<style>
.el-upload input {
    display: none !important;
}

.el-card__header {
    padding: 6px 20px !important;
}

.el-divider__text {
    background-color: #f5f7f9 !important;
}
</style>
  
<style scoped>
.container {
    max-width: 700px;
    margin: 0 auto;
    padding: 20px;
}

.title {
    text-align: center;
    margin-bottom: 20px;
}

.upload-section {
    margin-bottom: 20px;
}

.select-section {
    margin-bottom: 20px;
    width: 100% !important;
}

.submit-section {
    text-align: center;
}


.table-cell-section {
    width: 100%;
    display: table-cell;
}

.inline-section {
    display: inline-block;
    width: 100%;
    /* 根据需要调整宽度 */
    margin-right: 10px;
    margin-bottom: 20px;
    /* 根据需要调整右侧间距 */
}

.card-title {
    font-size: large;
    font-weight: bold;
}

.right-button {
    float: right;
    padding: 5px;
}

.card-box {
    margin-bottom: 20px;
}
</style>