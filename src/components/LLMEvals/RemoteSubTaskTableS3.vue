<template>
    <div>
        <!-- <Navigator @refresh="refresh"></Navigator> -->
        <div class="container">
        <el-row>
            <el-col :span="4">
                <!-- <el-button type="primary" @click="search_tasks"><i class="el-icon-search"></i> 搜索</el-button> -->
                <el-button type="primary" @click="showDialog"><i class="el-icon-setting"></i>配置展示字段</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="siftChange(null,null)"><i class="el-icon-refresh-left"></i>清空筛选</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="siftChange(1,null)"><i class="el-icon-search"></i>筛选通过用例</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="siftChange(0,null)"><i class="el-icon-search"></i>筛选不通过用例</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="siftChange(null,1)" v-if="diff_task_id"><i class="el-icon-search"></i>新增通过</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="siftChange(null,0)" v-if="diff_task_id"><i class="el-icon-search"></i>新增不通过</el-button>
            </el-col>
        </el-row>
        <el-card v-if="shownData.length === 0">
            <div slot="header" class="detail-card-title">
                <span>提示信息</span>
            </div>
            <div style="text-align: center; padding: 20px;">
                暂无数据
            </div>
        </el-card>
        <el-card v-for="item, ind in shownData" v-loading="loading" :key="ind">
            <div slot="header" class="detail-card-title">
                <span>编号: {{ getDiffShowData(item, '__id__') }}</span>
            </div>
            <el-row>
                <el-col :span="12">
                    <div v-for="header in displayedHeaders" :key="header">
                        <h4>{{ headerMap[header] ? headerMap[header].label : header }}</h4>
                        <pre class="none-border-pre pre-scrollable-y" v-html="getDiffShowData(item, header)"></pre>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="chat-box">
                    <pre class="chat-message-input pre-scrollable-y" v-if="item.input"><em><b>INPUT</b></em><br>{{ getDiffShowData(item, 'input') }}</pre>
                    <pre class="chat-message-completion pre-scrollable-y" v-if="item.completion"><em><b>COMPLETION</b></em><br>{{ getDiffShowData(item, 'completion') }}</pre>
                    <pre class="chat-message-eval-reason pre-scrollable-y" v-if="item.eval_reason"><em><b>EVAL</b></em><br>{{ getDiffShowData(item, 'eval_reason') }}</pre>
                    <pre class="chat-message-eval-reason pre-scrollable-y" v-if="item['评测原因']"><em><b>EVAL</b></em><br>{{ getDiffShowData(item, '评测原因') }}</pre>
                    </div>
                </el-col>
            </el-row>
        </el-card>
        <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            @current-change="handleCurrentChange">
        </el-pagination>
        <el-dialog title="配置展示字段" :visible.sync="dialogVisible" width="30%">
            <el-checkbox-group v-model="selectedHeaders">
                <el-checkbox v-for="header in headers" :key="header" :label="header">
                    {{ headerMap[header] ? headerMap[header].label : header }}</el-checkbox>
            </el-checkbox-group>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">返回</el-button>
            </div>
        </el-dialog>
        </div>
    </div>
</template>


<script>
import Navigator from '../LLMEvals/Navigator.vue'

export default {
  props: {
    subtask: Object,
    task: Object,
    data: Array,
    tool: Object,
    loading: Boolean
  },
  components: {
    Navigator
  },
  data() {
    return {
      headerMap: {
        __id__: { label: '编号', width: 60 }, // 例如，宽度设置为100px
        score: {
          label: '得分',
          width: 100 // 设置宽度
        },
        __spend_seconds__: {
          label: 'API接口耗时(s)',
          width: 120 // 设置宽度
        },
        completion: { label: '机器人回复' },
        eval_reason: { label: '评测原因' },
        input: { label: '机器人输入' },
        ideal: { label: '预期回答ideal' },
        model_graded: { label: '评测Prompt' },
        update_time: { label: '更新时间' },
        评测原因: { label: '评测原因' }
      },
      headerWidth: {
      },
      taskList: [
                // 任务数据...
      ],
      dialogVisible: false,
      selectedHeaders: [],
      currentPage: 1,
      pageSize: 1,
      total: 0,
      siftScore: null,
      siftDiffScore: null
    }
  },
  watch: {
    data() {
      const baseHeaders = Object.keys(this.headerMap)
      if (this.data.length > 0) {
        this.selectedHeaders = Object.keys(this.data[0])
        .filter(header => baseHeaders.includes(header))
        .filter(header => !['model_graded', '__id__', '__es_id__', 'completion'].includes(header))
        this.total = this.data.length
      }
      this.currentPage = 1
    }
  },
  computed: {
    displayedHeaders() {
      return this.selectedHeaders
    },
    shownData() {
      if (this.siftScore !== null && this.siftScore !== undefined) {
        return this.data.filter(item => {
          const value = item.score
          if (typeof value !== 'object') {
            return value === this.siftScore
          }
          let {current} = value
          return current === this.siftScore
        })
      } else if (this.siftDiffScore !== null && this.siftDiffScore !== undefined) {
        return this.data.filter(item => {
          const value = item.score
          if (typeof value !== 'object') {
            return false
          }
          let {current, diff} = value
          return current === this.siftDiffScore && diff !== current
        })
      }
      return this.data
    },
    headers() {
      if (this.data.length > 0) {
        return Object.keys(this.data[0])
      } else {
        return []
      }
    },
    diff_task_id() {
      return this.$route.query.diff_task_id
    }
  },
  mounted() {

  },
  methods: {
    siftChange(siftScore, siftDiffScore) {
      this.siftScore = siftScore
      this.siftDiffScore = siftDiffScore
    },
    defaultFormatter(cellValue) {
      return cellValue && cellValue.length > 50 ? cellValue.toString().slice(0, 50) + ' ... ' : cellValue
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
    },
    showDialog() {
      this.dialogVisible = true
    },
    getDiffShowData(item, header) {
      const value = item[header]
      if (typeof value !== 'object') {
        return value
      }
      let {current, diff} = value
      if (['score', '__spend_seconds__'].includes(header)) {
        return `当前: ${current}<br>历史: ${diff}`
      } else if (['__id__', '__es_id__', 'update_time'].includes(header)) {
        return current
      }
    //   return this.highlightDifferences(current, diff)
      return current
    },
    highlightDifferences(current, diff) {
      let result = ''
      let i = 0
      let j = 0
      while (i < current.length && j < diff.length) {
        if (current[i] === diff[j]) {
          result += current[i]
          i++
          j++
        } else {
          let startI = i
          let startJ = j
      // 寻找下一个相同的字符位置
          while (i < current.length && j < diff.length && current[i] !== diff[j]) {
            i++
            j++
          }
      // 将不同的部分分别用红色和绿色标记
          if (startI < i) {
            result += `<span style="color: red;">${current.slice(startI, i)}</span>`
          }
          if (startJ < j) {
            result += `<span style="color: green;">${diff.slice(startJ, j)}</span>`
          }
        }
      }
  // 处理剩余的字符
      if (i < current.length) {
        result += `<span style="color: red;">${current.slice(i)}</span>`
      }
      if (j < diff.length) {
        result += `<span style="color: green;">${diff.slice(j)}</span>`
      }
      return result
    }
  }
}
</script>


<style scoped>
.detail-card-title{
  text-align: center;
  font-weight: bold;
  font-size: larger;
  color: #333; /* 标题颜色 */
  margin-top: 5px; /* 顶部间距 */
  margin-bottom: 5px; /* 底部间距 */
}
.pre-line {
  white-space: pre-line;
}
.el-col pre {
    max-width: 80%;
    overflow-x: auto; /* 当内容超出时显示滚动条 */
}

.chat-box {
  display: flex;
  flex-direction: column;
  gap: 15px; /* 增加聊天框之间的间距，使其更加美观 */
  padding: 10px; /* 增加内边距，使内容不会紧贴边缘 */
  border: 1px solid #E0E0E0; /* 添加边框，使聊天框更加突出 */
  border-radius: 8px; /* 增加圆角，使聊天框更加柔和 */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 添加阴影，增加立体感 */
  background-color: #F8F8F8; /* 调整聊天框背景色，使其更加柔和 */
}

.chat-message-input {
  background-color: #C2DFFF; /* 浅蓝色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 5px; /* 圆角 */
  padding: 5px 10px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: 0;
  max-width: 80%;
  margin-right: auto; /* 靠左 */
}

.chat-message-completion {
  background-color: #DCF8C6; /* 浅绿色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 5px; /* 圆角 */
  padding: 5px 10px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: 0;
  max-width: 80%;
  margin-right: auto; /* 靠左 */
}

.chat-message-eval-reason {
  background-color: #FFD700; /* 金色背景 */
  color: #000000; /* 字体颜色改为深色，增加对比度，提高可读性 */
  border-radius: 5px; /* 圆角 */
  padding: 5px 10px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: auto;
  max-width: 80%;
  margin-right: 0; /* 靠右 */
}

.pre-scrollable-y {
  white-space: pre-wrap; /* 保留空白符，允许自动换行 */
  max-height: 400px; /* 设置最大高度，例如200px */
  overflow-y: auto; /* 内容超出时显示垂直滚动条 */
}

.none-border-pre{
    border: none; /* 取消边框 */
    background-color: transparent;
}

</style>