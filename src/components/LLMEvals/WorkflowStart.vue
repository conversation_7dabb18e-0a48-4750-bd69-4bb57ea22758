<template>
    <div>
      <Layout>
        <Header style="background: #fff; border-bottom: 1px solid lightgray;">
          <Navigator @refresh="refresh"></Navigator>
        </Header>
        <Content>
          <div class="container">
            <h1 class="title">LLMEvals开始测评</h1>

            <div class="upload-section">
                <el-divider>报告配置</el-divider>
                <el-input class="select-section" v-model="wikiTitle" placeholder="请输入wiki报告名称">
                    <template slot="prepend">报告名称</template>
                </el-input>
                <el-input class="select-section" v-model="wikiDesc" placeholder="请输入简要描述">
                    <template slot="prepend">简要描述</template>
                </el-input>
                <el-input class="select-section" label="学城父文档ID" v-model="parent_wiki" placeholder="请输入学城父文档id">
                    <template slot="prepend">学城父文档ID</template>
                </el-input>
                <!-- <div class="el-input-group__prepend">API并发数</div>
                <el-input-number class="table-cell-section" controls-position="right" v-model="completion_threads" placeholder="">
                </el-input-number> -->
                <el-divider>仓库配置</el-divider>
                <div class="inline-section">
                    <div class="el-input-group__prepend">评测仓库</div>
                    <el-select class="table-cell-section" v-model="repo" filterable placeholder="请选评测仓库">
                        <el-option v-for="spec in repoList" :key="spec" :label="spec" :value="spec"></el-option>
                    </el-select>
                </div>
                <div class="inline-section">
                    <div class="el-input-group__prepend">评测仓库分支</div>
                    <el-select class="table-cell-section" v-model="branch" filterable
                        placeholder="请选择评测仓库分支">
                        <el-option v-for="spec in branchList" :key="spec"
                            :label="spec" :value="spec"></el-option>
                    </el-select>
                </div>
                <el-divider>评测配置</el-divider>
                <el-card v-for="(item, ind) in evaluations" :key="item.key" class="card-box">
                    <div slot="header" class="clearfix">
                        <span class="card-title">评测</span>
                        <!-- <el-button class="right-button" type="text" icon="el-icon-minus"
                            @click="removeReplacer(ind)"></el-button>
                        <el-button class="right-button" type="text" icon="el-icon-plus" @click="addReplacer"></el-button> -->
                    </div>
                    <div class="inline-section">
                        <div class="el-input-group__prepend">评测配置</div>
                        <el-select class="table-cell-section" v-model="evaluations[ind].key" filterable
                            placeholder="请选择评测 workflow">
                            <el-option v-for="spec in workflowList" :key="spec.workflow"
                                :label="get_eval_name_display(spec)" :value="spec.workflow"></el-option>
                        </el-select>
                    </div>
                    <el-upload class="upload-demo" :action="get_upload_base(item)" ref="rebateUpload" :headers="upload_header"
                        :on-remove="handleRemove.bind(this, item)" :on-success="handleSuccess.bind(this, item)"
                        :before-remove="beforeRemove.bind(this, item)" :on-exceed="handleExceed.bind(this, item)"
                        :on-error="handleError.bind(this, item)" :on-change="handleChange.bind(this, item)"
                        :on-preview="handlePreview.bind(this, item)">
                        <el-button size="small" type="primary">点击上传文件</el-button>
                    </el-upload>
                    <el-collapse v-model="evaluations[ind].showAdvancedParams">
                        <el-collapse-item>
                            <template slot="title">
                                高级参数
                                <el-tag type="success" v-if="validateJSON(evaluations[ind].advancedParams)">json校验成功</el-tag>
                            </template>
                            <el-input type="textarea" v-model="evaluations[ind].advancedParams" rows="6"></el-input>
                        </el-collapse-item>
                    </el-collapse>
                </el-card>
            </div>
            <el-divider>操作</el-divider>
            <div class="submit-section">
                <el-button type="primary" @click="submitTask">提交任务</el-button>
            </div>
        </div>
        </Content>
      </Layout>
    </div>
</template>


<script>
import api from '../LLMEvals/api'
import Navigator from '../LLMEvals/Navigator.vue'
export default {
  components: {
    Navigator
  },
  data() {
    return {
      selectedEvaluation: '',
      evalSpecList: [],
      parent_wiki: '1893747845',
      completion_threads: 1,
      evaluations: [
        {key: '', files: [], showAdvancedParams: false, advancedParams: ''}
      ],
      wikiTitle: '',
      wikiDesc: '',
      repoList: [],
      repo: '',
      branch: '',
      branchList: [],
      workflowList: [],
      upload_header: api.localHeaders
    }
  },
  mounted() {
    this.refresh()
  },
  watch: {
    repo(newRepo) {
      this.get_branch_list()
      this.get_workflow_list()
    },
    branch(newCurrentRepo) {
      this.get_workflow_list()
    }
  },
  methods: {
    validateJSON(input) {
      try {
        JSON.parse(input)
        return true
      } catch (error) {
        return false
      }
    },
    get_branch_list() {
      api.getWorkflowBranch(this.repo).then(result => {
        this.branchList = result.data
        this.branch = this.branchList[0]
      })
    },
    get_workflow_list() {
      api.getWorkflowList(this.repo, this.branch).then(result => {
        this.workflowList = result.data
        this.evaluations[0].key = result.data[0].workflow
        this.evaluations[0].item = result.data[0]
      })
    },
    get_eval_name_display(spec) {
      if (spec.description) {
        return `${spec.description}(${spec.workflow})`
      } else {
        return spec.workflow
      }
    },
    get_repo_config() {
      api.getWorkflowRepo().then((result) => {
        this.repoList = result.data
        console.log(result.data)
        if (this.repoList !== undefined && this.repoList !== null && this.repoList.length > 0) {
          this.repo = this.repoList[0]
          api.getWorkflowBranch(this.repo).then(result => {
            this.branchList = result.data
            this.branch = this.branchList[0]
            if (this.branch) {
              api.getWorkflowList(this.repo, this.branch).then(result => {
                this.workflowList = result.data
                this.evaluations[0].key = result.data[0].workflow
                this.evaluations[0].item = result.data[0]
              })
            }
          })
        }
      })
    },
    refresh() {
      this.sync()
      const currentDate = new Date()
      const year = currentDate.getFullYear() // 获取当前年份
      const month = currentDate.getMonth() + 1 // 获取当前月份（注意月份是从0开始计数的，所以要加1）
      const day = currentDate.getDate()
      const hour = currentDate.getHours() // 获取当前小时
      const minute = currentDate.getMinutes() // 获取当前分钟
      const second = currentDate.getSeconds() // 获取当前秒数
      this.wikiTitle = `LLM 评测 - ${year}/${month}/${day} - ${hour}:${minute}:${second}`
      this.wikiDesc = this.wikiTitle
      this.evaluations = [{ key: '', files: [] }]
      this.get_repo_config()
    },
    get_upload_base(item) {
      return api.LLM_EVAL_URL() + '/registry/data/upload'
    },
    sync() {
    },
    addReplacer() {
      this.evaluations.push({ key: null, files: [] })
    },
    removeReplacer(ind) {
      this.evaluations.splice(ind, 1)
    },
    submitTask() { // 处理提交任务的逻辑
      console.log(this.evaluations)
      const pipeline = this.evaluations[0].key

      let adv = {}
      if (this.validateJSON(this.evaluations[0].advancedParams)) {
        adv = JSON.parse(this.evaluations[0].advancedParams)
      }
      try {
        const fileName = this.evaluations[0].files[0].name
        adv['file_path'] = fileName
      } catch (error) {
        console.log(error)
      }

      let payload = {
        'pipeline': pipeline,
        'parent_wiki': this.parent_wiki,
        'report_title': this.wikiTitle,
        'task_name': this.wikiTitle,
        'report_description': this.wikiTitle,
        'data_limit': 10000,
        'silence_mode': true,
        'replacer_dict': adv,
        'completion_threads': 1,
        'running_mode': 'REMOTE_MODE',
        'repo': this.repo,
        'branch': this.branch
      }
      api.taskSubmit(payload).then(res => {
        this.$notify({ title: '任务提交成功', message: res.statusText, type: 'success' })
      }).catch(error => {
        api.notifyError(this.$notify, '任务提交失败', error)
      })
    },
    handleRemove(item, file, fileList) {
      console.log('handleRemove')
      console.log(file, fileList)
      item.files = fileList
    },
    handlePreview(item, file) {
      window.open(file.url)
    },
    handleChange(item, file, fileList) {
      console.log('handleChange')
      if (fileList.length > 1) {
        fileList.splice(0, 1)  // 删除之前那个文件
      }
      item.files = fileList
    },
    handleError(item, response, files, fileList) {
      console.log('handleError')
      this.$notify({ title: '上传失败', message: response, type: 'error' })
    },
    handleExceed(item, files, fileList) {
      console.log('handleExceed')
      this.$notify({ title: '文件太多', message: '当前限制选择 1 个文件，请删除后上传', type: 'warning' })
      item.files = files
    },
    beforeRemove(item, file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleSuccess(item, response, file, fileList) {
      console.log('handleSuccess')
      console.log(response)
      file.name = response.data.resource_url
      file.url = response.data.resource_url
      console.log(fileList)
      item.files = fileList
    }
  }
}
</script>


<style>
.el-upload input {
    display: none !important;
}

.el-card__header {
    padding: 6px 20px !important;
}

.el-divider__text {
    background-color: #f5f7f9 !important;
}
</style>

<style scoped>
.container {
    max-width: 700px;
    margin: 0 auto;
    padding: 20px;
}

.title {
    text-align: center;
    margin-bottom: 20px;
}

.upload-section {
    margin-bottom: 20px;
}

.select-section {
    margin-bottom: 20px;
    width: 100% !important;
}

.submit-section {
    text-align: center;
}


.table-cell-section {
    width: 100%;
    display: table-cell;
}

.inline-section {
    display: inline-block;
    width: 100%;
    /* 根据需要调整宽度 */
    margin-right: 10px;
    margin-bottom: 20px;
    /* 根据需要调整右侧间距 */
}

.card-title {
    font-size: large;
    font-weight: bold;
}

.right-button {
    float: right;
    padding: 5px;
}

.card-box {
    margin-bottom: 20px;
}
</style>
