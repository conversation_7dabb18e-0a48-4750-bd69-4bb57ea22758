import axios from 'axios'
import portal from '@/main'
import { Bus } from '@/global/bus'

const LLM_EVAL_URL = () => {
  let currentEnv = portal.$store.state.LLMEvalStorage.ENV
  if (process.env.NODE_ENV !== 'prod') {
    return 'https://aiengineering.sankuai.com/eval'
  } else if (currentEnv === 'test') {
    return 'https://llm.hotel.test.sankuai.com/eval'
  } else if (currentEnv === 'prod') {
    return 'https://aiengineering.sankuai.com/eval'
  }
}

const rePatterns = ['(.*)', '', '回答[：:]\\s*([\\u4E00-\\u9Fa5]+)', 'request_body=(\\{.*\\})', '(\\{.*\\})', '(\\{\\w+\\})', "\\+(\\d{2}):(\\d{2})'"].map(name => { return {value: name, label: name} })
const jpathPatterns = ['.', '$.data', '$.data.result'].map(name => { return {value: name, label: name} })

let localHeaders = {
  userInfo: JSON.stringify({
    userId: '',
    userName: 'Dev',
    userLogin: 'Dev',
    userUrl: '',
    ssoId: ''
  })
}

Bus.$on('refreshUserInfo', (userInfo) => {
  localHeaders.userInfo = encodeURI(JSON.stringify(userInfo))
})

const getEvalSpec = (repo) => {
  return axios({
    url: LLM_EVAL_URL() + '/registry/names?repo=' + repo,
    method: 'GET',
    headers: localHeaders
  })
}

const taskSubmit = (payload, repo) => {
  var url = LLM_EVAL_URL() + '/task'
  if (repo) {
    url = url + '?repo=' + repo
  }
  return axios({
    url: url,
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const getRepo = () => {
  return axios({
    url: LLM_EVAL_URL() + '/registry/repo',
    method: 'GET',
    headers: localHeaders
  })
}

const getWorkflowRepo = () => {
  return axios({
    url: LLM_EVAL_URL() + '/workflow/registry/list/repo',
    method: 'GET',
    headers: localHeaders
  })
}

const getWorkflowBranch = (repo) => {
  return axios({
    url: LLM_EVAL_URL() + '/workflow/registry/list/branch?repo=' + repo,
    method: 'GET',
    headers: localHeaders
  })
}

const getWorkflowList = (repo, branch) => {
  return axios({
    url: LLM_EVAL_URL() + '/workflow/registry/list?repo=' + repo + '&branch=' + branch,
    headers: localHeaders
  })
}

const getRemoteTaskList = (createBegin, createEnd, from, size, repo, branch,
                           workflow, taskName, person) => {
  let params = new URLSearchParams()
  params.append('size', size)
  params.append('from', from)
  params.append('create_begin', createBegin)
  params.append('create_end', createEnd)
  params.append('create_begin', createBegin)
  if (repo) {
    params.append('repo', repo)
  }
  if (branch) { params.append('branch', branch) }
  if (workflow) { params.append('workflow', workflow) }
  if (taskName) { params.append('task_name', taskName) }
  if (person) { params.append('person', person) }
  let url = LLM_EVAL_URL() + '/task/remote/list?' + params.toString()
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const getDataList = (taskId, subTaskId) => {
  let url = LLM_EVAL_URL() + `/datalist/show?task_id=${taskId}&sub_task_id=${subTaskId}`
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const diffDataList = (taskId, subTaskId, diffTaskId) => {
  let url = LLM_EVAL_URL() + `/datalist/diff?task_id=${taskId}&sub_task_id=${subTaskId}&diff_task_id=${diffTaskId}`
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const getRemoteTask = (taskId) => {
  let url = LLM_EVAL_URL() + `/task/remote/get?task_id=${taskId}`
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const getTaskOverview = (taskId) => {
  let url = LLM_EVAL_URL() + `/task/overview?task_id=${taskId}`
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const listDataItem = (taskId, subTaskId, from_, size_, otherSift, scoreMin, scoreMax, dataIndex = null) => {
  let params = new URLSearchParams()
  params.append('task_id', taskId)
  params.append('sub_task_id', subTaskId)
  params.append('from', from_)
  params.append('size', size_)
  params.append('score_min', scoreMin)
  params.append('score_max', scoreMax)
  if (dataIndex) {
    params.append('data_index', dataIndex)
  }
  for (let key in otherSift) {
    params.append(`${key}_sift`, otherSift[key])
  }
  let url = LLM_EVAL_URL() + '/data/item/list?' + params.toString()
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const listDiffDataItem = (taskId, subTaskId, diffTaskId, from_, size_, otherSift, scoreMin, scoreMax) => {
  let params = new URLSearchParams()
  params.append('task_id', taskId)
  params.append('sub_task_id', subTaskId)
  params.append('diff_task_id', diffTaskId)
  params.append('from', from_)
  params.append('size', size_)
  params.append('score_min', scoreMin)
  params.append('score_max', scoreMax)
  for (let key in otherSift) {
    params.append(`${key}_sift`, otherSift[key])
  }
  let url = LLM_EVAL_URL() + '/data/item/diff?' + params.toString()
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const updateDataItem = (dataItemEsID, changeLog) => {
  let url = LLM_EVAL_URL() + `/data/item/update`
  return axios({
    url: url,
    method: 'POST',
    data: {
      '_id': dataItemEsID,
      'change_log': changeLog
    },
    headers: localHeaders
  })
}

const aggrData = (taskId, subTaskId, diffTaskId = null) => {
  let urlPath = `/data/item/aggr?task_id=${taskId}&sub_task_id=${subTaskId}`
  if (diffTaskId) {
    urlPath = `/data/item/aggr?task_id=${taskId}&sub_task_id=${subTaskId}&diff_task_id=${diffTaskId}`
  }
  let url = LLM_EVAL_URL() + urlPath
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const updateDataList = (dsId, payload) => {
  let url = LLM_EVAL_URL() + `/data/list/update`
  return axios({
    url: url,
    method: 'POST',
    headers: localHeaders,
    data: {
      'ds_id': dsId,
      'payload': payload
    }
  })
}

const exportDataList = (taskId, subTaskId, diffTaskId, fileExt, otherSift, scoreMin, scoreMax) => {
  let params = new URLSearchParams()
  params.append('task_id', taskId)
  params.append('sub_task_id', subTaskId)
  params.append('file_ext', fileExt)
  params.append('score_min', scoreMin)
  params.append('score_max', scoreMax)
  for (let key in otherSift) {
    params.append(`${key}_sift`, otherSift[key])
  }
  if (diffTaskId) {
    params.append('diff_task_id', diffTaskId)
  }
  let url = LLM_EVAL_URL() + `/data/item/export?` + params.toString()
  return axios({
    url: url,
    method: 'GET',
    headers: localHeaders
  })
}

const taskMetric = (startDate, endDate, interval, workflow, taskName) => {
  let url = LLM_EVAL_URL() + `/task/metric`
  return axios({
    url: url,
    method: 'POST',
    headers: localHeaders,
    data: {
      'start_date': startDate,
      'end_date': endDate,
      'interval': interval,
      'workflow': workflow,
      'task_name': taskName
    }
  })
}

const taskMetricConfig = () => {
  return axios({
    url: LLM_EVAL_URL() + '/task/metric/config',
    method: 'GET',
    headers: localHeaders
  })
}

const suggestPrompt = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/fields/suggest',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const listPrompt = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/list',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const editPrompt = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/edit',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const addPrompt = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/add',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const suggestDataFields = () => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/input/fields/suggest',
    method: 'GET',
    headers: localHeaders
  })
}

const randomDataField = (fields, size) => {
  return axios({
    url: LLM_EVAL_URL() + '/prompt/input/random',
    method: 'POST',
    headers: localHeaders,
    data: {
      'fields': fields,
      'size': size
    }
  })
}

const onlineScorerRun = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/remote_tool/online_scorer',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const onlineHttpRun = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/remote_tool/http_completion_fn',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const onlineFieldTransform = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + '/remote_tool/field_preparer',
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const enumModels = () => {
  return axios({
    url: LLM_EVAL_URL() + '/model/enum',
    method: 'GET',
    headers: localHeaders
  })
}

const isAdmin = () => {
  return axios({
    url: LLM_EVAL_URL() + '/admin/check',
    method: 'GET',
    headers: localHeaders
  })
}

const notifyError = (notifyFunc, title, error) => {
  if (error.response) {
    console.error(error.response.data)
    notifyFunc({title: title, message: error.response.data.err, type: 'error'})
  } else {
    notifyFunc({title: title, message: error.message, type: 'error'})
  }
}

const getToolsOnline = (repo, regType) => {
  return axios({
    url: LLM_EVAL_URL() + `/tools/scan?repo=${repo}&reg_type=${regType}`,
    method: 'GET',
    headers: localHeaders
  })
}

const getFieldSuggest = (payload) => {
  return axios({
    url: LLM_EVAL_URL() + `/data/field/suggest`,
    method: 'POST',
    headers: localHeaders,
    data: payload
  })
}

const runToolsOnline = (repo, tool, data, task) => {
  return axios({
    url: LLM_EVAL_URL() + `/tool/run`,
    method: 'POST',
    headers: localHeaders,
    data: {
      'repo': repo,
      'tool': tool,
      'data': data,
      'task': task
    }
  })
}

export default {
  getEvalSpec,
  localHeaders,
  getRepo,
  LLM_EVAL_URL,
  taskSubmit,
  getWorkflowBranch,
  getWorkflowRepo,
  getWorkflowList,
  getRemoteTaskList,
  getDataList,
  getRemoteTask,
  diffDataList,
  getTaskOverview,
  updateDataItem,
  listDiffDataItem,
  listDataItem,
  aggrData,
  notifyError,
  updateDataList,
  exportDataList,
  taskMetric,
  taskMetricConfig,
  suggestPrompt,
  listPrompt,
  addPrompt,
  editPrompt,
  suggestDataFields,
  randomDataField,
  onlineScorerRun,
  onlineHttpRun,
  enumModels,
  isAdmin,
  onlineFieldTransform,
  rePatterns,
  jpathPatterns,
  getToolsOnline,
  runToolsOnline,
  getFieldSuggest
}
