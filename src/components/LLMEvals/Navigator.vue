<template>
    <div class='row navigator' >
      <div class="col-2-thin">
          <h1 slot="content">
            <a :href="base_url" class="nav-title">
                <img src="/static/eval_icon.png" style="height: 42px;"/>EvalCap
            </a>
          </h1>
      </div>
      <div class='col-8-thin' style="text-align: right; padding-left: 32px; padding-right: 32px; ">
        <el-row>
            <el-col :span="9">
                <div class="inline-section">
                    &emsp;
                    <div class="inline-section-title">
                        <slot name="inline-section-title"></slot>
                    </div>
                    <slot name="inline-section-place"></slot>
                </div>
            </el-col>
            <el-col :span="15">
                <a class="nav-lnks" target="_blank" href="https://km.sankuai.com/collabpage/2267523013">使用说明</a>
                <a class="nav-lnks" target="_blank" href="/evals/prompt/suggest">快速试用</a>
                <a class="nav-lnks" target="_blank" href="/evals/remote/tasklist">任务列表</a>
                <a class="nav-lnks" target="_blank" href="/evals/workflow/start">任务创建</a>
                <a class="nav-lnks" target="_blank" href="/evals/task/metric">评测统计</a>
                <slot></slot>
            </el-col>
        </el-row>
      </div>
      <div class="col-1-thin" >
          <el-button size="small" icon="el-icon-refresh" circle @click="refreshPage"></el-button>
          <slot name="other-btn"></slot>
      </div>
      <div id="userInfo col-1-thin" >
          <Dropdown @on-click="onDropdown">
          <div style="margin-top: 2px">
              {{ userInfo.userName }}<Icon type="ios-arrow-down" style="padding-left: 5px"></Icon>
          </div>
          <DropdownMenu slot="list">
              <DropdownItem style="text-align: center" name="quit">
                  <Icon type="md-exit" style="padding-right: 5px" />退出
              </DropdownItem>
              <DropdownItem style="text-align: center" name="env">
                  进入{{ next_env }}环境
              </DropdownItem>
          </DropdownMenu>
          </Dropdown>
      </div>
    </div>
  </template>

  <script>
  import { Bus } from '@/global/bus'
  export default {
    data() {
      return {

      }
    },
    computed: {
      current_env() {
        return this.$store.state.LLMEvalStorage.ENV
      },
      next_env() {
        if (this.current_env === 'test') {
          return 'prod'
        } else {
          return 'test'
        }
      },
      userInfo() {
        return this.$store.state.LLMEvalStorage.userInfo
      },
      base_url() {
        return '/evals/remote/tasklist'
      }
    },
    watch: {
    },
    mounted() {

    },
    methods: {
      refreshPage(menuItemIndex) {
        console.log('refresh')
        this.$emit('refresh')
      },
      onDropdown(value) {
        if (value === 'quit') {
          // 退出
          Bus.ssoWeb.logout().then(result => {
            window.location.href = window.location.href
          })
        } else if (value === 'env') {
          this.$store.dispatch('updateLLMEvalsEnv', this.next_env)
          this.$emit('refresh')
        }
      },
      goBack() {
        if (this.$router.history.length === 1) {
          this.$notify({title: '已经到达第一页', type: 'warn'})
          return
        }
        this.$router.go(-1)
      }
    }
  }
  </script>

<style scoped>

.inline-section {
    display: flex;
    line-height: 38px;
    margin: 10px;
}

.table-cell-section {
    width: 100%;
    display: table-cell;
}
.nav-title{
    font-size: 28px;
    line-height: 38px;
    font-weight: 500;
    color: #111925;
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
}
.nav-lnks{
    font-size: 16px;
    line-height: 24px;
    margin: 16px;
    font-weight: 500;
    color: #111925;
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
}
.nav-lnks:hover{
    color: #646971;
}

.ivu-layout-header {
    background: #515a6e;
    padding: 0 38px;
    height: 70px;
    line-height: 70px;
}
</style>

  <style>

  .active :hover {
    color: rgb(0, 149, 255);
  }

  .deactive :hover {
    color: rgb(0, 149, 255);
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    /* margin: 15px; */
    /* width: 100%; */
  }

  .col-3-thin {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-1-thin {
    flex: 0 0 8.33%;
    max-width: 8.33%;
  }
  .col-2-thin {
    flex: 0 0 16.66%;
    max-width: 16.66%;
  }

  .col-7-thin {
    flex: 0 0 58%;
    max-width: 58%;
  }

  .col-8-thin {
    flex: 0 0 66.66%;
    max-width: 66.66%;
  }

  .table th,
  .table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
  }
  .table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
  }
  .table tbody + tbody {
    border-top: 2px solid #dee2e6;
  }

  .table-sm th,
  .table-sm td {
    padding: 0.3rem;
  }

  .pagination-rounded .el-pagination button {
      border-radius: 20px;
  }

</style>
