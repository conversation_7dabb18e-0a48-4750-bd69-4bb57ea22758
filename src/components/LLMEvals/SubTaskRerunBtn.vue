<template>
    <div>
        <el-popover placement="bottom" width="400" v-model="rerunVisible">
            <p>子任务重试会造成数据覆盖，当前任务状态为{{task.status}}，是否执行重试？重试是否仅执行当前任务？</p>
            <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="onClick('cancel')">取消</el-button>
                <el-button type="primary" size="mini" @click="onClick('current')">执行当前</el-button>
                <el-button type="primary" size="mini" @click="onClick('bellow')">执行后续</el-button>
                <el-button type="primary" size="mini" @click="onClick('after')">执行当前和后续</el-button>
            </div>
            <el-button slot="reference">重试</el-button>
        </el-popover>
    </div>
</template>

<script>
export default {
  props: {
    subTask: Object,
    task: Object
  },
  data() {
    return {
      rerunVisible: false
    }
  },
  methods: {
    onClick(btnSelect) {
      if (btnSelect === 'cancel') {
        this.rerunVisible = false
      } else {
        this.$emit('click', btnSelect, this.subTask.sub_task_id)
        this.rerunVisible = false
      }
    }
  }
}
</script>