<template>
    <div>
        <div class="mt-container" v-loading="loading">
        <el-row class="sub-control-row">
            <el-col :span="24" class="text-right">
                <el-button type="primary" size="mini" @click="showDialog" class="mt-button"><i class="el-icon-setting"></i>表头</el-button>
            </el-col>
        </el-row>

        <el-table :data="subTaskList" border ref="subTaskList" row-key="spec_id">
            <el-table-column v-for="header in displayedHeaders" :key="header" :prop="header" 
                :label="headerMap[header]" sortable></el-table-column>
            <el-table-column :label="'操作'">
                <template slot-scope="scope">
                    <SubTaskRerunBtn :task="task" :sub-task="scope.row" @click="handleRerun"></SubTaskRerunBtn>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog title="配置表头" 
        :modal-append-to-body="false" 
        :visible.sync="dialogVisible" width="30%">
            <el-checkbox-group v-model="selectedHeaders">
                <el-checkbox v-for="header in headers" :key="header" :label="header">
                {{ headerMap[header] }}</el-checkbox>
            </el-checkbox-group>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">返回</el-button>
            </div>
        </el-dialog>
        </div>
    </div>
</template>


<script>
import api from '../LLMEvals/api'
import SubTaskRerunBtn from './SubTaskRerunBtn.vue'

export default {
  components: {
    SubTaskRerunBtn
  },
  data() {
    return {
      headerMap: {
        action_type: '执行方式',
        // description: '任务描述',
        ds_name: '关联数据集',
        // machine_desc: '执行机描述',
        // pre_sub_task_ids: '依赖任务',
        rerun_times: '重试次数',
        // running_mode: '执行模式',
        // slice_path: '数据切片名称',
        // slice_serialize_type: '数据序列化方式',
        slice_size: '切片大小',
        status: '状态',
        sub_task_id: '子任务ID',
        sub_task_name: '名称',
        submit_time: '提交时间',
        end_time: '完成时间',
        during: '耗时(秒)',
        // swimlane: '泳道',
        // task_id: '关联任务ID',
        thread_num: '线程数',
        tool_name: '工具名称',
        tool_type: '工具类型',
        update_time: '更新时间',
        start_time: '开始时间',
        workflow_name: 'workflow 配置'
      },
      subTaskList: [
                // 任务数据...
      ],
      loading: false,
      dialogVisible: false,
      selectedHeaders: [],
      rerunVisible: false,
      task: null
    }
  },
  computed: {
    displayedHeaders() {
    //   return ['submit_time', 'spec_id', 'task_name', ...this.selectedHeaders]
      const baseHeaders = ['sub_task_id', 'sub_task_name', 'workflow_name', 'tool_name', 'status']
      console.log(this.selectedHeaders)
      const filteredSelectedHeaders = this.selectedHeaders.filter(header => !baseHeaders.includes(header))
      return [...baseHeaders, ...filteredSelectedHeaders]
    },
    headers() {
      const baseHeaders = ['sub_task_id', 'sub_task_name', 'workflow_name', 'tool_name', 'status']
      return Object.keys(this.headerMap).filter(header => !baseHeaders.includes(header))
    },
    task_id() {
      return this.$route.query.task_id
    }
  },
  watch: {
  },
  mounted() {
    this.selectedHeaders = ['during']
    this.refresh()
  },
  methods: {
    refresh() {
      this.search_tasks()
    },
    search_tasks() {
      this.loading = true
      api.getRemoteTask(this.task_id).then(r => {
        this.task = r.data.task
        this.loading = false
        this.subTaskList = r.data.sub_tasks
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 信息失败', error)
        this.loading = false
      })
    },
    showDialog() {
      this.dialogVisible = true
    },
    selectSubTask(btnSelect, subTaskId) {
      const index = this.subTaskList.findIndex(item => item.sub_task_id === subTaskId)
      if (index !== -1) {
        if (btnSelect === 'after') {
          const subTaskIds = this.subTaskList.slice(index).map(item => item.sub_task_id)
          return subTaskIds
        } else if (btnSelect === 'current') {
          return [subTaskId]
        } else if (btnSelect === 'bellow') {
          const subTaskIds = this.subTaskList.slice(index).map(item => item.sub_task_id)
          return subTaskIds
        }
      }
      return []
    },
    handleRerun(btnSelect, subTaskId) {
      const selectedSubTaskIds = this.selectSubTask(btnSelect, subTaskId)
      const payload = {
        'workflow': this.task.workflow,
        'running_mode': 'REMOTE_MODE',
        'trigger_action': 'RERUN',
        'repo': this.task.repo,
        'branch': this.task.branch,
        'rerun': {
          'task_id': this.task.spec_id,
          'sub_task_ids': selectedSubTaskIds
        }
      }
      api.taskSubmit(payload).then(r => {
        this.$notify({title: '提交成功', message: '', type: 'success'})
      }).catch((error) => {
        api.notifyError(this.$notify, '任务重跑失败', error)
      })
    }
  }
}
</script>


<style scoped>
.mt-container{
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 20px;
}
.sub-control-row{
    margin-top: 4px;
    margin-bottom: 8px;
    padding-right: 8px;
}
.table-cell-section {
    width: 100%;
    display: inline-block;
}
</style>