import portal from '@/main'
import { Bus } from '@/global/bus'

Bus.$on('refreshUserInfo', (userInfo) => {
  portal.$store.dispatch('updateLLMEvalsUserInfo', userInfo)
})

export default {
  state: {
    ENV: 'prod',
    userInfo: {
      userId: '',
      userName: 'Dev',
      userLogin: 'Dev',
      userUrl: '',
      ssoId: ''
    },
    repo: 'ssh://*******************/hbqa/toolchain-llmeval.git'
  },
  mutations: {
    setLLMEvalsEnv(state, env) {
      state.ENV = env
    },
    setLLMEvalsUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setLLMEValRepo(state, repo) {
      state.repo = repo
    }
  },
  actions: {
    updateLLMEvalsEnv({ commit }, env) {
      commit('setLLMEvalsEnv', env)
    },
    updateLLMEvalsUserInfo({ commit }, userInfo) {
      commit('setLLMEvalsUserInfo', userInfo)
    },
    updateLLMEValRepo({ commit }, repo) {
      commit('setLLMEValRepo', repo)
    }
  }
}
