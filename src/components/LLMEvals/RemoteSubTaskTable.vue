<template>
    <div class="remote_sub_task_table">
        <el-backtop target=".remote_sub_task_table"></el-backtop>
        <!-- <Navigator @refresh="refresh"></Navigator> -->
        <div class="mt-container" v-loading="loading">
            <h2 v-if="subtask" class="tool-name">{{ subtask.tool_name }}</h2>
            <el-row>
                <el-col style="padding-left: 10px;padding-bottom: 10px" :span="3">
                    <mtd-checkbox v-model="showRange">得分范围筛选</mtd-checkbox>
                </el-col>

                <el-col v-if="showRange" :span="8">
                    <div style="width: 300px;padding-left: 10px">
                        <span style="margin: 0 4px">得分范围</span>
                        <mtd-input-number v-model="scoreShiftRange[0]" :controls="false" :min="0" :max="10" />
                        <span style="margin: 0 4px">—</span>
                        <mtd-input-number v-model="scoreShiftRange[1]" :controls="false" :min="0" :max="10" />
                    </div>
                </el-col>
                <template v-else></template>
            </el-row>

            <el-row class="sift-row" v-for="line_num in getSiftLineNum()" :key="`row-${line_num}`">
                <el-col class="sift-col" :span="getSiftSpan(header, ind)" v-for="header, ind in getSiftEnableHeaders(line_num)" :key="header">
                    <div v-if="header === 'score'"></div>
                    <div v-else-if="header === '_button_'" class="text-right">
                        <mtd-button  type="primary" @click="handleCurrentChange(currentPage)" icon="search">搜索</mtd-button>
                        <mtd-button  type="primary" @click="showDialog" icon="setting">配置</mtd-button>
                        <template v-if="$route.query.diff_task_id">
                          <mtd-button  type="warning" @click="switchDiffMode" icon="mtdicon-share-arrow-fill">{{ diffModeNext }}</mtd-button>
                        </template>
                        <el-dropdown @command="exportFile" trigger="click">
                            <mtd-button type="success" v-loading="exporting" icon="arrow-down">导出
                            </mtd-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="xlsx">xlsx</el-dropdown-item>
                                <el-dropdown-item command="csv">csv</el-dropdown-item>
                                <el-dropdown-item command="json">json</el-dropdown-item>
                                <el-dropdown-item command="jsonl">jsonl</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                    <template v-else>
                      <!-- <mtd-input @input="setSiftModel(header, $event)" :value="getSiftModel(header, '')"
                                 placeholder="请输入"  clearable>
                        <template #prepend>{{ headerMap[header] ? headerMap[header].label : header }}筛选</template>
                    </mtd-input> -->
                    <el-autocomplete
                        v-model="siftMethod[header]"
                        :fetch-suggestions="(queryString, cb) => querySearch(queryString, cb, header)"
                        placeholder="请输入"
                        clearable
                        style="width: 100%; height: 20px;"
                        class="custom-autocomplete">
                        <template #prepend>
                            <span style="color: black;">
                                {{ headerMap[header] ? headerMap[header].label : header }}筛选</span>
                        </template>
                        <template slot-scope="{ item }">
                            <div style="display: flex; justify-content: space-between; width: 100%;">
                                <span>{{ limitValue(item.value) }}</span>
                                <span style="color: #bbb;">{{ item.count }}次({{ item.percentage.toFixed(1) }}%)</span>
                            </div>
                        </template>
                    </el-autocomplete>
                    </template>
                </el-col>
            </el-row>
            <Card v-if="shownData.length === 0" :bordered="false">
                <div slot="title" class="detail-card-title">提示信息</div>
                <div style="text-align: center; padding: 20px;">
                    暂无数据
                </div>
            </Card>
            <DetailCard v-for="(item, ind) in shownData" :key="ind"
                :ind="ind" :item="item" :frontEndConfig="frontEndConfig"
                :currentFristIndex="currentFristIndex" :headerMap="headerMap"
                :mutiRoundData="allDataMutiRound[ind]"
                :taskId="task.spec_id"
                :subTaskId="subtask.sub_task_id"
                :dataIndex="item.__id__"
            ></DetailCard>
            <el-pagination class="pagination-container"
                :current-page="currentPage"
                :page-size="pageSize"
                :total="total"
                :page-sizes="[5, 10, 20]"
                layout="sizes, prev, pager, next, jumper, total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
            </el-pagination>
            <el-dialog :modal-append-to-body="false" title="配置展示字段" :visible.sync="dialogVisible" width="70%">
                <el-row v-for="header in headers" :key="header" :label="header">
                    <el-col :span="4">
                        <el-button class="mini-del-btn" circle
                        @click="delConfigHeader(header)"
                        v-if="!dataCalculatedHeaders.includes(header)">
                            <i class="el-icon-minus"></i>
                        </el-button>
                        <template v-if="editingHeader === header">
                            <mtd-input
                                :value="getConfigValueStr(header, 'alias')"
                                @input="setConfigValue(header, 'alias', $event)"
                                @blur="editingHeader = null"
                                size="small"
                                @keyup.enter="editingHeader = null"
                            />
                        </template>
                        <template v-else>
                            <span @click="editingHeader = header">
                                {{ headerMap[header] ? headerMap[header].label : header }}
                            </span>
                        </template>
                    </el-col>
                    <el-col :span="3">
                        <select
                            :value="getConfigValueStr(header, 'type')"
                            @input="(event) => setConfigValue(header, 'type', event.target.value)"
                            size="small" 
                            style="width: 100px"
                            :disabled="['score', 'completion', '__id__'].includes(header)"
                            placeholder="选择类型">
                            <option label="字符串" value="str"/>
                            <option label="JSON" value="json"/>
                        </select>
                    </el-col>
                    <el-col :span="2">
                        <el-checkbox :value="getConfigValue(header, 'sift')"
                        @input="setConfigValue(header, 'sift', $event)"
                        :disabled="['__id__', 'score'].includes(header)">筛选</el-checkbox>
                    </el-col>
                    <el-col :span="2">
                        <el-checkbox :value="getConfigValue(header, 'show')"
                        :disabled="['score'].includes(header)"
                        @input="setConfigValue(header, 'show', $event)">查看</el-checkbox>
                    </el-col>
                    <el-col :span="2">
                        <el-checkbox :value="getConfigValue(header, 'action')"
                        @input="setConfigValue(header, 'action', $event)"
                        :disabled="['__id__', 'score', 'completion'].includes(header)">修改</el-checkbox>
                    </el-col>
                    <el-col :span="3" v-if="diff_task_id">
                        <el-checkbox :value="getConfigValue(header, 'diff')"
                        :disabled="['score', '__id__'].includes(header)"
                        @input="setConfigValue(header, 'diff', $event)">对比</el-checkbox>
                    </el-col>
                    <el-col :span="6">
                        <mtd-input :value="getConfigValueStr(header, 'options')" style="width: 100%;"
                        v-if="getConfigValue(header, 'action') && !['score', '__id__', 'completion'].includes(header) "
                        placeholder="请输入逗号分隔项" size="small"
                        @input="setConfigValue(header, 'options', $event)">
                            <template #prepend>选项</template>
                        </mtd-input>
                    </el-col>
                </el-row>
                <el-row >
                    <el-col :span="8">
                        <el-button v-if="!newFieldEditting" class="mini-del-btn" circle @click="addNewFieldActivate"><i class="el-icon-plus"></i></el-button>
                        <input class="new-field-inp" v-if="newFieldEditting" v-model="newFieldName" placeholder="请输入字段名"></input>
                    </el-col>
                    <el-col :span="4" v-if="newFieldEditting">
                        <el-button class="mini-del-btn" circle @click="addNewFieldCheck"><i class="el-icon-check"></i></el-button>
                        <el-button class="mini-del-btn" circle @click="newFieldEditting=false"><i class="el-icon-close"></i></el-button>
                    </el-col>
                    <el-col :span="12" v-if="newFieldEditting" class="new-field-warn">
                        {{ this.newFieldWarning }}
                    </el-col>
                </el-row>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="saveDataList">持久保存</el-button>
                    <el-button @click="dialogVisible = false">返回</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>


<script>
import Navigator from '../LLMEvals/Navigator.vue'
import api from '../LLMEvals/api'
import DetailCard from '../LLMEvals/DetailCard.vue'

export default {
  props: {
    subtask: Object,
    task: Object,
    scoreShift: String
  },
  components: {
    Navigator,
    DetailCard
  },
  data() {
    return {
      showRange: false,
      scoreShiftRange: [0, 1],
      headerMap: {
        __id__: { label: '内部ID', width: 60 }, // 例如，宽度设置为100px
        score: {
          label: '得分',
          width: 100 // 设置宽度
        },
        __spend_seconds__: {
          label: 'API接口耗时(s)',
          width: 120 // 设置宽度
        },
        eval_reason: { label: '评测原因' },
        ideal: { label: '预期回答ideal' },
        model_graded: { label: '评测Prompt' },
        update_time: { label: '更新时间' },
        completion: { label: '模型输出' }
      },
      editingHeader: null,
      headerWidth: {
      },
      dialogVisible: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      siftScore: null,
      siftDiffScore: null,
      data: [],
      loading: false,
      siftMethod: {'score': 'clear'},
      frontEndConfig: {},
      existHeaders: [],
      tmpHeaders: [],
      ds_id: null,
      exporting: false,
      newFieldEditting: false,
      newFieldName: '',
      newFieldWarning: '',
      diffModeNext: '对比',
      allDataMutiRound: []
    }
  },
  watch: {
    subtask() {
      this.refresh()
    },
    scoreShift() {
      this.refresh()
    }
  },
  computed: {
    siftHeaders() {
      const siftHeaders = []
      for (let key in this.frontEndConfig) {
        if (this.frontEndConfig.hasOwnProperty(key)) {
          let value = this.frontEndConfig[key]
          if (value.sift && this.headers.includes(key) && key !== 'score') {
            siftHeaders.push(key)
          }
        }
      }
      siftHeaders.push('_button_')
      return siftHeaders
    },
    currentFristIndex() {
      return (this.currentPage - 1) * this.pageSize + 1
    },
    shownData() {
      if (this.siftScore !== null && this.siftScore !== undefined) {
        return this.data.filter(item => {
          const value = item.score
          if (typeof value !== 'object') {
            return value === this.siftScore
          }
          let {current} = value
          return current === this.siftScore
        })
      } else if (this.siftDiffScore !== null && this.siftDiffScore !== undefined) {
        return this.data.filter(item => {
          const value = item.score
          if (typeof value !== 'object') {
            return false
          }
          let {current, diff} = value
          return current === this.siftDiffScore && diff !== current
        })
      }
      return this.data
    },
    dataCalculatedHeaders() {
      if (this.data.length > 0) {
        const dataLength = Math.min(this.data.length, 10) // 获取实际的data数量和10的较小值
        let tmpHeaders = []
        for (let i = 0; i < dataLength; i++) {
          const keys = Object.keys(this.data[i]).filter(item => !['__es_id__', '__change_log__'].includes(item))
          tmpHeaders = [...new Set([...tmpHeaders, ...keys])]
        }
        return tmpHeaders
      } else {
        return []
      }
    },
    feConfigCalculatedHeaders() {
      let headers = []
      for (let key in this.frontEndConfig) {
        if (this.frontEndConfig.hasOwnProperty(key)) {
          headers.push(key)
        }
      }
      return headers
    },
    headers() {
      if (this.data.length > 0) {
        this.tmpHeaders = [...new Set([...this.dataCalculatedHeaders, ...this.feConfigCalculatedHeaders])]
        this.tmpHeaders.sort((a, b) => {
          let aInd = this.getFeConfigHeaderIndex(a)
          let bInd = this.getFeConfigHeaderIndex(b)
          return aInd - bInd
        })
        return this.tmpHeaders
      } else {
        this.tmpHeaders.sort((a, b) => {
          let aInd = this.getFeConfigHeaderIndex(a)
          let bInd = this.getFeConfigHeaderIndex(b)
          return aInd - bInd
        })
        return this.tmpHeaders
      }
    },
    diff_task_id() {
      if (this.diffModeNext === '对比') {
        return null
      }
      return this.$route.query.diff_task_id
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    updateHeaderMap() {
    // 创建一个新对象而不是修改原对象
      const newMap = { ...this.headerMap }
      for (let key in this.frontEndConfig) {
        if (this.frontEndConfig.hasOwnProperty(key)) {
          let value = this.frontEndConfig[key]
          if (value.alias) {
            newMap[key] = { label: value.alias }
          }
        }
      }
    // 直接赋值新对象
      this.headerMap = newMap
    },
    querySearch(queryString, cb, header) {
      const payload = {
        'task_id': this.task.spec_id,
        'sub_task_id': this.subtask.sub_task_id,
        'content': queryString,
        'top_n': 10,
        'field': header
      }
      api.getFieldSuggest(payload).then(response => {
        // 假设返回的数据格式是 { data: [{ value: '选项1' }, { value: '选项2' }] }
        cb(response.data.data)
      }).catch(error => {
        console.error('获取建议失败', error)
      })
    },
    limitValue(value) {
      return value.length > 15 ? value.slice(0, 15) + '...' : value
    },
    switchDiffMode() {
      if (this.diffModeNext === '对比') {
        this.diffModeNext = '原始'
        this.$emit('switchDiffMode', true)
      } else {
        this.diffModeNext = '对比'
        this.$emit('switchDiffMode', false)
      }
      this.handleCurrentChange(this.currentPage)
    },
    getFeConfigHeaderIndex(field) {
      if (this.frontEndConfig[field] && this.frontEndConfig[field].index) {
        return this.frontEndConfig[field].index
      }
      return 0
    },
    addNewFieldActivate() {
      this.newFieldEditting = true
      this.newFieldName = ''
      this.newFieldWarning = ''
    },
    addNewFieldCheck() {
      if (this.headers.includes(this.newFieldName)) {
        const header = this.newFieldName
        this.newFieldWarning = `重复添加已有字段: ${this.headerMap[header] ? this.headerMap[header].label : header}`
      } else {
        this.setConfigValue(this.newFieldName, 'show', true)
        this.setConfigValue(this.newFieldName, 'action', true)
        this.newFieldEditting = false
      }
    },
    exportFile(command) {
      this.exporting = true
      api.exportDataList(this.task.spec_id, this.subtask.sub_task_id, this.diff_task_id, command,
        this.siftMethod, this.scoreShiftRange[0],
        this.scoreShiftRange[1]
      ).then(r => {
        const fileLink = r.data.public_link
        this.exporting = false
        window.open(fileLink)
      }).catch(error => {
        this.exporting = false
        api.notifyError(this.$notify, 'dataList 导出失败', error)
      })
    },
    getSiftModel(header, _default = '') {
      if (!this.siftMethod[header]) {
        this.$set(this.siftMethod, header, _default)
      }
      return this.siftMethod[header]
    },
    setSiftModel(header, value) {
      if (value && value !== '') {
        this.siftMethod[header] = value
      } else {
        this.$delete(this.siftMethod, header)
      }
    },
    getSiftSpan(header, index) {
      if (header === '_button_') {
        return Math.max(24 - 8 * index, 8)
      } else {
        return 8
      }
    },
    getSiftLineNum() {
      return Array.from({ length: Math.ceil(this.siftHeaders.length / 3) }, (_, index) => index)
    },
    getSiftEnableHeaders(line) {
      const start = line * 3
      const end = line * 3 + 3
      return this.siftHeaders.slice(start, end)
    },
    delConfigHeader(header) {
      if (this.frontEndConfig[header]) {
        this.$delete(this.frontEndConfig, header)
      }
    },
    getConfigValueStr(header, field) {
      if (!this.frontEndConfig[header]) {
        this.$set(this.frontEndConfig, header, {})
      }
      return this.frontEndConfig[header][field]
    },
    getConfigValue(header, field) {
      if (!this.frontEndConfig[header]) {
        this.$set(this.frontEndConfig, header, {})
      }
      if (this.frontEndConfig[header][field]) {
        return true
      }
      return null
    },
    setConfigValue(header, field, value) {
      if (!this.frontEndConfig[header]) {
        this.$set(this.frontEndConfig, header, {})
      }
      if (field === 'action') {
        if (value) {
          this.$set(this.frontEndConfig[header], 'action', 'edit')
          this.$set(this.frontEndConfig[header], 'type', 'str')
        } else {
          this.$delete(this.frontEndConfig[header], 'action')
          this.$delete(this.frontEndConfig[header], 'type')
        }
      } else if (field === 'sift') {
        if (value) {
          this.$set(this.frontEndConfig[header], 'sift', value)
        } else {
          this.$delete(this.frontEndConfig[header], 'sift')
          this.$delete(this.siftMethod, header)
        }
      } else {
        this.$set(this.frontEndConfig[header], field, value)
      }
      this.updateHeaderMap()
    },
    saveDataList() {
      const payload = {
        'front_end_config_json_str': JSON.stringify(this.frontEndConfig)
      }
      api.updateDataList(this.ds_id, payload).then(res => {
        this.dialogVisible = false
      }).catch(error => {
        api.notifyError(this.$notify, 'dataList 保存失败', error)
      })
    },
    refresh() {
      this.currentPage = 1
      this.loading = true
      this.siftMethod = {'score': this.scoreShift}
      this.getdata(true).then(r => {
        this.loading = false
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 列表失败', error)
        this.loading = false
      })
    },
    async getdata(changeFeConfig = false) {
      this.loading = true
      const _from = (this.currentPage - 1) * this.pageSize
      const _size = this.pageSize
      if (this.showRange) {
        this.setSiftModel('score', 'range')
      } else {
        this.setSiftModel('score', this.scoreShift)
      }
      if (this.diff_task_id) {
        try {
          const r = await api.listDiffDataItem(this.task.spec_id,
            this.subtask.sub_task_id,
            this.diff_task_id, _from, _size,
            this.siftMethod, this.scoreShiftRange[0],
            this.scoreShiftRange[1])
          this.data = r.data.data
          this.total = r.data.total.value
          this.ds_id = r.data.ds_id
          if (changeFeConfig) {
            this.frontEndConfig = r.data.ds.front_end_config
          }
          return r
        } catch (error) {
          console.warn(error)
        }
      }
      const r = await api.listDataItem(this.task.spec_id,
        this.subtask.sub_task_id, _from, _size,
        this.siftMethod, this.scoreShiftRange[0],
        this.scoreShiftRange[1])
      let rData = r.data
      let rDataArray = []
      if (Array.isArray(r.data) && r.data.length > 0) {
        rData = r.data[0]
        rDataArray = r.data
      } else {
        rDataArray = [r.data]
        rData = r.data
      }
      this.allDataMutiRound = this.transformToMutiTurnData(rDataArray)
      this.data = rData.data
      this.total = rData.total.value
      this.ds_id = rData.ds_id
      if (changeFeConfig) {
        this.frontEndConfig = rData.ds.front_end_config
      }
      return r
    },
    transformToMutiTurnData(rdata) {
        // allDataMutiRound 格式是 allDataMutiRound[case序号][多轮次]，但是rdata是[多轮次][case序号]
      let allDataMutiRound = []
      rdata.forEach((round, roundIndex) => {
        if (roundIndex === 0) {
          round.data.forEach((caseItem, caseIndex) => {
            allDataMutiRound.push([])
          })
        }
        round.data.forEach((caseItem, caseIndex) => {
          allDataMutiRound[caseIndex].push(caseItem)
        })
      })
      return allDataMutiRound
    },
    defaultFormatter(cellValue) {
      return cellValue && cellValue.length > 50 ? cellValue.toString().slice(0, 50) + ' ... ' : cellValue
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getdata(false).then(r => {
        this.loading = false
        // 切换分页后自动跳到顶部
        const scrollContainer = document.querySelector('.remote_sub_task_table')
        scrollContainer.scrollTop = 0
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 列表失败', error)
        this.loading = false
      })
    },
    handleSizeChange(pagesize) {
      this.pageSize = pagesize
      this.currentPage = 1
      this.getdata(false).then(r => {
        this.loading = false
        // 切换分页后自动跳到顶部
        const scrollContainer = document.querySelector('.remote_sub_task_table')
        scrollContainer.scrollTop = 0
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 列表失败', error)
        this.loading = false
      })
    },
    showDialog() {
      this.dialogVisible = true
    }
  }
}
</script>


<style scoped>

.sift-row{
    margin-bottom: 8px;
    margin-top: 4px
}
.sift-col{
    padding-left: 8px;
    padding-right: 8px;
}

.el-col pre {
  max-width: 80%;
  overflow-x: auto; /* 当内容超出时显示滚动条 */
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 24px;
}

.remote_sub_task_table{
    height: calc(100vh - 100px);
    overflow-x: scroll;
    scroll-behavior: smooth;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
}

.pagination-container {
  position: fixed;
  left: 200px;
  bottom: 0;
  width: 100%;
  background-color: #f0f2f5;
  padding: 10px 0;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.15);
  z-index: 1000;
}

.option-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.select-title {
    background-color: #F5F7FA;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 0 20px;
    width: 1px;
    white-space: nowrap;
    border-right: 0;
}

.mt-container{
    margin-left: 20px;
    margin-right: 20px;
}

.mt-select{
    display: table-cell;
    width: 100%;
}
h4 {
  font-weight: bolder;
  font-size: 18px;
  line-height: 26px;
}
</style>

<style>
.el-card__body{
    padding-left: 0px;
    padding-right: 0px;
}
.el-card__header{
    padding-top: 0px;
    padding-bottom: 0px;
}
.el-input__inner{
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.custom-autocomplete .el-input__inner {
    height: 30px; /* 调整输入框高度 */
    line-height: 30px; /* 调整行高以垂直居中文本 */
}
.el-card{
    padding: 16px;
}
.tool-name{
  text-align: center;
  font-weight: bold;
  color: #333; /* 标题颜色 */
  margin-top: 20px; /* 顶部间距 */
  margin-bottom: 10px; /* 底部间距 */
}
.mini-del-btn{
    padding: 0px !important;
}
.new-field-inp{
    border-radius: 4px;
    width: 90%;
    border: 1px solid #E0E0E0;
}
.new-field-warn{
    font-size: 12px;
    line-height: 20px;
    color: #FF5E00;
}
</style>
