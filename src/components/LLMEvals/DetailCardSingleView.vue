<template>
    <Layout>
        <Header style="background: #fff; border-bottom: 1px solid lightgray;">
           <Navigator>
              <a :href="`/evals/remote/task?task_id=${taskId}&activateName=${subTaskId}`">返回</a>
           </Navigator>
        </Header>
        <Content>
            <DetailCard v-for="(item, ind) in shownData" :key="ind"
                :ind="dataIndex" :item="item" :frontEndConfig="frontEndConfig"
                :currentFristIndex="currentFristIndex" :headerMap="headerMap"
                :mutiRoundData="allDataMutiRound[ind]"
                :taskId="taskId"
                :subTaskId="subTaskId"
                :dataIndex="dataIndex"
            ></DetailCard>
        </Content>
    </Layout>
</template>

<script>
import api from '../LLMEvals/api'
import DetailCard from './DetailCard.vue'
import Navigator from '../LLMEvals/Navigator.vue'

export default {
  components: {
    DetailCard,
    Navigator
  },
  computed: {
    taskId() { return this.$route.query.taskId },
    subTaskId() { return this.$route.query.subTaskId },
    dataIndex() { return this.$route.query.dataIndex }
  },
  mounted() {
    this.refresh()
  },
  data() {
    return {
      frontEndConfig: {},
      currentFristIndex: 1,
      headerMap: {
        __id__: { label: '内部ID', width: 60 }, // 例如，宽度设置为100px
        score: {
          label: '得分',
          width: 100 // 设置宽度
        },
        __spend_seconds__: {
          label: 'API接口耗时(s)',
          width: 120 // 设置宽度
        },
        eval_reason: { label: '评测原因-eval' },
        ideal: { label: '预期回答ideal' },
        model_graded: { label: '评测Prompt' },
        update_time: { label: '更新时间' }
      },
      allDataMutiRound: [],
      shownData: {}
    }
  },
  methods: {
    transformToMutiTurnData(rdata) {
        // allDataMutiRound 格式是 allDataMutiRound[case序号][多轮次]，但是rdata是[多轮次][case序号]
      let allDataMutiRound = []
      rdata.forEach((round, roundIndex) => {
        if (roundIndex === 0) {
          round.data.forEach((caseItem, caseIndex) => {
            allDataMutiRound.push([])
          })
        }
        round.data.forEach((caseItem, caseIndex) => {
          allDataMutiRound[caseIndex].push(caseItem)
        })
      })
      return allDataMutiRound
    },
    async getdata(changeFeConfig = false) {
      const _from = 0
      const _size = 1
      const r = await api.listDataItem(this.taskId,
        this.subTaskId, _from, _size, [], -100, 100, this.dataIndex)
      let rData = r.data
      let rDataArray = []
      if (Array.isArray(r.data) && r.data.length > 0) {
        rData = r.data[0]
        rDataArray = r.data
      } else {
        rDataArray = [r.data]
        rData = r.data
      }
      this.allDataMutiRound = this.transformToMutiTurnData(rDataArray)
      this.shownData = rData.data
      this.total = rData.total.value
      this.ds_id = rData.ds_id
      if (changeFeConfig) {
        this.frontEndConfig = rData.ds.front_end_config
      }
      return r
    },
    refresh() {
      this.getdata(true)
    }
  }
}
</script>
