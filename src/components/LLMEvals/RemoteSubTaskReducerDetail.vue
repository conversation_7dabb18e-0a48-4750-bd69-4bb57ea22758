<template>
    <div>
        <div class="mt-container">
        <div v-loading="loading" class="flex-row" v-if="aggrData">
            <div class="status-aside-title">
                <h2 class="vertical-text" v-if="!diff">
                    <a :href='`/evals/remote/task?task_id=${task.spec_id}`'>
                    当前任务</a>
                </h2>
                <h2 class="vertical-text" v-else>
                    <a :href='`/evals/remote/task?task_id=${task.spec_id}`'>
                    历史任务</a>
                </h2>
            </div>
            <div :class="{'status-content': !diff, 'status-content-diff': diff}"
            :style="{marginRight: dynamicMargin, marginLeft: dynamicMargin}"
            v-for="(item, key) in aggrData" :key="key">
                <div class="aggr-title">{{ key }}</div>
                <div class="aggr-content" v-for="entrance in item">
                    {{ entrance.__key__ }} : {{ entrance.result }}
                </div>
                <AggrViewComponent v-if="isScorer(key)"
                :sub-task-id="searchSubTaskByToolName(key).sub_task_id"
                :task-id="task.spec_id"></AggrViewComponent>
            </div>
        </div>
        <el-row v-loading="loading" class="flex-row" v-else>
            <el-col :span="1">
                <h2 class="vertical-text" v-if="!diff">
                    <a :href='`/evals/remote/task?task_id=${task.spec_id}`'>
                    当前任务</a>
                </h2>
                <h2 class="vertical-text" v-else>
                    <a :href='`/evals/remote/task?task_id=${task.spec_id}`'>
                    历史任务</a>
                </h2>
            </el-col>
            <el-col :class="{'status-content': !diff, 'status-content-diff': diff}"
            :span="Math.max(Math.floor(20 / aggrLength), 6)"
                v-for="(item, key) in aggregratedReducedData" :key="`arrg-reduced-${key}`">
                <div class="aggr-title" v-if="diff"> DIFF </div>
                <div class="aggr-title">{{ key }}</div>
                <div class="aggr-content" v-for="entrance in item">
                    {{ getStatusContent(entrance, key) }}
                </div>
                <div class="aggr-content" v-if="calculateRate(key)">
                    通过率 : {{ calculateRate(key) }}
                </div>
                <div class="aggr-content" v-if="aggregratedReducedToolName[key].includes('SpendTime')">
                    TP50: {{ calculatePercentiles(item[0].distribution || [0], 50).toFixed(2) }} 秒
                </div>
                <div class="aggr-content" v-if="aggregratedReducedToolName[key].includes('SpendTime')">
                    TP99: {{ calculatePercentiles(item[0].distribution || [0], 99).toFixed(2) }} 秒
                </div>
                <AggrViewComponent v-if="isScorer(key)"
                :sub-task-id="searchSubTaskByToolName(key).sub_task_id"
                :task-id="task.spec_id"></AggrViewComponent>
            </el-col>
        </el-row>
        </div>
    </div>
</template>


<script>
import Navigator from '../LLMEvals/Navigator.vue'
import api from '../LLMEvals/api'
import AggrViewComponent from '../LLMEvals/AggrViewComponent.vue'

export default {
  props: {
    subtasks: Array,
    task: Object,
    diff: Boolean,
    aggrData: Object,
    allSubTasks: Array
  },
  components: {
    Navigator,
    AggrViewComponent
  },
  data() {
    return {
      aggregratedReducedData: {},
      aggregratedReducedTotal: {},
      aggregratedReducedToolName: {},
      loading: false,
      cancelToken: {cancelled: true}
    }
  },
  watch: {
  },
  computed: {
    dynamicMargin() {
      if (this.aggrData) {
        const leng = this.aggrLength
        if (leng === 4) {
          return '32px'
        } else if (leng === 3) {
          return '72px'
        } else if (leng === 2) {
          return '128px'
        }
      }
      return '8px'
    },
    aggrLength() {
      if (this.aggrData) {
        return Object.entries(this.aggrData).length
      }
      return Object.entries(this.aggregratedReducedData).length
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    refresh() {
      if (this.cancelToken) {
        this.cancelToken.cancelled = true
      }
      this.cancelToken = { cancelled: false }
      this.aggregratedReducedData = {}
      this.aggregratedReducedToolName = {}
      this.aggregratedReducedTotal = {}
      if (this.aggrData === undefined) {
        this.getDetail(this.cancelToken)
        .catch((error) => {
          api.notifyError(this.$notify, '获取 subtask 的 data 详情失败', error)
        })
      }
    },
    getStatusContent(entrance, key) {
      let toolName = this.aggregratedReducedToolName[key]
      if (toolName.includes('SpendTime')) {
        return '平均: ' + entrance.result.toFixed(2) + ' 秒'
      } else {
        return entrance.__key__ + ' : ' + entrance.result + '个'
      }
    },
    calculatePercentiles(distribution, percentile) {
      const sortedDistribution = distribution.slice().sort((a, b) => a - b)
      const index = (percentile / 100) * (sortedDistribution.length - 1)
      const lowerIndex = Math.floor(index)
      const upperIndex = Math.ceil(index)
      const weight = index % 1

      if (upperIndex >= sortedDistribution.length) {
        return sortedDistribution[lowerIndex]
      } else {
        return sortedDistribution[lowerIndex] * (1 - weight) + sortedDistribution[upperIndex] * weight
      }
    },
    calculateRate(key) {
      let toolName = this.aggregratedReducedToolName[key]
      let dataList = this.aggregratedReducedData[key]
      let total = this.aggregratedReducedTotal[key]
      if (toolName.includes('ReportSumPass')) {
        let passed = dataList.filter(item => item.__key__ === '通过')
                        .reduce((sum, item) => sum + item.result, 0)
        return ((passed / total) * 100).toFixed(2) + '%'
      }
    },
    async getDetail(cancelToken) {
      this.loading = true
      for (const subtask of this.subtasks) {
        const r = await api.getDataList(this.task.spec_id, subtask.sub_task_id)
        if (cancelToken.cancelled) {
          console.log('getDetail 操作被取消')
          return
        }
        let key
        if (subtask.tool_name.includes('Exception')) {
          key = '错误统计'
        } else if (subtask.tool_name.includes('SpendTime')) {
          key = 'API-接口耗时'
        } else {
          key = subtask.args.scorer_name
        }
        if (!this.aggregratedReducedData.hasOwnProperty(key)) {
          this.aggregratedReducedData[key] = []
          this.aggregratedReducedTotal[key] = 0
        }
        this.aggregratedReducedData[key] = this.aggregratedReducedData[key].concat(r.data.data)
        if (r.data.data.length > 0) {
          const totalSum = r.data.data.reduce((sum, item) => {
            return item.total ? sum + item.total : sum
          }, 0)
          this.aggregratedReducedTotal[key] += totalSum
        }
        this.aggregratedReducedToolName[key] = subtask.tool_name
      }
      this.loading = false
    },
    searchSubTaskByToolName(toolName) {
      for (const subtask of this.allSubTasks) {
        if (subtask.tool_name === toolName) {
          return subtask
        }
      }
      return null
    },
    isScorer(toolName) {
      for (const subtask of this.allSubTasks) {
        if (subtask.tool_name === toolName) {
          return subtask.tool_type === 'Scorers'
        }
      }
      return false
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
    },
    showDialog() {
      this.dialogVisible = true
    }
  }
}
</script>


<style scoped>
.mt-container {
    padding: 16px;
}

.table-cell-section {
    width: 100%;
    display: inline-block;
}
.status-content {
    background-color: #F0F6FF; /* 背景色 */
    border-radius: 6px; /* 边框圆角 */
    padding: 8px; /* 内边距 */
    /*box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); !* 阴影效果 *!*/
    flex-basis: 18%;
    flex-shrink: 0;
    margin: 8px;
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
    font-size: 16px;
    line-height: 24px;
}
.status-content-diff {
    background-color: #ffedde; /* 背景色 */
    border-radius: 6px; /* 边框圆角 */
    padding: 8px; /* 内边距 */
    /*box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); !* 阴影效果 *!*/
    flex-basis: 18%;
    flex-shrink: 0;
    margin: 8px;
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
    font-size: 16px;
    line-height: 24px;
}

.status-aside-title {
    flex-basis: 3%;
    flex-shrink: 0;
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
}

.aggr-title {
    font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
    font-size: 14px;
    line-height: 22px;
    color:  #666;  /* 字体颜色 */
    margin-bottom: 5px; /* 下边距 */
    font-weight: 800; /* 字体加粗 */
    text-align: center;
}

.aggr-content {
    font-size: 16px; /* 字体大小 */
    color: #333; /* 字体颜色 */
    line-height: 1.5; /* 行高 */
    padding: 5px 0; /* 内边距 */
    text-align: center;
}

.flex-row {
    display: flex;
    align-items: stretch; /* 使所有el-col具有相同的高度 */
    overflow-x: scroll;
    width: inherit;
}

.vertical-text {
    writing-mode: vertical-rl; /* 文字垂直排列 */
    text-align: center;
    padding-top: 20px;
}
</style>
