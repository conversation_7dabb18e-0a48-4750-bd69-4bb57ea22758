<template>
    <div style="margin: 20px">
        <Row>
            <Col :span="11">
                <div class="chat-box">
                    <pre :class="getClassByRole(msg.role)"
                    v-for="msg in dispMessage(item.messages)"><em><b>{{msg.role.toUpperCase()}}</b></em><br>{{ msg.content }}</pre>
                </div>
            </Col>
            <Col :span="1"></Col>
            <Col :span="12">
                <div style="padding-top: 20px">
                    <h4>{{ 'score' }}
                    </h4>
                </div>
                <pre class="none-border-pre pre-scrollable-y" v-html="item.score"></pre>
                <div v-for="header in displayedHeaders" :key="header">
                    <h4>{{ header }}
                    </h4>
                    <pre class="none-border-pre pre-scrollable-y" v-html="item[header]"></pre>
                </div>
            </Col>
        </Row>
    </div>
</template>

<script>

export default {
  props: {
    frontEndConfig: Object,
    item: Object
  },
  computed: {
    displayedHeaders() {
      const dispHeaders = []
      for (let key in this.frontEndConfig) {
        if (this.frontEndConfig.hasOwnProperty(key)) {
          let value = this.frontEndConfig[key]
          if (value.show) {
            dispHeaders.push(key)
          }
        }
      }
      dispHeaders.sort((a, b) => {
        let aInd = this.getFeConfigHeaderIndex(a)
        let bInd = this.getFeConfigHeaderIndex(b)
        return aInd - bInd
      })
      let ignoreSet = new Set(this.itemIgnoreList)
      return dispHeaders.filter(item => !ignoreSet.has(item))
    }
  },
  methods: {
    getFeConfigHeaderIndex(field) {
      if (this.frontEndConfig[field] && this.frontEndConfig[field].index) {
        return this.frontEndConfig[field].index
      }
      return 0
    },
    dispMessage(messages) {
      if (!messages) {
        return []
      }
      return messages
    },
    get_fe_config(header) {
      if (this.frontEndConfig && this.frontEndConfig[header]) {
        return this.frontEndConfig[header]
      }
      return {}
    },
    getClassByRole(r) {
      const base = 'pre-scrollable-y '
      const role = r.toLowerCase()
      if (role === 'completion' || role.includes('custom')) {
        return base + 'chat-message-completion'
      } else if (role === 'input' || role === 'user') {
        return base + 'chat-message-input'
      } else if (role.includes('input') && role.includes('eval')) {
        return base + 'chat-message-eval-user'
      } else if (role.includes('user') && role.includes('eval')) {
        return base + 'chat-message-eval-user'
      } else if (role.includes('eval')) {
        return base + 'chat-message-eval-reason'
      } else {
        return base + 'chat-message-system'
      }
    }
  }
}
</script>

<style scoped>
.pre {
  max-width: 80%;
  overflow-x: auto; /* 当内容超出时显示滚动条 */
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 24px;
}

.chat-box {
  display: flex;
  flex-direction: column;
  gap: 4px; /* 增加聊天框之间的间距，使其更加美观 */
  padding: 16px; /* 增加内边距，使内容不会紧贴边缘 */
  border: 1px solid #E0E0E0; /* 添加边框，使聊天框更加突出 */
  border-radius: 10px; /* 增加圆角，使聊天框更加柔和 */
  /*box-shadow: 0 2px 4px rgba(0,0,0,0.1); !* 添加阴影，增加立体感 *!*/
  background-color: #F8F8F8; /* 调整聊天框背景色，使其更加柔和 */
}

.chat-message-system {
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 6px; /* 圆角 */
  padding: 4px 16px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: 0;
  max-width: 80%;
  margin-right: auto; /* 靠左 */
}

.chat-message-input {
  background-color: #C2DFFF; /* 浅蓝色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 6px; /* 圆角 */
  padding: 4px 16px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: 0;
  max-width: 80%;
  margin-right: auto; /* 靠左 */
}

.chat-message-completion {
  background-color: #DCF8C6; /* 浅绿色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 6px; /* 圆角 */
  padding: 4px 16px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-right: 0;
  max-width: 80%;
  margin-left: auto; /* 靠右 */
}

.chat-message-eval-user {
  background-color: #E4C9F7; /* 浅紫色背景 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
  border-radius: 6px; /* 圆角 */
  padding: 4px 16px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: 0;
  max-width: 80%;
  margin-right: auto; /* 靠左 */
}

.chat-message-eval-reason {
  background-color: #FFD9BF; /* 金色背景 */
  color: #000000; /* 字体颜色改为深色，增加对比度，提高可读性 */
  border-radius: 6px; /* 圆角 */
  padding: 4px 16px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  margin-left: auto;
  max-width: 80%;
  margin-right: 0; /* 靠右 */
}

.pre-scrollable-y {
  white-space: pre-wrap; /* 保留空白符，允许自动换行 */
  max-height: 400px; /* 设置最大高度，例如200px */
  overflow-y: auto; /* 内容超出时显示垂直滚动条 */
}

.none-border-pre{
    border: none; /* 取消边框 */
    max-width: 100% !important;
    background-color: transparent;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 4px;
    padding-bottom: 0px;
}
</style>