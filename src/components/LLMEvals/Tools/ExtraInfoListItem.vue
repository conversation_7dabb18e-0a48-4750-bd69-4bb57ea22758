<template>
    <ListItem>
        <ListItemMeta :avatar="avatar" class="custom-list-item-meta">
            <template #title>
                <Row>
                <Col :span="1"></Col>
                <Col :span="8">
                    <div>{{ headerMap[header] ? headerMap[header].label : header }}</div>
                </Col>
                <Col :span="3" v-if="get_fe_config(header)['action'] && !isEditing">
                    <mtd-button type="text" icon="mtdicon-edit-o" size="mini" circle
                        @click="start_editing(header, curItem)" >
                    </mtd-button>
                </Col>
                <Col :span="3" v-if="get_fe_config(header)['action'] && isEditing">
                    <mtd-button type="text" icon="mtdicon-check" size="mini" circle
                        @click="update_edit()" >
                    </mtd-button>
                </Col>
                <Col :span="3" v-if="get_fe_config(header)['action'] && isEditing">
                    <mtd-button type="text" icon="mtdicon-close" size="mini" circle
                        @click="end_editing(header, curItem)" >
                    </mtd-button>
                </Col>
                </Row>
            </template>
        </ListItemMeta>
        <div v-if="get_fe_config(header)['action'] && isEditing">
            <mtd-select v-model="editValue" style="width: 100%;" v-if="get_fe_config(header)['options']">
                <mtd-option v-for="item in get_options(get_fe_config(header)['options'])" :key="item.value" :label="item.label"
                :value="item.value"></mtd-option>
            </mtd-select>
            <mtd-textarea autosize v-model="editValue" style="width: 100%;" v-else/>
        </div>
        <template v-else>
            <vue-json-editor
                v-if="isJsonString(getDiffShowData(curItem, header)) && get_fe_config(header)['type'] === 'json'"
                :value="parseJson(getDiffShowData(curItem, header))"
                :show-btns="false"
                :expandedOnStart="false"
                mode="view"
            />
            <pre v-else class="none-border-pre info-pre-scrollable-y" v-html="getDiffShowData(curItem, header)"></pre>
        </template>
    </ListItem>
</template>

<script>
import api from '../../LLMEvals/api'
import vueJsonEditor from 'vue-json-editor'

export default {
  components: {
    vueJsonEditor
  },
  props: {
    header: String,
    headerMap: Object,
    curItem: Object,
    frontEndConfig: Object,
    avatar: String
  },
  data() {
    return {
      expended: true,
      isEditing: false,
      editValue: null
    }
  },
  computed: {
    editItem() {
      return this.curItem
    },
    editHeader() {
      return this.header
    }
  },
  methods: {
    isJsonString(str) {
      try {
        if (typeof str === 'string') {
          JSON.parse(str)
          return true
        } else if (typeof str === 'object') {
          return true
        }
        return false
      } catch (e) {
        return false
      }
    },
    parseJson(str) {
      try {
        return JSON.parse(str)
      } catch (e) {
        return str
      }
    },
    get_options(optionStr) {
      return optionStr.split(/[,，]/).map(option => ({label: option.trim(), value: option.trim()}))
    },
    get_fe_config(header) {
      if (this.frontEndConfig && this.frontEndConfig[header]) {
        return this.frontEndConfig[header]
      }
      return {}
    },
    highlightDifferences(current, diff) {
      let currentRes = ''
      let diffRes = ''
      let i = 0
      let j = 0
      while (i < current.length && j < diff.length) {
        if (current[i] === diff[j]) {
          currentRes += current[i]
          diffRes += current[i]
          i++
          j++
        } else {
          let startI = i
          let startJ = j
          while (i < current.length && j < diff.length && current[i] !== diff[j]) {
            i++
            j++
          }
          if (startI < i) {
            currentRes += `<span style="color: #DB3037;">${current.slice(startI, i)}</span>`
          }
          if (startJ < j) {
            diffRes += `<span style="color: #02A171;">${diff.slice(startJ, j)}</span>`
          }
        }
      }
      if (i < current.length) {
        currentRes += `<span style="color: #DB3037;">${current.slice(i)}</span>`
      }
      if (j < diff.length) {
        diffRes += `<span style="color: #02A171;">${diff.slice(j)}</span>`
      }
      return `当前: ${currentRes}<br>历史: ${diffRes}`
    },
    isDiffData(item, header) {
      const value = item[header]
      if (!value) {
        return false
      }
      if (typeof value !== 'object') {
        return false
      }
      if (typeof value.current === 'undefined' || value.current === null) {
        return false
      }
      return true
    },
    getDiffShowData(item, header) {
      const value = item[header]
      if (!this.isDiffData(item, header)) {
        return value
      }
      let {current, diff} = value
      if (['score', '__spend_seconds__'].includes(header)) {
        return `当前: ${current}<br>历史: ${diff}`
      } else if (['__id__', '__es_id__', 'update_time'].includes(header)) {
        return current
      }
      try {
        if (this.frontEndConfig[header].diff) {
          current = typeof current === 'object' ? JSON.stringify(current, null, 0) : current
          diff = typeof diff === 'object' ? JSON.stringify(diff, null, 0) : diff
          return this.highlightDifferences(current, diff)
        }
      } catch (error) {
        console.warn(error)
        return current
      }
      console.log(current)
      return current
    },
    start_editing(header, item) {
      this.isEditing = true
      let v = ''
      if (item[header]) {
        v = item[header]
        if (this.isDiffData(item, header)) {
          v = item[header].current
        }
      }
      this.editValue = JSON.parse(JSON.stringify(v))  // 深拷贝
    },
    end_editing() {
      this.isEditing = false
    },
    update_edit() {
      const esId = this.editItem['__es_id__']
      const header = this.header
      const oldValue = this.editItem[header] ? this.editItem[header].current || this.editItem[header] : ''
      let changeLog
      if (this.editValue === oldValue) {
        this.isEditing = false
        return
      }
      const targetItem = this.curItem
      if (targetItem) {
        const v = targetItem[header]
        if (v && typeof v === 'object' && v.current) {
          targetItem[header].current = this.editValue
        } else {
          targetItem[header] = this.editValue
        }
      }
      const feConfig = this.get_fe_config(this.header)
      if (feConfig.action && feConfig.type) {
        changeLog = {
          'action': feConfig.action,
          'field': this.header,
          'type': feConfig.type,
          'new_value': this.editValue,
          'old_value_': oldValue
        }
        api.updateDataItem(esId, changeLog).then((r) => {
          this.isEditing = false
          this.$notify({ title: '保存成功', message: '', type: 'success' })
        }).catch(e => {
          api.notifyError(this.$notify, '保存失败', e)
        })
      }
    }
  }
}
</script>

<style scoped>
.custom-list-item-meta{
    margin-bottom: 2px !important;
    font-style: italic; /* 斜体 */
}

.none-border-pre{
    border: none; /* 取消边框 */
    max-width: 100% !important;
    background-color: transparent;
    padding-left: 0px;
    padding-right: 16px;
    padding-top: 0px;
    padding-bottom: 0px;
    margin: 0;
}

.info-pre-scrollable-y {
  white-space: pre-wrap; /* 保留空白符，允许自动换行 */
  max-height: 200px;
  overflow-y: auto;
}
</style>
