<template>
  <div>
    <mtd-form inline>
        <mtd-form-item :label="key" v-for="(value, key, index) in dict" :key="index">
            <mtd-input v-model="dict[key]" :placeholder="'请输入' + key + '的值'"></mtd-input>
            <mtd-button type="danger" @click="removeField(key)" icon="minus" circle></mtd-button>
        </mtd-form-item>
        <mtd-form-item label="新字段">
            <mtd-input v-model="newField" placeholder="字段名"></mtd-input>
            <mtd-button type="primary" @click="addField" circle icon="add"></mtd-button>
        </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  name: 'StringDictEditor',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  created() {
    this.initializeText()
  },
  data() {
    return {
      dict: {},
      newField: ''
    }
  },
  watch: {
    dict(newVal) {
      this.$emit('input', newVal)
    },
    value(newVal) {
      this.initializeText()
    }
  },
  methods: {
    initializeText() {
      this.dict = this.value
      if (!this.dict) {
        this.dict = {}
      }
    },
    addField() {
      if (this.newField && !(this.newField in this.dict)) {
        this.$set(this.dict, this.newField, '')
        this.newField = ''
      }
    },
    removeField(key) {
      this.$delete(this.dict, key)
    }
  }
}
</script>

<style scoped>
.dict-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.add-field {
  display: flex;
  align-items: center;
}

.add-field mtd-input {
  margin-right: 10px;
}
</style>
