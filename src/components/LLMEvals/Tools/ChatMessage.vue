<template>
    <div :class="getClassByRole(msg.role)">
        <div style="font-size: 14px; color: #333;" class="chat-box-title" @click="toggleExpend">
            <i :class="['mtdicon', expended ? 'mtdicon-up-thick' : 'mtdicon-down-thick']"></i>
            {{getRoleChineseName(msg.role)}}
            <sup>{{ tag }}</sup>
        </div>
        <mtd-collapse-transition>
            <div v-show="expended">
                <pre class="none-border-pre">{{ msg.content }}</pre>
            </div>
        </mtd-collapse-transition>
    </div>
</template>

<script>

export default {
  props: {
    msg: Object,
    tag: String
  },
  data() {
    return {
      expended: true
    }
  },
  methods: {
    getRoleChineseName(role) {
      let r = role.toUpperCase()
      const mapper = {
        'INPUT': '用户输入',
        'USER': '历史提问',
        'ASSISTANT': '历史回答',
        'COMPLETION': '模型输出',
        'SYSTEM': '背景提示',
        'EVAL-ASSISTANT': '评测过程',
        'EVAL': '评测结果',
        'EVAL-USER': '评测方法'
      }
      return mapper[r] || r
    },
    getClassByRole(r) {
      const base = 'chat-message-common '
      const role = r.toLowerCase()
      if (role === 'completion' || role.includes('custom')) {
        return base + 'chat-message-completion'
      } else if (role === 'input' || role === 'user') {
        return base + 'chat-message-input'
      } else if (role.includes('input') && role.includes('eval')) {
        return base + 'chat-message-eval-user'
      } else if (role.includes('user') && role.includes('eval')) {
        return base + 'chat-message-eval-user'
      } else if (role === 'eval-assistant') {
        return base + 'chat-message-eval-assistant'
      } else if (role.includes('eval')) {
        return base + 'chat-message-eval-reason'
      } else {
        return base + 'chat-message-system'
      }
    },
    toggleExpend() {
      this.expended = !this.expended
    }
  }
}
</script>

<style scoped>
.chat-box-title {
  font-style: italic; /* 斜体 */
  font-size: 12px !important; /* 小字 */
  height: auto;
  font-weight: bold; /* 加粗 */
  color: #333; /* 字体颜色 */
}

.chat-message-common{
    width: 90%;
    margin-bottom: 4px;
}

.chat-message-common pre{
  border-radius: 6px; /* 圆角 */
  padding: 2px 8px; /* 增加内边距，使文字不会紧贴边缘 */
  font-size: 14px; /* 调整字体大小，提高可读性 */
  border: 1px solid #ccc; /* 添加边框 */
  color: #000000; /* 字体颜色改为黑色，增加对比度，提高可读性 */
}

.chat-message-input,
.chat-message-system,
.chat-message-eval-user,
.chat-message-eval-assistant{
    margin-left: 0;
    margin-right: auto; /* 靠左 */
}

.chat-message-completion .chat-box-title,
.chat-message-eval-reason .chat-box-title {
    text-align: right;
}

.chat-message-completion,
.chat-message-eval-reason,
.chat-message-completion .chat-box-title,
.chat-message-eval-reason .chat-box-title {
    margin-right: 0;
    margin-left: auto; /* 靠右 */
}

.chat-message-eval-assistant pre{
    background-color: rgb(238, 238, 238);
}

.chat-message-input pre{
  background-color: #C2DFFF; /* 浅蓝色背景 */
}

.chat-message-completion pre{
  background-color: #DCF8C6; /* 浅绿色背景 */
}

.chat-message-eval-user pre{
  background-color: #E4C9F7; /* 浅紫色背景 */
}

.chat-message-eval-reason pre{
  background-color: #FFD9BF; /* 金色背景 */
}

.none-border-pre{
    border: none; /* 取消边框 */
    max-width: 100% !important;
    background-color: transparent;
    padding-left: 0px;
    padding-right: 16px;
    padding-top: 0px;
    padding-bottom: 0px;
    margin: 0;
    white-space: pre-wrap; /* 自动换行 */
    word-wrap: break-word; /* 支持IE */
}

</style>
