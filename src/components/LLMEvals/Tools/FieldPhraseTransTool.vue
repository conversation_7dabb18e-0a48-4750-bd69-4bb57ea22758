<template>
    <div>
        <mtd-announcement title="提示" type="info" show-icon>
            <template #description>
                使用这个工具，能够帮助你快速进行数据转换，或者理解和调试 jpath，re-pattern。注意，这个工具可能会覆盖原数据
            </template>
        </mtd-announcement>
        <div style="height: 10px;"></div>
        <mtd-form inline>
            <mtd-form-item label="API名称">
                <mtd-input v-model="toolName" style="width: 200px;"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="切换展示">
                <mtd-button @click="switchToolMode" circle icon="mtdicon-share-arrow-fill" type="primary"></mtd-button>
            </mtd-form-item>
            <mtd-form-item label="开始执行">
                <mtd-button @click="runTool" circle icon="mtdicon-paperplane" type="primary"></mtd-button>
            </mtd-form-item>
            <mtd-form-item label="进度">
                <mtd-progress style="width: 260px;" type="steps" size="small" 
                :steps="data.length" 
                :percentage="percent"
                :show-info="false"
                class="progress-base">
                </mtd-progress>
                <span>{{ runningStep }} / {{ data.length }}</span>
            </mtd-form-item>
        </mtd-form>
        <div v-if="!coding">
            <Divider>字段转换参数</Divider>
            <div style="height: 10px;"></div>
            <mtd-form inline>
                <mtd-form-item label="输入字段" required helper="请填写一个待解析的字符串或者 json 对象的字段名称">
                    <mtd-input v-model="toolConfig.field_name" style="width: 500px;"
                    placeholder="field_name"></mtd-input>
                </mtd-form-item>
                <mtd-form-item label="输出字段" required helper="请填写一个空的字段名称">
                    <mtd-input v-model="toolConfig.output_field_name" style="width: 500px;"
                    placeholder="output_field_name"></mtd-input>
                </mtd-form-item>
                <mtd-form-item label="识别模式" required>
                    <mtd-select v-model="toolConfig.recognize" :options="recognizeOptions" style="width: 200px;">
                    </mtd-select>
                </mtd-form-item>
                <mtd-form-item label="jpath" v-if="toolConfig.recognize=='json'">
                    <template #helper>
                        用于识别这个字段的内容，如果写 . 那么就是返回值的 json 整体，<br>
                        如果填 $.data 就是 json 对象的 data 字段。建议先填 . 看下效果。
                    </template>
                    <mtd-select style="width: 300px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                    v-model="toolConfig.jpath" :options="jpathPatterns">
                        <template #footer>
                            <div class="create-option">
                                <mtd-input v-model="createName" style="margin-right:8px" />
                                <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                            </div>
                        </template>
                    </mtd-select>
                </mtd-form-item>
                <mtd-form-item label="行内聚焦" helper="关注这一行中的什么 pattern，请输入 re 表达式" v-if="toolConfig.recognize=='mutiline'">
                    <mtd-select style="width: 300px;" filterable clearable allow-create
                    v-model="toolConfig.pattern" :options="rePatterns">
                        <template #footer>
                            <div class="create-option">
                                <mtd-input v-model="createName2" style="margin-right:8px" />
                                <mtd-button type="primary" @click="rePatterns.push({'label': createName2, 'value': createName2})" :disabled="!createName2">新建</mtd-button>
                            </div>
                        </template>
                    </mtd-select>
                </mtd-form-item>
                <!-- <mtd-form-item label="聚焦行">
                    <mtd-input-number v-model="toolConfig.linenum" :step="50" :min="0" style="width: 200px;"
                    :formatter="num => `${num}行`" :parser="num => num.replace('行','')"></mtd-input-number>
                </mtd-form-item> -->
                <!-- <mtd-form-item label="score_mapping">
                    <string-dict-editor style="width: 500px;" v-model="toolConfig.score_mapping"/>
                </mtd-form-item> -->
            </mtd-form>
        </div>
        <pre v-else>{{ toolCode }}</pre>
    </div>
    </template>
    
    <script>
    import api from '../api'
    import StringDictEditor from '../Tools/StringDictEditor.vue'
    import JsonInputer from '../Tools/JsonInputer.vue'
    export default {
      props: {
        data: Array,
        toolName: {
          type: String,
          default: 'Trans'
        },
        tool: {
          type: Object,
          default: () => ({
            cls: 'toolchain_llmeval.workflow.workflow_tools.preparer.field_preparer:FieldPhrasePreparer',
            args: {
              field_name: 'context',
              recognize: 'json',
              jpath: '.',
              pattern: '(.*)',
              linenum: 0,
              score_mapping: {},
              default_score: 0,
              output_field_name: '_phrased_'
            }
          })
        }
      },
      components: {
        StringDictEditor,
        JsonInputer
      },
      data() {
        return {
          recognizeOptions: [{'value': 'json', 'label': 'json'}, {'value': 'mutiline', 'label': '多行'}, {'value': 'none', 'label': '无'}],
          toolConfig: this.tool.args,
          resultData: [],
          coding: false,
          toolCode: {},
          runningStep: 0,
          createName: '',
          jpathPatterns: api.jpathPatterns,
          createName2: '',
          rePatterns: api.rePatterns
        }
      },
      computed: {
        percent() {
          return this.runningStep / this.data.length * 100
        }
      },
      methods: {
        step() {
          this.$emit('step')
          this.runningStep = this.runningStep + 1
        },
        done(data) {
          this.$emit('done', data)
        },
        begin() {
          this.$emit('begin')
          this.runningStep = 0
        },
        cleanObject(obj) {
          const result = {}
          Object.keys(obj).forEach(key => {
            const value = obj[key]
            if (typeof value === 'object' && value !== null && !(value instanceof Array)) {
              const cleanedObject = this.cleanObject(value)
              if (Object.keys(cleanedObject).length > 0) {
                result[key] = cleanedObject
              }
            } else if (value instanceof Array) {
              const cleanedArray = value
              .map(item => (typeof item === 'object' && item !== null ? this.cleanObject(item) : item))
              .filter(item => !(item === null || item === undefined || item === '' || (typeof item === 'object' && Object.keys(item).length === 0)))
              if (cleanedArray.length > 0) {
                result[key] = cleanedArray
              }
            } else if (!(value === null || value === undefined || value === '')) {
              result[key] = value
            }
          })
          return result
        },
        switchToolMode() {
          const toolCode = {APICompletionFns: {[this.toolName]: this.tool}}
          this.toolCode = this.cleanObject(toolCode)
          this.coding = !this.coding
        },
        async runTool() {
          this.begin()
          if (!this.data || this.data.length === 0) {
            api.notifyError(this.$notify, '当前数据条目为 0 条', Error('缺少数据'))
            this.done(this.data)
            return
          }
          this.resultData = []
          for (const item of this.data) {
            try {
              const result = await api.onlineFieldTransform({
                payload: {
                  item: item,
                  id: 'RANDOM-CHOOSE',
                  ds_name: 'RANDOM-CHOOSE'
                },
                config: this.tool
              })
              this.resultData.push(result.data.item)
              this.step()
            } catch (error) {
              api.notifyError(this.$notify, '接口访问错误', error)
              this.step()
            }
          }
          this.done(this.resultData)
        }
      }
    }
    </script>