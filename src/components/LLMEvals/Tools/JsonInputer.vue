<template>
<div>
    <textarea v-model="text" :class="validateJSON()" 
    auto multi-line :placeholder="'请输入 json'"
    @keydown="insertTab"
    ></textarea>
</div>
</template>

<script>
export default {
  name: 'JsonInputer',
  props: {
    value: {
      type: [String, Object, Array],
      default: () => '{}'
    },
    valid: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      text: ''
    }
  },
  created() {
    this.initializeText()
  },
  watch: {
    text(newVal) {
      if (typeof this.value === 'object' || Array.isArray(this.value)) {
        this.$emit('input', JSON.parse(newVal)) // 当text变化时，触发input事件，将新值传递给父组件
      } else {
        this.$emit('input', newVal)
      }
    },
    value(newVal) {
      this.initializeText()
    }
  },
  methods: {
    initializeText() {
      if (typeof this.value === 'object' || Array.isArray(this.value)) {
        // 如果value是对象，转换为JSON字符串
        this.text = JSON.stringify(this.value, null, 2) // 美化JSON格式
      } else {
        // 如果value是字符串，直接使用
        this.text = this.value
      }
    },
    validateJSON() {
      if (!this.valid) {
        return 'code-input'
      }
      try {
        JSON.parse(this.text)
        return 'code-input-success'
      } catch (error) {
        return 'code-input-error'
      }
    },
    insertTab(event) {
      const start = event.target.selectionStart
      const end = event.target.selectionEnd
      if (event.key === 'Tab' && !event.shiftKey) {
        event.preventDefault() // 阻止默认的Tab键行为
    // 在光标位置插入两个空格
        this.text = this.text.substring(0, start) + '  ' + this.text.substring(end)
    // 使用Vue.nextTick确保DOM更新完成
        this.$nextTick(() => {
      // 移动光标到插入空格后的位置
          event.target.selectionStart = event.target.selectionEnd = start + 2
          event.target.focus()
        })
      } else if (event.key === 'Tab' && event.shiftKey) {
        event.preventDefault() // 阻止默认的Shift+Tab键行为
    // 检查光标前的两个字符是否为空格，并且确保start和end相等，即没有选中文本
        if (start === end && this.text.substring(start - 2, start) === '  ') {
      // 删除前两个空格
          this.text = this.text.substring(0, start - 2) + this.text.substring(end)
          this.$nextTick(() => {
        // 更新光标位置
            event.target.selectionStart = event.target.selectionEnd = start - 2
            event.target.focus()
          })
        }
      } else if (event.key === 'Enter') {
        event.preventDefault() // 阻止默认的Enter键行为
        const textUpToCursor = this.text.substring(0, start)
        const lastNewLineIndex = textUpToCursor.lastIndexOf('\n')
        let indent = ''
        if (lastNewLineIndex !== -1) {
          const lineAfterLastNewLine = textUpToCursor.substring(lastNewLineIndex + 1)
          const match = lineAfterLastNewLine.match(/^(\s+)/)
          if (match) {
            indent = match[1] // 获取上一行的缩进
          }
        }
    // 在光标位置插入换行符和相同数量的空格
        this.text = this.text.substring(0, start) + '\n' + indent + this.text.substring(end)
        const newPos = start + 1 + indent.length // 更新光标位置
        this.$nextTick(() => {
          event.target.selectionStart = event.target.selectionEnd = newPos
          event.target.focus()
        })
      }
    }
  }
}
</script>
  
<style scoped>
.code-input {
    font-family: monospace; /* 使字体更适合代码显示 */
    border-radius: 6px;
    border: 1px solid #ccc; /* 添加边框 */
    outline: none;
    width: 100%;
    height: 100%;
    line-height: 16px;
    padding: 10px;
}
.code-input-error {
    font-family: monospace; /* 使字体更适合代码显示 */
    border-radius: 6px;
    border: 1px solid #FF1F1F; /* 添加边框 */
    outline: none;
    width: 100%;
    height: 100%;
    line-height: 16px;
    padding: 10px;
}
.code-input-success {
    font-family: monospace; /* 使字体更适合代码显示 */
    border-radius: 6px;
    border: 1px solid #00A854; /* 添加边框 */
    outline: none;
    width: 100%;
    height: 100%;
    line-height: 16px;
    padding: 10px;
}
</style>
  