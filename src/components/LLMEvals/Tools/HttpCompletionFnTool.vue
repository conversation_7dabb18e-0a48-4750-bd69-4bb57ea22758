<template>
<div>
    <mtd-announcement title="提示" type="info" show-icon>
        <template #description>
            1. 可以用 HTTP 接口访问 RD 的机器人，HTTP 接口会覆盖上传得到的数据，如果有问题请重新上传数据<br>
            2. HTTP 接口暂无流式访问方法，仅支持返回 json 格式内容，通过 jsonpath 来定义最终有效的回复内容，更多 jsonpath 内容请参考<a target="_blank" href="https://gotest.hz.netease.com/doc/jie-kou-ce-shi/xin-zeng-yong-li/can-shu-xiao-yan/jsonpi-pei/jsonpathyu-fa.html"><span class="text-head" style="color: #E65100;">JsonPath 用法示例</span></a><br>
            3. 使用默认的 HTTP 连接工具，请参考<a target="_blank" href="https://km.sankuai.com/collabpage/**********"><span class="text-head" style="color: #E65100;">HTTPCompletionFn 使用</span></a><br>
            4. 通过切换展示按钮，你可以直接获得当前工具的 yaml 配置，可以在本地/远程任务中使用
        </template>
    </mtd-announcement>
    <div style="height: 10px;"></div>
    <mtd-form inline>
        <mtd-form-item label="API名称">
            <mtd-input v-model="toolName" style="width: 200px;"></mtd-input>
        </mtd-form-item>
        <mtd-form-item label="切换展示">
            <mtd-button @click="switchToolMode" circle icon="mtdicon-share-arrow-fill" type="primary"></mtd-button>
        </mtd-form-item>
        <mtd-form-item label="开始执行">
            <mtd-button @click="runTool" circle icon="mtdicon-paperplane" type="primary"></mtd-button>
        </mtd-form-item>
        <mtd-form-item label="进度">
            <mtd-progress style="width: 260px;" type="steps" size="small" 
            :steps="data.length" 
            :percentage="percent"
            :show-info="false"
            class="progress-base">
            </mtd-progress>
            <span>{{ runningStep }} / {{ data.length }}</span>
        </mtd-form-item>
    </mtd-form>
    <div v-if="!coding">
        <Divider>接口基本信息</Divider>
        <div style="height: 10px;"></div>
        <mtd-form inline>
            <mtd-form-item label="HOST" required helper="请将域名 HOST 和具体路径 URI 以及 GET 参数分开传递，因为这样会影响 Oceanus 鉴权逻辑">
                <mtd-input v-model="toolConfig.host" style="width: 500px;"
                placeholder="HOST 例如 https://qa.sankuai.com 必须填写协议头例如 http/https"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="请求方法" required>
                <mtd-select v-model="toolConfig.method" :options="httpMethodOptions" style="width: 200px;">
                </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="URI" required>
                <mtd-input v-model="toolConfig.uri" style="width: 500px;"
                placeholder="URI 例如 /evals/remote/tasklist 如果 URI 放在 HOST 里可能影响 MWS 鉴权"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="超时时间">
                <mtd-input-number v-model="toolConfig.timeout" :step="50" :min="0" style="width: 200px;"
                :formatter="num => `${num}秒`" :parser="num => num.replace('秒','')"></mtd-input-number>
            </mtd-form-item>
            <mtd-form-item label="首要填充内容">
                <template #helper>
                    首要填充内容，会出现于数据的 completion 字段中。如果写 . 那么就是返回值的 json 整体，<br>
                    如果填 $.data 就是 json 对象的 data 字段。建议先填 . 看下效果。
                </template>
                <mtd-select style="width: 500px;" filterable clearable allow-create placeholder="请填写 jpath 例如 $.data.answer"
                v-model="toolConfig.completion_jpath" :options="jpathPatterns">
                    <template #footer>
                        <div class="create-option">
                            <mtd-input v-model="createName" style="margin-right:8px" />
                            <mtd-button type="primary" @click="jpathPatterns.push({'label': createName, 'value': createName})" :disabled="!createName">新建</mtd-button>
                        </div>
                    </template>
                </mtd-select>
            </mtd-form-item>
            <br/>
            <mtd-form-item label="请求体">
                <template #helper>
                    请求体和GET参数，都支持字符串替换功能<br>
                    大括号{}之内的纯字母、数字以及下划线(不能有空格)，都会被认为是替换的对象<br>
                    替换对象字段名必须于上传的数据对应，替换内容由上传的数据决定
                </template>
                <json-inputer style="width: 500px; height: 300px;" v-model="toolConfig.body_template"></json-inputer>
            </mtd-form-item>
            <mtd-form-item label="GET参数">
                <string-dict-editor style="width: 500px;" v-model="toolConfig.param_template"/>
            </mtd-form-item>
        </mtd-form>
        <Divider>额外待填充内容</Divider>
        <mtd-announcement title="提示" type="info" show-icon>
            <template #description>
                这里是除了 completion 首要填充内容外，还希望关注的内容，也是 jsonpath 的方式获取，结果回填到数据的相应字段中去<br>
                更多 jsonpath 内容请参考<a target="_blank" href="https://gotest.hz.netease.com/doc/jie-kou-ce-shi/xin-zeng-yong-li/can-shu-xiao-yan/jsonpi-pei/jsonpathyu-fa.html"><span class="text-head" style="color: #E65100;">JsonPath 用法示例</span></a>
            </template>
        </mtd-announcement>
        <div style="height: 10px;"></div>
        <string-dict-editor v-model="toolConfig.extension_output_field_jpath"/>
        <Divider>鉴权逻辑</Divider>
        <mtd-announcement title="提示" :description="announcement" v-if="announcement" type="info" show-icon/>
        <div style="height: 10px;"></div>
        <mtd-form inline>
            <mtd-form-item label="鉴权方法">
                <mtd-select v-model="toolConfig.auth_method" :options="authMethodOptions" style="width: 150px;"></mtd-select>
            </mtd-form-item>
            <mtd-form-item label="sso字段名" v-if="toolConfig.auth_method==='sso_token'">
                <mtd-input v-model="toolConfig.sso_field_name" placeholder="例如 573beaded4_ssoid"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="token" v-if="['sso_token', 'bearer_token'].includes(toolConfig.auth_method)">
                <mtd-input textarea auto-height multi-line style="width: 500px;" v-model="toolConfig.lion_token_name" placeholder="请填写 token"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="mws密钥" v-if="toolConfig.auth_method==='mws'">
                <mtd-input v-model="toolConfig.lion_mws_secretary_name" placeholder="请到 MWS 申请密钥"></mtd-input>
            </mtd-form-item>
            <mtd-form-item label="token" v-if="toolConfig.auth_method==='oceanus'">
                <mtd-input textarea auto-height multi-line style="width: 500px;" v-model="toolConfig.lion_oceanus_token_name" placeholder="请填写 token"></mtd-input>
            </mtd-form-item>
        </mtd-form>
        <Divider>Headers</Divider>
        <string-dict-editor v-model="toolConfig.headers"/>
        <Divider>Cookie</Divider>
        <string-dict-editor v-model="toolConfig.cookie"/>
    </div>
    <pre v-else>{{ toolCode }}</pre>
</div>
</template>

<script>
import api from '../api'
import StringDictEditor from '../Tools/StringDictEditor.vue'
import JsonInputer from '../Tools/JsonInputer.vue'
export default {
  props: {
    data: Array,
    toolName: String,
    tool: {
      type: Object,
      default: () => ({
        cls: 'toolchain_llmeval.workflow.workflow_tools.api_completion_fns.default:HTTPCompletionFn',
        args: {
          host: '',
          uri: '',
          method: 'GET',
          completion_jpath: '',
          param_template: {},
          body_template: '',
          auth_method: 'none',
          extension_output_field_jpath: {},
          lion_token_name: null,
          lion_mws_secretary_name: null,
          lion_oceanus_token_name: null,
          sso_field_name: null,
          server_appkey: null,
          timeout: 1200,
          headers: {},
          cookie: {}
        }
      })
    }
  },
  components: {
    StringDictEditor,
    JsonInputer
  },
  data() {
    return {
      httpMethodOptions: [{'value': 'GET', 'label': 'GET'}, {'value': 'POST', 'label': 'POST'}],
      authMethodOptions: ['sso_token', 'bearer_token', 'oceanus', 'shepherd', 'mws', 'none'].map(v => { return {value: v, label: v} }),
      toolConfig: this.tool.args,
      resultData: [],
      coding: false,
      toolCode: {},
      runningStep: 0,
      jpathPatterns: api.jpathPatterns,
      createName: ''
    }
  },
  computed: {
    percent() {
      return this.runningStep / this.data.length * 100
    },
    announcement() {
      if (this.toolConfig.auth_method === 'none') {
        return '建议选择一个鉴权方式，例如 shepherd 鉴权、oceanus 鉴权。如果是 IP 直连，请注意环境隔离，当前访问的是 prod 环境'
      } else if (this.toolConfig.auth_method === 'sso_token') {
        return '前端接口的 SSO 鉴权，需要从前端的 cookie 里获取 token，方法: f12->应用->Cookie->找到带有 ssoid 的内容。如果有 test 环境自动登陆的需要，请联系 liyilun02'
      } else if (this.toolConfig.auth_method === 'mws') {
        return 'MCM、COE、OCTO等基础服务，都会挂在 MWS 上，如果需要鉴权，要给他们提 TT'
      } else if (this.toolConfig.auth_method === 'shepherd') {
        return '牧羊人 shepherd 会代理一些 thrift 请求，这个需要 RD 配置，如果要用的话，让 RD 给 aiengineering_llmeval 授权分组，授权以后就能用了'
      } else if (this.toolConfig.auth_method === 'oceanus') {
        return '这个需要 RD 在 Oceanus 上给 com.sankuai.aiengineering 授权，联系 @liyilun02 找到密钥信息'
      } else if (this.toolConfig.auth_method === 'bearer_token') {
        return '这个需要 RD 提供一个服务鉴权的 Token，具体而言就是请求头里面写入 Authorization: Bearer xxxx 即可'
      }
      return null
    }
  },
  methods: {
    step() {
      this.$emit('step')
      this.runningStep = this.runningStep + 1
    },
    done(data) {
      this.$emit('done', data)
    },
    begin() {
      this.$emit('begin')
      this.runningStep = 0
    },
    cleanObject(obj) {
      const result = {}
      Object.keys(obj).forEach(key => {
        const value = obj[key]
        if (typeof value === 'object' && value !== null && !(value instanceof Array)) {
          const cleanedObject = this.cleanObject(value)
          if (Object.keys(cleanedObject).length > 0) {
            result[key] = cleanedObject
          }
        } else if (value instanceof Array) {
          const cleanedArray = value
          .map(item => (typeof item === 'object' && item !== null ? this.cleanObject(item) : item))
          .filter(item => !(item === null || item === undefined || item === '' || (typeof item === 'object' && Object.keys(item).length === 0)))
          if (cleanedArray.length > 0) {
            result[key] = cleanedArray
          }
        } else if (!(value === null || value === undefined || value === '')) {
          result[key] = value
        }
      })
      return result
    },
    switchToolMode() {
      const toolCode = {APICompletionFns: {[this.toolName]: this.tool}}
      this.toolCode = this.cleanObject(toolCode)
      this.coding = !this.coding
    },
    async runTool() {
      this.begin()
      if (!this.data || this.data.length === 0) {
        api.notifyError(this.$notify, '当前数据条目为 0 条', Error('缺少数据'))
        this.done(this.data)
        return
      }
      this.resultData = []
      for (const item of this.data) {
        try {
          const result = await api.onlineHttpRun({
            payload: {
              item: item,
              id: 'RANDOM-CHOOSE',
              ds_name: 'RANDOM-CHOOSE'
            },
            config: this.tool.args
          })
          this.resultData.push(result.data.item)
          this.step()
        } catch (error) {
          api.notifyError(this.$notify, '接口访问错误', error)
          this.step()
        }
      }
      this.done(this.resultData)
    }
  }
}
</script>