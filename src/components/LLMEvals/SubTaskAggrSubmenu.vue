<template>
    <Submenu icon="checkbox-checked-o" :name="sub_task.sub_task_id">
        <template slot="title">
            <Icon type="md-checkbox-outline" />
            {{ sub_task.tool_name }}
        </template>
        <template v-if="loading">
            <mtd-loading></mtd-loading>
        </template>
        <template v-else>
            <template v-for="(v, k, index) in aggrResultConfig">
            <Badge v-if="aggrResult[k]"
                    :count=aggrResult[k].value overflow-count="999"
                    style="vertical-align: middle; line-height: 1;" :offset="[19, 19]"
                    :type="v.color" v-show="aggrResult[k].value>0">
                <div style="width: 160px">
                    <MenuItem :name="`${sub_task.sub_task_id}*${v.scoreShift}`">{{v.text}}</MenuItem>
                </div>
            </Badge>
            </template>
        </template>
    </Submenu>
</template>

<script>
import api from '../LLMEvals/api'

export default {
  props: {
    sub_task: Object,
    task_id: String,
    diff_task_id: String,
    enableDiffMode: Boolean
  },
  data() {
    return {
      aggrResult: {},
      loading: false,
      aggrResultConfig: {
        '总数据条目数': {
          'color': 'primary',
          'text': '全部用例',
          'scoreShift': 'clear'
        },
        '通过数量': {
          'color': 'success',
          'text': '通过用例',
          'scoreShift': 'pass'
        },
        '不通过数量': {
          'color': 'error',
          'text': '不通过用例',
          'scoreShift': 'unpass'
        },
        '异常数量': {
          'color': 'warning',
          'text': '接口错误',
          'scoreShift': 'error'
        },
        '新增通过': {
          'color': 'success',
          'text': '新增通过',
          'scoreShift': 'new_pass'
        },
        '新增不通过': {
          'color': 'error',
          'text': '新增不通过',
          'scoreShift': 'new_unpass'
        }
      }
    }
  },
  mounted() {
    this.refresh()
  },
  watch: {
    enableDiffMode() {
      this.refresh()
    },
    isOpen(v) {
      this.refresh()
    }
  },
  methods: {
    refresh() {
      let diffTaskId = this.diff_task_id
      let taskId = this.task_id
      let subTaskId = this.sub_task.sub_task_id
      if (!this.enableDiffMode) {
        diffTaskId = null
      }
      if (!taskId) {
        this.aggrResult = {}
        return
      }
      this.loading = true
      api.aggrData(taskId, subTaskId, diffTaskId).then(r => {
        this.aggrResult = r.data
        this.$emit('subTaskAggrInfo', this.aggrResult, this.sub_task)
        this.loading = false
      }).catch((error) => {
        console.error(error)
        this.loading = false
        api.notifyError(this.$notify, '获取菜单信息失败', error)
      })
    }
  }
}
</script>
