<template>
    <div>
    <Card :key="ind">
        <div slot="title" class="detail-card-title">
            <a :href="`/evals/task/detail/single?taskId=${taskId}&subTaskId=${subTaskId}&dataIndex=${dataIndex}`" target="_blank">编号: {{ currentFristIndex + Number(ind)}}</a>
        </div>
        <template #extra v-if="mutiRoundData.length > 1">
            <div class="dropdown-container">
                <Dropdown>
                    <Button :type="getBtnType(0)" ghost>
                        稳定性
                        <Icon type="ios-arrow-down"></Icon>
                    </Button>
                    <template #list>
                        <DropdownMenu>
                            <DropdownItem v-for="(item, index) in mutiRoundData" :key="index">
                                <Button :type="getBtnType(index)" shape="circle" @click="onMutiRoundDataIndexChange(index)"
                                    v-if="index===0" icon="ios-contract">所有</Button>
                                <Button :type="getBtnType(index)" shape="circle" ghost @click="onMutiRoundDataIndexChange(index)"
                                    v-else>{{ index }}</Button>
                            </DropdownItem>
                        </DropdownMenu>
                    </template>
                </Dropdown>
            </div>
        </template>
        <div style="margin: 20px">
          <Row>
            <Col :span="12">
              <Divider orientation="left">评测结果</Divider>
              <Row>
                <Col :span="10">
                  <List item-layout="vertical" size="small">
                    <ExtraInfoListItem header="score"
                        :headerMap="headerMap" :curItem="item" :frontEndConfig="frontEndConfig"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-eval.png"></ExtraInfoListItem>
                    <!-- <ListItem>
                      <ListItemMeta
                        :title="headerMap['score'] ? headerMap['score'].label : 'score'"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-eval.png"
                      >
                      </ListItemMeta>
                      <Row>
                        <Col :span="1"></Col>
                        <Col :span="8">
                          <pre class="none-border-pre info-pre-scrollable-y" v-html="getDiffShowData(curItem, 'score')"></pre>
                        </Col>
                        <Col :span="2">
                          <mtd-button type="text" icon="mtdicon-edit-o" style="padding-bottom: 10px"
                            @click="update_dialog('score', curItem)" >
                          </mtd-button>
                        </Col>
                      </Row>
                    </ListItem> -->
                  </List>
                </Col>
                <Col :span="1"></Col>
                <Col :span="12">
                  <List item-layout="vertical" size="small">
                    <ExtraInfoListItem header="eval_reason"
                        :headerMap="headerMap" :curItem="item" :frontEndConfig="frontEndConfig"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/ai_logo.png"></ExtraInfoListItem>
                    <!-- <ListItem>
                      <ListItemMeta
                        title="评测原因"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/ai_logo.png"
                      ></ListItemMeta>
                      <pre class="none-border-pre info-pre-scrollable-y" v-html="getDiffShowData(curItem, 'eval_reason')"></pre>
                    </ListItem> -->
                  </List>
                </Col>
              </Row>
              <Divider orientation="left">对话信息</Divider>
              <Row v-if="curItem['answer'] && curItem['ideal-answer']">
                <Col :span="10">
                  <List item-layout="vertical" size="small">
                    <ExtraInfoListItem header="answer"
                        :headerMap="headerMap" :curItem="item" :frontEndConfig="frontEndConfig"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-bot.png"></ExtraInfoListItem>
                    <!-- <ListItem>
                      <ListItemMeta
                        :title="headerMap['answer'] ? headerMap['answer'].label : 'answer'"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-bot.png"
                      ></ListItemMeta>
                      <pre class="none-border-pre info-pre-scrollable-y" v-html="getDiffShowData(curItem, 'answer')"></pre>
                    </ListItem> -->
                  </List>
                </Col>
                <Col :span="1"></Col>
                <Col :span="12">
                  <List item-layout="vertical" size="small">
                    <!-- <ListItem> -->
                      <ExtraInfoListItem header="ideal-answer"
                        :headerMap="headerMap" :curItem="item" :frontEndConfig="frontEndConfig"
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-correct.png"></ExtraInfoListItem>
                      <!-- <ListItemMeta
                        avatar="https://s3plus.sankuai.com/vision-image/resource/icon-correct.png"
                        :title="headerMap['ideal-answer'] ? headerMap['ideal-answer'].label : 'ideal-answer'">
                      </ListItemMeta>
                      <pre class="none-border-pre info-pre-scrollable-y" v-html="getDiffShowData(curItem, 'ideal-answer')"></pre> -->
                    <!-- </ListItem> -->
                  </List>
                </Col>
              </Row>
              <div class="chat-box">
                  <ChatMessage v-for="msg, ind in dispMessage(curItem.messages)" :msg="msg"
                  :key="ind" :tag="getMessageTag(item, msg.role)">
                  </ChatMessage>
              </div>
            </Col>
            <Col :span="1"></Col>
            <Col :span="11">
              <Divider orientation="left">其他信息</Divider>
              <List item-layout="vertical" size="small">
                <ExtraInfoListItem :header="header" v-for="header in displayedHeaders" :key="header"
                :headerMap="headerMap" :curItem="item" :frontEndConfig="frontEndConfig" :avatar="null"></ExtraInfoListItem>
              </List>
            </Col>
          </Row>
        </div>
    </Card>
    <!-- <el-dialog :modal-append-to-body="false" title="修改字段的值" :visible.sync="editVisible" width="30%">
        <el-input type="textarea" autosize v-model="editValue"></el-input>
        <div slot="footer" class="dialog-footer">
            <el-button @click="update_edit">保存</el-button>
        </div>
    </el-dialog> -->
    </div>
</template>

<script>
import ChatMessage from '../LLMEvals/Tools/ChatMessage.vue'
import ExtraInfoListItem from './Tools/ExtraInfoListItem.vue'

export default {
  props: {
    ind: Number,
    item: Object,
    frontEndConfig: Object,
    currentFristIndex: Number,
    headerMap: Object,
    mutiRoundData: Array,
    taskId: String,
    subTaskId: String,
    dataIndex: String
  },
  components: {
    Navigator, ChatMessage, ExtraInfoListItem
  },
  computed: {
    displayedHeaders() {
      const dispHeaders = []
      for (let key in this.frontEndConfig) {
        if (this.frontEndConfig.hasOwnProperty(key)) {
          let value = this.frontEndConfig[key]
          if (value.show) {
            dispHeaders.push(key)
          }
        }
      }
      dispHeaders.sort((a, b) => {
        let aInd = this.getFeConfigHeaderIndex(a)
        let bInd = this.getFeConfigHeaderIndex(b)
        return aInd - bInd
      })
      let ignoreSet = new Set(this.itemIgnoreList)
      return dispHeaders.filter(item => !ignoreSet.has(item))
    }
  },
  data() {
    return {
      editVisible: false,
      editValue: null,
      editItem: null,
      editHeader: null,
      mutiRoundDataIndex: 0,
      itemIgnoreList: ['input', 'score', 'answer', 'ideal-answer'],
      curItem: this.item
    }
  },
  watch: {
    item(newValue) {
      this.curItem = newValue
    }
  },
  methods: {
    getBtnType(index) {
      const item = this.mutiRoundData[index]
      if (item.score === 0) {
        return 'error'
      } else {
        return 'success'
      }
    },
    onMutiRoundDataIndexChange(value) {
      this.mutiRoundDataIndex = value
      this.curItem = this.mutiRoundData[value]
    },
    getFeConfigHeaderIndex(field) {
      if (this.frontEndConfig[field] && this.frontEndConfig[field].index) {
        return this.frontEndConfig[field].index
      }
      return 0
    },
    dispMessage(messages) {
      if (!messages) {
        return []
      }
      if (messages.current) {
        return messages.current
      }
      return messages
    },
    getMessageTag(item, role) {
      const r = role.toUpperCase()
      if (r === 'INPUT') {
        if (item['是否变种'] === '是') {
          return '变种'
        }
        if (item['问题来源'] && item['问题来源'].includes('变种问题')) {
          return '变种'
        }
      }
      return null
    }
  }
}
</script>

<style scoped>
.chat-box {
  flex-direction: column;
  gap: 4px; /* 增加聊天框之间的间距，使其更加美观 */
  padding: 16px; /* 增加内边距，使内容不会紧贴边缘 */
  border: 1px solid #E0E0E0; /* 添加边框，使聊天框更加突出 */
  border-radius: 10px; /* 增加圆角，使聊天框更加柔和 */
  /*box-shadow: 0 2px 4px rgba(0,0,0,0.1); !* 添加阴影，增加立体感 *!*/
  background-color: #F8F8F8; /* 调整聊天框背景色，使其更加柔和 */
  max-height: 400px;
  overflow-y: auto;
}

.detail-card-title{
  text-align: center;
  font-weight: bolder;
  font-size: 20px;
}

.info-pre-scrollable-y {
  white-space: pre-wrap; /* 保留空白符，允许自动换行 */
  max-height: 200px;
  overflow-y: auto;
}

.none-border-pre{
    border: none; /* 取消边框 */
    max-width: 100% !important;
    background-color: transparent;
    padding-left: 0px;
    padding-right: 16px;
    padding-top: 0px;
    padding-bottom: 0px;
    margin: 0;
}

/deep/ .ivu-list-vertical .ivu-list-item-meta-title{
    margin-bottom: 2px;
}
/deep/ .ivu-divider-with-text-left {
  margin: 0 0 10px 0;
}
</style>
