<template>
    <div>
      <Layout>
        <Header style="background: #fff; border-bottom: 1px solid lightgray;">
           <Navigator>
            <template v-slot:other-btn>
                <el-button size="small" icon="el-icon-setting" circle @click="handleSelect('subTaskControl')"></el-button>
            </template>
           </Navigator>
        </Header>
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
              <Menu :open-names="openNames"  ref="side_menu" theme="light"
                    width="auto" @on-select="handleSelect">
                <MenuItem name="overview">
                     <Icon type="ios-home"/>
                        总览
                </MenuItem>
                <template v-for="sub_task in sub_tasks">
                    <SubTaskAggrSubmenu v-if="sub_task.tool_type && ['Scorers'].includes(sub_task.tool_type)"
                        :task_id="task_id" :sub_task="sub_task" @subTaskAggrInfo="getSubTaskAggrInfo"
                        :diff_task_id="diff_task_id" :enableDiffMode="enableDiffMode"
                    ></SubTaskAggrSubmenu>
                </template>
              </Menu>
            </Sider>
          <Content>
            <div v-show="activateName===undefined || activateName===null || activateName === 'overview'">
              <Card v-if="aggrData" dis-hover>
                  <h2>{{ aggrData.report_title }}</h2>
              </Card>
              <Card v-else-if="task" dis-hover>
                  <h2>{{ task.cmd_args.report_title }}</h2>
              </Card>
              <Row justify="center" align="middle">
                <Col :span="12">
                  <Card dis-hover v-show="showRadarChart">
                    <div id="radar-chart" style="height: 400px; "></div>
                  </Card>
                </Col>
                <Col :span="1"></Col>
                <Col :span="11">
                  <VueBotUI
                    :messages="chatBotMessages"
                    :options="botOptions"
                    :bot-typing=botTyping
                    :isOpen=true
                    @msg-send="sendMessage"
                  >
                    <template v-slot:header>
                      <div style="display: flex;">
                        <img src="/static/img/ai-chat-logo.png" style="height: 34px;width: 34px">
                        <div style="padding-top:6px;font-size: 15px;font-weight: bold;color:#492ae8">结果分析</div>
                      </div>
                    </template>
                    <template v-slot:actions>
                      <div style="padding-top: 5px;padding-right: 2px">
                        <Button shape="circle" size="small" icon="md-refresh" @click="clearChatHistory"></Button>
                      </div>
                    </template>
                  </VueBotUI>
                </Col>
              </Row>
            </div>
            <div v-if="activateName===undefined || activateName===null || activateName === 'overview'">
              <el-card v-for="(group, dsName) in groupByDsName()" :key="dsName">
                  <div slot="header" class="clearfix">数据集: {{ dsName }}</div>
                  <RemoteSubTaskReducerDetail :ref="dsName" :diff="false"
                      :subtasks="group" :task="task"
                      :all-sub-tasks="sub_tasks"
                      :aggr-data="getDsAggrData(dsName, aggrData, 'current')">
                  </RemoteSubTaskReducerDetail>
                  <RemoteSubTaskReducerDetail :ref="dsName+'-diff'" :diff="true"
                      v-if="diff_task_id && diff_sub_task_ids && diff_sub_task_ids.length !== 0"
                      :subtasks="group.map(findCorrespondingDiffSubTaskId)" :task="difftask"
                      :all-sub-tasks="diff_sub_tasks"
                      :aggr-data="getDsAggrData(dsName, aggrDataDiff, 'diff')">
                  </RemoteSubTaskReducerDetail>
              </el-card>
              <div>
                overview
              </div>
            </div>
            <div v-else-if="activateName === 'subTaskControl'">
                <RemoteSubTaskControl :ref="'sub-task-control'"></RemoteSubTaskControl>
            </div>
            <div v-else>
                <RemoteSubTaskTable :ref="'remote-sub-table'" :subtask="cur_subtask" :task="task"
                    :scoreShift="aggrScoreShift" :tool="tool" :loading="loading" @switchDiffMode="onDiffSwitch">
                </RemoteSubTaskTable>
            </div>
          </Content>
        </Layout>
    </Layout>
    </div>

</template>


<script>
import api from '../LLMEvals/api'
import Navigator from '../LLMEvals/Navigator.vue'
import RemoteSubTaskTable from './RemoteSubTaskTable.vue'
import RemoteSubTaskReducerDetail from './RemoteSubTaskReducerDetail.vue'
import RemoteSubTaskControl from './RemoteSubTaskControl.vue'
import SubTaskAggrSubmenu from './SubTaskAggrSubmenu.vue'
import * as echarts from 'echarts'
import { VueBotUI } from 'vue-bot-ui'

export default {
  props: {
    subtask: Object
  },
  components: {
    Navigator,
    RemoteSubTaskTable,
    RemoteSubTaskControl,
    RemoteSubTaskReducerDetail,
    SubTaskAggrSubmenu,
    VueBotUI
  },
  data() {
    return {
      task: null,
      difftask: null,
      sub_tasks: [],
      sub_task_ids: [],
      diff_sub_tasks: [],
      diff_sub_task_ids: [],
      cur_subtask: null,
      activateName: 'overview',
      tool: null,
      loading: false,
      aggrLoading: false,
      aggrData: null,
      aggrDataDiff: null,
      aggrResult: {},
      aggrResultStatus: false,
      aggrResultLength: 0,
      aggrScoreShift: 'overview',
      openNames: [],
      enableDiffMode: false,
      radarKeys: [],
      currentSeries: {},
      diffSeries: {},
      radarChart: null,
      radarChartOptionComplete: 0,
      showRadarChart: true,
      userQuery: '',
      chatBotMessages: [{
        agent: 'bot',
        type: 'button',
        text: '请选择你的问题',
        disableInput: true,
        options: [
          {
            text: '分析评测结果',
            value: '',
            action: 'postback'
          },
          {
            text: '提供应用改进建议',
            value: '',
            action: 'postback'
          }
        ]
      }],
      botTyping: false,
      botOptions: {
        colorScheme: '#fff',
        inputPlaceholder: '请输入你的问题',
        botAvatarImg: 'https://s3plus.sankuai.com/vision-image/resource/chatbot-logo.png'
      },
      botSubTaskAggrInfo: []
    }
  },
  computed: {
    task_id() {
      return this.$route.query.task_id
    },
    diff_task_id() {
      return this.$route.query.diff_task_id
    },
    filteredReducedSubTasks() {
      let subTasks = this.sub_tasks.filter(subTask => {
        return subTask.tool_type && ['Reducers'].includes(subTask.tool_type) && !subTask.tool_name.includes('Wiki')
      })
      let spendTimeSubTasks = subTasks.filter(subTask => subTask.tool_name.includes('SpendTime'))
      let exceptionSubTasks = subTasks.filter(subTask => subTask.tool_name.includes('Exception'))
      let uniqueSpendTimeSubTasks = spendTimeSubTasks.reduce(this.uniqueByDsName, [])
      let uniqueExceptionSubTasks = exceptionSubTasks.reduce(this.uniqueByDsName, [])
      subTasks = subTasks.filter(subTask => !subTask.tool_name.includes('SpendTime') && !subTask.tool_name.includes('Exception'))
      subTasks = subTasks.concat(uniqueSpendTimeSubTasks).concat(uniqueExceptionSubTasks)
      return subTasks
    }
  },
  watch: {
    activateName(newVal) {
      let query = { ...this.$route.query, activateName: newVal }
      this.$router.replace({ query })
      if (newVal === 'overview') {
        this.radarChartOptionComplete = 0
      }
    },
    openNames(newVal) {
      this.$nextTick(() => {
        this.$refs.side_menu.updateOpened()
      })
    }
  },
  mounted() {
    this.refresh()
    this.initChart()
  },
  methods: {
    onDiffSwitch(value) {
      this.enableDiffMode = value
    },
    async getSubTaskAggrInfo(v, subTask) {
      let toolName = subTask.tool_name
      for (let k in v) {
        if (k === '不通过数量' || k === '异常数量') {
          if (v[k].value > 0 && !toolName.includes('安全')) {
            const r = await api.listDataItem(this.task_id,
            subTask.sub_task_id, 0, 20,
            {'score': 'unpass'}, 0, 1)
            let botMessages = r.data[0].data
            let evalCases = botMessages.map(item => ({ 'userInput': item.input,
              'botAnswer': item.completion,
              'evalReason': item.eval_reason }))
            this.botSubTaskAggrInfo.push(toolName + '-' + k, evalCases)
          }
        }
      }
    },
    buildQueryPrompt(query) {
      let prompt = `以下是问答机器人的评测数据，汇总数据表示评测项整体评分，不通过用例表示有问题的具体例子，请回答query.
      ===query===
      ${query}
      ===汇总数据===
      ${JSON.stringify(this.currentSeries)}
      ===不通过用例===
      ${JSON.stringify(this.botSubTaskAggrInfo)}
      ===回答方式===
      条理清晰，做简短回答`
      console.log(prompt)
      return prompt
    },
    clearChatHistory() {
      this.chatBotMessages = [{
        agent: 'bot',
        type: 'button',
        text: '请选择你的问题',
        disableInput: true,
        options: [
          {
            text: '分析评测结果',
            value: '',
            action: 'postback'
          },
          {
            text: '提供应用改进建议',
            value: '',
            action: 'postback'
          }
        ]
      }]
    },
    async sendMessage(value) {
      this.userQuery = value.text
      this.chatBotMessages.push({
        agent: 'user',
        type: 'text',
        text: this.userQuery
      })
      this.botTyping = true
      let queryPrompt = this.buildQueryPrompt(this.userQuery)
      // eslint-disable-next-line no-undef
      this.controller = new AbortController()
      this.response = ''
      this.stopGenerating = false
      const response = await fetch(this.env.aiengineering_url + '/gpt_stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_query: queryPrompt, business: 'eval_cap_qa', model: 'gpt-4o-mini' }),
        signal: this.controller.signal
      })
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      while (true) {
        if (this.stopGenerating) break
        const { done, value } = await reader.read()
        if (done) break
        let resText = decoder.decode(value)
        if (this.chatBotMessages.at(-1).agent === 'user') {
          this.botTyping = false
          this.chatBotMessages.push({
            agent: 'bot',
            type: 'text',
            text: resText,
            disableInput: true
          })
        } else {
          this.chatBotMessages.at(-1).text = this.chatBotMessages.at(-1).text + resText
        }
      }
      console.log('chat completion')
      this.chatBotMessages.at(-1).disableInput = false
    },
    stopGenerating() {
      this.controller.abort()
      this.stopGenerating = true
    },
    initChart() {
      this.radarChart = echarts.init(document.getElementById('radar-chart'))
    },
    setRadarChartOption() {
      this.showRadarChart = true
      this.radarChartOptionComplete = this.radarChartOptionComplete + 1
      let indicatorList = this.radarKeys.map(key => { return { name: key, min: 0.5, max: 1.0 } })
      let seriesList = [
        {
          value: this.radarKeys.map(key => this.currentSeries[key]),
          name: '当前版本',
          symbol: 'rect',
          symbolSize: 12,
          label: {
            show: true,
            formatter: function (params) {
              return params.value
            }
          }
        }
      ]
      if (this.diff_task_id) {
        seriesList.push(
          {
            value: this.radarKeys.map(key => this.diffSeries[key]),
            name: '历史版本',
            areaStyle: {
              color: new echarts.graphic.RadialGradient(0.1, 0.5, 1, [
                {
                  color: 'rgba(255, 145, 124, 0.1)',
                  offset: 0
                },
                {
                  color: 'rgba(255, 145, 124, 0.9)',
                  offset: 1
                }
              ])
            }
          }
        )
      }
      this.radarChart.setOption({
        color: ['#56A3F1', '#FF917C'],
        legend: {
          data: ['当前版本', '历史版本']
        },
        tooltip: {
          trigger: 'item'
        },
        radar: {
          indicator: indicatorList,
          center: ['45%', '56%'],
          radius: 150,
          axisName: {
            color: '#000'
          }
        },
        series: [
          {
            name: '评测结果',
            type: 'radar',
            data: seriesList
          }
        ]
      })
    },
    getDsAggrData(ds, aggrdata, type) {
      if (aggrdata && aggrdata.err) {
        return undefined
      }
      if (aggrdata) {
        let result = aggrdata.aggregrated_reduced_data[ds]
        for (let radarKey of this.radarKeys) {
          if (result.hasOwnProperty(radarKey)) {
            for (let k of result[radarKey]) {
              if (k['__key__'] === '通过率') {
                if (type === 'current' && !this.currentSeries.hasOwnProperty(radarKey)) {
                  this.currentSeries[radarKey] = (parseFloat(k['result']) / 100).toFixed(2)
                }
                if (type === 'diff' && !this.diffSeries.hasOwnProperty(radarKey)) {
                  this.diffSeries[radarKey] = (parseFloat(k['result']) / 100).toFixed(2)
                }
              }
            }
          }
        }
        if (this.diff_task_id) {
          if (Object.keys(this.currentSeries).length === this.radarKeys.length && this.radarChartOptionComplete <= 10 &&
            Object.keys(this.diffSeries).length > 1) {
            this.setRadarChartOption()
          }
        } else if (Object.keys(this.currentSeries).length === this.radarKeys.length &&
          this.radarChartOptionComplete <= 1) {
          this.setRadarChartOption()
        }
        return aggrdata.aggregrated_reduced_data[ds]
      }
      return null
    },
    groupByDsName() {
      return this.filteredReducedSubTasks.reduce((acc, item) => {
        if (!acc[item.ds_name]) {
          acc[item.ds_name] = []
        }
        acc[item.ds_name].push(item)
        return acc
      }, {})
    },
    findCorrespondingDiffSubTaskId(subTask) {
      const subTaskId = subTask.sub_task_id
      const index = this.sub_task_ids.indexOf(subTaskId)
      if (index !== -1 && index < this.diff_sub_task_ids.length) {
        return this.diff_sub_tasks[index]
      }
      return null
    },
    uniqueByDsName(acc, current) {
      const x = acc.find(item => item.ds_name === current.ds_name)
      if (!x) {
        return acc.concat([current])
      } else {
        return acc
      }
    },
    refresh() {
      this.get_task()
      this.aggrLoading = true
      this.get_aggr_data().then((response) => { this.aggrLoading = false })
      Object.keys(this.$refs).forEach(refName => {
        if (Array.isArray(this.$refs[refName]) && this.$refs[refName].length > 0) {
          if (this.$refs[refName][0].refresh) {
            this.$refs[refName][0].refresh()
          }
        } else if (typeof this.$refs[refName] === 'object') {
          if (this.$refs[refName].refresh) {
            this.$refs[refName].refresh()
          }
        }
      })
    },
    async get_aggr_data() {
      const r = await api.getTaskOverview(this.task_id)
      this.aggrData = r.data
      if (this.diff_task_id) {
        const r2 = await api.getTaskOverview(this.diff_task_id)
        this.aggrDataDiff = r2.data
      }
    },
    get_task() {
      api.getRemoteTask(this.task_id).then(r => {
        this.task = r.data.task
        this.sub_task_ids = r.data.sub_task_ids
        this.sub_tasks = r.data.sub_tasks
        this.handleSelect(this.$route.query.activateName || 'overview')
        if (this.diff_task_id) {
          api.getRemoteTask(this.diff_task_id).then(r => {
            this.difftask = r.data.task
            this.diff_sub_task_ids = r.data.sub_task_ids
            this.diff_sub_tasks = r.data.sub_tasks
          }).catch((error) => {
            api.notifyError(this.$notify, '获取 diff_task 信息失败', error)
          })
        }
        this.aggrResultLength = 0
        for (let i in this.sub_tasks) {
          let subTask = this.sub_tasks[i]
          if (subTask.tool_type && ['Scorers'].includes(subTask.tool_type)) {
            this.radarKeys.push(subTask.tool_name)
            this.aggrResultLength = this.aggrResultLength + 1
            if (this.aggrResultLength <= 1) {
              this.openNames.push(subTask.sub_task_id)
            }
          }
        }
        if (this.radarKeys.length < 3) {
          this.showRadarChart = false
        }
      }).catch((error) => {
        api.notifyError(this.$notify, '获取 task 信息失败', error)
      })
    },
    handleSelect(val) {
      let activateName = val.split('*')[0]
      this.aggrScoreShift = val.split('*')[1]
      this.activateName = activateName
      if (activateName === 'overview') {
        return
      }
      this.cur_subtask = this.sub_tasks.filter((value) => value.sub_task_id === activateName)[0]
    }
  }
}
</script>


<style scoped>

.clearfix{
    font-size: larger;
    font-weight: bolder;
}

h2 {
  text-align: center;
  font-weight: bold;
  font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
  color: #333; /* 标题颜色 */
  margin-top: 20px; /* 顶部间距 */
  margin-bottom: 10px; /* 底部间距 */
}
h4 {
  text-align: center;
  font-weight: normal;
  font-family: MeituanFont, 微软雅黑, 方正兰亭中黑, sans-serif;
  color: #666; /* 描述文字颜色 */
  margin-bottom: 20px; /* 底部间距 */
  font-size: 20px;
  line-height: 28px;
}
/* keep it */
.ivu-menu {
  color: #111925;
}
/* keep it */
.ivu-menu-item {
  color: #111925;
}
/deep/ .qkb-bot-ui {
  position: static;
}
/deep/ .qkb-board {
  position: static;
  width: 500px;
  height: 395px;
  box-shadow: none;
  bottom: 0;
}
/deep/ .qkb-board-header__title{
  font-size: 15px;
}
/deep/ .qkb-msg-bubble-component__text {
  font-size: 14px;
}
/deep/ .qkb-board-action__input {
  font-size: 15px;
}
/deep/ .qkb-bubble-btn {
  display: none;
}
/deep/ .qkb-mb-button-options__btn {
  font-size: 12px;
}
/deep/ .qkb-msg-bubble-component__text {
  white-space: pre-wrap;
}
</style>
