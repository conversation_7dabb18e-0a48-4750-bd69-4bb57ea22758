<template>
    <div style="display: inline-flex;">
        <div class="flow-chart-col">
            <slot></slot>
        </div>
        <div class="flow-edge-button" v-if="edgeButton">
            <Icon type="md-add" />
        </div>
        <div class="middle" v-else-if="!end">
        </div>
    </div>
</template>

<script>
export default{
  props: {
    edgeButton: {
      type: Function,
      default: null,
      validator: function(value) {
        return typeof value === 'function' || value == null
      }
    },
    end: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.flow-edge-button:before, .middle:before{
    content:"";
    height: 1px;
    width: 21px;
    background: #dcdee2;
    position: absolute;
    left: -22px;
    top: 12px;
}
.middle:before{
    left: -21px !important;
}
.flow-edge-button:after,.middle:after{
    content:"";
    height: 1px;
    width: 20px;
    background: #dcdee2;
    position: absolute;
    right: -21px;
    top: 12px;
}
.middle:after{
    right: -20px !important;
}
.middle{
    width: 0px;
    margin-left: 20px;
    margin-right: 20px;
    height: 25px;
    position: relative;
    margin-top: 7px;
    text-align: center;
    line-height: 16px;
    font-size: 16px;

}
.flow-edge-button{
    font-weight: bold;
    width: 25px;
    height: 25px;
    margin-left: 20px;
    margin-right: 20px;
    color: rgb(22, 111, 247);
    cursor: pointer;
    border-radius: 15px;
    border: 1px solid rgb(22, 111, 247);
    margin-top: 6px;
    position: relative;
    text-align: center;
    padding-top: 3px;
    padding-bottom: 2px;
    line-height: 16px;
    font-size: 16px;
}
</style>