<template>
    <div :class="['chart-flow-lane', laneClass]"
        :style="{ position: 'relative' }" @click="handleLaneClick">
        <div :class="['chart-flow-basic-box', ...boxClasses]" :style="{ position: 'relative', width: laneWidth }">
            <!--- label -->
            <div :class="['chart-flow-basic-box-label', ...boxLabelClasses]" v-if="!addition">
                <span>{{ label }}</span>
            </div>
            <div :class="['chart-flow-basic-box-label', ...boxLabelClasses]" v-else style="width: 0px; min-width: 0px;"></div>
            <!-- name -->
            <div class="chart-flow-basic-box-name"
                :style="{ width: nameWidth }" v-if="!addition">
                <slot></slot>
            </div>
            <div v-else class="chart-flow-basic-box-name-add" 
                :style="{'text-align': 'center', color: rgb(22, 111, 247), width: nameWidth}">
                <Icon type="md-add" style="margin-right: 3px;"/>
                添加节点
            </div>
            <!--- extra -->
            <div class="chart-flow-basic-box-extra" v-if="!addition">
                <slot name="extra"></slot>
                <i class="mtdicon mtdicon-setting-fill" v-if="focus"></i>
            </div>
        </div>
    </div>
</template>

<script>
export default{
  props: {
    label: {
      type: String,
      default: '1'
    },
    focus: {
      type: Boolean,
      default: false
    },
    laneType: {
      type: String,
      default: '',
      validator: function(value) {
        const allowedValues = ['start', 'middle', 'end', '']
        return allowedValues.includes(value)
      }
    },
    left: {
      type: Boolean,
      default: false
    },
    right: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 120
    },
    addition: {
      type: Boolean,
      default: false
    },
    check: {
      type: Function,
      default: null,
      validator: function(value) {
        return typeof value === 'function' || value == null
      }
    }
  },
  computed: {
    laneClass() {
      return this.laneType ? `chart-flow-lane-${this.laneType}` : ''
    },
    laneWidth() {
      return `${this.width + 75}px`
    },
    nameWidth() {
      return `${this.width}px`
    },
    boxClasses() {
      const isTop = (this.laneType && this.laneType === 'start') ? '' : '-not-top'
      let boxClasses = []
      if (this.left) {
        boxClasses.push(`chart-flow-basic-box-not-start${isTop}`)
      }
      if (this.right) {
        boxClasses.push(`chart-flow-basic-box-not-end${isTop}`)
      }
      if (this.check === null) {
        boxClasses.push('not-check-status-border')
      }
      return boxClasses
    },
    boxLabelClasses() {
      let boxClasses = []
      if (this.left) {
        boxClasses.push(`chart-flow-basic-box-label-not-start`)
      }
      if (this.check === null) {
        boxClasses.push('not-check-status')
      }
      return boxClasses
    }
  },
  methods: {
    handleLaneClick() {
      if (this.check) {
        this.check()
      }
    }
  }
}
</script>

<style>
.chart-flow-basic-box {
    background-color: #fff;
    color: #000;
    border: 1px solid #166ff7;
    margin-bottom: 10px;
    border-radius: 3px;
    height: 38px;
    display: inline-flex;
    cursor: pointer !important;
}

.chart-flow-basic-box-not-end-not-top,.chart-flow-basic-box-not-end {
    margin-right: 45px
}

.chart-flow-basic-box-not-start-not-top,.chart-flow-basic-box-not-start {
    margin-left: 45px
}

.chart-flow-basic-box-label {
    background-color: #166ff7;
    width: 40px;
    line-height: 14px;
    padding-top: 11px;
    font-size: 14px;
    text-align: center;
    color: #fff;
    font-weight: bolder
}

.chart-flow-basic-box-name {
    padding: 8px;
    color: #1a1a1a;
    font-weight: bolder;
    overflow: hidden;
    text-overflow: ellipsis
}

.chart-flow-basic-box-extra {
    padding: 8px;
    width: 35px;
    color: #1a1a1a;
    font-weight: bolder;
    overflow: hidden;
    text-overflow: ellipsis
}

.small-title {
    color: #1a1a1a;
    font-weight: 600;
    font-size: 13px
}

.chart-flow-basic-box-name-add {
    padding: 8px;
    width: 255px!important;
    color: #1a1a1a;
    font-weight: bolder;
    overflow: hidden;
    text-overflow: ellipsis
}

.chart-flow-basic-box-add{
    display: inline-flex;
    border: 1px dashed rgb(22, 111, 247) !important;
    cursor: pointer !important;
}

.chart-flow-basic-box-not-end:after {
    content: "";
    height: 1px;
    width: 45px;
    background: #dcdee2;
    position: absolute;
    right: -46px;
    top: 18px;
}

.chart-flow-basic-box-not-end-not-top:after {
    content: "";
    height: 1px;
    width: 45px;
    background: #dcdee2;
    position: absolute;
    right: -46px;
    top: 18px
}

.chart-flow-basic-box-not-start:before {
    content:"";
    height: 1px;
    width: 45px;
    background: #dcdee2;
    position: absolute;
    left: -47px;
    top: 18px
}

.chart-flow-basic-box-not-start-not-top:before {
    content: "";
    height: 1px;
    width: 40px;
    background: #dcdee2;
    position: absolute;
    left: -47px;
    top: 18px
}

.chart-flow-basic-box-label-not-start:before {
    content: "";
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    position: absolute;
    left: -8px;
    top: 12px;
    border-left: 6px solid #dcdee2
}

.chart-flow-lane {
    margin-bottom: 5px;
}

.chart-flow-lane-start:before {
    content:"";
    height: 34px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    left: -1px;
    top: 19px
}

.chart-flow-lane-start:after {
    content: "";
    height: 34px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    right: 0;
    top: 19px
}

.chart-flow-lane-middle:before {
    content: "";
    height: 53px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    left: -1px;
    top: 0
}

.chart-flow-lane-middle:after {
    content: "";
    height: 53px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    right: 0px;
    top: 0
}

.chart-flow-lane-end:before {
    content: "";
    height: 19px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    left: -1px;
    top: 0
}

.chart-flow-lane-end:after {
    content: "";
    height: 20px;
    width: 1px;
    background: #dcdee2;
    position: absolute;
    right: 0;
    top: 0
}

.not-check-status-border {
    border: 1px solid #c5c8ce!important;
    cursor: default !important;
}

.not-check-status {
    background-color: #c5c8ce!important;
    color: #1a1a1a;
    cursor: default !important;
}
</style>