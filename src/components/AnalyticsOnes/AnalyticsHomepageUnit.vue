<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div>
          <Spin v-if="isLoading">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <Row v-if="!isLoading" type="flex">
            <Col span="24">
            <Timeline style="padding-top: 15px;padding-left: 2px">
              <TimelineItem>
                <span>查询项目列表: （项目列表有问题,请到方向页进行修改）</span>
                <Row style="margin-top: 5px;cursor: default" type="flex">
                  <common-card-list :initaldata="onesProjectList" :span="span" :row="row" :keywordlist="keys" style="cursor: default">
                    <div slot="customContent" slot-scope="props">
                      <tag color="#ffffff"><span style="font-size: 12px;font-weight: bolder;color: #4a9eff;cursor: default">{{props.customdata.projectName}}({{props.customdata.projectId}})</span></tag>
                    </div>
                  </common-card-list>
                  <div v-if="onesProjectList.length === 0">
                    <div style="margin-top: 5px;cursor: default">
                      <span style="font-weight: bolder;font-size: 12px;margin-left: 20px;">当前查询方向未添加项目信息，请到方向接入页完成项目接入</span>
                    </div>
                  </div>
                </Row>
                <!--<Row type="flex" style="margin-top: 10px;margin-left: 25px;cursor:default;">-->
                  <!--<Col span="3" v-for="item in onesProjectList" :key="item.projectId">-->
                  <!--<Tooltip placement="top">-->
                    <!--<direction type="ones" :name="item.projectName" :id="item.projectId"></direction>-->
                    <!--<div slot="content">-->
                      <!--<p>{{item.projectName}}({{item.projectId}})</p>-->
                    <!--</div>-->
                  <!--</Tooltip>-->
                  <!--</Col>-->
                <!--</Row>-->
              </TimelineItem>
              <TimelineItem>
                <span>整体运营数据: </span>
                <Row type="flex" :style="{paddingTop:'15px',minHeight:'200px',marginBottom:'5px'}">
                  <Col span="8">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',minHeight:'200px'}" dis-hover>
                    <div :style="{height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        需求（已上线占比）
                      </div>
                      <div style="font-size: 10px">
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:18%; font-weight: bolder;padding-top: 3px;">
                            全部需求
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) < 1" class="leftChart" :style="{width: homepagedata.reqReleaseCount.totalReleaseRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.reqReleaseCount.totalReleaseRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.reqReleaseCount.totalReleaseRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.reqReleaseCount.totalReleaseCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.totalReleaseRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.reqReleaseCount.totalReleaseRate * 100}}%
                          </div>
                          <div style="width:8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.reqReleaseCount.totalReqCount}}</div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:18%; font-weight: bolder;padding-top: 3px;">
                            产品需求
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.productReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.productReleaseRate) < 1" class="leftChart" :style="{width: homepagedata.reqReleaseCount.productReleaseRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.reqReleaseCount.productReleaseRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.productReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.productReleaseRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.reqReleaseCount.productReleaseRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.reqReleaseCount.productReleaseCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.productReleaseRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.productReleaseRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.reqReleaseCount.productReleaseRate}}
                          </div>
                          <div style="width:8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.reqReleaseCount.productReqCount}}</div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:18%; font-weight: bolder;padding-top: 3px">
                            技术需求
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.techReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.techReleaseRate) < 1" class="leftChart" :style="{width: parseFloat(homepagedata.reqReleaseCount.techReleaseRate) * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.reqReleaseCount.techReleaseRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.techReleaseRate) > 0 && parseFloat(homepagedata.reqReleaseCount.techReleaseRate) < 1" class="rightChart" :style="{width: (100 - parseFloat(homepagedata.reqReleaseCount.techReleaseRate) * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.reqReleaseCount.techReleaseCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.techReleaseRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.reqReleaseCount.techReleaseRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">100%
                          </div>
                          <div style="width:8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.reqReleaseCount.techReqCount}}</div>
                        </div>
                        <!--<div-->
                          <!--style="width:100%; font-weight: bolder;padding-top: 3px;margin-top: 15px;text-align: right;font-size: 10px;">-->
                          <!--<a target="_blank">详情</a>-->
                        <!--</div>-->
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="10">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',minHeight:'200px'}" dis-hover>
                    <div :style="{height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        任务（已完成占比）
                      </div>
                      <div style="font-size: 10px">
                        <div style="display: flex;margin-top: 10px">
                          <div style="width:22%; font-weight: bolder;padding-top: 2px;">
                            整体任务进展
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.totalcompleteRate) > 0 && parseFloat(homepagedata.devCount.totalcompleteRate) < 1" class="leftChart" :style="{width: homepagedata.devCount.totalcompleteRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.devCount.totalcompleteRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.totalcompleteRate) > 0 && parseFloat(homepagedata.devCount.totalcompleteRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.devCount.totalcompleteRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.devCount.totalcompleteCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.totalcompleteRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.totalcompleteRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.devCount.totalcompleteRate * 100}}%
                          </div>
                          <div style="width: 8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.devCount.totalCount}}</div>
                        </div>
                        <div style="display: flex;margin-top: 10px">
                          <div style="width:22%; font-weight: bolder;padding-top: 2px;">
                            前端开发任务
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.fecompleteRate) > 0 && parseFloat(homepagedata.devCount.fecompleteRate) < 1" class="leftChart" :style="{width: homepagedata.devCount.fecompleteRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.devCount.fecompleteRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.fecompleteRate) > 0 && parseFloat(homepagedata.devCount.fecompleteRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.devCount.fecompleteRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.devCount.fecompleteCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.fecompleteRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.fecompleteRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.devCount.fecompleteRate * 100}}%
                          </div>
                          <div style="width: 8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.devCount.feCount}}</div>
                        </div>
                        <div style="display: flex;margin-top: 10px">
                          <div style="width:22%; font-weight: bolder;padding-top: 2px">
                            后端开发任务
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.becompleteRate) > 0 && parseFloat(homepagedata.devCount.becompleteRate) < 1" class="leftChart" :style="{width: homepagedata.devCount.becompleteRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.devCount.becompleteRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.becompleteRate) > 0 && parseFloat(homepagedata.devCount.becompleteRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.devCount.becompleteRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.devCount.becompleteCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.becompleteRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.becompleteRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.devCount.becompleteRate * 100}}%
                          </div>
                          <div style="width: 8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.devCount.beCount}}</div>
                        </div>
                        <div style="display: flex;margin-top: 10px">
                          <div style="width:22%; font-weight: bolder;padding-top: 2px">
                            客户端开发任务
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.clientcompleteRate) > 0 && parseFloat(homepagedata.devCount.clientcompleteRate) < 1" class="leftChart" :style="{width: homepagedata.devCount.clientcompleteRate * 100 * 0.74 + '%'}">{{(parseFloat(homepagedata.devCount.clientcompleteRate)*100).toFixed(0)}}%
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.clientcompleteRate) > 0 && parseFloat(homepagedata.devCount.clientcompleteRate) < 1" class="rightChart" :style="{width: (100 - homepagedata.devCount.clientcompleteRate * 100) * 0.74 + '%'}"><div style="font-weight: bolder;font-size: 10px;text-align: left;color:black;margin-left: 3px;margin-top: -2px">{{homepagedata.devCount.clientcompleteCount}}</div>
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.clientcompleteRate) === 0" class="rightChart" :style="{width: '74%', borderTopLeftRadius: '5px',borderBottomLeftRadius: '5px'}">
                          </div>
                          <div v-if="parseFloat(homepagedata.devCount.clientcompleteRate) === 1" class="leftChart" :style="{width: '74%', borderTopRightRadius: '5px',borderBottomRightRadius: '5px'}">{{homepagedata.devCount.clientcompleteRate * 100}}%
                          </div>
                          <div style="width: 8%;color: black;text-align: left;font-size: 10px;font-weight: bolder;margin-left: 3px">{{homepagedata.devCount.clientCount}}</div>
                        </div>
                        <!--<div-->
                          <!--style="width:100%; font-weight: bolder;padding-top: 3px;margin-top: 15px;text-align: right;font-size: 10px;">-->
                          <!--<a target="_blank">详情</a>-->
                        <!--</div>-->
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="6">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',minHeight:'200px'}" dis-hover>
                    <div :style="{height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        仓库/分支/代码量
                      </div>
                      <div style="font-size: 10px">
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:30%; font-weight: bolder;padding-top: 3px;">
                            涉及仓库
                          </div>
                          <div style="height: 20px">
                            <Tag style="height: 20px" color="primary">{{deliveryqualitydata.repoCount}} 个</Tag>
                          </div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:30%; font-weight: bolder;padding-top: 3px">
                            关联分支
                          </div>
                          <div style="height: 20px">
                            <Tag style="height: 20px" color="primary">{{deliveryqualitydata.branchCount}} 个</Tag>
                          </div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="width:30%; font-weight: bolder;padding-top: 3px;">
                            代码变更行数
                          </div>
                          <div style="height: 20px">
                            <Tag style="height: 20px" color="primary">{{deliveryqualitydata.codeLines}} 行</Tag>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                <!--<Row type="flex" :style="{marginBottom:'5px',marginLeft: '25px',marginTop: '-10px',marginRight: '20px'}">-->
                  <!--<Table id="deliverytable" border :columns="columnsdevops" :data="datadevops" style="width: 100%"></Table>-->
                <!--</Row>-->
              </TimelineItem>
              <TimelineItem>
                <span>各状态需求运营数据: </span>
                <Row type="flex" style="margin-top: 10px">
                  <Col span="4">
                  <Card :style="{backgroundColor:'#3576AE',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        需求总数
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.totalReqCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#3576AE',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        产品需求
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.productReqCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#1D68A7',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        技术需求
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.techReqCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#0165A3',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        有QA介入需求数
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.reqTestByQaCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#015C9C',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        提测需求数
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.reqTestByQaCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="4">
                  <Card :style="{backgroundColor:'#01599C',marginLeft:'0px',marginRight:'20px'}" dis-hover>
                    <div :style="{color:'#ffffff',height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        上线需求数
                      </div>
                      <div style="display: flex;padding-top: 15px">
                        <div :style="{textAlign:'right',fontWeight:'border',fontSize:'30px',width:'100%'}">
                          {{homepagedata.reqCount.releaseCount}}
                          <span style="font-size: 16px">个</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>资源消耗情况: </span>
                <Row type="flex">
                  <Col span="8">
                  <div id="devandtestcolumn"
                       style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                  </Col>
                  <Col span="8">
                  <div id="devandjointdebugpie"
                       style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                  </Col>
                  <Col span="8">
                  <Row type="flex" style="margin-top: 20px">
                    <Col span="12">
                    <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                      <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                        <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">测试人效
                        </div>
                        <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{homepagedata.ticeStats.testEfficiency}}:1</div>
                      </div>
                    </Card>
                    </Col>
                    <Col span="12">
                    <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                      <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                        <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">平均测试工时（PD/个）
                        </div>
                        <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{homepagedata.ticeStats.averageTestPd}}</div>
                      </div>
                    </Card>
                    </Col>
                  </Row>
                  <Row type="flex" style="margin-top: 30px">
                    <Col span="12">
                    <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                      <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                        <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">开发联调比
                        </div>
                        <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{homepagedata.ticeStats.devDebugRate}}:1</div>
                      </div>
                    </Card>
                    </Col>
                    <Col span="12">
                    <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                      <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                        <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">平均开发联调工时(PD/个)
                        </div>
                        <div :style="{fontWeight:'bolder',fontSize:'25px', color: '#17233d'}">{{homepagedata.ticeStats.averageDevPd}}</div>
                      </div>
                    </Card>
                    </Col>
                  </Row>
                  </Col>
                </Row>
              </TimelineItem>
              <!--<TimelineItem>-->
                <!--<span>需求交付情况: </span>-->
                <!--<Row type="flex" style="margin-top: 20px;margin-left: 40%;margin-bottom: 5px">-->
                  <!--<div-->
                    <!--style="background-color: #8192D6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                  <!--</div>-->
                  <!--<span style="margin-top:-3px;margin-right: 8px">: 评审时长;</span>-->
                  <!--<div-->
                    <!--style="background-color: #20B2AA;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                  <!--</div>-->
                  <!--<span style="margin-top:-3px;margin-right: 8px">: 开发时长;</span>-->
                  <!--<div-->
                    <!--style="background-color: #c1405a;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                  <!--</div>-->
                  <!--<span style="margin-top:-3px;margin-right: 8px">: 测试时长;</span>-->
                  <!--<div-->
                    <!--style="background-color: #c581d6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">-->
                  <!--</div>-->
                  <!--<span style="margin-top:-3px;margin-right: 8px">: 上线前等待时长;</span>-->
                <!--</Row>-->
                <!--<Row style="margin-top: 10px" :style="{'height': deliveryheight}">-->
                  <!--<Col span="24">-->
                  <!--<div id="deliveryspeedchart" style="padding-top:10px; margin-right:5px;max-height: 3000px"-->
                       <!--:style='deliveryheight'></div>-->
                  <!--</Col>-->
                <!--</Row>-->
                <!--<Row style="margin-top: 10px">-->
                  <!--<Col span="24">-->
                  <!--<div id="deliverybubblechart" style="padding-top:10px; margin-right:5px;max-height: 3000px"></div>-->
                  <!--</Col>-->
                <!--</Row>-->
              <!--</TimelineItem>-->
              <TimelineItem>
                <span>需求交付&收益统计数据: </span>
                <Row type="flex" :style="{paddingTop:'15px',minHeight:'255px',marginBottom:'5px'}">
                  <Col span="24">
                  <Card :style="{borderWidth:0,marginLeft:'10px',marginRight:'10px',minHeight:'255px'}" dis-hover>
                    <div :style="{height:'96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        需求交付情况
                      </div>
                      <div style="font-size: 10px">
                        <div style="display: flex;margin-top: 15px">
                          <div style="min-width:21%; font-weight: bolder;padding-top: 3px;">
                            上线需求数（有效果评估需求数）
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.effectReview !== 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="leftestChart" :style="{width: homepagedata.reqDeliverStats.effectReviewReqCount / homepagedata.reqDeliverStats.totalReqCount * 100 * 0.74 + '%'}">{{homepagedata.reqDeliverStats.effectReviewReqCount}}</div>
                          <div v-if="homepagedata.reqDeliverStats.effectReview !== 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="rightChart" :style="{width: (homepagedata.reqDeliverStats.releaedReqCount / homepagedata.reqDeliverStats.totalReqCount *100 - homepagedata.reqDeliverStats.effectReviewReqCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.releaedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.effectReview === 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="onlyrightChart" :style="{width: (homepagedata.reqDeliverStats.releaedReqCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.releaedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaedReqCount !== 0" style="width:5%; font-weight: bolder;padding-left: 3px;color:#2d8cf0">
                            {{(homepagedata.reqDeliverStats.effectReviewReqCount / homepagedata.reqDeliverStats.releaedReqCount *100).toFixed(1)}}%
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaedReqCount === 0"><div style="font-size: 10px;font-weight: bolder;margin-left: 2px;margin-top: 2px">无</div></div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="min-width:21%; font-weight: bolder;padding-top: 3px">
                            已上线需求数（延期上线需求数）
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaseDelayCount !== 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="leftestChart" :style="{width: homepagedata.reqDeliverStats.releaseDelayCount / homepagedata.reqDeliverStats.totalReqCount * 100 * 0.74 + '%'}">{{homepagedata.reqDeliverStats.releaseDelayCount}}</div>
                          <div v-if="homepagedata.reqDeliverStats.releaseDelayCount !== 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="middleleftChart" :style="{width: (homepagedata.reqDeliverStats.releaedReqCount / homepagedata.reqDeliverStats.totalReqCount *100 - homepagedata.reqDeliverStats.releaseDelayCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.releaedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaseDelayCount === 0 && homepagedata.reqDeliverStats.releaedReqCount !== 0" class="onlymiddleleftChart" :style="{width: (homepagedata.reqDeliverStats.releaedReqCount / homepagedata.reqDeliverStats.totalReqCount *100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.releaedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaedReqCount !==0" style="width:5%; font-weight: bolder;padding-left: 3px;color:#2d8cf0">
                            {{(homepagedata.reqDeliverStats.releaseDelayCount / homepagedata.reqDeliverStats.releaedReqCount *100).toFixed(1)}}%
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.releaedReqCount === 0"><div style="font-size: 10px;font-weight: bolder;margin-left: 2px;margin-top: 2px">无</div></div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="min-width:21%; font-weight: bolder;padding-top: 3px;">
                            已测试完成需求数（不符合预期测试周期需求数）
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.testedReqCount !== 0 && homepagedata.reqDeliverStats.testPDdelayCount !== 0" class="leftestChart" :style="{width: homepagedata.reqDeliverStats.testPDdelayCount / homepagedata.reqDeliverStats.totalReqCount * 100 * 0.74 + '%'}">{{homepagedata.reqDeliverStats.testPDdelayCount}}</div>
                          <div v-if="homepagedata.reqDeliverStats.testedReqCount !== 0 && homepagedata.reqDeliverStats.testPDdelayCount !== 0" class="middleleftChart" :style="{width: (homepagedata.reqDeliverStats.testedReqCount / homepagedata.reqDeliverStats.totalReqCount *100 - homepagedata.reqDeliverStats.testPDdelayCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.testedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.testPDdelayCount === 0 && homepagedata.reqDeliverStats.testedReqCount !== 0" class="onlymiddleleftChart" :style="{width: (homepagedata.reqDeliverStats.testedReqCount / homepagedata.reqDeliverStats.totalReqCount *100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.testedReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.testedReqCount !== 0" style="width:5%; font-weight: bolder;padding-left: 3px;color:#2d8cf0">
                            {{(homepagedata.reqDeliverStats.testPDdelayCount / homepagedata.reqDeliverStats.testedReqCount * 100).toFixed(1)}}%
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.testedReqCount === 0"><div style="font-size: 10px;font-weight: bolder;margin-left: 2px;margin-top: 2px">无</div></div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="min-width:21%; font-weight: bolder;padding-top: 3px;">
                            提测需求数(延期提测需求数)
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.reqTestByQaCount !== 0 && homepagedata.reqDeliverStats.ticeDelayCount !== 0" class="leftestChart" :style="{width: homepagedata.reqDeliverStats.ticeDelayCount / homepagedata.reqDeliverStats.totalReqCount * 100 * 0.74 + '%'}">{{homepagedata.reqDeliverStats.ticeDelayCount}}</div>
                          <div v-if="homepagedata.reqDeliverStats.reqTestByQaCount !== 0 && homepagedata.reqDeliverStats.ticeDelayCount !== 0" class="middleleftChart" :style="{width: (homepagedata.reqDeliverStats.reqTestByQaCount / homepagedata.reqDeliverStats.totalReqCount * 100 - homepagedata.reqDeliverStats.ticeDelayCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.reqTestByQaCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.reqTestByQaCount !== 0 && homepagedata.reqDeliverStats.ticeDelayCount === 0" class="onlymiddleleftChart" :style="{width: (homepagedata.reqDeliverStats.reqTestByQaCount / homepagedata.reqDeliverStats.totalReqCount * 100) * 0.74 + '%'}">
                            {{homepagedata.reqDeliverStats.reqTestByQaCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.reqTestByQaCount !== 0" style="width:5%; font-weight: bolder;padding-left: 3px;color:#2d8cf0">
                            {{(homepagedata.reqDeliverStats.ticeDelayCount / homepagedata.reqDeliverStats.reqTestByQaCount *100).toFixed(1)}}%
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.reqTestByQaCount === 0"><div style="font-size: 10px;font-weight: bolder;margin-left: 2px;margin-top: 2px">无</div></div>
                        </div>
                        <div style="display: flex;margin-top: 15px">
                          <div style="min-width:21%; font-weight: bolder;padding-top: 3px;">
                            需求总数
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.totalReqCount !== 0" class="allChart" :style="{width: 100 * 0.79 + '%'}">{{homepagedata.reqDeliverStats.totalReqCount}}
                          </div>
                          <div v-if="homepagedata.reqDeliverStats.totalReqCount === 0"><div style="font-size: 10px;font-weight: bolder;margin-left: 2px;margin-top: 2px">无</div></div>
                        </div>
                        <!--<div-->
                          <!--style="width:100%; font-weight: bolder;padding-top: 3px;margin-top: 15px;text-align: right;font-size: 10px;">-->
                          <!--<a target="_blank">详情</a>-->
                        <!--</div>-->
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>需求交付质量: </span>
                <Row type="flex" style="margin-top: 10px;min-height: 480px">
                  <Col span="12">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{height: '96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        QA测试问题
                      </div>
                      <div>
                        <Row type="flex">
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                触发提测Pipeline次数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{deliveryqualitydata.tice_all_count}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                提测pipeline有效打回次数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{deliveryqualitydata.tice_valid_back}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                产生bug总数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{homepagedata.bugStats.totalBugCount}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                有效bug数
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  {{homepagedata.bugStats.validBugCount}}
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                        </Row>
                        <Row type="flex" style="margin-top: 5px">
                          <Col span="12">
                          <div id="bugattributepie"
                               style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                          <Col span="12">
                          <div id="effectivebugperdaycolumn"
                               style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="12">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'10px'}" dis-hover>
                    <div :style="{height: '96px'}">
                      <div
                        :style="{borderBottom:'1px solid #e9eaec',paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        线上问题
                      </div>
                      <div>
                        <Row type="flex">
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                <Poptip trigger="hover" :transfer="true">
                                  有效线上bug
                                </Poptip>
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                <Poptip trigger="hover" :transfer="true">
                                  线上故障数
                                </Poptip>
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                <Poptip trigger="hover" :transfer="true">
                                  平均潜藏时长
                                </Poptip>
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  <span style="color: #808695; font-size: 16px">h</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                          <Col span="6">
                          <Card :bordered="false" :style="{marginLeft:'-15px',marginRight:'0px'}" dis-hover>
                            <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                              <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                                <Poptip trigger="hover" :transfer="true">
                                  平均解决时长
                                </Poptip>
                              </div>
                              <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                                <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                                  <span style="color: #808695; font-size: 16px">h</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                          </Col>
                        </Row>
                        <Row type="flex" style="margin-top: 5px">
                          <Col span="12">
                          <div id="solvetimeratiopie"
                               style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                          <Col span="12">
                          <div id="hiddentimeratiopie"
                               style="padding-top:10px; margin-right:5px; max-height: 300px;min-height: 300px;"></div>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
                <Row type="flex" class="bottomBorder">
                  <Col span="4">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        发布次数
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{deliveryqualitydata.deploy}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        回滚次数
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{deliveryqualitydata.rollback}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        线上发布回滚率
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div v-if="deliveryqualitydata.deploy !== 0" :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{(deliveryqualitydata.rollback / deliveryqualitydata.deploy * 100).toFixed(2)}}
                          <span style="color: #808695; font-size: 16px">%</span>
                        </div>
                        <div v-if="deliveryqualitydata.deploy === 0" :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{0}}
                          <span style="color: #808695; font-size: 16px">%</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        hotfix合入master数量
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{deliveryqualitydata.hotfix_merge}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                  <Col span="5">
                  <Card :bordered="false" :style="{marginLeft:'0px',marginRight:'0px'}" dis-hover>
                    <div :style="{color:'#808695',height:'72px'}" style="text-align:center" class="rightBorder">
                      <div :style="{paddingBottom:'8px',marginBottom:'8px',fontWeight:'bolder'}">
                        hotfix跳过数量
                      </div>
                      <div style="padding-top: 15px;  width: 100%; margin-top: -15px">
                        <div :style="{fontWeight:'border',fontSize:'25px', color: '#17233d'}">
                          {{deliveryqualitydata.hotfix_deploy}}
                        </div>
                      </div>
                    </div>
                  </Card>
                  </Col>
                </Row>
              </TimelineItem>
            </Timeline>
            </Col>
          </Row>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import {Bus} from '@/global/bus'
  // import axios from 'axios'
  // import router from '@/router'
  import CommonDirection from './CommonDirection'
  import Highcharts from 'highcharts/highstock'
  import Xrange from 'highcharts/modules/xrange'
  import HighchartsMore from 'highcharts/highcharts-more'
  import Direction from '../Common/direction'
  import CommonCardList from '../Common/CommonCardList'

  Xrange(Highcharts)
  HighchartsMore(Highcharts)

  Bus.$on('refreshhomepagedelayinfo', function () {
    Bus.analyticsHomepageUnitOnesObject.transferDelayCnt = Bus.transferDelayCnt
    Bus.analyticsHomepageUnitOnesObject.testDelayCnt = Bus.testDelayCnt
    Bus.analyticsHomepageUnitOnesObject.launchDelayCnt = Bus.launchDelayCnt
    Bus.analyticsHomepageUnitOnesObject.devTestRatio = Bus.devTestRatio
    Bus.analyticsHomepageUnitOnesObject.jointDebugRatio = Bus.jointDebugRatio
  })
  Bus.$on('refreshhomepagetestinfo', function () {
    Bus.analyticsHomepageUnitOnesObject.validBugCnt = Bus.validBugCnt
    Bus.analyticsHomepageUnitOnesObject.validBugRatio = Bus.validBugRatio
    Bus.analyticsHomepageUnitOnesObject.averageBugPerDay = Bus.averageBugPerDay
  })
  Bus.$on('refreshhomepageonlineinfo', function () {
    // console.log(Bus.processMetricsUnitObject)
    Bus.analyticsHomepageUnitOnesObject.validFaultCnt = Bus.validFaultCnt
    Bus.analyticsHomepageUnitOnesObject.validOnlineBugCnt = Bus.validOnlineBugCnt
    Bus.analyticsHomepageUnitOnesObject.averageDuration = Bus.averageDuration
    Bus.analyticsHomepageUnitOnesObject.averageHideDuration = Bus.averageHideDuration
  })
  Bus.$on('refreshProjectList', function () {
    Bus.analyticsHomepageUnitOnesObject.onesProjectList = Bus.onesProjectList
  })
  Bus.$on('refreshhomepagedataOnes', function () {
    Bus.analyticsHomepageUnitOnesObject.homepagedata = Bus.homepagedata
    Bus.analyticsHomepageUnitOnesObject.handleresourceconsumechartdata(Bus.analyticsHomepageUnitOnesObject.homepagedata.devStats, Bus.analyticsHomepageUnitOnesObject.homepagedata.testStats)
    Bus.analyticsHomepageUnitOnesObject.handletestqualitychartdata(Bus.analyticsHomepageUnitOnesObject.homepagedata.bugStats)
  })
  Bus.$on('refreshdeliverydataOnes', function () {
    Bus.analyticsHomepageUnitOnesObject.handledeliverydata(Bus.marketdetail)
  })
  Bus.$on('refreshdeliveryqualityOnes', function () {
    Bus.analyticsHomepageUnitOnesObject.deliveryqualitydata = Bus.deliveryquality
  })

  let noticeStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '30px'
  }
  export default {
    components: {
      CommonCardList,
      Direction,
      CommonDirection
    },
    name: 'analytics-homepage-unit-ones',
    data: function () {
      // let date = new Date()
      // let dateRange = []
      // // 设置初始时间
      // let end = this.getTimeString(date)
      // date.setDate(date.getDate() - 7)s
      // let start = this.getTimeString(date)
      // dateRange.push(start)
      // dateRange.push(end)
      return {
        span: 3,
        row: 10,
        keys: ['projectName', 'projectId'],
        isLoading: false,
        visiable: true,
        noticeStyle: noticeStyle,
        directionid: this.$route.query.group,
        transferDelayCnt: 0,
        testDelayCnt: 0,
        launchDelayCnt: 0,
        averageBugPerDay: '',
        validBugRatio: '',
        validBugCnt: 0,
        pageSize: 20,
        currentPage: 1,
        tablewidth: 0,
        columnsdevops: [
          {
            title: '流动效率',
            key: 'flowEfficiency',
            align: 'center',
            children: [
              {
                title: '需求响应周期',
                key: 'responsePeriod',
                align: 'center',
                children: [
                  {
                    title: '交付周期（天）',
                    key: 'deliveryPeriod',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: '开发周期（天）',
                    key: 'developPeriod',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  }
                ]
              },
              {
                title: '持续发布能力',
                key: 'ContinuousReleaseCapability',
                align: 'center',
                children: [
                  {
                    title: '集成发布时长（天）',
                    key: 'integrationReleaseTime',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: '发布频率（次数/天）',
                    key: 'releaseFrequency',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  }
                ]
              }
            ]
          },
          {
            title: '资源效率',
            key: 'sourceEfficiency',
            align: 'center',
            children: [
              {
                title: '交付吞吐率',
                key: 'deliveryThroughput',
                align: 'center',
                children: [
                  {
                    title: '度量周期交付需求数',
                    key: 'deliveryReqs',
                    align: 'center'
                  }
                ]
              }
            ]
          },
          {
            title: '质量',
            key: 'quality',
            align: 'center',
            children: [
              {
                title: '过程质量',
                key: 'processQuality',
                align: 'center',
                children: [
                  {
                    title: '单位时间bug率',
                    key: 'bugRate',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: '新增千行代码bug率',
                    key: 'thousandLinesBugRate',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: '代码测试覆盖率',
                    key: 'codeTestCoverage',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  }
                ]
              },
              {
                title: '交付质量',
                key: 'deliveryQuality',
                align: 'center',
                children: [
                  {
                    title: '线上问题数',
                    key: 'onlineIssues',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: 'Hotfix合入次数',
                    key: 'hotfixMerge',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  },
                  {
                    title: '发布回滚率',
                    key: 'releaseRollbackRate',
                    align: 'center',
                    width: this.tablewidth * 0.09
                  }
                ]
              }
            ]
          }
        ],
        datadevops: [],
        deliveryheight: {minHeight: '500px'},
        devTestRatio: '',
        jointDebugRatio: '',
        validFaultCnt: '',
        validOnlineBugCnt: '',
        averageDuration: '',
        averageHideDuration: '',
        sourceconsumeseries: [],
        devjointratioseries: [],
        validbugattributeseries: [],
        validbugperdayseries: [],
        deliveryReq: [],
        deliverydata: [],
        deliverybubble: [],
        backupdisplaylist: [],
        reqstatistics: {},
        onesProjectList: [],
        deliveryqualitydata: this.getdeliveryquality(),
        homepagedata: this.initdata(),
        marketdetail: [],
        filterStatus: [],
        backupOriginList: [],
        filterType: [],
        jiraIssueDisplayList: [],
        jiraIssueOriginList: [],
        filterReqStatusList: [
          '评审中', '开发中', '测试中', '测试完成待上线', '已上线', '效果评估完成'
        ],
        filterReqTypeList: [
          '产品需求', '技术需求'
        ],
        jiraIssueDictOrigin: {}
      }
    },
    methods: {
      initdata: function () {
        let data = {
          devCount: {
            fecompleteCount: 0,
            fecompleteRate: '0.00',
            feCount: 0,
            totalcompleteCount: 0,
            becompleteCount: 0,
            becompleteRate: '0.00',
            totalCount: 0,
            beCount: 0,
            clientCount: 0,
            clientcompleteCount: 0,
            clientcompleteRate: '0.00',
            totalcompleteRate: '0.00'
          },
          reqCount: {
            reqTestByQaCount: 0,
            totalReqCount: 0,
            productReqCount: 0,
            releaseCount: 0,
            techReqCount: 0
          },
          testStats: {
            totalTestPD: 0,
            feTestPD: 0,
            beTestPD: 0,
            clientTestPD: 0
          },
          reqReleaseCount: {
            totalReleaseCount: 0,
            totalReleaseRate: '0.00',
            totalReqCount: 0,
            techReleaseRate: '0.00',
            productReqCount: 0,
            productReleaseRate: '0.00',
            techReqCount: 0,
            techReleaseCount: 0,
            productReleaseCount: 0
          },
          devStats: {
            beDevPD: 0,
            beDebugPD: 0,
            feDevPD: 0,
            feDebugPD: 0,
            dataDevPD: 0,
            dataDebugPD: 0,
            hardwareDevPD: 0,
            hardwareDebugPD: 0,
            iOSDevPD: 0,
            iOSDebugPD: 0,
            androidDevPD: 0,
            androidDebugPD: 0,
            clientDevPD: 0,
            clientDebugPD: 0,
            totalDevPD: 0,
            totalDebugPD: 0,
            beDevRate: '0.00',
            feDevRate: '0.00',
            beDebugRate: '0.00',
            feDebugRate: '0.00',
            clientDevRate: '0.00',
            clientDebugRate: '0.00'
          },
          reqDeliverStats: {
            reqTestByQaCount: 0,
            totalReqCount: 0,
            effectReviewReqCount: 0,
            testedReqCount: 0,
            ticeDelayCount: 0,
            testPDdelayCount: 0,
            releaedReqCount: 0,
            releaseDelayCount: 0
          },
          bugStats: {
            clientBugRate: '0.00',
            clientBugCount: 0,
            productBugRate: '0.00',
            beAverageBugRate: '0.00',
            clientAverageBugRate: '0.00',
            feBugCount: 0,
            feValidBugRate: '0.00',
            beBugRate: '0.00',
            feAverageBugRate: '0.00',
            validBugCount: 0,
            otherBugCount: 0,
            otherBugRate: '0.00',
            beBugCount: 0,
            totalAverageBugRate: '0.00',
            totalBugCount: 0,
            productBugCount: 0,
            feBugRate: '0.00',
            clientValidBugRate: '0.00',
            totalValidBugRate: '0.00',
            beValidBugRate: '0.00'
          },
          ticeStats: {
            averageDevPd: '0.00',
            testEfficiency: '0.00',
            devDebugRate: '0.00',
            averageTestPd: '0.00'
          }
        }
        return data
      },
      comparetime: function (plantime, actualtime) {
        let start = []
        let end = []
        let result = -3
        if (plantime !== '' && actualtime !== '') {
          start = plantime.split('-')
          var startdate = new Date(start[0], parseInt(start[1] - 1), start[2])
          end = actualtime.split('-')
          var enddate = new Date(end[0], parseInt(end[1] - 1), end[2])
          if (startdate > enddate) {
            result = 1
          } else if (startdate < enddate) {
            result = -1
          } else {
            result = 0
          }
        }
        if (!actualtime) {
          result = -2
        }
        return result
      },
      setfilterreqtype: function (type) {
        let result = []
        for (let each in this.jiraIssueDictOrigin) {
          if (type === '产品需求') {
            if (this.jiraIssueDictOrigin[each].reqType === '需求' || this.jiraIssueDictOrigin[each].reqType === '产品需求') {
              result.push(each)
            }
          } else {
            if (this.jiraIssueDictOrigin[each].reqType === type) {
              result.push(each)
            }
          }
        }
        return result
      },
      setfilterreqstatus: function (status) {
        let result = []
        for (let each in this.jiraIssueDictOrigin) {
          if (this.jiraIssueDictOrigin[each].status === status) {
            result.push(each)
          }
        }
        return result
      },
      cardClickReview () {
        this.filterStatus = ['评审中']
        this.filterReq()
      },
      cardClickDevelop () {
        this.filterStatus = ['开发中']
        this.filterReq()
      },
      cardClickTest () {
        this.filterStatus = ['测试中']
        this.filterReq()
      },
      cardClickTested () {
        this.filterStatus = ['测试完成待上线']
        this.filterReq()
      },
      cardClickLaunch () {
        this.filterStatus = ['已上线']
        this.filterReq()
      },
      cardClickClosed () {
        this.filterStatus = ['效果评估完成']
        this.filterReq()
      },
      filterReq: function () {
        let originDisplay = []
        let reqTypeSet = []
        let reqStatusSet = []
        if (this.filterStatus.length === 0 && this.filterType.length === 0) {
          originDisplay = this.backupOriginList
        } else {
          if (this.filterType.length === 0) {
            reqTypeSet = this.backupOriginList
          } else {
            for (let type of this.filterType) {
              reqTypeSet = this.getUnion(reqTypeSet, this.setfilterreqtype(type))
            }
          }
          if (this.filterStatus.length === 0) {
            reqStatusSet = this.backupOriginList
          } else {
            for (let status of this.filterStatus) {
              reqStatusSet = this.getUnion(reqStatusSet, this.setfilterreqstatus(status))
            }
          }
          originDisplay = this.getIntersection(reqTypeSet, reqStatusSet)
        }
        this.jiraIssueOriginList = originDisplay
        this.changePage(1)
        // this.changeCurrentPage(this.jiraIssueOriginList)
      },
      // handlestructuredata: function (data) {
      //   this.jiraIssueOriginList = []
      //   this.jiraIssueDictOrigin = {}
      //   this.backupOriginList = []
      //   if (data) {
      //     for (const each of data) {
      //       this.jiraIssueOriginList.push(each.reqKey)
      //       this.jiraIssueDictOrigin[each.reqKey] = each
      //     }
      //     this.backupOriginList = this.jiraIssueOriginList
      //     this.changeCurrentPage(this.jiraIssueOriginList)
      //   }
      // },
      getdeliveryquality: function () {
        let data = {
          deploy: 0,
          rollback: 0,
          hotfix_deploy: 0,
          hotfix_merge: 0,
          tice_sum: 0,
          tice_valid_back: 0,
          repoCount: 0,
          branchCount: 0,
          codeLines: 0
        }
        return data
      },
      handledeliverydata: function (data) {
        const self = this
        // console.log('data', data)
        this.deliveryReq = []
        this.deliverydata = []
        this.deliverybubble = []
        this.deliveryspeedhighchart()
        this.deliverybubblechart()
        let num = 0
        for (const each in data) {
          if (data[each].status === '已上线' || data[each].status === '效果评估完成') {
            let createTime, startDevTime, startTestTime, endTestTime, actualReleaseTime, deliveryTime
            // if (data[each].createdTime && data[each].actualReleaseTime && this.comparetime(data[each].createdTime, data[each].actualReleaseTime) === -1) {
            //   createTime = this.splittime(data[each].createdTime)
            //   actualReleaseTime = this.splittime(data[each].actualReleaseTime)
            //   this.deliverydata.push({
            //     x: Date.UTC(createTime[0], createTime[1], createTime[2]),
            //     x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1], actualReleaseTime[2]),
            //     y: num,
            //     color: '#8192D6'
            //   })
            //   this.deliveryReq.push(data[each].reqKey)
            //   num += 1
            // }
            if (data[each].createdTime && data[each].actualReleaseTime && this.comparetime(data[each].createdTime, data[each].actualReleaseTime) === -1) {
              createTime = this.splittime(data[each].createdTime)
              actualReleaseTime = this.splittime(data[each].actualReleaseTime)
              if (data[each].startDevTime) {
                startDevTime = this.splittime(data[each].startDevTime)
                this.deliverydata.push({
                  x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2]),
                  x2: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                  y: num,
                  name: data[each].reqSummary,
                  key: data[each].reqKey,
                  url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                  period: '创建到开始开发',
                  z: parseFloat(data[each].deliverPD),
                  starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2])),
                  endtime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                  color: '#8192D6'
                })
                if (data[each].startTestTime) {
                  startTestTime = this.splittime(data[each].startTestTime)
                  this.deliverydata.push({
                    x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                    x2: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                    y: num,
                    name: data[each].reqSummary,
                    key: data[each].reqKey,
                    url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                    period: '开始开发到开始测试',
                    z: parseFloat(data[each].deliverPD),
                    starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                    endtime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                    color: '#20B2AA'
                  })
                  if (data[each].endTestTime) {
                    endTestTime = this.splittime(data[each].endTestTime)
                    this.deliverydata.push({
                      x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                      x2: Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '开始测试到测试完成',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2])),
                      color: '#c1405a'
                    })
                    this.deliverydata.push({
                      x: Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2]),
                      x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '测试完成到上线',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                      color: '#c581d6'
                    })
                  } else {
                    this.deliverydata.push({
                      x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2]),
                      x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                      y: num,
                      name: data[each].reqSummary,
                      key: data[each].reqKey,
                      url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                      period: '开始测试到上线',
                      z: parseFloat(data[each].deliverPD),
                      starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2])),
                      endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                      color: '#c1405a'
                    })
                  }
                } else {
                  this.deliverydata.push({
                    x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2]),
                    x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                    y: num,
                    name: data[each].reqSummary,
                    key: data[each].reqKey,
                    url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                    period: '开始开发到上线',
                    z: parseFloat(data[each].deliverPD),
                    starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2])),
                    endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                    color: '#20B2AA'
                  })
                }
              } else {
                this.deliverydata.push({
                  x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2]),
                  x2: Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2]),
                  y: num,
                  name: data[each].reqSummary,
                  key: data[each].reqKey,
                  url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                  period: '创建到上线',
                  z: parseFloat(data[each].deliverPD),
                  starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2])),
                  endtime: this.handlexAxis(Date.UTC(actualReleaseTime[0], actualReleaseTime[1] - 1, actualReleaseTime[2])),
                  color: '#8192D6'
                })
              }
              this.deliveryReq.push(data[each].reqKey)
              num += 1
            }
            // console.log('pd', parseFloat(data[each].deliverPD))
            if (data[each].actualReleaseTime && parseFloat(data[each].deliverPD) !== 0) {
              deliveryTime = this.splittime(data[each].actualReleaseTime)
              this.deliverybubble.push({
                x: Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2]),
                y: parseFloat(data[each].deliverPD),
                z: parseFloat(data[each].deliverPD),
                name: data[each].reqSummary,
                key: data[each].reqKey,
                url: 'https://flow.sankuai.com/browse/' + data[each].reqKey,
                time: this.handlexAxis(Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2]))
              })
            }
          }
        }
        // console.log('y', this.deliveryReq)
        // console.log('x', this.deliverydata)
        setTimeout(function () {
          // console.log(self.deliveryReq.length)
          self.refreshHeight(self.deliveryReq.length)
        }, 0)
      },
      splittime: function (data) {
        let result = data.split('-')
        return result
      },
      refreshHeight: function (data) {
        const self = this
        // console.log(data)
        let height = data * 15 + 25 > 500 ? data * 15 + 25 : 500
        this.deliveryheight = {minHeight: height + 'px'}
        // console.log(height)
        setTimeout(function () {
          self.deliveryspeedhighchart()
          self.deliverybubblechart()
        }, 100)
      },
      handleresourceconsumechartdata: function (devData, testData) {
        let self = this
        self.sourceconsumeseries = []
        self.sourceconsumeseries = [
          {
            name: '开发',
            data: [devData.feDevPD, devData.beDevPD, devData.clientDevPD, devData.totalDevPD]
          },
          {
            name: '联调',
            data: [devData.feDebugPD, devData.beDebugPD, devData.clientDebugPD, devData.totalDebugPD]
          },
          {
            name: '测试',
            data: [testData.feTestPD, testData.beTestPD, testData.clientTestPD, testData.totalTestPD]
          }
        ]
        self.devjointratioseries = []
        self.devjointratioseries = [
          {
            name: '后端开发时长',
            y: devData.beDevRate * 100
          },
          {
            name: '前端开发时长',
            y: devData.feDevRate * 100
          },
          {
            name: '客户端开发时长',
            y: devData.clientDevRate * 100
          },
          {
            name: '后端联调时长',
            y: devData.beDebugRate * 100
          },
          {
            name: '前端联调时长',
            y: devData.feDebugRate * 100
          },
          {
            name: '客户联调时长',
            y: devData.clientDebugRate * 100
          }
        ]
        setTimeout(function () {
          self.devtestratiohighchart()
          self.devandjointdebughighchart()
        }, 0)
      },
      handletestqualitychartdata: function (data) {
        let self = this
        self.validbugattributeseries = []
        self.validbugattributeseries = [
          {
            name: '后端',
            y: data.beBugRate * 100
          },
          {
            name: '前端',
            y: data.feBugRate * 100
          },
          {
            name: '客户端',
            y: data.clientBugRate * 100
          },
          {
            name: '产品',
            y: data.productBugRate * 100
          },
          {
            name: '其他',
            y: data.otherBugRate * 100
          }
        ]
        self.validbugperdayseries = []
        self.validbugperdayseries = [
          {
            name: '工时平均有效bug',
            data: [parseFloat(data.feAverageBugRate), parseFloat(data.beAverageBugRate), parseFloat(data.clientAverageBugRate), parseFloat(data.totalAverageBugRate)]
          },
          {
            name: '有效bug占比',
            data: [parseFloat(data.feValidBugRate), parseFloat(data.beValidBugRate), parseFloat(data.clientValidBugRate), parseFloat(data.totalValidBugRate)]
          }
        ]
        setTimeout(function () {
          self.bugattributeratiochart()
          self.effectivebugperdayhighchart()
        }, 0)
      },
      deliverybubblechart: function () {
        Highcharts.chart('deliverybubblechart', {
          chart: {
            type: 'bubble',
            zoomType: 'xy'
          },
          title: {
            text: '需求创建到上线交付周期'
          },
          subtitle: {
            text: '横坐标代表需求交付时间，纵坐标为需求交付耗费时间，点击可查看具体需求'
          },
          tooltip: {
            enabled: true,
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>交付时间 :&nbsp</th><td>{point.time}</td></tr>' +
            '<tr><th>需求周期 :&nbsp</th><td>{point.y}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open(event.point.url)
                }
              }
            }
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '交付周期（PD）'
            },
            labels: {
              format: '{value} PD'
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: this.reqstatistics.averageDeliverPD,
              width: 2,
              label: {
                text: '平均:' + this.reqstatistics.averageDeliverPD,
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -50
              }
            }],
            min: 0
          },
          // tooltip: {
          //   dateTimeLabelFormats: {
          //     day: '%Y-%m-%d'
          //   }
          // },
          series: [{
            name: '交付分布图',
            data: this.deliverybubble
          }],
          credits: {
            enabled: false
          }
        })
      },
      deliveryspeedhighchart: function () {
        Highcharts.chart('deliveryspeedchart', {
          chart: {
            type: 'xrange'
          },
          title: {
            text: ''
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '需求key'
            },
            categories: this.deliveryReq,
            reversed: true
          },
          legend: {
            enabled: true
          },
          tooltip: {
            dateTimeLabelFormats: {
              day: '%Y-%m-%d'
            },
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>{point.period} :&nbsp</th><td>{point.starttime}~{point.endtime}</td></tr>' +
            '<tr><th>需求周期 :</th><td>{point.z}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open(event.point.url)
                }
              }
            }
          },
          // colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '需求创建到上线交付时间',
            pointWidth: 8,
            data: this.deliverydata,
            dataLabels: {
              enabled: false
            }
          }],
          credits: {
            enabled: false
          }
        })
      },
      devtestratiohighchart: function () {
        Highcharts.chart('devandtestcolumn', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '各端开发测试资源消耗'
          },
          xAxis: {
            categories: ['前端', '后端', '客户端', '整体']
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            // plotLines: [{
            //   color: 'red',
            //   dashStyle: 'longdashdot',
            //   value: 5,
            //   width: 2,
            //   label: {
            //     text: '5',
            //     style: {
            //       color: 'red',
            //       fontWeight: 'bold'
            //     },
            //     align: 'left',
            //     x: -10
            //   }
            // },
            // {
            //   color: 'blue',
            //   dashStyle: 'longdashdot',
            //   value: 5,
            //   width: 2,
            //   label: {
            //     text: '50',
            //     style: {
            //       color: 'blue',
            //       fontWeight: 'bold'
            //     },
            //     align: 'left',
            //     x: -10
            //   }
            // }],
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.sourceconsumeseries,
          credits: {
            enabled: false
          }
        })
      },
      devandjointdebughighchart: function () {
        Highcharts.chart('devandjointdebugpie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '各端开发联调占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: this.devjointratioseries
          }],
          credits: {
            enabled: false
          }
        })
      },
      bugattributeratiochart: function () {
        Highcharts.chart('bugattributepie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '有效bug归属方向占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: this.validbugattributeseries
          }],
          credits: {
            enabled: false
          }
        })
      },
      effectivebugperdayhighchart: function () {
        Highcharts.chart('effectivebugperdaycolumn', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '各端工时平均有效bug'
          },
          xAxis: {
            categories: ['前端', '后端', '客户端', '整体']
          },
          yAxis: {
            min: 0,
            max: 1,
            title: {
              text: null
            },
            // plotLines: [{
            //   color: 'red',
            //   dashStyle: 'longdashdot',
            //   value: 5,
            //   width: 2,
            //   label: {
            //     text: '5',
            //     style: {
            //       color: 'red',
            //       fontWeight: 'bold'
            //     },
            //     align: 'left',
            //     x: -10
            //   }
            // },
            // {
            //   color: 'blue',
            //   dashStyle: 'longdashdot',
            //   value: 5,
            //   width: 2,
            //   label: {
            //     text: '50',
            //     style: {
            //       color: 'blue',
            //       fontWeight: 'bold'
            //     },
            //     align: 'left',
            //     x: -10
            //   }
            // }],
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.validbugperdayseries,
          credits: {
            enabled: false
          }
        })
      },
      evesolvetimehighchart: function () {
        Highcharts.chart('solvetimeratiopie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '线上问题解决时间占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: [{
              name: '1小时以内',
              y: 45
            }, {
              name: '大于1小时小于24小时',
              y: 15
            }, {
              name: '大于24小时小于1个月',
              y: 10
            }, {
              name: '大于1个月',
              y: 20
            }, {
              name: '其他',
              y: 10
            }]
          }],
          credits: {
            enabled: false
          }
        })
      },
      evehiddentimehighchart: function () {
        Highcharts.chart('hiddentimeratiopie', {
          chart: {
            type: 'pie'
          },
          title: {
            text: '线上问题隐藏时间占比统计'
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              allowPointSelect: true,
              cursor: 'pointer',
              dataLabels: {
                enabled: false
              },
              showInLegend: true
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '占比',
            colorByPoint: true,
            data: [{
              name: '24小时以内',
              y: 45
            }, {
              name: '大于24小时小于1个月',
              y: 15
            }, {
              name: '大于1个月小于3个月',
              y: 10
            }, {
              name: '大于3个月',
              y: 20
            }, {
              name: '其他',
              y: 10
            }]
          }],
          credits: {
            enabled: false
          }
        })
      },
      handlexAxis (time) {
        var date = new Date(time)
        return date.getFullYear() + '-' + parseInt(date.getMonth() + 1) + '-' + date.getDate()
      },
      getUnion: function (a, b) {
        // 并集
        return Array.from(new Set(a.concat(b)))
      },
      getIntersection: function (a, b) {
        // 交集
        let bSet = new Set(b)
        return Array.from(new Set(a.filter(v => bSet.has(v))))
      },
      getDifference: function (a, b) {
        // 差集
        let aSet = new Set(a)
        Array.from(new Set(a.concat(b).filter(v => !aSet.has(v) || !b.has(v))))
      },
      changePage: function (page) {
        this.currentPage = page
        this.changeCurrentPage(this.jiraIssueOriginList)
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage(this.jiraIssueOriginList)
      },
      changeCurrentPage: function (data) {
        let currentPage = this.currentPage
        let pageSize = this.pageSize
        this.jiraIssueDisplayList = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          this.jiraIssueDisplayList.push(data[i])
        }
      }
    },
    mounted: function () {
      Bus.analyticsHomepageUnitOnesObject = this
      // this.tablewidth = document.getElementById('deliverytable').offsetWidth
    }
  }
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }

  @keyframes ani-demo-spin {
    from {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  a:link {
    text-decoration: none;
  }

  a:active {
    text-decoration: none;
  }

  　a:hover {
    text-decoration: none;
  }

  　a:visited {
    text-decoration: none;
  }

  .reqreviewChart {
    background-color: #83C75D;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .techreviewChart {
    background-color: #5BBD2B;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    vertical-align: middle;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .devChart {
    background-color: #50A625;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .testChart {
    background-color: #489620;
    height: 20px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    /*border-top-left-radius: 5px;*/
    /*border-bottom-left-radius: 5px*/
  }

  .leftChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .allChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .onlymiddleleftChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .middleleftChart {
    background-color: #2d8cf0;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .leftestChart {
    background-color: #e8488b;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .rightChart {
    background-color: #f3f3f3;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .onlyrightChart {
    background-color: #f3f3f3;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .rightBorder {
    padding-left: -0px;
    border-right-style: dotted;
    border-right-color: #dcdee2;
    border-right-width: 1px;
  }

  .bottomBorder {
    border-bottom-style: dotted;
    border-bottom-color: #dcdee2;
    border-bottom-width: 1px;
  }

  .reqChart {
    background-color: #19be6b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .techChart {
    background-color: #ff9900;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .otherChart {
    background-color: #f3f3f3;
    height: 20px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #000000;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .noneChart {
    background-color: #f3f3f3;
    height: 15px;
    margin: 0;
    padding-top: 3.5px;
    text-align: center;
    color: #000000;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .ivu-card-bordered {
    border: 0px #f3f3f3;
  }
</style>

