<template>
  <div>
    <Row type="flex" style="width: 100%">
      <Col style="text-align: right;width: 100%;display: flex">
      <div style="width: 50px">
        <Tooltip placement="bottom" content="点击查看实时统计数据说明">
          <a href="https://km.sankuai.com/page/140955020" target="_blank">数据说明</a>
        </Tooltip>
      </div>
      <div>
        <span :style="{fontWeight:'bolder',marginLeft:'10px'}">组织节点</span>
      </div>
      <div style="margin-top: -8px;width:27%;padding-left: 10px">
        <Cascader :transfer=true :data="directionTree" :change-on-select=true @on-change="changeselect" :clearable=false placeholder="请选择要筛选的方向" style="padding-left: 10px"></Cascader>
        <!--<direction :width="width"></direction>-->
      </div>
      <div>
        <span :style="{fontWeight:'bolder',marginLeft:'10px'}">业务方向</span>
      </div>
      <div style="margin-top: -8px;width: 20%;margin-left: 10px">
        <sub-direction :width="width"></sub-direction>
      </div>
      <Select :transfer=true
              style="margin-left:10px;margin-top: -8px;width: 85px;text-align: left"
              v-model="value6" :label-in-value="true" @on-change="changedirection">
        <Option v-for="item in selectDirection" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <DatePicker v-if="value6 && value6 === '服务端'" style="margin-top: -8px;width: 200px;margin-left: 10px" v-model="value3" :options="options2" type="daterange" placement="bottom-end" placeholder="请选择时间间隔" @on-change="changedatepicker"></DatePicker>
      <!--<Select v-if="value6 && value6 === '服务端'" v-model="reqtype" multiple clearable style="margin-top: -8px;margin-left: 10px; text-align: left; width:210px" placeholder="请选择需求类型" @on-change="selectreqtype">-->
        <!--<Option v-for="item in reqtypelist" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
      <!--</Select>-->
      <Cascader :transfer=true v-if="value6 && value6 === '客户端'" placeholder="请选择要筛选的版本"
              style="margin-left:10px;margin-top: -8px;width: 200px;text-align: left"
              v-model="value7" :data="data2" :change-on-select=true @on-change="changeversion">
      </Cascader>
      <Select :transfer=true v-if="value6 && value6 === '客户端'" @on-change="changeapp"
              style="margin-left:10px;margin-top: -8px;width: 90px;text-align: left"
              v-model="value8" :label-in-value="true">
        <Option v-for="item in appList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <div style="margin-left: 10px;margin-top: -8px" v-if="value6">
        <Button @click="postData()" type="primary">查看</Button>
      </div>
      <div v-if="display5 && value6 && value6 === '服务端'" style="margin-top: -8px;margin-left: 10px">
        <Button type="primary" @click="modal1 = true">导出</Button>
        <Modal v-model="modal1" title="提示：" @on-ok="ok" @on-cancel="cancel1">
          <Row>
            <Col order="1" span="1">
            <Icon type="help-circled"
                  style="padding-left:13px;padding-top: 7px;font-size: larger;font-weight: bolder"
                  color="rgb(255, 165, 0)"></Icon>
            </Col>
            <Col order="2" span="20">
            <p style="font-size: 14px;padding-left: 17px;color:rgb(255, 165, 0);padding-top: 4px;">是否导出数据到Wiki？</p>
            </Col>
          </Row>
        </Modal>
      </div>
      <div style="margin-left: 10px;margin-top: -5px">
        <Button class="tool-button" icon="md-arrow-forward" type="dashed" size="small" @click="toOldRouter()">返回旧版</Button>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
  import axios from 'axios'
  import router from '@/router'
  import {Bus} from '@/global/bus'
  import {analyticswikiAPI, customanalyticsbaseAPI, analyticsbaseonesAPI} from '@/global/variable'
  import moment from 'moment'
  import Direction from './multipleDirectionComponent'
  import SubDirection from './subDirectionComponent'

  let tableData = []
  Bus.$on('refreshRequirmentTaskTypeOnes', function (data) {
    Bus.commonDirectionOnesObject.setreqType(data)
  })
  Bus.$on('showProcessMetricsTabOnes', function () {
    Bus.commonDirectionOnesObject.display5 = true
  })
  Bus.$on('refreshDistributionOnes', function () {
    Bus.commonDirectionOnesObject.distribution = true
    // Bus.commonDirectionOnesObject.getProcessData(data)
  })
  Bus.$on('refreshgetprocessdataOnes', function (data) {
    Bus.commonDirectionOnesObject.getProcessData(data)
  })

  export default {
    components: {
      SubDirection, Direction},
    name: 'common-direction-ones',
    data: function () {
      return {
        reqtype: [],
        modal1: false,
        searchapi: true,
        flag: false,
        addModal: false,
        directionTree: [],
        directionList: {
          selected: []
        },
        width: 500,
        backupSelect: [],
        backupSelectId: [],
        directionIdList: [],
        dirTree: {},
        clickparams: {},
        backupclickparams: {},
        showdirection: [],
        onesProjectList: [],
        columns4: [
          {
            title: 'task key',
            key: 'issueKey',
            sortable: true
          },
          {
            title: 'task名称',
            key: 'summary',
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  props: {
                    href: 'https://flow.sankuai.com/browse/' + params.row['issueKey']
                  },
                  on: {
                    click: () => {
                      window.open('https://flow.sankuai.com/browse/' + params.row['issueKey'])
                    }
                  }
                }, params.row['summary'])
              ])
            }
          },
          {
            title: '错误原因',
            key: 'reason',
            sortable: true
          },
          {
            title: '经办人',
            key: 'assignee',
            sortable: true
          }
        ],
        tableData: tableData,
        pattern: '',
        display2: false,
        display3: false,
        display5: false,
        distribution: false,
        modal6: false,
        invalidData: [],
        value2: 'CUSTOMED',
        value1: [],
        value3: [],
        value4: 0,
        value6: '',
        value7: [],
        value8: '',
        data1: [],
        data2: [],
        selectOnesDirection: '',
        backupselectOnesDirection: '',
        reqtypelist: [
          {
            value: '产品需求',
            label: '产品需求'
          }, {
            value: '技术需求',
            label: '技术需求'
          }
        ],
        selectList: [
          {
            value: 'CUSTOMED',
            label: '自定义'
          }
          // {
          //   value: 'QUARTER',
          //   label: '按季度'
          // },
          // {
          //   value: 'MONTH',
          //   label: '按月份'
          // }
        ],
        selectDirection: [
          {
            value: '客户端',
            label: '客户端'
          },
          {
            value: '服务端',
            label: '服务端'
          }
        ],
        appList: [
          {
            value: 'iOS',
            label: 'iOS'
          },
          {
            value: 'Android',
            label: 'Android'
          }
        ],
        options2: {
          shortcuts: [
            {
              text: 'A week',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                return [start, end]
              }
            },
            {
              text: 'A month',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                return [start, end]
              }
            },
            {
              text: '3 month',
              value () {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                return [start, end]
              }
            }
          ]
        }
      }
    },
    methods: {
      handleOpen: function () {
        this.addModal = true
        this.backupSelect = []
        this.backupSelect = this.directionList.selected
      },
      handleClose: function (data, index) {

      },
      changedirection: function (value) {
        let self = this
        let dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          searchtype: this.$route.query.searchtype,
          app: this.$route.query.app,
          version: this.$route.query.version,
          platform: this.$route.query.platform,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (Object.keys(value).length !== 0) {
          dict.searchtype = value.value
        } else {
          dict.searchtype = '服务端'
        }
        setTimeout(function () {
          if (self.$route.path.indexOf('custom') === -1) {
            router.push({ path: `/homepage/analytics/ones/${self.$route.params.tab}`, query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${self.$route.params.tab}`, query: dict })
          }
        }, 10)
      },
      changeversion: function (value) {
        let dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          searchtype: this.$route.query.searchtype,
          app: this.$route.query.app,
          version: this.$route.query.version,
          platform: this.$route.query.platform,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value && value.length === 1) {
          dict.app = value[0]
        } else if (value && value.length === 2) {
          dict.app = value[0]
          dict.version = value[1]
        }
        if (this.$route.path.indexOf('custom') === -1) {
          router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
        } else {
          router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
        }
      },
      changeapp: function (value) {
        let dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          searchtype: this.$route.query.searchtype,
          app: this.$route.query.app,
          version: this.$route.query.version,
          platform: this.$route.query.platform,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value) {
          dict.platform = value.value
        }
        if (this.$route.path.indexOf('custom') === -1) {
          router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
        } else {
          router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
        }
      },
      getClientData: function () {
        const self = this
        axios.get(Bus.basicApi + '/common/client/version', {
          timeout: 10000,
          dataType: 'json'
        }).then(function (message) {
          if (message.data.status === 0) {
            let versionData = []
            for (let bgInfo in message['data']['data']) {
              let bgTop = {}
              bgTop['value'] = bgInfo
              bgTop['label'] = bgInfo
              bgTop['children'] = self.getClinetVersionData(message['data']['data'][bgInfo])
              versionData.push(bgTop)
            }
            self.data2 = versionData
          }
        }).catch(function (error) {
          console.log(error)
        })
      },
      getClinetVersionData: function (data) {
        let childrenList = []
        if (data) {
          for (var each of data) {
            let temp = {
              value: each,
              label: each
            }
            childrenList.push(temp)
          }
        }
        return childrenList
      },
      getBizlineDataFromOnes: function () {
        const self = this
        axios.get('http://analytics.hotel.test.sankuai.com/analytics/common/bizLine/ones', {
          timeout: 10000,
          dataType: 'json'
        }).then(function (message) {
          if (message.data.status === 0) {
            self.getOnesBgData(message.data.data)
          }
        }).catch(function (error) {
          console.log(error)
        })
      },
      getreqtype: function () {
        if (parseInt(this.$route.query.reqtype, 0) === 0) {
          this.reqtype.push('技术需求')
          this.reqtype.push('产品需求')
        } else if (parseInt(this.$route.query.reqtype, 0) === 1) {
          this.reqtype.push('产品需求')
        } else if (parseInt(this.$route.query.reqtype, 0) === 2) {
          this.reqtype.push('技术需求')
        } else {
          this.reqtype.push('技术需求')
          this.reqtype.push('产品需求')
        }
      },
      showDirectionAddModal: function () {
        this.addModal = true
      },
      toOldRouter: function () {
        window.open('http://qa.sankuai.com/homepage/analytics/analyticshome')
      },
      customConfig: function () {
        window.open('http://0.0.0.0:8000/homepage/new-config')
      },
      selectreqtype: function (value) {
        const self = this
        self.flag = false
        if (value.length === 1) {
          if (value[0] === '技术需求') {
            Bus.$emit('refreshRequirmentTaskTypeOnes', '2')
          } else {
            Bus.$emit('refreshRequirmentTaskTypeOnes', '1')
          }
        } else if (value.length === 2) {
          Bus.$emit('refreshRequirmentTaskTypeOnes', '0')
        } else {
          self.flag = true
          self.$Message.info('产品需求、技术需求至少选一个！')
        }
        // setTimeout(function () {
        //   if (!self.flag) {
        //     self.postData(self.value1, self.value2, self.value3, self.reqtype)
        //   }
        // }, 100)
      },
      link () {
        window.open('https://wiki.sankuai.com/pages/viewpage.action?pageId=1350559139')
      },
      setOption (value) {
        if (value.value === 'QUARTER') {
          this.secondList = [
            {
              value: 'this-season',
              label: '本季度'
            },
            {
              value: 'last-season',
              label: '上季度'
            }
          ]
        }
        if (value.value === 'MONTH') {
          this.secondList = [
            {
              value: 'this-month',
              label: '本月'
            },
            {
              value: 'last-month',
              label: '上月'
            }
          ]
        }
      },
      changedatepicker (value) {
        // console.log(value, typeof value[0])
        const dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          name: this.$route.query.name,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value.length === 2) {
          dict.start = value[0]
          dict.end = value[1]
          if (this.$route.path.indexOf('custom') === -1) {
            router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
          }
        }
      },
      getDirectionList: function () {
        let self = this
        axios.get('http://config-hotel.sankuai.com/mcd/org/basic?direction_id=1').then(function (message) {
          self.directionTree.length = 0
          if (message.data.result) {
            self.directionTree.push(message.data.info)
          }
        }).catch(function () {
          self.directionTree.length = 0
        })
      },
      setreqType (value) {
        const dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          name: this.$route.query.name,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value) {
          dict.reqtype = value
          if (this.$route.path.indexOf('custom') === -1) {
            router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
          }
        }
      },
      selectlistchanged (value) {
        const dict = {
          // bg: this.$route.query.bg,
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          name: this.$route.query.name,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (value.value) {
          dict.type = value.value
          if (this.$route.path.indexOf('custom') === -1) {
            router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
          }
        }
      },
      changeselect (value, selectedData) {
        // console.log(' 678 ', typeof value[2], selectedData)
        if (selectedData.length > 0) {
          this.value1 = []
          this.value1.push(selectedData[selectedData.length - 1].direction_id)
        }
        let dict = {
          // bizline: this.$route.query.bizline,
          // group: this.$route.query.group,
          // project: value[value.length - 1],
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          req: this.$route.query.req,
          searchtype: this.$route.query.searchtype,
          reqtype: this.$route.query.reqtype
        }
        if (value.length > 0) {
          dict.project = value[value.length - 1]
        }
        if (this.$route.path.indexOf('custom') === -1) {
          router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
        } else {
          router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${this.$route.params.tab}`, query: dict })
        }
      },
      getProcessData (data) {
        let resourceStats = []
        let timelineStats = []
        let evaluationStats = []
        Bus.transferDelayCnt = 0
        Bus.testDelayCnt = 0
        Bus.launchDelayCnt = 0
        Bus.devTestRatio = ''
        Bus.jointDebugRatio = ''
        if (data) {
          for (let period in data) {
            let eachBizData = []
            eachBizData = data[period]['periodProcessStats']
            for (let each in eachBizData) {
              if (data[period].name === '总计') {
                Bus.transferDelayCnt = eachBizData[each]['timelineStats'].transferDelayCnt
                Bus.testDelayCnt = eachBizData[each]['timelineStats'].testDelayCnt
                Bus.launchDelayCnt = eachBizData[each]['timelineStats'].launchDelayCnt
                Bus.devTestRatio = eachBizData[each]['resourceStats'].devTestRatio
                Bus.jointDebugRatio = eachBizData[each]['resourceStats'].jointDebugRatio
              }
              eachBizData[each]['resourceStats']['direction'] = data[period]['name']
              eachBizData[each]['timelineStats']['direction'] = data[period]['name']
              eachBizData[each]['evaluationStats']['direction'] = data[period]['name']
              eachBizData[each]['resourceStats']['key'] = data[period]['key']
              eachBizData[each]['timelineStats']['key'] = data[period]['key']
              eachBizData[each]['evaluationStats']['key'] = data[period]['key']
              eachBizData[each]['resourceStats']['period'] = each
              eachBizData[each]['timelineStats']['period'] = each
              eachBizData[each]['evaluationStats']['period'] = each
              resourceStats.push(eachBizData[each]['resourceStats'])
              timelineStats.push(eachBizData[each]['timelineStats'])
              evaluationStats.push(eachBizData[each]['evaluationStats'])
            }
          }
          // this.$Spin.hide()
        } else {
          // this.$Spin.hide()
        }
        if (this.$route.query.type === 'CUSTOMED') {
          Bus.$emit('refreshhomepagedelayinfoOnes')
        }
        Bus.$emit('refreshResourceStatOnes', resourceStats, this.value6)
        Bus.$emit('refreshProcessTableLengthOnes', resourceStats.length)
        Bus.$emit('refreshTimelineStatOnes', timelineStats)
        Bus.$emit('refreshEvaluationStatOnes', evaluationStats)
        if (Bus.commonDirectionOnesObject.distribution) {
          Bus.$emit('showProcessMetricsTabOnes')
          // Bus.$emit('refreshisLoadingshow')
        }
      },
      getQualityData (data) {
        if (data) {
          let qualityStats = []
          Bus.qualityStats = []
          Bus.validBugCnt = 0
          Bus.validBugRatio = ''
          Bus.averageBugPerDay = ''
          // console.log('data:', data)
          for (let each in data) {
            let eachBizData = []
            eachBizData = data[each]['periodQualityStats']
            for (let period in eachBizData) {
              if (data[each].name === '总计') {
                Bus.validBugCnt = eachBizData[period].validBugCnt
                Bus.validBugRatio = eachBizData[period].validBugRatio
                Bus.averageBugPerDay = eachBizData[period].averageBugPerDay
              }
              eachBizData[period]['direction'] = data[each]['name']
              eachBizData[period]['key'] = data[each]['key']
              eachBizData[period]['period'] = period
              qualityStats.push(eachBizData[period])
            }
          }
          if (this.$route.query.type === 'CUSTOMED') {
            Bus.$emit('refreshhomepagetestinfoOnes')
          }
          Bus.$emit('refreshQualityStatOnes', qualityStats, this.value6)
          Bus.$emit('refreshQualityTableLengthOnes', qualityStats.length)
        } else {
          Bus.$emit('refreshQualityStatOnes', [])
        }
      },
      getEfficiencyData (data) {
        let rdEfficiencyStats = []
        let qaEfficiencyStats = []
        // let pmEfficiencyStats = []
        // console.log('人效data', data)
        if (data) {
          for (let period in data) {
            let eachBizData = []
            eachBizData = data[period]['efficiencyStats']
            for (let each in eachBizData) {
              eachBizData[each]['rdEfficiencyStats']['direction'] = data[period]['name']
              eachBizData[each]['qaEfficiencyStats']['direction'] = data[period]['name']
              // eachBizData[each]['evaluationStats']['direction'] = data[period]['name'] // pm
              eachBizData[each]['rdEfficiencyStats']['period'] = each
              eachBizData[each]['qaEfficiencyStats']['period'] = each
              // eachBizData[each]['evaluationStats']['period'] = each
              rdEfficiencyStats.push(eachBizData[each]['rdEfficiencyStats'])
              qaEfficiencyStats.push(eachBizData[each]['qaEfficiencyStats'])
              // evaluationStats.push(eachBizData[each]['evaluationStats'])
            }
          }
        }
        // console.log('人效数据', rdEfficiencyStats, qaEfficiencyStats)
        Bus.$emit('refreshRdEfficiencyStatOnes', rdEfficiencyStats)
        Bus.$emit('refreshEfficiencyTableLengthOnes', rdEfficiencyStats.length)
        Bus.$emit('refreshQaEfficiencyStatOnes', qaEfficiencyStats)
        // Bus.$emit('refreshEvaluationStat', qaEfficiencyStats)
        // if (Bus.processMetricsObject.distribution) {
        //   Bus.$emit('showProcessMetricsTab')
        // }
      },
      postData () {
        let self = this
        var parentDirectionList = []
        var subDirectionList = []
        for (let item of this.$store.state.directionList) {
          parentDirectionList.push(item.direction_id)
        }
        for (let item of this.$store.state.subdirectionList) {
          let tempDirection = `${~~(parseInt(item.direction_id) / 100)}^${parseInt(item.direction_id) % 100}`
          subDirectionList.push(tempDirection)
        }
        let params = {}
        Bus.ifdisplayincompletedata = false
        let status = 1
        if (self.value6 && self.value6 === '服务端') {
          let type = 0
          if (self.value1.length === 0) {
            self.$Message.info('请先选择业务线和方向！')
          } else if (self.value2 === '') {
            self.$Message.info('请选择时间范围！')
          } else if (self.value3.length === 0) {
            self.$Message.info('请选择时间！')
          } else if (self.value4.length === 0) {
            self.$Message.info('产品需求、技术需求至少选一个！')
          } else {
            self.$Spin.show()
            status = 0
            if (self.value4.length === 1) {
              if (self.value4[0] === '技术需求') {
                type = 2
              } else {
                type = 1
              }
            } else if (self.value4.length === 2) {
              type = 0
            }
            let start = moment(self.value3[0]).format('YYYY-MM-DD')
            let end = moment(self.value3[1]).format('YYYY-MM-DD')
            params = {
              directionList: self.value1,
              subdirectionList: subDirectionList,
              searchtype: self.value6,
              periodType: self.value2,
              beginTime: start,
              endTime: end,
              reqType: type
            }
          }
        } else if (self.value6 && self.value6 === '客户端') {
          if (self.value1.length === 0) {
            self.$Message.info('请先选择业务线和方向！')
          } else if (self.value7.length !== 2) {
            self.$Message.info('请选择平台及版本！')
          } else if (!self.value8) {
            self.$Message.info('请选择iOS或Android！')
          } else {
            self.$Spin.show()
            status = 0
            params = {
              directionList: self.value1,
              subdirectionList: subDirectionList,
              searchtype: self.value6,
              app: self.value7[0],
              version: self.value7[1],
              platform: self.value8
            }
          }
        } else {
          self.$Spin.hide()
          self.$Message.info('请先选择客户端或服务端！')
        }
        if (status === 0 && params) {
          // console.log('start emit ')
          self.getChooseDirection(self.value1)
          self.gethomepagedata(params) // 首页数据
          // self.getdeliverydata(params) // 获取首页中交付质量&交付吞吐量数据
          self.getmarketstatistics(params) // 需求统计数据
          self.getinitProcessData(params) // 度量数据
          // self.getinitEfficiencyData(params) // 人效数据
          self.getinitOnlineQualityStats(params) // 线上数据
        }
      },
      getChooseDirection (data) {
        let self = this
        Bus.onesProjectList = []
        let temp = []
        for (const id in data) {
          axios.get('http://config-hotel.sankuai.com/mcd/org/analytics/get?direction_id=' + data[id]).then(function (message) {
            if (message.data.result) {
              var result = message.data.info
              if (result) {
                for (const each of result) {
                  if (temp.indexOf(each.projectId) === -1) {
                    temp.push(each.projectId)
                    Bus.onesProjectList.push(each)
                  }
                }
                Bus.onesProjectList = self.sortbyKey(Bus.onesProjectList, 'projectId')
                Bus.$emit('refreshProjectList')
              }
            }
          })
        }
      },
      getMarketOtherData (params) {
        axios.get(Bus.basicApi + '/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0 || message['data']['status'] === 1) {
            let data = message['data']['data']
            for (let each in data) {
              let eachBizData = []
              let everyBizData = []
              eachBizData = data[each]['periodQualityStats']
              everyBizData = data[each]['periodProcessStats']
              for (let period in eachBizData) {
                if (data[each].name === '总计') {
                  Bus.validBugCnt = eachBizData[period].validBugCnt
                  Bus.validBugRatio = eachBizData[period].validBugRatio
                  Bus.averageBugPerDay = eachBizData[period].averageBugPerDay
                  Bus.$emit('refreshhomepagetestinfoOnes')
                  break
                }
              }
              for (let period in everyBizData) {
                if (data[each].name === '总计') {
                  Bus.transferDelayCnt = everyBizData[period]['timelineStats'].transferDelayCnt
                  Bus.testDelayCnt = everyBizData[period]['timelineStats'].testDelayCnt
                  Bus.launchDelayCnt = everyBizData[period]['timelineStats'].launchDelayCnt
                  Bus.devTestRatio = everyBizData[period]['resourceStats'].devTestRatio
                  Bus.jointDebugRatio = everyBizData[period]['resourceStats'].jointDebugRatio
                  Bus.$emit('refreshhomepagedelayinfoOnes')
                  break
                }
              }
            }
          }
        })
        axios.get(Bus.basicApi + '/online/issuestats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.onlineBugStats = []
          Bus.searchpattern = ''
          Bus.validOnlineBugCnt = 0
          Bus.validFaultCnt = 0
          Bus.averageDuration = ''
          Bus.averageHideDuration = ''
          if (message['data']['status'] === 0) {
            // Bus.$emit('refreshDistribution')
            let temp = message['data']['data']['tableData']
            for (const each in temp) {
              if (temp[each].directionName === '总计') {
                Bus.validOnlineBugCnt = temp[each].validBugCnt
                Bus.validFaultCnt = temp[each].validFaultCnt
                Bus.averageDuration = temp[each].averageDuration
                Bus.averageHideDuration = temp[each].averageHideDuration
                Bus.$emit('refreshhomepageonlineinfoOnes')
                break
              }
            }
          } else {
            // self.$Spin.hide()
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getmarketstatistics (params) {
        let devopsParams = {
          start: params.beginTime,
          end: params.endTime,
          projectList: params.directionList
        }
        axios.post(this.getDomain('env-monitor') + '/api/devops/search/data', devopsParams).then(function (mes) {
          if (mes['data']['status'] === 0) {
            Bus.devopsDetailData = {}
            Bus.devopsSumData = {}
            Bus.devopsDetailData = mes['data']['data']['devops_data']
            Bus.devopsSumData = mes['data']['data']['sum_data']
            Bus.$emit('refreshdevopsDataOnes')
          }
        })
        axios.post(Bus.basicApi + '/analytics/overview/req/ones', params).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.marketstatistics = {}
            Bus.marketstatistics = message['data']['data']
            Bus.$emit('refreshmarketstatisticsOnes')
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getdeliverydata (params) {
        // axios.get(Bus.basicApi + '/homepage/req/deliveryquality', {
        axios.post(Bus.basicApi + '/analytics/homepage/req/delivery/quality', params).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.deliveryquality = {}
            Bus.deliveryquality = message['data']['data']
            Bus.$emit('refreshdeliveryqualityOnes')
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      gethomepagedata (params) {
        let self = this
        axios.post(Bus.basicApi + '/analytics/homepage/req/ones/stats', params).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.homepagedata = {}
            Bus.homepagedata = message['data']['data']
            Bus.$emit('refreshhomepagedataOnes')
            self.$Spin.hide()
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getmarketdetail (params) {
        axios.post(Bus.basicApi + '/analytics/overview/req/ones', params).then(function (message) {
          if (message['data']['status'] === 0) {
            Bus.marketdetail = []
            Bus.marketdetail = message['data']['data']
            Bus.$emit('refreshmarketdetailOnes')
            Bus.$emit('refreshdeliverydataOnes')
          } else {
            alert(message['data']['msg'])
          }
        }).catch(error => {
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitProcessData (params) {
        let self = this
        axios.post(Bus.basicApi + '/analytics/tice/ones/stats', params).then(function (message) {
          Bus.resourceStats = []
          Bus.timelineStats = []
          Bus.evaluationStats = []
          // console.log(message)
          Bus.flag = ''
          Bus.flag = message['data']['data'][0]['pattern']
          if (message['data']['status'] === 0) {
            Bus.$emit('refreshDistributionOnes')
            Bus.processCreatestats = []
            Bus.processCreatestats = message['data']['data']  // 先保存数据，用于wiki导出
            self.getProcessData(message['data']['data'])
            self.getQualityData(message['data']['data'])
          } else if (message['data']['status'] === 1) {
            self.getQualityData(message['data']['data'])
            let alertdata = JSON.parse(message['data']['msg'])
            Bus.processCreatestats = []
            Bus.processCreatestats = message['data']['data']
            Bus.$emit('refreshDistributionOnes')
            Bus.incompletedata = []
            Bus.incompletedata = message['data']['data']
            let testdata = []
            for (let issuekey in alertdata) {
              let invalidIssue = []
              invalidIssue = alertdata[issuekey]
              testdata.push(invalidIssue)
            }
            if (self.$route.params.tab === 'process_data' || self.$route.params.tab === 'quality_data') {
              Bus.$emit('refreshshowincompletedataOnes', testdata)
            } else {
              self.getProcessData(Bus.incompletedata)
            }
          } else {
            self.$Spin.hide()
            alert(message['data']['msg'])
            // }
          }
        }).catch(error => {
          self.$Spin.hide()
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitEfficiencyData (params) {
        let self = this
        axios.get(Bus.basicApi + '/efficiency/stats', {
          params: params,
          timeout: 10000000,
          dataType: 'json'
        }).then(function (message) {
          Bus.rdEfficiencyStats = []
          Bus.qaEfficiencyStats = []
          // Bus.pmEfficiencyStats = []
          // console.log(message)
          Bus.searchpattern = ''
          Bus.searchpattern = message['data']['data'][0]['pattern']
          if (message['data']['status'] === 0) {
            // console.log('metrics', message['data']['data'])
            // Bus.$emit('refreshDistribution')
            Bus.efficiencyCreatestats = []
            Bus.efficiencyCreatestats = message['data']['data']  // 先保存数据，用于wiki导出,人效的导出数据，后面可能使用
            self.getEfficiencyData(message['data']['data'])
          } else {
            // self.$Spin.hide()
            alert(message['data']['msg'])
            // }
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getinitOnlineQualityStats (params) {
        let self = this
        // axios.get(Bus.basicApi + '/online/issuestats/ones', {
        axios.post(Bus.basicApi + '/analytics/online/issue/stats/ones', params).then(function (message) {
          Bus.onlineBugStats = []
          Bus.searchpattern = ''
          Bus.validOnlineBugCnt = 0
          Bus.validFaultCnt = 0
          Bus.averageDuration = ''
          Bus.averageHideDuration = ''
          Bus.searchpattern = message['data']['data']['pattern']
          if (message['data']['status'] === 0) {
            // Bus.$emit('refreshDistribution')
            Bus.onlineBugStats = message['data']['data']  // 先保存数据，用于wiki导出,人效的导出数据，后面可能使用
            let temp = message['data']['data']['tableData']
            for (const each in temp) {
              if (temp[each].directionName === '总计') {
                Bus.validOnlineBugCnt = temp[each].validBugCnt
                Bus.validFaultCnt = temp[each].validFaultCnt
                Bus.averageDuration = temp[each].averageDuration
                Bus.averageHideDuration = temp[each].averageHideDuration
              }
            }
            if (self.$route.query.type === 'CUSTOMED') {
              Bus.$emit('refreshhomepageonlineinfoOnes')
            }
            Bus.$emit('refreshOnlineQualityStatOnes', Bus.onlineBugStats['tableData'].length, self.value6)
          } else {
            // self.$Spin.hide()
            alert(message['data']['msg'])
          }
        }).catch(error => {
          // self.$Spin.hide()
          // console.log(error.response.status)
          if (!(error.response && error.response.status === 400)) {
            self.$Message.info('网络超时')
          }
        })
      },
      getrouterparams () {
        const self = this
        self.display3 = false
        const dict = {
          reqtype: '0',
          req: 0,
          searchtype: '服务端'
        }
        if (self.$route.query.searchtype) {
          self.value6 = self.$route.query.searchtype
          dict.searchtype = self.$route.query.searchtype
        } else {
          self.value6 = '服务端'
          dict.searchtype = '服务端'
        }
        if (self.$route.query.periodType) {
          self.value2 = self.$route.query.periodType
          dict.type = this.$route.query.periodType
        } else {
          self.value2 = 'CUSTOMED'
          dict.type = 'CUSTOMED'
        }
        self.value7 = []
        if (self.$route.query.app) {
          self.value7.push(self.$route.query.app)
          dict.app = self.$route.query.app
        } else {
          self.value7.push('点评')
          dict.app = '点评'
        }
        if (self.$route.query.version) {
          self.value7.push(self.$route.query.version)
          dict.version = self.$route.query.version
        } else {
          dict.version = '10.2'
          self.value7.push('10.2')
        }
        if (self.$route.query.platform) {
          self.value8 = self.$route.query.platform
          dict.platform = self.$route.query.platform
        } else {
          self.value8 = 'iOS'
          dict.platform = 'iOS'
        }
        self.value3 = []
        if (self.$route.query.start) {
          // console.log('start', self.$route.query.start, typeof self.$route.query.start)
          self.value3.push(self.$route.query.start)
          dict.start = self.$route.query.start
        } else {
          const startdate = new Date()
          startdate.setTime(startdate.getTime() - 3600 * 1000 * 24 * 7)
          self.value3.push(startdate.toISOString().substring(0, 10))
          dict.start = startdate.toISOString().substring(0, 10)
        }
        if (self.$route.query.end) {
          self.value3.push(self.$route.query.end)
          dict.end = self.$route.query.end
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.end = new Date().toISOString().substring(0, 10)
        }
        // console.log('value3', self.value3)
        let type = ''
        self.reqtype = []
        if (self.$route.query.reqtype) {
          if (self.$route.query.reqtype === '0') {
            self.reqtype.push('产品需求')
            self.reqtype.push('技术需求')
          } else if (self.$route.query.reqtype === '1') {
            self.reqtype.push('产品需求')
          } else if (self.$route.query.reqtype === '2') {
            self.reqtype.push('技术需求')
          }
          type = self.$route.query.reqtype
        } else {
          self.reqtype.push('产品需求')
          self.reqtype.push('技术需求')
          type = '0'
        }
        dict.reqtype = type
        if (self.$route.params.tab !== 'undefined') {
          if (self.$route.path.indexOf('custom') === -1) {
            router.push({ path: `/homepage/analytics/ones/${self.$route.params.tab}`, query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones/${self.$route.params.tab}`, query: dict })
          }
        } else {
          if (self.$route.path.indexOf('custom') === -1) {
            router.push({ path: '/homepage/analytics/ones', query: dict })
          } else {
            router.push({ path: `/homepage/analytics/custom/${self.$route.params.keyword}/ones`, query: dict })
          }
        }
      },
      getHost: function (key) {
        let self = this
        axios.get(customanalyticsbaseAPI + '/search_single_config?keyword=' + key).then(function (message) {
          if (message.data.status === 0) {
            Bus.basicApi = message.data.data
          } else {
            self.$Modal.error({
              title: '获取路由失败',
              message: message.data.msg
            })
          }
        }).catch(function () {
          self.$Modal.error({
            title: '获取路由失败',
            message: '服务器内部错误'
          })
        })
      },
      // visitchange (data) {
      //   if (data === true) {
      //     this.backupselectOnesDirection = this.selectOnesDirection
      //     this.backupclickparams = this.clickparams
      //   }
      // },
      getinitparams (self) {
        self.display3 = false
        self.value1 = []
        const dir = []
        const dict = {
          reqtype: '0',
          req: 0
        }
        if (self.$route.query.bg) {
          dir.push(self.$route.query.bg)
          dict.bg = this.$route.query.bg
        } else {
          dict.bg = '2'
        }
        if (self.$route.query.bizline) {
          if (self.$route.query.bizline === '0') {
            dir.push(self.$route.query.bizline)
          } else {
            dir.push(parseInt(self.$route.query.bizline, 0))
          }
          dict.bizline = this.$route.query.bizline
        } else {
          dict.bizline = '1'
        }
        if (self.$route.query.group) {
          if (self.$route.query.group === '0') {
            dir.push(self.$route.query.group)
          } else {
            dir.push(parseInt(self.$route.query.group, 0))
          }
          dict.group = this.$route.query.group
        }
        if (self.$route.query.project) {
          if (self.$route.query.project === '0') {
            dir.push(self.$route.query.project)
          } else {
            dir.push(parseInt(self.$route.query.project, 0))
          }
          dict.project = this.$route.query.project
        }
        if (dir.length === 0) {
          dir.push(2)
          dir.push(1)
          dir.push(2)
          dict.bg = '2'
          dict.bizline = '1'
          dict.group = '2'
        }
        self.value1 = dir
        if (self.$route.query.type) {
          self.value2 = self.$route.query.type
          dict.type = this.$route.query.type
        } else {
          self.value2 = 'CUSTOMED'
          dict.type = 'CUSTOMED'
        }
        self.value3 = []
        if (self.$route.query.start) {
          // console.log('start', self.$route.query.start, typeof self.$route.query.start)
          self.value3.push(self.$route.query.start)
          dict.start = self.$route.query.start
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.start = new Date().toISOString().substring(0, 10)
        }
        if (self.$route.query.end) {
          self.value3.push(self.$route.query.end)
          dict.end = self.$route.query.end
        } else {
          self.value3.push(new Date().toISOString().substring(0, 10))
          dict.end = new Date().toISOString().substring(0, 10)
        }
        // console.log('value3', self.value3)
        let type = ''
        self.reqtype = []
        if (self.$route.query.reqtype) {
          if (self.$route.query.reqtype === '0') {
            self.reqtype.push('产品需求')
            self.reqtype.push('技术需求')
          } else if (self.$route.query.reqtype === '1') {
            self.reqtype.push('产品需求')
          } else if (self.$route.query.reqtype === '2') {
            self.reqtype.push('技术需求')
          }
          type = self.$route.query.reqtype
        } else {
          self.reqtype.push('产品需求')
          self.reqtype.push('技术需求')
          type = '0'
        }
        // console.log('type', type)
        dict.reqtype = type
        // console.log('重新读取参数之后', self.value1, self.value2, self.value3, self.reqtype)
        // router.push({ path: '/analytics', query: dict })
      },
      setstarttime (monthNum) {
        const date = new Date()
        const month = date.getMonth()
        const year = date.getFullYear()
        date.setYear(year)
        date.setMonth(month - monthNum)
        return date
      },
      asyncOK (data) {
        let self = this
        $('#' + data)[0].__vue__.exportCsv({
          filename: '异常task'
        })
        if (Bus.incompletedata) {
          // self.pattern = Bus.incompletedata[0]['pattern']
          self.getProcessData(Bus.incompletedata)
          Bus.$emit('refreshDistributionOnes')
        }
      },
      getParentDirection (data) {
        if (data.data.label !== undefined) {
          this.showdirection.push(data.data.label)
        }
        if (data.parent === null) {
        } else {
          this.getParentDirection(data.parent)
        }
      },
      cancelDirConfig () {
        this.directionList.selected = []
        this.directionList.selected = this.backupSelect
      },
      saveDirConfig () {
        // this.selectOnesDirection = ''
        // for (var i = this.showdirection.length - 1; i >= 0; i--) {
        //   if (i !== 0) {
        //     this.selectOnesDirection += this.showdirection[i] + ' / '
        //   } else {
        //     this.selectOnesDirection += this.showdirection[i]
        //   }
        // }
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          name: this.$route.query.name,
          type: this.$route.query.type,
          start: this.$route.query.start,
          end: this.$route.query.end,
          reqtype: this.$route.query.reqtype
        }
        if (this.directionList.selected[0].direction_id) {
          dict.group = this.directionList.selected[0].direction_id
        } else {
          dict.group = '1'
          this.directionList.selected = []
          this.directionList.selected.push(
            {
              direction_id: '1',
              label: '集团',
              value: '集团'
            }
          )
        }
        router.push({ path: `/homepage/analytics/ones/${this.$route.params.tab}`, query: dict })
      },
      cancel () {
        let self = this
        if (Bus.incompletedata) {
          self.pattern = Bus.incompletedata[0]['pattern']
          self.getProcessData(Bus.incompletedata)
          Bus.$emit('refreshDistributionOnes')
        }
      },
      cancel1 () {
      },
      ok () {
        let self = this
        self.$Spin.show()
        let wikidata = {
          processStats: Bus.processCreatestats
        }
        // console.log(wikidata)
        axios.defaults.headers.post['Content-Type'] = 'application/json'
        if (wikidata) {
          axios.post(analyticswikiAPI + '/createstats',
            JSON.stringify(wikidata)
          ).then(function (message) {
            if (message['data']['status'] === 0) {
              let wikiUrl = message['data']['data']
              let show = 'Wiki链接'
              self.$Spin.hide()
              self.$Message.info(
                {
                  render: h => {
                    return h('a', {
                      attrs: {
                        href: wikiUrl,
                        target: '_blank'
                      }
                    }, show)
                  },
                  duration: 120,
                  closable: true
                }
              )
            } else {
              self.$Spin.hide()
              alert(message['data']['msg'])
            }
          }).catch(error => {
            self.$Spin.hide()
            console.log(error)
          })
        } else {
          self.$Spin.hide()
          return []
        }
      }
    },
    mounted: function () {
      Bus.commonDirectionOnesObject = this
      let self = this
      // self.getBizlineDataFromOnes()
      Bus.basicApi = ''
      if (this.$route.path.indexOf('custom') === -1) {
        Bus.basicApi = analyticsbaseonesAPI
      } else {
        self.getHost(self.$route.params.keyword)
      }
      setTimeout(function () {
        // self.getDirectionList()
        // self.getBizlineData(self)
        // self.getClientData()
        self.getreqtype()
        self.getrouterparams()
        self.getDirectionList()
      }, 2000)
      // console.log('dict', dict)
    }
  }
</script>

<style>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }

  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }

  .el-tree {
    color: #586e98;
  }

  /*.el-tree-node__content:hover{*/
    /*background-color: #F08080;*/
  /*}*/

  .el-tree-node:focus > .el-tree-node__content {
    color: #409eff !important;
  }

  .inputcard {
    box-sizing: border-box;
    border: 1px solid #dcdee2;
    background-color: #fff;
    border-radius: 4px;
    height: auto;
    min-height: 32px;
    display: block;
    cursor: pointer;
    text-align: left;
    padding-top: 2px;
    padding-bottom: 2px;
  }

</style>
