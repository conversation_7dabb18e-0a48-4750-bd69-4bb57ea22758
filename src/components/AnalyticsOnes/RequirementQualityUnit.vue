<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <Alert show-icon banner style="margin-top: -5px">
          <template slot="desc">
            <div style="margin-top: 5px;display: flex">a. 整体需求数据分为：
              <Tag color="#19be6b" style="margin-top: -1px">有QA介入需求</Tag>
              <Tag color="#ff9900" style="margin-top: -1px">无QA介入需求</Tag>
            </div>
            <div style="margin-top: 3px">
              b.如何区分是否有QA介入：有提测task则认为该需求有QA介入
            </div>
            <div style="display: flex;margin-top: 5px">
              <div style="padding-bottom: 5px;display: flex">c.说明：
                <div class="onlyleftChart" style="width:30px;margin-top: 2px"></div>
                ： 开发时长
                <div class="onlyrightChart" style="margin-left:5px;width:30px;margin-top: 2px"></div>
                ： 测试时长
              </div>
            </div>
            <div style="display: flex;margin-top: 5px">
              <div style="padding-bottom: 5px;display: flex">d.数据范围：汇总数据和交付数据均为在plus中已发布上线的需求
              </div>
            </div>
          </template>
        </Alert>
        <div>
          <Row type="flex">
            <Col span="24">
            <Timeline style="padding-top: 15px;padding-left: 2px">
              <TimelineItem>
                <Row type="flex">
                  <span>汇总数据</span>
                </Row>
                <Row style="margin-top: 15px">
                  <Table stripe border :columns="customColumn" :data="displayList" no-data-text="未查询到符合条件的数据"></Table>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>需求交付详情:（<span style="color:red">注：开发+测试+PR时长为研发周期 </span> )</span>
                <Row type="flex" style="margin-top: 20px;margin-left:32%;margin-bottom: 5px">
                  <div
                    style="background-color: #8192D6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px;margin-left: 3px">: 需求创建到开始开发;</span>
                  <div
                    style="background-color: #20B2AA;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px;margin-left: 8px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px;margin-left: 3px">: 开发时长;</span>
                  <div
                    style="background-color: #c1405a;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px;margin-left: 8px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px;margin-left: 3px">: 测试时长;</span>
                  <div
                    style="background-color: #2f99c1;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px;margin-left: 8px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px;margin-left: 3px">: PR时长（创建PR到PR合入）;</span>
                  <div
                    style="background-color: #c581d6;height: 10px;border-top-left-radius: 5px;border-bottom-left-radius: 5px;border-top-right-radius: 5px;border-bottom-right-radius: 5px;width: 30px;margin-left: 8px">
                  </div>
                  <span style="margin-top:-3px;margin-right: 8px;margin-left: 3px">: 发布时长（开始发布到发布结束）</span>
                </Row>
                <Row style="margin-top: 10px" :style="{'height': deliveryheight}">
                  <Col span="24">
                  <div id="deliveryspeedchart" style="padding-top:10px; margin-right:5px;"
                       :style='deliveryheight'></div>
                  </Col>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <span>需求交付分布</span>
                <Row style="margin-top: 10px">
                  <Col span="24">
                  <div id="deliverybubblechart" style="padding-top:10px; margin-right:5px;max-height: 3000px"></div>
                  </Col>
                </Row>
              </TimelineItem>
              <TimelineItem>
                <Row type="flex">
                  <span>QA介入需求列表: </span>
                </Row>
                <Row type="flex" style="margin-top: -15px">
                  <Col span="6" v-for="item in prodReqTestByQaIssueOriginList" :key="item">
                  <Card :bordeerror="true" :style="noticeStyle"
                        style="border-left-color: #19be6b; border-left-width: 4px; min-height: 260px">
                    <Row>
                      <div style="font-weight: bolder; font-size: 18px; ">
                        <i class="fa fa-list-ul" style="padding-right: 5px"></i>
                        <a target="_blank" :href=getIssueUrl(item)>{{item}}
                          {{prodReqTestByQaIssueDictOrigin[item].reqSummary}}</a>
                        <Tag v-if="prodReqTestByQaIssueDictOrigin[item].status === '关闭'"
                             style="margin-top: 2px;margin-left: 8px"
                             color="success">{{prodReqTestByQaIssueDictOrigin[item].status}}
                        </Tag>
                        <Tag v-else style="margin-top: 2px;margin-left: 8px" color="warning">
                          {{prodReqTestByQaIssueDictOrigin[item].status}}
                        </Tag>
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                        需求评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].reqReviewCnt}}</span>次<span
                        style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].reqReviewHours}}</span>h,
                        技术评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].techReviewCnt}}</span>次<span
                        style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].techReviewHours}}</span>h
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <Row type="flex">
                          <div v-if="prodReqTestByQaIssueDictOrigin[item].planTransferTime !== ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                            计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].planTransferTime}}</span>
                          </div>
                          <div v-if="prodReqTestByQaIssueDictOrigin[item].planTransferTime === ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                            计划提测时间: <span
                            style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planTransferTime, prodReqTestByQaIssueDictOrigin[item].actualTransferTime) === 1">
                            ; 实际提测时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planTransferTime, prodReqTestByQaIssueDictOrigin[item].actualTransferTime) === -1">
                            ; 实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planTransferTime, prodReqTestByQaIssueDictOrigin[item].actualTransferTime) === -2">
                            ; 实际提测时间: <span
                            style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planTransferTime, prodReqTestByQaIssueDictOrigin[item].actualTransferTime) === 0">
                            ; 实际提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualTransferTime}}</span>
                          </div>
                        </Row>
                        <!--<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>-->
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <Row type="flex">
                          <div v-if="prodReqTestByQaIssueDictOrigin[item].planReleaseTime !== ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                            计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].planReleaseTime}}</span>
                          </div>
                          <div v-if="prodReqTestByQaIssueDictOrigin[item].planReleaseTime === ''">
                            <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                            计划上线时间: <span
                            style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planReleaseTime, prodReqTestByQaIssueDictOrigin[item].actualReleaseTime) === 1">
                            ; 实际上线时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planReleaseTime, prodReqTestByQaIssueDictOrigin[item].actualReleaseTime) === -1">
                            ; 实际上线时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planReleaseTime, prodReqTestByQaIssueDictOrigin[item].actualReleaseTime) === -2">
                            ; 实际上线时间: <span
                            style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>
                          </div>
                          <div
                            v-if="comparetime(prodReqTestByQaIssueDictOrigin[item].planReleaseTime, prodReqTestByQaIssueDictOrigin[item].actualReleaseTime) === 0">
                            ; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{prodReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>
                          </div>
                        </Row>
                        <!--<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>-->
                      </div>
                    </Row>
                    <Row style="padding-top: 15px">
                      <div style="font-weight: bolder; font-size: 14px">
                        <div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>
                        交付速度(PD):
                      </div>
                    </Row>
                    <Row style="padding-top: 15px; padding-left: 20px">
                      <div
                        v-if="parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) === 0 && parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD) !== 0"
                        class="onlyrightChart" :style="{width: 100 + '%'}">
                        <div style="font-size: 12px;margin-top: -3px">
                          {{parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD)}}
                        </div>
                      </div>
                      <div
                        v-if="parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) !== 0 && parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD) === 0"
                        class="onlyleftChart" :style="{width: 100 + '%'}">
                        <div style="font-size: 12px;margin-top: -3px">
                          {{parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD)}}
                        </div>
                      </div>
                      <Row
                        v-if="parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) !== 0 && parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD) !== 0"
                        type="flex">
                        <div class="leftChart"
                             :style="{width: parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) / (parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) + parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD)) * 100 + '%'}">
                          <div style="font-size: 12px;margin-top: -3px">
                            {{parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD)}}
                          </div>
                        </div>
                        <div class="rightChart"
                             :style="{width: parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD) / (parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) + parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD)) * 100 + '%'}">
                          <div style="font-size: 12px;margin-top: -3px">
                            {{parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD)}}
                          </div>
                        </div>
                      </Row>
                      <div
                        v-if="parseFloat(prodReqTestByQaIssueDictOrigin[item].developPD) === 0 && parseFloat(prodReqTestByQaIssueDictOrigin[item].testPD) === 0"
                        class="noneChart" :style="{width: 100 + '%'}">
                        <div style="margin-top: -5px;font-size: 10px;font-weight: bolder;margin-left: 5px">无</div>
                      </div>
                    </Row>
                  </Card>
                  </Col>
                </Row>
                <Row style="text-align: right; padding-top: 15px">
                  <Page :total="backupProdReqTestByQaOriginList.length" :page-size='pageSize'
                        @on-change="changeProdTestByQaPage" :current='currentProdReqTestByQaPage'></Page>
                </Row>
              </TimelineItem>
              <!--<TimelineItem>-->
              <!--<Row type="flex">-->
              <!--<span>无QA介入: </span>-->
              <!--</Row>-->
              <!--<Row type="flex" style="margin-top: -15px">-->
              <!--<Col span="6" v-for="item in techReqTestByQaIssueOriginList" :key="item">-->
              <!--<Card :bordeerror="true" :style="noticeStyle"-->
              <!--style="border-left-color: #ff9900; border-left-width: 4px; min-height: 230px">-->
              <!--<Row>-->
              <!--<div style="font-weight: bolder; font-size: 18px; ">-->
              <!--<i class="fa fa-list-ul" style="padding-right: 5px"></i>-->
              <!--<a target="_blank" :href=getIssueUrl(item)>{{item}} {{techReqTestByQaIssueDictOrigin[item].reqSummary}}</a>-->
              <!--<Tag v-if="techReqTestByQaIssueDictOrigin[item].status === '关闭'" style="margin-top: 2px;margin-left: 8px"-->
              <!--color="success">{{techReqTestByQaIssueDictOrigin[item].status}}-->
              <!--</Tag>-->
              <!--<Tag v-else style="margin-top: 2px;margin-left: 8px" color="warning">-->
              <!--{{techReqTestByQaIssueDictOrigin[item].status}}-->
              <!--</Tag>-->
              <!--</div>-->
              <!--</Row>-->
              <!--<Row style="padding-top: 15px">-->
              <!--<div style="font-weight: bolder; font-size: 14px">-->
              <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
              <!--技术评审: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].techReviewCnt}}</span>-->
              <!--次<span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].techReviewHours}}</span>-->
              <!--h-->
              <!--</div>-->
              <!--</Row>-->
              <!--<Row style="padding-top: 15px">-->
              <!--<div style="font-weight: bolder; font-size: 14px">-->
              <!--<Row type="flex">-->
              <!--<div v-if="techReqTestByQaIssueDictOrigin[item].planReleaseTime !== ''">-->
              <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
              <!--计划上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].planReleaseTime}}</span>-->
              <!--</div>-->
              <!--<div v-if="techReqTestByQaIssueDictOrigin[item].planReleaseTime === ''">-->
              <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
              <!--计划上线时间: <span-->
              <!--style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>-->
              <!--</div>-->
              <!--<div-->
              <!--v-if="comparetime(techReqTestByQaIssueDictOrigin[item].planReleaseTime, techReqTestByQaIssueDictOrigin[item].actualReleaseTime) === 1">-->
              <!--; 实际上线时间: <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>-->
              <!--</div>-->
              <!--<div-->
              <!--v-if="comparetime(techReqTestByQaIssueDictOrigin[item].planReleaseTime, techReqTestByQaIssueDictOrigin[item].actualReleaseTime) === -1">-->
              <!--; 实际上线时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>-->
              <!--</div>-->
              <!--<div-->
              <!--v-if="comparetime(techReqTestByQaIssueDictOrigin[item].planReleaseTime, techReqTestByQaIssueDictOrigin[item].actualReleaseTime) === -2">-->
              <!--; 实际上线时间: <span-->
              <!--style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">无</span>-->
              <!--</div>-->
              <!--<div-->
              <!--v-if="comparetime(techReqTestByQaIssueDictOrigin[item].planReleaseTime, techReqTestByQaIssueDictOrigin[item].actualReleaseTime) === 0">-->
              <!--; 实际上线时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{techReqTestByQaIssueDictOrigin[item].actualReleaseTime}}</span>-->
              <!--</div>-->
              <!--</Row>-->
              <!--&lt;!&ndash;<div v-if="jiraIssueDictOrigin[item].planTransferTime !== 'null' && jiraIssueDictOrigin[item].actualTransferTime !== 'null'" class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>计划提测时间: <span style="color: rgb(45, 140, 240); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].planTransferTime}}</span> ;实际提测时间: <span style="color: rgb(255, 0, 0); font-weight: bolder; padding-left: 5px">{{jiraIssueDictOrigin[item].actualTransferTime}}</span>&ndash;&gt;-->
              <!--</div>-->
              <!--</Row>-->
              <!--<Row style="padding-top: 15px">-->
              <!--<div style="font-weight: bolder; font-size: 14px">-->
              <!--<div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div>-->
              <!--交付速度(PD):-->
              <!--</div>-->
              <!--</Row>-->
              <!--<Row style="padding-top: 15px; padding-left: 20px">-->
              <!--&lt;!&ndash;<div style="font-weight: bolder; font-size: 14px;">&ndash;&gt;-->
              <!--&lt;!&ndash;<div style="background-color: #2d8cf0"></div>开发时长:  <span style="color: #ff0000; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 联调时长:  <span style="color: #19be6b; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 测试时长:  <span style="color: #2d8cf0; font-weight: bolder; padding-left: 5px">{{}}</span> PD, 上线时长:  <span style="color: #2d8cf0; font-weight: bolder; padding-left: 5px">{{}}</span> PD。&ndash;&gt;-->
              <!--&lt;!&ndash;</div>&ndash;&gt;-->
              <!--&lt;!&ndash;<Tag v-if="getStageStatus(item, unit) === -1" >{{unit}}</Tag>&ndash;&gt;-->
              <!--&lt;!&ndash;<Tag v-if="getStageStatus(item, unit) === 0" color="error">{{unit}}</Tag>&ndash;&gt;-->
              <!--<Tag v-if="techReqTestByQaIssueDictOrigin[item].developPD" color="success">-->
              <!--开发时长:{{techReqTestByQaIssueDictOrigin[item].developPD}}-->
              <!--</Tag>-->
              <!--<Tag v-if="techReqTestByQaIssueDictOrigin[item].testPD" color="success">-->
              <!--测试时长:{{techReqTestByQaIssueDictOrigin[item].testPD}}-->
              <!--</Tag>-->
              <!--</Row>-->
              <!--</Card>-->
              <!--</Col>-->
              <!--</Row>-->
              <!--<Row style="text-align: right; padding-top: 15px">-->
              <!--<Page :total="backupTechReqTestByQaOriginList.length" :page-size="pageSize" @on-change="changeTechTestByQaPage" :current='currentTechReqTestByQaPage'></Page>-->
              <!--</Row>-->
              <!--</TimelineItem>-->
            </Timeline>
            </Col>
          </Row>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>


<script>
  import {Bus} from '@/global/bus'
  import Highcharts from 'highcharts/highstock'
  import Xrange from 'highcharts/modules/xrange'
  import HighchartsMore from 'highcharts/highcharts-more'

  Xrange(Highcharts)
  HighchartsMore(Highcharts)

  let noticeStyle = {
    'marginLeft': '2.5%',
    'marginRight': '2.5%',
    'marginTop': '30px'
  }

  Bus.$on('refreshmarketstatisticsOnes', function () {
    Bus.RequirementQualityOnesUnit.ReqStatisticsAll = Bus.marketstatistics
    Bus.RequirementQualityOnesUnit.handlestructuredata(Bus.marketstatistics)
  })

  Bus.$on('refreshdevopsDataOnes', function () {
    Bus.RequirementQualityOnesUnit.handleReqdeliverydata(Bus.devopsDetailData)
    Bus.RequirementQualityOnesUnit.handleDevopsSumData(Bus.devopsSumData)
  })

  // Bus.$on('refreshmarketdetail', function () {
  //   Bus.RequirementQualityOnesUnit.handlestructuredata(Bus.marketdetail)
  // })

  export default {
    name: 'requirement-quality-unit-ones',
    data: function () {
      return {
        pageSize: 20,
        currentProdReqPage: 1,
        currentProdReqTestByQaPage: 1,
        currentTechReqPage: 1,
        currentTechReqTestByQaPage: 1,
        noticeStyle: noticeStyle,
        deliveryheight: {minHeight: '500px'},
        deliveryReq: [],
        deliverydata: [],
        deliverybubble: [],
        backupdisplaylist: [],
        ReqStatisticsAll: this.getreqStatisticsAllData(),
        marketdetail: [],
        filterStatus: [],
        backupOriginList: [],
        filterType: [],
        prodReqIssueOriginList: [],
        prodReqIssueDictOrigin: {},
        techReqIssueOriginList: [],
        techReqIssueDictOrigin: {},
        prodReqTestByQaIssueOriginList: [],
        prodReqTestByQaIssueDictOrigin: {},
        techReqTestByQaIssueOriginList: [],
        techReqTestByQaIssueDictOrigin: {},
        backupProdReqOriginList: [],
        backupProdReqTestByQaOriginList: [],
        backupTechReqOriginList: [],
        backupTechReqTestByQaOriginList: [],
        filterReqStatusList: [
          '评审中', '开发中', '测试中', '测试完成待上线', '已上线', '效果评估完成'
        ],
        filterReqTypeList: [
          '产品需求', '技术需求'
        ],
        jiraIssueDictOrigin: {},
        displayList: [],
        customColumn: [
          {
            title: '方向',
            key: 'direction',
            align: 'center'
          },
          {
            title: '效率',
            key: 'speed',
            align: 'center',
            children: [
              {
                title: '需求响应周期',
                align: 'center',
                key: 'response_speed',
                children: [
                  {
                    title: '研发周期',
                    align: 'center',
                    key: 'flow_time'
                  },
                  {
                    title: '交付周期',
                    align: 'center',
                    key: 'lead_time'
                  }
                ]
              },
              {
                title: '持续发布能力',
                align: 'center',
                key: 'delivery_speed',
                children: [
                  {
                    title: '发布时长',
                    align: 'center',
                    key: 'delivery_time'
                  },
                  {
                    title: '发布频率',
                    align: 'center',
                    key: 'delivery_frequency'
                  }
                ]
              },
              {
                title: '交付吞吐率',
                align: 'center',
                key: 'delivery_req_speed',
                children: [
                  {
                    title: '单位时间交付需求数',
                    align: 'center',
                    key: 'delivery_throughput'
                  }
                ]
              }
            ]
          },
          {
            title: '质量',
            key: 'quality',
            align: 'center',
            children: [
              {
                title: '过程质量',
                align: 'center',
                key: 'process_quality',
                children: [
                  {
                    title: '单位时间bug率',
                    align: 'center',
                    key: 'bug_rate'
                  },
                  {
                    title: '千行代码改动bug率',
                    align: 'center',
                    key: 'thousand_change_bug_rate'
                  }
                ]
              },
              {
                title: '交付质量',
                align: 'center',
                key: 'delivery_quality',
                children: [
                  {
                    title: 'hotfix合入次数',
                    align: 'center',
                    key: 'hotfix_merge'
                  },
                  {
                    title: '发布回滚率',
                    align: 'center',
                    key: 'roll_back'
                  }
                ]
              }
            ]
          },
          {
            title: '资源',
            key: 'resource',
            align: 'center',
            children: [
              {
                title: '代码维度',
                align: 'center',
                key: 'code',
                children: [
                  {
                    title: '覆盖仓库数',
                    align: 'center',
                    key: 'repo'
                  },
                  {
                    title: '增加代码行数',
                    align: 'center',
                    key: 'add_code_line'
                  },
                  {
                    title: '删除代码行数',
                    align: 'center',
                    key: 'delete_code_line'
                  }
                ]
              }
            ]
          }
        ]
      }
    },
    methods: {
      splittime: function (data) {
        try {
          let temp = data.split(' ')
          let result1 = temp[0].split('-')
          let result2 = temp[1].split(':')
          let result = result1.concat(result2)
          return result
        } catch (e) {
          let temp = data.split(' ')
          let result1 = temp[0].split('-')
          let result2 = ['00', '00', '00']
          let result = result1.concat(result2)
          return result
        }
      },
      comparetime: function (plantime, actualtime) {
        let start = []
        let end = []
        let result = -3
        if (plantime !== '' && actualtime !== '') {
          start = plantime.split('-')
          var startdate = new Date(start[0], parseInt(start[1] - 1), start[2])
          end = actualtime.split('-')
          var enddate = new Date(end[0], parseInt(end[1] - 1), end[2])
          if (startdate > enddate) {
            result = 1
          } else if (startdate < enddate) {
            result = -1
          } else {
            result = 0
          }
        }
        if (!actualtime) {
          result = -2
        }
        return result
      },
      getIssueUrl: function (item) {
        return '/req-info/' + item + '?start=' + this.$route.query.start + '&end=' + this.$route.query.end
      },
      handleDevopsSumData: function (data) {
        this.displayList = []
        for (const each in data) {
          let temp = {
            direction: each,
            flow_time: data[each].flow_time,
            lead_time: data[each].lead_time,
            delivery_time: data[each].delivery_lead_time,
            delivery_frequency: data[each].delivery_frequency,
            delivery_throughput: data[each].delivery_throughput,
            bug_rate: data[each].bug_rate_eve_time,
            thousand_change_bug_rate: data[each].thousand_code_chenge_bug_rate,
            hotfix_merge: data[each].hotfix_merge,
            roll_back: data[each].roll_back_rate,
            repo: data[each].repos,
            add_code_line: data[each].add_code_line,
            delete_code_line: data[each].delete_code_line
          }
          this.displayList.push(temp)
        }
      },
      deliverybubblechart: function () {
        let self = this
        Highcharts.chart('deliverybubblechart', {
          chart: {
            type: 'bubble',
            zoomType: 'xy'
          },
          title: {
            text: '需求创建到上线交付周期'
          },
          subtitle: {
            text: '横坐标代表需求交付时间，纵坐标为需求交付耗费时间，点击可查看具体需求'
          },
          tooltip: {
            enabled: true,
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            // '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>交付时间 :&nbsp</th><td>{point.time}</td></tr>' +
            '<tr><th>交付周期 :&nbsp</th><td>{point.y}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open('/req-info/' + event.point.key + '?start=' + self.$route.query.start + '&end=' + self.$route.query.end)
                }
              }
            }
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '交付周期（PD）'
            },
            labels: {
              format: '{value} PD'
            },
            // plotLines: [{
            //   color: 'red',
            //   dashStyle: 'longdashdot',
            //   value: this.reqstatistics.averageDeliverPD,
            //   width: 2,
            //   label: {
            //     text: '平均:' + this.reqstatistics.averageDeliverPD,
            //     style: {
            //       color: 'red',
            //       fontWeight: 'bold'
            //     },
            //     align: 'left',
            //     x: -50
            //   }
            // }],
            min: 0
          },
          // tooltip: {
          //   dateTimeLabelFormats: {
          //     day: '%Y-%m-%d'
          //   }
          // },
          series: [{
            name: '交付分布图',
            data: this.deliverybubble
          }],
          credits: {
            enabled: false
          }
        })
      },
      refreshHeight: function (data) {
        const self = this
        let height = data * 15 + 25 > 500 ? data * 15 + 25 : 500
        this.deliveryheight = {minHeight: height + 'px'}
        setTimeout(function () {
          self.deliveryspeedhighchart()
          self.deliverybubblechart()
        }, 100)
      },
      deliveryspeedhighchart: function () {
        let self = this
        Highcharts.chart('deliveryspeedchart', {
          chart: {
            type: 'xrange'
          },
          title: {
            text: ''
          },
          xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
              week: '%Y-%m-%d'
            }
          },
          yAxis: {
            title: {
              text: '需求key ( 交付周期/PD )'
            },
            categories: this.deliveryReq,
            reversed: true
          },
          legend: {
            enabled: true
          },
          tooltip: {
            dateTimeLabelFormats: {
              day: '%Y-%m-%d'
            },
            useHTML: true,
            headerFormat: '<table>',
            pointFormat: '<tr><th colspan="2">{point.key}</th></tr>' +
            '<tr><th>需求名称 :&nbsp</th><td>{point.name}</td></tr>' +
            '<tr><th>{point.period} :&nbsp</th><td>{point.starttime}~{point.endtime}</td></tr>' +
            '<tr><th>需求周期 :</th><td>{point.z}PD</td></tr>',
            footerFormat: '</table>',
            followPointer: true
          },
          plotOptions: {
            series: {
              turboThreshold: 0,
              dataLabels: {
                enabled: true,
                format: '{point.key}'
              },
              events: {
                click: function (event) {
                  window.open('/req-info/' + event.point.key + '?start=' + self.$route.query.start + '&end=' + self.$route.query.end)
                }
              }
            }
          },
          // colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: [{
            name: '需求创建到上线交付时间',
            pointWidth: 8,
            data: this.deliverydata,
            dataLabels: {
              enabled: false
            }
          }],
          credits: {
            enabled: false
          }
        })
      },
      handleReqdeliverydata: function (initdata) {
        const self = this
        this.deliveryReq = []
        this.deliverydata = []
        this.deliverybubble = []
        this.deliveryspeedhighchart()
        this.deliverybubblechart()
        let num = 0
        for (const direction in initdata) {
          for (const month in initdata[direction]) {
            let data = initdata[direction][month]
            for (const each in data) {
              let createTime, startDevTime, startTestTime, endTestTime, plusEndTime, deliveryTime
              if (data[each].req_created_at && data[each].plus_ended_at) {
                createTime = this.splittime(data[each].req_created_at)
                plusEndTime = this.splittime(data[each].plus_ended_at)
                if (data[each].dev_started_at) {
                  startDevTime = this.splittime(data[each].dev_started_at)
                  this.deliverydata.push({
                    x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2], createTime[3], createTime[4], createTime[5]),
                    x2: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5]),
                    y: num,
                    // name: data[each].reqSummary,
                    key: data[each].req_key,
                    period: '创建到开始开发',
                    z: parseFloat(data[each].lead_time).toFixed(2),
                    starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2], createTime[3], createTime[4], createTime[5])),
                    endtime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5])),
                    color: '#8192D6'
                  })
                  if (data[each].test_started_at) {
                    startTestTime = this.splittime(data[each].test_started_at)
                    this.deliverydata.push({
                      x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5]),
                      x2: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5]),
                      y: num,
                      // name: data[each].reqSummary,
                      key: data[each].req_key,
                      period: '开始开发到开始测试',
                      z: parseFloat(data[each].lead_time).toFixed(2),
                      starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5])),
                      endtime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5])),
                      color: '#20B2AA'
                    })
                    if (data[each].test_passed_at) {
                      endTestTime = this.splittime(data[each].test_passed_at)
                      this.deliverydata.push({
                        x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5]),
                        x2: Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2], endTestTime[3], endTestTime[4], endTestTime[5]),
                        y: num,
                        key: data[each].req_key,
                        period: '开始测试到测试完成',
                        z: parseFloat(data[each].lead_time).toFixed(2),
                        starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5])),
                        endtime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2], endTestTime[3], endTestTime[4], endTestTime[5])),
                        color: '#c1405a'
                      })
                    } else if (data[each].pr_created_at) {
                      let prStartTime = this.splittime(data[each].pr_created_at)
                      this.deliverydata.push({
                        x: Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5]),
                        x2: Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5]),
                        y: num,
                        key: data[each].req_key,
                        period: '开始测试到PR创建',
                        z: parseFloat(data[each].lead_time).toFixed(2),
                        starttime: this.handlexAxis(Date.UTC(startTestTime[0], startTestTime[1] - 1, startTestTime[2], startTestTime[3], startTestTime[4], startTestTime[5])),
                        endtime: this.handlexAxis(Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5])),
                        color: '#c1405a'
                      })
                    }
                  } else if (data[each].pr_created_at) {
                    let prStartTime = this.splittime(data[each].pr_created_at)
                    this.deliverydata.push({
                      x: Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5]),
                      x2: Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5]),
                      y: num,
                      key: data[each].req_key,
                      period: '开始开发到PR创建',
                      z: parseFloat(data[each].lead_time).toFixed(2),
                      starttime: this.handlexAxis(Date.UTC(startDevTime[0], startDevTime[1] - 1, startDevTime[2], startDevTime[3], startDevTime[4], startDevTime[5])),
                      endtime: this.handlexAxis(Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5])),
                      color: '#20B2AA'
                    })
                  }
                  if (data[each].pr_created_at && data[each].pr_merged_at) {
                    let prStartTime = this.splittime(data[each].pr_created_at)
                    let prEndTime = this.splittime(data[each].pr_merged_at)
                    this.deliverydata.push({
                      x: Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5]),
                      x2: Date.UTC(prEndTime[0], prEndTime[1] - 1, prEndTime[2], prEndTime[3], prEndTime[4], prEndTime[5]),
                      y: num,
                      key: data[each].req_key,
                      period: '创建PR到PR合入主分支',
                      z: parseFloat(data[each].lead_time).toFixed(2),
                      starttime: this.handlexAxis(Date.UTC(prStartTime[0], prStartTime[1] - 1, prStartTime[2], prStartTime[3], prStartTime[4], prStartTime[5])),
                      endtime: this.handlexAxis(Date.UTC(prEndTime[0], prEndTime[1] - 1, prEndTime[2], prEndTime[3], prEndTime[4], prEndTime[5])),
                      color: '#2f99c1'
                    })
                  }
                  if (data[each].plus_started_at) {
                    let plusStartTime = this.splittime(data[each].plus_started_at)
                    this.deliverydata.push({
                      x: Date.UTC(plusStartTime[0], plusStartTime[1] - 1, plusStartTime[2], plusStartTime[3], plusStartTime[4], plusStartTime[5]),
                      x2: Date.UTC(plusEndTime[0], plusEndTime[1] - 1, plusEndTime[2], plusEndTime[3], plusEndTime[4], plusEndTime[5]),
                      y: num,
                      key: data[each].req_key,
                      period: '开始发布到发布完成',
                      z: parseFloat(data[each].lead_time).toFixed(2),
                      starttime: this.handlexAxis(Date.UTC(endTestTime[0], endTestTime[1] - 1, endTestTime[2], endTestTime[3], endTestTime[4], endTestTime[5])),
                      endtime: this.handlexAxis(Date.UTC(plusEndTime[0], plusEndTime[1] - 1, plusEndTime[2], plusEndTime[3], plusEndTime[4], plusEndTime[5])),
                      color: '#c581d6'
                    })
                  }
                } else {
                  this.deliverydata.push({
                    x: Date.UTC(createTime[0], createTime[1] - 1, createTime[2], createTime[3], createTime[4], createTime[5]),
                    x2: Date.UTC(plusEndTime[0], plusEndTime[1] - 1, plusEndTime[2], plusEndTime[3], plusEndTime[4], plusEndTime[5]),
                    y: num,
                    key: data[each].req_key,
                    period: '创建到上线',
                    z: parseFloat(data[each].lead_time).toFixed(2),
                    starttime: this.handlexAxis(Date.UTC(createTime[0], createTime[1] - 1, createTime[2], createTime[3], createTime[4], createTime[5])),
                    endtime: this.handlexAxis(Date.UTC(plusEndTime[0], plusEndTime[1] - 1, plusEndTime[2], plusEndTime[3], plusEndTime[4], plusEndTime[5])),
                    color: '#8192D6'
                  })
                }
                this.deliveryReq.push(`${data[each].req_key}(${data[each].lead_time}PD)`)
                num += 1
              }
              if (data[each].plus_ended_at) {
                deliveryTime = this.splittime(data[each].plus_ended_at)
                this.deliverybubble.push({
                  x: Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2], deliveryTime[3], deliveryTime[4], deliveryTime[5]),
                  y: data[each].lead_time,
                  z: data[each].lead_time,
                  key: data[each].req_key,
                  time: this.handlexAxis(Date.UTC(deliveryTime[0], deliveryTime[1] - 1, deliveryTime[2], deliveryTime[3], deliveryTime[4], deliveryTime[5]))
                })
              }
            }
          }
        }
        setTimeout(function () {
          self.refreshHeight(self.deliveryReq.length)
        }, 500)
      },
      handlexAxis (time) {
        var date = new Date(time)
        return date.getFullYear() + '-' + parseInt(date.getMonth() + 1) + '-' + date.getDate()
      },
      getreqStatisticsAllData: function () {
        let data = {
          reqStatusCount: {
            createdCnt: 0,
            inReviewCnt: 0,
            developingCnt: 0,
            transferedCnt: 0,
            testingCnt: 0,
            toBeReleasedCnt: 0,
            releasedCnt: 0,
            closedCnt: 0,
            averageDeliverPD: 0
          }
        }
        return data
      },
      setfilterreqstatus: function (status) {
        let result = []
        for (let each in this.jiraIssueDictOrigin) {
          if (this.jiraIssueDictOrigin[each].status === status) {
            result.push(each)
          }
        }
        return result
      },
      handlestructuredata: function (data) {
        this.prodReqIssueOriginList = []
        this.prodReqIssueDictOrigin = {}
        this.techReqIssueOriginList = []
        this.techReqIssueDictOrigin = {}
        this.prodReqTestByQaIssueOriginList = []
        this.prodReqTestByQaIssueDictOrigin = {}
        this.techReqTestByQaIssueOriginList = []
        this.techReqTestByQaIssueDictOrigin = {}
        this.backupProdReqOriginList = []
        this.backupProdReqTestByQaOriginList = []
        this.backupTechReqOriginList = []
        this.backupTechReqTestByQaOriginList = []
        if (data) {
          for (const each of data['prodReqTestByQaDetails']) {
            if (this.prodReqTestByQaIssueOriginList.indexOf(each.reqKey) === -1) {
              this.prodReqTestByQaIssueOriginList.push(each.reqKey)
              this.prodReqTestByQaIssueDictOrigin[each.reqKey] = each
            }
          }
          for (const each of data['prodReqDetails']) {
            if (this.prodReqIssueOriginList.indexOf(each.reqKey) === -1) {
              this.prodReqIssueOriginList.push(each.reqKey)
              this.prodReqIssueDictOrigin[each.reqKey] = each
            }
          }
          for (const each of data['techReqTestByQaDetails']) {
            if (this.techReqTestByQaIssueOriginList.indexOf(each.reqKey) === -1) {
              this.techReqTestByQaIssueOriginList.push(each.reqKey)
              this.techReqTestByQaIssueDictOrigin[each.reqKey] = each
            }
          }
          for (const each of data['techReqDetails']) {
            if (this.techReqIssueOriginList.indexOf(each.reqKey) === -1) {
              this.techReqIssueOriginList.push(each.reqKey)
              this.techReqIssueDictOrigin[each.reqKey] = each
            }
          }
          this.backupProdReqOriginList = this.prodReqIssueOriginList
          this.backupProdReqTestByQaOriginList = this.prodReqTestByQaIssueOriginList
          this.backupTechReqOriginList = this.techReqIssueOriginList
          this.backupTechReqTestByQaOriginList = this.techReqTestByQaIssueOriginList
          this.prodReqIssueOriginList = this.changeCurrentPage(this.prodReqIssueOriginList, this.currentProdReqPage)
          this.prodReqTestByQaIssueOriginList = this.changeCurrentPage(this.prodReqTestByQaIssueOriginList, this.currentProdReqTestByQaPage)
          this.techReqIssueOriginList = this.changeCurrentPage(this.techReqIssueOriginList, this.currentTechReqPage)
          this.techReqTestByQaIssueOriginList = this.changeCurrentPage(this.techReqTestByQaIssueOriginList, this.currentTechReqTestByQaPage)
        }
      },
      filterReq: function () {
        let originDisplay = []
        let reqTypeSet = []
        let reqStatusSet = []
        if (this.filterStatus.length === 0 && this.filterType.length === 0) {
          originDisplay = this.backupOriginList
        } else {
          if (this.filterType.length === 0) {
            reqTypeSet = this.backupOriginList
          } else {
            for (let type of this.filterType) {
              reqTypeSet = this.getUnion(reqTypeSet, this.setfilterreqtype(type))
            }
          }
          if (this.filterStatus.length === 0) {
            reqStatusSet = this.backupOriginList
          } else {
            for (let status of this.filterStatus) {
              reqStatusSet = this.getUnion(reqStatusSet, this.setfilterreqstatus(status))
            }
          }
          originDisplay = this.getIntersection(reqTypeSet, reqStatusSet)
        }
        this.jiraIssueOriginList = originDisplay
        this.changePage(1)
        // this.changeCurrentPage(this.jiraIssueOriginList)
      },
      getUnion: function (a, b) {
        // 并集
        return Array.from(new Set(a.concat(b)))
      },
      getIntersection: function (a, b) {
        // 交集
        let bSet = new Set(b)
        return Array.from(new Set(a.filter(v => bSet.has(v))))
      },
      getDifference: function (a, b) {
        // 差集
        let aSet = new Set(a)
        Array.from(new Set(a.concat(b).filter(v => !aSet.has(v) || !b.has(v))))
      },
      changeProdTestByQaPage: function (page) {
        this.currentProdReqTestByQaPage = page
        this.prodReqTestByQaIssueOriginList = this.changeCurrentPage(this.backupProdReqTestByQaOriginList, page)
      },
      changeTechTestByQaPage: function (page) {
        this.currentTechReqTestByQaPage = page
        this.techReqTestByQaIssueOriginList = this.changeCurrentPage(this.backupTechReqTestByQaOriginList, page)
      },
      changeProdPage: function (page) {
        this.currentProdReqPage = page
        this.prodReqIssueOriginList = this.changeCurrentPage(this.backupProdReqOriginList, page)
      },
      changeTechPage: function (page) {
        this.currentTechReqPage = page
        this.techReqIssueOriginList = this.changeCurrentPage(this.backupTechReqOriginList, page)
      },
      changePageSize: function (pageSize) {
        this.pageSize = pageSize
        this.changeCurrentPage(this.jiraIssueOriginList)
      },
      changeCurrentPage: function (data, currentpage) {
        let currentPage = currentpage
        let pageSize = this.pageSize
        let changedata = []
        let count = data.length > pageSize * currentPage ? pageSize * currentPage : data.length
        for (let i = (currentPage - 1) * pageSize; i < count; i++) {
          changedata.push(data[i])
        }
        return changedata
      }
    },
    mounted: function () {
      Bus.RequirementQualityOnesUnit = this
    }
  }
</script>

<style scoped>
  .reqChart {
    background-color: #19be6b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .techChart {
    background-color: #ff9900;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 7.5px;
    border-bottom-left-radius: 7.5px;
    border-top-right-radius: 7.5px;
    border-bottom-right-radius: 7.5px
  }

  .leftChart {
    background-color: #2f99c1;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px
  }

  .rightChart {
    background-color: #e8488b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .onlyleftChart {
    background-color: #2f99c1;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }

  .onlyrightChart {
    background-color: #e8488b;
    height: 15px;
    margin: 0;
    padding-top: 3px;
    text-align: center;
    color: #ffffff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px
  }
</style>
