<template>
  <div style="text-align: center"></div>
</template>

<script>
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  import Sankey from 'highcharts/modules/sankey'

  HighchartsMore(Highcharts)
  Sankey(Highcharts)
  export default {
    name: 'common-chart',
    props: {
      width: {
        type: [String, Number],
        default: 300
      },
      height: {
        type: [String, Number],
        default: 300
      },
      options: {
        type: Object,
        default: function () {
          return {}
        }
      },
      charttype: {
        type: String,
        default: ''
      },
      data: {
        type: Array,
        default: function () {
          return []
        }
      }
    },
    data: function () {
      return {
        chart: {}
      }
    },
    methods: {
      init: function (type) {
        let self = this
        var options
        if (typeof self.width === 'number') {
          self.$el.style.width = self.width.toString() + 'px'
        } else {
          self.$el.style.width = self.width
        }
        if (typeof self.height === 'number') {
          self.$el.style.height = self.height.toString() + 'px'
        } else {
          self.$el.style.height = self.height
        }
        if (JSON.stringify(self.options) !== '{}') {
          options = self.options
        } else {
          options = self.getChartOptions()
        }
        if (type === 'init') {
          self.chart = new Highcharts.Chart(self.$el, options)
        } else {
          setTimeout(function () {
            self.chart.destroy()
            options.series = self.data
            self.chart = new Highcharts.Chart(self.$el, options)
          }, Math.random() * 1000)
        }
      },
      getChartOptions: function () {
        let colorSet = ['#c581d6', '#20B2AA', '#FFA500', '#8192D6', '#c1405a', '#392EEC', '#8FA2C6', '#C87489', '#C8C2E4', '#7CB5EC', '#815D63', '#7CB12D']
        if (this.charttype) {
          if (this.charttype === 'pie') {
            return {
              chart: {
                animation: false,
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false,
                type: 'pie'
              },
              title: '',
              colors: colorSet,
              tooltip: {
                pointFormat: '<b>{point.y} ({point.percentage:.1f}%)</b>'
              },
              plotOptions: {
                pie: {
                  allowPointSelect: false,
                  cursor: 'pointer',
                  dataLabels: {
                    enabled: true,
                    formatter: function () {
                      if (this.percentage > 0 && this.percentage !== 100) {
                        return this.y
                      }
                    }
                  },
                  showInLegend: true
                }
              },
              series: [],
              credits: {
                enabled: false
              }
            }
          } else if (this.charttype === 'column') {
            return {
              chart: {
                animation: false,
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false,
                type: 'column'
              },
              title: '',
              colors: colorSet,
              tooltip: {
                pointFormat: '<b>{series.name}: {point.y}</b><br/>'
              },
              plotOptions: {
                column: {
                  dataLabels: {
                    enabled: true
                    // color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white'
                  }
                }
              },
              series: [],
              credits: {
                enabled: false
              }
            }
          } else if (this.charttype === 'spline') {
            return {
              chart: {
                animation: false,
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false,
                type: 'spline'
              },
              title: '',
              colors: colorSet,
              tooltip: {
                pointFormat: '<b>{series.name}: {point.y}</b><br/>'
              },
              plotOptions: {
                series: {
                  dataLabels: {
                    enabled: true
                  }
                }
              },
              series: [],
              credits: {
                enabled: false
              }
            }
          }
        } else {
          return this.options
        }
      }
    },
    watch: {
      options: function () {
        this.init('init')
      },
      data: function () {
        let self = this
        setTimeout(function () {
          self.init('update')
        }, 500)
      }
    },
    mounted: function () {
      this.init('init')
      if (this.data.length > 0) {
        this.init('update')
      }
    }
  }
</script>

<style scoped>

</style>
