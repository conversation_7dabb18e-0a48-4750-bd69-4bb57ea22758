<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <!--<div slot="title">-->
          <!--<common-direction></common-direction>-->
        <!--</div>-->
        <div >
          <div v-if="display5">
              <Row>
                <Col span="8">
                <div id="bugattributetable" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="effectivebugratio" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="averageffectivebug" name="ticedelayratio" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
              </Row>
            <div>
              <Card>
                <div style="margin-bottom: 5px;margin-top: -6px">
                  <span style="font-weight: bolder;font-size: larger;">质量数据</span>
                </div>
                <Table :height="600" border :row-class-name="rowClassName" :columns="columnsObject.column" :data="qualityStats"></Table>
              </Card>
            </div>
          </div>
          <div v-else>
          <Row>
            <Col span="8">
            <div id="bugattributetable" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="effectivebugratio" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="averageffectivebug" name="ticedelayratio" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
          </Row>
        <div>
          <Card>
            <div style="margin-bottom: 5px;margin-top: -6px">
              <span style="font-weight: bolder;font-size: larger;">质量数据</span>
            </div>
            <Table border :row-class-name="rowClassName" :columns="columnsObject.column" :data="qualityStats"></Table>
          </Card>
        </div>
      </div>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  // import axios from 'axios'
  // import Head from '@/components/common/Head'
  import { Bus } from '@/global/bus'
  // import router from '@/router'
  import CommonDirection from './CommonDirection'
  // import { analyticsbaseAPI } from '@/global/variable'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  HighchartsMore(Highcharts)
  // import Layout from 'iview/src/components/layout/layout'
  // let tableData = []
  // Vue.component('head-component', Head)
  // Bus.$on('createTable', function (data, self) {
  //   self.tableData = data['statsData']
  //   self.data6 = self.getProcessData(self.tableData)
  //   self.data_resource = self.data6[0]
  //   self.data_deal = self.data6[1]
  //   self.data_access = self.data6[2]
  // })
  Bus.$on('refreshQualityStatOnes', function (data, searchtype) {
    if (searchtype === '客户端') {
      let obj = Bus.qualityMetricsUnitOnesObject.columns6
      Bus.qualityMetricsUnitOnesObject.$set(Bus.qualityMetricsUnitOnesObject.columnsObject, 'column', obj)
    } else if (searchtype === '服务端') {
      let obj = Bus.qualityMetricsUnitOnesObject.columns5
      Bus.qualityMetricsUnitOnesObject.$set(Bus.qualityMetricsUnitOnesObject.columnsObject, 'column', obj)
    }
    Bus.qualityMetricsUnitOnesObject.qualityStats = Bus.qualityMetricsUnitOnesObject.setQualityData(data)
    Bus.qualityMetricsUnitOnesObject.setbugattributetable(Bus.qualityMetricsUnitOnesObject.qualityStats) // bug归属方向数据处理
  })
  Bus.$on('refreshQualityTableLengthOnes', function (data) {
    Bus.qualityMetricsUnitOnesObject.display5 = Bus.qualityMetricsUnitOnesObject.adjustTableHight(data)
  })
  Bus.$on('reflushClearqualitydataOnes', function () {
    Bus.qualityMetricsUnitOnesObject.qualityStats = []
  })
  export default {
    components: {CommonDirection},
    name: 'quality-metrics-unit-ones',
    data: function () {
      return {
        display5: false,
        searchtype: this.$route.query.searchtype ? this.$route.query.searchtype : '服务端',
        directories: [],
        validbugratio: [],
        averagebugperday: [],
        periodlist: [],
        columnsObject: {
          column: []
        },
        columns5: [
          {
            title: '方向',
            key: 'direction',
            // width: 20,
            sortable: true
          },
          {
            title: '时间',
            key: 'period',
            // width: 20,
            sortable: true
          },
          {
            title: '问题总数',
            key: 'totalCnt',
            // width: 120,
            sortable: true
          },
          {
            title: '有效问题总数',
            // width: 110,
            sortable: true,
            render: (h, params) => {
              if (!this.getDetailFlag(params.row)) {
                return h('div', [
                  h('span', {
                  }, params.row['totalBugCnt'])
                ])
              } else {
                return h('div', [
                  h('span', params.row['totalBugCnt'])
                ])
              }
            }
          },
          {
            title: '有效bug数',
            key: 'validBugCnt',
            // width: 120,
            sortable: true
          },
          {
            title: '有效Bug占比',
            key: 'validBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '前端Bug占比',
            key: 'feBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '后端Bug占比',
            key: 'beBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '客户端Bug占比',
            key: 'appBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '产品Bug占比',
            key: 'pmBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '总Bug率',
            key: 'averageBugPerDay',
            // width: 190,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: this.getclassname(params.row),
                    fontWeight: this.getfontweight(params.row)
                  }
                }, params.row.averageBugPerDay)
              ])
            }
          },
          {
            title: '前端Bug率',
            key: 'feAverageBugPerDay',
            // width: 200,
            sortable: true
          },
          {
            title: '后端Bug率',
            key: 'beAverageBugPerDay',
            // width: 200,
            sortable: true
          },
          {
            title: '客户端Bug率',
            key: 'clientAverageBugPerDay',
            // width: 200,
            sortable: true
          },
          {
            title: '千行代码Bug率',
            key: 'changeBugRatio',
            // width: 200,
            sortable: true
          },
          {
            title: '自动化发现问题占比',
            key: 'autoTestBugRatio',
            // width: 200,
            sortable: true
          },
          {
            title: '风险前置率',
            key: 'preRiskBugRatio',
            // width: 200,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  }
                  // style: {
                  //   marginRight: '5px'
                  // },
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                }, '查看详情')
              ])
            }
          }
        ],
        columns6: [
          {
            title: '方向',
            key: 'direction',
            // width: 20,
            sortable: true
          },
          {
            title: '系统',
            key: 'period',
            sortable: true
          },
          {
            title: '平台',
            key: 'app',
            sortable: true
          },
          {
            title: '版本',
            key: 'version',
            sortable: true
          },
          {
            title: 'Bug总数',
            // width: 110,
            sortable: true,
            render: (h, params) => {
              if (!this.getDetailFlag(params.row)) {
                return h('div', [
                  h('a', {
                    attrs: {
                      href: params.row['subTaskJQLUrl'],
                      target: '_blank'
                    }
                  }, params.row['totalBugCnt'])
                ])
              } else {
                return h('div', [
                  h('span', params.row['totalBugCnt'])
                ])
              }
            }
          },
          {
            title: '有效bug数',
            key: 'validBugCnt',
            // width: 120,
            sortable: true
          },
          {
            title: '有效Bug占比',
            key: 'validBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '客户端Bug占比',
            key: 'appBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '产品Bug占比',
            key: 'pmBugRatio',
            // width: 140,
            sortable: true
          },
          {
            title: '总Bug率',
            key: 'averageBugPerDay',
            // width: 190,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: this.getclassname(params.row),
                    fontWeight: this.getfontweight(params.row)
                  }
                }, params.row.averageBugPerDay)
              ])
            }
          },
          {
            title: '客户端Bug率',
            key: 'clientAverageBugPerDay',
            // width: 200,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jumpclient(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  }
                  // style: {
                  //   marginRight: '5px'
                  // },
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                }, '查看详情')
              ])
            }
          }
        ],
        qualityStats: []
      }
    },

    methods: {
      getcolumn: function () {
        if (this.$route.query.searchtype === '服务端') {
          return this.columns5
        } else {
          return this.columns6
        }
      },
      adjustTableHight: function (len) {
        let flag
        if (len > 10) {
          flag = true
        } else {
          flag = false
        }
        return flag
      },
      rowClassName: function (raw, index) {
        if (index % 2 === 0 && raw.direction !== '总计') {
          return 'demo-stripe'
        } else if (raw.direction === '总计') {
          return 'demo-table-info-row'
        } else {
          return ''
        }
      },
      getclassname: function (raw) {
        if (raw.averageBugPerDay && Number(raw.averageBugPerDay, 0) > 0.5) {
          return 'red'
        } else {
          return '#495060'
        }
      },
      getfontweight: function (raw) {
        if (raw.averageBugPerDay && Number(raw.averageBugPerDay, 0) > 0.5) {
          return 'bold'
        }
      },
      setQualityData: function (rawdata) {
        // debugger
        if (rawdata) {
          let metricsStats = []
          for (let period in rawdata) {
            metricsStats.push(rawdata[period])
          }
          return metricsStats
        } else {
          return []
        }
      },
      setbugattributetable: function (data) { // bug归属
        let self = this
        this.directories = []
        this.periodlist = []
        let periodlist = []
        for (const each in data) {
          if (self.directories.indexOf(data[each].direction) === -1) {
            this.directories.push(data[each].direction)
          }
          if (periodlist.indexOf(data[each].period) === -1) { // 可以按顺序排列
            periodlist.push(data[each].period)
          }
        }
        self.periodlist = self.sortbyTime(periodlist)
        const bugattributedict = {}
        const validbugratio = {}
        const averagebugperday = {}
        for (let i = 0; i < self.periodlist.length; i += 1) {
          const evefebugattribute = []
          const evebebugattribute = []
          const eveappbugattribute = []
          const evepmbugattribute = []
          const eveotherattribute = []
          const evevalidbugretio = []
          const eveaverageBugPerDay = []
          const bugattribute = []
          for (let k = 0; k < self.directories.length; k += 1) {
            for (const j in data) {
              let sum = 0
              if (data[j].period === self.periodlist[i] && data[j].direction === self.directories[k]) {
                if (data[j].feBugRatio) {
                  evefebugattribute.push(Math.floor(data[j].feBugRatio * 10000) / 100)
                  sum += Math.floor(data[j].feBugRatio * 10000) / 100
                } else {
                  evefebugattribute.push(0)
                }
                if (data[j].validBugRatio) {
                  if (data[j].validBugRatio.indexOf('%') !== -1) {
                    evevalidbugretio.push(this.roundFun(parseFloat(data[j].validBugRatio.split('%', 1)[0] * 100), 2))
                  } else {
                    evevalidbugretio.push(this.roundFun(parseFloat(data[j].validBugRatio) * 100, 2))
                  }
                } else {
                  evevalidbugretio.push(0)
                }
                if (data[j].averageBugPerDay) {
                  eveaverageBugPerDay.push(Number(data[j].averageBugPerDay))
                } else {
                  eveaverageBugPerDay.push(0)
                }
                if (data[j].beBugRatio) {
                  evebebugattribute.push(Math.floor(Number(data[j].beBugRatio * 10000) / 100))
                  sum += Math.floor(Number(data[j].beBugRatio * 10000) / 100)
                } else {
                  evebebugattribute.push(0)
                }
                if (data[j].appBugRatio) {
                  eveappbugattribute.push(Math.floor(Number(data[j].appBugRatio * 10000) / 100))
                  sum += Math.floor(Number(data[j].appBugRatio * 10000) / 100)
                } else {
                  eveappbugattribute.push(0)
                }
                if (data[j].pmBugRatio) {
                  evepmbugattribute.push(Math.floor(Number(data[j].pmBugRatio * 10000) / 100))
                  sum += Math.floor(Number(data[j].pmBugRatio * 10000) / 100)
                } else {
                  evepmbugattribute.push(0)
                }
                if (data[j].totalBugCnt && data[j].totalBugCnt > 0) {
                  eveotherattribute.push(Math.floor((100 - sum) * 100) / 100)
                } else {
                  eveotherattribute.push(0)
                }
                break
              }
            }
          }
          if (evevalidbugretio.length < self.directories.length) {
            evevalidbugretio.push(0)
          }
          if (eveaverageBugPerDay.length < self.directories.length) {
            eveaverageBugPerDay.push(0)
          }
          if (evefebugattribute.length < self.directories.length) {
            evefebugattribute.push(0)
          }
          if (evebebugattribute.length < self.directories.length) {
            evebebugattribute.push(0)
          }
          if (eveappbugattribute.length < self.directories.length) {
            eveappbugattribute.push(0)
          }
          if (evepmbugattribute.length < self.directories.length) {
            evepmbugattribute.push(0)
          }
          if (eveotherattribute.length < self.directories.length) {
            eveotherattribute.push(0)
          }
          validbugratio[self.periodlist[i]] = evevalidbugretio
          averagebugperday[self.periodlist[i]] = eveaverageBugPerDay
          bugattribute.push(
            {
              'name': '后端',
              'data': evebebugattribute,
              'color': '#FFA500',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          bugattribute.push(
            {
              'name': '其他',
              'data': eveotherattribute,
              'color': '#c1405a',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          bugattribute.push(
            {
              'name': '前端',
              'data': evefebugattribute,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          bugattribute.push(
            {
              'name': '客户端',
              'data': eveappbugattribute,
              'color': '#20B2AA',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          bugattribute.push(
            {
              'name': '产品',
              'data': evepmbugattribute,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )

          bugattributedict[self.periodlist[i]] = bugattribute
        }
        // const color = ['#00CD66', '#405ac1', '#20B2AA', '#FFA500', '#8192D6', '#c581d6', '#c1405a']
        self.bugattribute = []
        self.validbugratio = []
        self.averagebugperday = []
        for (const each in bugattributedict) {
          for (const i in bugattributedict[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: bugattributedict[each][i].name,
                  data: bugattributedict[each][i].data,
                  color: bugattributedict[each][i].color,
                  stack: '第一季度',
                  showInLegend: bugattributedict[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: bugattributedict[each][i].name,
                  data: bugattributedict[each][i].data,
                  color: bugattributedict[each][i].color,
                  stack: '第二季度',
                  showInLegend: bugattributedict[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: bugattributedict[each][i].name,
                  data: bugattributedict[each][i].data,
                  color: bugattributedict[each][i].color,
                  stack: '第三季度',
                  showInLegend: bugattributedict[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: bugattributedict[each][i].name,
                  data: bugattributedict[each][i].data,
                  color: bugattributedict[each][i].color,
                  stack: '第四季度',
                  showInLegend: bugattributedict[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: bugattributedict[each][i].name,
                data: bugattributedict[each][i].data,
                color: bugattributedict[each][i].color,
                stack: bugattributedict[each][i].stack,
                showInLegend: bugattributedict[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: bugattributedict[each][i].name,
                data: bugattributedict[each][i].data,
                color: bugattributedict[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: bugattributedict[each][i].showInLegend
              }
            }
            self.bugattribute.push(tempdata)
          }
        }
        for (const each in validbugratio) {
          const result = []
          for (const i in validbugratio[each]) {
            const tempdata = {
              name: self.directories[i],
              y: validbugratio[each][i]
            }
            result.push(tempdata)
          }
          let validbugdata = {}
          if (self.$route.query.type === 'QUARTER') {
            if (parseInt(each.split('-')[1]) <= 3) {
              validbugdata = {
                name: '第一季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else if (parseInt(each.split('-')[1]) <= 6) {
              validbugdata = {
                name: '第二季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else if (parseInt(each.split('-')[1]) <= 9) {
              validbugdata = {
                name: '第三季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else {
              validbugdata = {
                name: '第四季度',
                visible: true,
                // color: color[index],
                data: result
              }
            }
          } else if (self.$route.query.type === 'CUSTOMED') {
            validbugdata = {
              name: each,
              visible: true,
              // color: color[index],
              data: result
            }
          } else {
            validbugdata = {
              name: each.split('-')[1] + '月',
              visible: true,
              data: result
            }
          }
          this.validbugratio.push(validbugdata)
          // index += 1
        }
        // let no = 0
        for (const each in averagebugperday) {
          const result = []
          for (const i in averagebugperday[each]) {
            const tempdata = {
              name: self.directories[i],
              y: averagebugperday[each][i]
            }
            result.push(tempdata)
          }
          let averagebugperdaydata = {}
          if (self.$route.query.type === 'QUARTER') {
            if (parseInt(each.split('-')[1]) <= 3) {
              averagebugperdaydata = {
                name: '第一季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else if (parseInt(each.split('-')[1]) <= 6) {
              averagebugperdaydata = {
                name: '第二季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else if (parseInt(each.split('-')[1]) <= 9) {
              averagebugperdaydata = {
                name: '第三季度',
                visible: true,
                // color: color[index],
                data: result
              }
            } else {
              averagebugperdaydata = {
                name: '第四季度',
                visible: true,
                // color: color[index],
                data: result
              }
            }
          } else if (self.$route.query.type === 'CUSTOMED') {
            averagebugperdaydata = {
              name: each,
              visible: true,
              // color: color[index],
              data: result
            }
          } else {
            averagebugperdaydata = {
              name: each.split('-')[1] + '月',
              visible: true,
              // color: color[index],
              data: result
            }
          }
          this.averagebugperday.push(averagebugperdaydata)
          // no += 1
        }
        setTimeout(function () {
          self.bugattributehighchart()
          self.averagebugperdaychart()
          self.validbugratiochart()
        }, 500)
      },
      averagebugperdaychart: function () {
        Highcharts.chart('averageffectivebug', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '工时平均有效bug'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            max: 1,
            title: {
              text: null
            },
            plotLines: [{
              color: 'red',
              dashStyle: 'longdashdot',
              value: 0.5,
              width: 2,
              label: {
                text: '0.5',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -10
              }
            }],
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          series: this.averagebugperday,
          credits: {
            enabled: false
          }
        })
      },
      validbugratiochart: function () {
        Highcharts.chart('effectivebugratio', {
          chart: {
            type: 'column',
            inverted: false
          },
          title: {
            text: '有效Bug占比'
          },
          xAxis: {
            categories: this.directories
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: false
          },
          legend: {
            enabled: true
          },
          plotOptions: {
            column: {
              dataLabels: {
                enabled: true,
                color: '#000000',
                formatter: function () {
                  return this.y + '%'
                }
              },
              pointPadding: 0.2,
              borderWidth: 0
            }
          },
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d', '＃2f7ed8', '＃0d233a', '＃8bbc21', '＃910000', '＃1aadce',
            '＃492970', '＃f28f43', '＃77a1e5', '＃c42525', '＃a6c96a'],
          series: this.validbugratio,
          credits: {
            enabled: false
          }
        })
      },
      bugattributehighchart: function () {
        Highcharts.chart('bugattributetable', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: 'Bug归属方向占比'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            max: 100,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'percent'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y} %',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '%'
          },
          series: this.bugattribute,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      getDetailFlag: function (data) {
        if (data['direction'] === '总计') {
          return true
        } else {
          return false
        }
        // return true
      },
      jumpclient: function (data) {
        const dict = {
          bg: this.$route.query.bg,
          bizline: this.$route.query.bizline,
          group: this.$route.query.group,
          searchtype: '客户端',
          name: data['direction'],
          app: data['app'],
          version: data['version'],
          platform: data['period']
        }
        let temp = ''
        temp += '/overview/tice/ones/detail?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      },
      roundFun: function (value, n) {
        return Math.round(value * Math.pow(10, n)) / Math.pow(10, n)
      },
      jump: function (data) {
        const dict = {
          searchtype: '服务端',
          name: data['key'],
          period: data['period']
        }
        let temp = ''
        temp += '/overview/tice/ones/detail?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      }
    },
    mounted: function () {
      Bus.qualityMetricsUnitOnesObject = this
    }
  }
</script>

<style>
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
