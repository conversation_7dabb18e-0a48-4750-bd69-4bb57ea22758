<template>
  <div>
    <head-component></head-component>
    <div>
      <Alert show-icon closable banner style="margin-top: 15px;margin-left: 50px;margin-right: 50px">
        <template slot="desc">
          <span style="font-weight: bolder">本页面支持为特定方向配置自定义接口，适用场景如下：</span>
          <div><span style="font-weight: bolder">1.使用Ones作为项目管理工具</span></div>
          <div><span style="font-weight: bolder">2.使用自定义模版（不是从到店标准Ones模版中创建的项目）</span></div>
          <div><span style="font-weight: bolder">3.已按照规定接口形式提供后台接口并可调用，<span style="color: red">请务必确保所有参数均存在</span>。具体接口格式可参照文档  <a href="https://km.sankuai.com/page/142413917">接口定义</a></span></div>
          <div><span style="font-weight: bolder">4.具体数据说明请参考文档  <a href="https://km.sankuai.com/page/140955020">数据说明</a></span></div>
          <div><span style="font-weight: bolder">注：配置完自定义接口后，该方向的查询数据将会调用配置后的接口链接，请确保接口可用&格式正确。如需替换成默认接口，将该配置删除即可</span></div>
          <div><span style="font-weight: bolder">相关使用上的反馈请联系张淼（zhangmiao07）、贾晨宇（jiachenyu02）。</span></div>
        </template>
      </Alert>
      <div style="display: flex;margin-left: 50px;margin-top: 25px">
        <span style="font-weight: bolder;font-size: 12px">请选择要配置的方向：</span>
        <Cascader v-if="!displaymode" v-model="direction" :transfer=true :data="directionList" @on-change="changedirection" :change-on-select=true :clearable=false placeholder="请选择要配置的方向" style="width: 410px;margin-top: -8px;padding-left: 10px"></Cascader>
        <tag v-if="displaymode" style="margin-left: 10px;margin-top: -3px"><span style="font-size: 10px;font-weight: bolder">{{displaydirection}}</span></tag>
      </div>
      <div style="display: flex;margin-left: 50px;margin-top: 25px">
        <span style="font-weight: bolder;font-size: 12px;width: 120px">请填写接口域名：</span>
        <Input v-if="!displaymode" style="margin-left: 10px;width: 400px;margin-top: -5px" v-model="domain" placeholder="请输入接口域名">
        </Input>
        <span v-if="!displaymode" style="font-size: 10px;font-weight: bolder;margin-left: 10px;margin-top: 2px">如：http://***.sankuai.com/analytics</span>
        <tag v-if="displaymode" style="margin-left: 10px;margin-top: -3px;height: 25px"><span style="font-size: 10px;font-weight: bolder">{{domain}}</span></tag>
      </div>
      <div style="margin-top: 30px;margin-left: 176px">
        <Button v-if="displaymode" type="dashed" size="small" @click="edit" style="margin-left: 5px;margin-top: -3px"><i class="fa fa-pencil" style="padding-right: 5px" aria-hidden="true"></i>修改</Button>
        <Button v-if="!displaymode" class="tool-button" type="dashed" size="small" @click="save"><i class="fa fa-floppy-o" style="padding-right: 5px" aria-hidden="true"></i>保存</Button>
        <Button v-if="!displaymode && cancelmode" class="tool-button" type="dashed" size="small" @click="cancel">取消</Button>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import {analyticsbaseAPI} from '@/global/variable'

  export default {
    name: 'config',
    data: function () {
      return {
        directionList: [],
        direction: [],
        backupdirection: [],
        domain: '',
        backupdomain: '',
        displaydirection: '',
        backupdisplaydirection: '',
        cancelmode: false,
        displaymode: false
      }
    },
    methods: {
      changedirection: function (value, selectedData) {
        this.displaydirection = ''
        if (selectedData.length > 0) {
          for (const each in selectedData) {
            if (parseInt(each) !== selectedData.length - 1) {
              this.displaydirection += selectedData[each].label
              this.displaydirection += ' / '
            } else {
              this.displaydirection += selectedData[each].label
            }
          }
        }
      },
      edit: function () {
        this.backupdomain = ''
        this.backupdomain = this.domain
        this.backupdisplaydirection = this.displaydirection
        this.backupdirection = JSON.parse(JSON.stringify(this.direction))
        this.displaymode = false
        this.cancelmode = true
      },
      save: function () {
        if (this.direction.length !== 0 && this.domain) {
          this.displaymode = true
        } else {
          this.$Message.info('存在未填写的参数！')
        }
      },
      cancel: function () {
        this.displaymode = true
        this.domain = this.backupdomain
        this.displaydirection = this.backupdisplaydirection
        this.direction = JSON.parse(JSON.stringify(this.backupdirection))
      },
      getBizlineData () {
        let self = this
        axios.get(analyticsbaseAPI + '/common/bizLine/ones/all', {
          timeout: 10000,
          dataType: 'json'
        }).then(function (message) {
          if (message['data']['status'] === 0) {
            let bizData = []
            for (let bgInfo in message['data']['data']) {
              let bgBizInfo = message['data']['data'][bgInfo]
              let bgTop = {}
              bgTop['value'] = bgBizInfo['bgId']
              bgTop['label'] = bgBizInfo['bgName']
              bgTop['children'] = self.getOnesBGData(bgBizInfo['bizLineList'])
              bizData.push(bgTop)
            }
            self.directionList = bizData
          } else {
            console.log(message['data']['msg'])
          }
        }).catch(function (error) {
          console.log(error)
        })
      },
      getOnesBGData (data) {
        let bizData = []
        if (data) {
          for (let eachBizLine in data) {
            let tempData = {}
            tempData['value'] = data[eachBizLine]['id']
            tempData['label'] = data[eachBizLine]['bizLineName']
            let tempBizData = []
            for (let groupList in data[eachBizLine]['projectSetList']) {
              let groupData = {}
              groupData['value'] = data[eachBizLine]['projectSetList'][groupList]['projectSetId']
              groupData['label'] = data[eachBizLine]['projectSetList'][groupList]['projectName']
              tempBizData.push(groupData)
            }
            tempData['children'] = tempBizData
            bizData.push(tempData)
          }
        }
        return bizData
      }
    },
    mounted: function () {
      this.getBizlineData()
    }
  }
</script>

<style scoped>
  .tool-button{
    margin-left: 5px;
    color:#2d8cf0;
    border-color: #2d8cf0;
    font-weight: bolder;
  }
  .tool-button:hover{
    margin-left: 5px;
    color:#1a1a1a;
    border-color: #e9eaec;
    font-weight: bolder;
  }

</style>
