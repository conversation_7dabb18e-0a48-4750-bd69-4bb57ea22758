<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <div>
          <Row>
            <Col span="8">
            <div id="effectiveonlinebugratio" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="averagesolvetime" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
            <Col span="8">
            <div id="averagehiddentime" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
            </Col>
          </Row>
          <div>
            <Card>
              <div style="margin-bottom: 5px;margin-top: -6px">
                <span style="font-weight: bolder;font-size: larger;">线上问题</span>
              </div>
              <Table border :row-class-name="rowClassName" :columns="columnsObject.column" :data="tableData"></Table>
            </Card>
          </div>
          <div>
              <Row>
                <Col span="6" v-for="(item, index) of bugTrendChartData" :key="index">
                  <div style="font-weight: bolder;padding-top: 5px"><div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div><span style="font-size: 16px">{{index}}趋势占比分析（个）</span></div>
                  <common-chart :charttype="'pie'" :data="item" width="100%"></common-chart>
                </Col>
              </Row>
              <Row>
                <Col span="6" v-for="(item, index) of bugBelongChartData" :key="index">
                  <div style="font-weight: bolder;padding-top: 5px"><div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div><span style="font-size: 16px">{{index}}归属方向占比分析（个）</span></div>
                  <common-chart :charttype="'pie'" :data="item" width="100%"></common-chart>
                </Col>
              </Row>
              <Row>
                <Col span="6" v-for="(item, index) of bugReasonChartData" :key="index">
                  <div style="font-weight: bolder;padding-top: 5px"><div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div><span style="font-size: 16px">{{index}}原因占比分析（个）</span></div>
                  <common-chart :charttype="'pie'" :data="item" width="100%"></common-chart>
                </Col>
              </Row>
              <Row>
                <Col span="6" v-for="(item, index) of bugPriorityChartData" :key="index">
                  <div style="font-weight: bolder;padding-top: 5px"><div class="ivu-tag-dot-inner" style="background-color: #2d8cf0"></div><span style="font-size: 16px">{{index}}等级占比分析（个）</span></div>
                  <common-chart :charttype="'pie'" :data="item" width="100%"></common-chart>
                </Col>
              </Row>
          </div>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import { Bus } from '@/global/bus'
//  import router from '@/router'
  import CommonDirection from './CommonDirection'
  import Highcharts from 'highcharts/highstock'
  import HighchartsMore from 'highcharts/highcharts-more'
  import CommonChart from './commonChart'
  HighchartsMore(Highcharts)
  // import Layout from 'iview/src/components/layout/layout'
  // let tableData = []
  // Vue.component('head-component', Head)
  // Bus.$on('createTable', function (data, self) {
  //   self.tableData = data['statsData']
  //   self.data6 = self.getProcessData(self.tableData)
  //   self.data_resource = self.data6[0]
  //   self.data_deal = self.data6[1]
  //   self.data_access = self.data6[2]
  // })
  Bus.$on('refreshOnlineQualityStatOnes', function (data, searchtype) {
    if (searchtype === '客户端') {
      let obj = Bus.OnlineQualityOnesObject.columns6
      Bus.OnlineQualityOnesObject.$set(Bus.OnlineQualityOnesObject.columnsObject, 'column', obj)
    } else if (searchtype === '服务端') {
      let obj = Bus.OnlineQualityOnesObject.columns5
      Bus.OnlineQualityOnesObject.$set(Bus.OnlineQualityOnesObject.columnsObject, 'column', obj)
    }
    Bus.OnlineQualityOnesObject.tableData = Bus.onlineBugStats['tableData']
    Bus.OnlineQualityOnesObject.setonlinebugchartdata(Bus.OnlineQualityOnesObject.tableData)
    Bus.OnlineQualityOnesObject.setOnlineBugPieChartData(Bus.onlineBugStats['chartData'])
    Bus.OnlineQualityOnesObject.queryPattern = Bus.onlineBugStats['pattern']
  })
  Bus.$on('refreshQualityTableLengthOnes', function (data) {
  })
  export default {
    // components: {Layout},
    components: {CommonDirection, CommonChart},
    name: 'onlinebug-metrics-unit-ones',
    data: function () {
      return {
        display5: false,
        directories: [],
        periodlist: [],
        columnsObject: {
          column: []
        },
        columns5: [
          {
            title: '方向',
            key: 'directionName',
            // width: 20,
            sortable: true
          },
          {
            title: '时间',
            key: 'periodTime',
            // width: 20,
            sortable: true
          },
          {
            title: '线上问题数',
            // width: 110,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['totalCnt'])
              ])
            }
          },
          {
            title: '线上有效问题数',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['validBugCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '未解决问题',
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: 'red'
                  }
                }, params.row['unsolvedCnt'])
              ])
            },
            // width: 120,
            sortable: true
          },
          {
            title: '已解决问题',
            render: (h, params) => {
              return h('div', [
                h('span', {
                }, params.row['solveErrorCnt'])
              ])
            },
            // width: 120,
            sortable: true
          },
          {
            title: '线上隐藏问题数',
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: 'red'
                  }
                }, params.row['hiddenErrorCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '自动化发现问题',
            render: (h, params) => {
              return h('div', [
                h('span', {
                  style: {
                    color: 'red'
                  }
                }, params.row['couldFindByAutoTestCnt'])
              ])
            },
            // width: 120,
            sortable: true
          },
          {
            title: '问题平均解决时长(h)',
            key: 'averageDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '问题平均潜藏时长(h)',
            key: 'averageHideDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    target: '_blank',
                    to: this.jump(params.row),
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                }, '查看详情')
              ])
            }
          }
        ],
        columns6: [
          {
            title: '方向',
            key: 'directionName',
            // width: 20,
            sortable: true
          },
          {
            title: '系统',
            key: 'periodTime',
            sortable: true
          },
          {
            title: '平台',
            key: 'app',
            sortable: true
          },
          {
            title: '版本',
            key: 'version',
            sortable: true
          },
          {
            title: '线上问题数',
            // width: 110,
            sortable: true,
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['totalUrl'],
                    target: '_blank'
                  }
                }, params.row['totalCnt'])
              ])
            }
          },
          {
            title: '未解决问题',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['unsolvedUrl'],
                    target: '_blank'
//                    style: {
//                      color: "red"
//                    }
                  }
                }, params.row['unsolvedCnt'])
              ])
            },
            // width: 120,
            sortable: true
          },
          {
            title: '线上有效Bug数',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['validBugUrl'],
                    target: '_blank'
                  }
                }, params.row['validBugCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '线上隐藏问题数',
            render: (h, params) => {
              return h('div', [
                h('a', {
                  attrs: {
                    href: params.row['validFaultUrl'],
                    target: '_blank'
                  }
                }, params.row['hiddenErrorCnt'])
              ])
            },
            // width: 140,
            sortable: true
          },
          {
            title: '问题平均解决时长(h)',
            key: 'averageDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '问题平均潜藏时长(h)',
            key: 'averageHideDuration',
            // width: 140,
            sortable: true
          },
          {
            title: '操作',
            key: 'action',
            // width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    target: '_blank',
                    to: this.jumpclient(params.row),
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                }, '查看详情')
              ])
            }
          }
        ],
        bugBelongChartData: {},
        bugPriorityChartData: {},
        bugTrendChartData: {},
        bugReasonChartData: {},
        tableData: [],
        averagebughiddentime: [],
        averagebugsolvetime: [],
        onlineTotalCnt: []
      }
    },

    methods: {
      getDetailFlag: function (data) {
        if (data['directionName'] === '总计') {
          return true
        } else {
          return false
        }
        // return true
      },
      rowClassName: function (raw, index) {
        // console.log(raw)
        if (index % 2 === 0 && raw.direction !== '总计') {
          return 'demo-stripe'
        } else if (raw.direction === '总计') {
          return 'demo-table-info-row'
        } else {
          return ''
        }
      },
      setonlinebugchartdata: function (data) {
        let self = this
        this.directories = []
        this.periodlist = []
        let periodlist = []
        for (const each in data) {
          if (self.directories.indexOf(data[each].directionName) === -1) {
            this.directories.push(data[each].directionName)
          }
          if (periodlist.indexOf(data[each].periodTime) === -1) { // 可以按顺序排列
            periodlist.push(data[each].periodTime)
          }
        }
        self.periodlist = self.sortbyTime(periodlist)
        const averagebugsolvetime = {}
        const averagebughiddentime = {}
        const onlinebugcnt = {}
        for (let i = 0; i < self.periodlist.length; i += 1) {
          const solveErrorCnt = []
          const hiddenErrorCnt = []
          const solveresult = []
          const hiddenresult = []
          const validBugCnt = []
          const validFaultCnt = []
          const onlineCnt = []
          for (let k = 0; k < self.directories.length; k += 1) {
            for (const j in data) {
              if (data[j].periodTime === self.periodlist[i] && data[j].directionName === self.directories[k]) {
                validBugCnt.push(data[j].validBugCnt)
                validFaultCnt.push(data[j].validFaultCnt)
                solveErrorCnt.push(data[j].solveErrorCnt)
                hiddenErrorCnt.push(data[j].hiddenErrorCnt)
                break
              }
            }
          }
          if (solveErrorCnt.length < self.directories.length) {
            solveErrorCnt.push(0)
          }
          if (hiddenErrorCnt.length < self.directories.length) {
            hiddenErrorCnt.push(0)
          }
          onlineCnt.push(
            {
              'name': '线上bug',
              'data': validBugCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          onlineCnt.push(
            {
              'name': '线上故障',
              'data': validFaultCnt,
              'color': '#c581d6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          solveresult.push(
            {
              'name': '线上解决问题',
              'data': solveErrorCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          hiddenresult.push(
            {
              'name': '线上隐藏问题',
              'data': hiddenErrorCnt,
              'color': '#8192D6',
              'stack': self.periodlist[i],
              'showInLegend': i === 0
            }
          )
          averagebugsolvetime[self.periodlist[i]] = solveresult
          averagebughiddentime[self.periodlist[i]] = hiddenresult
          onlinebugcnt[self.periodlist[i]] = onlineCnt
        }
        self.averagebugsolvetime = []
        self.averagebughiddentime = []
        self.onlineTotalCnt = []
        for (const each in averagebugsolvetime) {
          for (const i in averagebugsolvetime[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第一季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第二季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第三季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: averagebugsolvetime[each][i].name,
                  data: averagebugsolvetime[each][i].data,
                  color: averagebugsolvetime[each][i].color,
                  stack: '第四季度',
                  showInLegend: averagebugsolvetime[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: averagebugsolvetime[each][i].name,
                data: averagebugsolvetime[each][i].data,
                color: averagebugsolvetime[each][i].color,
                stack: averagebugsolvetime[each][i].stack,
                showInLegend: averagebugsolvetime[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: averagebugsolvetime[each][i].name,
                data: averagebugsolvetime[each][i].data,
                color: averagebugsolvetime[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: averagebugsolvetime[each][i].showInLegend
              }
            }
            self.averagebugsolvetime.push(tempdata)
          }
        }
        for (const each in averagebughiddentime) {
          for (const i in averagebughiddentime[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第一季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第二季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第三季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: averagebughiddentime[each][i].name,
                  data: averagebughiddentime[each][i].data,
                  color: averagebughiddentime[each][i].color,
                  stack: '第四季度',
                  showInLegend: averagebughiddentime[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: averagebughiddentime[each][i].name,
                data: averagebughiddentime[each][i].data,
                color: averagebughiddentime[each][i].color,
                stack: averagebughiddentime[each][i].stack,
                showInLegend: averagebughiddentime[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: averagebughiddentime[each][i].name,
                data: averagebughiddentime[each][i].data,
                color: averagebughiddentime[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: averagebughiddentime[each][i].showInLegend
              }
            }
            self.averagebughiddentime.push(tempdata)
          }
        }
        for (const each in onlinebugcnt) {
          for (const i in onlinebugcnt[each]) {
            let tempdata = {}
            if (self.$route.query.type === 'QUARTER') {
              if (parseInt(each.split('-')[1]) <= 3) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第一季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 6) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第二季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else if (parseInt(each.split('-')[1]) <= 9) {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第三季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              } else {
                tempdata = {
                  name: onlinebugcnt[each][i].name,
                  data: onlinebugcnt[each][i].data,
                  color: onlinebugcnt[each][i].color,
                  stack: '第四季度',
                  showInLegend: onlinebugcnt[each][i].showInLegend
                }
              }
            } else if (self.$route.query.type === 'CUSTOMED') {
              tempdata = {
                name: onlinebugcnt[each][i].name,
                data: onlinebugcnt[each][i].data,
                color: onlinebugcnt[each][i].color,
                stack: onlinebugcnt[each][i].stack,
                showInLegend: onlinebugcnt[each][i].showInLegend
              }
            } else {
              tempdata = {
                name: onlinebugcnt[each][i].name,
                data: onlinebugcnt[each][i].data,
                color: onlinebugcnt[each][i].color,
                stack: each.split('-')[1] + '月',
                showInLegend: onlinebugcnt[each][i].showInLegend
              }
            }
            self.onlineTotalCnt.push(tempdata)
          }
        }
        setTimeout(function () {
          self.averagebugsolvechart()
          self.averagebughiddenchart()
          self.onlinetotalchart()
        }, 500)
      },
      setOnlineBugPieChartData: function (data) {
        let self = this
        self.bugBelongChartData = data.bugBelongDist
        self.bugPriorityChartData = data.bugPriorityDist
        self.bugTrendChartData = data.bugTrendDist
        self.bugReasonChartData = data.bugReasonDist
      },
      onlinetotalchart: function () {
        Highcharts.chart('effectiveonlinebugratio', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '有效线上问题'
          },
          subtitle: {
            text: '从左到右按照业务方向排序'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'normal'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y}',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '个'
          },
          series: this.onlineTotalCnt,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      averagebugsolvechart: function () {
        Highcharts.chart('averagesolvetime', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '线上解决问题'
          },
          subtitle: {
            text: '从左到右按照业务方向排序'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'normal'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y}',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '个'
          },
          series: this.averagebugsolvetime,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      averagebughiddenchart: function () {
        Highcharts.chart('averagehiddentime', {
          chart: {
            type: 'column'
            // inverted: false
          },
          title: {
            text: '线上隐藏问题'
          },
          subtitle: {
            text: '从左到右按照业务方向排序'
          },
          xAxis: {
            categories: this.directories,
            allowDecimals: false
          },
          yAxis: {
            min: 0,
            title: {
              text: null
            },
            allowDecimals: false
          },
          plotOptions: {
            column: {
              stacking: 'normal'
            },
            series: {
              dataLabels: {
                enabled: true,
                format: '{y}',
                style: {
                  fontWeight: 'bold',
                  fontSize: '8px',
                  color: '#000000',
                  fill: '#000000'
                }
              }
            }
          },
          tooltip: {
            valueSuffix: '个'
          },
          series: this.averagebughiddentime,
          colors: ['#8192D6', '#20B2AA', '#FFA500', '#c581d6', '#c1405a', '#434348', '#90ed7d'],
          credits: {
            enabled: false
          }
        })
      },
      getCountColor: function (cnt) {
        if (cnt > 0) {
          return 'red'
        } else {
          return '#495060'
        }
      },
      getCountFont: function (cnt) {
        if (cnt > 0) {
          return 'bold'
        }
      },
      jumpclient: function (data) {
        const dict = {
          bg: this.$route.query.bg,
          bizLine: this.$route.query.bizline,
          group: this.$route.query.group,
          platform: data['periodTime'],
          app: data['app'],
          version: data['version'],
          searchtype: '客户端',
          queryType: Bus.OnlineQualityOnesObject.queryPattern
        }
        let temp = ''
        temp += '/online/issuedetail/ones?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      },
      jump: function (data) {
        const dict = {
          name: data['directionName'],
          period: data['periodTime'],
          index: data['key'],
          searchtype: '服务端',
          queryType: Bus.OnlineQualityOnesObject.queryPattern
        }
        let temp = ''
        temp += '/online/issuedetail/ones?'
        for (const each in dict) {
          if (dict[each]) {
            temp += `${each}=${dict[each]}`
            temp += '&'
          }
        }
        return temp
      }
    },
    mounted: function () {
      Bus.OnlineQualityOnesObject = this
      // console.log('begin', Bus.OnlineQualityOnesObject)
    }
  }
</script>

<style>
  /*.layout{*/
    /*!*border: 1px solid #d7dde4;*!*/
    /*position: relative;*/
    /*border-radius: 4px;*/
    /*overflow: hidden;*/
  /*}*/
  /*.layout-sider{*/
    /*border: 1px solid #d7dde4;*/
    /*margin-top: 100px;*/
    /*background: #f5f7f9;*/
    /*position: fixed;*/
    /*width:10px;*/
    /*border-radius: 4px;*/
    /*overflow: hidden;*/
    /*left: 0;*/
  /*}*/
  /*.layout-header-bar{*/
    /*!*position: relative;*!*/
    /*margin-right: 0;*/
    /*margin-left: 105px;*/
  /*}*/
  .ivu-table .demo-table-info-row td{
    /*background-color: #bbbec4;*/
    font-weight: bolder;
    color: #2d8cf0;
    /*color: #fff;*/
  }
  .ivu-table .demo-stripe td{
    background-color: #f8f8f9;
    /*color: #fff;*/
  }
</style>
