<template>
  <div>
    <Modal v-model="add" title="请选择业务方向" width="500px" :mask-closable="false" :closable="false" @on-ok="confirmAddDirection()" @on-cancel="cancelDirConfig()">
      <div style="margin-left: 20px">
        <el-tree
          ref="tree"
          class="filter-tree"
          show-checkbox
          style="overflow:auto;"
          :data="subDirectionTree"
          @check="selectchange"
          node-key="direction_id"
          :highlight-current="true"
          :default-checked-keys="checkedList"
          :default-expanded-keys="checkedKeyList">
        </el-tree>
      </div>
    </Modal>
    <div class="inputcard" @click="addDirection()" :style="{maxWidth: width + 'px'}">
      <div v-if="subdirectionList.selected.length > 0" v-for="(item, index) in subdirectionList.selected" :key="index" style="display: inline;padding-left: 3px;">
        <tag closable @on-close="handleClose(item, index)">{{item.label}}</tag>
      </div>
      <div v-if="subdirectionList.selected.length === 0">
        <div style="margin-top: 6px"><span style="color: #c5c8ce;font-size: 12px;padding-left: 8px;">点击选择业务方向</span></div>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'
  export default {
    name: 'sub-direction',
    props: {
      width: {
        type: Number,
        default: 500
      }
    },
    data: function () {
      return {
        checkedList: [],
        subDirectionTree: [],
        subdirectionList: {
          selected: []
        },
        checkedKeyList: [],
        backupSelect: [],
        backupSelectId: [],
        subdirectionIdList: [],
        initialDirection: [],
        add: false,
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      confirmAddDirection: function () {
        this.add = false
        this.$store.commit('setsubDirectionList', this.subdirectionList.selected)
        this.$store.commit('setsubDirectionIdList', this.subdirectionIdList)
      },
      addDirection: function () {
        this.add = true
        this.backupSelect = []
        this.backupSelectId = []
        this.backupSelect = this.subdirectionList.selected
        this.backupSelectId = this.subdirectionIdList
      },
      cancelDirConfig: function () {
        this.$set(this.subdirectionList, 'selected', [])
        this.$set(this.subdirectionList, 'selected', this.backupSelect)
        this.subdirectionIdList = []
        this.subdirectionIdList = JSON.parse(JSON.stringify(this.backupSelectId))
        this.$refs.tree.setCheckedNodes(this.subdirectionList.selected)
        this.$store.commit('setsubDirectionList', this.subdirectionList.selected)
        this.$store.commit('setsubDirectionIdList', this.subdirectionIdList)
      },
      handleClose: function (data, index) {
        this.subdirectionList.selected.splice(index, 1)
        this.subdirectionIdList.splice(index, 1)
        this.$refs.tree.setCheckedNodes(this.subdirectionList.selected)
        this.$store.commit('setsubDirectionList', this.subdirectionList.selected)
        this.$store.commit('setsubDirectionIdList', this.subdirectionIdList)
      },
      getinitialdata: function () {
        this.subdirectionList.selected = []
        this.subdirectionIdList = []
        this.checkedKeyList = []
        if (this.initialDirection.length > 0) {
          for (const each in this.initialDirection) {
            let temp = {
              children: [],
              direction_id: this.initialDirection[each].id,
              if_leaf: false,
              label: this.initialDirection[each].name,
              value: this.initialDirection[each].name
            }
            this.subdirectionList.selected.push(temp)
            this.subdirectionIdList.push(temp.direction_id)
            this.checkedKeyList.push(parseInt(temp.direction_id))
          }
        } else {
          this.subdirectionList.selected.push({
            children: [],
            direction_id: '',
            if_leaf: false,
            label: '',
            value: ''
          })
          this.subdirectionIdList.push()
          this.checkedKeyList.push()
        }
        this.$nextTick(function () {
          this.$refs.tree.setCheckedKeys(this.checkedKeyList, false)
        })
        this.$store.commit('setsubDirectionList', this.subdirectionList.selected)
        this.$store.commit('setsubDirectionIdList', this.subdirectionIdList)
      },
      getRssDirectionList: function () {
        let self = this
        let params = {
          mis: this.mis
        }
        axios.post(this.getDomain('cq') + '/rss/query', JSON.stringify(params)).then(function (message) {
          if (message.data.status === 'success') {
            let data = message.data.data
            if (data[0]) {
              self.setinitialList(data[0].direction_list)
            }
          } else {
            self.setinitialList([])
          }
        })
      },
      setinitialList: function (data) {
        let self = this
        self.initialDirection = []
        if (data) {
          for (const each in data) {
            axios.get(this.getDomain('config') + '/mcd/org/basic_info?direction_id=' + data[each]).then(function (message) {
              if (message.data.result) {
                let temp = {
                  id: data[each],
                  name: message.data.info.direction_name
                }
                self.initialDirection.push(temp)
                // console.log('lenfth', self.initialDirection.length)
              }
            })
          }
          setTimeout(function () {
            self.getinitialdata()
          }, 500)
        }
        // console.log('lalalal', self.initialDirection)
      },
      selectchange: function (data, checkedNode) {
        this.$set(this.subdirectionList, 'selected', [])
        this.subdirectionIdList = []
        this.subdirectionIdList = JSON.parse(JSON.stringify(checkedNode.checkedKeys))
        for (const each of checkedNode.checkedNodes) {
          if (!each.if_leaf && each.children) {
            for (const child of each.children) {
              for (var index in this.subdirectionIdList) {
                if (this.subdirectionIdList[index] === child.direction_id) {
                  this.subdirectionIdList.splice(index, 1)
                }
              }
            }
          }
        }
        for (const each of checkedNode.checkedNodes) {
          if (this.subdirectionIdList.indexOf(each.direction_id) !== -1) {
            this.subdirectionList.selected.push(each)
          }
        }
      },
      getsubDirectionList: function () {
        let self = this
        axios.get('https://pm.sankuai.com/ones/get_ones_direction_field').then(function (message) {
          self.subDirectionTree.length = 0
          if (message.data.status === 0) {
            self.subDirectionTree = message.data.data
            // self.getinitialdata()
          }
        }).catch(function () {
          self.subDirectionTree.length = 0
        })
      }
    },
    mounted: function () {
      // this.getRssDirectionList()
      this.getsubDirectionList()
    }
  }
</script>

<style scoped>
  .inputcard {
    box-sizing: border-box;
    border: 1px solid #dcdee2;
    background-color: #fff;
    border-radius: 4px;
    height: auto;
    min-height: 32px;
    display: block;
    cursor: pointer;
    text-align: left;
    padding-top: 2px;
    padding-bottom: 2px;
  }
</style>

