<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <!--<div slot="title">-->
        <!--<common-direction></common-direction>-->
        <!--</div>-->
        <div>
          <Spin v-if="isLoading">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <Row v-if="!isLoading" type="flex">
            <Col span="24">
            <div v-if="display4" style="padding-left:15px;padding-right: 15px;padding-bottom: 10px">
              <Row>
                <Col span="8">
                <div id="jointdebugratio" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="devtestratio" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="ticedelayratio" name="ticedelayratio" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
              </Row>
              <div id="source">
                <Card>
                  <div style="margin-bottom: 5px;margin-top: -6px">
                    <span style="font-weight: bolder;font-size: larger;">资源消耗统计</span>
                  </div>
                  <Table :height="600" border :row-class-name="rowClassName" :columns="columnsresource.column" :data="resourceStats"></Table>
                </Card>
              </div>
              <div id="timeline" style="margin-top: 5px">
                <Card>
                  <div style="margin-bottom: 5px;margin-top: -6px">
                    <span style="font-weight: bolder;font-size: larger;">如期交付情况</span>
                  </div>
                  <Table :height="600" border :row-class-name="rowClassName" :columns="columnstimeline.column" :data="timelineStats"></Table>
                </Card>
              </div>
              <div id="evaluation" style="margin-top: 5px">
                <Card>
                  <div style="margin-bottom: 5px;margin-top: -6px">
                    <span style="font-weight: bolder;font-size: larger;">预期收益与效果评估</span>
                  </div>
                  <Table :height="600" border :row-class-name="rowClassName" :columns="columnsevaluation.column" :data="evaluationStats"></Table>
                </Card>
              </div>
            </div>
            <div v-else style="padding-left:15px;padding-right: 15px;padding-bottom: 10px">
              <Row>
                <Col span="8">
                <div id="jointdebugratio" style="padding-top:10px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="devtestratio" style="padding-top:10px; margin-left:5px; margin-right:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
                <Col span="8">
                <div id="ticedelayratio" name="ticedelayratio" style="padding-top:10px; margin-left:5px; max-height: 400px;min-height: 400px;"></div>
                </Col>
              </Row>
              <div id="source1">
                <Card>
                  <div style="margin-bottom: 5px;margin-top: -6px">
                    <span style="font-weight: bolder;font-size: larger;">资源消耗统计</span>
                  </div>
                  <Table stripe border :row-class-name="rowClassName" :columns="columnsresource.column" :data="resourceStats"></Table>
                </Card>
              </div>
              <div id="timeline1" style="margin-top: 5px">
                <Card>
                  <div style="margin-bottom: 5px;margin-top: -6px">
                    <span style="font-weight: bolder;font-size: larger;">如期交付情况</span>
                  </div>
                  <Table stripe border :row-class-name="rowClassName" :columns="columnstimeline.column" :data="timelineStats"></Table>
                </Card>
              </div>
            </div>
            </Col>
          </Row>
        </div>
      </Card>
      <div v-if="display2">
        <Modal v-model="modal6" title="异常task提醒" @on-ok="asyncOK('invalidData')" ok-text="导出" @on-cancel="cancel()">
          <div style="max-height: 400px;overflow-y: auto">
            <Table id="invalidData" stripe border :columns="columns4" :data="invalidData"></Table>
          </div>
        </Modal>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
// import axios from 'axios'
// import Vue from 'vue'
// import Head from '@/components/common/Head'
import { Bus } from '@/global/bus'
// import router from '@/router'
// import {analyticsbaseAPI} from '@/global/variable'
import CommonDirection from './CommonDirection'
import Highcharts from 'highcharts/highstock'
import HighchartsMore from 'highcharts/highcharts-more'
import Spin from 'view-design/src/components/spin/spin'
HighchartsMore(Highcharts)

// import moment from 'moment'
// import Layout from 'iview/src/components/layout/layout'
let tableData = []
Bus.$on('refreshResourceStatOnes', function (data, searchtype) {
  if (searchtype === '客户端') {
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnsresource,
      'column',
      Bus.processMetricsUnitOnesObject.columns5
    )
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnstimeline,
      'column',
      Bus.processMetricsUnitOnesObject.columns6
    )
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnsevaluation,
      'column',
      Bus.processMetricsUnitOnesObject.columns7
    )
  } else if (searchtype === '服务端') {
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnsresource,
      'column',
      Bus.processMetricsUnitOnesObject.columns1
    )
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnstimeline,
      'column',
      Bus.processMetricsUnitOnesObject.columns2
    )
    Bus.processMetricsUnitOnesObject.$set(
      Bus.processMetricsUnitOnesObject.columnsevaluation,
      'column',
      Bus.processMetricsUnitOnesObject.columns3
    )
  }
  Bus.processMetricsUnitOnesObject.resourceStats =
    Bus.processMetricsUnitOnesObject.setProcessMetricsData(data)
  Bus.processMetricsUnitOnesObject.setqaratiodata(
    Bus.processMetricsUnitOnesObject.resourceStats
  )
})
Bus.$on('refreshTimelineStatOnes', function (data) {
  Bus.processMetricsUnitOnesObject.timelineStats =
    Bus.processMetricsUnitOnesObject.setProcessMetricsData(data)
  Bus.processMetricsUnitOnesObject.setrdratiodata(
    Bus.processMetricsUnitOnesObject.timelineStats
  )
})
Bus.$on('refreshEvaluationStatOnes', function (data) {
  Bus.processMetricsUnitOnesObject.evaluationStats =
    Bus.processMetricsUnitOnesObject.setProcessMetricsData(data)
})
Bus.$on('refreshProcessTableLengthOnes', function (data) {
  Bus.processMetricsUnitOnesObject.display4 =
    Bus.processMetricsUnitOnesObject.adjustTableHight(data)
})
Bus.$on('refreshshowincompletedataOnes', function (data) {
  Bus.processMetricsUnitOnesObject.display2 = true
  Bus.processMetricsUnitOnesObject.modal6 = true
  Bus.processMetricsUnitOnesObject.invalidData = data
})

export default {
  components: {
    Spin,
    CommonDirection
  },
  name: 'process-metrics-unit-ones',
  data: function () {
    return {
      columnsresource: {
        column: []
      },
      columnstimeline: {
        column: []
      },
      columnsevaluation: {
        column: []
      },
      columns1: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '时间',
          key: 'period',
          sortable: true
        },
        {
          title: '后端开发PD',
          key: 'beDevPd',
          sortable: true
        },
        {
          title: '前端开发PD',
          key: 'feDevPd',
          sortable: true
        },
        {
          title: '客户端开发PD',
          key: 'clientDevPd',
          sortable: true
        },
        {
          title: '联调PD',
          key: 'jointDebugPd',
          sortable: true
        },
        {
          title: '总开发PD',
          key: 'totalDevPd',
          sortable: true
        },
        {
          title: '联调占比',
          key: 'jointDebugRatio',
          sortable: true
        },
        {
          title: '实际测试PD',
          key: 'testPd',
          sortable: true
        },
        {
          title: '平均开发PD',
          key: 'averageDevPd',
          sortable: true
        },
        {
          title: '平均测试PD',
          key: 'averageTestPd',
          sortable: true
        },
        {
          title: '开发测试比',
          key: 'devTestRatio',
          sortable: true,
          render: (h, params) => {
            return h('div', [
              h(
                'span',
                {
                  style: {
                    color: this.getclassname(params.row),
                    fontWeight: this.getfontweight(params.row)
                  }
                },
                params.row.devTestRatio
              )
            ])
          }
        },
        {
          title: '操作',
          key: 'action',
          // width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      columns2: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '时间',
          key: 'period',
          sortable: true
        },
        {
          title: '提测数',
          key: 'transferTaskCnt',
          sortable: true
        },
        {
          title: '提测延期数',
          key: 'transferDelayCnt',
          sortable: true
        },
        {
          title: '提测延期占比',
          key: 'transferDelayRatio',
          sortable: true
        },
        {
          title: '测试完成需求数',
          key: 'testTaskCnt',
          sortable: true
        },
        {
          title: '不符预期测试周期需求数',
          key: 'testDelayCnt',
          sortable: true
        },
        {
          title: '不符预期测试周期占比 ',
          key: 'testDelayRatio',
          sortable: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      columns3: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '时间',
          key: 'period',
          sortable: true
        },
        {
          title: '提测需求数',
          key: 'transferTaskCnt',
          sortable: true
        },
        {
          title: '有预期收益',
          key: 'hasExpectEarnCnt',
          sortable: true
        },
        {
          title: '有预期收益占比',
          key: 'hasExpectEarnRatio',
          sortable: true
        },
        {
          title: '上线需求数',
          key: 'launchTaskCnt',
          sortable: true
        },
        {
          title: '有效果评估需求数',
          key: 'resultEvaluateCnt',
          sortable: true
        },
        {
          title: '有效果评估占比',
          key: 'resultEvaluateRatio',
          sortable: true
        },
        {
          title: '操作',
          key: 'action',
          // width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jump(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      columns4: [
        {
          title: 'task key',
          key: 'issueKey',
          sortable: true
        },
        {
          title: 'task名称',
          key: 'summary',
          sortable: true,
          render: (h, params) => {
            return h('div', [
              h(
                'a',
                {
                  props: {
                    href:
                      'https://flow.sankuai.com/browse/' +
                      params.row['issueKey']
                  },
                  on: {
                    click: () => {
                      window.open(
                        'https://flow.sankuai.com/browse/' +
                          params.row['issueKey']
                      )
                    }
                  }
                },
                params.row['summary']
              )
            ])
          }
        },
        {
          title: '错误原因',
          key: 'reason',
          sortable: true
        },
        {
          title: '经办人',
          key: 'assignee',
          sortable: true
        }
      ],
      columns5: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '系统',
          key: 'period',
          sortable: true
        },
        {
          title: '平台',
          key: 'app',
          sortable: true
        },
        {
          title: '版本',
          key: 'version',
          sortable: true
        },
        {
          title: '客户端开发PD',
          key: 'clientDevPd',
          sortable: true
        },
        {
          title: '客户端联调PD',
          key: 'jointDebugPd',
          sortable: true
        },
        {
          title: '总开发PD',
          key: 'totalDevPd',
          sortable: true
        },
        {
          title: '联调占比',
          key: 'jointDebugRatio',
          sortable: true
        },
        {
          title: '实际测试PD',
          key: 'testPd',
          sortable: true
        },
        {
          title: '平均开发PD',
          key: 'averageDevPd',
          sortable: true
        },
        {
          title: '平均测试PD',
          key: 'averageTestPd',
          sortable: true
        },
        {
          title: '开发测试比',
          key: 'devTestRatio',
          sortable: true,
          render: (h, params) => {
            return h('div', [
              h(
                'span',
                {
                  style: {
                    color: this.getclassname(params.row),
                    fontWeight: this.getfontweight(params.row)
                  }
                },
                params.row.devTestRatio
              )
            ])
          }
        },
        {
          title: '操作',
          key: 'action',
          // width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jumpclient(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      columns6: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '系统',
          key: 'period',
          sortable: true
        },
        {
          title: '平台',
          key: 'app',
          sortable: true
        },
        {
          title: '版本',
          key: 'version',
          sortable: true
        },
        {
          title: '提测需求数',
          key: 'transferTaskCnt',
          sortable: true
        },
        {
          title: '提测延期需求数',
          key: 'transferDelayCnt',
          sortable: true
        },
        {
          title: '提测延期占比',
          key: 'transferDelayRatio',
          sortable: true
        },
        {
          title: '测试完成需求数',
          key: 'testTaskCnt',
          sortable: true
        },
        {
          title: '不符预期测试周期需求数',
          key: 'testDelayCnt',
          sortable: true
        },
        {
          title: '不符预期测试周期占比 ',
          key: 'testDelayRatio',
          sortable: true
        },
        {
          title: '上线需求数',
          key: 'launchTaskCnt',
          sortable: true
        },
        {
          title: '延期上线数',
          key: 'launchDelayCnt',
          sortable: true
        },
        {
          title: '延期上线比例',
          key: 'launchDelayRio',
          sortable: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jumpclient(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      columns7: [
        {
          title: '方向',
          key: 'direction',
          sortable: true
        },
        {
          title: '系统',
          key: 'period',
          sortable: true
        },
        {
          title: '平台',
          key: 'app',
          sortable: true
        },
        {
          title: '版本',
          key: 'version',
          sortable: true
        },
        {
          title: '提测需求数',
          key: 'transferTaskCnt',
          sortable: true
        },
        {
          title: '有预期收益',
          key: 'hasExpectEarnCnt',
          sortable: true
        },
        {
          title: '有预期收益占比',
          key: 'hasExpectEarnRatio',
          sortable: true
        },
        {
          title: '上线需求数',
          key: 'launchTaskCnt',
          sortable: true
        },
        {
          title: '有效果评估需求数',
          key: 'resultEvaluateCnt',
          sortable: true
        },
        {
          title: '有效果评估占比',
          key: 'resultEvaluateRatio',
          sortable: true
        },
        {
          title: '操作',
          key: 'action',
          // width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'primary',
                    size: 'small',
                    to: this.jumpclient(params.row),
                    target: '_blank',
                    disabled: this.getDetailFlag(params.row)
                  },
                  style: {
                    marginRight: '5px'
                  }
                  // on: {
                  //   click: () => {
                  //     this.jump(params.row)
                  //   }
                  // }
                },
                '查看详情'
              )
            ])
          }
        }
      ],
      display3: false,
      display2: false,
      modal6: false,
      isLoading: false,
      searchtype: this.$route.query.searchtype
        ? this.$route.query.searchtype
        : '服务端',
      invalidData: [],
      tableData: tableData,
      directories: [],
      jointdebugratio: [],
      devtestratio: [],
      ticedelayratio: [],
      periodlist: [],
      pattern: Bus.flag,
      resourceStats: [],
      timelineStats: [],
      evaluationStats: [],
      display4: false
    }
  },
  methods: {
    adjustTableHight: function (len) {
      let flag
      if (len > 10) {
        flag = true
      } else {
        flag = false
      }
      return flag
    },
    getclassname: function (raw) {
      if (raw.devTestRatio && Number(raw.devTestRatio, 0) < 5) {
        return 'red'
      } else {
        return '#495060'
      }
    },
    getfontweight: function (raw) {
      if (raw.devTestRatio && Number(raw.devTestRatio, 0) < 5) {
        return 'bold'
      }
    },
    rowClassName: function (raw, index) {
      if (index % 2 === 0 && raw.direction !== '总计') {
        return 'demo-stripe'
      } else if (raw.direction === '总计') {
        return 'demo-table-info-row'
      } else {
        return ''
      }
    },
    getDetailFlag: function (data) {
      if (data['direction'] === '总计') {
        return true
      } else {
        return false
      }
      // return true
    },
    jumpclient: function (data) {
      const dict = {
        bg: this.$route.query.bg,
        bizline: this.$route.query.bizline,
        group: this.$route.query.group,
        searchtype: '客户端',
        version: data['version'],
        app: data['app'],
        name: data['direction'],
        platform: data['period']
      }
      let temp = ''
      temp += '/overview/tice/ones/detail?'
      for (const each in dict) {
        if (dict[each]) {
          temp += `${each}=${dict[each]}`
          temp += '&'
        }
      }
      return temp
    },
    jump: function (data) {
      const dict = {
        searchtype: '服务端',
        name: data['key'],
        period: data['period']
      }
      let temp = ''
      temp += '/overview/tice/ones/detail?'
      for (const each in dict) {
        if (dict[each]) {
          temp += `${each}=${dict[each]}`
          temp += '&'
        }
      }
      return temp
    },
    setqaratiodata: function (data) {
      const self = this
      this.directories = []
      this.periodlist = []
      let periodlist = []
      for (const each in data) {
        if (self.directories.indexOf(data[each].direction) === -1) {
          this.directories.push(data[each].direction)
        }
        if (periodlist.indexOf(data[each].period) === -1) {
          // 可以按顺序排列
          periodlist.push(data[each].period)
        }
      }
      self.periodlist = self.sortbyTime(periodlist)
      const jointdebugratio = {}
      const devtest = {}
      for (let i = 0; i < self.periodlist.length; i += 1) {
        const eveperiodjointdebug = []
        const evedevtest = []
        for (let k = 0; k < self.directories.length; k += 1) {
          for (const j in data) {
            if (
              data[j].period === self.periodlist[i] &&
              data[j].direction === self.directories[k]
            ) {
              if (data[j].jointDebugRatio) {
                if (data[j].jointDebugRatio.indexOf('%') !== -1) {
                  eveperiodjointdebug.push(
                    this.roundFun(
                      parseFloat(
                        data[j].jointDebugRatio.split('%', 1)[0] * 100
                      ),
                      2
                    )
                  )
                } else {
                  eveperiodjointdebug.push(
                    this.roundFun(parseFloat(data[j].jointDebugRatio) * 100, 2)
                  )
                }
              } else {
                eveperiodjointdebug.push(0)
              }
              if (data[j].devTestRatio) {
                evedevtest.push(Number(data[j].devTestRatio))
              } else {
                evedevtest.push(0)
              }
              break
            }
          }
        }
        if (eveperiodjointdebug.length < self.directories.length) {
          eveperiodjointdebug.push(0)
        }
        if (evedevtest.length < self.directories.length) {
          evedevtest.push(0)
        }
        jointdebugratio[self.periodlist[i]] = eveperiodjointdebug
        devtest[self.periodlist[i]] = evedevtest
      }
      // const color = ['#00CD66', '#405ac1', '#20B2AA', '#FFA500', '#8192D6', '#c581d6', '#c1405a']
      this.jointdebugratio = []
      this.devtestratio = []
      for (const each in jointdebugratio) {
        const result = []
        for (const i in jointdebugratio[each]) {
          const tempdata = {
            name: self.directories[i],
            y: jointdebugratio[each][i]
          }
          result.push(tempdata)
        }
        let jointdebugdata = {}
        if (self.$route.query.type === 'QUARTER') {
          if (parseInt(each.split('-')[1]) <= 3) {
            jointdebugdata = {
              name: '第一季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 6) {
            jointdebugdata = {
              name: '第二季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 9) {
            jointdebugdata = {
              name: '第三季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else {
            jointdebugdata = {
              name: '第四季度',
              visible: true,
              // color: color[index],
              data: result
            }
          }
        } else if (self.$route.query.type === 'CUSTOMED') {
          jointdebugdata = {
            name: each,
            visible: true,
            // color: color[index],
            data: result
          }
        } else {
          jointdebugdata = {
            name: each.split('-')[1] + '月',
            visible: true,
            // color: color[index],
            data: result
          }
        }
        this.jointdebugratio.push(jointdebugdata)
        // index += 1
      }
      for (const each in devtest) {
        const result = []
        for (const i in devtest[each]) {
          const tempdata = {
            name: self.directories[i],
            y: devtest[each][i]
          }
          result.push(tempdata)
        }
        let devtestdata = {}
        if (self.$route.query.type === 'QUARTER') {
          if (parseInt(each.split('-')[1]) <= 3) {
            devtestdata = {
              name: '第一季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 6) {
            devtestdata = {
              name: '第二季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 9) {
            devtestdata = {
              name: '第三季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else {
            devtestdata = {
              name: '第四季度',
              visible: true,
              // color: color[index],
              data: result
            }
          }
        } else if (self.$route.query.type === 'CUSTOMED') {
          devtestdata = {
            name: each,
            visible: true,
            // color: color[index],
            data: result
          }
        } else {
          devtestdata = {
            name: each.split('-')[1] + '月',
            visible: true,
            // color: color[index],
            data: result
          }
        }
        this.devtestratio.push(devtestdata)
        // no += 1
      }
      setTimeout(function () {
        // self.isLoading = false
        self.jointdebugratiohighchart()
        self.devtestratiohighchart()
      }, 0)
    },
    roundFun: function (value, n) {
      return Math.round(value * Math.pow(10, n)) / Math.pow(10, n)
    },
    setrdratiodata: function (data) {
      const self = this
      self.directories = []
      self.periodlist = []
      let periodlist = []
      for (const each in data) {
        if (self.directories.indexOf(data[each].direction) === -1) {
          self.directories.push(data[each].direction)
        }
        if (periodlist.indexOf(data[each].period) === -1) {
          periodlist.push(data[each].period)
        }
      }
      self.periodlist = self.sortbyTime(periodlist)
      const ticedelayratio = {}
      for (let i = 0; i < self.periodlist.length; i += 1) {
        const eveticedelay = []
        for (let k = 0; k < self.directories.length; k += 1) {
          for (const j in data) {
            if (
              data[j].period === self.periodlist[i] &&
              data[j].direction === self.directories[k]
            ) {
              if (data[j].transferDelayRatio) {
                eveticedelay.push(
                  Number(data[j].transferDelayRatio.split('%', 1)[0])
                )
              } else {
                eveticedelay.push(0)
              }
              break
            }
          }
        }
        if (eveticedelay.length < self.directories.length) {
          eveticedelay.push(0)
        }
        ticedelayratio[self.periodlist[i]] = eveticedelay
      }
      // const color = ['#00CD66', '#405ac1', '#20B2AA', '#FFA500', '#8192D6', '#c581d6', '#c1405a']
      this.ticedelayratio = []
      // let no = 0
      for (const each in ticedelayratio) {
        const result = []
        for (const i in ticedelayratio[each]) {
          const tempdata = {
            name: self.directories[i],
            y: ticedelayratio[each][i]
          }
          result.push(tempdata)
        }
        let ticedelaydata = {}
        if (self.$route.query.type === 'QUARTER') {
          if (parseInt(each.split('-')[1]) <= 3) {
            ticedelaydata = {
              name: '第一季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 6) {
            ticedelaydata = {
              name: '第二季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else if (parseInt(each.split('-')[1]) <= 9) {
            ticedelaydata = {
              name: '第三季度',
              visible: true,
              // color: color[index],
              data: result
            }
          } else {
            ticedelaydata = {
              name: '第四季度',
              visible: true,
              // color: color[index],
              data: result
            }
          }
        } else if (self.$route.query.type === 'CUSTOMED') {
          ticedelaydata = {
            name: each,
            visible: true,
            // color: color[index],
            data: result
          }
        } else {
          ticedelaydata = {
            name: each.split('-')[1] + '月',
            visible: true,
            // color: color[index],
            data: result
          }
        }
        this.ticedelayratio.push(ticedelaydata)
        // no += 1
      }
      setTimeout(function () {
        self.ticedelayhighchart()
      }, 500)
    },
    jointdebugratiohighchart: function () {
      Highcharts.chart('jointdebugratio', {
        chart: {
          type: 'column',
          inverted: false
        },
        title: {
          text: '联调占比'
        },
        xAxis: {
          categories: this.directories
        },
        yAxis: {
          min: 0,
          max: 100,
          title: {
            text: null
          },
          allowDecimals: false
        },
        legend: {
          enabled: true
        },
        plotOptions: {
          column: {
            dataLabels: {
              enabled: true,
              color: '#000000',
              formatter: function () {
                return this.y + '%'
              }
            },
            pointPadding: 0.2,
            borderWidth: 0
          }
        },
        colors: [
          '#8192D6',
          '#20B2AA',
          '#FFA500',
          '#c581d6',
          '#c1405a',
          '#434348',
          '#90ed7d'
        ],
        series: this.jointdebugratio,
        credits: {
          enabled: false
        }
      })
    },
    devtestratiohighchart: function () {
      Highcharts.chart('devtestratio', {
        chart: {
          type: 'column',
          inverted: false
        },
        title: {
          text: '开发测试比'
        },
        xAxis: {
          categories: this.directories
        },
        yAxis: {
          min: 0,
          title: {
            text: null
          },
          plotLines: [
            {
              color: 'red',
              dashStyle: 'longdashdot',
              value: 5,
              width: 2,
              label: {
                text: '5:1',
                style: {
                  color: 'red',
                  fontWeight: 'bold'
                },
                align: 'left',
                x: -10
              }
            }
          ],
          allowDecimals: false
        },
        legend: {
          enabled: true
        },
        plotOptions: {
          column: {
            dataLabels: {
              enabled: true,
              color: '#000000',
              formatter: function () {
                return this.y + ':1'
              }
            },
            pointPadding: 0.2,
            borderWidth: 0
          }
        },
        colors: [
          '#8192D6',
          '#20B2AA',
          '#FFA500',
          '#c581d6',
          '#c1405a',
          '#434348',
          '#90ed7d'
        ],
        series: this.devtestratio,
        credits: {
          enabled: false
        }
      })
    },
    ticedelayhighchart: function () {
      Highcharts.chart('ticedelayratio', {
        chart: {
          type: 'column',
          inverted: false
        },
        title: {
          text: '提测延期占比'
        },
        xAxis: {
          categories: this.directories
        },
        yAxis: {
          min: 0,
          title: {
            text: null
          },
          allowDecimals: false
        },
        legend: {
          enabled: true
        },
        plotOptions: {
          column: {
            dataLabels: {
              enabled: true,
              color: '#000000',
              formatter: function () {
                return this.y + '%'
              }
            },
            pointPadding: 0.3,
            borderWidth: 0
          }
        },
        colors: [
          '#8192D6',
          '#20B2AA',
          '#FFA500',
          '#c581d6',
          '#c1405a',
          '#434348',
          '#90ed7d'
        ],
        series: this.ticedelayratio,
        credits: {
          enabled: false
        }
      })
    },
    setProcessMetricsData: function (rawdata) {
      if (rawdata) {
        let metricsStats = []
        for (let period in rawdata) {
          metricsStats.push(rawdata[period])
        }
        return metricsStats
      } else {
        return []
      }
    },
    asyncOK(data) {
      // let self = this
      $('#' + data)[0].__vue__.exportCsv({
        filename: '异常task'
      })
      if (Bus.incompletedata) {
        // self.pattern = Bus.incompletedata[0]['pattern']
        // self.getProcessData(Bus.incompletedata)
        Bus.$emit('refreshDistribution')
        Bus.$emit('refreshgetprocessdata', Bus.incompletedata)
      }
    },
    cancel() {
      // let self = this
      if (Bus.incompletedata) {
        // self.pattern = Bus.incompletedata[0]['pattern']
        // self.getProcessData(Bus.incompletedata)
        Bus.$emit('refreshDistribution')
        Bus.$emit('refreshgetprocessdata', Bus.incompletedata)
      }
    }
  },
  mounted: function () {
    Bus.processMetricsUnitOnesObject = this
  }
}
</script>

<style scoped>
.ivu-table .demo-table-info-row td {
  /*background-color: #bbbec4;*/
  font-weight: bolder;
  color: #2d8cf0;
  /*color: #fff;*/
}

.ivu-table .demo-stripe td {
  background-color: #f8f8f9;
  /*color: #fff;*/
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
