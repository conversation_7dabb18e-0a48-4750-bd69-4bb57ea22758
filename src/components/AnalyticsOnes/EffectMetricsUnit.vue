<template>
  <div>
    <Row type="flex" :style="{minHeight:'2950px'}">
      <Col span="24" order="1">
      <Card :style="{borderWidth:0,marginTop:'10px',marginLeft:'10px',marginRight:'10px',marginBottom:'5px'}" dis-hover>
        <!--<div slot="title">-->
          <!--<common-direction></common-direction>-->
        <!--</div>-->
        <div>
          <Tabs :value="efficiencytab">
            <TabPane label="RD人效数据" name="rd-effect-unit">
              <rd-effect-unit></rd-effect-unit>
            </TabPane>
            <TabPane label="QA人效数据" name="qa-effect-unit">
              <qa-effect-unit></qa-effect-unit>
            </TabPane>
            <!--<TabPane label="PM人效数据" name="pmeffect">-->
            <!--<pm-effect-unit></pm-effect-unit>-->
            <!--</TabPane>-->
          </Tabs>
        </div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { Bus } from '@/global/bus'
  // import router from '@/router'
  import RdEffectUnit from './RdEffectUnit'
  import QaEffectUnit from './QaEffectUnit'
  import CommonDirection from './CommonDirection'
  // import PmEffectUnit from './PmEffectUnit'

  // Vue.component('pm-effect-unit', PmEffectUnit)
  Vue.component('qa-effect-unit', QaEffectUnit)
  Vue.component('rd-effect-unit', RdEffectUnit)
  export default {
    components: {CommonDirection},
    name: 'effect-metrics-unit-ones',
    data: function () {
      return {
        efficiencytab: 'qa-effect-unit'
      }
    },
    mounted: function () {
      Bus.EfficiencyOnesObject = this
    }
  }
</script>

<style scoped>

</style>
