<template>
  <div>
    <Modal v-model="add" title="请选择方向" width="500px" :mask-closable="false" :closable="false" @on-ok="confirmAddDirection()" @on-cancel="cancelDirConfig()">
      <div style="margin-left: 20px">
        <el-tree
          ref="tree"
          class="filter-tree"
          show-checkbox
          style="overflow:auto;"
          :data="directionTree"
          @check="selectchange"
          node-key="direction_id"
          :highlight-current="true"
          :default-checked-keys="checkedList"
          :default-expanded-keys="checkedKeyList">
        </el-tree>
      </div>
    </Modal>
    <div class="inputcard" @click="addDirection()" :style="{maxWidth: width + 'px'}">
      <div v-if="directionList.selected.length > 0" v-for="(item, index) in directionList.selected" :key="index" style="display: inline;padding-left: 3px;">
        <tag closable @on-close="handleClose(item, index)">{{item.label}}</tag>
      </div>
      <div v-if="directionList.selected.length === 0">
        <div style="margin-top: 6px"><span style="color: #c5c8ce;font-size: 12px;padding-left: 8px;">点击选择方向</span></div>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import { Bus } from '@/global/bus'
  export default {
    name: 'direction',
    props: {
      width: {
        type: Number,
        default: 500
      }
    },
    data: function () {
      return {
        checkedList: [],
        directionTree: [],
        directionList: {
          selected: []
        },
        checkedKeyList: [],
        backupSelect: [],
        backupSelectId: [],
        directionIdList: [],
        initialDirection: [],
        add: false,
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      confirmAddDirection: function () {
        this.add = false
        this.$store.commit('setDirectionList', this.directionList.selected)
        this.$store.commit('setDirectionIdList', this.directionIdList)
      },
      addDirection: function () {
        this.add = true
        this.backupSelect = []
        this.backupSelectId = []
        this.backupSelect = this.directionList.selected
        this.backupSelectId = this.directionIdList
      },
      cancelDirConfig: function () {
        this.$set(this.directionList, 'selected', [])
        this.$set(this.directionList, 'selected', this.backupSelect)
        this.directionIdList = []
        this.directionIdList = JSON.parse(JSON.stringify(this.backupSelectId))
        this.$refs.tree.setCheckedNodes(this.directionList.selected)
        this.$store.commit('setDirectionList', this.directionList.selected)
        this.$store.commit('setDirectionIdList', this.directionIdList)
      },
      handleClose: function (data, index) {
        this.directionList.selected.splice(index, 1)
        this.directionIdList.splice(index, 1)
        this.$refs.tree.setCheckedNodes(this.directionList.selected)
        this.$store.commit('setDirectionList', this.directionList.selected)
        this.$store.commit('setDirectionIdList', this.directionIdList)
      },
      getinitialdata: function () {
        this.directionList.selected = []
        this.directionIdList = []
        this.checkedKeyList = []
        if (this.initialDirection.length > 0) {
          for (const each in this.initialDirection) {
            let temp = {
              children: [],
              direction_id: this.initialDirection[each].id,
              if_enable: true,
              if_leaf: false,
              label: this.initialDirection[each].name,
              value: this.initialDirection[each].name
            }
            this.directionList.selected.push(temp)
            this.directionIdList.push(temp.direction_id)
            this.checkedKeyList.push(parseInt(temp.direction_id))
          }
        } else {
          this.directionList.selected.push({
            children: [],
            direction_id: '1',
            if_enable: true,
            if_leaf: false,
            label: '集团',
            value: '集团'
          })
          this.directionIdList.push('1')
          this.checkedKeyList.push([1])
        }
        this.$nextTick(function () {
          this.$refs.tree.setCheckedKeys(this.checkedKeyList, false)
        })
        this.$store.commit('setDirectionList', this.directionList.selected)
        this.$store.commit('setDirectionIdList', this.directionIdList)
      },
      getRssDirectionList: function () {
        let self = this
        let params = {
          mis: this.mis
        }
        axios.post(this.getDomain('cq') + '/rss/query', JSON.stringify(params)).then(function (message) {
          if (message.data.status === 'success') {
            let data = message.data.data
            if (data[0]) {
              self.setinitialList(data[0].direction_list)
            }
          } else {
            self.setinitialList([])
          }
        })
      },
      setinitialList: function (data) {
        let self = this
        self.initialDirection = []
        if (data) {
          for (const each in data) {
            axios.get(this.getDomain('config') + '/mcd/org/basic_info?direction_id=' + data[each]).then(function (message) {
              if (message.data.result) {
                let temp = {
                  id: data[each],
                  name: message.data.info.direction_name
                }
                self.initialDirection.push(temp)
                // console.log('lenfth', self.initialDirection.length)
              }
            })
          }
          setTimeout(function () {
            self.getinitialdata()
          }, 500)
        }
        // console.log('lalalal', self.initialDirection)
      },
      selectchange: function (data, checkedNode) {
        this.$set(this.directionList, 'selected', [])
        this.directionIdList = []
        this.directionIdList = JSON.parse(JSON.stringify(checkedNode.checkedKeys))
        for (const each of checkedNode.checkedNodes) {
          if (!each.if_leaf && each.children) {
            for (const child of each.children) {
              for (var index in this.directionIdList) {
                if (this.directionIdList[index] === child.direction_id) {
                  this.directionIdList.splice(index, 1)
                }
              }
            }
          }
        }
        for (const each of checkedNode.checkedNodes) {
          if (this.directionIdList.indexOf(each.direction_id) !== -1) {
            this.directionList.selected.push(each)
          }
        }
      },
      getDirectionList: function () {
        let self = this
        axios.get(this.getDomain('config') + '/mcd/org/basic?direction_id=1').then(function (message) {
          self.directionTree.length = 0
          if (message.data.result) {
            self.directionTree.push(message.data.info)
            // self.getinitialdata()
          }
        }).catch(function () {
          self.directionTree.length = 0
        })
      }
    },
    mounted: function () {
      this.getRssDirectionList()
      this.getDirectionList()
    }
  }
</script>

<style scoped>
  .inputcard {
    box-sizing: border-box;
    border: 1px solid #dcdee2;
    background-color: #fff;
    border-radius: 4px;
    height: auto;
    min-height: 32px;
    display: block;
    cursor: pointer;
    text-align: left;
    padding-top: 2px;
    padding-bottom: 2px;
  }
</style>
