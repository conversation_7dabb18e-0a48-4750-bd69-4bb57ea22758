<template>
  <div>
    <head-component></head-component>
    <div style="margin-left: 1.5%;margin-top: 15px">
      <Breadcrumb>
        <BreadcrumbItem to="/">首页</BreadcrumbItem>
        <BreadcrumbItem to="/homepage/raptorMonitor">Raptor主干监控大盘</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="tab">
      <raptor-monitor-unit></raptor-monitor-unit>
    </div>
    <common-footer></common-footer>
  </div>
</template>

<script>
  import Vue from 'vue'
  import coverRatechart from '../ConfigurationCenter/coverRatechart'
  import { Bus } from '@/global/bus'
  import CommonFooter from '../Common/Footer'
  import RaptorMonitorUnit from './raptorMonitor-unit'
  Vue.component('coverrate-chart-component', coverRatechart)
  export default {
    name: 'RaptorMonitorHomepage',
    components: {CommonFooter, RaptorMonitorUnit},
    data: function () {
      return {
        tab: 'raptorMonitor',
        style: {
          'width': '100%',
          'height': (window.innerHeight - 55).toString() + 'px'
        },
        mis: Bus.userInfo.userLogin
      }
    },
    methods: {
      registerTab: function (value) {
        if (value !== 'homepage') {
          this.$router.push('/homepage/' + value)
        } else {
          this.$router.push('/')
        }
      },
      haveRootAuth: function () {
        return this.toolchainAuth(this.mis)
      }
    }
  }
</script>

<style scoped>
.tab{
  margin-top: 15px;
  margin-left: 1.0%;
  margin-right: 1.0%;
  width: auto
}
</style>
