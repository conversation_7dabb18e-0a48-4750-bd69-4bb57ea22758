var url = require('url');
var crypto = require('crypto');
var fs = require('fs');
var path = require('path');

/***
 * 美团签名验证,返回header
 * http://wiki.sankuai.com/pages/viewpage.action?pageId=29755412
 * @param method http请求方式
 * @param url 请求地址
 * @param key
 * @param secret
 * @returns {{Date: *, Authorization: string, Content-Type: string, Accept: string}}
 */
exports.createAuthHeader = function createAuthHeader(method, path, key, secret) {
    console.log(key);
    console.log(secret);
    var date_string = (new Date()).toGMTString(),
        string_to_sign = method.toUpperCase() + " "
            + path + "\n"
            + date_string,
        signature = crypto.createHmac('sha1', secret).update(string_to_sign).digest('base64');
    return {
        "Date": date_string,
        "Authorization": 'MWS ' + key + ':' + signature
    }
};

/**
 * 获取JSON文件中对象
 * @param relativePath 相对工程根目录
 * @returns {{}} 返回解析后对象
 */
exports.readJsonFileSync = function readJsonFileSync(relativePath) {
    try {
        return JSON.parse(fs.readFileSync(path.join(__dirname, relativePath), 'utf8'));
    } catch (exception) {
        console.error(exception);
        return {};
    }
};

/**
 * 获取JSON to Obj
 * @returns {{}} 返回解析后对象
 */
exports.parseMyJson = function (jsonString) {
    try {
        return JSON.parse(jsonString);
    } catch (exception){
        console.error(exception);
        return {};
    }

};



/**
 * 读取文件
 * utf8 相对工程根目录
 * @param relativePath 相对工程根目录
 */
exports.readFileSync = function readFileSync(relativePath) {
    try {
        return fs.readFileSync(path.join(__dirname, relativePath), 'utf8');
    } catch (exception) {
        console.error(exception);
        return '';
    }
};

exports.isEmptyValue = function (value) {
    var type;
    if (value == null) { // 等同于 value === undefined || value === null
        return true;
    }
    type = Object.prototype.toString.call(value).slice(8, -1);
    switch (type) {
        case 'String':
            return !String.prototype.trim.call(value);
        case 'Array':
            return !value.length;
        case 'Object':
            return isEmptyObject(value); // 普通对象使用 for...in 判断，有 key 即为 false
        default:
            return false; // 其他对象均视作非空
    }
};

var isEmptyObject = function isEmptyObject(a) {
    for (var b in a)
        return false;
    return true
};

/**
 * 判断element是否在数组中
 * @param arr 数组
 * @param element 数组元素
 */

exports.include = function(arr, element) {
    return (arr.indexOf(element) != -1);
};

exports.objSort = function (list) {
    return Object.keys(list).sort(function(a,b){return list[a]-list[b]})
};

exports.isFunction = function(fn) {
    return Object.prototype.toString.call(fn)=== '[object Function]';
};




