
{
  "apps" : [
    {
      "name"        : "qa-portal-fe",
      "script"      : "./node_server.js",
      "log_date_format"  : "\\\[YYYY-MM-DD HH:mm:ss.SSS\\\]",
      "error_file"       : "/opt/logs/qa-portal-fe-err-new.log",
      "out_file"         : "/opt/logs/qa-portal-fe-out-new.log",
      "instances"        : "0",
      "watch"            : false,
      "ignoreWatch"      : ["[\\/\\\\]\\./", "node_modules"],
      "merge_logs"       : true,
      "exec_mode"        : "cluster_mode",
      "env": {
        "PORT": 8000
      }
    }
  ]
}
