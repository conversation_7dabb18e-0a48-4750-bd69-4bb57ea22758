#!/usr/bin/env bash

# 导出已有的 HOME 环境变量
export HOME=/home/<USER>
baseDir=/opt/meituan/tool/projects
source $HOME/.bashrc


# node 环境判断
echo '开始 node 环境判断:'
	if ! type node 2>/dev/null || [[ `node -v` != 'v8.11.3' ]] ; then
		echo 'node 环境不匹配，开始构建：'
	  curl -Ls -o- http://build.sankuai.com/nvm/install | bash
	  source ~/.bashrc
	  nvm install v8.11.3
	  nvm alias default v8.11.3
	fi
echo 'node 环境判断构建成功！'

# 切换到项目家路径，执行 pm2 操作
echo '切换到项目家路径 $baseDir，执行 pm2 操作'
cd $baseDir

echo '开始检查全局 pm2'
	if ! type pm2 2>/dev/null ; then
		echo '全局下不存在 pm2 开始，开始安装 pm2:'
	  npm install pm2@4.5.0 -g --registry=http://r.npm.sankuai.com
	  echo 'pm2 安装成功！'
	fi
echo '检查全局 pm2 完成'


echo '杀死当前服务'
pm2 kill

echo '启动服务'
if [ -f "/var/sankuai/hulk/core_num" ]; then
  cpus=`cat /var/sankuai/hulk/core_num`
  pm2 start process.json --no-daemon --env $Node_Env -i $cpus
else
  pm2 start process.json --no-daemon --env $Node_Env
fi
