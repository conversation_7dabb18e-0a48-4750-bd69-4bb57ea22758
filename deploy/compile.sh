#!/usr/bin/env bash

echo '开始执行 npm install'
npm --registry=http://r.npm.sankuai.com install @mtfe/sso-web@1.1.17 --save
npm --registry=http://r.npm.sankuai.com install @ai/mss-upload-js@0.3.14 --save

npm --registry=http://r.npm.sankuai.com install @ss/mtd-vue2@1.2.25 --save
npm --registry=http://r.npm.sankuai.com install @vue/composition-api@1.7.2 --save
npm --registry=http://r.npm.sankuai.com install vue-bot-ui@0.2.11 --save

npm --registry=http://r.npm.sankuai.com install papaparse@5.4.1 --save
npm --registry=http://r.npm.sankuai.com install jsondiffpatch@^0.6.2 --save
npm --registry=http://r.npm.sankuai.com install vue-code-diff@^1.2.0 --save

npm install --registry=https://registry.npm.taobao.org/
npm install view-design@4.7.0 --registry=https://registry.npm.taobao.org/

echo 'npm install 成功'

echo '开始构建项目:'
npm run ${Webpack_Template}

# 判断 npm 是否成功执行
if [ $? -ne 0 ]
then
	echo 'npm run build 执行失败！'
  exit 1;
fi

echo '成功构建项目！'
echo 'compile.sh 执行完成'
