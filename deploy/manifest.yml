version: v1
build:
  os: centos7
  tools:
    node: 8.11.3

  cache:
    dirs:
      - ./node_modules
  run:
    workDir: ./       # workDir是代码仓库的相对目录
    cmd:
      # 安装前端依赖
      - sh ./deploy/compile.sh

  target:
    distDir: ./  # distDir是代码仓库的相对目录
    files:
      - ./

autodeploy:
  targetDir: /opt/meituan/tool/projects
  env:
    HOME: /home/<USER>
    USER: sankuai
  run: sh ./deploy/run.sh
  check: sh ./deploy/check.sh
  checkRetry: 10
  checkInterval: 10s
