# toolchain-qaportal-fe

> A Vue.js project

## Build Setup

``` bash
# install mt/sso-web dependencies
npm --registry=http://r.npm.sankuai.com install @mtfe/sso-web@1.1.15 --save

# install mt/mss-upload-js dependencies
npm --registry=http://r.npm.sankuai.com install @ai/mss-upload-js@0.3.14 --save

# install mtd2
npm --registry=http://r.npm.sankuai.com install @ss/mtd-vue2@1.2.25 --save
npm --registry=http://r.npm.sankuai.com install @vue/composition-api@1.7.2 --save

# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
