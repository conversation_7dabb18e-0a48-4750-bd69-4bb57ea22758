{"name": "toolchain-qaportal-fe", "version": "1.0.0", "description": "A Vue.js project", "author": "liuzhenzhou <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js", "test": "node build/test.js"}, "dependencies": {"@ai/mss-upload-js": "^0.3.14", "@mtfe/sso-web": "^1.1.15", "@ss/mtd-vue2": "^1.2.25", "@vue/composition-api": "^1.7.2", "axios": "^0.18.0", "bootstrap": "^3.4.1", "cal-heatmap": "^3.6.2", "cookie-parser": "^1.4.4", "d3": "^7.8.5", "echarts": "^5.4.2", "element-ui": "^2.15.1", "express": "^4.16.4", "express-async-errors": "^3.1.1", "font-awesome": "^4.7.0", "forever": "^0.15.3", "generate-schema": "^2.6.0", "gojs": "^2.1.37", "highcharts": "^7.0.3", "jquery": "^3.3.1", "jsondiffpatch": "^0.6.2", "jsoneditor": "^5.30.0", "moment": "^2.29.4", "npm": "^6.9.0", "papaparse": "^5.4.1", "pm2": "^2.10.4", "request": "^2.88.0", "socket.io-client": "^4.7.1", "video.js": "^7.17.0", "view-design": "^4.7.0", "vue": "^2.6.10", "vue-bot-ui": "^0.2.11", "vue-clipboard2": "^0.3.1", "vue-code-diff": "^1.2.0", "vue-cropper": "^0.5.5", "vue-json-editor": "^1.4.3", "vue-json-viewer": "^2.2.22", "vue-load-image": "^0.1.7", "vue-picture-preview": "^1.3.0", "vue-qriously": "^1.1.1", "vue-quill-editor": "^3.0.6", "vue-resizable": "^1.3.0", "vue-router": "^3.0.2", "vue-video-player": "^5.0.2", "vue-virtual-scroller": "^1.0.0-rc.2", "vuedraggable": "^2.24.3", "vuewordcloud": "^18.7.12", "vuex": "^3.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"autoprefixer": "^7.2.6", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.5", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.4.2", "copy-webpack-plugin": "^4.6.0", "css-loader": "^0.28.11", "eslint": "^3.19.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^3.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.4.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.4.0", "portfinder": "^1.0.20", "postcss-import": "^11.1.0", "postcss-loader": "^2.1.6", "postcss-url": "^7.3.2", "rimraf": "^2.6.3", "semver": "^5.6.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.3.0", "url-loader": "^1.1.2", "vue-easytable": "^1.7.2", "vue-loader": "^13.7.3", "vue-style-loader": "^3.1.2", "vue-template-compiler": "^2.6.14", "webpack": "^3.12.0", "webpack-bundle-analyzer": "^2.13.1", "webpack-dev-server": "^2.11.3", "webpack-merge": "^4.1.5"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}