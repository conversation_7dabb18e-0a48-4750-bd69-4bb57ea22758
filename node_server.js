/**
 * Created by ji<PERSON><PERSON><PERSON><PERSON> on 2017/2/6.
 */

const express = require('express');
const fs = require('fs');
const key = fs.readFileSync('./key.pem');
const cert = fs.readFileSync('./cert.pem');
const https_options = {
  key: key,
  cert: cert
};
const app = express();
const http = require('http').Server(app);
const https = require('https').Server(https_options, app);

app.use(express.static(__dirname + '/dist/'));

app.get('/regist', function(req, res){
  try{
    console.log('get /regist ' + __dirname);
    console.log(req.param.uuid);
    res.send("<html><body><h1>注册成功</h1></body></html>");
  }
  catch(err){
    console.log(err);
  }
});


app.get("*",(req, res) => {
  console.log('get * ' + __dirname);
  res.sendFile(__dirname + '/dist/index.html');
});


http.listen(8000, function(){
  console.log('listening on *:8000');
});

https.listen(8443, function(){
  console.log('listening on *:8443');
});
